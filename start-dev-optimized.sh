#!/bin/bash

# 多平台网课商品管理系统 - 优化版开发环境启动脚本
# 特性：智能检测、安全启动、详细日志、错误恢复

# 项目配置
PROJECT_PATH="/www/wwwroot/FDnew/SoybeanAdmin"
PROJECT_NAME="多平台网课商品管理系统"
BACKEND_PORT=3000
FRONTEND_PORT=5959
FRONTEND_ALT_PORT=5960

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 安全的进程终止函数
safe_kill_process() {
    local pid=$1
    local name=$2
    local timeout=${3:-10}
    
    if [ -z "$pid" ]; then
        return 1
    fi
    
    if ! kill -0 $pid 2>/dev/null; then
        return 0
    fi
    
    print_message $YELLOW "🔧 停止 $name (PID: $pid)..."
    
    # 温和终止
    kill -TERM $pid 2>/dev/null
    
    # 等待进程结束
    for i in $(seq 1 $timeout); do
        if ! kill -0 $pid 2>/dev/null; then
            print_message $GREEN "✅ $name 已停止"
            return 0
        fi
        sleep 1
    done
    
    # 强制终止
    print_message $YELLOW "🔧 强制停止 $name..."
    kill -KILL $pid 2>/dev/null
    sleep 2
    
    if ! kill -0 $pid 2>/dev/null; then
        print_message $GREEN "✅ $name 已强制停止"
        return 0
    else
        print_message $RED "❌ 无法停止 $name"
        return 1
    fi
}

# 智能端口检查和清理
smart_port_check() {
    local port=$1
    local service_name=$2
    
    local pid=$(lsof -ti:$port 2>/dev/null)
    
    if [ -z "$pid" ]; then
        print_message $GREEN "✅ 端口 $port 可用"
        return 0
    fi
    
    # 检查是否为项目相关进程
    local process_info=$(ps -p $pid -o args= 2>/dev/null)
    
    if echo "$process_info" | grep -qE "(ts-node.*src/index\.ts|vite.*--mode|nodemon.*server)" && \
       echo "$process_info" | grep -q "$PROJECT_PATH"; then
        print_message $YELLOW "⚠️  端口 $port 被项目进程占用 (PID: $pid)"
        
        # 询问是否停止
        read -p "是否停止现有的 $service_name 进程？(Y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]] || [[ -z $REPLY ]]; then
            safe_kill_process $pid "$service_name"
            return $?
        else
            print_message $RED "❌ 用户取消，无法启动 $service_name"
            return 1
        fi
    else
        print_message $YELLOW "⚠️  端口 $port 被其他进程占用 (PID: $pid)"
        echo "进程信息: $process_info"
        
        read -p "是否强制停止占用端口的进程？(y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            safe_kill_process $pid "端口占用进程"
            return $?
        else
            print_message $RED "❌ 端口冲突，无法启动 $service_name"
            return 1
        fi
    fi
}

# 环境检查
check_environment() {
    print_message $CYAN "📋 环境检查"
    echo "----------------------------------------"
    
    # Node.js检查
    if ! command -v node &> /dev/null; then
        print_message $RED "❌ Node.js 未安装"
        return 1
    fi
    
    local node_version=$(node -v)
    print_message $GREEN "✅ Node.js: $node_version"
    
    # pnpm检查
    if ! command -v pnpm &> /dev/null; then
        print_message $RED "❌ pnpm 未安装"
        echo "   安装命令: npm install -g pnpm"
        return 1
    fi
    
    local pnpm_version=$(pnpm -v)
    print_message $GREEN "✅ pnpm: $pnpm_version"
    
    # 数据库连接检查
    print_message $BLUE "🔍 检查数据库连接..."
    if mysql -h localhost -u newfd -pjx7KFPLnEsr4fzi7 -e "USE newfd; SELECT 1;" &>/dev/null; then
        print_message $GREEN "✅ 数据库连接正常"
    else
        print_message $YELLOW "⚠️  数据库连接失败，服务可能无法正常工作"
    fi
    
    # 授权系统API检查
    print_message $BLUE "🔍 检查授权系统API..."
    if curl -s --connect-timeout 5 https://shouquan.hhigq.luxe/public/api/system/info &>/dev/null; then
        print_message $GREEN "✅ 授权系统API连接正常"
    else
        print_message $YELLOW "⚠️  授权系统API连接失败"
    fi
    
    return 0
}

# 依赖检查和安装
check_dependencies() {
    print_message $CYAN "📦 依赖检查"
    echo "----------------------------------------"
    
    # 前端依赖
    if [ ! -d "node_modules" ]; then
        print_message $YELLOW "📦 安装前端依赖..."
        if ! pnpm install; then
            print_message $RED "❌ 前端依赖安装失败"
            return 1
        fi
    else
        print_message $GREEN "✅ 前端依赖已安装"
    fi
    
    # 后端依赖
    if [ ! -d "server/node_modules" ]; then
        print_message $YELLOW "📦 安装后端依赖..."
        if ! (cd server && npm install); then
            print_message $RED "❌ 后端依赖安装失败"
            return 1
        fi
    else
        print_message $GREEN "✅ 后端依赖已安装"
    fi
    
    return 0
}

# 启动后端服务
start_backend() {
    print_message $CYAN "📡 启动后端服务"
    echo "----------------------------------------"
    
    # 端口检查
    if ! smart_port_check $BACKEND_PORT "后端服务"; then
        return 1
    fi
    
    # 启动后端
    print_message $BLUE "🚀 启动后端服务 (端口: $BACKEND_PORT)..."
    
    cd server
    bash start.sh dev > ../logs/backend.log 2>&1 &
    local backend_pid=$!
    cd ..
    
    # 保存PID
    echo $backend_pid > logs/backend.pid
    
    # 等待启动
    print_message $BLUE "⏳ 等待后端服务启动..."
    local max_wait=30
    local wait_count=0
    
    while [ $wait_count -lt $max_wait ]; do
        if curl -s http://localhost:$BACKEND_PORT/api/license/status &>/dev/null; then
            print_message $GREEN "✅ 后端服务启动成功 (PID: $backend_pid)"
            return 0
        fi
        
        # 检查进程是否还在运行
        if ! kill -0 $backend_pid 2>/dev/null; then
            print_message $RED "❌ 后端进程意外退出"
            print_message $YELLOW "📋 后端日志 (最后10行):"
            tail -10 logs/backend.log
            return 1
        fi
        
        sleep 2
        wait_count=$((wait_count + 2))
        echo -n "."
    done
    
    echo ""
    print_message $RED "❌ 后端服务启动超时"
    print_message $YELLOW "📋 后端日志 (最后20行):"
    tail -20 logs/backend.log
    
    # 清理失败的进程
    safe_kill_process $backend_pid "后端服务"
    rm -f logs/backend.pid
    
    return 1
}

# 启动前端服务
start_frontend() {
    print_message $CYAN "🌐 启动前端服务"
    echo "----------------------------------------"
    
    # 端口检查（检查主端口和备用端口）
    local use_alt_port=false
    
    if ! smart_port_check $FRONTEND_PORT "前端服务"; then
        print_message $YELLOW "⚠️  尝试使用备用端口 $FRONTEND_ALT_PORT..."
        if ! smart_port_check $FRONTEND_ALT_PORT "前端服务"; then
            return 1
        fi
        use_alt_port=true
    fi
    
    # 运行端口检测脚本
    if [ -f "scripts/check-backend-port.js" ]; then
        print_message $BLUE "🔍 更新前端配置..."
        node scripts/check-backend-port.js
    fi
    
    # 启动前端
    if [ "$use_alt_port" = "true" ]; then
        print_message $BLUE "🚀 启动前端服务 (备用端口: $FRONTEND_ALT_PORT)..."
    else
        print_message $BLUE "🚀 启动前端服务 (端口: $FRONTEND_PORT)..."
    fi
    
    pnpm dev > logs/frontend.log 2>&1 &
    local frontend_pid=$!
    
    # 保存PID
    echo $frontend_pid > logs/frontend.pid
    
    # 等待启动
    print_message $BLUE "⏳ 等待前端服务启动..."
    local max_wait=60
    local wait_count=0
    local actual_port=""
    
    while [ $wait_count -lt $max_wait ]; do
        # 检查两个可能的端口
        if curl -s http://localhost:$FRONTEND_PORT/ &>/dev/null; then
            actual_port=$FRONTEND_PORT
            break
        elif curl -s http://localhost:$FRONTEND_ALT_PORT/ &>/dev/null; then
            actual_port=$FRONTEND_ALT_PORT
            break
        fi
        
        # 检查进程是否还在运行
        if ! kill -0 $frontend_pid 2>/dev/null; then
            print_message $RED "❌ 前端进程意外退出"
            print_message $YELLOW "📋 前端日志 (最后10行):"
            tail -10 logs/frontend.log
            return 1
        fi
        
        sleep 3
        wait_count=$((wait_count + 3))
        echo -n "."
    done
    
    echo ""
    
    if [ ! -z "$actual_port" ]; then
        print_message $GREEN "✅ 前端服务启动成功 (PID: $frontend_pid, 端口: $actual_port)"
        return 0
    else
        print_message $RED "❌ 前端服务启动超时"
        print_message $YELLOW "📋 前端日志 (最后20行):"
        tail -20 logs/frontend.log
        
        # 清理失败的进程
        safe_kill_process $frontend_pid "前端服务"
        rm -f logs/frontend.pid
        
        return 1
    fi
}

# 显示启动成功信息
show_success_info() {
    local frontend_port=$FRONTEND_PORT
    
    # 检测实际使用的端口
    if ! curl -s http://localhost:$FRONTEND_PORT/ &>/dev/null; then
        if curl -s http://localhost:$FRONTEND_ALT_PORT/ &>/dev/null; then
            frontend_port=$FRONTEND_ALT_PORT
        fi
    fi
    
    echo ""
    print_message $GREEN "🎉 开发环境启动成功！"
    echo "================================================"
    echo "🌐 访问地址:"
    echo "   前端: http://localhost:$frontend_port/"
    echo "   后端: http://localhost:$BACKEND_PORT/"
    echo ""
    echo "🔐 默认管理员账号:"
    echo "   用户名: admin"
    echo "   密码: 123456"
    echo ""
    echo "🛠️  开发工具:"
    echo "   - 前端日志: logs/frontend.log"
    echo "   - 后端日志: logs/backend.log"
    echo "   - 进程状态: ./dev.sh status"
    echo "   - 详细检查: ./dev.sh check"
    echo ""
    echo "🔧 管理命令:"
    echo "   - 查看状态: ./dev.sh status"
    echo "   - 查看日志: ./dev.sh logs"
    echo "   - 安全停止: ./dev.sh stop"
    echo "   - 重启服务: ./dev.sh restart"
    echo ""
    echo "⚠️  注意事项:"
    echo "   1. 首次使用需要完成授权验证"
    echo "   2. 系统会自动检测端口冲突并处理"
    echo "   3. 使用 Ctrl+C 或 ./dev.sh stop 安全停止"
    echo "================================================"
}

# 主程序
main() {
    print_message $CYAN "🚀 启动 $PROJECT_NAME 开发环境"
    echo "================================================"
    
    # 确保在正确的目录
    if [ ! -f "package.json" ] || [ ! -d "server" ]; then
        print_message $RED "❌ 错误: 请在项目根目录中运行此脚本"
        exit 1
    fi
    
    # 创建日志目录
    mkdir -p logs
    
    # 环境检查
    if ! check_environment; then
        print_message $RED "❌ 环境检查失败"
        exit 1
    fi
    
    echo ""
    
    # 依赖检查
    if ! check_dependencies; then
        print_message $RED "❌ 依赖检查失败"
        exit 1
    fi
    
    echo ""
    
    # 启动后端
    if ! start_backend; then
        print_message $RED "❌ 后端启动失败"
        exit 1
    fi
    
    echo ""
    
    # 启动前端
    if ! start_frontend; then
        print_message $RED "❌ 前端启动失败"
        print_message $YELLOW "🛑 清理后端进程..."
        
        if [ -f "logs/backend.pid" ]; then
            local backend_pid=$(cat logs/backend.pid)
            safe_kill_process $backend_pid "后端服务"
            rm -f logs/backend.pid
        fi
        
        exit 1
    fi
    
    # 显示成功信息
    show_success_info
    
    # 设置信号处理
    trap 'echo ""; print_message $YELLOW "🛑 正在安全停止服务..."; ./stop-dev-safe.sh; exit 0' INT TERM
    
    # 保持脚本运行
    print_message $BLUE "📱 服务正在运行，按 Ctrl+C 安全停止"
    wait
}

# 运行主程序
main "$@"
