#!/bin/bash

# 多平台网课商品管理系统 - 超安全停止开发环境脚本
# 此脚本采用最保守的策略，只停止明确的项目进程

echo "🛑 超安全停止开发环境（保护系统进程）"
echo "================================================"

PROJECT_PATH="/www/wwwroot/FDnew/SoybeanAdmin"

# 超安全的进程终止函数
ultra_safe_kill() {
    local pid=$1
    local name=$2
    
    if [ -z "$pid" ]; then
        return 1
    fi
    
    # 检查进程是否存在
    if ! kill -0 $pid 2>/dev/null; then
        echo "ℹ️  进程 $name 已不存在"
        return 1
    fi
    
    # 获取进程详细信息
    local process_info=$(ps -p $pid -o pid,ppid,user,args= 2>/dev/null)
    
    # 严格检查：只处理明确的项目进程
    if echo "$process_info" | grep -qE "(ts-node.*src/index\.ts|vite.*--mode.*test|nodemon.*server|pnpm.*dev)" && \
       echo "$process_info" | grep -q "$PROJECT_PATH"; then
        
        echo "🔧 安全停止项目进程: $name (PID: $pid)"
        
        # 温和终止
        kill -TERM $pid 2>/dev/null
        
        # 等待最多10秒
        for i in {1..10}; do
            if ! kill -0 $pid 2>/dev/null; then
                echo "✅ $name 已安全停止"
                return 0
            fi
            sleep 1
        done
        
        # 如果仍然存在，使用SIGKILL
        echo "🔧 强制停止 $name..."
        kill -KILL $pid 2>/dev/null
        sleep 2
        
        if ! kill -0 $pid 2>/dev/null; then
            echo "✅ $name 已强制停止"
            return 0
        else
            echo "❌ 无法停止 $name"
            return 1
        fi
    else
        echo "⚠️  跳过非项目进程: $name (PID: $pid)"
        return 1
    fi
}

echo "🔒 超安全模式：只停止确认的项目进程"
echo "📁 项目路径：$PROJECT_PATH"
echo ""

# 停止PID文件记录的进程
if [ -f "logs/backend.pid" ]; then
    backend_pid=$(cat logs/backend.pid)
    echo "📡 检查后端服务 (PID: $backend_pid)..."
    if ultra_safe_kill $backend_pid "后端服务"; then
        rm -f logs/backend.pid
    fi
else
    echo "ℹ️  未找到后端PID文件"
fi

if [ -f "logs/frontend.pid" ]; then
    frontend_pid=$(cat logs/frontend.pid)
    echo "🌐 检查前端服务 (PID: $frontend_pid)..."
    if ultra_safe_kill $frontend_pid "前端服务"; then
        rm -f logs/frontend.pid
    fi
else
    echo "ℹ️  未找到前端PID文件"
fi

# 检查项目相关的残留进程
echo ""
echo "🔍 检查项目残留进程..."

# 只查找明确的项目进程
project_processes=$(ps aux | grep -E "(ts-node.*src/index\.ts|vite.*--mode.*test|nodemon.*server)" | grep "$PROJECT_PATH" | grep -v grep)

if [ ! -z "$project_processes" ]; then
    echo "发现项目进程："
    echo "$project_processes"
    echo ""
    
    # 逐个安全停止
    echo "$project_processes" | while read -r line; do
        pid=$(echo "$line" | awk '{print $2}')
        process_name=$(echo "$line" | awk '{for(i=11;i<=NF;i++) printf "%s ", $i; print ""}' | cut -c1-50)
        ultra_safe_kill $pid "$process_name"
    done
else
    echo "✅ 未发现项目相关的残留进程"
fi

# 最终检查
echo ""
echo "🔍 最终安全检查..."

# 确认关键系统进程仍在运行
critical_count=$(ps aux | grep -E "(sshd|nginx|systemd)" | grep -v grep | wc -l)
echo "✅ 系统关键进程数量: $critical_count"

# 确认项目进程已停止
remaining_count=$(ps aux | grep -E "(ts-node.*src/index\.ts|vite.*--mode.*test|nodemon.*server)" | grep "$PROJECT_PATH" | grep -v grep | wc -l)
if [ $remaining_count -eq 0 ]; then
    echo "✅ 所有项目进程已安全停止"
else
    echo "⚠️  仍有 $remaining_count 个项目进程在运行"
fi

echo ""
echo "✅ 超安全停止完成"
echo "================================================"
echo "📋 安全保证："
echo "   - 只停止了明确的项目进程"
echo "   - SSH连接完全不受影响"
echo "   - 系统服务保持正常运行"
echo "   - 远程连接保持稳定"
echo ""
echo "🚀 重新启动: ./start-dev.sh"
echo "================================================"
