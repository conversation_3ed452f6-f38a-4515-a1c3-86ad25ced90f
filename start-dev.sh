#!/bin/bash

# SoybeanAdmin 智能启动脚本
# 避免重复启动，减少内存占用

PROJECT_DIR="/www/wwwroot/FDnew/SoybeanAdmin"
FRONTEND_PORT=5959
BACKEND_PORT=3000

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    local service_name=$2

    if netstat -tlnp | grep -q ":$port "; then
        local pid=$(netstat -tlnp | grep ":$port " | awk '{print $7}' | cut -d'/' -f1)
        log_warning "$service_name 端口 $port 已被占用 (PID: $pid)"
        return 0
    else
        log_info "$service_name 端口 $port 可用"
        return 1
    fi
}

# 停止指定端口的进程
stop_port_process() {
    local port=$1
    local service_name=$2

    local pid=$(netstat -tlnp | grep ":$port " | awk '{print $7}' | cut -d'/' -f1 | head -1)
    if [ ! -z "$pid" ] && [ "$pid" != "-" ]; then
        log_warning "停止 $service_name 进程 (PID: $pid)"
        kill -TERM $pid 2>/dev/null
        sleep 2

        # 如果进程仍在运行，强制杀死
        if kill -0 $pid 2>/dev/null; then
            log_warning "强制停止 $service_name 进程 (PID: $pid)"
            kill -KILL $pid 2>/dev/null
        fi
    fi
}

# 清理重复的前端进程
cleanup_frontend() {
    log_info "清理重复的前端进程..."

    # 停止所有vite进程
    pkill -f "vite.*--mode test" 2>/dev/null
    pkill -f "pnpm dev" 2>/dev/null

    # 等待进程完全停止
    sleep 3

    # 检查是否还有残留进程
    if pgrep -f "vite.*--mode test" > /dev/null; then
        log_warning "强制清理残留的vite进程"
        pkill -9 -f "vite.*--mode test" 2>/dev/null
    fi
}

# 清理重复的后端进程
cleanup_backend() {
    log_info "清理重复的后端进程..."

    # 保留第一个后端进程，停止其他重复进程
    local backend_pids=($(pgrep -f "ts-node.*src/index.ts"))

    if [ ${#backend_pids[@]} -gt 1 ]; then
        log_warning "发现 ${#backend_pids[@]} 个后端进程，保留第一个，停止其他"

        for ((i=1; i<${#backend_pids[@]}; i++)); do
            local pid=${backend_pids[$i]}
            log_warning "停止重复的后端进程 (PID: $pid)"
            kill -TERM $pid 2>/dev/null
        done

        sleep 2
    fi
}

# 启动后端服务
start_backend() {
    if check_port $BACKEND_PORT "后端服务"; then
        log_info "后端服务已在运行，跳过启动"
        return 0
    fi

    log_info "启动后端服务..."
    cd "$PROJECT_DIR/server"

    # 检查依赖
    if [ ! -d "node_modules" ]; then
        log_info "安装后端依赖..."
        npm install
    fi

    # 后台启动后端服务
    nohup npm run dev > ../logs/backend.log 2>&1 &
    local backend_pid=$!

    # 等待后端启动
    local count=0
    while [ $count -lt 30 ]; do
        if check_port $BACKEND_PORT "后端服务"; then
            log_success "后端服务启动成功 (PID: $backend_pid, 端口: $BACKEND_PORT)"
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done

    log_error "后端服务启动失败"
    return 1
}

# 启动前端服务
start_frontend() {
    if check_port $FRONTEND_PORT "前端服务"; then
        log_info "前端服务已在运行，跳过启动"
        return 0
    fi

    log_info "启动前端服务..."
    cd "$PROJECT_DIR"

    # 检查依赖
    if [ ! -d "node_modules" ]; then
        log_info "安装前端依赖..."
        pnpm install
    fi

    # 后台启动前端服务
    nohup pnpm dev > logs/frontend.log 2>&1 &
    local frontend_pid=$!

    # 等待前端启动
    local count=0
    while [ $count -lt 60 ]; do
        if check_port $FRONTEND_PORT "前端服务"; then
            log_success "前端服务启动成功 (PID: $frontend_pid, 端口: $FRONTEND_PORT)"
            return 0
        fi
        sleep 1
        count=$((count + 1))
    done

    log_error "前端服务启动失败"
    return 1
}

# 显示服务状态
show_status() {
    echo
    log_info "=== 服务状态 ==="

    if check_port $BACKEND_PORT "后端服务"; then
        log_success "后端服务: 运行中 (端口: $BACKEND_PORT)"
    else
        log_error "后端服务: 未运行"
    fi

    if check_port $FRONTEND_PORT "前端服务"; then
        log_success "前端服务: 运行中 (端口: $FRONTEND_PORT)"
    else
        log_error "前端服务: 未运行"
    fi

    echo
    log_info "访问地址:"
    log_info "前端: http://localhost:$FRONTEND_PORT"
    log_info "后端: http://localhost:$BACKEND_PORT"
    echo
}

# 创建日志目录
create_log_dir() {
    if [ ! -d "$PROJECT_DIR/logs" ]; then
        mkdir -p "$PROJECT_DIR/logs"
        log_info "创建日志目录: $PROJECT_DIR/logs"
    fi
}

# 主函数
main() {
    log_info "SoybeanAdmin 智能启动脚本"
    log_info "项目目录: $PROJECT_DIR"
    echo

    # 检查项目目录
    if [ ! -d "$PROJECT_DIR" ]; then
        log_error "项目目录不存在: $PROJECT_DIR"
        exit 1
    fi

    # 创建日志目录
    create_log_dir

    # 清理重复进程
    cleanup_frontend
    cleanup_backend

    # 启动服务
    if start_backend && start_frontend; then
        show_status
        log_success "所有服务启动完成！"
    else
        log_error "服务启动失败，请检查日志"
        exit 1
    fi
}

# 处理命令行参数
case "${1:-start}" in
    "start")
        main
        ;;
    "stop")
        log_info "停止所有服务..."
        stop_port_process $BACKEND_PORT "后端服务"
        stop_port_process $FRONTEND_PORT "前端服务"
        cleanup_frontend
        cleanup_backend
        log_success "所有服务已停止"
        ;;
    "restart")
        log_info "重启所有服务..."
        $0 stop
        sleep 3
        $0 start
        ;;
    "status")
        show_status
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status}"
        echo "  start   - 启动所有服务 (默认)"
        echo "  stop    - 停止所有服务"
        echo "  restart - 重启所有服务"
        echo "  status  - 显示服务状态"
        exit 1
        ;;
esac
