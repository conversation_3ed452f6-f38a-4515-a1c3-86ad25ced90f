#!/bin/bash
# SoybeanAdmin 生产环境一键部署脚本
# 专为宝塔环境设计，简单易用

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目路径
PROJECT_DIR="/www/wwwroot/FDnew/SoybeanAdmin"
FRONTEND_DIST="$PROJECT_DIR/dist"
SERVER_DIR="$PROJECT_DIR/server"

# 日志函数
log_info() {
    echo -e "${GREEN}[信息]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[步骤]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        exit 1
    fi
}

# 检查并安装依赖
install_dependencies() {
    log_step "检查系统依赖..."

    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装，请先在宝塔面板安装 Node.js 18+"
        exit 1
    fi

    # 检查pnpm
    if ! command -v pnpm &> /dev/null; then
        log_info "安装 pnpm..."
        npm install -g pnpm
    fi

    # 检查PM2
    if ! command -v pm2 &> /dev/null; then
        log_info "安装 PM2..."
        npm install -g pm2
    fi

    log_info "✅ 系统依赖检查完成"
}

# 构建前端
build_frontend() {
    log_step "构建前端项目..."

    cd "$PROJECT_DIR"

    # 清理可能存在的宝塔文件
    if [ -d "dist" ]; then
        log_info "清理旧的构建文件..."
        # 移除宝塔自动生成的文件
        chattr -i dist/.user.ini 2>/dev/null || true
        rm -f dist/.user.ini 2>/dev/null || true
        rm -f dist/.htaccess 2>/dev/null || true
    fi

    # 安装前端依赖
    if [ ! -d "node_modules" ]; then
        log_info "安装前端依赖..."
        pnpm install
    fi

    # 构建前端
    log_info "正在构建前端..."
    pnpm build

    if [ -d "$FRONTEND_DIST" ]; then
        log_info "✅ 前端构建完成: $FRONTEND_DIST"
    else
        log_error "❌ 前端构建失败"
        exit 1
    fi
}

# 部署后端
deploy_backend() {
    log_step "部署后端服务..."

    cd "$SERVER_DIR"

    # 安装后端依赖
    if [ ! -d "node_modules" ]; then
        log_info "安装后端依赖..."
        pnpm install
    fi

    # 构建后端
    if [ ! -d "dist" ]; then
        log_info "构建后端..."
        pnpm run build
    fi

    # 启动后端服务
    log_info "启动后端服务..."
    if pm2 describe soybean-server > /dev/null 2>&1; then
        log_info "重启现有服务..."
        pm2 restart soybean-server
    else
        log_info "启动新服务..."
        pm2 start dist/index.js --name soybean-server
    fi

    pm2 save
    log_info "✅ 后端服务部署完成"
}

# 生成Nginx配置
generate_nginx_config() {
    log_step "生成Nginx配置..."

    # 获取服务器IP
    SERVER_IP=$(curl -s ifconfig.me 2>/dev/null || echo "your-server-ip")

    cat > "$PROJECT_DIR/nginx-site-config.txt" << EOF
# 将以下配置复制到宝塔面板的站点配置文件中

server {
    listen 6666;
    server_name ${SERVER_IP}_6666;
    root $FRONTEND_DIST;
    index index.html;

    # API代理 - 重要：让前端能访问后端
    location /api/ {
        proxy_pass http://127.0.0.1:3000/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
    }

    # Vue路由支持 - 重要：让前端路由正常工作
    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # 静态文件缓存
    location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg)\$ {
        expires 1y;
        add_header Cache-Control "public";
    }

    # 开启压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
}
EOF

    log_info "✅ Nginx配置已生成: $PROJECT_DIR/nginx-site-config.txt"
}

# 显示部署完成信息
show_completion_guide() {
    echo ""
    echo "🎉 部署完成！请按以下步骤完成配置："
    echo ""
    echo "📋 第1步：在宝塔面板创建站点"
    echo "   1. 打开宝塔面板 → 网站 → 添加站点"
    echo "   2. 域名：$(curl -s ifconfig.me)_6666"
    echo "   3. 根目录：$FRONTEND_DIST"
    echo ""
    echo "📋 第2步：配置Nginx"
    echo "   1. 点击站点 → 设置 → 配置文件"
    echo "   2. 复制 nginx-site-config.txt 的内容到配置文件"
    echo "   3. 保存并重载"
    echo ""
    echo "📋 第3步：测试访问"
    echo "   前端地址：http://$(curl -s ifconfig.me):6666"
    echo "   管理员账号：admin"
    echo "   密码：123456"
    echo ""
    echo "🔧 常用命令："
    echo "   查看后端状态：pm2 status"
    echo "   查看后端日志：pm2 logs soybean-server"
    echo "   重启后端：pm2 restart soybean-server"
    echo ""
}

# 主函数
main() {
    echo "🚀 开始部署 SoybeanAdmin..."
    echo "项目路径：$PROJECT_DIR"
    echo ""

    # 检查项目目录
    if [ ! -d "$PROJECT_DIR" ]; then
        log_error "项目目录不存在：$PROJECT_DIR"
        exit 1
    fi

    cd "$PROJECT_DIR"

    # 执行部署步骤
    install_dependencies
    build_frontend
    deploy_backend
    generate_nginx_config
    show_completion_guide

    log_info "🎉 部署完成！"
}

# 运行主函数
main "$@"
