#!/usr/bin/env node

/** 代码重复检查工具 用于检查项目中的重复代码和文件 */

import fs from 'node:fs';
import path from 'node:path';
import crypto from 'node:crypto';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class DuplicateChecker {
  constructor() {
    this.fileHashes = new Map();
    this.duplicates = [];
    this.codeBlocks = new Map();
    this.ignorePaths = ['node_modules', 'dist', '.git', 'logs', 'coverage', 'server/data', 'server/database'];
    this.ignoreExtensions = ['.log', '.tmp', '.temp', '.cache', '.lock'];
  }

  /** 检查重复文件 */
  checkDuplicateFiles(dir = '.') {
    console.log('🔍 检查重复文件...');
    this.scanDirectory(dir);

    // 查找重复的哈希值
    const hashGroups = new Map();
    for (const [filePath, hash] of this.fileHashes) {
      if (!hashGroups.has(hash)) {
        hashGroups.set(hash, []);
      }
      hashGroups.get(hash).push(filePath);
    }

    // 输出重复文件
    let duplicateCount = 0;
    for (const [hash, files] of hashGroups) {
      if (files.length > 1) {
        console.log(`\n📄 发现重复文件 (${files.length} 个):`);
        files.forEach(file => console.log(`  - ${file}`));
        duplicateCount += files.length - 1;
      }
    }

    if (duplicateCount === 0) {
      console.log('✅ 未发现重复文件');
    } else {
      console.log(`\n⚠️  总共发现 ${duplicateCount} 个重复文件`);
    }

    return duplicateCount;
  }

  /** 扫描目录 */
  scanDirectory(dir) {
    const items = fs.readdirSync(dir);

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const relativePath = path.relative('.', fullPath);

      // 跳过忽略的路径
      if (this.shouldIgnore(relativePath)) {
        continue;
      }

      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        this.scanDirectory(fullPath);
      } else if (stat.isFile()) {
        this.processFile(fullPath, relativePath);
      }
    }
  }

  /** 处理文件 */
  processFile(fullPath, relativePath) {
    try {
      const content = fs.readFileSync(fullPath, 'utf8');
      const hash = crypto.createHash('md5').update(content).digest('hex');
      this.fileHashes.set(relativePath, hash);
    } catch (error) {
      // 忽略无法读取的文件（如二进制文件）
    }
  }

  /** 检查是否应该忽略 */
  shouldIgnore(filePath) {
    // 检查路径
    for (const ignorePath of this.ignorePaths) {
      if (filePath.includes(ignorePath)) {
        return true;
      }
    }

    // 检查扩展名
    const ext = path.extname(filePath);
    if (this.ignoreExtensions.includes(ext)) {
      return true;
    }

    // 检查隐藏文件
    if (path.basename(filePath).startsWith('.')) {
      return true;
    }

    return false;
  }

  /** 检查代码重复 */
  checkCodeDuplicates() {
    console.log('\n🔍 检查代码重复...');

    const codeFiles = [];
    this.findCodeFiles('.', codeFiles);

    let duplicateBlocks = 0;

    for (const file of codeFiles) {
      try {
        const content = fs.readFileSync(file, 'utf8');
        const blocks = this.extractCodeBlocks(content, file);

        for (const block of blocks) {
          const hash = crypto.createHash('md5').update(block.code).digest('hex');

          if (!this.codeBlocks.has(hash)) {
            this.codeBlocks.set(hash, []);
          }

          this.codeBlocks.get(hash).push({
            file,
            line: block.line,
            code: block.code
          });
        }
      } catch (error) {
        // 忽略读取错误
      }
    }

    // 输出重复代码块
    for (const [hash, blocks] of this.codeBlocks) {
      if (blocks.length > 1 && blocks[0].code.trim().length > 100) {
        console.log(`\n📝 发现重复代码块 (${blocks.length} 处):`);
        blocks.forEach(block => {
          console.log(`  - ${block.file}:${block.line}`);
        });
        console.log(`  代码片段: ${blocks[0].code.substring(0, 100)}...`);
        duplicateBlocks++;
      }
    }

    if (duplicateBlocks === 0) {
      console.log('✅ 未发现明显的重复代码块');
    } else {
      console.log(`\n⚠️  发现 ${duplicateBlocks} 个重复代码块`);
    }

    return duplicateBlocks;
  }

  /** 查找代码文件 */
  findCodeFiles(dir, files) {
    const items = fs.readdirSync(dir);

    for (const item of items) {
      const fullPath = path.join(dir, item);
      const relativePath = path.relative('.', fullPath);

      if (this.shouldIgnore(relativePath)) {
        continue;
      }

      const stat = fs.statSync(fullPath);

      if (stat.isDirectory()) {
        this.findCodeFiles(fullPath, files);
      } else if (this.isCodeFile(fullPath)) {
        files.push(fullPath);
      }
    }
  }

  /** 判断是否为代码文件 */
  isCodeFile(filePath) {
    const codeExtensions = ['.js', '.ts', '.vue', '.jsx', '.tsx', '.css', '.scss', '.less'];
    const ext = path.extname(filePath);
    return codeExtensions.includes(ext);
  }

  /** 提取代码块 */
  extractCodeBlocks(content, filePath) {
    const lines = content.split('\n');
    const blocks = [];

    // 提取函数和类
    let currentBlock = '';
    let blockStart = 0;
    let braceCount = 0;
    let inBlock = false;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // 检测函数或类的开始
      if (
        line.includes('function ') ||
        line.includes('class ') ||
        line.includes('const ') ||
        line.includes('let ') ||
        line.includes('export ')
      ) {
        if (!inBlock) {
          blockStart = i + 1;
          currentBlock = '';
          inBlock = true;
          braceCount = 0;
        }
      }

      if (inBlock) {
        currentBlock += `${lines[i]}\n`;

        // 计算大括号
        braceCount += (line.match(/\{/g) || []).length;
        braceCount -= (line.match(/\}/g) || []).length;

        // 块结束
        if (braceCount === 0 && line.includes('}')) {
          if (currentBlock.trim().length > 50) {
            blocks.push({
              line: blockStart,
              code: currentBlock.trim()
            });
          }
          inBlock = false;
          currentBlock = '';
        }
      }
    }

    return blocks;
  }

  /** 生成报告 */
  generateReport() {
    console.log('\n📊 生成项目结构优化报告...');

    const report = {
      timestamp: new Date().toISOString(),
      duplicateFiles: this.checkDuplicateFiles(),
      duplicateCode: this.checkCodeDuplicates(),
      recommendations: this.getRecommendations()
    };

    const reportPath = 'project-optimization-report.json';
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

    console.log(`\n📄 报告已生成: ${reportPath}`);
    return report;
  }

  /** 获取优化建议 */
  getRecommendations() {
    const recommendations = [];

    if (this.duplicates.length > 0) {
      recommendations.push('删除重复文件以减少项目体积');
    }

    if (this.codeBlocks.size > 0) {
      recommendations.push('提取重复代码为公共函数或组件');
    }

    recommendations.push('定期运行此工具检查项目结构');
    recommendations.push('使用ESLint和Prettier保持代码风格一致');
    recommendations.push('实施代码审查流程');

    return recommendations;
  }
}

// 运行检查
if (import.meta.url === `file://${process.argv[1]}`) {
  const checker = new DuplicateChecker();
  const report = checker.generateReport();

  console.log('\n🎯 优化建议:');
  report.recommendations.forEach(rec => {
    console.log(`  • ${rec}`);
  });

  process.exit(0);
}

export default DuplicateChecker;
