#!/usr/bin/env node

const fs = require('node:fs');
const path = require('node:path');

/** 日志清理脚本 清理过大的日志文件，设置日志轮转机制 */

const LOG_DIRS = ['logs', 'server/logs'];

const LOG_FILES = ['server/server.log'];

const MAX_LOG_SIZE = 10 * 1024 * 1024; // 10MB
const MAX_BACKUP_COUNT = 5;

function formatBytes(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
}

function rotateLog(filePath) {
  const dir = path.dirname(filePath);
  const basename = path.basename(filePath);
  const ext = path.extname(basename);
  const name = path.basename(basename, ext);

  // 轮转现有的备份文件
  for (let i = MAX_BACKUP_COUNT - 1; i >= 1; i--) {
    const oldBackup = path.join(dir, `${name}.${i}${ext}`);
    const newBackup = path.join(dir, `${name}.${i + 1}${ext}`);

    if (fs.existsSync(oldBackup)) {
      if (i === MAX_BACKUP_COUNT - 1) {
        // 删除最老的备份
        fs.unlinkSync(oldBackup);
        console.log(`🗑️  删除最老的备份: ${oldBackup}`);
      } else {
        fs.renameSync(oldBackup, newBackup);
        console.log(`📦 轮转备份: ${oldBackup} -> ${newBackup}`);
      }
    }
  }

  // 将当前日志文件重命名为 .1 备份
  const firstBackup = path.join(dir, `${name}.1${ext}`);
  fs.renameSync(filePath, firstBackup);
  console.log(`📦 创建备份: ${filePath} -> ${firstBackup}`);

  // 创建新的空日志文件
  fs.writeFileSync(filePath, '');
  console.log(`📝 创建新日志文件: ${filePath}`);
}

function cleanupLogs() {
  console.log('🧹 开始清理日志文件...\n');

  let totalCleaned = 0;
  let filesProcessed = 0;

  // 处理日志目录
  LOG_DIRS.forEach(dir => {
    if (!fs.existsSync(dir)) {
      console.log(`⚠️  目录不存在: ${dir}`);
      return;
    }

    const files = fs.readdirSync(dir);
    files.forEach(file => {
      if (file.endsWith('.log')) {
        const filePath = path.join(dir, file);
        const stats = fs.statSync(filePath);

        console.log(`📄 检查: ${filePath} (${formatBytes(stats.size)})`);
        filesProcessed++;

        if (stats.size > MAX_LOG_SIZE) {
          console.log(`⚠️  文件过大，需要轮转: ${filePath}`);
          rotateLog(filePath);
          totalCleaned += stats.size;
        }
      }
    });
  });

  // 处理单独的日志文件
  LOG_FILES.forEach(filePath => {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  文件不存在: ${filePath}`);
      return;
    }

    const stats = fs.statSync(filePath);
    console.log(`📄 检查: ${filePath} (${formatBytes(stats.size)})`);
    filesProcessed++;

    if (stats.size > MAX_LOG_SIZE) {
      console.log(`⚠️  文件过大，需要轮转: ${filePath}`);
      rotateLog(filePath);
      totalCleaned += stats.size;
    }
  });

  console.log(`\n✅ 日志清理完成!`);
  console.log(`📊 处理文件数: ${filesProcessed}`);
  console.log(`💾 清理空间: ${formatBytes(totalCleaned)}`);

  if (totalCleaned === 0) {
    console.log('🎉 所有日志文件大小正常，无需清理');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  cleanupLogs();
}

module.exports = { cleanupLogs };
