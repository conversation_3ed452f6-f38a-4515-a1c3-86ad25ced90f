#!/usr/bin/env node

/** 开发工具脚本 提供各种开发辅助功能 */

import fs from 'node:fs';
import path from 'node:path';
import { execSync } from 'node:child_process';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

/** 开发工具类 */
class DevTools {
  constructor() {
    this.commands = {
      check: this.runQualityCheck.bind(this),
      fix: this.fixCodeIssues.bind(this),
      test: this.runTests.bind(this),
      build: this.buildProject.bind(this),
      clean: this.cleanProject.bind(this),
      deps: this.checkDependencies.bind(this),
      security: this.securityCheck.bind(this),
      performance: this.performanceCheck.bind(this),
      setup: this.setupProject.bind(this),
      release: this.prepareRelease.bind(this),
      help: this.showHelp.bind(this)
    };
  }

  /** 运行命令 */
  run(command, ...args) {
    if (!this.commands[command]) {
      console.error(`❌ 未知命令: ${command}`);
      this.showHelp();
      process.exit(1);
    }

    try {
      this.commands[command](...args);
    } catch (error) {
      console.error(`❌ 命令执行失败: ${error.message}`);
      process.exit(1);
    }
  }

  /** 代码质量检查 */
  runQualityCheck() {
    console.log('🔍 运行代码质量检查...\n');

    const checks = [
      { name: 'TypeScript 类型检查', command: 'pnpm run type-check' },
      { name: 'ESLint 代码检查', command: 'pnpm run lint' },
      { name: 'Prettier 格式检查', command: 'pnpm run format:check' },
      { name: '重复代码检查', command: 'node scripts/check-duplicates.js' }
    ];

    let allPassed = true;

    for (const check of checks) {
      try {
        console.log(`⏳ ${check.name}...`);
        execSync(check.command, { cwd: projectRoot, stdio: 'pipe' });
        console.log(`✅ ${check.name} 通过`);
      } catch (error) {
        console.log(`❌ ${check.name} 失败`);
        console.log(error.stdout?.toString() || error.message);
        allPassed = false;
      }
    }

    if (allPassed) {
      console.log('\n🎉 所有代码质量检查通过！');
    } else {
      console.log('\n⚠️  部分检查未通过，请修复后重试');
      process.exit(1);
    }
  }

  /** 修复代码问题 */
  fixCodeIssues() {
    console.log('🔧 自动修复代码问题...\n');

    const fixes = [
      { name: 'ESLint 自动修复', command: 'pnpm run lint:fix' },
      { name: 'Prettier 格式化', command: 'pnpm run format' }
    ];

    for (const fix of fixes) {
      try {
        console.log(`⏳ ${fix.name}...`);
        execSync(fix.command, { cwd: projectRoot, stdio: 'inherit' });
        console.log(`✅ ${fix.name} 完成`);
      } catch (error) {
        console.log(`❌ ${fix.name} 失败: ${error.message}`);
      }
    }

    console.log('\n🎉 代码修复完成！');
  }

  /** 运行测试 */
  runTests(type = 'all') {
    console.log(`🧪 运行测试 (${type})...\n`);

    const testCommands = {
      all: ['pnpm run test', 'cd server && pnpm run test'],
      unit: ['pnpm run test:unit'],
      component: ['pnpm run test:component'],
      backend: ['cd server && pnpm run test'],
      coverage: ['pnpm run test:coverage']
    };

    const commands = testCommands[type] || testCommands.all;

    for (const command of commands) {
      try {
        console.log(`⏳ 执行: ${command}`);
        execSync(command, { cwd: projectRoot, stdio: 'inherit' });
        console.log(`✅ 测试完成: ${command}`);
      } catch (error) {
        console.log(`❌ 测试失败: ${command}`);
        process.exit(1);
      }
    }

    console.log('\n🎉 所有测试通过！');
  }

  /** 构建项目 */
  buildProject(env = 'production') {
    console.log(`🏗️  构建项目 (${env})...\n`);

    const buildCommands = ['pnpm run build', 'cd server && pnpm run build'];

    for (const command of buildCommands) {
      try {
        console.log(`⏳ 执行: ${command}`);
        execSync(command, {
          cwd: projectRoot,
          stdio: 'inherit',
          env: { ...process.env, NODE_ENV: env }
        });
        console.log(`✅ 构建完成: ${command}`);
      } catch (error) {
        console.log(`❌ 构建失败: ${command}`);
        process.exit(1);
      }
    }

    console.log('\n🎉 项目构建完成！');
  }

  /** 清理项目 */
  cleanProject() {
    console.log('🧹 清理项目...\n');

    const cleanTargets = ['dist', 'server/dist', 'node_modules/.cache', 'coverage', 'test-results', '.nuxt', '.output'];

    for (const target of cleanTargets) {
      const targetPath = path.join(projectRoot, target);
      if (fs.existsSync(targetPath)) {
        try {
          fs.rmSync(targetPath, { recursive: true, force: true });
          console.log(`✅ 已清理: ${target}`);
        } catch (error) {
          console.log(`❌ 清理失败: ${target} - ${error.message}`);
        }
      } else {
        console.log(`⏭️  跳过: ${target} (不存在)`);
      }
    }

    console.log('\n🎉 项目清理完成！');
  }

  /** 检查依赖 */
  checkDependencies() {
    console.log('📦 检查依赖...\n');

    const checks = [
      { name: '前端依赖检查', command: 'pnpm audit', cwd: projectRoot },
      { name: '后端依赖检查', command: 'pnpm audit', cwd: path.join(projectRoot, 'server') },
      { name: '过期依赖检查', command: 'pnpm outdated', cwd: projectRoot }
    ];

    for (const check of checks) {
      try {
        console.log(`⏳ ${check.name}...`);
        const result = execSync(check.command, {
          cwd: check.cwd,
          stdio: 'pipe',
          encoding: 'utf8'
        });
        console.log(`✅ ${check.name} 通过`);
        if (result.trim()) {
          console.log(result);
        }
      } catch (error) {
        console.log(`⚠️  ${check.name} 发现问题:`);
        console.log(error.stdout?.toString() || error.message);
      }
    }

    console.log('\n📋 依赖检查完成！');
  }

  /** 安全检查 */
  securityCheck() {
    console.log('🔒 运行安全检查...\n');

    const securityChecks = [
      { name: '依赖漏洞扫描', command: 'pnpm audit --audit-level moderate' },
      { name: '敏感信息检查', command: 'git secrets --scan' }
    ];

    let hasIssues = false;

    for (const check of securityChecks) {
      try {
        console.log(`⏳ ${check.name}...`);
        execSync(check.command, { cwd: projectRoot, stdio: 'pipe' });
        console.log(`✅ ${check.name} 通过`);
      } catch (error) {
        console.log(`⚠️  ${check.name} 发现问题:`);
        console.log(error.stdout?.toString() || error.message);
        hasIssues = true;
      }
    }

    if (!hasIssues) {
      console.log('\n🎉 安全检查通过！');
    } else {
      console.log('\n⚠️  发现安全问题，请及时处理');
    }
  }

  /** 性能检查 */
  performanceCheck() {
    console.log('⚡ 运行性能检查...\n');

    // 检查包大小
    try {
      console.log('⏳ 分析包大小...');
      execSync('pnpm run build:analyze', { cwd: projectRoot, stdio: 'inherit' });
      console.log('✅ 包大小分析完成');
    } catch (error) {
      console.log('⚠️  包大小分析失败');
    }

    // 检查代码复杂度
    try {
      console.log('⏳ 检查代码复杂度...');
      execSync('pnpm run complexity', { cwd: projectRoot, stdio: 'inherit' });
      console.log('✅ 代码复杂度检查完成');
    } catch (error) {
      console.log('⚠️  代码复杂度检查失败');
    }

    console.log('\n📊 性能检查完成！');
  }

  /** 项目设置 */
  setupProject() {
    console.log('⚙️  设置项目环境...\n');

    const setupSteps = [
      { name: '安装前端依赖', command: 'pnpm install' },
      { name: '安装后端依赖', command: 'cd server && pnpm install' },
      { name: '设置Git钩子', command: 'pnpm run prepare' },
      { name: '初始化数据库', command: 'cd server && pnpm run db:init' }
    ];

    for (const step of setupSteps) {
      try {
        console.log(`⏳ ${step.name}...`);
        execSync(step.command, { cwd: projectRoot, stdio: 'inherit' });
        console.log(`✅ ${step.name} 完成`);
      } catch (error) {
        console.log(`❌ ${step.name} 失败: ${error.message}`);
      }
    }

    console.log('\n🎉 项目设置完成！');
  }

  /** 准备发布 */
  prepareRelease(version) {
    if (!version) {
      console.error('❌ 请指定版本号，例如: pnpm dev-tools release 1.0.0');
      process.exit(1);
    }

    console.log(`🚀 准备发布版本 ${version}...\n`);

    const releaseSteps = [
      () => this.runQualityCheck(),
      () => this.runTests(),
      () => this.buildProject(),
      () => {
        console.log('⏳ 更新版本号...');
        execSync(`npm version ${version} --no-git-tag-version`, { cwd: projectRoot });
        console.log('✅ 版本号更新完成');
      },
      () => {
        console.log('⏳ 生成变更日志...');
        execSync('pnpm run changelog', { cwd: projectRoot, stdio: 'inherit' });
        console.log('✅ 变更日志生成完成');
      }
    ];

    for (const step of releaseSteps) {
      step();
    }

    console.log(`\n🎉 版本 ${version} 准备完成！`);
    console.log('📝 请检查变更日志并提交代码');
  }

  /** 显示帮助信息 */
  showHelp() {
    console.log(`
🛠️  开发工具脚本

用法: node scripts/dev-tools.js <command> [options]

命令:
  check         运行代码质量检查
  fix           自动修复代码问题
  test [type]   运行测试 (all|unit|component|backend|coverage)
  build [env]   构建项目 (production|development)
  clean         清理项目文件
  deps          检查依赖
  security      运行安全检查
  performance   运行性能检查
  setup         设置项目环境
  release <ver> 准备发布版本
  help          显示帮助信息

示例:
  node scripts/dev-tools.js check
  node scripts/dev-tools.js test unit
  node scripts/dev-tools.js build production
  node scripts/dev-tools.js release 1.0.0
    `);
  }
}

// 运行工具
if (import.meta.url === `file://${process.argv[1]}`) {
  const devTools = new DevTools();
  const [, , command, ...args] = process.argv;

  if (!command) {
    devTools.showHelp();
    process.exit(0);
  }

  devTools.run(command, ...args);
}

export default DevTools;
