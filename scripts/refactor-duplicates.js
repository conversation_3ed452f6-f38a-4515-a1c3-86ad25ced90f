#!/usr/bin/env node

import fs from 'node:fs';
import path from 'node:path';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/** 重构重复代码脚本 自动更新组件以使用共享的工具和组件 */

class DuplicateRefactor {
  constructor() {
    this.projectRoot = process.cwd();
    this.refactoredFiles = [];
    this.errors = [];
  }

  /** 更新文件中的导入语句 */
  updateImports(filePath, oldImport, newImport) {
    try {
      let content = fs.readFileSync(filePath, 'utf8');

      if (content.includes(oldImport)) {
        content = content.replace(oldImport, newImport);
        fs.writeFileSync(filePath, content);
        this.refactoredFiles.push({
          file: filePath,
          change: `更新导入: ${oldImport} -> ${newImport}`
        });
        return true;
      }

      return false;
    } catch (error) {
      this.errors.push({
        file: filePath,
        error: error.message
      });
      return false;
    }
  }

  /** 查找并更新授权相关的导入 */
  refactorAuthImports() {
    console.log('🔄 重构授权相关导入...\n');

    const authImportUpdates = [
      {
        pattern: /from ['"]@\/store\/modules\/auth\/shared['"]/g,
        replacement: "from '@/utils/auth'"
      },
      {
        pattern: /import\s*{\s*getToken\s*,\s*clearAuthStorage\s*}\s*from\s*['"]@\/utils\/storage['"]/g,
        replacement: "import { getToken, clearAuthStorage } from '@/utils/auth'"
      },
      {
        pattern: /localStg\.get\(['"]token['"]\)/g,
        replacement: 'getToken()'
      },
      {
        pattern: /localStg\.get\(['"]refreshToken['"]\)/g,
        replacement: 'getRefreshToken()'
      }
    ];

    // 查找所有Vue和TS文件
    const files = this.findFiles(['src'], ['.vue', '.ts'], ['node_modules', 'dist']);

    files.forEach(file => {
      try {
        let content = fs.readFileSync(file, 'utf8');
        let hasChanges = false;

        authImportUpdates.forEach(update => {
          if (update.pattern.test(content)) {
            content = content.replace(update.pattern, update.replacement);
            hasChanges = true;
          }
        });

        if (hasChanges) {
          fs.writeFileSync(file, content);
          this.refactoredFiles.push({
            file,
            change: '更新授权相关导入'
          });
        }
      } catch (error) {
        this.errors.push({
          file,
          error: error.message
        });
      }
    });
  }

  /** 查找文件 */
  findFiles(dirs, extensions, excludeDirs = []) {
    const files = [];

    function scanDir(dir) {
      try {
        const items = fs.readdirSync(dir);

        items.forEach(item => {
          const fullPath = path.join(dir, item);
          const stat = fs.statSync(fullPath);

          if (stat.isDirectory()) {
            if (!excludeDirs.some(exclude => fullPath.includes(exclude))) {
              scanDir(fullPath);
            }
          } else if (stat.isFile()) {
            const ext = path.extname(item);
            if (extensions.includes(ext)) {
              files.push(fullPath);
            }
          }
        });
      } catch (error) {
        // 忽略无法访问的目录
      }
    }

    dirs.forEach(dir => {
      if (fs.existsSync(dir)) {
        scanDir(dir);
      }
    });

    return files;
  }

  /** 生成重构建议 */
  generateRefactorSuggestions() {
    console.log('\n💡 重构建议:\n');

    const suggestions = [
      {
        title: '表格操作组件重构',
        items: [
          '使用 src/hooks/common/table-operate.ts 替换重复的表格操作逻辑',
          '使用 src/components/common/operate-drawer.vue 替换重复的抽屉组件',
          '统一表格操作的API调用方式'
        ]
      },
      {
        title: '授权逻辑重构',
        items: [
          '✅ 已创建 src/utils/auth.ts 统一授权工具函数',
          '✅ 已更新相关文件使用统一的授权函数',
          '建议在新组件中直接使用 @/utils/auth 中的函数'
        ]
      },
      {
        title: '请求处理重构',
        items: ['统一错误处理逻辑', '提取公共的请求拦截器', '标准化API响应格式处理']
      },
      {
        title: '组件重构',
        items: ['提取公共的表单验证规则', '创建可复用的业务组件', '统一组件的props和events命名']
      }
    ];

    suggestions.forEach(section => {
      console.log(`📋 ${section.title}:`);
      section.items.forEach(item => {
        console.log(`  • ${item}`);
      });
      console.log();
    });
  }

  /** 运行重构 */
  runRefactor() {
    console.log('🚀 开始重构重复代码...\n');

    // 重构授权相关导入
    this.refactorAuthImports();

    // 生成重构建议
    this.generateRefactorSuggestions();

    // 输出结果
    console.log('📊 重构结果:');
    console.log(`✅ 成功重构文件: ${this.refactoredFiles.length}`);
    console.log(`❌ 错误文件: ${this.errors.length}\n`);

    if (this.refactoredFiles.length > 0) {
      console.log('📝 重构的文件:');
      this.refactoredFiles.forEach(item => {
        console.log(`  • ${item.file}: ${item.change}`);
      });
      console.log();
    }

    if (this.errors.length > 0) {
      console.log('⚠️  错误文件:');
      this.errors.forEach(item => {
        console.log(`  • ${item.file}: ${item.error}`);
      });
      console.log();
    }

    console.log('✅ 重复代码重构完成!');

    return {
      refactoredFiles: this.refactoredFiles,
      errors: this.errors,
      timestamp: new Date().toISOString()
    };
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  const refactor = new DuplicateRefactor();
  const result = refactor.runRefactor();

  // 保存结果到文件
  fs.writeFileSync('refactor-report.json', JSON.stringify(result, null, 2));

  console.log('\n📄 重构报告已保存到: refactor-report.json');
}

export default DuplicateRefactor;
