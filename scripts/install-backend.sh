#!/usr/bin/env bash
# One-click backend install & start script for SoybeanAdmin server (BT/宝塔环境友好)
# Usage: bash scripts/install-backend.sh

set -euo pipefail

APP_NAME="soybean-server"
ROOT_DIR="$(cd "$(dirname "$0")/.." && pwd)"
SERVER_DIR="$ROOT_DIR/server"
LOG_DIR="$SERVER_DIR/logs"
ENV_FILE="$SERVER_DIR/.env"
DIST_ENTRY="$SERVER_DIR/dist/index.js"

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
RESET='\033[0m'

log_info()  { echo -e "${GREEN}[INFO]${RESET} $*"; }
log_warn()  { echo -e "${YELLOW}[WARN]${RESET} $*"; }
log_error() { echo -e "${RED}[ERROR]${RESET} $*"; }

need_cmd() {
  if ! command -v "$1" >/dev/null 2>&1; then
    log_error "Missing command: $1"; return 1; fi
}

# Detect OS family: debian|rhel|alpine|unknown
os_family() {
  if [ -f /etc/os-release ]; then
    . /etc/os-release
    case "${ID_LIKE:-$ID}" in
      *debian*) echo debian;;
      *rhel*|*centos*|*fedora*) echo rhel;;
      *alpine*) echo alpine;;
      *) echo unknown;;
    esac
  else
    echo unknown
  fi
}

install_build_tools() {
  local osf; osf=$(os_family)
  log_info "Installing build toolchain for bcrypt (best-effort), OS family: $osf"
  case "$osf" in
    debian)
      sudo apt-get update -y || true
      sudo apt-get install -y python3 make g++ build-essential || true
      ;;
    rhel)
      sudo yum groupinstall -y "Development Tools" || true
      sudo yum install -y python3 || true
      ;;
    alpine)
      sudo apk add --no-cache python3 make g++ || true
      ;;
    *)
      log_warn "Unknown OS. Please ensure python3/make/g++ are installed manually."
      ;;
  esac
}

ensure_node() {
  if ! command -v node >/dev/null 2>&1; then
    log_error "Node.js not installed. Please install Node.js 18+ first."; exit 1
  fi
  local v; v=$(node -v || echo v0)
  log_info "Detected Node.js $v"
}

ensure_corepack_pnpm() {
  if ! command -v corepack >/dev/null 2>&1; then
    log_warn "corepack not found; Node 16+ usually includes it. Proceeding to install pnpm via npm."
    if ! command -v pnpm >/dev/null 2>&1; then
      npm i -g pnpm@8 || { log_error "Failed to install pnpm"; exit 1; }
    fi
  else
    corepack enable || true
    corepack prepare pnpm@8 --activate || true
  fi
  need_cmd pnpm || { log_error "pnpm not available"; exit 1; }
  log_info "pnpm version: $(pnpm -v)"
}

ensure_pm2() {
  if ! command -v pm2 >/dev/null 2>&1; then
    log_warn "pm2 not found, installing globally via pnpm (fallback npm)"
    (pnpm add -g pm2 || npm i -g pm2) >/dev/null 2>&1 || true
  fi
  need_cmd pm2 || { log_error "pm2 is required"; exit 1; }
  log_info "pm2 version: $(pm2 -v)"
}

prepare_env() {
  mkdir -p "$LOG_DIR"
  if [ ! -f "$ENV_FILE" ]; then
    log_warn ".env not found at $ENV_FILE"
    cat > "$ENV_FILE" <<'EOF'
# Generated by install-backend.sh - adjust values as needed
NODE_ENV=production
PORT=3000
DB_HOST=localhost
DB_PORT=3306
DB_USER=newfd
DB_PASSWORD=jx7KFPLnEsr4fzi7
DB_NAME=newfd
# LICENSE_ENCRYPTION_KEY=change_me_32_chars
# LICENSE_SIGNATURE_SECRET=change_me
TZ=Asia/Shanghai
EOF
    log_info "Created default .env at $ENV_FILE (please review)"
  fi
}

is_workspace_root() {
  [ -f "$ROOT_DIR/pnpm-workspace.yaml" ] && [ -d "$SERVER_DIR" ]
}

install_deps_and_build() {
  local need_build="no"
  if [ ! -f "$DIST_ENTRY" ]; then
    need_build="yes"; log_info "server/dist not found, will install dev deps and build"
  fi

  if is_workspace_root; then
    if [ "$need_build" = "yes" ]; then
      pnpm -F server install
      pnpm -F server run build
    else
      pnpm -F server install --prod
    fi
    # Rebuild bcrypt to current OS
    (pnpm -F server rebuild bcrypt || true)
  else
    # standalone mode
    cd "$SERVER_DIR"
    if [ "$need_build" = "yes" ]; then
      pnpm install
      pnpm run build
    else
      pnpm install --prod
    fi
    (pnpm rebuild bcrypt || npm rebuild bcrypt || true)
  fi
}

start_pm2() {
  if pm2 describe "$APP_NAME" >/dev/null 2>&1; then
    log_info "Restarting existing PM2 app: $APP_NAME"
    pm2 restart "$APP_NAME"
  else
    log_info "Starting PM2 app: $APP_NAME"
    pm2 start "$DIST_ENTRY" --name "$APP_NAME"
  fi
  pm2 save || true
}

print_nginx_hint() {
  cat <<'NGINX'

[Hint] Configure Nginx (BT 面板站点) with:
  - Static root: <frontend dist/>
  - Proxy /api -> http://127.0.0.1:3000
  - Enable gzip and SPA fallback to index.html

Example proxy:
  location /api/ {
    proxy_pass http://127.0.0.1:3000/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }
NGINX
}

main() {
  log_info "Root: $ROOT_DIR"
  log_info "Server: $SERVER_DIR"
  ensure_node
  install_build_tools
  ensure_corepack_pnpm
  ensure_pm2
  prepare_env
  install_deps_and_build
  start_pm2
  log_info "Backend deployed and running under PM2: $APP_NAME"
  print_nginx_hint
}

main "$@"

