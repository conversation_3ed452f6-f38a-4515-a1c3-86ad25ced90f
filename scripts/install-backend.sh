#!/usr/bin/env bash
# SoybeanAdmin 前后端一键部署脚本 (宝塔环境友好)
# Usage: bash scripts/install-backend.sh

set -euo pipefail

APP_NAME="soybean-server"
ROOT_DIR="$(cd "$(dirname "$0")/.." && pwd)"
SERVER_DIR="$ROOT_DIR/server"
FRONTEND_DIST="$ROOT_DIR/dist"
LOG_DIR="$SERVER_DIR/logs"
ENV_FILE="$SERVER_DIR/.env"
DIST_ENTRY="$SERVER_DIR/dist/index.js"

GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
RESET='\033[0m'

log_info()  { echo -e "${GREEN}[INFO]${RESET} $*"; }
log_warn()  { echo -e "${YELLOW}[WARN]${RESET} $*"; }
log_error() { echo -e "${RED}[ERROR]${RESET} $*"; }

need_cmd() {
  if ! command -v "$1" >/dev/null 2>&1; then
    log_error "Missing command: $1"; return 1; fi
}

# Detect OS family: debian|rhel|alpine|unknown
os_family() {
  if [ -f /etc/os-release ]; then
    . /etc/os-release
    case "${ID_LIKE:-$ID}" in
      *debian*) echo debian;;
      *rhel*|*centos*|*fedora*) echo rhel;;
      *alpine*) echo alpine;;
      *) echo unknown;;
    esac
  else
    echo unknown
  fi
}

install_build_tools() {
  local osf; osf=$(os_family)
  log_info "Installing build toolchain for bcrypt (best-effort), OS family: $osf"
  case "$osf" in
    debian)
      sudo apt-get update -y || true
      sudo apt-get install -y python3 make g++ build-essential || true
      ;;
    rhel)
      sudo yum groupinstall -y "Development Tools" || true
      sudo yum install -y python3 || true
      ;;
    alpine)
      sudo apk add --no-cache python3 make g++ || true
      ;;
    *)
      log_warn "Unknown OS. Please ensure python3/make/g++ are installed manually."
      ;;
  esac
}

ensure_node() {
  if ! command -v node >/dev/null 2>&1; then
    log_error "Node.js not installed. Please install Node.js 18+ first."; exit 1
  fi
  local v; v=$(node -v || echo v0)
  log_info "Detected Node.js $v"
}

ensure_corepack_pnpm() {
  if ! command -v corepack >/dev/null 2>&1; then
    log_warn "corepack not found; Node 16+ usually includes it. Proceeding to install pnpm via npm."
    if ! command -v pnpm >/dev/null 2>&1; then
      npm i -g pnpm@8 || { log_error "Failed to install pnpm"; exit 1; }
    fi
  else
    corepack enable || true
    corepack prepare pnpm@8 --activate || true
  fi
  need_cmd pnpm || { log_error "pnpm not available"; exit 1; }
  log_info "pnpm version: $(pnpm -v)"
}

ensure_pm2() {
  if ! command -v pm2 >/dev/null 2>&1; then
    log_warn "pm2 not found, installing globally via pnpm (fallback npm)"
    (pnpm add -g pm2 || npm i -g pm2) >/dev/null 2>&1 || true
  fi
  need_cmd pm2 || { log_error "pm2 is required"; exit 1; }
  log_info "pm2 version: $(pm2 -v)"
}

prepare_env() {
  mkdir -p "$LOG_DIR"
  if [ ! -f "$ENV_FILE" ]; then
    log_warn ".env not found at $ENV_FILE"
    cat > "$ENV_FILE" <<'EOF'
# Generated by install-backend.sh - adjust values as needed
NODE_ENV=production
PORT=3000
DB_HOST=localhost
DB_PORT=3306
DB_USER=newfd
DB_PASSWORD=jx7KFPLnEsr4fzi7
DB_NAME=newfd
# LICENSE_ENCRYPTION_KEY=change_me_32_chars
# LICENSE_SIGNATURE_SECRET=change_me
TZ=Asia/Shanghai
EOF
    log_info "Created default .env at $ENV_FILE (please review)"
  fi
}

is_workspace_root() {
  [ -f "$ROOT_DIR/pnpm-workspace.yaml" ] && [ -d "$SERVER_DIR" ]
}

build_frontend() {
  log_info "开始构建前端..."
  cd "$ROOT_DIR"

  # 检查前端依赖
  if [ ! -d "node_modules" ]; then
    log_info "安装前端依赖..."
    pnpm install
  fi

  # 构建前端
  log_info "构建前端项目..."
  pnpm build

  if [ -d "$FRONTEND_DIST" ]; then
    log_info "✅ 前端构建完成: $FRONTEND_DIST"
  else
    log_error "❌ 前端构建失败"
    exit 1
  fi
}

install_deps_and_build() {
  local need_build="no"
  if [ ! -f "$DIST_ENTRY" ]; then
    need_build="yes"; log_info "server/dist not found, will install dev deps and build"
  fi

  if is_workspace_root; then
    if [ "$need_build" = "yes" ]; then
      pnpm -F server install
      pnpm -F server run build
    else
      pnpm -F server install --prod
    fi
    # Rebuild bcrypt to current OS
    (pnpm -F server rebuild bcrypt || true)
  else
    # standalone mode
    cd "$SERVER_DIR"
    if [ "$need_build" = "yes" ]; then
      pnpm install
      pnpm run build
    else
      pnpm install --prod
    fi
    (pnpm rebuild bcrypt || npm rebuild bcrypt || true)
  fi
}

start_pm2() {
  if pm2 describe "$APP_NAME" >/dev/null 2>&1; then
    log_info "Restarting existing PM2 app: $APP_NAME"
    pm2 restart "$APP_NAME"
  else
    log_info "Starting PM2 app: $APP_NAME"
    pm2 start "$DIST_ENTRY" --name "$APP_NAME"
  fi
  pm2 save || true
}

create_nginx_config() {
  log_info "创建Nginx配置文件..."

  # 获取当前服务器IP
  local server_ip=$(curl -s ifconfig.me 2>/dev/null || echo "your-server-ip")

  cat > "$ROOT_DIR/nginx-config-example.conf" <<EOF
# SoybeanAdmin Nginx配置示例
# 请将此配置添加到您的宝塔站点配置中

server {
    listen 6666;  # 您的端口
    server_name ${server_ip}_6666;  # 您的服务器名
    root $FRONTEND_DIST;  # 前端文件路径
    index index.html;

    # API反向代理 - 将前端API请求转发到后端
    location /api/ {
        proxy_pass http://127.0.0.1:3000/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        proxy_buffering off;
    }

    # SPA路由支持 - 让Vue路由正常工作
    location / {
        try_files \$uri \$uri/ /index.html;
    }

    # 静态资源缓存优化
    location ~* \\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)\$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # HTML文件不缓存
    location ~* \\.html\$ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    # Gzip压缩 - 让网站加载更快
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_comp_level 6;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 安全配置
    location ~ ^/(\\.user\\.ini|\\.htaccess|\\.git|\\.env|\\.svn|\\.project|LICENSE|README\\.md) {
        return 404;
    }
}
EOF

  log_info "✅ Nginx配置示例已生成: $ROOT_DIR/nginx-config-example.conf"
}

print_deployment_guide() {
  cat <<'GUIDE'

🎉 部署完成！接下来请按以下步骤配置：

📋 第一步：在宝塔面板中配置站点
1. 打开宝塔面板 → 网站 → 添加站点
2. 域名填写：您的IP_端口 (如: ************_6666)
3. 根目录设置为：/www/wwwroot/FDnew/SoybeanAdmin/dist
4. 不需要创建数据库和FTP

📋 第二步：配置Nginx
1. 点击站点设置 → 配置文件
2. 将生成的 nginx-config-example.conf 内容复制到配置文件中
3. 保存并重载Nginx

📋 第三步：验证部署
1. 访问：http://您的IP:端口 (如: http://************:6666)
2. 测试API：http://您的IP:端口/api/license/status
3. 使用账号 admin/123456 登录测试

🔧 如果遇到问题：
- 检查后端服务：pm2 status
- 查看后端日志：pm2 logs soybean-server
- 检查Nginx配置：nginx -t
- 重载Nginx：nginx -s reload

GUIDE
}

main() {
  log_info "🚀 开始部署SoybeanAdmin前后端..."
  log_info "项目路径: $ROOT_DIR"
  log_info "后端路径: $SERVER_DIR"
  log_info "前端构建路径: $FRONTEND_DIST"

  # 环境检查
  ensure_node
  install_build_tools
  ensure_corepack_pnpm
  ensure_pm2

  # 环境配置
  prepare_env

  # 构建前端
  build_frontend

  # 构建并启动后端
  install_deps_and_build
  start_pm2

  # 生成配置文件
  create_nginx_config

  log_info "✅ 前后端部署完成！"
  print_deployment_guide
}

main "$@"

