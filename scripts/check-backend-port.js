#!/usr/bin/env node

/** 后端端口检测和前端配置更新脚本 自动检测后端实际运行端口，并更新前端环境变量配置 */

import fs from 'node:fs';
import path from 'node:path';
import net from 'node:net';
import http from 'node:http';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 配置
const BACKEND_PORTS = [3000, 3001, 3002, 3003, 3004]; // 可能的后端端口
const ENV_FILE = path.join(__dirname, '../.env.development');

/** 检查端口是否被占用 */
function checkPort(port) {
  return new Promise(resolve => {
    const server = net.createServer();

    server.listen(port, () => {
      server.once('close', () => {
        resolve(false); // 端口可用
      });
      server.close();
    });

    server.on('error', () => {
      resolve(true); // 端口被占用
    });
  });
}

/** 检测后端服务是否在指定端口运行 */
async function detectBackendPort() {
  console.log('🔍 检测后端服务端口...');

  for (const port of BACKEND_PORTS) {
    const isOccupied = await checkPort(port);

    if (isOccupied) {
      // 检查是否是我们的后端服务
      try {
        // 使用 http 模块检测服务
        const isServiceRunning = await new Promise(resolve => {
          const req = http.get(`http://localhost:${port}/api/license/status`, res => {
            resolve(res.statusCode === 200 || res.statusCode === 401 || res.statusCode === 403);
          });

          req.on('error', () => {
            resolve(false);
          });

          req.setTimeout(3000, () => {
            req.destroy();
            resolve(false);
          });
        });

        if (isServiceRunning) {
          console.log(`✅ 检测到后端服务运行在端口 ${port}`);
          return port;
        }
      } catch (error) {
        // 不是我们的服务，继续检查下一个端口
      }
    }
  }

  console.log('⚠️  未检测到后端服务，使用默认端口 3000');
  return 3000;
}

/** 更新前端环境变量文件 */
function updateEnvFile(port) {
  try {
    let envContent = fs.readFileSync(ENV_FILE, 'utf8');

    // 更新 VITE_SERVICE_BASE_URL
    const baseUrlRegex = /VITE_SERVICE_BASE_URL=http:\/\/localhost:\d+/;
    const newBaseUrl = `VITE_SERVICE_BASE_URL=http://localhost:${port}`;

    if (baseUrlRegex.test(envContent)) {
      envContent = envContent.replace(baseUrlRegex, newBaseUrl);
    } else {
      // 如果没有找到，添加到文件末尾
      envContent += `\n${newBaseUrl}\n`;
    }

    // 更新 VITE_BACKEND_PORT
    const portRegex = /VITE_BACKEND_PORT=\d+/;
    const newPort = `VITE_BACKEND_PORT=${port}`;

    if (portRegex.test(envContent)) {
      envContent = envContent.replace(portRegex, newPort);
    } else {
      envContent += `${newPort}\n`;
    }

    fs.writeFileSync(ENV_FILE, envContent);
    console.log(`✅ 已更新前端配置，后端端口: ${port}`);
  } catch (error) {
    console.error('❌ 更新环境变量文件失败:', error.message);
  }
}

/** 主函数 */
async function main() {
  console.log('🚀 启动后端端口检测...');

  const backendPort = await detectBackendPort();
  updateEnvFile(backendPort);

  console.log('✅ 端口检测完成');
  console.log(`📋 后端服务地址: http://localhost:${backendPort}`);
  console.log('💡 提示: 如果后端端口发生变化，请重新运行此脚本');
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}

export { detectBackendPort, updateEnvFile };
