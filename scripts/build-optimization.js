#!/usr/bin/env node

import fs from 'node:fs';
import path from 'node:path';
import { execSync } from 'node:child_process';
import { fileURLToPath } from 'node:url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/** 构建优化脚本 检查和优化构建配置，移除不必要的依赖 */

class BuildOptimizer {
  constructor() {
    this.projectRoot = process.cwd();
    this.packageJson = this.loadPackageJson();
    this.unusedDeps = [];
    this.optimizations = [];
  }

  loadPackageJson() {
    const packagePath = path.join(this.projectRoot, 'package.json');
    return JSON.parse(fs.readFileSync(packagePath, 'utf8'));
  }

  /** 检查依赖使用情况 */
  checkDependencyUsage() {
    console.log('🔍 检查依赖使用情况...\n');

    const dependencies = {
      ...this.packageJson.dependencies,
      ...this.packageJson.devDependencies
    };

    // 可能未使用的依赖列表（需要手动检查）
    const suspiciousDeps = [
      'dhtmlx-gantt',
      'wangeditor',
      'vue-pdf-embed',
      'xgplayer',
      'jsbarcode',
      'print-js',
      'vditor',
      'pinyin-pro'
    ];

    console.log('📦 检查可能未使用的依赖:');
    suspiciousDeps.forEach(dep => {
      if (dependencies[dep]) {
        const isUsed = this.checkIfDependencyIsUsed(dep);
        console.log(`  ${dep}: ${isUsed ? '✅ 使用中' : '⚠️  可能未使用'}`);
        if (!isUsed) {
          this.unusedDeps.push(dep);
        }
      }
    });

    return this.unusedDeps;
  }

  /** 检查依赖是否被使用 */
  checkIfDependencyIsUsed(depName) {
    try {
      // 检查源码中是否有import或require
      const result = execSync(
        `grep -r "from '${depName}'" src/ --include="*.vue" --include="*.ts" --include="*.js" || ` +
          `grep -r "import.*'${depName}'" src/ --include="*.vue" --include="*.ts" --include="*.js" || ` +
          `grep -r "require.*'${depName}'" src/ --include="*.vue" --include="*.ts" --include="*.js"`,
        { encoding: 'utf8', stdio: 'pipe' }
      );
      return result.trim().length > 0;
    } catch (error) {
      return false;
    }
  }

  /** 分析构建配置 */
  analyzeBuildConfig() {
    console.log('\n🔧 分析构建配置...\n');

    const viteConfigPath = path.join(this.projectRoot, 'vite.config.ts');
    if (fs.existsSync(viteConfigPath)) {
      const viteConfig = fs.readFileSync(viteConfigPath, 'utf8');

      // 检查是否启用了源码映射
      if (viteConfig.includes('sourcemap: true') || viteConfig.includes("sourcemap: viteEnv.VITE_SOURCE_MAP === 'Y'")) {
        console.log('📍 源码映射: 已配置 (根据环境变量控制)');
      }

      // 检查是否启用了压缩报告
      if (viteConfig.includes('reportCompressedSize: false')) {
        console.log('📊 压缩报告: 已禁用 (提升构建速度)');
      }

      // 检查代码分割配置
      if (viteConfig.includes('manualChunks')) {
        console.log('📦 代码分割: 已配置');
      } else {
        this.optimizations.push('建议配置代码分割以优化加载性能');
      }
    }

    return this.optimizations;
  }

  /** 检查TypeScript配置 */
  checkTypeScriptConfig() {
    console.log('\n📝 检查TypeScript配置...\n');

    const tsconfigPath = path.join(this.projectRoot, 'tsconfig.json');
    if (fs.existsSync(tsconfigPath)) {
      const tsconfig = JSON.parse(fs.readFileSync(tsconfigPath, 'utf8'));

      // 检查严格模式
      if (tsconfig.compilerOptions?.strict) {
        console.log('🔒 TypeScript严格模式: 已启用');
      }

      // 检查未使用的本地变量检查
      if (tsconfig.compilerOptions?.noUnusedLocals) {
        console.log('🧹 未使用变量检查: 已启用');
      }

      // 检查跳过库检查
      if (tsconfig.compilerOptions?.skipLibCheck) {
        console.log('⚡ 跳过库检查: 已启用 (提升编译速度)');
      }
    }
  }

  /** 检查ESLint配置 */
  checkESLintConfig() {
    console.log('\n🔍 检查ESLint配置...\n');

    const eslintConfigPath = path.join(this.projectRoot, 'eslint.config.js');
    if (fs.existsSync(eslintConfigPath)) {
      console.log('✅ ESLint配置: 已找到');

      // 检查是否有性能相关的规则
      const eslintConfig = fs.readFileSync(eslintConfigPath, 'utf8');
      if (eslintConfig.includes('no-console')) {
        console.log('🚫 控制台输出检查: 已配置');
      }
    }
  }

  /** 生成优化建议 */
  generateOptimizationSuggestions() {
    console.log('\n💡 构建优化建议:\n');

    const suggestions = [
      {
        title: '依赖优化',
        items: [
          this.unusedDeps.length > 0 ? `移除未使用的依赖: ${this.unusedDeps.join(', ')}` : '✅ 依赖使用合理',
          '定期运行 pnpm audit 检查安全漏洞',
          '使用 pnpm update-pkg 更新依赖版本'
        ]
      },
      {
        title: '构建性能',
        items: ['启用 Vite 的 esbuild 进行快速构建', '配置合理的代码分割策略', '生产环境禁用源码映射以减小包体积']
      },
      {
        title: '代码质量',
        items: ['保持 TypeScript 严格模式', '启用 ESLint 的性能相关规则', '定期运行代码重复检查']
      }
    ];

    suggestions.forEach(section => {
      console.log(`📋 ${section.title}:`);
      section.items.forEach(item => {
        console.log(`  • ${item}`);
      });
      console.log();
    });
  }

  /** 运行完整的优化检查 */
  runOptimization() {
    console.log('🚀 开始构建配置优化检查...\n');

    this.checkDependencyUsage();
    this.analyzeBuildConfig();
    this.checkTypeScriptConfig();
    this.checkESLintConfig();
    this.generateOptimizationSuggestions();

    console.log('✅ 构建优化检查完成!');

    return {
      unusedDependencies: this.unusedDeps,
      optimizations: this.optimizations,
      timestamp: new Date().toISOString()
    };
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  const optimizer = new BuildOptimizer();
  const result = optimizer.runOptimization();

  // 保存结果到文件
  fs.writeFileSync('build-optimization-report.json', JSON.stringify(result, null, 2));

  console.log('\n📄 优化报告已保存到: build-optimization-report.json');
}

export default BuildOptimizer;
