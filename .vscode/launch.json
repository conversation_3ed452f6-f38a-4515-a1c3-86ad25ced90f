{"version": "0.2.0", "configurations": [{"type": "chrome", "request": "launch", "name": "<PERSON><PERSON>", "url": "http://localhost:3000", "webRoot": "${workspaceFolder}"}, {"type": "node", "request": "launch", "name": "TS Debugger", "runtimeExecutable": "tsx", "skipFiles": ["<node_internals>/**", "${workspaceFolder}/node_modules/**"], "program": "${file}", "console": "integratedTerminal", "internalConsoleOptions": "neverOpen"}]}