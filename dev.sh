#!/bin/bash

# 多平台网课商品管理系统 - 智能开发环境管理脚本
# 功能：自动检测状态，智能启动/停止/重启/状态检查

# 项目配置
PROJECT_PATH="/www/wwwroot/FDnew/SoybeanAdmin"
PROJECT_NAME="多平台网课商品管理系统"
BACKEND_PORT=3000
FRONTEND_PORT=5959
FRONTEND_ALT_PORT=5960

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

# 显示帮助信息
show_help() {
    echo "🚀 智能开发环境管理工具"
    echo "================================================"
    echo "用法: $0 [命令]"
    echo ""
    echo "可用命令:"
    echo "  start     启动开发环境"
    echo "  stop      安全停止开发环境"
    echo "  restart   重启开发环境"
    echo "  status    查看状态"
    echo "  check     详细检查"
    echo "  logs      查看日志"
    echo "  clean     清理环境"
    echo "  help      显示帮助"
    echo ""
    echo "无参数运行时，将根据当前状态智能选择操作："
    echo "  - 如果服务未运行，则启动"
    echo "  - 如果服务正在运行，则显示状态"
    echo "  - 如果服务异常，则提供修复选项"
    echo ""
    echo "示例:"
    echo "  $0           # 智能模式"
    echo "  $0 start     # 强制启动"
    echo "  $0 status    # 查看状态"
    echo "  $0 restart   # 重启服务"
    echo "================================================"
}

# 检查进程是否存在
check_process() {
    local pid=$1
    if [ -z "$pid" ]; then
        return 1
    fi
    kill -0 $pid 2>/dev/null
}

# 检查端口是否被占用
check_port() {
    local port=$1
    lsof -ti:$port 2>/dev/null
}

# 获取项目进程状态
get_project_status() {
    local backend_status="stopped"
    local frontend_status="stopped"
    local backend_pid=""
    local frontend_pid=""

    # 检查PID文件
    if [ -f "logs/backend.pid" ]; then
        backend_pid=$(cat logs/backend.pid)
        if check_process $backend_pid; then
            backend_status="running"
        else
            backend_status="dead"
        fi
    fi

    if [ -f "logs/frontend.pid" ]; then
        frontend_pid=$(cat logs/frontend.pid)
        if check_process $frontend_pid; then
            frontend_status="running"
        else
            frontend_status="dead"
        fi
    fi

    # 检查端口占用（可能是其他实例）
    local backend_port_pid=$(check_port $BACKEND_PORT)
    local frontend_port_pid=$(check_port $FRONTEND_PORT)
    local frontend_alt_port_pid=$(check_port $FRONTEND_ALT_PORT)

    if [ ! -z "$backend_port_pid" ] && [ "$backend_status" = "stopped" ]; then
        backend_status="unknown"
    fi

    if [ ! -z "$frontend_port_pid" ] && [ "$frontend_status" = "stopped" ]; then
        frontend_status="unknown"
    elif [ ! -z "$frontend_alt_port_pid" ] && [ "$frontend_status" = "stopped" ]; then
        frontend_status="unknown"
    fi

    echo "$backend_status:$frontend_status:$backend_pid:$frontend_pid"
}

# 显示状态
show_status() {
    local detailed=${1:-false}

    print_message $CYAN "🔍 $PROJECT_NAME 状态检查"
    echo "================================================"

    local status=$(get_project_status)
    local backend_status=$(echo $status | cut -d: -f1)
    local frontend_status=$(echo $status | cut -d: -f2)
    local backend_pid=$(echo $status | cut -d: -f3)
    local frontend_pid=$(echo $status | cut -d: -f4)

    # 后端状态
    echo -n "📡 后端服务 (端口 $BACKEND_PORT): "
    case $backend_status in
        "running")
            print_message $GREEN "✅ 运行中 (PID: $backend_pid)"
            ;;
        "dead")
            print_message $YELLOW "⚠️  进程已停止 (PID文件存在: $backend_pid)"
            ;;
        "unknown")
            local port_pid=$(check_port $BACKEND_PORT)
            local first_pid=$(echo $port_pid | awk '{print $1}')
            print_message $YELLOW "⚠️  端口被占用 (PID: $first_pid，非项目进程)"
            ;;
        *)
            print_message $RED "❌ 未运行"
            ;;
    esac

    # 前端状态
    echo -n "🌐 前端服务 (端口 $FRONTEND_PORT/$FRONTEND_ALT_PORT): "
    case $frontend_status in
        "running")
            print_message $GREEN "✅ 运行中 (PID: $frontend_pid)"
            ;;
        "dead")
            print_message $YELLOW "⚠️  进程已停止 (PID文件存在: $frontend_pid)"
            ;;
        "unknown")
            local port_pid=$(check_port $FRONTEND_PORT)
            if [ -z "$port_pid" ]; then
                port_pid=$(check_port $FRONTEND_ALT_PORT)
            fi
            # 只显示第一个PID
            local first_pid=$(echo $port_pid | awk '{print $1}')
            print_message $YELLOW "⚠️  端口被占用 (PID: $first_pid，非项目进程)"
            ;;
        *)
            print_message $RED "❌ 未运行"
            ;;
    esac

    # 详细信息
    if [ "$detailed" = "true" ]; then
        echo ""
        echo "📊 详细信息:"
        echo "----------------------------------------"

        # 端口占用详情
        for port in $BACKEND_PORT $FRONTEND_PORT $FRONTEND_ALT_PORT; do
            local port_info=$(lsof -ti:$port 2>/dev/null)
            if [ ! -z "$port_info" ]; then
                local process_info=$(ps -p $port_info -o pid,user,args= 2>/dev/null)
                echo "端口 $port: $process_info"
            else
                echo "端口 $port: 未占用"
            fi
        done

        echo ""
        echo "📄 PID文件:"
        [ -f "logs/backend.pid" ] && echo "后端: $(cat logs/backend.pid)" || echo "后端: 不存在"
        [ -f "logs/frontend.pid" ] && echo "前端: $(cat logs/frontend.pid)" || echo "前端: 不存在"

        echo ""
        echo "🔒 系统状态:"
        echo "SSH进程: $(ps aux | grep sshd | grep -v grep | wc -l)"
        echo "VSCode Server: $(ps aux | grep vscode-server | grep -v grep | wc -l)"
        echo "系统负载: $(uptime | awk -F'load average:' '{print $2}')"
    fi

    echo ""

    # 返回整体状态
    if [ "$backend_status" = "running" ] && [ "$frontend_status" = "running" ]; then
        return 0  # 全部运行
    elif [ "$backend_status" = "stopped" ] && [ "$frontend_status" = "stopped" ]; then
        return 1  # 全部停止
    else
        return 2  # 部分运行或异常
    fi
}

# 智能模式主逻辑
intelligent_mode() {
    print_message $PURPLE "🤖 智能模式启动"
    echo "================================================"

    show_status false
    local status_code=$?

    case $status_code in
        0)
            print_message $GREEN "✅ 开发环境正在正常运行"
            echo ""
            echo "🌐 访问地址:"
            echo "   前端: http://localhost:$FRONTEND_PORT/"
            echo "   后端: http://localhost:$BACKEND_PORT/"
            echo ""
            echo "🛠️  可用操作:"
            echo "   $0 stop     - 停止服务"
            echo "   $0 restart  - 重启服务"
            echo "   $0 logs     - 查看日志"
            echo "   $0 check    - 详细检查"
            ;;
        1)
            print_message $YELLOW "🚀 开发环境未运行，准备启动..."
            echo ""
            read -p "是否启动开发环境？(Y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]] || [[ -z $REPLY ]]; then
                start_services
            else
                echo "取消启动"
            fi
            ;;
        2)
            print_message $YELLOW "⚠️  开发环境状态异常"
            echo ""
            echo "🔧 建议操作:"
            echo "   1. 重启服务: $0 restart"
            echo "   2. 清理环境: $0 clean"
            echo "   3. 详细检查: $0 check"
            echo ""
            read -p "选择操作 (1-3) 或按 Enter 跳过: " -n 1 -r
            echo
            case $REPLY in
                1)
                    restart_services
                    ;;
                2)
                    clean_environment
                    ;;
                3)
                    show_status true
                    ;;
                *)
                    echo "跳过操作"
                    ;;
            esac
            ;;
    esac
}

# 启动服务：直接在当前脚本内启动前后端（不再委托外部脚本）
start_services() {
    print_message $PURPLE "🚀 启动前后端开发环境"

    # 确保日志目录存在
    mkdir -p logs

    # 尝试启用 pnpm（优先），否则回退 npm
    if ! command -v pnpm >/dev/null 2>&1; then
        if command -v corepack >/dev/null 2>&1; then
            corepack enable >/dev/null 2>&1 || true
            corepack prepare pnpm@8 --activate >/dev/null 2>&1 || true
        fi
    fi
    if ! command -v pnpm >/dev/null 2>&1; then
        print_message $YELLOW "⚠️ 未检测到 pnpm，将回退使用 npm（建议安装 pnpm 以获得更快的安装速度）"
    fi

    # 启动后端
    if [ -n "$(check_port $BACKEND_PORT)" ]; then
        print_message $GREEN "📡 后端已在端口 $BACKEND_PORT 运行，跳过启动"
    else
        print_message $CYAN "📡 启动后端（dev）..."
        pushd "$PROJECT_PATH/server" >/dev/null
        if [ ! -d "node_modules" ]; then
            print_message $CYAN "安装后端依赖..."
            if command -v pnpm >/dev/null 2>&1; then pnpm install; else npm install; fi
        fi
        if command -v pnpm >/dev/null 2>&1; then
            nohup pnpm dev > "$PROJECT_PATH/logs/backend.log" 2>&1 &
        else
            nohup npm run dev > "$PROJECT_PATH/logs/backend.log" 2>&1 &
        fi
        echo $! > "$PROJECT_PATH/logs/backend.pid"
        popd >/dev/null
    fi

    # 启动前端（优先 5959，若被占用则仍让 Vite 自行选择空闲端口）
    if [ -n "$(check_port $FRONTEND_PORT)" ] || [ -n "$(check_port $FRONTEND_ALT_PORT)" ]; then
        print_message $GREEN "🌐 前端已在运行，跳过启动"
    else
        print_message $CYAN "🌐 启动前端（dev）..."
        pushd "$PROJECT_PATH" >/dev/null
        if [ ! -d "node_modules" ]; then
            print_message $CYAN "安装前端依赖..."
            if command -v pnpm >/dev/null 2>&1; then pnpm install; else npm install; fi
        fi
        if command -v pnpm >/dev/null 2>&1; then
            nohup pnpm dev > "logs/frontend.log" 2>&1 &
        else
            nohup npm run dev > "logs/frontend.log" 2>&1 &
        fi
        echo $! > "logs/frontend.pid"
        popd >/dev/null
    fi

    print_message $GREEN "✅ 启动流程已触发，稍等几秒后可用 '$0 status' 查看状态"
}

# 停止服务（调用安全停止脚本）
stop_services() {
    if [ -f "./stop-dev-safe.sh" ]; then
        ./stop-dev-safe.sh
    else
        ./stop-dev.sh
    fi
}

# 重启服务
restart_services() {
    print_message $YELLOW "🔄 重启开发环境"
    echo "================================================"

    echo "🛑 停止现有服务..."
    stop_services

    echo ""
    echo "⏳ 等待服务完全停止..."
    sleep 3

    echo "🚀 启动服务..."
    start_services
}

# 查看日志
show_logs() {
    local log_type=${1:-both}

    case $log_type in
        "backend"|"be")
            if [ -f "logs/backend.log" ]; then
                print_message $CYAN "📡 后端日志 (最后50行):"
                echo "----------------------------------------"
                tail -50 logs/backend.log
            else
                print_message $RED "❌ 后端日志文件不存在"
            fi
            ;;
        "frontend"|"fe")
            if [ -f "logs/frontend.log" ]; then
                print_message $CYAN "🌐 前端日志 (最后50行):"
                echo "----------------------------------------"
                tail -50 logs/frontend.log
            else
                print_message $RED "❌ 前端日志文件不存在"
            fi
            ;;
        *)
            show_logs "backend"
            echo ""
            show_logs "frontend"
            ;;
    esac
}

# 清理环境
clean_environment() {
    print_message $YELLOW "🧹 清理开发环境"
    echo "================================================"

    echo "🛑 停止所有服务..."
    stop_services

    echo "🗑️  清理PID文件..."
    rm -f logs/*.pid

    echo "🗑️  清理日志文件..."
    rm -f logs/*.log

    echo "🔍 检查残留进程..."
    ./check-processes.sh

    print_message $GREEN "✅ 环境清理完成"
}

# 主程序
main() {
    # 确保在项目目录中
    if [ ! -f "package.json" ] || [ ! -d "server" ]; then
        print_message $RED "❌ 错误: 请在项目根目录中运行此脚本"
        exit 1
    fi

    # 创建日志目录
    mkdir -p logs

    # 解析命令行参数
    case ${1:-intelligent} in
        "start"|"s")
            start_services
            ;;
        "stop"|"st")
            stop_services
            ;;
        "restart"|"r")
            restart_services
            ;;
        "status"|"stat")
            show_status false
            ;;
        "check"|"c")
            show_status true
            ;;
        "logs"|"log"|"l")
            show_logs ${2:-both}
            ;;
        "clean"|"cl")
            clean_environment
            ;;
        "help"|"h"|"-h"|"--help")
            show_help
            ;;
        "intelligent"|"")
            intelligent_mode
            ;;
        *)
            print_message $RED "❌ 未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 运行主程序
main "$@"
