# SoybeanAdmin 内存优化指南

## 🎯 优化目标

避免前端重复启动，减少内存占用，提升开发体验。

## 📊 当前内存使用情况

根据监控结果：
- **前端服务(Vite)**: ~196MB (1个进程)
- **后端服务(Node.js)**: ~316MB (已清理重复进程)
- **VSCode Server**: ~3.44GB (主要内存占用)
- **MySQL**: ~737MB
- **系统总内存**: 7.75GB，使用率 66.21%

## 🛠️ 已实施的优化措施

### 1. 智能启动脚本 (`start-dev.sh`)

**功能特性：**
- ✅ 自动检测并清理重复进程
- ✅ 避免重复启动前后端服务
- ✅ 智能端口检测和进程管理
- ✅ 后台运行，减少终端占用
- ✅ 完整的日志记录

**使用方法：**
```bash
# 启动所有服务
./start-dev.sh start

# 停止所有服务
./start-dev.sh stop

# 重启所有服务
./start-dev.sh restart

# 查看服务状态
./start-dev.sh status
```

### 2. 内存监控脚本 (`monitor-memory.sh`)

**功能特性：**
- ✅ 实时监控系统和进程内存使用
- ✅ 自动检测重复进程
- ✅ 内存使用警告和建议
- ✅ 内存清理功能

**使用方法：**
```bash
# 查看当前内存使用情况
./monitor-memory.sh monitor

# 清理内存后查看使用情况
./monitor-memory.sh cleanup

# 持续监控内存使用情况
./monitor-memory.sh watch
```

### 3. Vite内存优化配置 (`vite.config.memory-optimized.ts`)

**优化项目：**
- ✅ 减少HMR开销
- ✅ 优化文件监听范围
- ✅ 代码分割优化
- ✅ 预构建优化
- ✅ 测试模式不生成sourcemap

### 4. VSCode内存优化配置 (`.vscode/settings.json`)

**优化项目：**
- ✅ TypeScript自动导入优化
- ✅ 文件监听排除优化
- ✅ 搜索范围优化
- ✅ 扩展自动更新关闭
- ✅ Git自动刷新关闭
- ✅ 遥测数据关闭

## 📈 优化效果

### 进程管理优化
- **前端进程**: 从多个重复进程 → 单一进程 (196MB)
- **后端进程**: 从3个重复进程 → 单一进程 (清理后)
- **启动时间**: 减少重复启动等待时间
- **稳定性**: 避免端口冲突和进程冲突

### 内存使用优化
- **前端内存**: 通过Vite配置优化，减少不必要的文件监听
- **开发工具**: VSCode配置优化，减少TypeScript服务内存占用
- **系统缓存**: 定期清理npm/pnpm缓存

## 🔧 使用建议

### 日常开发流程

1. **启动开发环境**
   ```bash
   ./start-dev.sh start
   ```

2. **定期检查内存使用**
   ```bash
   ./monitor-memory.sh monitor
   ```

3. **发现内存过高时清理**
   ```bash
   ./monitor-memory.sh cleanup
   ```

4. **结束开发时停止服务**
   ```bash
   ./start-dev.sh stop
   ```

### 内存优化最佳实践

1. **避免重复启动**
   - 使用 `start-dev.sh` 脚本管理服务
   - 启动前检查服务状态
   - 避免手动启动多个相同进程

2. **定期内存清理**
   - 每天开发结束后运行内存清理
   - 发现内存使用过高时及时清理
   - 定期重启开发服务释放内存

3. **VSCode优化**
   - 关闭不必要的扩展
   - 使用优化后的配置文件
   - 避免打开过多文件标签

4. **浏览器优化**
   - 关闭不必要的标签页
   - 使用开发者工具时注意内存使用
   - 定期清理浏览器缓存

## 🚨 内存警告阈值

- **系统内存使用率 > 90%**: 🔴 严重警告
- **系统内存使用率 > 80%**: 🟡 注意警告
- **单个进程内存 > 2GB**: 🔴 严重警告
- **单个进程内存 > 1GB**: 🟡 注意警告

## 📝 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   ./start-dev.sh restart
   ```

2. **内存使用过高**
   ```bash
   ./monitor-memory.sh cleanup
   ```

3. **进程重复启动**
   ```bash
   ./start-dev.sh stop
   ./start-dev.sh start
   ```

4. **VSCode内存过高**
   - 重启VSCode
   - 检查扩展使用情况
   - 使用优化配置

### 日志查看

- **前端日志**: `logs/frontend.log`
- **后端日志**: `logs/backend.log`
- **系统日志**: 使用 `journalctl` 查看

## 🔄 持续优化

1. **定期监控**: 每周运行内存监控，记录使用情况
2. **配置调优**: 根据实际使用情况调整配置参数
3. **工具更新**: 定期更新开发工具和依赖包
4. **性能测试**: 定期进行性能测试，发现瓶颈

## 📞 技术支持

如果遇到内存相关问题：
1. 运行 `./monitor-memory.sh monitor` 获取详细信息
2. 查看相关日志文件
3. 尝试重启服务解决问题
4. 记录问题现象和解决方案

---

**最后更新**: 2025-07-09
**版本**: v1.0.0
