# 将以下配置复制到宝塔面板的站点配置文件中

server {
    listen 6666;
    server_name 38.55.192.88_6666;
    root /www/wwwroot/FDnew/SoybeanAdmin/dist;
    index index.html;

    # API代理 - 重要：让前端能访问后端
    location /api/ {
        proxy_pass http://127.0.0.1:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # Vue路由支持 - 重要：让前端路由正常工作
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public";
    }

    # 开启压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml;
}
