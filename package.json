{"name": "@sa/elp", "type": "module", "version": "1.3.12", "description": "A fresh and elegant admin template, based on Vue3、Vite3、TypeScript、ElementPlus and UnoCSS. 一个基于Vue3、Vite3、TypeScript、ElementPlus and UnoCSS的清新优雅的中后台模版。", "author": {"name": "Soybean", "email": "<EMAIL>", "url": "https://github.com/soybeanjs"}, "license": "MIT", "homepage": "https://github.com/soybeanjs/soybean-admin", "repository": {"url": "https://github.com/soybeanjs/soybean-admin.git"}, "bugs": {"url": "https://github.com/soybeanjs/soybean-admin/issues"}, "keywords": ["Vue3 admin ", "vue-admin-template", "Vite5", "TypeScript", "element-plus", "element-plus-admin", "UnoCSS"], "engines": {"node": ">=18.20.0", "pnpm": ">=8.7.0"}, "scripts": {"build": "vite build --mode prod", "build:test": "vite build --mode test", "cleanup": "sa cleanup", "cleanup:logs": "node scripts/log-cleanup.js", "commit": "sa git-commit", "commit:zh": "sa git-commit -l=zh-cn", "dev": "vite --mode test", "dev:prod": "vite --mode prod", "gen-route": "sa gen-route", "lint": "eslint . --fix", "optimize:build": "node scripts/build-optimization.js", "postbuild": "sa print-soybean", "prepare": "simple-git-hooks", "preview": "vite preview", "refactor:duplicates": "node scripts/refactor-duplicates.js", "release": "sa release", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "update-pkg": "sa update-pkg"}, "dependencies": {"@antv/data-set": "0.11.8", "@antv/g2": "5.3.3", "@antv/g6": "5.0.49", "@better-scroll/core": "2.5.1", "@iconify/vue": "5.0.0", "@sa/alova": "workspace:*", "@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/utils": "workspace:*", "@visactor/vchart": "2.0.0", "@visactor/vchart-theme": "1.12.2", "@visactor/vtable-editors": "1.18.6", "@visactor/vtable-gantt": "1.18.6", "@visactor/vue-vtable": "1.18.6", "@vueuse/components": "13.4.0", "@vueuse/core": "13.4.0", "clipboard": "2.0.11", "crypto-js": "4.2.0", "dayjs": "1.11.13", "defu": "^6.1.4", "dompurify": "3.2.6", "echarts": "5.6.0", "element-plus": "^2.10.2", "json5": "2.2.3", "nprogress": "0.2.0", "pinia": "3.0.3", "swiper": "11.2.10", "tailwind-merge": "3.3.1", "typeit": "8.8.7", "vue": "3.5.17", "vue-draggable-plus": "0.6.0", "vue-i18n": "11.1.7", "vue-router": "4.5.1", "xlsx": "0.18.5"}, "devDependencies": {"@amap/amap-jsapi-types": "0.0.15", "@elegant-router/vue": "0.3.8", "@iconify/json": "2.2.353", "@sa/scripts": "workspace:*", "@sa/uno-preset": "workspace:*", "@soybeanjs/eslint-config": "1.7.0", "@types/bmapgl": "0.0.7", "@types/crypto-js": "4.2.2", "@types/node": "24.0.7", "@types/nprogress": "0.2.3", "@unocss/eslint-config": "66.3.2", "@unocss/preset-icons": "66.3.2", "@unocss/preset-uno": "66.3.2", "@unocss/transformer-directives": "66.3.2", "@unocss/transformer-variant-group": "66.3.2", "@unocss/vite": "66.3.2", "@vitejs/plugin-vue": "5.1.4", "@vitejs/plugin-vue-jsx": "4.0.1", "consola": "3.4.2", "eslint": "9.30.0", "eslint-plugin-vue": "10.2.0", "kolorist": "1.8.0", "lint-staged": "16.1.2", "sass": "1.89.2", "simple-git-hooks": "2.13.0", "tsx": "4.20.3", "typescript": "5.8.3", "unplugin-icons": "22.1.0", "unplugin-vue-components": "28.7.0", "vite": "6.0.1", "vite-plugin-progress": "0.0.7", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.7.7", "vue-eslint-parser": "10.1.4", "vue-tsc": "2.2.10"}, "simple-git-hooks": {"commit-msg": "pnpm sa git-commit-verify", "pre-commit": "pnpm typecheck && pnpm lint-staged"}, "lint-staged": {"*": "eslint --fix"}, "website": "https://elp.soybeanjs.cn", "packageManager": "pnpm@8.15.9+sha512.499434c9d8fdd1a2794ebf4552b3b25c0a633abcee5bb15e7b5de90f32f47b513aca98cd5cfd001c31f0db454bc3804edccd578501e4ca293a6816166bbd9f81"}