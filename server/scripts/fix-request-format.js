/**
 * 修复请求格式 - 添加缺少的act参数并更新HTTP方法
 */

const process = require('node:process');
const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'newfd',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

async function fixRequestFormat() {
  const pool = mysql.createPool(dbConfig);

  try {
    const providerId = 2;
    const interfaceType = 'query';

    console.log('修复请求格式...');
    console.log('参数:', { providerId, interfaceType });

    // 更新请求模板 - 添加act参数
    /* eslint-disable no-template-curly-in-string */
    const correctRequestTemplate = {
      key: '${auth.token}', // API密钥
      uid: '${auth.uid}', // 用户ID
      act: 'get', // 操作类型（从错误信息看需要这个参数）
      pass: '${data.password}', // 密码
      user: '${data.username}', // 用户名
      school: '${data.school}', // 学校
      platform: '${data.platform}', // 平台
      course_id: '${data.course_id}' // 课程ID
    };
    /* eslint-enable no-template-curly-in-string */

    console.log('新的请求模板:', JSON.stringify(correctRequestTemplate, null, 2));

    // 更新接口配置 - 改为GET方法，因为从错误信息看上游期望GET请求
    const [updateResult] = await pool.query(
      `UPDATE fd_provider_interface
       SET request_template = ?, http_method = 'GET', update_time = NOW()
       WHERE provider_id = ? AND interface_type = ?`,
      [JSON.stringify(correctRequestTemplate), providerId, interfaceType]
    );

    console.log('接口配置更新结果:', updateResult);

    // 验证更新结果
    console.log('\n验证更新结果...');

    const [verifyInterface] = await pool.query(
      'SELECT request_template, http_method FROM fd_provider_interface WHERE provider_id = ? AND interface_type = ?',
      [providerId, interfaceType]
    );

    if (verifyInterface.length > 0) {
      console.log('更新后的配置:');
      console.log('- HTTP方法:', verifyInterface[0].http_method);
      console.log('- 请求模板:', verifyInterface[0].request_template);

      try {
        let parsedTemplate;
        if (typeof verifyInterface[0].request_template === 'string') {
          parsedTemplate = JSON.parse(verifyInterface[0].request_template);
        } else {
          parsedTemplate = verifyInterface[0].request_template;
        }
        console.log('- 解析后的请求模板:', JSON.stringify(parsedTemplate, null, 2));
      } catch (e) {
        console.log('- 请求模板解析失败:', e.message);
        console.log('- 原始数据类型:', typeof verifyInterface[0].request_template);
        console.log('- 原始数据:', verifyInterface[0].request_template);
      }
    }

    console.log('\n✅ 请求格式修复完成！');
    console.log('现在请求将使用GET方法和表单参数格式发送。');
  } catch (error) {
    console.error('❌ 修复过程中出错:', error);
  } finally {
    await pool.end();
  }
}

fixRequestFormat().catch(console.error);
