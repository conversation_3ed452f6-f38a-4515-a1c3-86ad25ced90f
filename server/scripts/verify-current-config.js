/**
 * 验证当前配置状态
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'newfd',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

async function verifyCurrentConfig() {
  const pool = mysql.createPool(dbConfig);

  try {
    const providerId = 2;
    const interfaceType = 'query';

    console.log('🔍 验证当前配置状态...');
    console.log('参数:', { providerId, interfaceType });

    // 检查接口配置
    const [interfaceResult] = await pool.query(
      'SELECT * FROM fd_provider_interface WHERE provider_id = ? AND interface_type = ?',
      [providerId, interfaceType]
    );

    if (interfaceResult.length === 0) {
      console.log('❌ 没有找到接口配置');
      return;
    }

    const interfaceConfig = interfaceResult[0];
    console.log('\n✅ 接口配置:');
    console.log('- HTTP方法:', interfaceConfig.http_method);
    console.log('- 接口URL:', interfaceConfig.endpoint_url);
    console.log('- 启用状态:', interfaceConfig.is_enabled ? '启用' : '禁用');

    // 解析请求模板
    let requestTemplate;
    try {
      if (typeof interfaceConfig.request_template === 'string') {
        requestTemplate = JSON.parse(interfaceConfig.request_template);
      } else {
        requestTemplate = interfaceConfig.request_template;
      }
      console.log('- 请求模板:', JSON.stringify(requestTemplate, null, 2));
    } catch (e) {
      console.log('- 请求模板解析失败:', e.message);
    }

    // 检查货源配置
    const [providerResult] = await pool.query(
      'SELECT token, username, password, field_mapping FROM fd_provider WHERE provider_id = ?',
      [providerId]
    );

    if (providerResult.length === 0) {
      console.log('❌ 没有找到货源配置');
      return;
    }

    const providerConfig = providerResult[0];
    console.log('\n✅ 货源配置:');
    console.log('- API密钥 (token):', providerConfig.token || '未设置');
    console.log('- 用户名:', providerConfig.username || '未设置');
    console.log('- 密码:', providerConfig.password ? '已设置' : '未设置');

    // 解析字段映射
    let fieldMapping;
    try {
      if (typeof providerConfig.field_mapping === 'string') {
        fieldMapping = JSON.parse(providerConfig.field_mapping);
      } else {
        fieldMapping = providerConfig.field_mapping;
      }
      console.log('- 字段映射:', JSON.stringify(fieldMapping, null, 2));
    } catch (e) {
      console.log('- 字段映射解析失败:', e.message);
    }

    // 模拟请求数据构建
    console.log('\n🔧 模拟请求数据构建...');

    const testData = {
      school: '测试大学',
      username: 'test_student',
      password: 'test123456',
      platform: 'network_course',
      course_id: ''
    };

    console.log('测试数据:', testData);

    const requestData = {};

    // 处理请求模板
    if (requestTemplate && Object.keys(requestTemplate).length > 0) {
      for (const [key, value] of Object.entries(requestTemplate)) {
        if (typeof value === 'string' && value.startsWith('${')) {
          const variable = value.slice(2, -1);

          if (variable.startsWith('auth.')) {
            const authField = variable.split('.')[1];
            if (authField === 'token') {
              requestData[key] = providerConfig.token;
            } else if (authField === 'username') {
              requestData[key] = providerConfig.username;
            } else if (authField === 'password') {
              requestData[key] = providerConfig.password;
            } else if (authField === 'uid') {
              requestData[key] = '5'; // 默认值
            }
          } else if (variable.startsWith('data.')) {
            const dataField = variable.split('.')[1];
            requestData[key] = testData[dataField];
          } else {
            requestData[key] = testData[variable];
          }
        } else {
          requestData[key] = value;
        }
      }
    }

    console.log('构建的请求数据:', requestData);

    // 检查必要参数
    console.log('\n📋 参数检查:');
    const requiredParams = ['key', 'uid', 'act'];
    let allParamsOk = true;

    for (const param of requiredParams) {
      if (requestData[param]) {
        console.log(`✅ ${param}: ${requestData[param]}`);
      } else {
        console.log(`❌ ${param}: 缺失`);
        allParamsOk = false;
      }
    }

    if (allParamsOk) {
      console.log('\n🎉 配置验证通过！');
      console.log('接口测试应该可以正常工作了。');

      if (interfaceConfig.http_method === 'GET') {
        const queryString = new URLSearchParams(requestData).toString();
        console.log('\n📡 GET请求URL预览:');
        console.log(`${interfaceConfig.endpoint_url}?${queryString}`);
      }
    } else {
      console.log('\n⚠️  配置不完整，需要补充缺失的参数。');
    }
  } catch (error) {
    console.error('❌ 验证过程中出错:', error);
  } finally {
    await pool.end();
  }
}

verifyCurrentConfig().catch(console.error);
