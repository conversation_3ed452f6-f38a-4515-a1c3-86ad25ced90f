/**
 * 修复接口配置 - 更新请求模板以匹配上游接口要求
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'newfd',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

async function fixInterfaceConfig() {
  const pool = mysql.createPool(dbConfig);

  try {
    const providerId = 2;
    const interfaceType = 'query';

    console.log('修复接口配置...');
    console.log('参数:', { providerId, interfaceType });

    // 更新请求模板 - 根据实际测试结果，上游接口需要这些参数
    const correctRequestTemplate = {
      key: '${auth.token}', // API密钥从token字段获取
      uid: '${auth.uid}', // 用户ID，需要在货源配置中添加
      pass: '${data.password}', // 密码从测试数据获取
      user: '${data.username}', // 用户名从测试数据获取
      school: '${data.school}', // 学校从测试数据获取
      platform: '${data.platform}', // 平台从测试数据获取
      course_id: '${data.course_id}', // 课程ID从测试数据获取（查课时可为空）
      type: 'query' // 固定值，表示查课操作
    };

    // 更新响应映射 - 根据实际响应格式
    const correctResponseMapping = {
      success_field: 'code', // 成功标识字段
      success_value: 0, // 成功值（0表示成功，-1表示失败）
      message_field: 'msg', // 消息字段
      data_field: 'data', // 数据字段
      course_mapping: {
        // 课程数据映射（如果返回课程列表）
        id: 'course_id',
        name: 'course_name',
        price: 'price',
        description: 'description'
      }
    };

    console.log('新的请求模板:', JSON.stringify(correctRequestTemplate, null, 2));
    console.log('新的响应映射:', JSON.stringify(correctResponseMapping, null, 2));

    // 更新接口配置
    const [updateResult] = await pool.query(
      `UPDATE fd_provider_interface 
       SET request_template = ?, response_mapping = ?, update_time = NOW()
       WHERE provider_id = ? AND interface_type = ?`,
      [JSON.stringify(correctRequestTemplate), JSON.stringify(correctResponseMapping), providerId, interfaceType]
    );

    console.log('接口配置更新结果:', updateResult);

    // 更新货源配置 - 添加必要的认证信息
    const correctProviderConfig = {
      school: {
        provider_field: 'school',
        required: true,
        description: '学校名称'
      },
      username: {
        provider_field: 'user',
        required: true,
        description: '学生用户名'
      },
      password: {
        provider_field: 'pass',
        required: true,
        description: '学生密码'
      },
      platform: {
        provider_field: 'platform',
        required: true,
        description: '平台类型',
        default: 'network_course'
      },
      course_id: {
        provider_field: 'course_id',
        required: false,
        description: '课程ID（查课时可为空）'
      }
    };

    // 同时更新货源的认证信息
    const [providerUpdateResult] = await pool.query(
      `UPDATE fd_provider 
       SET field_mapping = ?, token = ?, update_time = NOW()
       WHERE provider_id = ?`,
      [
        JSON.stringify(correctProviderConfig),
        'YsIYr7lZ75plP8Y5', // 从测试结果中看到的key值
        providerId
      ]
    );

    console.log('货源配置更新结果:', providerUpdateResult);

    // 验证更新结果
    console.log('\n验证更新结果...');

    const [verifyInterface] = await pool.query(
      'SELECT * FROM fd_provider_interface WHERE provider_id = ? AND interface_type = ?',
      [providerId, interfaceType]
    );

    const [verifyProvider] = await pool.query('SELECT field_mapping, token FROM fd_provider WHERE provider_id = ?', [
      providerId
    ]);

    console.log('更新后的接口配置:');
    console.log('- 请求模板:', JSON.parse(verifyInterface[0].request_template));
    console.log('- 响应映射:', JSON.parse(verifyInterface[0].response_mapping));

    console.log('\n更新后的货源配置:');
    console.log('- 字段映射:', JSON.parse(verifyProvider[0].field_mapping));
    console.log('- API密钥:', verifyProvider[0].token);

    console.log('\n✅ 接口配置修复完成！');
    console.log('现在测试数据应该能正确传递到上游接口了。');
  } catch (error) {
    console.error('❌ 修复过程中出错:', error);
  } finally {
    await pool.end();
  }
}

fixInterfaceConfig().catch(console.error);
