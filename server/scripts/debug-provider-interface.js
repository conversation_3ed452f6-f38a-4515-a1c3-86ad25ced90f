/**
 * 调试货源接口配置
 * 用于检查接口配置是否正确保存和查询
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

// 数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'newfd',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
};

async function main() {
  // 创建数据库连接池
  const pool = mysql.createPool(dbConfig);

  try {
    // 检查货源表
    console.log('检查货源表结构...');
    const [providerStructure] = await pool.query('DESCRIBE fd_provider');
    console.log('货源表结构:', providerStructure.map(row => `${row.Field} (${row.Type})`).join(', '));

    // 检查接口表
    console.log('\n检查接口表结构...');
    const [interfaceStructure] = await pool.query('DESCRIBE fd_provider_interface');
    console.log('接口表结构:', interfaceStructure.map(row => `${row.Field} (${row.Type})`).join(', '));

    // 获取货源列表
    console.log('\n获取货源列表...');
    const [providers] = await pool.query('SELECT provider_id, name, code FROM fd_provider LIMIT 10');
    console.log('货源列表:', providers);

    if (providers.length === 0) {
      console.log('没有找到货源，请先创建货源');
      return;
    }

    // 获取第一个货源的ID
    const providerId = providers[0].provider_id;
    console.log(`\n使用货源ID: ${providerId} (${providers[0].name})`);

    // 检查该货源的接口配置
    console.log('\n检查接口配置...');
    const [interfaces] = await pool.query('SELECT * FROM fd_provider_interface WHERE provider_id = ?', [providerId]);
    console.log(`找到 ${interfaces.length} 个接口配置:`);

    if (interfaces.length > 0) {
      interfaces.forEach((intf, index) => {
        console.log(`\n接口 ${index + 1}:`);
        console.log(`- 接口类型: ${intf.interface_type}`);
        console.log(`- 接口URL: ${intf.endpoint_url}`);
        console.log(`- HTTP方法: ${intf.http_method}`);
        console.log(`- 启用状态: ${intf.is_enabled}`);

        try {
          const requestTemplate = intf.request_template ? JSON.parse(intf.request_template) : null;
          console.log(`- 请求模板: ${JSON.stringify(requestTemplate, null, 2)}`);
        } catch (e) {
          console.log(`- 请求模板解析失败: ${intf.request_template}`);
        }

        try {
          const responseMapping = intf.response_mapping ? JSON.parse(intf.response_mapping) : null;
          console.log(`- 响应映射: ${JSON.stringify(responseMapping, null, 2)}`);
        } catch (e) {
          console.log(`- 响应映射解析失败: ${intf.response_mapping}`);
        }
      });
    } else {
      console.log('没有找到接口配置，请先配置接口');

      // 创建测试接口配置
      console.log('\n创建测试接口配置...');
      const testInterface = {
        provider_id: providerId,
        interface_type: 'query',
        endpoint_url: 'https://example.com/api/query',
        http_method: 'POST',
        request_template: JSON.stringify({
          username: '${auth.username}',
          password: '${auth.password}',
          school: '${data.school}'
        }),
        response_mapping: JSON.stringify({
          success_field: 'code',
          success_value: 0,
          message_field: 'msg',
          data_field: 'data'
        }),
        custom_code: '',
        is_enabled: 1
      };

      const [result] = await pool.query(
        `INSERT INTO fd_provider_interface
         (provider_id, interface_type, endpoint_url, http_method, request_template, response_mapping, custom_code, is_enabled, create_time, update_time)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          testInterface.provider_id,
          testInterface.interface_type,
          testInterface.endpoint_url,
          testInterface.http_method,
          testInterface.request_template,
          testInterface.response_mapping,
          testInterface.custom_code,
          testInterface.is_enabled
        ]
      );

      console.log('测试接口创建结果:', result);

      // 再次检查接口配置
      console.log('\n再次检查接口配置...');
      const [newInterfaces] = await pool.query('SELECT * FROM fd_provider_interface WHERE provider_id = ?', [
        providerId
      ]);
      console.log(`找到 ${newInterfaces.length} 个接口配置`);
    }

    // 检查字段映射
    console.log('\n检查字段映射...');
    const [providerDetail] = await pool.query('SELECT field_mapping FROM fd_provider WHERE provider_id = ?', [
      providerId
    ]);

    if (providerDetail.length > 0 && providerDetail[0].field_mapping) {
      try {
        const fieldMapping = JSON.parse(providerDetail[0].field_mapping);
        console.log('字段映射:', JSON.stringify(fieldMapping, null, 2));
      } catch (e) {
        console.log('字段映射解析失败:', providerDetail[0].field_mapping);
      }
    } else {
      console.log('没有找到字段映射配置');

      // 创建测试字段映射
      console.log('\n创建测试字段映射...');
      const testFieldMapping = {
        school: {
          provider_field: 'school_name',
          required: true
        },
        username: {
          provider_field: 'user_account',
          required: true
        },
        password: {
          provider_field: 'user_password',
          required: true
        }
      };

      const [updateResult] = await pool.query('UPDATE fd_provider SET field_mapping = ? WHERE provider_id = ?', [
        JSON.stringify(testFieldMapping),
        providerId
      ]);

      console.log('字段映射更新结果:', updateResult);
    }
  } catch (error) {
    console.error('调试过程中出错:', error);
  } finally {
    // 关闭连接池
    await pool.end();
  }
}

main().catch(console.error);
