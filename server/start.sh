#!/bin/bash

# 后端启动脚本 - 自动处理端口冲突
# 使用方法: ./start.sh [dev|prod]

MODE=${1:-dev}
PORT=${2:-3000}

echo "🚀 启动后端服务器..."
echo "📋 模式: $MODE"
echo "🔌 端口: $PORT"

# 检查并停止占用端口的进程
check_and_kill_port() {
    local port=$1
    local pid=$(lsof -ti:$port)

    if [ ! -z "$pid" ]; then
        echo "⚠️  端口 $port 被进程 $pid 占用"
        echo "🔄 正在停止占用端口的进程..."
        kill -9 $pid 2>/dev/null
        sleep 2

        # 再次检查
        local new_pid=$(lsof -ti:$port)
        if [ ! -z "$new_pid" ]; then
            echo "❌ 无法停止进程 $new_pid，请手动处理"
            return 1
        else
            echo "✅ 端口 $port 已释放"
        fi
    else
        echo "✅ 端口 $port 可用"
    fi
    return 0
}

# 启动开发服务器
start_dev() {
    echo "🔧 启动开发模式..."
    check_and_kill_port $PORT
    if [ $? -eq 0 ]; then
        npm run dev
    else
        echo "❌ 端口冲突，启动失败"
        exit 1
    fi
}

# 启动生产服务器
start_prod() {
    echo "🏗️  编译项目..."
    npm run build

    if [ $? -eq 0 ]; then
        echo "✅ 编译完成"
        echo "🚀 启动生产模式..."
        check_and_kill_port $PORT
        if [ $? -eq 0 ]; then
            node dist/index.js
        else
            echo "❌ 端口冲突，启动失败"
            exit 1
        fi
    else
        echo "❌ 编译失败"
        exit 1
    fi
}

# 根据模式启动
case $MODE in
    "dev")
        start_dev
        ;;
    "prod")
        start_prod
        ;;
    *)
        echo "❌ 无效的模式: $MODE"
        echo "📖 使用方法: ./start.sh [dev|prod] [port]"
        echo "📖 示例: ./start.sh dev 3000"
        exit 1
        ;;
esac
