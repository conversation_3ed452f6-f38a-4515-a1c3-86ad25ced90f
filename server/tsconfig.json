{"compilerOptions": {"target": "ES2020", "rootDir": "./src", "module": "commonjs", "strict": false, "strictNullChecks": false, "exactOptionalPropertyTypes": false, "noImplicitAny": false, "noImplicitOverride": false, "noImplicitReturns": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "noUnusedLocals": false, "noUnusedParameters": false, "outDir": "./dist", "esModuleInterop": true, "forceConsistentCasingInFileNames": false, "skipLibCheck": true}, "include": ["src/**/*"], "exclude": ["node_modules"]}