{"name": "server", "version": "1.0.0", "description": "", "author": "", "license": "ISC", "keywords": [], "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "nodemon --exec ts-node --transpile-only src/index.ts", "start": "node dist/index.js"}, "dependencies": {"@types/bcrypt": "^5.0.2", "@types/multer": "^1.4.13", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "axios": "^1.9.0", "bcrypt": "^6.0.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "jsonwebtoken": "^9.0.2", "multer": "^2.0.1", "mysql2": "^3.14.1", "node-cache": "^5.1.2", "node-cron": "^4.1.0", "nodemailer": "^7.0.3"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.6", "@types/node": "^20.14.0", "nodemon": "^3.1.0", "ts-node": "^10.9.2", "typescript": "^5.4.5"}}