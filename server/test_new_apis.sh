#!/bin/bash

# 测试新增的API接口
echo "🧪 测试新增的API接口"

BASE_URL="http://localhost:3000"
USER_ID="18"

# 获取登录token
echo "📝 获取登录token..."
LOGIN_RESPONSE=$(curl -s -X POST "$BASE_URL/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"123456"}')

TOKEN=$(echo $LOGIN_RESPONSE | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
  echo "❌ 登录失败，无法获取token"
  echo "登录响应: $LOGIN_RESPONSE"
  exit 1
fi

echo "✅ 登录成功，token: ${TOKEN:0:20}..."

# 测试用户下级用户接口
echo ""
echo "🔍 测试获取用户下级用户接口..."
INVITEES_RESPONSE=$(curl -s "$BASE_URL/api/user/invitees/$USER_ID?limit=10" \
  -H "Authorization: Bearer $TOKEN")
echo "响应: $INVITEES_RESPONSE"

# 测试用户统计接口
echo ""
echo "📊 测试获取用户统计接口..."
STATS_RESPONSE=$(curl -s "$BASE_URL/api/user/stats/$USER_ID" \
  -H "Authorization: Bearer $TOKEN")
echo "响应: $STATS_RESPONSE"

# 测试用户订单接口
echo ""
echo "📋 测试获取用户订单接口..."
ORDERS_RESPONSE=$(curl -s "$BASE_URL/api/order/user/$USER_ID?limit=10" \
  -H "Authorization: Bearer $TOKEN")
echo "响应: $ORDERS_RESPONSE"

# 测试用户权限接口
echo ""
echo "🔐 测试获取用户权限接口..."
PERMISSIONS_RESPONSE=$(curl -s "$BASE_URL/api/user-permission/list/$USER_ID" \
  -H "Authorization: Bearer $TOKEN")
echo "响应: $PERMISSIONS_RESPONSE"

echo ""
echo "🎉 API接口测试完成！"
