# 数据库表结构与功能总览

## 📊 数据库概况

- **数据库名称**: newfd
- **表总数**: 30个（29个基础表 + 1个视图）
- **优化状态**: 已完成精简优化
- **最后更新**: 2025-08-03

---

## 📋 表分类说明

### 🔵 核心业务表（8个）
用户、订单、商品、货源等核心业务功能

### 🟢 权限管理表（5个）
用户权限、角色管理、功能限制等

### 🟡 系统配置表（6个）
系统配置、平台对接、邮件模板等

### 🟠 日志记录表（2个）
操作日志、数据库迁移记录

### 🔴 备份归档表（2个）
数据备份、历史记录归档

### 🟣 其他功能表（7个）
公告、反馈、充值配置等辅助功能

---

## 📚 详细表结构分析

### 🔵 核心业务表

#### 1. fd_user - 用户表
**功能**: 存储系统用户的基础信息
**数据量**: 2条记录
**主要字段**:
- `user_id` - 用户ID（主键）
- `username` - 用户名（QQ账号）
- `password` - 密码（加密存储）
- `nickname` - 昵称
- `email` - 邮箱
- `role` - 角色（1:管理员 2:代理商 3:普通用户）
- `status` - 状态（0:禁用 1:启用）
- `balance` - 账户余额
- `invite_code` - 邀请码
- `sid` - 上级用户ID

#### 2. fd_order - 订单表
**功能**: 存储用户订单信息
**数据量**: 45条记录
**主要字段**:
- `order_id` - 订单ID（主键）
- `user_id` - 用户ID
- `product_id` - 商品ID
- `order_no` - 订单号
- `amount` - 订单金额
- `status` - 订单状态
- `create_time` - 创建时间

#### 3. fd_product - 商品表
**功能**: 存储商品信息
**数据量**: 52条记录
**主要字段**:
- `cid` - 商品ID（主键）
- `name` - 商品名称
- `price` - 商品价格
- `cost_price` - 成本价格
- `content` - 商品描述
- `fenlei` - 分类ID
- `provider_id` - 货源ID
- `status` - 商品状态

#### 4. fd_provider - 货源表
**功能**: 存储货源供应商信息
**数据量**: 3条记录
**主要字段**:
- `provider_id` - 货源ID（主键）
- `provider_name` - 货源名称
- `provider_type` - 货源类型
- `api_config` - API配置
- `status` - 状态

#### 5. fd_category - 分类表
**功能**: 存储商品分类信息
**主要字段**:
- `category_id` - 分类ID（主键）
- `category_name` - 分类名称
- `parent_id` - 父分类ID
- `sort_order` - 排序

#### 6. fd_provider_interface - 货源接口表
**功能**: 存储货源接口配置
**主要字段**:
- `interface_id` - 接口ID（主键）
- `provider_id` - 货源ID
- `interface_type` - 接口类型
- `interface_config` - 接口配置

#### 7. fd_platform_config - 平台配置表
**功能**: 存储平台对接配置
**主要字段**:
- `config_id` - 配置ID（主键）
- `platform_name` - 平台名称
- `config_data` - 配置数据

#### 8. fd_platform_item - 平台项目表
**功能**: 存储平台项目信息
**主要字段**:
- `item_id` - 项目ID（主键）
- `platform_id` - 平台ID
- `item_name` - 项目名称
- `item_config` - 项目配置

---

### 🟢 权限管理表

#### 9. fd_permission - 权限表
**功能**: 定义系统权限
**主要字段**:
- `permission_id` - 权限ID（主键）
- `permission_code` - 权限代码
- `permission_name` - 权限名称
- `permission_type` - 权限类型（menu/button/api）
- `parent_id` - 父权限ID

#### 10. fd_role - 角色表
**功能**: 定义用户角色
**主要字段**:
- `role_id` - 角色ID（主键）
- `role_name` - 角色名称
- `role_code` - 角色代码
- `description` - 角色描述

#### 11. fd_user_permission - 用户权限关联表
**功能**: 用户与权限的关联关系
**主要字段**:
- `id` - 关联ID（主键）
- `user_id` - 用户ID
- `permission_id` - 权限ID
- `grant_type` - 授权类型（grant/deny）

#### 12. fd_role_permission - 角色权限关联表
**功能**: 角色与权限的关联关系
**主要字段**:
- `id` - 关联ID（主键）
- `role_id` - 角色ID
- `permission_id` - 权限ID

#### 13. fd_user_role - 用户角色关联表
**功能**: 用户与角色的关联关系
**主要字段**:
- `id` - 关联ID（主键）
- `user_id` - 用户ID
- `role_id` - 角色ID

---

### 🟡 系统配置表

#### 14. fd_config - 系统配置表
**功能**: 存储系统配置参数
**主要字段**:
- `config_id` - 配置ID（主键）
- `config_key` - 配置键
- `config_value` - 配置值
- `config_group` - 配置分组
- `description` - 配置描述

#### 15. fd_config_template - 配置模板表
**功能**: 存储配置模板
**数据量**: 0条记录（空表）
**主要字段**:
- `template_id` - 模板ID（主键）
- `template_name` - 模板名称
- `template_config` - 模板配置

#### 16. fd_field_mapping_template - 字段映射模板表
**功能**: 存储字段映射模板
**数据量**: 0条记录（空表）
**主要字段**:
- `template_id` - 模板ID（主键）
- `template_name` - 模板名称
- `mapping_config` - 映射配置

#### 17. fd_email_template - 邮件模板表
**功能**: 存储邮件模板
**数据量**: 0条记录（空表）
**主要字段**:
- `template_id` - 模板ID（主键）
- `template_name` - 模板名称
- `template_subject` - 邮件主题
- `template_content` - 邮件内容

#### 18. fd_license_info - 许可证信息表
**功能**: 存储系统许可证信息
**主要字段**:
- `id` - 许可证ID（主键）
- `license_key` - 许可证密钥
- `license_data` - 许可证数据
- `expire_time` - 过期时间

#### 19. fd_recharge_config - 充值配置表
**功能**: 存储充值配置信息
**数据量**: 0条记录（空表）
**主要字段**:
- `config_id` - 配置ID（主键）
- `payment_method` - 支付方式
- `config_data` - 配置数据

---

### 🟠 日志记录表

#### 20. fd_log - 统一日志表
**功能**: 记录所有系统操作日志
**主要字段**:
- `log_id` - 日志ID（主键）
- `user_id` - 操作用户ID
- `operator_id` - 操作者ID
- `operator_type` - 操作者类型（system/admin/user）
- `module` - 模块名称
- `action` - 操作类型
- `target_type` - 目标类型
- `target_id` - 目标ID
- `content` - 操作内容
- `data_before` - 操作前数据
- `data_after` - 操作后数据
- `result` - 操作结果
- `create_time` - 创建时间

#### 21. fd_migrations - 数据库迁移表
**功能**: 记录数据库迁移历史
**主要字段**:
- `id` - 迁移ID（主键）
- `migration` - 迁移文件名
- `batch` - 批次号

---

### 🔴 备份归档表

#### 22. fd_product_backup - 商品备份表
**功能**: 存储商品历史备份数据
**数据量**: 2649条记录
**说明**: 包含重要的商品历史数据，暂时保留
**主要字段**: 与fd_product表结构相同

#### 23. fd_config_history_archive - 配置历史归档表
**功能**: 存储配置变更历史（归档）
**数据量**: 7条记录
**说明**: 从fd_config_history表迁移的历史数据
**主要字段**:
- `history_id` - 历史ID（主键）
- `provider_id` - 货源ID
- `old_config` - 旧配置
- `new_config` - 新配置
- `change_type` - 变更类型

---

### 🟣 其他功能表

#### 24. fd_announcement - 公告表
**功能**: 存储系统公告
**数据量**: 0条记录（空表，有代码使用）
**主要字段**:
- `announcement_id` - 公告ID（主键）
- `title` - 公告标题
- `content` - 公告内容
- `type` - 公告类型
- `status` - 状态

#### 25. fd_backup_record - 备份记录表
**功能**: 记录数据库备份信息
**数据量**: 0条记录（空表，有代码使用）
**主要字段**:
- `backup_id` - 备份ID（主键）
- `backup_name` - 备份名称
- `backup_type` - 备份类型
- `file_path` - 文件路径
- `status` - 备份状态

#### 26. fd_email_record - 邮件记录表
**功能**: 记录邮件发送历史
**数据量**: 0条记录（空表，有代码使用）
**主要字段**:
- `record_id` - 记录ID（主键）
- `recipient` - 收件人
- `subject` - 邮件主题
- `content` - 邮件内容
- `status` - 发送状态

#### 27. fd_order_feedback - 订单反馈表
**功能**: 存储订单反馈信息
**数据量**: 0条记录（空表）
**主要字段**:
- `feedback_id` - 反馈ID（主键）
- `order_id` - 订单ID
- `feedback_content` - 反馈内容
- `feedback_type` - 反馈类型

#### 28. fd_user_level - 用户等级表
**功能**: 定义用户等级
**主要字段**:
- `level_id` - 等级ID（主键）
- `level_name` - 等级名称
- `level_config` - 等级配置

#### 29. fd_user_limitations - 用户功能限制表
**功能**: 存储用户功能限制配置
**主要字段**:
- `id` - 限制ID（主键）
- `user_id` - 用户ID
- `limitations` - 限制配置（JSON）

#### 30. Tables_in_newfd - 系统表
**说明**: 系统生成的表，应该被删除但仍存在

---

### 🔍 视图

#### v_provider_config_summary - 货源配置汇总视图
**功能**: 提供货源配置的汇总信息
**基于表**: fd_provider, fd_provider_interface

---

## 📊 数据统计

### 有数据的表（6个）
| 表名 | 记录数 | 状态 | 重要性 |
|------|--------|------|--------|
| fd_product_backup | 2649条 | 🟡 备份数据 | 中等 |
| fd_log | 112条 | 🟢 活跃使用 | 高 |
| fd_product | 52条 | 🟢 活跃使用 | 高 |
| fd_order | 45条 | 🟢 活跃使用 | 高 |
| fd_config_history_archive | 7条 | 🟡 归档数据 | 低 |
| fd_provider | 3条 | 🟢 活跃使用 | 高 |
| fd_user | 2条 | 🟢 活跃使用 | 高 |

### 空表但有代码使用（4个）
| 表名 | 功能模块 | 使用状态 |
|------|----------|----------|
| fd_announcement | 公告系统 | 🟠 待开发 |
| fd_backup_record | 备份管理 | 🟠 待开发 |
| fd_email_record | 邮件系统 | 🟠 待开发 |
| fd_email_template | 邮件模板 | 🟠 待开发 |

### 完全空表（19个）
其余表均为空表，但保留用于未来功能扩展

### 表使用频率分析
- **高频使用**: fd_user, fd_order, fd_product, fd_provider, fd_log
- **中频使用**: fd_permission, fd_role, fd_config
- **低频使用**: 各种模板表、配置表
- **备份归档**: fd_product_backup, fd_config_history_archive

---

## 🔗 表关系图

### 核心业务关系
```
fd_user (用户)
    ├── fd_order (订单) [user_id]
    ├── fd_user_role (用户角色) [user_id]
    ├── fd_user_permission (用户权限) [user_id]
    └── fd_user_limitations (功能限制) [user_id]

fd_product (商品)
    ├── fd_order (订单) [product_id]
    ├── fd_category (分类) [fenlei]
    └── fd_provider (货源) [provider_id]

fd_provider (货源)
    ├── fd_product (商品) [provider_id]
    ├── fd_provider_interface (接口) [provider_id]
    └── fd_platform_config (平台配置) [provider_id]
```

### 权限管理关系
```
fd_permission (权限)
    ├── fd_user_permission (用户权限) [permission_id]
    └── fd_role_permission (角色权限) [permission_id]

fd_role (角色)
    ├── fd_user_role (用户角色) [role_id]
    └── fd_role_permission (角色权限) [role_id]
```

## 🏗️ 功能模块映射

### 用户管理模块
- **核心表**: fd_user, fd_user_level, fd_user_limitations
- **关联表**: fd_user_role, fd_user_permission
- **功能**: 用户注册、登录、权限管理、功能限制

### 订单管理模块
- **核心表**: fd_order, fd_order_feedback
- **关联表**: fd_user, fd_product, fd_provider
- **功能**: 订单创建、状态管理、反馈处理

### 商品管理模块
- **核心表**: fd_product, fd_category
- **关联表**: fd_provider, fd_product_backup
- **功能**: 商品管理、分类管理、价格控制

### 货源管理模块
- **核心表**: fd_provider, fd_provider_interface
- **关联表**: fd_platform_config, fd_platform_item
- **功能**: 货源对接、接口管理、平台配置

### 权限管理模块
- **核心表**: fd_permission, fd_role
- **关联表**: fd_user_permission, fd_role_permission, fd_user_role
- **功能**: 权限定义、角色管理、权限分配

### 系统管理模块
- **核心表**: fd_config, fd_log, fd_license_info
- **关联表**: fd_migrations, fd_backup_record
- **功能**: 系统配置、日志管理、许可证管理

### 通信模块
- **核心表**: fd_email_template, fd_email_record
- **关联表**: fd_announcement
- **功能**: 邮件发送、公告管理

### 支付模块
- **核心表**: fd_recharge_config
- **关联表**: fd_user (balance字段)
- **功能**: 充值配置、余额管理

## 🎯 优化建议

### 立即优化项
1. **删除Tables_in_newfd** - 无用的系统表
2. **清理fd_log历史数据** - 定期清理超过6个月的日志
3. **优化fd_product_backup** - 考虑压缩或归档到文件系统

### 中期优化项
1. **合并配置表** - 将空的配置表合并到fd_config
2. **索引优化** - 为高频查询字段添加索引
3. **分区策略** - 对大表实施分区策略

### 长期规划
1. **读写分离** - 核心业务表实施读写分离
2. **缓存策略** - 热点数据Redis缓存
3. **归档策略** - 历史数据定期归档

## 🛡️ 数据安全建议

### 核心数据保护
- **fd_user**: 用户密码加密、敏感信息脱敏
- **fd_order**: 订单数据完整性校验
- **fd_log**: 操作日志防篡改

### 备份策略
- **每日备份**: 核心业务表
- **每周备份**: 完整数据库
- **实时同步**: 关键配置表

### 权限控制
- **最小权限原则**: 用户只能访问必要的数据
- **操作审计**: 所有敏感操作记录日志
- **定期检查**: 权限配置定期审查
