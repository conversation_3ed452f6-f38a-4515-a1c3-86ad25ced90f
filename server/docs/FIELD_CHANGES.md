# 字段变更记录

## 版本变更概述

### v2.0 字段变更 (2024-01-XX)

本版本主要解决了前后端数据结构不一致的问题，统一了字段命名规范，修正了SQL查询语句错误。

## 用户相关接口变更

### 🔄 字段名称变更

| 旧字段名 | 新字段名 | 变更类型 | 说明 |
|---------|---------|---------|------|
| `created_at` | `create_time` | 重命名 | 统一时间字段命名 |
| `updated_at` | `update_time` | 重命名 | 统一时间字段命名 |
| `userRate` | `price_rate` | 重命名 | 统一费率字段命名 |

### ✅ 新增字段

| 字段名 | 数据类型 | 说明 | 默认值 |
|-------|---------|------|--------|
| `user_role` | string | 字符串角色 (admin/agent/user) | 'user' |
| `total_recharge` | number | 总充值金额 | 0 |
| `referrer_id` | number | 推荐人ID | null |
| `level_id` | number | 用户等级ID | null |

### 🔧 字段规范化

| 字段名 | 旧规范 | 新规范 | 说明 |
|-------|--------|--------|------|
| `role` | 混用字符串和数值 | 统一为数值 | 1=管理员, 2=代理商, 3=普通用户 |
| `user_role` | 不存在 | 字符串类型 | admin/agent/user |

## 订单相关接口变更

### 🐛 SQL语法错误修正

**修正前**:
```sql
SELECT o.order_id as Number(orderId) FROM fd_order o;
```

**修正后**:
```sql
SELECT o.order_id as orderId FROM fd_order o;
```

### ✅ 新增JSON字段处理

| 字段名 | 数据类型 | 处理方式 | 说明 |
|-------|---------|---------|------|
| `course_info` | JSON | 自动解析 | 课程详细信息 |
| `extra_data` | JSON | 自动解析 | 扩展数据 |

## 配置管理变更

### 🔄 统一配置管理

- **移除**: 多处重复的默认配置定义
- **统一**: 所有配置通过 `ConfigManager` 管理
- **文件**: `server/src/utils/configManager.ts`

### 📁 配置文件整理

| 文件 | 状态 | 说明 |
|------|------|------|
| `server/src/controller/config.ts` | 简化 | 移除重复配置，使用ConfigManager |
| `server/src/config/system.json` | 保留 | 配置存储文件 |
| `server/src/utils/configManager.ts` | 主要 | 统一配置管理器 |

## 数据库连接变更

### 🔧 统一数据库用户

**修正前**:
- `server/src/utils/db.ts`: 使用 `newfd` 用户
- `server/src/database/connection.ts`: 使用 `root` 用户

**修正后**:
- 统一使用 `newfd` 用户
- 统一连接配置参数

## 前端类型定义变更

### 📝 TypeScript接口更新

**文件**: `src/typings/api.d.ts`

**Auth.UserInfo 接口变更**:
```typescript
// 新增字段
user_role: string;         // 字符串角色
price_rate: number;        // 用户费率
total_recharge?: number;   // 总充值金额
referrer_id?: number;      // 推荐人ID
level_id?: number;         // 用户等级ID
sid?: number;              // 上级用户ID

// 字段重命名
create_time: string;       // 原 created_at
update_time: string;       // 原 updated_at
```

## 迁移指南

### 前端代码迁移

**用户信息字段变更**:
```typescript
// ❌ v1.x 旧写法
userInfo.created_at
userInfo.userRate

// ✅ v2.0 新写法  
userInfo.create_time
userInfo.price_rate
```

**角色判断逻辑**:
```typescript
// ✅ 推荐写法：使用数值角色进行权限判断
if (userInfo.role === 1) {
  // 管理员权限
}

// ✅ 显示用途：使用字符串角色
const roleText = {
  'admin': '管理员',
  'agent': '代理商', 
  'user': '普通用户'
}[userInfo.user_role];
```

### 后端代码迁移

**SQL查询修正**:
```sql
-- ✅ 正确的字段映射
SELECT 
  u.user_id,
  u.role,           -- 数值角色
  u.user_role,      -- 字符串角色
  u.price_rate,     -- 费率字段
  u.create_time,    -- 时间字段
  u.update_time
FROM fd_user u;
```

**JSON字段处理**:
```typescript
// ✅ 正确的JSON字段处理
course_info: order.course_info ? 
  (typeof order.course_info === 'string' ? 
    JSON.parse(order.course_info) : 
    order.course_info
  ) : null
```

## 兼容性说明

### 向后兼容

- API接口在过渡期内可能同时返回新旧字段名
- 前端应优先使用新字段名
- 旧字段名将在v3.0版本中移除

### 数据库兼容

- 数据库表结构保持不变
- 仅修正了代码中的字段映射
- 无需数据迁移

## 测试验证

### 验证清单

- [ ] 用户登录功能正常
- [ ] 用户信息获取字段完整
- [ ] 订单列表查询无SQL错误
- [ ] JSON字段正确解析
- [ ] 前端数据绑定正确
- [ ] 角色权限判断正常

### 测试用例

**用户信息API测试**:
```bash
curl -H "Authorization: Bearer {token}" \
     http://localhost:3000/api/auth/getUserInfo
```

**预期响应包含所有新字段**:
- `user_role`
- `price_rate` 
- `total_recharge`
- `create_time`

## 更新记录

- **2024-01-XX**: 创建字段变更记录文档
- **2024-01-XX**: 修正用户表字段映射
- **2024-01-XX**: 统一时间字段命名
- **2024-01-XX**: 修正SQL查询语句错误
- **2024-01-XX**: 添加JSON字段处理
- **2024-01-XX**: 统一配置管理系统
