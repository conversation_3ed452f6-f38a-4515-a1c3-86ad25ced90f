# API接口规范文档

## 1. 通用规范

### 1.1 基础信息
- **API版本**: v2.0
- **基础URL**: `http://localhost:3000/api`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

### 1.2 版本更新说明
**v2.0 重要变更**:
- 统一时间字段使用 `create_time` 和 `update_time`
- 用户费率字段统一使用 `price_rate`
- 用户角色字段规范：`role` (数值) 和 `user_role` (字符串)
- 修正SQL查询语句中的语法错误
- 添加JSON字段的正确处理

### 1.2 统一响应格式
所有API接口统一返回以下格式：

```json
{
  "code": "0000",           // 响应码，0000表示成功
  "msg": "操作成功",         // 响应消息
  "data": {}               // 响应数据，可以是对象、数组或null
}
```

### 1.3 分页响应格式
对于分页查询接口，data字段包含以下结构：

```json
{
  "code": "0000",
  "msg": "查询成功",
  "data": {
    "records": [],          // 数据列表
    "current": 1,           // 当前页码
    "size": 10,            // 每页大小
    "total": 100           // 总记录数
  }
}
```

### 1.4 错误码规范
- `0000`: 操作成功
- `1001`: 参数错误
- `1002`: 数据不存在
- `1003`: 数据已存在
- `1004`: 操作失败
- `2001`: 未登录
- `2002`: 权限不足
- `2003`: 登录过期
- `3001`: 系统错误
- `3002`: 数据库错误
- `4001`: 授权验证失败

## 2. 认证接口

### 2.1 用户登录
- **接口**: `POST /api/auth/login`
- **描述**: 用户登录获取token
- **请求参数**:
```json
{
  "userName": "admin",      // 用户名，必填
  "password": "123456"      // 密码，必填
}
```

- **响应示例**:
```json
{
  "code": "0000",
  "msg": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  }
}
```

### 2.2 获取用户信息
- **接口**: `GET /api/auth/getUserInfo`
- **描述**: 获取当前登录用户的详细信息
- **请求头**: `Authorization: Bearer {token}`

- **响应示例**:
```json
{
  "code": "0000",
  "msg": "获取用户信息成功",
  "data": {
    "user_id": 1,
    "username": "admin",
    "nickname": "管理员",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "avatar": null,
    "role": 1,                    // 数值角色：1=管理员，2=代理商，3=普通用户
    "user_role": "admin",         // 字符串角色：admin/agent/user
    "status": 1,                  // 状态：0=禁用，1=启用
    "create_time": "2024-01-01 12:00:00",
    "update_time": "2024-01-01 12:00:00",
    "invite_code": "ABC123",
    "balance": 100.00,
    "price_rate": 0.85,          // 用户费率
    "total_recharge": 500.00,    // 总充值金额
    "referrer_id": null,         // 推荐人ID
    "level_id": 1,               // 用户等级ID
    "sid": null,                 // 上级用户ID
    "roles": ["admin"],
    "permissions": ["user:read", "user:write"]
  }
}
```

**字段说明**:
- `role`: 数值类型角色，用于权限判断
- `user_role`: 字符串类型角色，用于显示
- `price_rate`: 用户费率，影响订单价格计算
- `create_time`: 统一使用此字段名，格式为 YYYY-MM-DD HH:mm:ss
- `total_recharge`: 用户总充值金额
- `referrer_id`: 推荐人用户ID，用于推荐关系
- `level_id`: 用户等级ID，关联用户等级表
- **响应示例**:
```json
{
  "code": "0000",
  "msg": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIs...",
    "userInfo": {
      "userId": 1,
      "username": "admin",
      "userRole": "super",
      "permissions": ["platform:manage", "user:manage"]
    }
  }
}
```

### 2.2 获取用户信息
- **接口**: `GET /api/auth/getUserInfo`
- **描述**: 获取当前登录用户信息
- **请求头**: `Authorization: Bearer {token}`
- **响应示例**:
```json
{
  "code": "0000",
  "msg": "获取成功",
  "data": {
    "userId": 1,
    "username": "admin",
    "userRole": "super",
    "permissions": ["platform:manage", "user:manage"],
    "balance": 1000.00
  }
}
```

## 3. 平台管理接口

### 3.1 获取平台列表
- **接口**: `GET /api/platform/list`
- **描述**: 分页获取平台商品列表
- **权限**: 管理员权限
- **请求参数**:
  - `current`: 页码，默认1
  - `size`: 每页大小，默认10
  - `name`: 商品名称，可选
  - `platformType`: 平台类型，可选
  - `status`: 状态，可选

### 3.2 获取平台详情
- **接口**: `GET /api/platform/:id`
- **描述**: 获取指定平台商品详情
- **权限**: 管理员权限

### 3.3 创建平台商品
- **接口**: `POST /api/platform`
- **描述**: 创建新的平台商品
- **权限**: 管理员权限
- **请求参数**:
```json
{
  "providerId": 1,          // 服务商ID，必填
  "itemCode": "test001",    // 商品代码，必填
  "name": "测试平台",        // 商品名称，必填
  "platformType": "chaoxing", // 平台类型，必填
  "price": 50.00,          // 价格，必填
  "costPrice": 30.00,      // 成本价格，必填
  "status": 1              // 状态，必填
}
```

## 4. 服务商管理接口

### 4.1 获取服务商列表
- **接口**: `GET /api/provider/list`
- **描述**: 分页获取服务商列表
- **权限**: 管理员权限

### 4.2 测试API连接
- **接口**: `POST /api/provider/:id/test`
- **描述**: 测试服务商API连接状态
- **权限**: 管理员权限

## 5. 订单管理接口

### 5.1 获取订单列表
- **接口**: `GET /api/order/list`
- **描述**: 分页获取订单列表
- **权限**: 所有登录用户（普通用户只能查看自己的订单）
- **请求参数**:
  - `current`: 页码，默认1
  - `size`: 每页大小，默认10
  - `status`: 订单状态，可选
  - `startTime`: 开始时间，可选
  - `endTime`: 结束时间，可选

### 5.2 创建订单
- **接口**: `POST /api/order`
- **描述**: 创建新订单
- **权限**: 所有登录用户
- **请求参数**:
```json
{
  "itemId": 1,                    // 商品ID，必填
  "platformAccount": "<EMAIL>", // 平台账号，必填
  "platformPassword": "123456",    // 平台密码，必填
  "courseName": "测试课程",        // 课程名称，必填
  "quantity": 1                   // 数量，默认1
}
```

## 6. 错误处理规范

### 6.1 参数验证错误
```json
{
  "code": "1001",
  "msg": "参数错误：用户名不能为空",
  "data": null
}
```

### 6.2 权限错误
```json
{
  "code": "2002",
  "msg": "权限不足，无法访问该资源",
  "data": null
}
```

### 6.3 系统错误
```json
{
  "code": "3001",
  "msg": "系统内部错误，请稍后重试",
  "data": null
}
```

## 7. 请求头规范

### 7.1 必需请求头
- `Content-Type: application/json`
- `Authorization: Bearer {token}` (需要认证的接口)

### 7.2 可选请求头
- `X-Request-ID`: 请求唯一标识，用于日志追踪
- `X-Client-Version`: 客户端版本号
- `X-Platform`: 客户端平台标识

## 8. 状态码说明

### 8.1 HTTP状态码
- `200`: 请求成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

### 8.2 业务状态码
详见错误码规范部分。

## 9. API版本管理

### 9.1 版本策略
- 使用URL路径版本控制：`/api/v1/`, `/api/v2/`
- 当前版本：v1
- 向后兼容原则：新版本保持对旧版本的兼容

### 9.2 版本升级
- 重大变更时发布新版本
- 旧版本保持6个月的支持期
- 提前3个月通知版本废弃

## 10. 接口限流规范

### 10.1 限流策略
- 普通用户：100次/分钟
- 管理员：500次/分钟
- 超级管理员：1000次/分钟

### 10.2 限流响应
```json
{
  "code": "4002",
  "msg": "请求过于频繁，请稍后重试",
  "data": {
    "retryAfter": 60
  }
}
```
