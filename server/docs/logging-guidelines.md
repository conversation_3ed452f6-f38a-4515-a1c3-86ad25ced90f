# 日志记录规范

## 概述

项目使用统一的日志记录模块，所有操作日志都记录到 `fd_log` 表中。请使用 `server/src/utils/logger.ts` 中提供的日志记录函数，而不是直接操作数据库。

## 主要日志记录函数

### 1. 通用日志记录

```typescript
import { logOperation } from '../utils/logger';

await logOperation({
  user_id: number,           // 操作用户ID
  operator_id: number,       // 操作者ID
  operator_type: 'system' | 'admin' | 'user',
  module: string,            // 模块名称
  action: string,            // 操作类型
  target_type?: string,      // 目标类型
  target_id?: number,        // 目标ID
  content?: string,          // 操作描述
  data_before?: any,         // 操作前数据
  data_after?: any,          // 操作后数据
  result?: 'success' | 'failed',
  error_message?: string,
  request?: Request          // 请求对象（用于获取IP等信息）
});
```

### 2. 专门的日志记录函数

#### 用户操作日志
```typescript
import { logUserOperation } from '../utils/logger';

await logUserOperation(
  action: string,           // 操作类型
  targetUserId: number,     // 目标用户ID
  data_before?: any,        // 操作前数据
  data_after?: any,         // 操作后数据
  user_id?: number,         // 操作者ID
  operator_type?: 'system' | 'admin' | 'user',
  error_message?: string
);
```

#### 订单操作日志
```typescript
import { logOrderOperation } from '../utils/logger';

await logOrderOperation(
  action: string,           // 操作类型
  orderId: number,          // 订单ID
  data_before?: any,        // 操作前数据
  data_after?: any,         // 操作后数据
  user_id?: number,         // 操作者ID
  operator_type?: 'system' | 'admin' | 'user',
  error_message?: string,
  request?: Request
);
```

#### 商品操作日志
```typescript
import { logProductOperation } from '../utils/logger';

await logProductOperation(
  action: string,           // 操作类型
  productId: number,        // 商品ID
  data_before?: any,        // 操作前数据
  data_after?: any,         // 操作后数据
  user_id?: number,         // 操作者ID
  operator_type?: 'system' | 'admin' | 'user',
  error_message?: string
);
```

#### 系统操作日志
```typescript
import { logSystemOperation } from '../utils/logger';

await logSystemOperation(
  action: string,           // 操作类型
  details: any,             // 操作详情
  result?: 'success' | 'failed',
  error_message?: string,
  user_id?: number,
  operator_type?: 'system' | 'admin' | 'user'
);
```

## 模块分类

使用 `module` 字段区分不同类型的操作：

- `user`: 用户管理相关
- `permission`: 权限管理相关
- `finance`: 财务相关（充值、扣费等）
- `order`: 订单相关
- `product`: 商品相关
- `system`: 系统相关
- `platform`: 平台对接相关

## 操作类型示例

### 用户管理
- `create`: 创建用户
- `update`: 更新用户信息
- `delete`: 删除用户
- `update_permissions`: 更新用户权限
- `ban`: 封禁用户
- `unban`: 解封用户

### 财务管理
- `recharge`: 用户充值
- `deduct`: 用户扣费
- `consume`: 消费
- `refund`: 退款

### 订单管理
- `create`: 创建订单
- `update`: 更新订单
- `cancel`: 取消订单
- `sync`: 同步订单状态

## 使用示例

### 用户充值日志
```typescript
await logOperation({
  user_id: user_id,
  operator_id: operatorId,
  operator_type: 'admin',
  module: 'finance',
  action: 'recharge',
  target_type: 'user',
  target_id: user_id,
  content: `用户充值：${amount}元`,
  data_before: { balance: oldBalance },
  data_after: { balance: newBalance, amount, remark },
  result: 'success',
  request: req
});
```

### 权限更新日志
```typescript
await logUserOperation(
  'update_permissions',
  userId,
  { routes: [], functions: {} },
  { routes: newRoutes, functions: newFunctions },
  operatorId,
  'admin'
);
```

## 注意事项

1. **不要直接操作 fd_log 表**：始终使用提供的日志记录函数
2. **日志记录失败不应影响业务**：日志记录函数已经处理了异常情况
3. **包含足够的上下文信息**：确保日志包含足够的信息用于审计和调试
4. **使用标准的模块和操作类型**：保持日志的一致性和可查询性
5. **敏感信息处理**：避免在日志中记录密码等敏感信息

## 查询日志

查询日志时可以使用以下字段进行过滤：
- `user_id`: 操作用户
- `operator_id`: 操作者
- `module`: 模块
- `action`: 操作类型
- `target_type`: 目标类型
- `result`: 操作结果
- `create_time`: 创建时间
