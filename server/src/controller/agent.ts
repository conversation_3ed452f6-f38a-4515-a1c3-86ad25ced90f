import type { Response } from 'express';
import { Request } from 'express';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import type { AuthenticatedRequest } from '../middleware/auth';

/**
 * 代理分销系统控制器
 * 基于现有fd_user表的代理关系字段实现基础的上下级关系管理
 */

/** 获取用户的代理关系信息 */
export async function getAgentInfo(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      return res.json(createErrorResponse('用户未登录', ResponseCode.UNAUTHORIZED));
    }

    // 查询用户基本信息和代理关系
    const userQuery = `
      SELECT
        u.user_id,
        u.username,
        u.nickname,
        u.invite_code,
        u.referrer_id,
        u.sid,
        u.balance,
        u.total_recharge,
        u.create_time,
        r.username as referrer_username,
        r.nickname as referrer_nickname
      FROM fd_user u
      LEFT JOIN fd_user r ON u.referrer_id = r.user_id
      WHERE u.user_id = ?
    `;

    const userResult = await executeQuery(userQuery, [userId]);
    if (userResult.length === 0) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.PARAM_ERROR));
    }

    const userInfo = userResult[0];

    // 查询下级用户数量
    const subordinateCountQuery = `
      SELECT COUNT(*) as count
      FROM fd_user
      WHERE referrer_id = ? AND status = 1
    `;
    const subordinateCountResult = await executeQuery(subordinateCountQuery, [userId]);
    const subordinateCount = subordinateCountResult[0].count;

    // 查询下级用户列表（最近10个）
    const subordinateListQuery = `
      SELECT
        user_id,
        username,
        nickname,
        balance,
        total_recharge,
        create_time
      FROM fd_user
      WHERE referrer_id = ? AND status = 1
      ORDER BY create_time DESC
      LIMIT 10
    `;
    const subordinateList = await executeQuery(subordinateListQuery, [userId]);

    // 查询团队总充值金额
    const teamRechargeQuery = `
      SELECT COALESCE(SUM(total_recharge), 0) as team_recharge
      FROM fd_user
      WHERE referrer_id = ? AND status = 1
    `;
    const teamRechargeResult = await executeQuery(teamRechargeQuery, [userId]);
    const teamRecharge = teamRechargeResult[0].team_recharge;

    const agentInfo = {
      userInfo: {
        user_id: userInfo.user_id,
        username: userInfo.username,
        nickname: userInfo.nickname,
        invite_code: userInfo.invite_code,
        balance: userInfo.balance,
        total_recharge: userInfo.total_recharge,
        create_time: userInfo.create_time
      },
      referrer: userInfo.referrer_id
        ? {
            user_id: userInfo.referrer_id,
            username: userInfo.referrer_username,
            nickname: userInfo.referrer_nickname
          }
        : null,
      team: {
        subordinate_count: subordinateCount,
        team_recharge: teamRecharge,
        recent_subordinates: subordinateList
      }
    };

    return res.json(createSuccessResponse(agentInfo, '获取代理信息成功'));
  } catch (error) {
    console.error('🤝 [代理系统] 获取代理信息失败:', error);
    return res.json(createErrorResponse('获取代理信息失败', ResponseCode.ERROR));
  }
}

/** 获取下级用户列表 */
export async function getSubordinateList(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const userId = req.user?.userId;
    const { page = 1, pageSize = 20, keyword } = req.query;

    if (!userId) {
      return res.json(createErrorResponse('用户未登录', ResponseCode.UNAUTHORIZED));
    }

    // 构建查询条件
    let whereClause = 'referrer_id = ? AND status = 1';
    const queryParams: any[] = [userId];

    if (keyword) {
      whereClause += ' AND (username LIKE ? OR nickname LIKE ?)';
      queryParams.push(`%${keyword}%`, `%${keyword}%`);
    }

    // 查询总数
    const countSql = `SELECT COUNT(*) as total FROM fd_user WHERE ${whereClause}`;
    const countResult = await executeQuery(countSql, queryParams);
    const total = countResult[0].total;

    // 查询列表数据
    const offset = (Number(page) - 1) * Number(pageSize);
    const listSql = `
      SELECT
        user_id,
        username,
        nickname,
        balance,
        total_recharge,
        create_time,
        last_login_time
      FROM fd_user
      WHERE ${whereClause}
      ORDER BY create_time DESC
      LIMIT ? OFFSET ?
    `;

    const list = await executeQuery(listSql, [...queryParams, Number(pageSize), offset]);

    return res.json(
      createSuccessResponse(
        {
          list,
          pagination: {
            page: Number(page),
            pageSize: Number(pageSize),
            total
          }
        },
        '获取下级用户列表成功'
      )
    );
  } catch (error) {
    console.error('🤝 [代理系统] 获取下级用户列表失败:', error);
    return res.json(createErrorResponse('获取下级用户列表失败', ResponseCode.ERROR));
  }
}

/** 为下级用户充值 */
export async function rechargeForSubordinate(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const userId = req.user?.userId;
    const { target_user_id, amount, description } = req.body;

    if (!userId) {
      return res.json(createErrorResponse('用户未登录', ResponseCode.UNAUTHORIZED));
    }

    if (!target_user_id || !amount || amount <= 0) {
      return res.json(createErrorResponse('参数错误', ResponseCode.PARAM_ERROR));
    }

    // 验证目标用户是否为当前用户的下级
    const relationQuery = `
      SELECT user_id, username, nickname, balance
      FROM fd_user
      WHERE user_id = ? AND referrer_id = ? AND status = 1
    `;
    const relationResult = await executeQuery(relationQuery, [target_user_id, userId]);

    if (relationResult.length === 0) {
      return res.json(createErrorResponse('目标用户不是您的下级或不存在', ResponseCode.PARAM_ERROR));
    }

    const targetUser = relationResult[0];

    // 查询当前用户余额
    const currentUserQuery = `
      SELECT balance FROM fd_user WHERE user_id = ?
    `;
    const currentUserResult = await executeQuery(currentUserQuery, [userId]);
    const currentBalance = currentUserResult[0].balance;

    if (Number(currentBalance) < Number(amount)) {
      return res.json(createErrorResponse('余额不足', ResponseCode.PARAM_ERROR));
    }

    // 执行转账操作
    const newCurrentBalance = Number(currentBalance) - Number(amount);
    const newTargetBalance = Number(targetUser.balance) + Number(amount);

    // 更新当前用户余额（扣减）
    await executeQuery('UPDATE fd_user SET balance = ? WHERE user_id = ?', [newCurrentBalance, userId]);

    // 更新目标用户余额（增加）
    await executeQuery('UPDATE fd_user SET balance = ?, total_recharge = total_recharge + ? WHERE user_id = ?', [
      newTargetBalance,
      amount,
      target_user_id
    ]);

    // 记录财务流水（当前用户支出）
    await executeQuery(
      `
      INSERT INTO fd_finance (
        user_id, type, amount, balance_before, balance_after,
        related_id, related_type, description, operator_id
      ) VALUES (?, 'consume', ?, ?, ?, ?, 'recharge', ?, ?)
    `,
      [userId, -amount, currentBalance, newCurrentBalance, target_user_id, description || '为下级充值', userId]
    );

    // 记录财务流水（目标用户收入）
    await executeQuery(
      `
      INSERT INTO fd_finance (
        user_id, type, amount, balance_before, balance_after,
        related_id, related_type, description, operator_id
      ) VALUES (?, 'recharge', ?, ?, ?, ?, 'recharge', ?, ?)
    `,
      [target_user_id, amount, targetUser.balance, newTargetBalance, userId, description || '上级充值', userId]
    );

    console.log(`🤝 [代理系统] 充值成功: ${userId} -> ${target_user_id}, 金额: ${amount}`);

    return res.json(
      createSuccessResponse(
        {
          target_user: {
            user_id: target_user_id,
            username: targetUser.username,
            nickname: targetUser.nickname,
            balance_before: targetUser.balance,
            balance_after: newTargetBalance
          },
          current_user: {
            balance_before: currentBalance,
            balance_after: newCurrentBalance
          },
          amount
        },
        '充值成功'
      )
    );
  } catch (error) {
    console.error('🤝 [代理系统] 为下级充值失败:', error);
    return res.json(createErrorResponse('充值失败', ResponseCode.ERROR));
  }
}

/** 修改邀请码 */
export async function updateInviteCode(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const userId = req.user?.userId;
    const { invite_code } = req.body;

    if (!userId) {
      return res.json(createErrorResponse('用户未登录', ResponseCode.UNAUTHORIZED));
    }

    if (!invite_code || invite_code.length < 4 || invite_code.length > 20) {
      return res.json(createErrorResponse('邀请码长度必须在4-20个字符之间', ResponseCode.PARAM_ERROR));
    }

    // 检查邀请码是否已被使用
    const checkQuery = `
      SELECT user_id FROM fd_user WHERE invite_code = ? AND user_id != ?
    `;
    const checkResult = await executeQuery(checkQuery, [invite_code, userId]);

    if (checkResult.length > 0) {
      return res.json(createErrorResponse('邀请码已被使用', ResponseCode.PARAM_ERROR));
    }

    // 更新邀请码
    await executeQuery('UPDATE fd_user SET invite_code = ? WHERE user_id = ?', [invite_code, userId]);

    console.log(`🤝 [代理系统] 邀请码更新成功: ${userId} -> ${invite_code}`);

    return res.json(
      createSuccessResponse(
        {
          invite_code
        },
        '邀请码更新成功'
      )
    );
  } catch (error) {
    console.error('🤝 [代理系统] 更新邀请码失败:', error);
    return res.json(createErrorResponse('更新邀请码失败', ResponseCode.ERROR));
  }
}

/** 获取代理统计数据 */
export async function getAgentStatistics(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const userId = req.user?.userId;

    if (!userId) {
      return res.json(createErrorResponse('用户未登录', ResponseCode.UNAUTHORIZED));
    }

    // 直接下级统计
    const directSubordinatesQuery = `
      SELECT
        COUNT(*) as count,
        COALESCE(SUM(total_recharge), 0) as total_recharge
      FROM fd_user
      WHERE referrer_id = ? AND status = 1
    `;
    const directResult = await executeQuery(directSubordinatesQuery, [userId]);

    // 今日新增下级
    const todayNewQuery = `
      SELECT COUNT(*) as count
      FROM fd_user
      WHERE referrer_id = ? AND status = 1 AND DATE(create_time) = CURDATE()
    `;
    const todayNewResult = await executeQuery(todayNewQuery, [userId]);

    // 本月新增下级
    const monthNewQuery = `
      SELECT COUNT(*) as count
      FROM fd_user
      WHERE referrer_id = ? AND status = 1
      AND YEAR(create_time) = YEAR(NOW())
      AND MONTH(create_time) = MONTH(NOW())
    `;
    const monthNewResult = await executeQuery(monthNewQuery, [userId]);

    // 最近7天新增趋势
    const trendQuery = `
      SELECT
        DATE(create_time) as date,
        COUNT(*) as count
      FROM fd_user
      WHERE referrer_id = ? AND status = 1
      AND create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
      GROUP BY DATE(create_time)
      ORDER BY date ASC
    `;
    const trendResult = await executeQuery(trendQuery, [userId]);

    const statistics = {
      direct_subordinates: {
        count: directResult[0].count,
        total_recharge: directResult[0].total_recharge
      },
      today_new: todayNewResult[0].count,
      month_new: monthNewResult[0].count,
      trend: trendResult.map((item: any) => ({
        date: item.date,
        count: item.count
      }))
    };

    return res.json(createSuccessResponse(statistics, '获取代理统计数据成功'));
  } catch (error) {
    console.error('🤝 [代理系统] 获取代理统计数据失败:', error);
    return res.json(createErrorResponse('获取代理统计数据失败', ResponseCode.ERROR));
  }
}
