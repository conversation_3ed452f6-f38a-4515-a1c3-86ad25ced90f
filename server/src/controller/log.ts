/**
 * 统一日志管理控制器
 * 所有操作日志都记录到fd_log表中
 */

import type { Response } from 'express';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import type { AuthenticatedRequest } from '../middleware/auth';
import { logOperation } from '../utils/logger';

/**
 * 记录操作日志
 */
export async function createLog(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { action, module, target_type, target_id, details, status = 'success', error_message } = req.body;

    if (!action || !module) {
      return res.json(createErrorResponse('操作类型和模块不能为空', ResponseCode.PARAM_ERROR));
    }

    const user_id = req.user?.userId || 0;
    const username = req.user?.userName || 'system';

    console.log('📝 [日志管理] 记录操作日志:', {
      user_id,
      username,
      action,
      module,
      target_type,
      target_id,
      status
    });

    // 适配现有表结构
    const operator_type = user_id === 0 ? 'system' : req.user?.roles?.includes('admin') ? 'admin' : 'user';
    const result_status = status === 'success' ? 'success' : 'failed';

    // 使用新的日志记录函数
    await logOperation({
      user_id,
      operator_type,
      module,
      action,
      target_type,
      target_id,
      content: JSON.stringify(details || {}),
      data_after: details,
      result: result_status,
      error_message,
      request: req
    });

    const result = { insertId: Date.now() }; // 模拟插入结果

    return res.json(
      createSuccessResponse(
        {
          log_id: result.insertId
        },
        '日志记录成功'
      )
    );
  } catch (error: any) {
    console.error('📝 [日志管理] 记录日志失败:', error);
    return res.json(createErrorResponse(error.message || '记录日志失败', ResponseCode.ERROR));
  }
}

/**
 * 获取用户操作日志
 */
export async function getUserLogs(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { userId } = req.params;
    const { limit = 50, offset = 0 } = req.query;

    if (!userId || isNaN(Number(userId))) {
      return res.json(createErrorResponse('用户ID无效', ResponseCode.PARAM_ERROR));
    }

    console.log('📝 [日志管理] 获取用户操作日志:', { userId, limit, offset });

    // 查询用户操作日志
    const userIdNum = Number(userId);
    const limitNum = Number(limit);
    const offsetNum = Number(offset);

    const query = `
      SELECT
        log_id,
        user_id,
        operator_type,
        module,
        action,
        target_type,
        target_id,
        content,
        result,
        ip_address,
        user_agent,
        create_time
      FROM fd_log
      WHERE user_id = ${userIdNum}
      ORDER BY create_time DESC
      LIMIT ${limitNum} OFFSET ${offsetNum}
    `;

    const logs = await executeQuery(query, []);

    // 格式化日志数据
    const formattedLogs = logs.map((log: any) => {
      let details = null;
      try {
        // 尝试解析JSON，如果失败则使用原始内容
        details = log.content ? JSON.parse(log.content) : null;
      } catch {
        // 如果不是JSON格式，直接使用原始内容
        details = log.content;
      }

      return {
        log_id: log.log_id,
        operation_type: log.action,
        operation_desc: `${log.module} - ${log.action}`,
        target_type: log.target_type,
        target_id: log.target_id,
        result: log.result,
        ip_address: log.ip_address,
        user_agent: log.user_agent,
        create_time: log.create_time,
        details
      };
    });

    return res.json(createSuccessResponse('获取用户日志成功', formattedLogs));
  } catch (error) {
    console.error('获取用户日志失败:', error);
    return res.json(createErrorResponse('获取用户日志失败', ResponseCode.SYSTEM_ERROR));
  }
}

/**
 * 获取日志列表
 */
export async function getLogList(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const {
      page = 1,
      pageSize = 20,
      user_id,
      username,
      action,
      module,
      target_type,
      status,
      start_date,
      end_date,
      keyword
    } = req.query;

    console.log('📝 [日志管理] 获取日志列表:', {
      page,
      pageSize,
      module,
      action,
      status
    });

    // 构建查询条件
    const conditions = ['1 = 1'];
    const params: any[] = [];

    if (user_id) {
      conditions.push('user_id = ?');
      params.push(user_id);
    }

    if (username) {
      conditions.push('operator_type LIKE ?');
      params.push(`%${username}%`);
    }

    if (action) {
      conditions.push('action = ?');
      params.push(action);
    }

    if (module) {
      conditions.push('module = ?');
      params.push(module);
    }

    if (target_type) {
      conditions.push('target_type = ?');
      params.push(target_type);
    }

    if (status) {
      conditions.push('result = ?');
      params.push(status === 'success' ? 'success' : 'failed');
    }

    if (start_date) {
      conditions.push('create_time >= ?');
      params.push(start_date);
    }

    if (end_date) {
      conditions.push('create_time <= ?');
      params.push(end_date);
    }

    if (keyword) {
      conditions.push('(action LIKE ? OR module LIKE ? OR content LIKE ?)');
      params.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
    }

    const whereClause = conditions.join(' AND ');

    // 获取总数
    const countQuery = `SELECT COUNT(*) as total FROM fd_log WHERE ${whereClause}`;
    const countResult = await executeQuery(countQuery, params);
    const total = countResult[0].total;

    // 获取分页数据
    const offset = (Number(page) - 1) * Number(pageSize);
    const listQuery = `
      SELECT
        log_id,
        user_id,
        operator_id,
        operator_type as username,
        action,
        module,
        target_type,
        target_id,
        content as details,
        data_after,
        ip_address,
        user_agent,
        result as status,
        error_message,
        create_time
      FROM fd_log
      WHERE ${whereClause}
      ORDER BY create_time DESC
      LIMIT ${Number(pageSize)} OFFSET ${offset}
    `;

    const list = await executeQuery(listQuery, params);

    // 解析JSON字段
    const processedList = list.map((item: any) => {
      let details = {};
      let data_after = {};

      // 安全解析 details 字段
      if (item.details) {
        if (typeof item.details === 'string') {
          try {
            details = JSON.parse(item.details);
          } catch (error) {
            console.warn('解析details字段失败:', item.details);
            details = { raw: item.details };
          }
        } else {
          details = item.details;
        }
      }

      // 安全解析 data_after 字段
      if (item.data_after) {
        if (typeof item.data_after === 'string') {
          try {
            data_after = JSON.parse(item.data_after);
          } catch (error) {
            console.warn('解析data_after字段失败:', item.data_after);
            data_after = { raw: item.data_after };
          }
        } else {
          data_after = item.data_after;
        }
      }

      return {
        ...item,
        details,
        data_after
      };
    });

    const totalPages = Math.ceil(total / Number(pageSize));

    return res.json(
      createSuccessResponse(
        {
          list: processedList,
          total,
          page: Number(page),
          pageSize: Number(pageSize),
          totalPages
        },
        '获取日志列表成功'
      )
    );
  } catch (error: any) {
    console.error('📝 [日志管理] 获取日志列表失败:', error);
    return res.json(createErrorResponse(error.message || '获取日志列表失败', ResponseCode.ERROR));
  }
}

/**
 * 获取日志详情
 */
export async function getLogDetail(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { logId } = req.params;

    const query = `
      SELECT
        log_id,
        user_id,
        operator_id,
        operator_type as username,
        action,
        module,
        target_type,
        target_id,
        content as details,
        data_before,
        data_after,
        ip_address,
        user_agent,
        result as status,
        error_message,
        create_time
      FROM fd_log
      WHERE log_id = ?
    `;

    const result = await executeQuery(query, [logId]);

    if (result.length === 0) {
      return res.json(createErrorResponse('日志不存在', ResponseCode.DATA_NOT_FOUND));
    }

    const log = result[0];
    log.details = log.details ? JSON.parse(log.details) : {};
    log.data_before = log.data_before ? JSON.parse(log.data_before) : {};
    log.data_after = log.data_after ? JSON.parse(log.data_after) : {};

    return res.json(createSuccessResponse(log, '获取日志详情成功'));
  } catch (error: any) {
    console.error('📝 [日志管理] 获取日志详情失败:', error);
    return res.json(createErrorResponse(error.message || '获取日志详情失败', ResponseCode.ERROR));
  }
}

/**
 * 删除日志
 */
export async function deleteLog(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { logId } = req.params;

    // 检查日志是否存在
    const checkQuery = 'SELECT log_id FROM fd_log WHERE log_id = ?';
    const checkResult = await executeQuery(checkQuery, [logId]);

    if (checkResult.length === 0) {
      return res.json(createErrorResponse('日志不存在', ResponseCode.DATA_NOT_FOUND));
    }

    // 删除日志
    const deleteQuery = 'DELETE FROM fd_log WHERE log_id = ?';
    await executeQuery(deleteQuery, [logId]);

    console.log('📝 [日志管理] 删除日志成功:', logId);

    return res.json(createSuccessResponse(null, '删除日志成功'));
  } catch (error: any) {
    console.error('📝 [日志管理] 删除日志失败:', error);
    return res.json(createErrorResponse(error.message || '删除日志失败', ResponseCode.ERROR));
  }
}

/**
 * 批量删除日志
 */
export async function batchDeleteLogs(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { logIds } = req.body;

    if (!logIds || !Array.isArray(logIds) || logIds.length === 0) {
      return res.json(createErrorResponse('日志ID列表不能为空', ResponseCode.PARAM_ERROR));
    }

    const placeholders = logIds.map(() => '?').join(',');
    const deleteQuery = `DELETE FROM fd_log WHERE log_id IN (${placeholders})`;
    const result = await executeQuery(deleteQuery, logIds);

    console.log('📝 [日志管理] 批量删除日志成功:', result.affectedRows);

    return res.json(
      createSuccessResponse(
        {
          affectedRows: result.affectedRows
        },
        '批量删除日志成功'
      )
    );
  } catch (error: any) {
    console.error('📝 [日志管理] 批量删除日志失败:', error);
    return res.json(createErrorResponse(error.message || '批量删除日志失败', ResponseCode.ERROR));
  }
}
