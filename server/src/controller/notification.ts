import type { Response } from 'express';
import { Request } from 'express';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import type { AuthenticatedRequest } from '../middleware/auth';

/**
 * 通知管理控制器
 * 遵循项目规范，提供完整的通知管理功能
 */

/** 创建系统公告 */
export async function createAnnouncement(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { title, content, type = 'info', priority = 'normal', targetUsers = 'all', expiresAt } = req.body;
    const userId = req.user?.userId;
    const username = req.user?.userName;

    if (!title || !content) {
      return res.json(createErrorResponse('标题和内容不能为空', ResponseCode.PARAM_ERROR));
    }

    // 插入公告
    const insertResult = await executeQuery(
      `
      INSERT INTO fd_announcement (
        title, content, type, priority, target_users, 
        creator_id, creator_name, expires_at, status, create_time
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1, NOW())
    `,
      [title, content, type, priority, targetUsers, userId, username, expiresAt]
    );

    console.log(`📢 [通知管理] 创建系统公告: ${title}`);

    return res.json(
      createSuccessResponse(
        {
          announcementId: insertResult.insertId,
          title,
          createTime: new Date().toLocaleString('zh-CN')
        },
        '系统公告创建成功'
      )
    );
  } catch (error) {
    console.error('📢 [通知管理] 创建系统公告失败:', error);
    return res.json(createErrorResponse('创建系统公告失败', ResponseCode.ERROR));
  }
}

/** 获取系统公告列表 */
export async function getAnnouncementList(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { page = 1, pageSize = 20, type = '', status = '', keyword = '' } = req.query;

    const offset = (Number(page) - 1) * Number(pageSize);
    const whereConditions = ['1=1'];
    const queryParams: any[] = [];

    // 类型筛选
    if (type) {
      whereConditions.push('type = ?');
      queryParams.push(type);
    }

    // 状态筛选
    if (status) {
      whereConditions.push('status = ?');
      queryParams.push(status);
    }

    // 关键词搜索
    if (keyword) {
      whereConditions.push('(title LIKE ? OR content LIKE ?)');
      queryParams.push(`%${keyword}%`, `%${keyword}%`);
    }

    const whereClause = whereConditions.join(' AND ');

    // 查询公告列表
    const announcementListQuery = `
      SELECT 
        announcement_id as announcementId,
        title,
        content,
        type,
        priority,
        target_users as targetUsers,
        creator_name as creatorName,
        status,
        expires_at as expiresAt,
        create_time as createTime,
        update_time as updateTime
      FROM fd_announcement
      WHERE ${whereClause}
      ORDER BY create_time DESC
      LIMIT ? OFFSET ?
    `;

    queryParams.push(Number(pageSize), offset);
    const announcementList = await executeQuery(announcementListQuery, queryParams);

    // 查询总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM fd_announcement
      WHERE ${whereClause}
    `;

    const countParams = queryParams.slice(0, -2);
    const countResult = await executeQuery(countQuery, countParams);
    const total = countResult[0]?.total || 0;

    // 处理公告数据
    const processedAnnouncementList = announcementList.map((announcement: any) => ({
      ...announcement,
      createTime: new Date(announcement.createTime).toLocaleString('zh-CN'),
      updateTime: announcement.updateTime ? new Date(announcement.updateTime).toLocaleString('zh-CN') : null,
      expiresAt: announcement.expiresAt ? new Date(announcement.expiresAt).toLocaleString('zh-CN') : null,
      isExpired: announcement.expiresAt ? new Date(announcement.expiresAt) < new Date() : false
    }));

    return res.json(
      createSuccessResponse(
        {
          list: processedAnnouncementList,
          pagination: {
            page: Number(page),
            pageSize: Number(pageSize),
            total,
            totalPages: Math.ceil(total / Number(pageSize))
          }
        },
        '获取系统公告列表成功'
      )
    );
  } catch (error) {
    console.error('📢 [通知管理] 获取系统公告列表失败:', error);
    return res.json(createErrorResponse('获取系统公告列表失败', ResponseCode.ERROR));
  }
}

/** 更新系统公告 */
export async function updateAnnouncement(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { announcementId } = req.params;
    const { title, content, type, priority, targetUsers, expiresAt, status } = req.body;

    if (!announcementId) {
      return res.json(createErrorResponse('公告ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查公告是否存在
    const existingAnnouncement = await executeQuery('SELECT * FROM fd_announcement WHERE announcement_id = ?', [
      announcementId
    ]);

    if (existingAnnouncement.length === 0) {
      return res.json(createErrorResponse('公告不存在', ResponseCode.PARAM_ERROR));
    }

    // 更新公告
    await executeQuery(
      `
      UPDATE fd_announcement 
      SET title = ?, content = ?, type = ?, priority = ?, 
          target_users = ?, expires_at = ?, status = ?, update_time = NOW()
      WHERE announcement_id = ?
    `,
      [title, content, type, priority, targetUsers, expiresAt, status, announcementId]
    );

    console.log(`📢 [通知管理] 更新系统公告: ${title}`);

    return res.json(createSuccessResponse(null, '系统公告更新成功'));
  } catch (error) {
    console.error('📢 [通知管理] 更新系统公告失败:', error);
    return res.json(createErrorResponse('更新系统公告失败', ResponseCode.ERROR));
  }
}

/** 删除系统公告 */
export async function deleteAnnouncement(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { announcementId } = req.params;

    if (!announcementId) {
      return res.json(createErrorResponse('公告ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 删除公告
    const deleteResult = await executeQuery('DELETE FROM fd_announcement WHERE announcement_id = ?', [announcementId]);

    if (deleteResult.affectedRows === 0) {
      return res.json(createErrorResponse('公告不存在', ResponseCode.PARAM_ERROR));
    }

    console.log(`📢 [通知管理] 删除系统公告: ID ${announcementId}`);

    return res.json(createSuccessResponse(null, '系统公告删除成功'));
  } catch (error) {
    console.error('📢 [通知管理] 删除系统公告失败:', error);
    return res.json(createErrorResponse('删除系统公告失败', ResponseCode.ERROR));
  }
}

/** 创建用户通知 */
export async function createUserNotification(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { userId, title, content, type = 'info', actionUrl } = req.body;
    const creatorId = req.user?.userId;

    if (!userId || !title || !content) {
      return res.json(createErrorResponse('用户ID、标题和内容不能为空', ResponseCode.PARAM_ERROR));
    }

    // 插入用户通知
    const insertResult = await executeQuery(
      `
      INSERT INTO fd_user_notification (
        user_id, title, content, type, action_url, 
        creator_id, status, create_time
      ) VALUES (?, ?, ?, ?, ?, ?, 'unread', NOW())
    `,
      [userId, title, content, type, actionUrl, creatorId]
    );

    console.log(`📢 [通知管理] 创建用户通知: ${title} -> 用户${userId}`);

    return res.json(
      createSuccessResponse(
        {
          notificationId: insertResult.insertId,
          title,
          createTime: new Date().toLocaleString('zh-CN')
        },
        '用户通知创建成功'
      )
    );
  } catch (error) {
    console.error('📢 [通知管理] 创建用户通知失败:', error);
    return res.json(createErrorResponse('创建用户通知失败', ResponseCode.ERROR));
  }
}

/** 获取用户通知列表 */
export async function getUserNotifications(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const userId = req.user?.userId;
    const { page = 1, pageSize = 20, status = '' } = req.query;

    const offset = (Number(page) - 1) * Number(pageSize);
    const whereConditions = ['user_id = ?'];
    const queryParams: any[] = [userId];

    // 状态筛选
    if (status) {
      whereConditions.push('status = ?');
      queryParams.push(status);
    }

    const whereClause = whereConditions.join(' AND ');

    // 查询用户通知列表
    const notificationListQuery = `
      SELECT 
        notification_id as notificationId,
        title,
        content,
        type,
        action_url as actionUrl,
        status,
        create_time as createTime,
        read_time as readTime
      FROM fd_user_notification
      WHERE ${whereClause}
      ORDER BY create_time DESC
      LIMIT ? OFFSET ?
    `;

    queryParams.push(Number(pageSize), offset);
    const notificationList = await executeQuery(notificationListQuery, queryParams);

    // 查询总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM fd_user_notification
      WHERE ${whereClause}
    `;

    const countParams = queryParams.slice(0, -2);
    const countResult = await executeQuery(countQuery, countParams);
    const total = countResult[0]?.total || 0;

    // 查询未读数量
    const unreadCountResult = await executeQuery(
      'SELECT COUNT(*) as unreadCount FROM fd_user_notification WHERE user_id = ? AND status = "unread"',
      [userId]
    );
    const unreadCount = unreadCountResult[0]?.unreadCount || 0;

    // 处理通知数据
    const processedNotificationList = notificationList.map((notification: any) => ({
      ...notification,
      createTime: new Date(notification.createTime).toLocaleString('zh-CN'),
      readTime: notification.readTime ? new Date(notification.readTime).toLocaleString('zh-CN') : null
    }));

    return res.json(
      createSuccessResponse(
        {
          list: processedNotificationList,
          unreadCount,
          pagination: {
            page: Number(page),
            pageSize: Number(pageSize),
            total,
            totalPages: Math.ceil(total / Number(pageSize))
          }
        },
        '获取用户通知列表成功'
      )
    );
  } catch (error) {
    console.error('📢 [通知管理] 获取用户通知列表失败:', error);
    return res.json(createErrorResponse('获取用户通知列表失败', ResponseCode.ERROR));
  }
}

/** 标记通知为已读 */
export async function markNotificationAsRead(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { notificationId } = req.params;
    const userId = req.user?.userId;

    if (!notificationId) {
      return res.json(createErrorResponse('通知ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 更新通知状态
    const updateResult = await executeQuery(
      `
      UPDATE fd_user_notification 
      SET status = 'read', read_time = NOW()
      WHERE notification_id = ? AND user_id = ?
    `,
      [notificationId, userId]
    );

    if (updateResult.affectedRows === 0) {
      return res.json(createErrorResponse('通知不存在或无权限', ResponseCode.PARAM_ERROR));
    }

    return res.json(createSuccessResponse(null, '通知已标记为已读'));
  } catch (error) {
    console.error('📢 [通知管理] 标记通知为已读失败:', error);
    return res.json(createErrorResponse('标记通知为已读失败', ResponseCode.ERROR));
  }
}

/** 批量标记通知为已读 */
export async function markAllNotificationsAsRead(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const userId = req.user?.userId;

    // 更新所有未读通知
    const updateResult = await executeQuery(
      `
      UPDATE fd_user_notification 
      SET status = 'read', read_time = NOW()
      WHERE user_id = ? AND status = 'unread'
    `,
      [userId]
    );

    return res.json(
      createSuccessResponse(
        {
          updatedCount: updateResult.affectedRows
        },
        '所有通知已标记为已读'
      )
    );
  } catch (error) {
    console.error('📢 [通知管理] 批量标记通知为已读失败:', error);
    return res.json(createErrorResponse('批量标记通知为已读失败', ResponseCode.ERROR));
  }
}

/** 获取有效的系统公告（用户端） */
export async function getActiveAnnouncements(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { limit = 10 } = req.query;

    // 查询有效的系统公告
    const announcementsQuery = `
      SELECT 
        announcement_id as announcementId,
        title,
        content,
        type,
        priority,
        create_time as createTime
      FROM fd_announcement
      WHERE status = 1 
        AND (expires_at IS NULL OR expires_at > NOW())
        AND (target_users = 'all' OR target_users LIKE '%user%')
      ORDER BY priority DESC, create_time DESC
      LIMIT ?
    `;

    const announcements = await executeQuery(announcementsQuery, [Number(limit)]);

    // 处理公告数据
    const processedAnnouncements = announcements.map((announcement: any) => ({
      ...announcement,
      createTime: new Date(announcement.createTime).toLocaleString('zh-CN')
    }));

    return res.json(createSuccessResponse(processedAnnouncements, '获取系统公告成功'));
  } catch (error) {
    console.error('📢 [通知管理] 获取系统公告失败:', error);
    return res.json(createErrorResponse('获取系统公告失败', ResponseCode.ERROR));
  }
}
