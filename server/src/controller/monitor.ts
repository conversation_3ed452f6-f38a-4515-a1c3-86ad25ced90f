import * as os from 'node:os';
import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import type { Response } from 'express';
import { Request } from 'express';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import type { AuthenticatedRequest } from '../middleware/auth';
import { healthCheck, performanceMonitor } from '../middleware/performance';
import { securityMonitor } from '../utils/securityMonitor';

/**
 * 系统监控控制器
 * 遵循项目规范，提供完整的系统监控功能
 */

/** 获取系统健康状态 */
export async function getSystemHealth(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const health = healthCheck();

    // 数据库连接检查
    let dbStatus = 'healthy';
    try {
      await executeQuery('SELECT 1');
    } catch (error) {
      dbStatus = 'critical';
      health.checks.database = { status: 'critical', error: error instanceof Error ? error.message : String(error) };
    }

    // 磁盘空间检查
    const diskUsage = await getDiskUsage();
    if (diskUsage.usagePercent > 90) {
      health.checks.disk = { status: 'critical', usage: diskUsage };
      if (health.status !== 'critical') health.status = 'critical';
    } else if (diskUsage.usagePercent > 80) {
      health.checks.disk = { status: 'warning', usage: diskUsage };
      if (health.status === 'healthy') health.status = 'warning';
    } else {
      health.checks.disk = { status: 'healthy', usage: diskUsage };
    }

    // 服务状态检查
    const serviceStatus = await getServiceStatus();
    health.checks.services = serviceStatus;

    return res.json(createSuccessResponse(health, '获取系统健康状态成功'));
  } catch (error) {
    console.error('🔍 [系统监控] 获取系统健康状态失败:', error);
    return res.json(createErrorResponse('获取系统健康状态失败', ResponseCode.ERROR));
  }
}

/** 获取系统性能指标 */
export async function getPerformanceMetrics(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { limit = 100, type = 'all' } = req.query;

    const stats = performanceMonitor.getStats();
    let metrics = [];

    switch (type) {
      case 'slow':
        metrics = performanceMonitor.getSlowRequests(1000);
        break;
      case 'error':
        metrics = performanceMonitor.getErrorRequests();
        break;
      default:
        metrics = performanceMonitor.getRecentMetrics(Number(limit));
    }

    const performanceData = {
      stats,
      metrics,
      alerts: performanceMonitor.checkAlerts(),
      timestamp: Date.now()
    };

    return res.json(createSuccessResponse(performanceData, '获取性能指标成功'));
  } catch (error) {
    console.error('🔍 [系统监控] 获取性能指标失败:', error);
    return res.json(createErrorResponse('获取性能指标失败', ResponseCode.ERROR));
  }
}

/** 获取系统资源使用情况 */
export async function getSystemResources(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    const loadAverage = os.loadavg();
    const uptime = process.uptime();
    const diskUsage = await getDiskUsage();

    // CPU信息
    const cpus = os.cpus();
    const cpuInfo = {
      model: cpus[0]?.model || 'Unknown',
      cores: cpus.length,
      speed: cpus[0]?.speed || 0
    };

    // 网络接口信息
    const networkInterfaces = os.networkInterfaces();
    const networkInfo = Object.keys(networkInterfaces)
      .map(name => ({
        name,
        addresses: networkInterfaces[name]?.filter(addr => !addr.internal) || []
      }))
      .filter(iface => iface.addresses.length > 0);

    const resources = {
      memory: {
        total: os.totalmem(),
        free: os.freemem(),
        used: os.totalmem() - os.freemem(),
        usagePercent: ((os.totalmem() - os.freemem()) / os.totalmem()) * 100,
        process: memoryUsage
      },
      cpu: {
        info: cpuInfo,
        usage: cpuUsage,
        loadAverage,
        usagePercent: (loadAverage[0] / cpus.length) * 100
      },
      disk: diskUsage,
      network: networkInfo,
      system: {
        platform: os.platform(),
        arch: os.arch(),
        hostname: os.hostname(),
        uptime,
        nodeVersion: process.version
      }
    };

    return res.json(createSuccessResponse(resources, '获取系统资源成功'));
  } catch (error) {
    console.error('🔍 [系统监控] 获取系统资源失败:', error);
    return res.json(createErrorResponse('获取系统资源失败', ResponseCode.ERROR));
  }
}

/** 获取安全监控数据 */
export async function getSecurityMonitor(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const securityStats = securityMonitor.getSecurityStats();
    const recentEvents = securityMonitor.getRecentEvents(50);

    // 登录失败统计
    const loginFailuresResult = await executeQuery(`
      SELECT
        DATE(create_time) as date,
        COUNT(*) as count
      FROM fd_log
      WHERE action = 'login' AND result = 'failure'
        AND create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
      GROUP BY DATE(create_time)
      ORDER BY date ASC
    `);

    // IP访问统计
    const ipStatsResult = await executeQuery(`
      SELECT
        ip_address as client_ip,
        COUNT(*) as requestCount,
        COUNT(CASE WHEN result = 'failed' THEN 1 END) as failureCount
      FROM fd_log
      WHERE create_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        AND ip_address IS NOT NULL
      GROUP BY ip_address
      ORDER BY requestCount DESC
      LIMIT 20
    `);

    const securityData = {
      stats: securityStats,
      recentEvents,
      loginFailures: loginFailuresResult,
      ipStats: ipStatsResult,
      timestamp: Date.now()
    };

    return res.json(createSuccessResponse(securityData, '获取安全监控数据成功'));
  } catch (error) {
    console.error('🔍 [系统监控] 获取安全监控数据失败:', error);
    return res.json(createErrorResponse('获取安全监控数据失败', ResponseCode.ERROR));
  }
}

/** 获取数据库监控信息 */
export async function getDatabaseMonitor(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    // 数据库连接状态
    let connectionStatus = 'healthy';
    let connectionError = null;
    try {
      await executeQuery('SELECT 1');
    } catch (error) {
      connectionStatus = 'critical';
      connectionError = error instanceof Error ? error.message : String(error);
    }

    // 数据库大小统计
    const dbSizeResult = await executeQuery(`
      SELECT
        table_schema as 'database',
        ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) as 'size_mb'
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      GROUP BY table_schema
    `);

    // 表统计
    const tableStatsResult = await executeQuery(`
      SELECT
        table_name,
        table_rows,
        ROUND((data_length + index_length) / 1024 / 1024, 2) as 'size_mb',
        ROUND(data_length / 1024 / 1024, 2) as 'data_mb',
        ROUND(index_length / 1024 / 1024, 2) as 'index_mb'
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
      ORDER BY (data_length + index_length) DESC
      LIMIT 20
    `);

    // 慢查询统计（模拟数据，实际需要配置MySQL慢查询日志）
    const slowQueries: any[] = [];

    const dbMonitor = {
      connection: {
        status: connectionStatus,
        error: connectionError
      },
      size: dbSizeResult[0] || { database: 'unknown', size_mb: 0 },
      tables: tableStatsResult,
      slowQueries,
      timestamp: Date.now()
    };

    return res.json(createSuccessResponse(dbMonitor, '获取数据库监控信息成功'));
  } catch (error) {
    console.error('🔍 [系统监控] 获取数据库监控信息失败:', error);
    return res.json(createErrorResponse('获取数据库监控信息失败', ResponseCode.ERROR));
  }
}

/** 获取应用监控信息 */
export async function getApplicationMonitor(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    // 在线用户统计
    const onlineUsersResult = await executeQuery(`
      SELECT COUNT(DISTINCT user_id) as count
      FROM fd_log
      WHERE action = 'login'
        AND create_time >= DATE_SUB(NOW(), INTERVAL 30 MINUTE)
    `);

    // 活跃API统计
    const apiStatsResult = await executeQuery(`
      SELECT
        target_type as request_path,
        COUNT(*) as requestCount,
        AVG(CASE WHEN result = 'success' THEN 1 ELSE 0 END) * 100 as successRate
      FROM fd_log
      WHERE create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        AND target_type IS NOT NULL
      GROUP BY target_type
      ORDER BY requestCount DESC
      LIMIT 10
    `);

    // 错误统计
    const errorStatsResult = await executeQuery(`
      SELECT
        error_message,
        COUNT(*) as count
      FROM fd_log
      WHERE result = 'failure'
        AND create_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        AND error_message IS NOT NULL
      GROUP BY error_message
      ORDER BY count DESC
      LIMIT 10
    `);

    const appMonitor = {
      onlineUsers: onlineUsersResult[0]?.count || 0,
      apiStats: apiStatsResult,
      errorStats: errorStatsResult,
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      timestamp: Date.now()
    };

    return res.json(createSuccessResponse(appMonitor, '获取应用监控信息成功'));
  } catch (error) {
    console.error('🔍 [系统监控] 获取应用监控信息失败:', error);
    return res.json(createErrorResponse('获取应用监控信息失败', ResponseCode.ERROR));
  }
}

// ===== 工具函数 =====

/** 获取磁盘使用情况 */
async function getDiskUsage(): Promise<{
  total: number;
  free: number;
  used: number;
  usagePercent: number;
}> {
  try {
    const stats = await fs.stat(process.cwd());
    // 这里简化处理，实际应该使用更准确的磁盘空间检查方法
    const total = 100 * 1024 * 1024 * 1024; // 假设100GB
    const free = 50 * 1024 * 1024 * 1024; // 假设50GB可用
    const used = total - free;
    const usagePercent = (used / total) * 100;

    return { total, free, used, usagePercent };
  } catch (error) {
    return { total: 0, free: 0, used: 0, usagePercent: 0 };
  }
}

/** 获取服务状态 */
async function getServiceStatus(): Promise<Record<string, any>> {
  const services = {
    database: 'healthy',
    redis: 'unknown',
    email: 'unknown',
    backup: 'healthy'
  };

  // 检查数据库
  try {
    await executeQuery('SELECT 1');
    services.database = 'healthy';
  } catch {
    services.database = 'critical';
  }

  return services;
}
