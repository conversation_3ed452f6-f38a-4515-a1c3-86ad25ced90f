import type { Request, Response } from 'express';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';

/**
 * 用户角色优化控制器
 * 简化用户角色管理，每个用户只保留一个主要角色
 */

/**
 * 清理用户多余角色，每个用户只保留一个主要角色
 */
export async function cleanupUserRoles(req: Request, res: Response): Promise<any> {
  try {
    console.log('🧹 开始清理用户多余角色...');

    // 开始事务
    await executeQuery('START TRANSACTION');

    try {
      // 获取所有有多个角色的用户
      const usersWithMultipleRoles = await executeQuery(`
        SELECT user_id, COUNT(*) as role_count
        FROM fd_user_role
        WHERE status = 1
        GROUP BY user_id
        HAVING COUNT(*) > 1
      `);

      let cleanedCount = 0;

      for (const user of usersWithMultipleRoles as any[]) {
        const userId = user.user_id;

        // 获取用户的所有角色，按优先级排序
        const userRoles = await executeQuery(
          `
          SELECT ur.*, r.role_level, r.role_code
          FROM fd_user_role ur
          JOIN fd_role r ON ur.role_id = r.role_id
          WHERE ur.user_id = ? AND ur.status = 1
          ORDER BY ur.is_primary DESC, r.role_level ASC, ur.create_time ASC
        `,
          [userId]
        );

        if (userRoles.length > 1) {
          // 保留第一个角色（优先级最高的）
          const primaryRole = (userRoles as any[])[0];

          // 更新用户表的主要角色
          await executeQuery(
            `
            UPDATE fd_user
            SET user_role = ?
            WHERE user_id = ?
          `,
            [primaryRole.role_code, userId]
          );

          // 删除其他角色关联
          await executeQuery(
            `
            DELETE FROM fd_user_role
            WHERE user_id = ? AND id != ?
          `,
            [userId, primaryRole.id]
          );

          // 确保保留的角色是主要角色
          await executeQuery(
            `
            UPDATE fd_user_role
            SET is_primary = 1
            WHERE id = ?
          `,
            [primaryRole.id]
          );

          cleanedCount++;
          console.log(`🗑️ 用户 ${userId} 清理完成，保留角色: ${primaryRole.role_code}`);
        }
      }

      // 提交事务
      await executeQuery('COMMIT');

      console.log(`✅ 用户角色清理完成: 处理了 ${cleanedCount} 个用户`);

      return res.json(
        createSuccessResponse(
          {
            cleanedUserCount: cleanedCount,
            totalUsersWithMultipleRoles: usersWithMultipleRoles.length
          },
          '用户角色清理成功'
        )
      );
    } catch (error) {
      // 回滚事务
      await executeQuery('ROLLBACK');
      throw error;
    }
  } catch (error: any) {
    console.error('❌ 用户角色清理失败:', error);
    return res.json(createErrorResponse(error.message || '用户角色清理失败', ResponseCode.ERROR));
  }
}

/**
 * 设置用户主要角色（简化版）
 */
export async function setUserPrimaryRole(req: Request, res: Response): Promise<any> {
  try {
    const { userId, roleId } = req.body;
    const operatorId = (req as any).user.userId;

    if (!userId || !roleId) {
      return res.json(createErrorResponse('用户ID和角色ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查用户是否存在
    const user = await executeQuery('SELECT user_id, user_role FROM fd_user WHERE user_id = ?', [userId]);
    if (!user || user.length === 0) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.NOT_FOUND));
    }

    // 检查角色是否存在
    const role = await executeQuery('SELECT role_id, role_code, role_name FROM fd_role WHERE role_id = ?', [roleId]);
    if (!role || role.length === 0) {
      return res.json(createErrorResponse('角色不存在', ResponseCode.NOT_FOUND));
    }

    const roleInfo = role[0];

    // 开始事务
    await executeQuery('START TRANSACTION');

    try {
      // 删除用户的所有角色关联
      await executeQuery('DELETE FROM fd_user_role WHERE user_id = ?', [userId]);

      // 添加新的主要角色
      await executeQuery(
        `
        INSERT INTO fd_user_role (user_id, role_id, is_primary, grant_type, status, granted_by, create_time, update_time)
        VALUES (?, ?, 1, 'direct', 1, ?, NOW(), NOW())
      `,
        [userId, roleId, operatorId]
      );

      // 更新用户表的角色字段
      await executeQuery(
        `
        UPDATE fd_user
        SET user_role = ?, update_time = NOW()
        WHERE user_id = ?
      `,
        [roleInfo.role_code, userId]
      );

      // 提交事务
      await executeQuery('COMMIT');

      console.log(`👤 用户 ${userId} 角色设置成功: ${roleInfo.role_name}`);

      return res.json(
        createSuccessResponse(
          {
            userId,
            roleId,
            roleName: roleInfo.role_name,
            roleCode: roleInfo.role_code
          },
          '用户角色设置成功'
        )
      );
    } catch (error) {
      // 回滚事务
      await executeQuery('ROLLBACK');
      throw error;
    }
  } catch (error: any) {
    console.error('❌ 设置用户角色失败:', error);
    return res.json(createErrorResponse(error.message || '设置用户角色失败', ResponseCode.ERROR));
  }
}

/**
 * 获取用户的简化角色信息
 */
export async function getUserSimpleRole(req: Request, res: Response): Promise<any> {
  try {
    const userId = Number.parseInt(req.params.userId);

    // 获取用户基本信息和角色
    const userQuery = `
      SELECT
        u.user_id,
        u.username,
        u.user_role,
        r.role_id,
        r.role_name,
        r.role_description,
        r.role_level,
        ur.create_time as role_assigned_time,
        granter.username as granted_by_name
      FROM fd_user u
      LEFT JOIN fd_user_role ur ON u.user_id = ur.user_id AND ur.status = 1 AND ur.is_primary = 1
      LEFT JOIN fd_role r ON ur.role_id = r.role_id
      LEFT JOIN fd_user granter ON ur.granted_by = granter.user_id
      WHERE u.user_id = ?
    `;

    const result = await executeQuery(userQuery, [userId]);

    if (!result || result.length === 0) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.NOT_FOUND));
    }

    const userRole = result[0];

    return res.json(createSuccessResponse(userRole, '获取用户角色成功'));
  } catch (error: any) {
    console.error('❌ 获取用户角色失败:', error);
    return res.json(createErrorResponse(error.message || '获取用户角色失败', ResponseCode.ERROR));
  }
}

/**
 * 检查用户角色一致性
 */
export async function checkUserRoleConsistency(req: Request, res: Response): Promise<any> {
  try {
    // 检查用户表和角色关联表的一致性
    const inconsistentUsers = await executeQuery(`
      SELECT
        u.user_id,
        u.username,
        CAST(u.user_role AS CHAR) as table_role,
        CAST(r.role_code AS CHAR) as relation_role,
        COUNT(ur.role_id) as role_count
      FROM fd_user u
      LEFT JOIN fd_user_role ur ON u.user_id = ur.user_id AND ur.status = 1
      LEFT JOIN fd_role r ON ur.role_id = r.role_id
      GROUP BY u.user_id, u.user_role, r.role_code
      HAVING role_count != 1 OR CAST(u.user_role AS CHAR) != CAST(r.role_code AS CHAR) OR u.user_role IS NULL OR r.role_code IS NULL
    `);

    const result = {
      totalUsers: (await executeQuery('SELECT COUNT(*) as count FROM fd_user'))[0].count,
      inconsistentUsers: inconsistentUsers.length,
      inconsistentUserList: inconsistentUsers,
      isConsistent: inconsistentUsers.length === 0
    };

    return res.json(createSuccessResponse(result, '用户角色一致性检查完成'));
  } catch (error: any) {
    console.error('❌ 用户角色一致性检查失败:', error);
    return res.json(createErrorResponse(error.message || '用户角色一致性检查失败', ResponseCode.ERROR));
  }
}
