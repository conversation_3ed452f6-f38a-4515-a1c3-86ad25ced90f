/**
 * 查课控制器
 * 实现查课和批量查课功能
 */

import type { Response } from 'express';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import type { AuthenticatedRequest } from '../middleware/auth';
import { ProviderManager } from '../service/ProviderManager';
import { executeQuery } from '../utils/db';

/**
 * 基于商品的查课接口
 */
// 验证查课请求参数
function validateQueryParams(productId: any, username: any, password: any) {
  if (!productId || !username || !password) {
    return '商品ID、账号和密码不能为空';
  }
  return null;
}

// 验证商品和服务商状态
function validateProductAndProvider(product: any) {
  if (!product.provider_id) {
    return '商品未配置服务商';
  }
  if (product.provider_status !== 1) {
    return '服务商已禁用';
  }
  return null;
}

// 格式化课程数据
function formatCourseData(course: any, context: { username: string; userRealName: string; school: string }) {
  return {
    id: course.id,
    name: course.name,
    teacher: course.teacher || '',
    class: course.class || '',
    progress: course.progress || '',
    state: course.state || '',
    credit: course.credit || '',
    username: context.username,
    userRealName: context.userRealName,
    school: context.school || '自动识别'
  };
}

// 处理嵌套结构的课程数据
function processNestedCourses(item: any, username: string, school: string) {
  if (item.data && Array.isArray(item.data)) {
    return item.data.map((course: any) =>
      formatCourseData(course, {
        username,
        userRealName: item.userName || '',
        school
      })
    );
  }
  return [];
}

// 处理直接课程数据
function processDirectCourse(item: any, username: string, school: string) {
  // 支持统一配置返回的字段格式
  const courseId = item.course_id || item.id;
  const courseName = item.course_name || item.name;

  if (courseId && courseName) {
    // 统一字段格式
    const normalizedItem = {
      id: courseId,
      name: courseName,
      teacher: item.teacher || '',
      class: item.class_name || item.class || '',
      progress: item.progress || '',
      state: item.status || item.state || '',
      credit: item.credit || ''
    };

    return formatCourseData(normalizedItem, { username, userRealName: '', school });
  }
  return null;
}

// 提取所有课程数据
function extractCourses(data: any[], username: string, school: string) {
  const courses = [];

  for (const item of data) {
    // 处理嵌套结构（29平台）
    const nestedCourses = processNestedCourses(item, username, school);
    if (nestedCourses.length > 0) {
      courses.push(...nestedCourses);
    } else {
      // 处理直接课程数据
      const directCourse = processDirectCourse(item, username, school);
      if (directCourse) {
        courses.push(directCourse);
      }
    }
  }

  return courses;
}

// 提取批量查课结果中的课程数据
function extractBatchCourses(results: any[]) {
  const allCourses: any[] = [];

  for (const accountResult of results) {
    if (accountResult.result?.success && accountResult.result?.data) {
      const accountCourses = extractAccountCourses(accountResult);
      allCourses.push(...accountCourses);
    }
  }

  return allCourses;
}

// 提取单个账号的课程数据
function extractAccountCourses(accountResult: any) {
  const courses: any[] = [];

  for (const item of accountResult.result.data) {
    if (item.data && Array.isArray(item.data)) {
      const accountCourses = item.data.map((course: any) => ({
        id: course.id,
        name: course.name,
        teacher: course.teacher,
        class: course.class,
        progress: course.progress || '',
        state: course.state || '',
        credit: course.credit || '',
        username: accountResult.account,
        userRealName: item.userName || '',
        school: '自动识别'
      }));
      courses.push(...accountCourses);
    }
  }

  return courses;
}

export async function queryCourses(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { productId, school, username, password } = req.body;

    // 参数验证
    const paramError = validateQueryParams(productId, username, password);
    if (paramError) {
      return res.json(createErrorResponse(paramError, ResponseCode.PARAM_ERROR));
    }

    // 获取商品信息
    const productQuery = `
      SELECT
        p.cid as product_id,
        p.name as product_name,
        p.queryplat as provider_id,
        p.getnoun,
        p.noun,
        p.docking,
        pr.name as provider_name,
        pr.status as provider_status
      FROM fd_product p
      LEFT JOIN fd_provider pr ON p.queryplat = pr.provider_id
      WHERE p.cid = ? AND p.status = 1
    `;

    const productResult = await executeQuery(productQuery, [productId]);

    if (productResult.length === 0) {
      return res.json(createErrorResponse('商品不存在或已禁用', ResponseCode.PARAM_ERROR));
    }

    const product = productResult[0];

    // 验证商品和服务商状态
    const validationError = validateProductAndProvider(product);
    if (validationError) {
      return res.json(createErrorResponse(validationError, ResponseCode.PARAM_ERROR));
    }

    // 调用货源查课接口
    console.log('查课参数:', {
      providerId: product.provider_id,
      providerIdType: typeof product.provider_id,
      parsedProviderId: Number.parseInt(product.provider_id, 10),
      school,
      username,
      password: '***',
      product: {
        platform_cid: product.getnoun,
        noun: product.noun
      }
    });

    const result = await ProviderManager.queryCourses(Number.parseInt(product.provider_id, 10), {
      school,
      username,
      password,
      product: {
        platform_cid: product.getnoun,
        noun: product.noun
      }
    });

    if (!result.success) {
      return res.json(createErrorResponse(`查课失败: ${result.message}`, ResponseCode.ERROR));
    }

    // 处理查课结果
    let courses = [];
    if (result.data && Array.isArray(result.data)) {
      courses = extractCourses(result.data, username, school);
    }

    return res.json(createSuccessResponse(courses, `查询成功，共找到 ${courses.length} 门课程`));
  } catch {
    return res.json(createErrorResponse('查课失败: 系统异常', ResponseCode.ERROR));
  }
}

/**
 * 批量查课
 */
export async function batchQueryCourses(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { productId, accountsText } = req.body;

    // 参数验证
    if (!productId || !accountsText) {
      return res.json(createErrorResponse('商品ID和账号列表不能为空', ResponseCode.PARAM_ERROR));
    }

    // 获取商品信息
    const productQuery = `
      SELECT
        p.cid as product_id,
        p.name as product_name,
        p.price,
        p.getnoun,
        p.noun,
        p.queryplat,
        p.docking
      FROM fd_product p
      WHERE p.cid = ? AND p.status = 1
    `;

    const productResult = await executeQuery(productQuery, [productId]);

    if (productResult.length === 0) {
      return res.json(createErrorResponse('商品不存在或已禁用', ResponseCode.PARAM_ERROR));
    }

    const product = productResult[0];

    // 检查货源配置
    if (!product.queryplat) {
      return res.json(createErrorResponse('商品未配置查课货源', ResponseCode.PARAM_ERROR));
    }

    // 解析账号列表
    const accountList = parseAccountsText(accountsText);
    if (accountList.length === 0) {
      return res.json(createErrorResponse('账号列表格式错误或为空', ResponseCode.PARAM_ERROR));
    }

    // 调用批量查课接口
    const result = await ProviderManager.batchQueryCourses(Number.parseInt(product.queryplat, 10), accountList, {
      platform_cid: product.getnoun, // 使用getnoun作为查课参数
      noun: product.noun
    });

    if (!result.success) {
      return res.json(createErrorResponse(`批量查课失败: ${result.message}`, ResponseCode.ERROR));
    }

    // 合并所有账号的课程数据
    const allCourses = extractBatchCourses(result.results);
    return res.json(createSuccessResponse(allCourses, `查询成功，共找到 ${allCourses.length} 门课程`));
  } catch {
    return res.json(createErrorResponse('批量查课失败: 系统异常', ResponseCode.ERROR));
  }
}

// 创建单个订单
async function createSingleOrder(course: any, product: any, userId: number) {
  const orderNo = generateOrderNo();

  const selectedCoursesJson = JSON.stringify({
    courses: [
      {
        courseId: course.courseId,
        courseName: course.courseName,
        platform: 'network_course'
      }
    ],
    totalCount: 1
  });

  const insertOrderQuery = `
    INSERT INTO fd_order (
      order_no, user_id, provider_id, product_id, item_id, platform_account,
      platform_password, school_name, selected_courses, amount,
      status, progress, create_time, update_time
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
  `;

  const orderResult = await executeQuery(insertOrderQuery, [
    orderNo,
    userId,
    product.provider_id,
    product.product_id,
    null, // item_id 设为 NULL
    course.username,
    course.password,
    course.school || '',
    selectedCoursesJson,
    product.price,
    1, // 待处理
    0 // 进度0%
  ]);

  return { orderId: orderResult.insertId, orderNo };
}

// 处理订单创建结果
async function handleOrderResult(params: { createResult: any; orderId: number; course: any; orderNo: string }) {
  const { createResult, orderId, course, orderNo } = params;

  if (createResult.success && createResult.data?.upstream_order_id) {
    await executeQuery(
      'UPDATE fd_order SET upstream_order_id = ?, status = 2, update_time = NOW() WHERE order_id = ?',
      [createResult.data.upstream_order_id, orderId]
    );

    return {
      account: course.username,
      courseName: course.courseName,
      success: true,
      orderId,
      orderNo,
      upstreamOrderId: createResult.data.upstream_order_id,
      message: '下单成功'
    };
  }

  await executeQuery('UPDATE fd_order SET status = 4, remark = ?, update_time = NOW() WHERE order_id = ?', [
    createResult.message || '下单失败',
    orderId
  ]);

  return {
    account: course.username,
    courseName: course.courseName,
    success: false,
    orderId,
    orderNo,
    message: createResult.message || '下单失败'
  };
}

// 验证批量下单参数
function validateBatchOrderParams(productId: any, selectedCourses: any, userId: any) {
  if (!productId || !selectedCourses || !Array.isArray(selectedCourses) || selectedCourses.length === 0) {
    return '商品ID和选中课程列表不能为空';
  }
  if (!userId) {
    return '用户未登录';
  }
  return null;
}

/**
 * 根据查课结果批量下单
 */
export async function batchCreateOrdersFromQuery(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { productId, selectedCourses } = req.body;
    const userId = req.user?.userId;

    // 参数验证
    const validationError = validateBatchOrderParams(productId, selectedCourses, userId);
    if (validationError) {
      return res.json(
        createErrorResponse(
          validationError,
          validationError === '用户未登录' ? ResponseCode.UNAUTHORIZED : ResponseCode.PARAM_ERROR
        )
      );
    }

    // 验证通过后，userId 确保不为 undefined
    const validUserId = userId as number;

    // 获取商品信息
    const productQuery = `
      SELECT
        p.cid as product_id,
        p.name as product_name,
        p.price,
        p.getnoun,
        p.noun,
        p.queryplat as provider_id,
        p.docking,
        pr.status as provider_status
      FROM fd_product p
      LEFT JOIN fd_provider pr ON p.queryplat = pr.provider_id
      WHERE p.cid = ? AND p.status = 1
    `;

    const productResult = await executeQuery(productQuery, [productId]);

    if (productResult.length === 0) {
      return res.json(createErrorResponse('商品不存在或已禁用', ResponseCode.PARAM_ERROR));
    }

    const product = productResult[0];

    if (!product.provider_id || product.provider_status !== 1) {
      return res.json(createErrorResponse('商品未配置有效服务商', ResponseCode.PARAM_ERROR));
    }

    const results = [];

    // 逐个创建订单（需要顺序处理以避免并发问题）
    // eslint-disable-next-line no-await-in-loop
    for (const course of selectedCourses) {
      try {
        // eslint-disable-next-line no-await-in-loop
        const { orderId, orderNo } = await createSingleOrder(course, product, validUserId);

        // eslint-disable-next-line no-await-in-loop
        const createResult = await ProviderManager.createOrder(Number.parseInt(product.provider_id, 10), {
          school: course.school,
          username: course.username,
          password: course.password,
          course_id: course.courseId,
          course_name: course.courseName,
          product: {
            noun: product.noun,
            getnoun: product.getnoun,
            name: product.product_name,
            platform_cid: product.noun || product.getnoun
          }
        });

        // eslint-disable-next-line no-await-in-loop
        const result = await handleOrderResult({ createResult, orderId, course, orderNo });
        results.push(result);
      } catch (error: any) {
        results.push({
          account: course.username,
          courseName: course.courseName,
          success: false,
          message: error.message || '系统异常'
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failCount = results.length - successCount;

    return res.json(
      createSuccessResponse(
        {
          total: selectedCourses.length,
          successCount,
          failCount,
          results
        },
        `批量下单完成: 成功 ${successCount} 个，失败 ${failCount} 个`
      )
    );
  } catch {
    return res.json(createErrorResponse('批量下单失败: 系统异常', ResponseCode.ERROR));
  }
}

/**
 * 解析账号文本
 * 格式：学校（可选） 账号 密码
 * 每行一个账号
 */
function parseAccountsText(text: string): Array<{
  school?: string;
  username: string;
  password: string;
}> {
  const accounts = [];
  const lines = text.trim().split('\n');

  for (const line of lines) {
    const trimmedLine = line.trim();
    if (trimmedLine) {
      const parts = trimmedLine.split(/\s+/);

      if (parts.length === 2) {
        // 格式：账号 密码
        accounts.push({
          username: parts[0],
          password: parts[1]
        });
      } else if (parts.length === 3) {
        // 格式：学校 账号 密码
        accounts.push({
          school: parts[0],
          username: parts[1],
          password: parts[2]
        });
      }
    }
  }

  return accounts;
}

/**
 * 生成订单号
 */
function generateOrderNo(): string {
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2);
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const hour = now.getHours().toString().padStart(2, '0');
  const minute = now.getMinutes().toString().padStart(2, '0');
  const second = now.getSeconds().toString().padStart(2, '0');
  const random = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, '0');

  return `FD${year}${month}${day}${hour}${minute}${second}${random}`;
}
