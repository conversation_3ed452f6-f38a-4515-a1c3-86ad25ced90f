/**
 * 分类管理控制器
 * 实现分类的CRUD操作和业务逻辑
 */

import type { Response } from 'express';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import type { AuthenticatedRequest } from '../middleware/auth';

/**
 * 获取分类列表（分页）
 */
export async function getCategoryList(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { page = 1, pageSize = 20, keyword, status } = req.query;

    console.log('📂 [分类管理] 获取分类列表:', {
      page,
      pageSize,
      keyword,
      status
    });

    // 构建查询条件
    const whereConditions = ['1=1'];
    const queryParams: any[] = [];

    if (keyword) {
      whereConditions.push('c.name LIKE ?');
      queryParams.push(`%${keyword}%`);
    }

    if (status !== undefined) {
      whereConditions.push('c.status = ?');
      queryParams.push(status);
    }

    const whereClause = whereConditions.join(' AND ');

    // 获取总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM fd_category c
      WHERE ${whereClause}
    `;

    const countResult = await executeQuery(countQuery, queryParams);
    const total = countResult[0].total;

    // 获取分页数据
    const offset = (Number(page) - 1) * Number(pageSize);
    const listQuery = `
      SELECT
        c.category_id,
        c.name,
        c.sort_order,
        c.status,
        c.create_time,
        c.update_time,
        COUNT(p.cid) as product_count
      FROM fd_category c
      LEFT JOIN fd_product p ON c.category_id = p.fenlei AND p.status = 1
      WHERE ${whereClause}
      GROUP BY c.category_id, c.name, c.sort_order, c.status, c.create_time, c.update_time
      ORDER BY c.sort_order ASC, c.category_id ASC
      LIMIT ${Number(pageSize)} OFFSET ${offset}
    `;

    const list = await executeQuery(listQuery, queryParams);

    console.log(`📂 [分类管理] 获取分类列表成功，共 ${total} 条记录`);

    return res.json(
      createSuccessResponse(
        {
          list,
          total,
          page: Number(page),
          pageSize: Number(pageSize),
          totalPages: Math.ceil(total / Number(pageSize))
        },
        '获取分类列表成功'
      )
    );
  } catch (error) {
    console.error('📂 [分类管理] 获取分类列表失败:', error);
    return res.json(createErrorResponse('获取分类列表失败', ResponseCode.ERROR));
  }
}

/**
 * 获取分类详情
 */
export async function getCategoryDetail(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { categoryId } = req.params;

    if (!categoryId) {
      return res.json(createErrorResponse('分类ID不能为空', ResponseCode.PARAM_ERROR));
    }

    console.log('📂 [分类管理] 获取分类详情:', categoryId);

    const query = `
      SELECT
        c.*,
        COUNT(p.cid) as product_count
      FROM fd_category c
      LEFT JOIN fd_product p ON c.category_id = p.fenlei AND p.status = 1
      WHERE c.category_id = ?
      GROUP BY c.category_id, c.name, c.sort_order, c.status, c.create_time, c.update_time
    `;

    const result = await executeQuery(query, [categoryId]);

    if (result.length === 0) {
      return res.json(createErrorResponse('分类不存在', ResponseCode.NOT_FOUND));
    }

    console.log('📂 [分类管理] 获取分类详情成功');
    return res.json(createSuccessResponse(result[0], '获取分类详情成功'));
  } catch (error) {
    console.error('📂 [分类管理] 获取分类详情失败:', error);
    return res.json(createErrorResponse('获取分类详情失败', ResponseCode.ERROR));
  }
}

/**
 * 创建分类（管理员）
 */
export async function createCategory(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { name, sortOrder = 10, status = 1 } = req.body;

    console.log('📂 [分类管理] 创建分类:', { name, sortOrder, status });

    // 参数验证
    if (!name) {
      return res.json(createErrorResponse('分类名称不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查分类名称是否已存在
    const existCheck = await executeQuery('SELECT category_id FROM fd_category WHERE name = ?', [name]);
    if (existCheck.length > 0) {
      return res.json(createErrorResponse('分类名称已存在', ResponseCode.DATA_EXISTS));
    }

    const insertQuery = `
      INSERT INTO fd_category (
        name, sort_order, status, create_time, update_time
      ) VALUES (?, ?, ?, NOW(), NOW())
    `;

    const result = await executeQuery(insertQuery, [name, sortOrder, status]);

    console.log('📂 [分类管理] 创建分类成功:', result.insertId);
    return res.json(createSuccessResponse({ categoryId: result.insertId }, '创建分类成功'));
  } catch (error) {
    console.error('📂 [分类管理] 创建分类失败:', error);
    return res.json(createErrorResponse('创建分类失败', ResponseCode.ERROR));
  }
}

/**
 * 更新分类（管理员）
 */
export async function updateCategory(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { categoryId } = req.params;
    const updateData = req.body;

    if (!categoryId) {
      return res.json(createErrorResponse('分类ID不能为空', ResponseCode.PARAM_ERROR));
    }

    console.log('📂 [分类管理] 更新分类:', categoryId, updateData);

    // 检查分类是否存在
    const existCheck = await executeQuery('SELECT category_id FROM fd_category WHERE category_id = ?', [categoryId]);
    if (existCheck.length === 0) {
      return res.json(createErrorResponse('分类不存在', ResponseCode.DATA_NOT_FOUND));
    }

    // 如果更新名称，检查是否重复
    if (updateData.name) {
      const nameCheck = await executeQuery('SELECT category_id FROM fd_category WHERE name = ? AND category_id != ?', [
        updateData.name,
        categoryId
      ]);
      if (nameCheck.length > 0) {
        return res.json(createErrorResponse('分类名称已存在', ResponseCode.DATA_EXISTS));
      }
    }

    // 构建更新字段
    const allowedFields = ['name', 'sort_order', 'status'];
    const updateFields: string[] = [];
    const updateValues: any[] = [];

    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        updateFields.push(`${field} = ?`);
        updateValues.push(updateData[field]);
      }
    }

    if (updateFields.length === 0) {
      return res.json(createErrorResponse('没有需要更新的字段', ResponseCode.PARAM_ERROR));
    }

    updateFields.push('update_time = NOW()');
    updateValues.push(categoryId);

    const updateQuery = `UPDATE fd_category SET ${updateFields.join(', ')} WHERE category_id = ?`;
    await executeQuery(updateQuery, updateValues);

    console.log('📂 [分类管理] 更新分类成功');
    return res.json(createSuccessResponse(null, '更新分类成功'));
  } catch (error) {
    console.error('📂 [分类管理] 更新分类失败:', error);
    return res.json(createErrorResponse('更新分类失败', ResponseCode.ERROR));
  }
}

/**
 * 删除分类（管理员）
 */
export async function deleteCategory(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { categoryId } = req.params;

    if (!categoryId) {
      return res.json(createErrorResponse('分类ID不能为空', ResponseCode.PARAM_ERROR));
    }

    console.log('📂 [分类管理] 删除分类:', categoryId);

    // 检查是否有关联的商品
    const productCheck = await executeQuery('SELECT COUNT(*) as count FROM fd_product WHERE category_id = ?', [
      categoryId
    ]);
    if (productCheck[0].count > 0) {
      return res.json(createErrorResponse('该分类下存在商品，无法删除', ResponseCode.DATA_EXISTS));
    }

    const deleteQuery = 'DELETE FROM fd_category WHERE category_id = ?';
    const result = await executeQuery(deleteQuery, [categoryId]);

    if (result.affectedRows === 0) {
      return res.json(createErrorResponse('分类不存在', ResponseCode.DATA_NOT_FOUND));
    }

    console.log('📂 [分类管理] 删除分类成功');
    return res.json(createSuccessResponse(null, '删除分类成功'));
  } catch (error) {
    console.error('📂 [分类管理] 删除分类失败:', error);
    return res.json(createErrorResponse('删除分类失败', ResponseCode.ERROR));
  }
}
