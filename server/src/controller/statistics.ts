import type { Response } from 'express';
import { Request } from 'express';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import type { AuthenticatedRequest } from '../middleware/auth';

/**
 * 数据统计分析控制器
 * 遵循项目规范，提供完整的数据统计分析功能
 */

/** 获取系统概览统计 */
export async function getSystemOverview(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    // 用户统计
    const userStatsResult = await executeQuery(`
      SELECT
        COUNT(*) as totalUsers,
        COUNT(CASE WHEN status = 1 THEN 1 END) as activeUsers,
        COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END) as todayUsers,
        SUM(balance) as totalBalance
      FROM fd_user
    `);

    // 订单统计
    const orderStatsResult = await executeQuery(`
      SELECT
        COUNT(*) as totalOrders,
        COUNT(CASE WHEN status = 4 THEN 1 END) as completedOrders,
        COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END) as todayOrders,
        SUM(CASE WHEN status != 5 THEN amount ELSE 0 END) as totalRevenue,
        SUM(CASE WHEN DATE(create_time) = CURDATE() AND status != 5 THEN amount ELSE 0 END) as todayRevenue
      FROM fd_order
    `);

    // 商品统计（替代课程统计）
    const courseStatsResult = await executeQuery(`
      SELECT
        COUNT(*) as totalCourses,
        COUNT(CASE WHEN status = 1 THEN 1 END) as activeCourses
      FROM fd_product
    `);

    // 供应商统计
    const providerStatsResult = await executeQuery(`
      SELECT
        COUNT(*) as totalProviders,
        COUNT(CASE WHEN status = 1 THEN 1 END) as activeProviders
      FROM fd_provider
    `);

    const overview = {
      users: userStatsResult[0] || { totalUsers: 0, activeUsers: 0, todayUsers: 0, totalBalance: 0 },
      orders: orderStatsResult[0] || {
        totalOrders: 0,
        completedOrders: 0,
        todayOrders: 0,
        totalRevenue: 0,
        todayRevenue: 0
      },
      courses: courseStatsResult[0] || { totalCourses: 0, activeCourses: 0 },
      providers: providerStatsResult[0] || { totalProviders: 0, activeProviders: 0 }
    };

    return res.json(createSuccessResponse(overview, '获取系统概览成功'));
  } catch (error) {
    console.error('📊 [统计分析] 获取系统概览失败:', error);
    return res.json(createErrorResponse('获取系统概览失败', ResponseCode.ERROR));
  }
}

/** 获取用户统计分析 */
export async function getUserAnalytics(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { period = '7d' } = req.query;

    // 根据时间周期设置日期范围
    let dateCondition = '';
    let groupBy = '';
    let dateFormat = '';

    switch (period) {
      case '24h':
        dateCondition = 'WHERE create_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)';
        groupBy = 'HOUR(create_time)';
        dateFormat = 'CONCAT(DATE(create_time), " ", HOUR(create_time), ":00")';
        break;
      case '7d':
        dateCondition = 'WHERE create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        groupBy = 'DATE(create_time)';
        dateFormat = 'DATE(create_time)';
        break;
      case '30d':
        dateCondition = 'WHERE create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
        groupBy = 'DATE(create_time)';
        dateFormat = 'DATE(create_time)';
        break;
      case '90d':
        dateCondition = 'WHERE create_time >= DATE_SUB(NOW(), INTERVAL 90 DAY)';
        groupBy = 'WEEK(create_time)';
        dateFormat = 'CONCAT(YEAR(create_time), "-W", WEEK(create_time))';
        break;
      default:
        dateCondition = 'WHERE create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        groupBy = 'DATE(create_time)';
        dateFormat = 'DATE(create_time)';
    }

    // 用户注册趋势
    const registrationTrendResult = await executeQuery(`
      SELECT
        ${dateFormat} as date,
        COUNT(*) as count
      FROM fd_user
      ${dateCondition}
      GROUP BY ${groupBy}
      ORDER BY date ASC
    `);

    // 用户角色分布
    const roleDistributionResult = await executeQuery(`
      SELECT
        user_role,
        COUNT(*) as count
      FROM fd_user
      WHERE status = 1
      GROUP BY user_role
    `);

    // 用户余额分布
    const balanceDistributionResult = await executeQuery(`
      SELECT
        CASE
          WHEN balance = 0 THEN '0元'
          WHEN balance > 0 AND balance <= 100 THEN '1-100元'
          WHEN balance > 100 AND balance <= 500 THEN '101-500元'
          WHEN balance > 500 AND balance <= 1000 THEN '501-1000元'
          WHEN balance > 1000 AND balance <= 5000 THEN '1001-5000元'
          ELSE '5000元以上'
        END as balanceRange,
        COUNT(*) as count
      FROM fd_user
      WHERE status = 1
      GROUP BY balanceRange
      ORDER BY
        CASE
          WHEN balance = 0 THEN 1
          WHEN balance > 0 AND balance <= 100 THEN 2
          WHEN balance > 100 AND balance <= 500 THEN 3
          WHEN balance > 500 AND balance <= 1000 THEN 4
          WHEN balance > 1000 AND balance <= 5000 THEN 5
          ELSE 6
        END
    `);

    // 活跃用户统计（基于订单活动）
    const activeUsersResult = await executeQuery(`
      SELECT
        ${dateFormat} as date,
        COUNT(DISTINCT user_id) as activeUsers
      FROM fd_order
      ${dateCondition}
      GROUP BY ${groupBy}
      ORDER BY date ASC
    `);

    const analytics = {
      registrationTrend: registrationTrendResult,
      roleDistribution: roleDistributionResult,
      balanceDistribution: balanceDistributionResult,
      activeUsersTrend: activeUsersResult
    };

    return res.json(createSuccessResponse(analytics, '获取用户统计分析成功'));
  } catch (error) {
    console.error('📊 [统计分析] 获取用户统计分析失败:', error);
    return res.json(createErrorResponse('获取用户统计分析失败', ResponseCode.ERROR));
  }
}

/** 获取订单统计分析 */
export async function getOrderAnalytics(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { period = '7d' } = req.query;

    // 根据时间周期设置日期范围
    let dateCondition = '';
    let groupBy = '';
    let dateFormat = '';

    switch (period) {
      case '24h':
        dateCondition = 'WHERE create_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)';
        groupBy = 'HOUR(create_time)';
        dateFormat = 'CONCAT(DATE(create_time), " ", HOUR(create_time), ":00")';
        break;
      case '7d':
        dateCondition = 'WHERE create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        groupBy = 'DATE(create_time)';
        dateFormat = 'DATE(create_time)';
        break;
      case '30d':
        dateCondition = 'WHERE create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
        groupBy = 'DATE(create_time)';
        dateFormat = 'DATE(create_time)';
        break;
      case '90d':
        dateCondition = 'WHERE create_time >= DATE_SUB(NOW(), INTERVAL 90 DAY)';
        groupBy = 'WEEK(create_time)';
        dateFormat = 'CONCAT(YEAR(create_time), "-W", WEEK(create_time))';
        break;
      default:
        dateCondition = 'WHERE create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        groupBy = 'DATE(create_time)';
        dateFormat = 'DATE(create_time)';
    }

    // 订单趋势
    const orderTrendResult = await executeQuery(`
      SELECT
        ${dateFormat} as date,
        COUNT(*) as totalOrders,
        COUNT(CASE WHEN status = 4 THEN 1 END) as completedOrders,
        COUNT(CASE WHEN status = 5 THEN 1 END) as cancelledOrders,
        SUM(CASE WHEN status != 5 THEN amount ELSE 0 END) as revenue
      FROM fd_order
      ${dateCondition}
      GROUP BY ${groupBy}
      ORDER BY date ASC
    `);

    // 订单状态分布
    const statusDistributionResult = await executeQuery(`
      SELECT
        status,
        COUNT(*) as count,
        CASE
          WHEN status = 1 THEN '待支付'
          WHEN status = 2 THEN '待处理'
          WHEN status = 3 THEN '处理中'
          WHEN status = 4 THEN '已完成'
          WHEN status = 5 THEN '已取消'
          WHEN status = 6 THEN '处理失败'
          ELSE '未知状态'
        END as statusText
      FROM fd_order
      ${dateCondition}
      GROUP BY status
    `);

    // 热门课程统计
    const popularCoursesResult = await executeQuery(`
      SELECT
        course_name,
        COUNT(*) as orderCount,
        SUM(amount) as totalRevenue
      FROM fd_order
      ${dateCondition}
      AND status != 5
      GROUP BY course_name
      ORDER BY orderCount DESC
      LIMIT 10
    `);

    // 用户订单分布
    const userOrderDistributionResult = await executeQuery(`
      SELECT
        CASE
          WHEN orderCount = 1 THEN '1单'
          WHEN orderCount >= 2 AND orderCount <= 5 THEN '2-5单'
          WHEN orderCount >= 6 AND orderCount <= 10 THEN '6-10单'
          WHEN orderCount >= 11 AND orderCount <= 20 THEN '11-20单'
          ELSE '20单以上'
        END as orderRange,
        COUNT(*) as userCount
      FROM (
        SELECT user_id, COUNT(*) as orderCount
        FROM fd_order
        ${dateCondition}
        GROUP BY user_id
      ) as userOrders
      GROUP BY orderRange
      ORDER BY
        CASE
          WHEN orderCount = 1 THEN 1
          WHEN orderCount >= 2 AND orderCount <= 5 THEN 2
          WHEN orderCount >= 6 AND orderCount <= 10 THEN 3
          WHEN orderCount >= 11 AND orderCount <= 20 THEN 4
          ELSE 5
        END
    `);

    const analytics = {
      orderTrend: orderTrendResult,
      statusDistribution: statusDistributionResult,
      popularCourses: popularCoursesResult,
      userOrderDistribution: userOrderDistributionResult
    };

    return res.json(createSuccessResponse(analytics, '获取订单统计分析成功'));
  } catch (error) {
    console.error('📊 [统计分析] 获取订单统计分析失败:', error);
    return res.json(createErrorResponse('获取订单统计分析失败', ResponseCode.ERROR));
  }
}

/** 获取收入统计分析 */
export async function getRevenueAnalytics(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { period = '7d' } = req.query;

    // 根据时间周期设置日期范围
    let dateCondition = '';
    let groupBy = '';
    let dateFormat = '';

    switch (period) {
      case '24h':
        dateCondition = 'WHERE create_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)';
        groupBy = 'HOUR(create_time)';
        dateFormat = 'CONCAT(DATE(create_time), " ", HOUR(create_time), ":00")';
        break;
      case '7d':
        dateCondition = 'WHERE create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        groupBy = 'DATE(create_time)';
        dateFormat = 'DATE(create_time)';
        break;
      case '30d':
        dateCondition = 'WHERE create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
        groupBy = 'DATE(create_time)';
        dateFormat = 'DATE(create_time)';
        break;
      case '90d':
        dateCondition = 'WHERE create_time >= DATE_SUB(NOW(), INTERVAL 90 DAY)';
        groupBy = 'WEEK(create_time)';
        dateFormat = 'CONCAT(YEAR(create_time), "-W", WEEK(create_time))';
        break;
      default:
        dateCondition = 'WHERE create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
        groupBy = 'DATE(create_time)';
        dateFormat = 'DATE(create_time)';
    }

    // 收入趋势
    const revenueTrendResult = await executeQuery(`
      SELECT
        ${dateFormat} as date,
        SUM(CASE WHEN status != 5 THEN amount ELSE 0 END) as revenue,
        SUM(CASE WHEN status != 5 THEN cost_amount ELSE 0 END) as cost,
        SUM(CASE WHEN status != 5 THEN (amount - cost_amount) ELSE 0 END) as profit,
        COUNT(CASE WHEN status != 5 THEN 1 END) as orderCount
      FROM fd_order
      ${dateCondition}
      GROUP BY ${groupBy}
      ORDER BY date ASC
    `);

    // 收入来源分析（按商品分类）
    const revenueByTypeResult = await executeQuery(`
      SELECT
        COALESCE(p.fenlei, '未分类') as platform_type,
        COUNT(o.order_id) as orderCount,
        SUM(o.amount) as revenue,
        SUM(o.cost_amount) as cost,
        SUM(o.amount - o.cost_amount) as profit
      FROM fd_order o
      LEFT JOIN fd_product p ON o.product_id = p.cid
      ${dateCondition}
      AND o.status != 5
      GROUP BY p.fenlei
      ORDER BY revenue DESC
    `);

    // 用户贡献分析
    const userContributionResult = await executeQuery(`
      SELECT
        u.username,
        u.user_role,
        COUNT(o.order_id) as orderCount,
        SUM(o.amount) as totalSpent,
        AVG(o.amount) as avgOrderValue
      FROM fd_order o
      JOIN fd_user u ON o.user_id = u.user_id
      ${dateCondition}
      AND o.status != 5
      GROUP BY o.user_id
      ORDER BY totalSpent DESC
      LIMIT 20
    `);

    // 月度收入对比
    const monthlyComparisonResult = await executeQuery(`
      SELECT
        DATE_FORMAT(create_time, '%Y-%m') as month,
        SUM(CASE WHEN status != 5 THEN amount ELSE 0 END) as revenue,
        SUM(CASE WHEN status != 5 THEN cost_amount ELSE 0 END) as cost,
        SUM(CASE WHEN status != 5 THEN (amount - cost_amount) ELSE 0 END) as profit,
        COUNT(CASE WHEN status != 5 THEN 1 END) as orderCount
      FROM fd_order
      WHERE create_time >= DATE_SUB(NOW(), INTERVAL 12 MONTH)
      GROUP BY DATE_FORMAT(create_time, '%Y-%m')
      ORDER BY month ASC
    `);

    // 收入统计汇总
    const revenueSummaryResult = await executeQuery(`
      SELECT
        SUM(CASE WHEN status != 5 THEN amount ELSE 0 END) as totalRevenue,
        SUM(CASE WHEN status != 5 THEN cost_amount ELSE 0 END) as totalCost,
        SUM(CASE WHEN status != 5 THEN (amount - cost_amount) ELSE 0 END) as totalProfit,
        AVG(CASE WHEN status != 5 THEN amount END) as avgOrderValue,
        COUNT(CASE WHEN status != 5 THEN 1 END) as totalOrders
      FROM fd_order
      ${dateCondition}
    `);

    const analytics = {
      revenueTrend: revenueTrendResult,
      revenueByType: revenueByTypeResult,
      userContribution: userContributionResult,
      monthlyComparison: monthlyComparisonResult,
      summary: revenueSummaryResult[0] || {
        totalRevenue: 0,
        totalCost: 0,
        totalProfit: 0,
        avgOrderValue: 0,
        totalOrders: 0
      }
    };

    return res.json(createSuccessResponse(analytics, '获取收入统计分析成功'));
  } catch (error) {
    console.error('📊 [统计分析] 获取收入统计分析失败:', error);
    return res.json(createErrorResponse('获取收入统计分析失败', ResponseCode.ERROR));
  }
}

/** 获取实时统计数据 */
export async function getRealTimeStats(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    // 今日实时数据
    const todayStatsResult = await executeQuery(`
      SELECT
        COUNT(CASE WHEN DATE(u.create_time) = CURDATE() THEN 1 END) as todayUsers,
        COUNT(CASE WHEN DATE(o.create_time) = CURDATE() THEN 1 END) as todayOrders,
        SUM(CASE WHEN DATE(o.create_time) = CURDATE() AND o.status != 5 THEN o.amount ELSE 0 END) as todayRevenue,
        COUNT(CASE WHEN DATE(o.create_time) = CURDATE() AND o.status = 4 THEN 1 END) as todayCompletedOrders
      FROM fd_user u
      CROSS JOIN fd_order o
    `);

    // 最近24小时趋势
    const hourlyTrendResult = await executeQuery(`
      SELECT
        HOUR(create_time) as hour,
        COUNT(*) as orderCount,
        SUM(CASE WHEN status != 5 THEN amount ELSE 0 END) as revenue
      FROM fd_order
      WHERE create_time >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
      GROUP BY HOUR(create_time)
      ORDER BY hour ASC
    `);

    // 在线用户估算（基于最近活动）
    const activeUsersResult = await executeQuery(`
      SELECT COUNT(DISTINCT user_id) as activeUsers
      FROM fd_order
      WHERE create_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
    `);

    // 系统状态
    const systemStatusResult = await executeQuery(`
      SELECT
        (SELECT COUNT(*) FROM fd_order WHERE status = 3) as processingOrders,
        (SELECT COUNT(*) FROM fd_order WHERE status = 6) as failedOrders,
        (SELECT COUNT(*) FROM fd_user WHERE status = 1) as activeUsers,
        (SELECT COUNT(*) FROM fd_product WHERE status = 1) as activeCourses
    `);

    const realTimeStats = {
      today: todayStatsResult[0] || { todayUsers: 0, todayOrders: 0, todayRevenue: 0, todayCompletedOrders: 0 },
      hourlyTrend: hourlyTrendResult,
      activeUsers: activeUsersResult[0]?.activeUsers || 0,
      systemStatus: systemStatusResult[0] || { processingOrders: 0, failedOrders: 0, activeUsers: 0, activeCourses: 0 }
    };

    return res.json(createSuccessResponse(realTimeStats, '获取实时统计数据成功'));
  } catch (error) {
    console.error('📊 [统计分析] 获取实时统计数据失败:', error);
    return res.json(createErrorResponse('获取实时统计数据失败', ResponseCode.ERROR));
  }
}
