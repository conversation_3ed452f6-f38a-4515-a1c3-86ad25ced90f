import * as os from 'node:os';
import type { Request, Response } from 'express';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import type { AuthenticatedRequest } from '../middleware/auth';

/** 确保默认配置存在 */
async function ensureDefaultConfig() {
  const defaultConfigs = [
    // 基础配置
    { key: 'systemName', value: 'SoybeanAdmin', group: 'system', description: '系统名称' },
    {
      key: 'systemDescription',
      value: '基于Vue3 + TypeScript + Element Plus的现代化管理系统',
      group: 'system',
      description: '系统描述'
    },
    { key: 'systemVersion', value: '2.0.0', group: 'system', description: '系统版本' },
    { key: 'maintenanceMode', value: 'false', group: 'system', description: '维护模式' },

    // 安全配置
    { key: 'tokenExpiry', value: '24', group: 'security', description: 'Token过期时间(小时)' },
    { key: 'passwordMinLength', value: '6', group: 'security', description: '密码最小长度' },
    { key: 'enableTwoFactor', value: 'false', group: 'security', description: '启用双因子认证' },
    { key: 'maxLoginAttempts', value: '5', group: 'security', description: '最大登录尝试次数' },
    { key: 'sessionTimeout', value: '30', group: 'security', description: '会话超时时间(分钟)' },

    // 邮件配置
    { key: 'smtpHost', value: '', group: 'email', description: 'SMTP服务器' },
    { key: 'smtpPort', value: '587', group: 'email', description: 'SMTP端口' },
    { key: 'emailFrom', value: '', group: 'email', description: '发件人邮箱' },
    { key: 'emailPassword', value: '', group: 'email', description: '邮箱密码' },
    { key: 'emailSSL', value: 'true', group: 'email', description: '启用SSL' },

    // 存储配置
    { key: 'maxUploadSize', value: '10', group: 'system', description: '最大上传大小(MB)' },
    { key: 'autoBackup', value: 'true', group: 'system', description: '自动备份' },
    { key: 'backupInterval', value: '24', group: 'system', description: '备份间隔(小时)' },
    { key: 'maxBackupFiles', value: '7', group: 'system', description: '最大备份文件数' },

    // 功能开关
    { key: 'enableRegistration', value: 'false', group: 'system', description: '启用注册' },
    { key: 'enableGuestAccess', value: 'false', group: 'system', description: '启用访客访问' },
    { key: 'enableApiDocs', value: 'true', group: 'system', description: '启用API文档' }
  ];

  for (const config of defaultConfigs) {
    try {
      await executeQuery(
        `INSERT IGNORE INTO fd_config (config_key, config_value, config_group, description, is_system, create_time, update_time)
         VALUES (?, ?, ?, ?, 0, NOW(), NOW())`,
        [config.key, config.value, config.group, config.description]
      );
    } catch (error) {
      // 忽略重复键错误
    }
  }
}

/**
 * 系统配置管理控制器
 * 提供系统配置、公告管理、数据统计等功能
 */

// ===== 系统配置管理 =====

/** 获取系统配置 */
export async function getSystemConfig(req: Request, res: Response): Promise<any> {
  try {
    console.log('⚙️ [系统配置] 获取系统配置');

    // 确保默认配置存在
    await ensureDefaultConfig();

    const query = `
      SELECT config_key, config_value, config_group, description
      FROM fd_config
      WHERE config_group = 'system' OR config_group = 'email' OR config_group = 'security'
      ORDER BY config_group ASC, config_key ASC
    `;

    const configList = await executeQuery(query);

    // 将配置转换为键值对格式
    const configMap: Record<string, any> = {};
    configList.forEach((item: any) => {
      let value = item.config_value;

      // 智能类型转换
      if (value === null || value === undefined) {
        value = '';
      } else if (typeof value === 'string') {
        // 尝试解析JSON
        if (value.startsWith('{') || value.startsWith('[')) {
          try {
            value = JSON.parse(value);
          } catch (e) {
            // 保持原字符串
          }
        }
        // 布尔值转换
        else if (value === 'true' || value === '1') {
          value = true;
        } else if (value === 'false' || value === '0') {
          value = false;
        }
        // 数字转换
        else if (/^\d+(\.\d+)?$/.test(value)) {
          value = Number(value);
        }
      }

      configMap[item.config_key] = value;
    });

    return res.json(createSuccessResponse(configMap, '获取系统配置成功'));
  } catch (error) {
    console.error('⚙️ [系统配置] 获取系统配置失败:', error);
    return res.json(createErrorResponse('获取系统配置失败', ResponseCode.ERROR));
  }
}

/** 更新系统配置 */
export async function updateSystemConfig(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    console.log('⚙️ [系统配置] 更新系统配置');

    const { configs } = req.body;

    if (!configs || typeof configs !== 'object') {
      return res.json(createErrorResponse('配置数据格式错误', ResponseCode.PARAM_ERROR));
    }

    const updatePromises = Object.entries(configs).map(async ([key, value]) => {
      let configValue = value;

      // 处理不同类型的值
      if (typeof value === 'object') {
        configValue = JSON.stringify(value);
      } else if (typeof value === 'boolean') {
        configValue = value ? '1' : '0';
      } else {
        configValue = String(value);
      }

      // 更新或插入配置
      const upsertQuery = `
        INSERT INTO fd_config (config_key, config_value, config_group, description, is_system, create_time, update_time)
        VALUES (?, ?, 'system', '系统配置', 0, NOW(), NOW())
        ON DUPLICATE KEY UPDATE
        config_value = VALUES(config_value),
        update_time = VALUES(update_time)
      `;

      return executeQuery(upsertQuery, [key, configValue]);
    });

    await Promise.all(updatePromises);

    console.log('⚙️ [系统配置] 系统配置更新成功');
    return res.json(createSuccessResponse(null, '系统配置更新成功'));
  } catch (error) {
    console.error('⚙️ [系统配置] 更新系统配置失败:', error);
    return res.json(createErrorResponse('更新系统配置失败', ResponseCode.ERROR));
  }
}

// ===== 系统统计数据 =====

/** 获取系统统计数据 */
export async function getSystemStatistics(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    console.log('📊 [系统统计] 获取系统统计数据');

    // 用户统计
    const userStatsQuery = `
      SELECT
        COUNT(*) as total_users,
        COUNT(CASE WHEN status = 1 THEN 1 END) as active_users,
        COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END) as today_new_users,
        COUNT(CASE WHEN YEAR(create_time) = YEAR(NOW()) AND MONTH(create_time) = MONTH(NOW()) THEN 1 END) as month_new_users
      FROM fd_user
    `;
    const userStats = await executeQuery(userStatsQuery);

    // 订单统计
    const orderStatsQuery = `
      SELECT
        COUNT(*) as total_orders,
        COUNT(CASE WHEN status = 4 THEN 1 END) as completed_orders,
        COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END) as today_orders,
        SUM(amount) as total_amount,
        SUM(CASE WHEN status = 4 THEN amount ELSE 0 END) as completed_amount
      FROM fd_order
    `;
    const orderStats = await executeQuery(orderStatsQuery);

    // 财务统计
    const financeStatsQuery = `
      SELECT
        SUM(CASE WHEN type = 'recharge' THEN amount ELSE 0 END) as total_recharge,
        SUM(CASE WHEN type = 'consume' THEN ABS(amount) ELSE 0 END) as total_consume,
        COUNT(CASE WHEN type = 'recharge' AND DATE(create_time) = CURDATE() THEN 1 END) as today_recharge_count,
        SUM(CASE WHEN type = 'recharge' AND DATE(create_time) = CURDATE() THEN amount ELSE 0 END) as today_recharge_amount
      FROM fd_finance
    `;
    const financeStats = await executeQuery(financeStatsQuery);

    // 商品统计（替代课程统计）
    const courseStatsQuery = `
      SELECT
        COUNT(*) as total_courses,
        COUNT(CASE WHEN status = 1 THEN 1 END) as active_courses,
        COUNT(DISTINCT fenlei) as total_categories,
        COUNT(DISTINCT queryplat) as total_platforms
      FROM fd_product
    `;
    const courseStats = await executeQuery(courseStatsQuery);

    // 最近7天趋势数据
    const trendQuery = `
      SELECT
        DATE(create_time) as date,
        COUNT(*) as order_count,
        SUM(amount) as order_amount
      FROM fd_order
      WHERE create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
      GROUP BY DATE(create_time)
      ORDER BY date ASC
    `;
    const trendData = await executeQuery(trendQuery);

    const statistics = {
      users: {
        total: userStats[0].total_users || 0,
        active: userStats[0].active_users || 0,
        today_new: userStats[0].today_new_users || 0,
        month_new: userStats[0].month_new_users || 0
      },
      orders: {
        total: orderStats[0].total_orders || 0,
        completed: orderStats[0].completed_orders || 0,
        today: orderStats[0].today_orders || 0,
        total_amount: orderStats[0].total_amount || 0,
        completed_amount: orderStats[0].completed_amount || 0
      },
      finance: {
        total_recharge: financeStats[0].total_recharge || 0,
        total_consume: financeStats[0].total_consume || 0,
        today_recharge_count: financeStats[0].today_recharge_count || 0,
        today_recharge_amount: financeStats[0].today_recharge_amount || 0
      },
      courses: {
        total: courseStats[0].total_courses || 0,
        active: courseStats[0].active_courses || 0,
        categories: courseStats[0].total_categories || 0,
        platforms: courseStats[0].total_platforms || 0
      },
      trend: trendData.map((item: any) => ({
        date: item.date,
        order_count: item.order_count || 0,
        order_amount: item.order_amount || 0
      }))
    };

    return res.json(createSuccessResponse(statistics, '获取系统统计数据成功'));
  } catch (error) {
    console.error('📊 [系统统计] 获取系统统计数据失败:', error);
    return res.json(createErrorResponse('获取系统统计数据失败', ResponseCode.ERROR));
  }
}

// ===== 系统公告管理 =====

/** 获取系统公告列表 */
export async function getAnnouncementList(req: Request, res: Response): Promise<any> {
  try {
    console.log('📢 [系统公告] 获取公告列表');

    const { page = 1, pageSize = 10, status } = req.query;

    // 构建查询条件（fd_config表没有status字段，所有公告都是有效的）
    const whereClause = '1=1';
    const queryParams: any[] = [];

    // 查询总数
    const countSql = `SELECT COUNT(*) as total FROM fd_config WHERE config_key LIKE 'announcement_%' AND ${whereClause}`;
    const countResult = await executeQuery(countSql, queryParams);
    const total = countResult[0].total;

    // 查询列表数据
    const offset = (Number(page) - 1) * Number(pageSize);
    const listSql = `
      SELECT
        config_key,
        config_value as content,
        description as title,
        create_time,
        update_time
      FROM fd_config
      WHERE config_key LIKE 'announcement_%' AND ${whereClause}
      ORDER BY create_time DESC
      LIMIT ? OFFSET ?
    `;

    const list = await executeQuery(listSql, [...queryParams, Number(pageSize), offset]);

    return res.json(
      createSuccessResponse(
        {
          list,
          pagination: {
            page: Number(page),
            pageSize: Number(pageSize),
            total
          }
        },
        '获取公告列表成功'
      )
    );
  } catch (error) {
    console.error('📢 [系统公告] 获取公告列表失败:', error);
    return res.json(createErrorResponse('获取公告列表失败', ResponseCode.ERROR));
  }
}

/** 创建系统公告 */
export async function createAnnouncement(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    console.log('📢 [系统公告] 创建公告');

    const { title, content, status = 1 } = req.body;

    if (!title || !content) {
      return res.json(createErrorResponse('标题和内容不能为空', ResponseCode.PARAM_ERROR));
    }

    // 生成公告配置键
    const configKey = `announcement_${Date.now()}`;

    const insertQuery = `
      INSERT INTO fd_config (config_key, config_value, description, config_group, is_system, create_time, update_time)
      VALUES (?, ?, ?, 'announcement', 0, NOW(), NOW())
    `;

    await executeQuery(insertQuery, [configKey, content, title]);

    console.log('📢 [系统公告] 公告创建成功');
    return res.json(createSuccessResponse({ config_key: configKey }, '公告创建成功'));
  } catch (error) {
    console.error('📢 [系统公告] 创建公告失败:', error);
    return res.json(createErrorResponse('创建公告失败', ResponseCode.ERROR));
  }
}

/** 更新系统公告 */
export async function updateAnnouncement(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    console.log('📢 [系统公告] 更新公告');

    const { configId } = req.params;
    const { title, content, status } = req.body;

    if (!title || !content) {
      return res.json(createErrorResponse('标题和内容不能为空', ResponseCode.PARAM_ERROR));
    }

    const updateQuery = `
      UPDATE fd_config
      SET config_value = ?, description = ?, update_time = NOW()
      WHERE config_key = ? AND config_key LIKE 'announcement_%'
    `;

    const result = await executeQuery(updateQuery, [content, title, configId]);

    if (result.affectedRows === 0) {
      return res.json(createErrorResponse('公告不存在', ResponseCode.DATA_NOT_FOUND));
    }

    console.log('📢 [系统公告] 公告更新成功');
    return res.json(createSuccessResponse(null, '公告更新成功'));
  } catch (error) {
    console.error('📢 [系统公告] 更新公告失败:', error);
    return res.json(createErrorResponse('更新公告失败', ResponseCode.ERROR));
  }
}

/** 删除系统公告 */
export async function deleteAnnouncement(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    console.log('📢 [系统公告] 删除公告');

    const { configId } = req.params;

    const deleteQuery = `
      DELETE FROM fd_config
      WHERE config_key = ? AND config_key LIKE 'announcement_%'
    `;

    const result = await executeQuery(deleteQuery, [configId]);

    if (result.affectedRows === 0) {
      return res.json(createErrorResponse('公告不存在', ResponseCode.DATA_NOT_FOUND));
    }

    console.log('📢 [系统公告] 公告删除成功');
    return res.json(createSuccessResponse(null, '公告删除成功'));
  } catch (error) {
    console.error('📢 [系统公告] 删除公告失败:', error);
    return res.json(createErrorResponse('删除公告失败', ResponseCode.ERROR));
  }
}

/** 获取系统状态 */
export async function getSystemStatus(req: Request, res: Response): Promise<any> {
  try {
    console.log('⚙️ [系统状态] 获取系统状态');

    // 获取系统配置
    const configQuery = `
      SELECT config_key, config_value
      FROM fd_config
      WHERE config_key IN ('systemName', 'systemVersion', 'maintenanceMode')
    `;
    const configs = await executeQuery(configQuery);

    // 转换配置为对象
    const configMap: Record<string, any> = {};
    configs.forEach((item: any) => {
      let value = item.config_value;
      if (value === 'true') value = true;
      else if (value === 'false') value = false;
      else if (/^\d+(\.\d+)?$/.test(value)) value = Number(value);
      configMap[item.config_key] = value;
    });

    // 获取系统信息
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();

    const systemStatus = {
      // 系统基本信息
      systemName: configMap.systemName || 'SoybeanAdmin',
      systemVersion: configMap.systemVersion || '2.0.0',
      platform: os.platform(),
      arch: os.arch(),
      nodeVersion: process.version,

      // 运行时间信息
      uptime: process.uptime(),
      timestamp: new Date().toISOString(),

      // 内存使用情况
      memoryUsage: {
        rss: memoryUsage.rss,
        heapTotal: memoryUsage.heapTotal,
        heapUsed: memoryUsage.heapUsed,
        external: memoryUsage.external
      },

      // CPU使用情况
      cpuUsage: {
        user: cpuUsage.user,
        system: cpuUsage.system
      },

      // 系统状态
      maintenanceMode: configMap.maintenanceMode || false,

      // 环境信息
      environment: process.env.NODE_ENV || 'development',
      pid: process.pid
    };

    return res.json(createSuccessResponse(systemStatus, '获取系统状态成功'));
  } catch (error) {
    console.error('⚙️ [系统状态] 获取系统状态失败:', error);
    return res.json(createErrorResponse('获取系统状态失败', ResponseCode.ERROR));
  }
}
