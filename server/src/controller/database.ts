import * as fs from 'node:fs';
import * as path from 'node:path';
import type { Response } from 'express';
import { Request } from 'express';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import type { AuthenticatedRequest } from '../middleware/auth';

/** 获取数据库信息 */
export async function getDatabaseInfo(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    console.log('🗄️ [数据库管理] 获取数据库信息');

    // 获取数据库连接状态
    let connectionStatus = 'connected';
    let connectionError = null;
    try {
      await executeQuery('SELECT 1');
    } catch (error: any) {
      connectionStatus = 'error';
      connectionError = error.message;
    }

    // 获取数据库基本信息
    const dbInfoQuery = `
      SELECT
        @@hostname as host,
        @@port as port,
        DATABASE() as database_name,
        @@character_set_database as charset,
        @@version as version
    `;
    const dbInfo = await executeQuery(dbInfoQuery);

    // 获取uptime信息
    const uptimeQuery = `SHOW STATUS LIKE 'Uptime'`;
    const uptimeResult = await executeQuery(uptimeQuery);
    const uptime = uptimeResult.length > 0 ? Number.parseInt(uptimeResult[0].Value) : 0;

    // 获取数据库大小信息
    const sizeQuery = `
      SELECT
        ROUND(SUM(data_length + index_length), 0) as total_size,
        ROUND(SUM(data_length), 0) as data_size,
        ROUND(SUM(index_length), 0) as index_size,
        COUNT(*) as table_count
      FROM information_schema.tables
      WHERE table_schema = DATABASE()
    `;
    const sizeInfo = await executeQuery(sizeQuery);

    // 获取性能信息
    const performanceQuery = `
      SHOW GLOBAL STATUS WHERE Variable_name IN (
        'Queries', 'Slow_queries', 'Threads_connected', 'Max_connections'
      )
    `;
    const performanceResult = await executeQuery(performanceQuery);

    const performance = {
      queries: 0,
      slowQueries: 0,
      connections: 0,
      maxConnections: 0
    };

    performanceResult.forEach((row: any) => {
      switch (row.Variable_name) {
        case 'Queries':
          performance.queries = Number.parseInt(row.Value);
          break;
        case 'Slow_queries':
          performance.slowQueries = Number.parseInt(row.Value);
          break;
        case 'Threads_connected':
          performance.connections = Number.parseInt(row.Value);
          break;
        case 'Max_connections':
          performance.maxConnections = Number.parseInt(row.Value);
          break;
      }
    });

    const databaseInfo = {
      connection: {
        status: connectionStatus,
        host: dbInfo[0]?.host || 'localhost',
        port: dbInfo[0]?.port || 3306,
        database: dbInfo[0]?.database_name || 'unknown',
        charset: dbInfo[0]?.charset || 'utf8mb4',
        version: dbInfo[0]?.version || 'unknown',
        uptime
      },
      size: {
        totalSize: sizeInfo[0]?.total_size || 0,
        dataSize: sizeInfo[0]?.data_size || 0,
        indexSize: sizeInfo[0]?.index_size || 0,
        tableCount: sizeInfo[0]?.table_count || 0
      },
      performance
    };

    return res.json(createSuccessResponse(databaseInfo, '获取数据库信息成功'));
  } catch (error) {
    console.error('🗄️ [数据库管理] 获取数据库信息失败:', error);
    return res.json(createErrorResponse('获取数据库信息失败', ResponseCode.ERROR));
  }
}

/** 获取表列表 */
export async function getTableList(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { page = 1, pageSize = 20, keyword = '', tableType = '' } = req.query;

    console.log('🗄️ [数据库管理] 获取表列表', { page, pageSize, keyword, tableType });

    const offset = (Number(page) - 1) * Number(pageSize);
    const whereConditions = ['table_schema = DATABASE()'];
    const queryParams: any[] = [];

    // 关键词搜索
    if (keyword) {
      whereConditions.push('table_name LIKE ?');
      queryParams.push(`%${keyword}%`);
    }

    // 表类型筛选
    if (tableType) {
      whereConditions.push('table_type = ?');
      queryParams.push(tableType);
    }

    // 获取表列表
    const tablesQuery = `
      SELECT
        table_name as tableName,
        table_type as tableType,
        table_rows as tableRows,
        data_length as dataLength,
        index_length as indexLength,
        table_comment as tableComment,
        create_time as createTime
      FROM information_schema.tables
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY table_name
      LIMIT ? OFFSET ?
    `;
    queryParams.push(Number(pageSize), offset);

    const tables = await executeQuery(tablesQuery, queryParams);

    // 获取总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM information_schema.tables
      WHERE ${whereConditions.join(' AND ')}
    `;
    const countParams = queryParams.slice(0, -2); // 移除 LIMIT 和 OFFSET 参数
    const countResult = await executeQuery(countQuery, countParams);

    const result = {
      list: tables,
      pagination: {
        page: Number(page),
        pageSize: Number(pageSize),
        total: countResult[0]?.total || 0
      }
    };

    return res.json(createSuccessResponse(result, '获取表列表成功'));
  } catch (error) {
    console.error('🗄️ [数据库管理] 获取表列表失败:', error);
    return res.json(createErrorResponse('获取表列表失败', ResponseCode.ERROR));
  }
}

/** 获取表结构 */
export async function getTableStructure(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { tableName } = req.params;
    console.log('🗄️ [数据库管理] 获取表结构:', tableName);

    const structureQuery = `DESCRIBE \`${tableName}\``;
    const structure = await executeQuery(structureQuery);

    return res.json(createSuccessResponse(structure, '获取表结构成功'));
  } catch (error) {
    console.error('🗄️ [数据库管理] 获取表结构失败:', error);
    return res.json(createErrorResponse('获取表结构失败', ResponseCode.ERROR));
  }
}

/** 优化表 */
export async function optimizeTable(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { tableName } = req.params;
    console.log('🗄️ [数据库管理] 优化表:', tableName);

    const optimizeQuery = `OPTIMIZE TABLE \`${tableName}\``;
    await executeQuery(optimizeQuery);

    return res.json(createSuccessResponse(null, '表优化成功'));
  } catch (error) {
    console.error('🗄️ [数据库管理] 优化表失败:', error);
    return res.json(createErrorResponse('优化表失败', ResponseCode.ERROR));
  }
}

/** 修复表 */
export async function repairTable(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { tableName } = req.params;
    console.log('🗄️ [数据库管理] 修复表:', tableName);

    const repairQuery = `REPAIR TABLE \`${tableName}\``;
    await executeQuery(repairQuery);

    return res.json(createSuccessResponse(null, '表修复成功'));
  } catch (error) {
    console.error('🗄️ [数据库管理] 修复表失败:', error);
    return res.json(createErrorResponse('修复表失败', ResponseCode.ERROR));
  }
}

/** 清空表 */
export async function truncateTable(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { tableName } = req.params;
    console.log('🗄️ [数据库管理] 清空表:', tableName);

    const truncateQuery = `TRUNCATE TABLE \`${tableName}\``;
    await executeQuery(truncateQuery);

    return res.json(createSuccessResponse(null, '表清空成功'));
  } catch (error) {
    console.error('🗄️ [数据库管理] 清空表失败:', error);
    return res.json(createErrorResponse('清空表失败', ResponseCode.ERROR));
  }
}

/** 删除表 */
export async function dropTable(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { tableName } = req.params;
    console.log('🗄️ [数据库管理] 删除表:', tableName);

    const dropQuery = `DROP TABLE \`${tableName}\``;
    await executeQuery(dropQuery);

    return res.json(createSuccessResponse(null, '表删除成功'));
  } catch (error) {
    console.error('🗄️ [数据库管理] 删除表失败:', error);
    return res.json(createErrorResponse('删除表失败', ResponseCode.ERROR));
  }
}

/** 导出表 */
export async function exportTable(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { tableName } = req.params;
    console.log('🗄️ [数据库管理] 导出表:', tableName);

    // 获取表结构
    const createTableQuery = `SHOW CREATE TABLE \`${tableName}\``;
    const createTableResult = await executeQuery(createTableQuery);

    // 获取表数据
    const dataQuery = `SELECT * FROM \`${tableName}\``;
    const tableData = await executeQuery(dataQuery);

    // 生成SQL文件内容
    let sqlContent = `-- 表结构导出: ${tableName}\n`;
    sqlContent += `-- 导出时间: ${new Date().toLocaleString('zh-CN')}\n\n`;

    // 添加表结构
    sqlContent += `-- 表结构\n`;
    sqlContent += `${createTableResult[0]['Create Table']};\n\n`;

    // 添加数据
    if (tableData.length > 0) {
      sqlContent += `-- 表数据\n`;
      sqlContent += `INSERT INTO \`${tableName}\` VALUES\n`;

      const values = tableData.map((row: any) => {
        const rowValues = Object.values(row).map(value => {
          if (value === null) return 'NULL';
          if (typeof value === 'string') return `'${value.replace(/'/g, "''")}'`;
          return value;
        });
        return `(${rowValues.join(', ')})`;
      });

      sqlContent += `${values.join(',\n')};\n`;
    }

    // 设置响应头
    res.setHeader('Content-Type', 'application/sql');
    res.setHeader(
      'Content-Disposition',
      `attachment; filename="${tableName}_${new Date().toISOString().split('T')[0]}.sql"`
    );

    return res.send(sqlContent);
  } catch (error) {
    console.error('🗄️ [数据库管理] 导出表失败:', error);
    return res.json(createErrorResponse('导出表失败', ResponseCode.ERROR));
  }
}

/** 执行SQL */
export async function executeSQL(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { sql, limit = 100 } = req.body;
    console.log('🗄️ [数据库管理] 执行SQL:', `${sql.substring(0, 100)}...`);

    if (!sql || !sql.trim()) {
      return res.json(createErrorResponse('SQL语句不能为空', ResponseCode.PARAM_ERROR));
    }

    const startTime = Date.now();

    // 检查是否是查询语句
    const trimmedSQL = sql.trim().toUpperCase();
    const isSelectQuery =
      trimmedSQL.startsWith('SELECT') ||
      trimmedSQL.startsWith('SHOW') ||
      trimmedSQL.startsWith('DESCRIBE') ||
      trimmedSQL.startsWith('EXPLAIN');

    let result;
    if (isSelectQuery) {
      // 对于查询语句，添加LIMIT限制
      let limitedSQL = sql;
      if (!trimmedSQL.includes('LIMIT') && limit > 0) {
        limitedSQL += ` LIMIT ${limit}`;
      }

      result = await executeQuery(limitedSQL);

      // 获取列名
      const columns = result.length > 0 ? Object.keys(result[0]) : [];

      const executionTime = Date.now() - startTime;

      return res.json(
        createSuccessResponse(
          {
            columns,
            rows: result,
            affectedRows: 0,
            executionTime
          },
          'SQL执行成功'
        )
      );
    }
    // 对于非查询语句
    result = await executeQuery(sql);

    const executionTime = Date.now() - startTime;

    return res.json(
      createSuccessResponse(
        {
          columns: [],
          rows: [],
          affectedRows: result.affectedRows || 0,
          executionTime
        },
        'SQL执行成功'
      )
    );
  } catch (error: any) {
    console.error('🗄️ [数据库管理] 执行SQL失败:', error);
    return res.json(createErrorResponse(`SQL执行失败: ${error.message}`, ResponseCode.ERROR));
  }
}
