import type { Response } from 'express';
import { Request } from 'express';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import type { AuthenticatedRequest } from '../middleware/auth';

/**
 * 工单管理控制器
 * 遵循项目规范，提供完整的工单管理功能
 */

/** 创建工单 */
export async function createTicket(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { title, content, category = 'general', priority = 'normal' } = req.body;
    const userId = req.user?.userId;
    const username = req.user?.userName;

    if (!title || !content) {
      return res.json(createErrorResponse('标题和内容不能为空', ResponseCode.PARAM_ERROR));
    }

    // 生成工单号
    const ticketNo = `TK${Date.now()}${Math.floor(Math.random() * 1000)
      .toString()
      .padStart(3, '0')}`;

    // 插入工单
    const insertResult = await executeQuery(
      `
      INSERT INTO fd_ticket (
        ticket_no, title, content, category, priority,
        user_id, username, status, create_time
      ) VALUES (?, ?, ?, ?, ?, ?, ?, 'open', NOW())
    `,
      [ticketNo, title, content, category, priority, userId, username]
    );

    console.log(`🎫 [工单管理] 创建工单: ${ticketNo} - ${title}`);

    return res.json(
      createSuccessResponse(
        {
          ticketId: insertResult.insertId,
          ticketNo,
          title,
          createTime: new Date().toLocaleString('zh-CN')
        },
        '工单创建成功'
      )
    );
  } catch (error) {
    console.error('🎫 [工单管理] 创建工单失败:', error);
    return res.json(createErrorResponse('创建工单失败', ResponseCode.ERROR));
  }
}

/** 获取工单列表 */
export async function getTicketList(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const {
      page = 1,
      pageSize = 20,
      status = '',
      category = '',
      priority = '',
      keyword = '',
      userId = '' // 管理员可以查看指定用户的工单
    } = req.query;

    const currentUserId = req.user?.userId;
    const userRole = req.user?.roles?.[0] || 'user';
    const isAdmin = userRole === 'admin' || userRole === 'super_admin';

    const offset = (Number(page) - 1) * Number(pageSize);
    const whereConditions = ['1=1'];
    const queryParams: any[] = [];

    // 权限控制：普通用户只能查看自己的工单
    if (!isAdmin) {
      whereConditions.push('user_id = ?');
      queryParams.push(currentUserId);
    } else if (userId) {
      whereConditions.push('user_id = ?');
      queryParams.push(userId);
    }

    // 状态筛选
    if (status) {
      whereConditions.push('status = ?');
      queryParams.push(status);
    }

    // 分类筛选
    if (category) {
      whereConditions.push('category = ?');
      queryParams.push(category);
    }

    // 优先级筛选
    if (priority) {
      whereConditions.push('priority = ?');
      queryParams.push(priority);
    }

    // 关键词搜索
    if (keyword) {
      whereConditions.push('(title LIKE ? OR content LIKE ? OR ticket_no LIKE ?)');
      queryParams.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
    }

    const whereClause = whereConditions.join(' AND ');

    // 查询工单列表
    const ticketListQuery = `
      SELECT
        ticket_id as ticketId,
        ticket_no as ticketNo,
        title,
        content,
        category,
        priority,
        user_id as userId,
        username,
        status,
        assigned_to as assignedTo,
        assigned_name as assignedName,
        create_time as createTime,
        update_time as updateTime,
        close_time as closeTime
      FROM fd_ticket
      WHERE ${whereClause}
      ORDER BY create_time DESC
      LIMIT ? OFFSET ?
    `;

    queryParams.push(Number(pageSize), offset);
    const ticketList = await executeQuery(ticketListQuery, queryParams);

    // 查询总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM fd_ticket
      WHERE ${whereClause}
    `;

    const countParams = queryParams.slice(0, -2);
    const countResult = await executeQuery(countQuery, countParams);
    const total = countResult[0]?.total || 0;

    // 处理工单数据
    const processedTicketList = ticketList.map((ticket: any) => ({
      ...ticket,
      createTime: new Date(ticket.createTime).toLocaleString('zh-CN'),
      updateTime: ticket.updateTime ? new Date(ticket.updateTime).toLocaleString('zh-CN') : null,
      closeTime: ticket.closeTime ? new Date(ticket.closeTime).toLocaleString('zh-CN') : null
    }));

    return res.json(
      createSuccessResponse(
        {
          list: processedTicketList,
          pagination: {
            page: Number(page),
            pageSize: Number(pageSize),
            total,
            totalPages: Math.ceil(total / Number(pageSize))
          }
        },
        '获取工单列表成功'
      )
    );
  } catch (error) {
    console.error('🎫 [工单管理] 获取工单列表失败:', error);
    return res.json(createErrorResponse('获取工单列表失败', ResponseCode.ERROR));
  }
}

/** 获取工单详情 */
export async function getTicketDetail(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { ticketId } = req.params;
    const currentUserId = req.user?.userId;
    const userRole = req.user?.roles?.[0] || 'user';
    const isAdmin = userRole === 'admin' || userRole === 'super_admin';

    if (!ticketId) {
      return res.json(createErrorResponse('工单ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 查询工单详情
    let ticketQuery = `
      SELECT
        ticket_id as ticketId,
        ticket_no as ticketNo,
        title,
        content,
        category,
        priority,
        user_id as userId,
        username,
        status,
        assigned_to as assignedTo,
        assigned_name as assignedName,
        create_time as createTime,
        update_time as updateTime,
        close_time as closeTime
      FROM fd_ticket
      WHERE ticket_id = ?
    `;

    const queryParams = [ticketId];

    // 权限控制：普通用户只能查看自己的工单
    if (!isAdmin) {
      ticketQuery += ' AND user_id = ?';
      queryParams.push(String(currentUserId));
    }

    const ticketResult = await executeQuery(ticketQuery, queryParams);

    if (ticketResult.length === 0) {
      return res.json(createErrorResponse('工单不存在或无权限查看', ResponseCode.PARAM_ERROR));
    }

    const ticket = ticketResult[0];

    // 查询工单回复
    const repliesQuery = `
      SELECT
        reply_id as replyId,
        content,
        user_id as userId,
        username,
        user_role as userRole,
        create_time as createTime
      FROM fd_ticket_reply
      WHERE ticket_id = ?
      ORDER BY create_time ASC
    `;

    const replies = await executeQuery(repliesQuery, [ticketId]);

    // 处理数据
    const processedTicket = {
      ...ticket,
      createTime: new Date(ticket.createTime).toLocaleString('zh-CN'),
      updateTime: ticket.updateTime ? new Date(ticket.updateTime).toLocaleString('zh-CN') : null,
      closeTime: ticket.closeTime ? new Date(ticket.closeTime).toLocaleString('zh-CN') : null
    };

    const processedReplies = replies.map((reply: any) => ({
      ...reply,
      createTime: new Date(reply.createTime).toLocaleString('zh-CN')
    }));

    return res.json(
      createSuccessResponse(
        {
          ticket: processedTicket,
          replies: processedReplies
        },
        '获取工单详情成功'
      )
    );
  } catch (error) {
    console.error('🎫 [工单管理] 获取工单详情失败:', error);
    return res.json(createErrorResponse('获取工单详情失败', ResponseCode.ERROR));
  }
}

/** 回复工单 */
export async function replyTicket(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { ticketId } = req.params;
    const { content } = req.body;
    const userId = req.user?.userId;
    const username = req.user?.userName;
    const userRole = req.user?.roles?.[0] || 'user';

    if (!ticketId || !content) {
      return res.json(createErrorResponse('工单ID和回复内容不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查工单是否存在且可回复
    const ticketQuery = `
      SELECT ticket_id, user_id, status
      FROM fd_ticket
      WHERE ticket_id = ? AND status != 'closed'
    `;
    const ticketResult = await executeQuery(ticketQuery, [ticketId]);

    if (ticketResult.length === 0) {
      return res.json(createErrorResponse('工单不存在或已关闭', ResponseCode.PARAM_ERROR));
    }

    const ticket = ticketResult[0];
    const isAdmin = userRole === 'admin' || userRole === 'super_admin';

    // 权限检查：只有工单创建者或管理员可以回复
    if (!isAdmin && ticket.user_id !== userId) {
      return res.json(createErrorResponse('无权限回复此工单', ResponseCode.PERMISSION_DENIED));
    }

    // 插入回复
    await executeQuery(
      `
      INSERT INTO fd_ticket_reply (
        ticket_id, content, user_id, username, user_role, create_time
      ) VALUES (?, ?, ?, ?, ?, NOW())
    `,
      [ticketId, content, userId, username, userRole]
    );

    // 更新工单状态和时间
    let newStatus = ticket.status;
    if (isAdmin && ticket.status === 'open') {
      newStatus = 'in_progress';
    } else if (!isAdmin && ticket.status === 'in_progress') {
      newStatus = 'waiting_response';
    }

    await executeQuery(
      `
      UPDATE fd_ticket
      SET status = ?, update_time = NOW()
      WHERE ticket_id = ?
    `,
      [newStatus, ticketId]
    );

    console.log(`🎫 [工单管理] 回复工单: ${ticketId} - ${username}`);

    return res.json(
      createSuccessResponse(
        {
          ticketId,
          replyTime: new Date().toLocaleString('zh-CN')
        },
        '工单回复成功'
      )
    );
  } catch (error) {
    console.error('🎫 [工单管理] 回复工单失败:', error);
    return res.json(createErrorResponse('回复工单失败', ResponseCode.ERROR));
  }
}

/** 更新工单状态 */
export async function updateTicketStatus(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { ticketId } = req.params;
    const { status, assignedTo } = req.body;
    const userRole = req.user?.roles?.[0] || 'user';
    const isAdmin = userRole === 'admin' || userRole === 'super_admin';

    if (!isAdmin) {
      return res.json(createErrorResponse('无权限更新工单状态', ResponseCode.PERMISSION_DENIED));
    }

    if (!ticketId || !status) {
      return res.json(createErrorResponse('工单ID和状态不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查工单是否存在
    const ticketResult = await executeQuery('SELECT ticket_id FROM fd_ticket WHERE ticket_id = ?', [ticketId]);

    if (ticketResult.length === 0) {
      return res.json(createErrorResponse('工单不存在', ResponseCode.PARAM_ERROR));
    }

    // 更新工单状态
    let updateQuery = 'UPDATE fd_ticket SET status = ?, update_time = NOW()';
    const updateParams = [status];

    if (status === 'closed') {
      updateQuery += ', close_time = NOW()';
    }

    if (assignedTo) {
      // 查询分配用户信息
      const userResult = await executeQuery('SELECT username FROM fd_user WHERE user_id = ?', [assignedTo]);

      if (userResult.length > 0) {
        updateQuery += ', assigned_to = ?, assigned_name = ?';
        updateParams.push(assignedTo, userResult[0].username);
      }
    }

    updateQuery += ' WHERE ticket_id = ?';
    updateParams.push(ticketId);

    await executeQuery(updateQuery, updateParams);

    console.log(`🎫 [工单管理] 更新工单状态: ${ticketId} -> ${status}`);

    return res.json(createSuccessResponse(null, '工单状态更新成功'));
  } catch (error) {
    console.error('🎫 [工单管理] 更新工单状态失败:', error);
    return res.json(createErrorResponse('更新工单状态失败', ResponseCode.ERROR));
  }
}

/** 获取工单统计 */
export async function getTicketStats(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const userRole = req.user?.roles?.[0] || 'user';
    const isAdmin = userRole === 'admin' || userRole === 'super_admin';

    if (!isAdmin) {
      return res.json(createErrorResponse('无权限查看工单统计', ResponseCode.PERMISSION_DENIED));
    }

    // 总体统计
    const overallStatsResult = await executeQuery(`
      SELECT
        COUNT(*) as totalTickets,
        COUNT(CASE WHEN status = 'open' THEN 1 END) as openTickets,
        COUNT(CASE WHEN status = 'in_progress' THEN 1 END) as inProgressTickets,
        COUNT(CASE WHEN status = 'waiting_response' THEN 1 END) as waitingResponseTickets,
        COUNT(CASE WHEN status = 'closed' THEN 1 END) as closedTickets,
        COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END) as todayTickets
      FROM fd_ticket
    `);

    // 分类统计
    const categoryStatsResult = await executeQuery(`
      SELECT
        category,
        COUNT(*) as count
      FROM fd_ticket
      GROUP BY category
      ORDER BY count DESC
    `);

    // 优先级统计
    const priorityStatsResult = await executeQuery(`
      SELECT
        priority,
        COUNT(*) as count
      FROM fd_ticket
      GROUP BY priority
      ORDER BY
        CASE priority
          WHEN 'urgent' THEN 1
          WHEN 'high' THEN 2
          WHEN 'normal' THEN 3
          WHEN 'low' THEN 4
        END
    `);

    const stats = {
      overview: overallStatsResult[0] || {
        totalTickets: 0,
        openTickets: 0,
        inProgressTickets: 0,
        waitingResponseTickets: 0,
        closedTickets: 0,
        todayTickets: 0
      },
      categoryStats: categoryStatsResult,
      priorityStats: priorityStatsResult
    };

    return res.json(createSuccessResponse(stats, '获取工单统计成功'));
  } catch (error) {
    console.error('🎫 [工单管理] 获取工单统计失败:', error);
    return res.json(createErrorResponse('获取工单统计失败', ResponseCode.ERROR));
  }
}
