import * as process from 'node:process';
import type { Request, Response } from 'express';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import { createLicenseService } from '../utils/licenseService';
import {
  deleteLicenseInfo,
  getCurrentLicenseInfo,
  isInGracePeriod,
  markLicenseAsSuspicious,
  saveLicenseInfo
} from '../model/license';
import { licenseManager } from '../utils/licenseManager';

/** 安全的JSON解析函数 */
function safeJsonParse(input: any): any {
  if (!input) return null;

  // 如果已经是对象，直接返回
  if (typeof input === 'object') {
    return input;
  }

  // 如果是字符串，尝试解析
  if (typeof input === 'string') {
    try {
      return JSON.parse(input);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      console.warn('🔒 [警告] JSON解析失败:', { input: input.substring(0, 100), error: errorMessage });
      return null;
    }
  }

  // 其他类型直接返回
  return input;
}

/** 安全验证中间件 - 检测可疑的授权状态变更 */
async function performSecurityCheck(_domain: string, licenseInfo: any): Promise<boolean> {
  if (!licenseInfo) return true; // 没有数据时跳过检查

  // 检查1: 验证时间异常
  const now = new Date();
  const validatedAt = new Date(licenseInfo.validated_at);
  const timeDiff = Math.abs(now.getTime() - validatedAt.getTime()) / (1000 * 60); // 分钟

  if (timeDiff > 60) {
    // 验证时间超过1小时前，记录异常但不阻止验证
  }

  // 检查2: 状态异常变更（暂时禁用，允许新的授权验证）
  // if (licenseInfo.status === 'active' && !licenseInfo.last_external_validation) {
  //   console.warn('🔒 [安全警告] 发现active状态但无外部验证记录');
  //   return false;
  // }

  // 检查3: 过期时间异常
  if (licenseInfo.expires_at) {
    const expiryDate = new Date(licenseInfo.expires_at);
    const yearsFromNow = (expiryDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24 * 365);

    if (yearsFromNow > 10) {
      // 过期时间超过10年，可能被篡改
      return false;
    }
  }

  return true;
}

/** 验证授权密钥（项目后端API - 调用外部授权系统并存储到项目数据库） */
export async function validateLicense(req: Request, res: Response): Promise<any> {
  try {
    const { licenseKey } = req.body;

    if (!licenseKey) {
      return res.json(createErrorResponse('授权密钥不能为空', ResponseCode.PARAM_ERROR));
      return;
    }

    // 获取域名
    const domain = req.headers.host?.split(':')[0] || process.env.DOMAIN || 'localhost';

    const licenseService = createLicenseService(domain);

    // 调用外部授权系统验证
    const result = await licenseService.validateLicenseKey(licenseKey);

    if (result.success && result.data) {
      // 验证成功，licenseService.validateLicenseKey 已经保存了完整的详细信息到数据库

      // 🔥 立即更新授权管理器状态（直接设置，避免重新验证破坏数据一致性）
      console.log('🔒 [授权验证] 验证成功，直接更新授权管理器状态...');
      if (result.data?.status === 'active') {
        licenseManager.setActivated(true);
        console.log('🔒 [授权验证] 授权管理器状态已更新为：已激活');
      } else {
        licenseManager.setActivated(false);
        console.log('🔒 [授权验证] 授权管理器状态已更新为：未激活');
      }

      return res.json(createSuccessResponse(result.data, result.message));
    }
    return res.json(createErrorResponse(result.message, ResponseCode.AUTH_ERROR));
  } catch {
    return res.json(createErrorResponse('服务器内部错误', ResponseCode.ERROR));
  }
}

/** 获取当前授权状态（安全模式 - 不暴露敏感信息） */
export async function getLicenseStatus(req: Request, res: Response): Promise<any> {
  try {
    // 获取域名
    const domain = req.headers.host?.split(':')[0] || process.env.DOMAIN || 'localhost';

    // 减少日志输出，只在必要时记录

    // 从项目数据库获取授权信息
    const licenseInfo = await getCurrentLicenseInfo(domain);

    if (licenseInfo) {
      // 减少频繁的日志输出

      // 执行安全检查
      const securityCheckPassed = await performSecurityCheck(domain, licenseInfo);
      if (!securityCheckPassed) {
        await markLicenseAsSuspicious(domain, '安全检查失败');

        return res.json(
          createSuccessResponse(
            {
              isAuthorized: false,
              status: 'security_violation',
              message: '系统安全检查失败，请联系管理员重新激活'
            },
            '安全检查失败'
          )
        );
        return;
      }

      // 检查授权是否过期
      const now = new Date();
      const isExpired = licenseInfo.expires_at && new Date(licenseInfo.expires_at) < now;
      const inGracePeriod = isInGracePeriod(licenseInfo);

      let currentStatus = licenseInfo.status;
      let isAuthorized = false;
      let message = '';

      if (isExpired && !inGracePeriod) {
        currentStatus = 'expired';
        isAuthorized = false;
        message = '系统授权已过期，请联系管理员续费';
      } else if (isExpired && inGracePeriod) {
        currentStatus = 'grace_period';
        isAuthorized = true; // 宽限期内仍可使用
        message = '授权验证失败，当前处于宽限期';
      } else if (currentStatus === 'active') {
        isAuthorized = true;
        message = '系统已授权，运行正常';
      } else {
        isAuthorized = false;
        message = '系统未授权，请联系管理员';
      }

      // 返回详细的授权信息给前端显示
      const responseData = {
        isAuthorized,
        status: currentStatus,
        message,
        // 详细授权信息
        licenseKey: licenseInfo.license_key ? '●●●●●●●●●●●●●●●●' : '', // 脱敏显示
        domain: licenseInfo.domain,
        expiryDate: licenseInfo.expires_at,
        validatedAt: licenseInfo.validated_at,
        boundAt: licenseInfo.created_at,
        plan: licenseInfo.plan,
        planDescription: licenseInfo.plan_description,
        startDate: licenseInfo.start_date,
        daysLeft: licenseInfo.days_left,
        maxProducts: licenseInfo.max_products,
        maxUsers: licenseInfo.max_users,
        // 安全解析JSON字段
        planFeatures: licenseInfo.plan_features ? safeJsonParse(licenseInfo.plan_features) : null,
        bindingInfo: licenseInfo.binding_info ? safeJsonParse(licenseInfo.binding_info) : null,
        usageStats: licenseInfo.usage_stats ? safeJsonParse(licenseInfo.usage_stats) : null,
        // 宽限期信息
        gracePeriodStart: licenseInfo.grace_period_start,
        gracePeriodEnd: licenseInfo.grace_period_end,
        inGracePeriod,
        // 验证信息
        lastExternalValidation: licenseInfo.last_external_validation,
        validationCount: licenseInfo.validation_count
      };

      console.log('🔒 [详细信息] 返回授权详细信息给前端');

      return res.json(createSuccessResponse(responseData, '授权检查完成'));
    }
    // 未找到授权信息，返回空的详细信息结构
    const responseData = {
      isAuthorized: false,
      status: 'inactive',
      message: '系统未授权，请联系管理员激活',
      // 空的详细授权信息
      licenseKey: '',
      domain: req.headers.host?.split(':')[0] || process.env.DOMAIN || 'localhost',
      expiryDate: null,
      validatedAt: null,
      boundAt: null,
      plan: null,
      planDescription: null,
      startDate: null,
      daysLeft: null,
      maxProducts: null,
      maxUsers: null,
      planFeatures: null,
      bindingInfo: null,
      usageStats: null,
      gracePeriodStart: null,
      gracePeriodEnd: null,
      inGracePeriod: false,
      lastExternalValidation: null,
      validationCount: 0
    };

    return res.json(createSuccessResponse(responseData, '未找到授权信息'));
  } catch {
    return res.json(createErrorResponse('系统授权检查失败', ResponseCode.ERROR));
  }
}

/** 刷新授权状态 */
export async function refreshLicense(req: Request, res: Response): Promise<any> {
  try {
    // 获取域名
    const domain = req.headers.host?.split(':')[0] || process.env.DOMAIN || 'localhost';
    const licenseService = createLicenseService(domain);

    // 刷新授权状态
    const result = await licenseService.refreshLicenseStatus();

    if (result.success) {
      return res.json(createSuccessResponse(result.data, result.message));
    }
    return res.json(createErrorResponse(result.message, ResponseCode.ERROR));
  } catch {
    return res.json(createErrorResponse('服务器内部错误', ResponseCode.ERROR));
  }
}

/** 重置授权信息 */
export async function resetLicense(req: Request, res: Response): Promise<any> {
  try {
    // 获取域名
    const domain = req.headers.host?.split(':')[0] || process.env.DOMAIN || 'localhost';

    // 删除授权信息
    const deleted = await deleteLicenseInfo(domain);

    if (deleted) {
      return res.json(createSuccessResponse(null, '授权信息已重置'));
    }
    return res.json(createErrorResponse('未找到授权信息', ResponseCode.ERROR));
  } catch {
    return res.json(createErrorResponse('服务器内部错误', ResponseCode.ERROR));
  }
}

// 获取授权详细信息 - 已移除，管理功能已解耦

/** 激活系统授权（新的统一接口） */
export async function activateSystem(req: Request, res: Response): Promise<any> {
  const { licenseKey } = req.body;

  if (!licenseKey) {
    return res.json(createErrorResponse('授权密钥不能为空', ResponseCode.PARAM_ERROR));
    return;
  }

  try {
    // 使用授权管理器激活系统
    const result = await licenseManager.activate(licenseKey);

    if (result.success) {
      return res.json(
        createSuccessResponse(
          {
            activated: true,
            message: result.message
          },
          result.message
        )
      );
    }
    return res.json(createErrorResponse(result.message, ResponseCode.AUTH_ERROR));
  } catch (error: any) {
    return res.json(createErrorResponse(error.message || '系统激活失败', ResponseCode.ERROR));
  }
}

/** 获取系统激活状态 */
export async function getSystemStatus(_req: Request, res: Response): Promise<any> {
  try {
    const status = licenseManager.getStatus();
    const isActive = licenseManager.isActive();

    return res.json(
      createSuccessResponse(
        {
          isActivated: isActive,
          lastCheck: status.lastCheck,
          nextCheck: status.nextCheck,
          message: isActive ? '系统已激活' : '系统未激活'
        },
        '获取系统状态成功'
      )
    );
  } catch {
    return res.json(createErrorResponse('获取系统状态失败', ResponseCode.ERROR));
  }
}

/** 手动重新验证授权 */
export async function revalidateSystem(_req: Request, res: Response): Promise<any> {
  try {
    const result = await licenseManager.revalidate();

    if (result.success) {
      return res.json(
        createSuccessResponse(
          {
            revalidated: true,
            message: result.message
          },
          result.message
        )
      );
    }
    return res.json(createErrorResponse(result.message, ResponseCode.ERROR));
  } catch {
    return res.json(createErrorResponse('重新验证失败', ResponseCode.ERROR));
  }
}
