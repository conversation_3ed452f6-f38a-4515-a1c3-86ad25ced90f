import axios from 'axios';
import type { Response } from 'express';
import { Request } from 'express';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse, sendError, sendSuccess } from '../utils/response';
import type { AuthenticatedRequest } from '../middleware/auth';
import { ProviderManager } from '../service/ProviderManager';

/**
 * 供应商管理控制器
 * 遵循项目规范，提供完整的供应商管理功能
 */

/** 获取供应商列表 */
export async function getProviderList(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { page = 1, pageSize = 20, keyword = '', status = '' } = req.query;

    const offset = (Number(page) - 1) * Number(pageSize);
    const whereConditions = ['1=1'];
    const queryParams: any[] = [];

    // 关键词搜索（供应商名称、代码）
    if (keyword) {
      whereConditions.push('(p.name LIKE ? OR p.code LIKE ?)');
      queryParams.push(`%${keyword}%`, `%${keyword}%`);
    }

    // 状态筛选
    if (status !== '') {
      whereConditions.push('p.status = ?');
      queryParams.push(Number(status));
    }

    const whereClause = whereConditions.join(' AND ');

    // 查询供应商列表
    const providerListQuery = `
      SELECT
        p.provider_id as providerId,
        p.code,
        p.name,
        p.logo_url as logoUrl,
        p.api_url as apiUrl,
        p.username,
        p.balance,
        p.ip_whitelist as ipWhitelist,
        p.status,
        p.create_time as createTime,
        p.update_time as updateTime
      FROM fd_provider p
      WHERE ${whereClause}
      ORDER BY p.create_time DESC
      LIMIT ${Number(pageSize)} OFFSET ${offset}
    `;

    const providerList = await executeQuery(providerListQuery, queryParams);

    // 查询总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM fd_provider p
      WHERE ${whereClause}
    `;

    const countResult = await executeQuery(countQuery, queryParams);
    const total = countResult[0]?.total || 0;

    // 处理供应商数据
    const processedProviderList = providerList.map((provider: any) => ({
      ...provider,
      createTime: new Date(provider.createTime).toLocaleString('zh-CN'),
      updateTime: new Date(provider.updateTime).toLocaleString('zh-CN'),
      // 隐藏敏感信息
      password: provider.password ? '******' : null,
      token: provider.token ? '******' : null
    }));

    return res.json(
      createSuccessResponse(
        {
          list: processedProviderList,
          pagination: {
            page: Number(page),
            pageSize: Number(pageSize),
            total,
            totalPages: Math.ceil(total / Number(pageSize))
          }
        },
        '获取供应商列表成功'
      )
    );
  } catch (error) {
    console.error('🏪 [供应商管理] 获取供应商列表失败:', error);
    return res.json(createErrorResponse('获取供应商列表失败', ResponseCode.ERROR));
  }
}

/** 获取供应商详情 */
export async function getProviderDetail(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;

    if (!providerId) {
      return res.json(createErrorResponse('供应商ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 查询供应商详情
    const providerQuery = `
      SELECT
        provider_id as providerId,
        code,
        name,
        logo_url as logoUrl,
        api_url as apiUrl,
        username,
        password,
        token,
        balance,
        ip_whitelist as ipWhitelist,
        api_config as apiConfig,
        custom_query_code as customQueryCode,
        custom_order_code as customOrderCode,
        custom_sync_code as customSyncCode,
        status,
        create_time as createTime,
        update_time as updateTime
      FROM fd_provider
      WHERE provider_id = ?
    `;

    const providerResult = await executeQuery(providerQuery, [providerId]);

    if (providerResult.length === 0) {
      return res.json(createErrorResponse('供应商不存在', ResponseCode.PARAM_ERROR));
    }

    const provider = providerResult[0];

    // 处理供应商数据
    const processedProvider = {
      ...provider,
      createTime: new Date(provider.createTime).toLocaleString('zh-CN'),
      updateTime: new Date(provider.updateTime).toLocaleString('zh-CN'),
      apiConfig: provider.apiConfig
        ? typeof provider.apiConfig === 'string'
          ? JSON.parse(provider.apiConfig)
          : provider.apiConfig
        : null
    };

    return res.json(createSuccessResponse(processedProvider, '获取供应商详情成功'));
  } catch (error) {
    console.error('🏪 [供应商管理] 获取供应商详情失败:', error);
    return res.json(createErrorResponse('获取供应商详情失败', ResponseCode.ERROR));
  }
}

/** 创建供应商 */
export async function createProvider(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const {
      code,
      name,
      logoUrl,
      apiUrl,
      username,
      password,
      token,
      ipWhitelist,
      apiConfig,
      customQueryCode,
      customOrderCode,
      customSyncCode,
      status = 1
    } = req.body;

    // 参数验证
    if (!code || !name) {
      return res.json(createErrorResponse('供应商代码和名称不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查代码是否已存在
    const existingProvider = await executeQuery('SELECT provider_id FROM fd_provider WHERE code = ?', [code]);

    if (existingProvider.length > 0) {
      return res.json(createErrorResponse('供应商代码已存在', ResponseCode.PARAM_ERROR));
    }

    // 创建供应商
    const insertResult = await executeQuery(
      `INSERT INTO fd_provider (
        code, name, logo_url, api_url, username, password, token,
        ip_whitelist, api_config, custom_query_code, custom_order_code,
        custom_sync_code, status, create_time, update_time
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [
        code,
        name,
        logoUrl || null,
        apiUrl,
        username,
        password,
        token || null,
        ipWhitelist || null,
        apiConfig ? JSON.stringify(apiConfig) : null,
        customQueryCode || null,
        customOrderCode || null,
        customSyncCode || null,
        status
      ]
    );

    const providerId = insertResult.insertId;

    console.log(`🏪 [供应商管理] 供应商创建成功: ${name} (ID: ${providerId})`);

    return res.json(
      createSuccessResponse(
        {
          providerId,
          code,
          name,
          message: '供应商创建成功'
        },
        '供应商创建成功'
      )
    );
  } catch (error) {
    console.error('🏪 [供应商管理] 创建供应商失败:', error);
    return res.json(createErrorResponse('创建供应商失败', ResponseCode.ERROR));
  }
}

/** 更新供应商 */
export async function updateProvider(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;
    const {
      code,
      name,
      logoUrl,
      apiUrl,
      username,
      password,
      token,
      ipWhitelist,
      apiConfig,
      customQueryCode,
      customOrderCode,
      customSyncCode,
      status
    } = req.body;

    if (!providerId) {
      return res.json(createErrorResponse('供应商ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查供应商是否存在
    const existingProvider = await executeQuery(
      'SELECT provider_id, code, name FROM fd_provider WHERE provider_id = ?',
      [providerId]
    );

    if (existingProvider.length === 0) {
      return res.json(createErrorResponse('供应商不存在', ResponseCode.PARAM_ERROR));
    }

    // 如果更新代码，检查是否与其他供应商冲突
    if (code && code !== existingProvider[0].code) {
      const codeExists = await executeQuery('SELECT provider_id FROM fd_provider WHERE code = ? AND provider_id != ?', [
        code,
        providerId
      ]);

      if (codeExists.length > 0) {
        return res.json(createErrorResponse('供应商代码已被其他供应商使用', ResponseCode.PARAM_ERROR));
      }
    }

    const updateFields: string[] = [];
    const updateValues: any[] = [];

    // 构建更新字段
    if (code !== undefined) {
      updateFields.push('code = ?');
      updateValues.push(code);
    }

    if (name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(name);
    }

    if (logoUrl !== undefined) {
      updateFields.push('logo_url = ?');
      updateValues.push(logoUrl);
    }

    if (apiUrl !== undefined) {
      updateFields.push('api_url = ?');
      updateValues.push(apiUrl);
    }

    if (username !== undefined) {
      updateFields.push('username = ?');
      updateValues.push(username);
    }

    if (password !== undefined) {
      updateFields.push('password = ?');
      updateValues.push(password);
    }

    if (token !== undefined) {
      updateFields.push('token = ?');
      updateValues.push(token);
    }

    if (ipWhitelist !== undefined) {
      updateFields.push('ip_whitelist = ?');
      updateValues.push(ipWhitelist);
    }

    if (apiConfig !== undefined) {
      updateFields.push('api_config = ?');
      updateValues.push(apiConfig ? JSON.stringify(apiConfig) : null);
    }

    if (status !== undefined) {
      updateFields.push('status = ?');
      updateValues.push(status);
    }

    if (customQueryCode !== undefined) {
      updateFields.push('custom_query_code = ?');
      updateValues.push(customQueryCode);
    }

    if (customOrderCode !== undefined) {
      updateFields.push('custom_order_code = ?');
      updateValues.push(customOrderCode);
    }

    if (customSyncCode !== undefined) {
      updateFields.push('custom_sync_code = ?');
      updateValues.push(customSyncCode);
    }

    if (updateFields.length === 0) {
      return res.json(createErrorResponse('没有需要更新的字段', ResponseCode.PARAM_ERROR));
    }

    updateFields.push('update_time = NOW()');
    updateValues.push(providerId);

    // 更新供应商
    await executeQuery(`UPDATE fd_provider SET ${updateFields.join(', ')} WHERE provider_id = ?`, updateValues);

    console.log(`🏪 [供应商管理] 供应商更新成功: ${existingProvider[0].name} (ID: ${providerId})`);

    return res.json(createSuccessResponse(null, '供应商更新成功'));
  } catch (error) {
    console.error('🏪 [供应商管理] 更新供应商失败:', error);
    return res.json(createErrorResponse('更新供应商失败', ResponseCode.ERROR));
  }
}

/** 删除供应商 */
export async function deleteProvider(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;

    if (!providerId) {
      return res.json(createErrorResponse('供应商ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查供应商是否存在
    const existingProvider = await executeQuery('SELECT provider_id, name FROM fd_provider WHERE provider_id = ?', [
      providerId
    ]);

    if (existingProvider.length === 0) {
      return res.json(createErrorResponse('供应商不存在', ResponseCode.PARAM_ERROR));
    }

    // 检查是否有关联的订单
    const relatedOrders = await executeQuery('SELECT COUNT(*) as count FROM fd_order WHERE provider_id = ?', [
      providerId
    ]);

    if (relatedOrders[0].count > 0) {
      return res.json(createErrorResponse('该供应商有关联订单，无法删除', ResponseCode.FORBIDDEN));
    }

    // 删除供应商
    await executeQuery('DELETE FROM fd_provider WHERE provider_id = ?', [providerId]);

    console.log(`🏪 [供应商管理] 供应商删除成功: ${existingProvider[0].name} (ID: ${providerId})`);

    return res.json(createSuccessResponse(null, '供应商删除成功'));
  } catch (error) {
    console.error('🏪 [供应商管理] 删除供应商失败:', error);
    return res.json(createErrorResponse('删除供应商失败', ResponseCode.ERROR));
  }
}

/** 测试供应商API连接 */
export async function testProviderConnection(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;

    if (!providerId) {
      return res.json(createErrorResponse('供应商ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 查询供应商信息
    const providerQuery = `
      SELECT
        provider_id, name, api_url, username, password, token, api_config
      FROM fd_provider
      WHERE provider_id = ? AND status = 1
    `;

    const providerResult = await executeQuery(providerQuery, [providerId]);

    if (providerResult.length === 0) {
      return res.json(createErrorResponse('供应商不存在或已禁用', ResponseCode.PARAM_ERROR));
    }

    const provider = providerResult[0];

    if (!provider.api_url) {
      return res.json(createErrorResponse('供应商未配置API地址', ResponseCode.PARAM_ERROR));
    }

    // 构建测试请求
    const testStartTime = Date.now();
    let testResult: any = {
      success: false,
      responseTime: 0,
      statusCode: 0,
      message: '',
      data: null
    };

    try {
      // 对于29平台，使用实际的API端点进行测试
      let testUrl = provider.api_url;
      let testMethod = 'POST';
      let testData = {};

      if (provider.code === '29pt') {
        // 29平台使用查课接口进行连接测试
        testUrl = 'https://freedomp.icu/api.php?act=get';
        testData = {
          uid: provider.username,
          key: provider.password,
          platform: 'test',
          user: 'test',
          pass: 'test',
          school: 'test',
          kcid: 'test'
        };
      } else {
        // 其他平台尝试访问根路径
        testMethod = 'GET';
      }

      // 发送测试请求
      const response = await axios({
        method: testMethod,
        url: testUrl,
        data: testMethod === 'POST' ? testData : undefined,
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'SoybeanAdmin/1.0'
        },
        validateStatus: () => true // 不抛出HTTP错误
      });

      // 判断测试是否成功
      let isSuccess = false;
      let message = '连接测试失败';

      if (provider.code === '29pt') {
        // 29平台：检查响应格式
        if (response.status === 200 && response.data) {
          if (typeof response.data === 'object' && 'code' in response.data) {
            isSuccess = true;
            message =
              response.data.code === 0 ? 'API连接正常' : `API连接正常，返回: ${response.data.msg || '未知错误'}`;
          } else {
            message = 'API连接正常，但响应格式异常';
            isSuccess = true; // 能连通就算成功
          }
        } else {
          message = `HTTP ${response.status}: 连接失败`;
        }
      } else {
        // 其他平台：HTTP状态码200就算成功
        if (response.status >= 200 && response.status < 400) {
          isSuccess = true;
          message = 'API连接正常';
        } else {
          message = `HTTP ${response.status}: 连接失败`;
        }
      }

      testResult = {
        success: isSuccess,
        responseTime: Date.now() - testStartTime,
        statusCode: response.status,
        message,
        data: response.data
      };
    } catch (error: any) {
      testResult = {
        success: false,
        responseTime: Date.now() - testStartTime,
        statusCode: error.response?.status || 0,
        message: error.message || '连接测试失败',
        data: error.response?.data || null
      };
    }

    // 更新最后测试时间
    await executeQuery('UPDATE fd_provider SET update_time = NOW() WHERE provider_id = ?', [providerId]);

    console.log(`🏪 [供应商管理] API连接测试: ${provider.name} - ${testResult.success ? '成功' : '失败'}`);

    return res.json(createSuccessResponse(testResult, 'API连接测试完成'));
  } catch (error) {
    console.error('🏪 [供应商管理] API连接测试失败:', error);
    return res.json(createErrorResponse('API连接测试失败', ResponseCode.ERROR));
  }
}

/** 查询供应商余额 */
export async function getProviderBalance(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;

    if (!providerId) {
      return res.json(createErrorResponse('供应商ID不能为空', ResponseCode.PARAM_ERROR));
    }

    console.log(`🏪 [供应商管理] 开始查询供应商余额: ID=${providerId}`);

    // 使用ProviderManager统一接口查询余额
    const result = await ProviderManager.getBalance(Number(providerId));

    if (result.success && result.data) {
      // 更新数据库中的余额
      await executeQuery('UPDATE fd_provider SET balance = ?, update_time = NOW() WHERE provider_id = ?', [
        result.data.balance,
        providerId
      ]);

      console.log(`🏪 [供应商管理] 余额查询成功: ID=${providerId} - ¥${result.data.balance}`);
    } else {
      console.error(`🏪 [供应商管理] 余额查询失败: ID=${providerId} - ${result.message}`);
    }

    return res.json(createSuccessResponse(result.data || result, result.message || '余额查询完成'));
  } catch (error: any) {
    console.error('🏪 [供应商管理] 余额查询异常:', error);
    return res.json(createErrorResponse(error.message || '余额查询失败', ResponseCode.ERROR));
  }
}

// ===== 服务商对接接口 =====

/** 查询课程接口 */
export async function queryCourses(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;
    const { platform, username, password, school } = req.body;

    if (!providerId || !platform || !username || !password) {
      return res.json(createErrorResponse('参数不完整', ResponseCode.PARAM_ERROR));
    }

    const result = await ProviderManager.queryCourses(Number(providerId), {
      school,
      username,
      password
    });

    if (result.success) {
      return res.json(createSuccessResponse(result.data, '查询课程成功'));
    }
    return res.json(createErrorResponse(result.message, ResponseCode.ERROR));
  } catch (error) {
    console.error('🏪 [服务商对接] 查询课程失败:', error);
    return res.json(createErrorResponse('查询课程失败', ResponseCode.ERROR));
  }
}

/** 创建上游订单接口 */
export async function createUpstreamOrder(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;
    const { platform, username, password, school, course_id, course_name, quantity, remark } = req.body;

    if (!providerId || !platform || !username || !password || !course_name) {
      return res.json(createErrorResponse('参数不完整', ResponseCode.PARAM_ERROR));
    }

    const result = await ProviderManager.createOrder(Number(providerId), {
      school,
      username,
      password,
      course_id,
      course_name
    });

    if (result.success) {
      return res.json(createSuccessResponse(result.data, '创建上游订单成功'));
    }
    return res.json(createErrorResponse(result.message, ResponseCode.ERROR));
  } catch (error) {
    console.error('🏪 [服务商对接] 创建上游订单失败:', error);
    return res.json(createErrorResponse('创建上游订单失败', ResponseCode.ERROR));
  }
}

/** 同步订单状态接口 */
export async function syncOrderStatus(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;
    const { order_id, upstream_order_id } = req.body;

    if (!providerId || !order_id) {
      return res.json(createErrorResponse('参数不完整', ResponseCode.PARAM_ERROR));
    }

    const result = await ProviderManager.syncOrderStatus(Number(providerId), {
      upstream_order_id
    });

    if (result.success) {
      return res.json(createSuccessResponse(result.data, '同步订单状态成功'));
    }
    return res.json(createErrorResponse(result.message, ResponseCode.ERROR));
  } catch (error) {
    console.error('🏪 [服务商对接] 同步订单状态失败:', error);
    return res.json(createErrorResponse('同步订单状态失败', ResponseCode.ERROR));
  }
}

/** 补刷订单接口 */
export async function refillOrder(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;
    const { order_id, upstream_order_id, remark } = req.body;

    if (!providerId || !order_id) {
      return res.json(createErrorResponse('参数不完整', ResponseCode.PARAM_ERROR));
    }

    const result = await ProviderManager.refillOrder(Number(providerId), {
      upstream_order_id
    });

    if (result.success) {
      return res.json(createSuccessResponse(result.data, '补刷订单成功'));
    }
    return res.json(createErrorResponse(result.message, ResponseCode.ERROR));
  } catch (error) {
    console.error('🏪 [服务商对接] 补刷订单失败:', error);
    return res.json(createErrorResponse('补刷订单失败', ResponseCode.ERROR));
  }
}

/** 修改密码接口 */
export async function changePassword(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;
    const { upstream_order_id, new_password } = req.body;

    if (!providerId || !upstream_order_id || !new_password) {
      return res.json(createErrorResponse('参数不完整', ResponseCode.PARAM_ERROR));
    }

    const result = await ProviderManager.changePassword(Number(providerId), {
      upstream_order_id,
      new_password
    });

    if (result.success) {
      return res.json(createSuccessResponse(result.data, '修改密码成功'));
    }
    return res.json(createErrorResponse(result.message, ResponseCode.ERROR));
  } catch (error) {
    console.error('🏪 [服务商对接] 修改密码失败:', error);
    return res.json(createErrorResponse('修改密码失败', ResponseCode.ERROR));
  }
}

/** 获取课程列表接口 */
export async function getCourseList(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;

    if (!providerId) {
      return res.json(createErrorResponse('服务商ID不能为空', ResponseCode.PARAM_ERROR));
    }

    console.log(`🏪 [服务商对接] 开始获取课程列表: ID=${providerId}`);

    const result = await ProviderManager.getCourseList(Number(providerId));

    if (result.success) {
      console.log(`🏪 [服务商对接] 获取课程列表成功: ID=${providerId}`);
      return res.json(createSuccessResponse(result.data, '获取课程列表成功'));
    }
    console.error(`🏪 [服务商对接] 获取课程列表失败: ID=${providerId} - ${result.message}`);
    return res.json(createErrorResponse(result.message, ResponseCode.ERROR));
  } catch (error: any) {
    console.error('🏪 [服务商对接] 获取课程列表异常:', error);
    return res.json(createErrorResponse(error.message || '获取课程列表失败', ResponseCode.ERROR));
  }
}
