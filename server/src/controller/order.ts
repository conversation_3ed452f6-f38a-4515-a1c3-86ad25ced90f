import type { Response } from 'express';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import type { AuthenticatedRequest } from '../middleware/auth';
import { ProviderManager } from '../service/ProviderManager';
import { logOrderOperation as logOrderOp } from '../utils/logger';
// 暂时注释同步模块导入，避免循环依赖
// import { SyncScheduler } from '../service/sync/SyncScheduler';
// import { SyncConfigManager } from '../service/sync/SyncConfigManager';
// import { SyncMonitor } from '../service/sync/SyncMonitor';

/**
 * 订单管理控制器
 * 遵循项目规范，提供完整的订单管理功能
 */

// 订单状态枚举
export enum OrderStatus {
  PENDING_PAYMENT = 1, // 待支付
  PENDING_PROCESS = 2, // 待处理
  PROCESSING = 3, // 处理中
  COMPLETED = 4, // 已完成
  CANCELLED = 5, // 已取消
  FAILED = 6, // 处理失败
  PARTIAL_SUCCESS = 7 // 部分成功
}

// 订单状态文本映射
const ORDER_STATUS_TEXT = {
  [OrderStatus.PENDING_PAYMENT]: '待支付',
  [OrderStatus.PENDING_PROCESS]: '待处理',
  [OrderStatus.PROCESSING]: '处理中',
  [OrderStatus.COMPLETED]: '已完成',
  [OrderStatus.CANCELLED]: '已取消',
  [OrderStatus.FAILED]: '处理失败',
  [OrderStatus.PARTIAL_SUCCESS]: '部分成功'
};

/** 获取订单列表 */
export async function getOrderList(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { page = 1, pageSize = 20, keyword = '', status, startTime, endTime } = req.query;

    const userId = req.user?.userId;
    const userRole = req.user?.roles?.[0];

    // 构建查询条件
    const whereConditions = [];
    const queryParams: any[] = [];

    // 权限控制：普通用户只能查看自己的订单
    if (userRole !== 'admin' && userRole !== 'super_admin') {
      whereConditions.push('user_id = ?');
      queryParams.push(userId);
    }

    // 关键词搜索
    if (keyword) {
      whereConditions.push('(order_no LIKE ? OR school_name LIKE ?)');
      const keywordParam = `%${keyword}%`;
      queryParams.push(keywordParam, keywordParam);
    }

    // 状态筛选
    if (status !== undefined && status !== '') {
      whereConditions.push('status = ?');
      queryParams.push(Number(status));
    }

    // 时间范围筛选
    if (startTime) {
      whereConditions.push('create_time >= ?');
      queryParams.push(startTime);
    }
    if (endTime) {
      whereConditions.push('create_time <= ?');
      queryParams.push(endTime);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 查询总数
    const countQuery = `SELECT COUNT(*) as total FROM fd_order ${whereClause}`;
    const countResult = await executeQuery(countQuery, queryParams);
    const total = countResult[0]?.total || 0;

    // 如果没有订单，直接返回空列表
    if (total === 0) {
      return res.json(
        createSuccessResponse(
          {
            list: [],
            total: 0,
            page: Number(page),
            pageSize: Number(pageSize),
            totalPages: 0
          },
          '获取订单列表成功'
        )
      );
    }

    // 查询列表数据 - 最简单的查询测试
    const offset = (Number(page) - 1) * Number(pageSize);

    // 如果没有搜索条件，使用优化的关联查询
    if (whereConditions.length === 0) {
      const simpleQuery = `
        SELECT
          o.order_id,
          o.order_no,
          u.username,
          p.name as product_name,
          pr.name as provider_name,
          o.platform_account,
          o.platform_password,
          o.school_name,
          o.course_name,
          o.quantity,
          o.amount,
          o.status,
          o.progress,
          o.upstream_order_id,
          o.process_status,
          o.remark,
          o.create_time,
          o.update_time
        FROM fd_order o
        LEFT JOIN fd_user u ON o.user_id = u.user_id
        LEFT JOIN fd_product p ON o.product_id = p.cid
        LEFT JOIN fd_provider pr ON o.provider_id = pr.provider_id
        ORDER BY o.create_time DESC
        LIMIT ${Number(pageSize)} OFFSET ${offset}
      `;

      const listResult = await executeQuery(simpleQuery, []);

      const totalPages = Math.ceil(total / Number(pageSize));

      return res.json(
        createSuccessResponse(
          {
            list: listResult,
            total,
            page: Number(page),
            pageSize: Number(pageSize),
            totalPages
          },
          '获取订单列表成功'
        )
      );
    }

    // 有搜索条件时使用完整查询
    const listQuery = `
      SELECT
        o.order_id,
        o.order_no,
        u.username,
        p.name as product_name,
        pr.name as provider_name,
        o.platform_account,
        o.platform_password,
        o.school_name,
        o.course_name,
        o.quantity,
        o.amount,
        o.status,
        o.progress,
        o.upstream_order_id,
        o.process_status,
        o.remark,
        o.create_time,
        o.update_time
      FROM fd_order o
      LEFT JOIN fd_user u ON o.user_id = u.user_id
      LEFT JOIN fd_product p ON o.product_id = p.cid
      LEFT JOIN fd_provider pr ON o.provider_id = pr.provider_id
      ${whereClause}
      ORDER BY o.create_time DESC
      LIMIT ? OFFSET ?
    `;

    const listResult = await executeQuery(listQuery, [...queryParams, Number(pageSize), offset]);

    const totalPages = Math.ceil(total / Number(pageSize));

    return res.json(
      createSuccessResponse(
        {
          list: listResult,
          total,
          page: Number(page),
          pageSize: Number(pageSize),
          totalPages
        },
        '获取订单列表成功'
      )
    );
  } catch {
    return res.json(createErrorResponse('获取订单列表失败', ResponseCode.ERROR));
  }
}

/** 获取订单详情 */
export async function getOrderDetail(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { orderId } = req.params;
    const userId = req.user?.userId;
    const isAdmin = req.user?.roles?.includes('admin');

    if (!orderId) {
      return res.json(createErrorResponse('订单ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 查询订单详情
    let orderQuery = `
      SELECT
        o.*,
        u.username,
        u.nickname,
        p.name as providerName,
        p.api_url as providerApiUrl,
        pi.name as itemName,
        'network_course' as platformType,
        c.name as categoryName
      FROM fd_order o
      LEFT JOIN fd_user u ON o.user_id = u.user_id
      LEFT JOIN fd_provider p ON o.provider_id = p.provider_id
      LEFT JOIN fd_product pi ON o.product_id = pi.product_id
      LEFT JOIN fd_category c ON o.category_id = c.category_id
      WHERE o.order_id = ?
    `;

    const queryParams = [orderId];

    // 权限控制：普通用户只能查看自己的订单
    if (!isAdmin) {
      orderQuery += ' AND o.user_id = ?';
      queryParams.push(String(userId));
    }

    const orderResult = await executeQuery(orderQuery, queryParams);

    if (orderResult.length === 0) {
      return res.json(createErrorResponse('订单不存在或无权限访问', ResponseCode.PARAM_ERROR));
    }

    const order = orderResult[0];

    // 查询订单日志
    const logQuery = `
      SELECT
        log_id,
        action as operation_type,
        content as operation_desc,
        operator_id,
        operator_type as operator_name,
        data_before as before_data,
        data_after as after_data,
        result as result_status,
        error_message,
        create_time
      FROM fd_log
      WHERE target_type = 'order' AND target_id = ?
      ORDER BY create_time DESC
    `;

    const logs = await executeQuery(logQuery, [orderId]);

    // 处理订单数据
    const processedOrder = {
      ...order,
      statusText: ORDER_STATUS_TEXT[order.status as keyof typeof ORDER_STATUS_TEXT] || '未知状态',
      createTime: new Date(order.create_time).toLocaleString('zh-CN'),
      updateTime: new Date(order.update_time).toLocaleString('zh-CN'),
      completionTime: order.completion_time ? new Date(order.completion_time).toLocaleString('zh-CN') : null,
      payTime: order.pay_time ? new Date(order.pay_time).toLocaleString('zh-CN') : null,
      logs: logs.map((log: any) => ({
        ...log,
        createTime: new Date(log.create_time).toLocaleString('zh-CN'),
        beforeData: log.before_data ? JSON.parse(log.before_data) : null,
        afterData: log.after_data ? JSON.parse(log.after_data) : null
      }))
    };

    return res.json(createSuccessResponse(processedOrder, '获取订单详情成功'));
  } catch {
    return res.json(createErrorResponse('获取订单详情失败', ResponseCode.ERROR));
  }
}

// 创建订单相关的辅助函数

/** 验证创建订单的参数 */
function validateCreateOrderParams(params: any) {
  const { productId, platformAccount, platformPassword, schoolName, selectedCourses } = params;

  if (!productId || !platformAccount || !platformPassword || !schoolName) {
    return '商品ID、平台账号、平台密码和学校名称不能为空';
  }

  if (!selectedCourses || !Array.isArray(selectedCourses) || selectedCourses.length === 0) {
    return '请选择要代刷的课程';
  }

  return null;
}

/** 获取商品信息 */
async function getProductInfo(productId: number) {
  const productQuery = `
    SELECT
      p.cid as product_id,
      p.name as product_name,
      p.fenlei as category_id,
      p.queryplat as provider_id,
      p.price,
      p.price as cost_price,
      p.getnoun,
      p.noun,
      p.docking,
      p.status,
      cat.name as categoryName,
      pr.name as providerName,
      pr.api_url as providerApiUrl,
      pr.status as providerStatus
    FROM fd_product p
    LEFT JOIN fd_category cat ON p.fenlei = cat.category_id
    LEFT JOIN fd_provider pr ON p.queryplat = pr.provider_id
    WHERE p.cid = ? AND p.status = 1
  `;

  const productResult = await executeQuery(productQuery, [productId]);
  return productResult.length > 0 ? productResult[0] : null;
}

/** 获取用户信息 */
async function getUserInfo(userId: number) {
  const userQuery = `
    SELECT user_id, username, balance, price_rate, status
    FROM fd_user
    WHERE user_id = ? AND status = 1
  `;

  const userResult = await executeQuery(userQuery, [userId]);
  return userResult.length > 0 ? userResult[0] : null;
}

/** 标准化课程数据 */
function normalizeCourses(selectedCourses: any[]) {
  return selectedCourses.map((course, index) => {
    if (!course.courseName && !course.kcname) {
      throw new Error(`第${index + 1}个课程缺少课程名称`);
    }

    const courseId = course.courseId || course.kcid || '';
    const courseName = course.courseName || course.kcname || '';

    return {
      courseId,
      courseName,
      kcid: courseId,
      kcname: courseName,
      platform: course.platform || 'network_course',
      status: course.status || 'pending',
      progress: course.progress || 0,
      createTime: new Date().toISOString(),
      hasId: Boolean(courseId)
    };
  });
}

/** 准备课程数据用于存储 */
function prepareCourseData(normalizedCourses: any[], platformAccount: string, schoolName: string) {
  const courseCount = normalizedCourses.length;
  const courseIds = normalizedCourses.map(c => c.courseId || '').join(',');

  let courseName: string;
  if (normalizedCourses.length === 1) {
    courseName = normalizedCourses[0].courseName;
  } else {
    courseName = `${normalizedCourses[0].courseName} 等${normalizedCourses.length}门课程`;
  }

  const courseInfo = {
    courses: normalizedCourses,
    totalCount: courseCount,
    platformAccount,
    schoolName,
    createTime: new Date().toISOString()
  };

  return { courseCount, courseIds, courseName, courseInfo };
}

/** 创建订单记录 */
async function createOrderRecord(orderData: any) {
  const insertOrderQuery = `
    INSERT INTO fd_order (
      order_no, user_id, provider_id, product_id, item_id, category_id,
      platform_account, platform_password, school_name,
      course_ids, course_name, course_info, quantity, unit_price, amount, cost_amount,
      status, progress, client_ip, remark, create_time, update_time
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
  `;

  const insertResult = await executeQuery(insertOrderQuery, [
    orderData.orderNo,
    orderData.userId,
    orderData.providerId,
    orderData.productId,
    null, // item_id 设为 NULL
    orderData.categoryId,
    orderData.platformAccount,
    orderData.platformPassword,
    orderData.schoolName,
    orderData.courseIds,
    orderData.courseName,
    JSON.stringify(orderData.courseInfo),
    orderData.courseCount,
    orderData.unitPrice,
    orderData.amount,
    orderData.costAmount,
    OrderStatus.PENDING_PROCESS,
    0,
    orderData.clientIp,
    orderData.remark
  ]);

  return insertResult.insertId;
}

/** 提交课程到服务商 */
async function submitCoursesToProvider(params: {
  normalizedCourses: any[];
  product: any;
  platformAccount: string;
  platformPassword: string;
  schoolName: string;
  orderId: number;
}) {
  const { normalizedCourses, product, platformAccount, platformPassword, schoolName, orderId } = params;
  const submitResults: any[] = [];
  let successCount = 0;
  let failedCount = 0;

  // 使用 Promise.allSettled 并行处理所有课程提交
  const submitPromises = normalizedCourses.map(async course => {
    try {
      // 使用统一货源配置系统创建订单
      const submitResult = await ProviderManager.createOrder(product.provider_id, {
        school: schoolName,
        username: platformAccount,
        password: platformPassword,
        course_id: course.kcid || null,
        course_name: course.kcname,
        product: {
          noun: product.noun,
          getnoun: product.getnoun,
          name: product.product_name,
          platform_cid: product.noun || product.getnoun
        }
      });

      if (submitResult.success) {
        // 如果29平台下单成功，更新订单的 upstream_order_id
        if (submitResult.data?.upstream_order_id) {
          await executeQuery('UPDATE fd_order SET upstream_order_id = ?, update_time = NOW() WHERE order_id = ?', [
            submitResult.data.upstream_order_id,
            orderId
          ]);
        }

        return {
          courseId: course.courseId,
          courseName: course.courseName,
          kcid: course.kcid,
          kcname: course.kcname,
          success: true,
          upstreamOrderId: submitResult.data?.upstream_order_id,
          message: '提交成功'
        };
      }

      return {
        courseId: course.courseId,
        courseName: course.courseName,
        kcid: course.kcid,
        kcname: course.kcname,
        success: false,
        message: submitResult.message
      };
    } catch (error: any) {
      return {
        courseId: course.courseId,
        courseName: course.courseName,
        kcid: course.kcid,
        kcname: course.kcname,
        success: false,
        message: error.message || '提交异常'
      };
    }
  });

  const settledResults = await Promise.allSettled(submitPromises);

  settledResults.forEach(result => {
    if (result.status === 'fulfilled') {
      submitResults.push(result.value);
      if (result.value.success) {
        successCount += 1;
      } else {
        failedCount += 1;
      }
    } else {
      failedCount += 1;
      submitResults.push({
        success: false,
        message: '提交处理异常'
      });
    }
  });

  return { submitResults, successCount, failedCount };
}

/** 创建订单（基于商品和选中课程） */
export async function createOrder(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { productId, platformAccount, platformPassword, schoolName, selectedCourses, remark } = req.body;

    const userId = req.user?.userId;
    if (!userId) {
      return res.json(createErrorResponse('用户未登录', ResponseCode.UNAUTHORIZED));
    }

    // 参数验证
    const validationError = validateCreateOrderParams(req.body);
    if (validationError) {
      return res.json(createErrorResponse(validationError, ResponseCode.PARAM_ERROR));
    }

    // 用户功能限制校验：禁止下单
    try {
      const [limitationRow] = await executeQuery('SELECT limitations FROM fd_user_limitations WHERE user_id = ?', [
        userId
      ]);
      const limits = limitationRow?.limitations ? JSON.parse(limitationRow.limitations) : {};
      if (limits?.disable_order === true || limits?.disable_place_order === true) {
        return res.json(createErrorResponse('已启用下单限制，无法创建订单', ResponseCode.FORBIDDEN));
      }
    } catch (e) {
      // 读取失败不影响主流程
    }

    // 查询商品信息
    const product = await getProductInfo(productId);
    if (!product) {
      return res.json(createErrorResponse('商品不存在或已下架', ResponseCode.PARAM_ERROR));
    }

    // 检查服务商状态
    if (!product.provider_id || product.providerStatus !== 1) {
      return res.json(createErrorResponse('商品服务商不可用', ResponseCode.PARAM_ERROR));
    }

    // 查询用户信息和费率
    const user = await getUserInfo(userId);
    if (!user) {
      return res.json(createErrorResponse('用户不存在或已禁用', ResponseCode.PARAM_ERROR));
    }

    // 验证和标准化课程数据
    const normalizedCourses = normalizeCourses(selectedCourses);

    // 计算总金额
    const unitPrice = Number(product.price) * Number(user.price_rate);
    const totalAmount = unitPrice * normalizedCourses.length;
    const totalCostAmount = Number(product.cost_price) * normalizedCourses.length;

    // 检查用户余额
    if (Number(user.balance) < totalAmount) {
      return res.json(createErrorResponse('余额不足，请先充值', ResponseCode.PARAM_ERROR));
    }

    // 获取客户端IP
    const clientIp = req.ip || req.socket.remoteAddress || '';

    // 为每门课程创建独立订单
    const createdOrders = [];
    const orderPromises = normalizedCourses.map(async course => {
      const orderNo = generateOrderNo();

      // 单门课程的课程信息
      const singleCourseInfo = {
        courses: [course],
        createTime: new Date().toISOString(),
        schoolName,
        totalCount: 1,
        platformAccount
      };

      const orderId = await createOrderRecord({
        orderNo,
        userId,
        providerId: product.provider_id,
        productId,
        categoryId: product.category_id,
        platformAccount,
        platformPassword,
        schoolName,
        courseIds: course.kcid?.toString() || '',
        courseName: course.kcname || '',
        courseInfo: singleCourseInfo,
        courseCount: 1,
        unitPrice,
        amount: unitPrice,
        costAmount: Number(product.cost_price),
        clientIp,
        remark
      });

      return { orderId, orderNo, course };
    });

    // 等待所有订单创建完成
    const orderResults = await Promise.all(orderPromises);
    createdOrders.push(...orderResults);

    // 扣除用户余额
    await executeQuery('UPDATE fd_user SET balance = balance - ? WHERE user_id = ?', [totalAmount, userId]);

    // 为每个订单独立提交到服务商并更新状态
    const submitPromises = createdOrders.map(async ({ orderId, orderNo, course }) => {
      try {
        // 记录操作日志
        await logOrderOperation(Number(orderId), {
          operationType: 'create',
          operationDesc: '创建订单',
          operatorId: userId,
          operatorName: user.username,
          beforeData: null,
          afterData: {
            orderNo,
            productId,
            productName: product.name,
            course,
            amount: unitPrice,
            platformAccount,
            schoolName
          }
        });

        // 提交单门课程到服务商
        const submitResult = await ProviderManager.createOrder(product.provider_id, {
          school: schoolName,
          username: platformAccount,
          password: platformPassword,
          course_id: course.kcid?.toString() || '',
          course_name: course.kcname || '',
          product: {
            platform_cid: product.noun || product.getnoun || '23',
            noun: product.noun || product.getnoun || '23',
            name: product.name
          }
        });

        if (submitResult.success && submitResult.data?.upstream_order_id) {
          // 提交成功，更新订单状态和上游订单ID
          await executeQuery(
            'UPDATE fd_order SET status = ?, upstream_order_id = ?, extra_data = ?, update_time = NOW() WHERE order_id = ?',
            [
              OrderStatus.PROCESSING,
              submitResult.data.upstream_order_id,
              JSON.stringify({ submitResult: submitResult.data }),
              orderId
            ]
          );

          return { orderId, orderNo, success: true, upstreamOrderId: submitResult.data.upstream_order_id };
        }
        // 提交失败
        await executeQuery('UPDATE fd_order SET status = ?, remark = ?, update_time = NOW() WHERE order_id = ?', [
          OrderStatus.FAILED,
          submitResult.message || '提交失败',
          orderId
        ]);

        return { orderId, orderNo, success: false, error: submitResult.message };
      } catch (error: any) {
        // 异常处理
        await executeQuery('UPDATE fd_order SET status = ?, remark = ?, update_time = NOW() WHERE order_id = ?', [
          OrderStatus.FAILED,
          `提交异常: ${error.message}`,
          orderId
        ]);

        return { orderId, orderNo, success: false, error: error.message };
      }
    });

    // 等待所有提交完成
    const submitResults = await Promise.all(submitPromises);
    const successCount = submitResults.filter(r => r.success).length;
    const failedCount = submitResults.filter(r => !r.success).length;

    // 如果全部失败，退还用户余额
    if (successCount === 0) {
      await executeQuery('UPDATE fd_user SET balance = balance + ? WHERE user_id = ?', [totalAmount, userId]);

      const failureReasons = submitResults
        .filter(result => !result.success)
        .map(result => result.error)
        .filter((message, index, array) => array.indexOf(message) === index);

      const errorMessage =
        failureReasons.length === 1
          ? `所有订单提交失败：${failureReasons[0]}，已退款`
          : `所有订单提交失败：${failureReasons.join('；')}，已退款`;

      return res.json(
        createErrorResponse(errorMessage, ResponseCode.ERROR, {
          orders: submitResults,
          totalOrders: createdOrders.length,
          successCount: 0,
          failedCount
        })
      );
    }

    // 构建成功响应
    let responseMessage = '';
    if (successCount === normalizedCourses.length) {
      responseMessage = `订单创建成功！共创建${successCount}个订单，所有课程已提交`;
    } else if (successCount > 0) {
      responseMessage = `订单部分成功！共创建${createdOrders.length}个订单，${successCount}个成功，${failedCount}个失败`;
    }

    const responseData = {
      totalOrders: createdOrders.length,
      successCount,
      failedCount,
      totalAmount,
      orders: submitResults,
      successOrders: submitResults.filter(r => r.success),
      failedOrders: submitResults.filter(r => !r.success)
    };

    return res.json(createSuccessResponse(responseData, responseMessage));
  } catch {
    return res.json(createErrorResponse('创建订单失败', ResponseCode.ERROR));
  }
}

/** 更新订单状态 */
export async function updateOrderStatus(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { orderId } = req.params;
    const { status, progress, processStatus, loginStatus, remark } = req.body;

    const userId = req.user?.userId;
    const username = req.user?.userName;
    const isAdmin = req.user?.roles?.includes('admin');

    if (!userId || !username) {
      return res.json(createErrorResponse('用户未登录', ResponseCode.UNAUTHORIZED));
    }

    if (!orderId) {
      return res.json(createErrorResponse('订单ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 查询订单信息
    let orderQuery = 'SELECT * FROM fd_order WHERE order_id = ?';
    const queryParams = [orderId];

    // 权限控制：普通用户只能操作自己的订单
    if (!isAdmin) {
      orderQuery += ' AND user_id = ?';
      queryParams.push(String(userId));
    }

    const orderResult = await executeQuery(orderQuery, queryParams);

    if (orderResult.length === 0) {
      return res.json(createErrorResponse('订单不存在或无权限操作', ResponseCode.PARAM_ERROR));
    }

    const order = orderResult[0];
    const beforeData = { ...order };

    // 构建更新字段
    const updateFields: string[] = [];
    const updateValues: any[] = [];

    if (status !== undefined) {
      updateFields.push('status = ?');
      updateValues.push(status);

      // 如果状态为已完成，设置完成时间
      if (Number(status) === OrderStatus.COMPLETED) {
        updateFields.push('completion_time = NOW()');
        updateFields.push('progress = 100');
      }
    }

    if (progress !== undefined) {
      updateFields.push('progress = ?');
      updateValues.push(progress);
    }

    if (processStatus !== undefined) {
      updateFields.push('process_status = ?');
      updateValues.push(processStatus);
    }

    if (loginStatus !== undefined) {
      updateFields.push('login_status = ?');
      updateValues.push(loginStatus);
    }

    if (remark !== undefined) {
      updateFields.push('remark = ?');
      updateValues.push(remark);
    }

    if (updateFields.length === 0) {
      return res.json(createErrorResponse('没有需要更新的字段', ResponseCode.PARAM_ERROR));
    }

    updateFields.push('update_time = NOW()');
    updateValues.push(orderId);

    // 更新订单
    await executeQuery(`UPDATE fd_order SET ${updateFields.join(', ')} WHERE order_id = ?`, updateValues);

    // 查询更新后的数据
    const updatedOrder = await executeQuery('SELECT * FROM fd_order WHERE order_id = ?', [orderId]);
    const afterData = updatedOrder[0];

    // 记录操作日志
    await logOrderOperation(Number(orderId), {
      operationType: 'update',
      operationDesc: '更新订单状态',
      operatorId: userId,
      operatorName: username,
      beforeData,
      afterData
    });

    return res.json(createSuccessResponse(null, '订单状态更新成功'));
  } catch {
    return res.json(createErrorResponse('更新订单状态失败', ResponseCode.ERROR));
  }
}

/** 取消订单 */
export async function cancelOrder(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { orderId } = req.params;
    const { reason } = req.body;

    const userId = req.user?.userId;
    const username = req.user?.userName;
    const isAdmin = req.user?.roles?.includes('admin');

    if (!userId || !username) {
      return res.json(createErrorResponse('用户未登录', ResponseCode.UNAUTHORIZED));
    }

    if (!orderId) {
      return res.json(createErrorResponse('订单ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 查询订单信息
    let orderQuery = 'SELECT * FROM fd_order WHERE order_id = ?';
    const queryParams = [orderId];

    // 权限控制：普通用户只能取消自己的订单
    if (!isAdmin) {
      orderQuery += ' AND user_id = ?';
      queryParams.push(String(userId));
    }

    const orderResult = await executeQuery(orderQuery, queryParams);

    if (orderResult.length === 0) {
      return res.json(createErrorResponse('订单不存在或无权限操作', ResponseCode.PARAM_ERROR));
    }

    const order = orderResult[0];

    // 检查订单状态是否可以取消
    if (order.status === OrderStatus.COMPLETED) {
      return res.json(createErrorResponse('已完成的订单无法取消', ResponseCode.PARAM_ERROR));
    }

    if (order.status === OrderStatus.CANCELLED) {
      return res.json(createErrorResponse('订单已取消', ResponseCode.PARAM_ERROR));
    }

    const beforeData = { ...order };

    // 更新订单状态为已取消
    await executeQuery('UPDATE fd_order SET status = ?, remark = ?, update_time = NOW() WHERE order_id = ?', [
      OrderStatus.CANCELLED,
      reason || '用户取消',
      orderId
    ]);

    // 退还用户余额
    await executeQuery('UPDATE fd_user SET balance = balance + ? WHERE user_id = ?', [order.amount, order.user_id]);

    // 记录操作日志
    await logOrderOperation(Number(orderId), {
      operationType: 'cancel',
      operationDesc: '取消订单',
      operatorId: userId,
      operatorName: username,
      beforeData,
      afterData: {
        status: OrderStatus.CANCELLED,
        reason
      }
    });

    return res.json(createSuccessResponse(null, '订单取消成功，余额已退还'));
  } catch {
    return res.json(createErrorResponse('取消订单失败', ResponseCode.ERROR));
  }
}

/** 获取订单统计信息 */
export async function getOrderStats(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const userId = req.user?.userId;
    const isAdmin = req.user?.roles?.includes('admin');

    let whereCondition = '1=1';
    const queryParams: any[] = [];

    // 权限控制：普通用户只能查看自己的统计
    if (!isAdmin) {
      whereCondition = 'user_id = ?';
      queryParams.push(String(userId));
    }

    // 总订单数
    const totalOrdersResult = await executeQuery(
      `SELECT COUNT(*) as count FROM fd_order WHERE ${whereCondition}`,
      queryParams
    );
    const totalOrders = totalOrdersResult[0].count;

    // 各状态订单数
    const statusStatsResult = await executeQuery(
      `
      SELECT
        status,
        COUNT(*) as count
      FROM fd_order
      WHERE ${whereCondition}
      GROUP BY status
    `,
      queryParams
    );

    // 今日订单数
    const todayOrdersResult = await executeQuery(
      `SELECT COUNT(*) as count FROM fd_order WHERE DATE(create_time) = CURDATE() AND ${whereCondition}`,
      queryParams
    );
    const todayOrders = todayOrdersResult[0].count;

    // 订单金额统计
    const amountStatsResult = await executeQuery(
      `
      SELECT
        SUM(amount) as totalAmount,
        AVG(amount) as avgAmount,
        MAX(amount) as maxAmount,
        MIN(amount) as minAmount
      FROM fd_order
      WHERE status != ${OrderStatus.CANCELLED} AND ${whereCondition}
    `,
      queryParams
    );

    // 处理状态统计数据
    const statusStats = statusStatsResult.reduce((acc: any, item: any) => {
      acc[item.status] = {
        count: item.count,
        text: ORDER_STATUS_TEXT[item.status as keyof typeof ORDER_STATUS_TEXT] || '未知状态'
      };
      return acc;
    }, {});

    const stats = {
      totalOrders,
      todayOrders,
      statusStats,
      amountStats: amountStatsResult[0] || {
        totalAmount: 0,
        avgAmount: 0,
        maxAmount: 0,
        minAmount: 0
      }
    };

    return res.json(createSuccessResponse(stats, '获取订单统计信息成功'));
  } catch {
    return res.json(createErrorResponse('获取统计信息失败', ResponseCode.ERROR));
  }
}

// ===== 工具函数 =====

/** 生成订单号 */
function generateOrderNo(): string {
  const now = new Date();
  const year = now.getFullYear().toString().slice(-2);
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  const day = now.getDate().toString().padStart(2, '0');
  const hour = now.getHours().toString().padStart(2, '0');
  const minute = now.getMinutes().toString().padStart(2, '0');
  const second = now.getSeconds().toString().padStart(2, '0');
  const random = Math.floor(Math.random() * 1000)
    .toString()
    .padStart(3, '0');

  return `ORD${year}${month}${day}${hour}${minute}${second}${random}`;
}

/** 记录订单操作日志 - 使用统一日志记录模块 */
async function logOrderOperation(
  orderId: number,
  logData: {
    operationType: string;
    operationDesc: string;
    operatorId: number;
    operatorName: string;
    beforeData?: any;
    afterData?: any;
  }
): Promise<any> {
  try {
    // 使用统一的日志记录模块
    await logOrderOp(
      logData.operationType,
      orderId,
      logData.beforeData,
      logData.afterData,
      logData.operatorId,
      'admin'
    );
  } catch {
    // 日志记录失败不应该影响主要业务流程
  }
}

// ===== 订单状态跟踪和数据分析接口 =====

// 批量同步订单状态相关的辅助函数

/** 获取订单信息 */
async function getOrderForSync(orderId: string) {
  const orderQuery = `
    SELECT o.*, p.provider_id, p.name as provider_name
    FROM fd_order o
    LEFT JOIN fd_provider p ON o.provider_id = p.provider_id
    WHERE o.order_id = ?
  `;

  const orderResult = await executeQuery(orderQuery, [orderId]);
  return orderResult.length > 0 ? orderResult[0] : null;
}

/** 映射服务商状态到本地状态 */
function mapProviderStatusToOrderStatus(providerStatus: string): OrderStatus {
  switch (providerStatus) {
    case 'completed':
      return OrderStatus.COMPLETED;
    case 'processing':
      return OrderStatus.PROCESSING;
    case 'failed':
      return OrderStatus.FAILED;
    default:
      return OrderStatus.PROCESSING; // 默认状态
  }
}

/** 更新订单状态到数据库 */
async function updateOrderInDatabase(orderId: string, order: any, updateData: any) {
  const newStatus = updateData.status ? mapProviderStatusToOrderStatus(updateData.status) : order.status;

  // 计算新的进度值
  let newProgress = order.progress;
  if (updateData.progress !== undefined) {
    newProgress = updateData.progress;
  } else if (newStatus === OrderStatus.COMPLETED) {
    newProgress = 100;
  }

  await executeQuery(
    `UPDATE fd_order
     SET status = ?, progress = ?, process_status = ?, login_status = ?, update_time = NOW()
     WHERE order_id = ?`,
    [
      newStatus,
      newProgress,
      updateData.process_status || order.process_status,
      updateData.login_status || order.login_status,
      orderId
    ]
  );

  return { newStatus, newProgress };
}

/** 同步单个订单状态 */
export async function syncSingleOrderStatus(orderId: string) {
  try {
    // 获取订单信息
    const order = await getOrderForSync(orderId);
    if (!order) {
      return {
        orderId,
        success: false,
        message: '订单不存在'
      };
    }

    // 检查订单是否可以同步
    if (!order.provider_id || !order.upstream_order_id) {
      return {
        orderId,
        success: false,
        message: '订单未提交到服务商'
      };
    }

    // 调用服务商同步接口
    const syncResult = await ProviderManager.syncOrderStatus(order.provider_id, {
      upstream_order_id: order.upstream_order_id,
      username: order.platform_account,
      school: order.school_name
    });

    if (syncResult.success && syncResult.data) {
      // 更新订单状态到数据库
      await updateOrderInDatabase(orderId, order, syncResult.data);

      return {
        orderId,
        success: true,
        message: '同步成功',
        data: syncResult.data
      };
    }

    return {
      orderId,
      success: false,
      message: syncResult.message || '同步失败'
    };
  } catch (error) {
    return {
      orderId,
      success: false,
      message: error instanceof Error ? error.message : '同步异常'
    };
  }
}

/** 批量同步订单状态 */
export async function batchSyncOrderStatus(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { orderIds } = req.body;

    if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
      return res.json(createErrorResponse('订单ID列表不能为空', ResponseCode.PARAM_ERROR));
    }

    // 使用 Promise.allSettled 并行处理所有订单同步
    const syncPromises = orderIds.map(orderId => syncSingleOrderStatus(orderId));
    const settledResults = await Promise.allSettled(syncPromises);

    // 处理结果
    const results = settledResults.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      }
      return {
        orderId: orderIds[index],
        success: false,
        message: '同步处理异常'
      };
    });

    const successCount = results.filter(r => r.success).length;

    return res.json(
      createSuccessResponse(
        {
          results,
          summary: {
            total: orderIds.length,
            success: successCount,
            failed: orderIds.length - successCount
          }
        },
        '批量同步订单状态完成'
      )
    );
  } catch {
    return res.json(createErrorResponse('批量同步订单状态失败', ResponseCode.ERROR));
  }
}

// ===== 自动同步管理接口 =====
// 暂时注释，避免启动问题

/** 获取同步配置 */
export async function getSyncConfig(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    // 动态导入避免循环依赖
    const { SyncConfigManager } = await import('../service/sync/SyncConfigManager');
    const configManager = SyncConfigManager.getInstance();
    await configManager.loadConfig();
    const config = configManager.getConfig();

    return res.json(createSuccessResponse(config, '获取同步配置成功'));
  } catch (error) {
    console.error('获取同步配置失败:', error);
    return res.json(createErrorResponse('获取同步配置失败', ResponseCode.ERROR));
  }
}

/** 更新同步配置 */
export async function updateSyncConfig(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { SyncConfigManager } = await import('../service/sync/SyncConfigManager');
    const configManager = SyncConfigManager.getInstance();
    const { config } = req.body;

    if (!config) {
      return res.json(createErrorResponse('配置数据不能为空', ResponseCode.PARAM_ERROR));
    }

    // 验证配置
    configManager.updateConfig(config);
    const validation = configManager.validateConfig();

    if (!validation.valid) {
      return res.json(createErrorResponse(`配置验证失败: ${validation.errors.join(', ')}`, ResponseCode.PARAM_ERROR));
    }

    return res.json(createSuccessResponse(null, '同步配置更新成功'));
  } catch (error) {
    console.error('更新同步配置失败:', error);
    return res.json(createErrorResponse('更新同步配置失败', ResponseCode.ERROR));
  }
}

/** 启动自动同步 */
export async function startAutoSync(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { SyncScheduler } = await import('../service/sync/SyncScheduler');
    const scheduler = SyncScheduler.getInstance();
    await scheduler.start();

    return res.json(createSuccessResponse(null, '自动同步已启动'));
  } catch (error) {
    console.error('启动自动同步失败:', error);
    return res.json(createErrorResponse('启动自动同步失败', ResponseCode.ERROR));
  }
}

/** 停止自动同步 */
export async function stopAutoSync(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { SyncScheduler } = await import('../service/sync/SyncScheduler');
    const scheduler = SyncScheduler.getInstance();
    scheduler.stop();

    return res.json(createSuccessResponse(null, '自动同步已停止'));
  } catch (error) {
    console.error('停止自动同步失败:', error);
    return res.json(createErrorResponse('停止自动同步失败', ResponseCode.ERROR));
  }
}

/** 获取同步状态和指标 */
export async function getSyncStatus(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { SyncScheduler } = await import('../service/sync/SyncScheduler');
    const { SyncMonitor } = await import('../service/sync/SyncMonitor');

    const scheduler = SyncScheduler.getInstance();
    const monitor = SyncMonitor.getInstance();

    const metrics = scheduler.getMetrics();
    const queueStatus = scheduler.getQueueStatus();
    const realtimeStats = monitor.getRealTimeStats(60); // 最近1小时
    const healthStatus = monitor.getHealthStatus();

    return res.json(
      createSuccessResponse(
        {
          metrics,
          queueStatus,
          realtimeStats,
          healthStatus,
          isRunning: scheduler.getIsRunning()
        },
        '获取同步状态成功'
      )
    );
  } catch (error) {
    console.error('获取同步状态失败:', error);
    return res.json(createErrorResponse('获取同步状态失败', ResponseCode.ERROR));
  }
}

/** 获取同步历史统计 */
export async function getSyncHistory(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { SyncMonitor } = await import('../service/sync/SyncMonitor');
    const { days = 7 } = req.query;
    const monitor = SyncMonitor.getInstance();

    const history = await monitor.getHistoricalStats(Number(days));

    return res.json(createSuccessResponse(history, '获取同步历史成功'));
  } catch (error) {
    console.error('获取同步历史失败:', error);
    return res.json(createErrorResponse('获取同步历史失败', ResponseCode.ERROR));
  }
}

/** 手动触发同步任务 */
export async function triggerManualSync(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { SyncScheduler } = await import('../service/sync/SyncScheduler');
    const { SyncMonitor } = await import('../service/sync/SyncMonitor');

    const { orderIds, priority = 8 } = req.body;

    if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
      return res.json(createErrorResponse('订单ID列表不能为空', ResponseCode.PARAM_ERROR));
    }

    const scheduler = SyncScheduler.getInstance();
    const monitor = SyncMonitor.getInstance();

    // 添加到同步队列
    orderIds.forEach((orderId: string) => {
      scheduler.addTask(orderId, priority, 'manual');
      monitor.recordEvent({
        type: 'start',
        orderId,
        strategy: 'manual'
      });
    });

    return res.json(
      createSuccessResponse(
        {
          addedCount: orderIds.length,
          queueSize: scheduler.getQueueStatus().total
        },
        '手动同步任务已添加到队列'
      )
    );
  } catch (error) {
    console.error('触发手动同步失败:', error);
    return res.json(createErrorResponse('触发手动同步失败', ResponseCode.ERROR));
  }
}

/** 管理同步策略 */
export async function manageSyncStrategy(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { SyncConfigManager } = await import('../service/sync/SyncConfigManager');
    const { action, strategyId, strategy } = req.body;
    const configManager = SyncConfigManager.getInstance();

    switch (action) {
      case 'add':
        if (!strategy) {
          return res.json(createErrorResponse('策略数据不能为空', ResponseCode.PARAM_ERROR));
        }
        configManager.addStrategy(strategy);
        break;

      case 'update':
        if (!strategyId || !strategy) {
          return res.json(createErrorResponse('策略ID和数据不能为空', ResponseCode.PARAM_ERROR));
        }
        configManager.updateStrategy(strategyId, strategy);
        break;

      case 'remove':
        if (!strategyId) {
          return res.json(createErrorResponse('策略ID不能为空', ResponseCode.PARAM_ERROR));
        }
        configManager.removeStrategy(strategyId);
        break;

      default:
        return res.json(createErrorResponse('无效的操作类型', ResponseCode.PARAM_ERROR));
    }

    return res.json(createSuccessResponse(null, '策略操作成功'));
  } catch (error) {
    console.error('管理同步策略失败:', error);
    return res.json(createErrorResponse('管理同步策略失败', ResponseCode.ERROR));
  }
}

/** 获取实时事件流 */
export async function getSyncEvents(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { SyncMonitor } = await import('../service/sync/SyncMonitor');
    const { limit = 100 } = req.query;
    const monitor = SyncMonitor.getInstance();

    const events = monitor.getEventStream(Number(limit));

    return res.json(createSuccessResponse(events, '获取同步事件成功'));
  } catch (error) {
    console.error('获取同步事件失败:', error);
    return res.json(createErrorResponse('获取同步事件失败', ResponseCode.ERROR));
  }
}

/** 获取订单统计数据 */
export async function getOrderStatistics(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const userId = req.user?.userId;
    const isAdmin = req.user?.roles?.includes('admin');

    // 构建查询条件
    let whereClause = '1=1';
    const queryParams: any[] = [];

    if (!isAdmin) {
      whereClause += ' AND user_id = ?';
      queryParams.push(userId);
    }

    // 总订单数
    const totalQuery = `SELECT COUNT(*) as total FROM fd_order WHERE ${whereClause}`;
    const totalResult = await executeQuery(totalQuery, queryParams);
    const totalOrders = totalResult[0].total;

    // 按状态统计
    const statusQuery = `
      SELECT
        status,
        COUNT(*) as count,
        SUM(amount) as total_amount
      FROM fd_order
      WHERE ${whereClause}
      GROUP BY status
    `;
    const statusResult = await executeQuery(statusQuery, queryParams);

    // 今日订单统计
    const todayQuery = `
      SELECT
        COUNT(*) as count,
        SUM(amount) as total_amount
      FROM fd_order
      WHERE ${whereClause} AND DATE(create_time) = CURDATE()
    `;
    const todayResult = await executeQuery(todayQuery, queryParams);

    // 本月订单统计
    const monthQuery = `
      SELECT
        COUNT(*) as count,
        SUM(amount) as total_amount
      FROM fd_order
      WHERE ${whereClause} AND YEAR(create_time) = YEAR(NOW()) AND MONTH(create_time) = MONTH(NOW())
    `;
    const monthResult = await executeQuery(monthQuery, queryParams);

    // 最近7天订单趋势
    const trendQuery = `
      SELECT
        DATE(create_time) as date,
        COUNT(*) as count,
        SUM(amount) as total_amount
      FROM fd_order
      WHERE ${whereClause} AND create_time >= DATE_SUB(NOW(), INTERVAL 7 DAY)
      GROUP BY DATE(create_time)
      ORDER BY date ASC
    `;
    const trendResult = await executeQuery(trendQuery, queryParams);

    const statistics = {
      total: {
        orders: totalOrders,
        amount: 0
      },
      today: {
        orders: todayResult[0].count || 0,
        amount: todayResult[0].total_amount || 0
      },
      month: {
        orders: monthResult[0].count || 0,
        amount: monthResult[0].total_amount || 0
      },
      statusBreakdown: statusResult.map((item: any) => ({
        status: item.status,
        statusText: ORDER_STATUS_TEXT[item.status as keyof typeof ORDER_STATUS_TEXT] || '未知状态',
        count: item.count,
        amount: item.total_amount || 0
      })),
      trend: trendResult.map((item: any) => ({
        date: item.date,
        count: item.count,
        amount: item.total_amount || 0
      }))
    };

    // 计算总金额
    statistics.total.amount = statusResult.reduce((sum: number, item: any) => sum + (item.total_amount || 0), 0);

    return res.json(createSuccessResponse(statistics, '获取订单统计数据成功'));
  } catch {
    return res.json(createErrorResponse('获取订单统计数据失败', ResponseCode.ERROR));
  }
}

/** 查课接口（29平台） */
export async function queryCourses(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId, platformType, platformAccount, platformPassword, schoolName, courseId } = req.body;

    // 参数验证
    if (!providerId || !platformType || !platformAccount || !platformPassword || !schoolName) {
      return res.json(
        createErrorResponse('服务商ID、平台类型、平台账号、平台密码和学校名称不能为空', ResponseCode.PARAM_ERROR)
      );
    }

    // 调用29平台查课接口
    const result = await ProviderManager.queryCourses(providerId, {
      school: schoolName,
      username: platformAccount,
      password: platformPassword,
      course_id: courseId
    });

    if (result.success) {
      return res.json(createSuccessResponse(result.data, '查课成功'));
    }
    return res.json(createErrorResponse(`查课失败: ${result.message}`, ResponseCode.ERROR));
  } catch {
    return res.json(createErrorResponse('查课失败: 系统异常', ResponseCode.ERROR));
  }
}

/** 补刷订单接口（29平台） */
export async function refillOrder(req: AuthenticatedRequest, res: Response): Promise<any> {
  console.log('🚀 [补刷订单] 接口被调用，订单ID:', req.params.orderId);
  try {
    const { orderId } = req.params;
    const userId = req.user?.userId;
    const isAdmin = req.user?.roles?.includes('admin');

    if (!orderId) {
      return res.json(createErrorResponse('订单ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 查询订单信息
    let orderQuery = `
      SELECT o.*, p.provider_id, p.name as providerName
      FROM fd_order o
      LEFT JOIN fd_provider p ON o.provider_id = p.provider_id
      WHERE o.order_id = ?
    `;

    const queryParams = [orderId];

    // 权限控制：普通用户只能操作自己的订单
    if (!isAdmin) {
      orderQuery += ' AND o.user_id = ?';
      queryParams.push(String(userId));
    }

    const orderResult = await executeQuery(orderQuery, queryParams);

    if (orderResult.length === 0) {
      return res.json(createErrorResponse('订单不存在或无权限访问', ResponseCode.PARAM_ERROR));
    }

    const order = orderResult[0];

    if (!order.upstream_order_id) {
      return res.json(createErrorResponse('订单未提交到服务商，无法补刷', ResponseCode.PARAM_ERROR));
    }

    // 调用29平台补刷接口
    const result = await ProviderManager.refillOrder(order.provider_id, {
      upstream_order_id: order.upstream_order_id
    });

    // 记录操作日志
    await logOrderOp(
      'refill',
      orderId,
      null,
      {
        upstreamOrderId: order.upstream_order_id,
        result: result.success ? 'success' : 'failed'
      },
      userId,
      req.user?.roles?.includes('admin') ? 'admin' : 'user'
    );

    if (result.success) {
      return res.json(createSuccessResponse(null, result.message || '补刷成功'));
    }

    return res.json(createErrorResponse(result.message || '补刷失败', ResponseCode.ERROR));
  } catch {
    return res.json(createErrorResponse('补刷失败: 系统异常', ResponseCode.ERROR));
  }
}

/** 同步订单状态接口（29平台） */
export async function syncOrderStatus(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { orderId } = req.params;
    const userId = req.user?.userId;
    const isAdmin = req.user?.roles?.includes('admin');

    if (!orderId) {
      return res.json(createErrorResponse('订单ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 查询订单信息
    let orderQuery = `
      SELECT o.*, p.provider_id, p.name as providerName
      FROM fd_order o
      LEFT JOIN fd_provider p ON o.provider_id = p.provider_id
      WHERE o.order_id = ?
    `;

    const queryParams = [orderId];

    // 权限控制：普通用户只能操作自己的订单
    if (!isAdmin) {
      orderQuery += ' AND o.user_id = ?';
      queryParams.push(String(userId));
    }

    const orderResult = await executeQuery(orderQuery, queryParams);

    if (orderResult.length === 0) {
      return res.json(createErrorResponse('订单不存在或无权限访问', ResponseCode.PARAM_ERROR));
    }

    const order = orderResult[0];

    if (!order.upstream_order_id) {
      return res.json(createErrorResponse('订单未提交到服务商，无法同步状态', ResponseCode.PARAM_ERROR));
    }

    console.log('🔄 [同步订单] 开始同步:', {
      orderId,
      providerId: order.provider_id,
      upstreamOrderId: order.upstream_order_id,
      username: order.platform_account,
      school: order.school_name,
      providerName: order.providerName
    });

    // 调用29平台同步接口
    const result = await ProviderManager.syncOrderStatus(order.provider_id, {
      upstream_order_id: order.upstream_order_id,
      username: order.platform_account,
      school: order.school_name
    });

    console.log('🔄 [同步订单] 源台返回结果:', {
      success: result.success,
      message: result.message,
      data: result.data,
      rawData: result.rawData,
      error: result.error,
      rawResponse: result
    });

    if (result.success) {
      // 检查是否有数据返回
      if (!result.data || (Array.isArray(result.data) && result.data.length === 0)) {
        // 没有数据但同步成功，说明订单不存在或已完成
        return res.json(
          createSuccessResponse(
            {
              orderId: order.order_id,
              orderNo: order.order_no,
              syncTime: new Date().toISOString(),
              message: '同步成功，但未找到订单数据（订单可能已完成或不存在）',
              // 返回29平台的原始响应数据
              providerData: result.data,
              rawSyncData: result.data
            },
            '同步成功'
          )
        );
      }

      // 29平台返回的是订单数组，需要找到匹配的订单
      let orderData: any;

      if (Array.isArray(result.data)) {
        // 在返回的订单数组中查找匹配的订单
        orderData = result.data.find(
          (item: any) => item.order_id === order.upstream_order_id || item.id === order.upstream_order_id
        );

        // 如果没找到匹配的订单，取第一个（兼容处理）
        if (!orderData && result.data.length > 0) {
          orderData = result.data[0];
        }
      } else if (typeof result.data === 'object') {
        orderData = result.data;
      }

      if (orderData) {
        // 处理29平台的同步响应格式
        const statusText = orderData.process_status || orderData.status || '';
        const progressText = orderData.progress_text || orderData.process || '0';
        const remarks = orderData.remark || orderData.remarks || '';

        // 映射状态到本地状态码
        const newStatus = mapProviderStatusToLocal(statusText);

        // 提取进度百分比数字
        const progressMatch = progressText.match(/(\d+(?:\.\d+)?)/);
        const progress = progressMatch ? Math.round(Number.parseFloat(progressMatch[1])) : 0;

        // 更新订单状态、进度和备注信息
        await executeQuery(
          'UPDATE fd_order SET status = ?, progress = ?, remark = ?, process_status = ?, login_status = ?, update_time = NOW() WHERE order_id = ?',
          [newStatus, progress, remarks, statusText, 'success', orderId]
        );

        // 记录操作日志
        await logOrderOp(
          'sync',
          orderId,
          { status: order.status, progress: order.progress },
          {
            status: newStatus,
            progress,
            providerData: orderData,
            rawSyncData: result.rawData
          },
          userId,
          req.user?.roles?.includes('admin') ? 'admin' : 'user'
        );

        return res.json(
          createSuccessResponse(
            {
              orderId: order.order_id,
              orderNo: order.order_no,
              localStatus: newStatus,
              localProgress: progress,
              syncTime: new Date().toISOString(),
              // 返回29平台的完整原始数据
              providerData: orderData,
              // 同时提供格式化后的数据供前端使用
              formattedData: {
                platformName: orderData.platform_name,
                courseName: orderData.course_name,
                studentName: orderData.student_name,
                courseStartTime: orderData.course_start_time,
                courseEndTime: orderData.course_end_time,
                examStartTime: orderData.exam_start_time,
                examEndTime: orderData.exam_end_time,
                status: orderData.status,
                progress: orderData.progress,
                remarks: orderData.remarks
              },
              // 原始同步响应数据
              rawSyncData: result.rawData || result.data
            },
            '同步成功'
          )
        );
      }
    }

    return res.json(createErrorResponse(`同步失败: ${result.message}`, ResponseCode.ERROR));
  } catch {
    return res.json(createErrorResponse('同步失败: 系统异常', ResponseCode.ERROR));
  }
}

/** 修改密码接口（29平台） */
export async function changePassword(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { orderId } = req.params;
    const { new_password, confirm_password } = req.body;
    const userId = req.user?.userId;
    const isAdmin = req.user?.roles?.includes('admin');

    if (!new_password || !confirm_password) {
      return res.json(createErrorResponse('新密码和确认密码不能为空', ResponseCode.PARAM_ERROR));
    }

    if (new_password !== confirm_password) {
      return res.json(createErrorResponse('两次输入的密码不一致', ResponseCode.PARAM_ERROR));
    }

    if (!orderId || !new_password) {
      return res.json(createErrorResponse('订单ID和新密码不能为空', ResponseCode.PARAM_ERROR));
    }

    // 查询订单信息
    let orderQuery = `
      SELECT o.*, p.provider_id, p.name as providerName
      FROM fd_order o
      LEFT JOIN fd_provider p ON o.provider_id = p.provider_id
      WHERE o.order_id = ?
    `;

    const queryParams = [orderId];

    // 权限控制：普通用户只能操作自己的订单
    if (!isAdmin) {
      orderQuery += ' AND o.user_id = ?';
      queryParams.push(String(userId));
    }

    const orderResult = await executeQuery(orderQuery, queryParams);

    if (orderResult.length === 0) {
      return res.json(createErrorResponse('订单不存在或无权限访问', ResponseCode.PARAM_ERROR));
    }

    const order = orderResult[0];

    if (!order.upstream_order_id) {
      return res.json(createErrorResponse('订单未提交到服务商，无法修改密码', ResponseCode.PARAM_ERROR));
    }

    // 调用29平台改密接口
    const result = await ProviderManager.changePassword(order.provider_id, {
      upstream_order_id: order.upstream_order_id,
      new_password
    });

    // 记录操作日志
    await logOrderOp(
      'change_password',
      orderId,
      null,
      {
        upstreamOrderId: order.upstream_order_id,
        result
      },
      userId,
      req.user?.roles?.includes('admin') ? 'admin' : 'user'
    );

    if (result.success) {
      // 更新本地订单的密码
      await executeQuery('UPDATE fd_order SET platform_password = ?, update_time = NOW() WHERE order_id = ?', [
        new_password,
        orderId
      ]);

      return res.json(createSuccessResponse(result.data, '密码修改成功'));
    }
    return res.json(createErrorResponse(`密码修改失败: ${result.message}`, ResponseCode.ERROR));
  } catch {
    return res.json(createErrorResponse('密码修改失败: 系统异常', ResponseCode.ERROR));
  }
}

/** 订单反馈接口 */
export async function feedbackOrder(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { orderId } = req.params;
    const { feedback_type, feedback_content, attachments } = req.body;
    const userId = req.user?.userId;
    const isAdmin = req.user?.roles?.includes('admin');

    if (!feedback_type || !feedback_content) {
      return res.json(createErrorResponse('反馈类型和内容不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查订单是否存在
    const orderQuery = `
      SELECT order_id, order_no, user_id, status
      FROM fd_order
      WHERE order_id = ?
    `;
    const orderResult = await executeQuery(orderQuery, [orderId]);

    if (orderResult.length === 0) {
      return res.json(createErrorResponse('订单不存在', ResponseCode.DATA_NOT_FOUND));
    }

    const order = orderResult[0];

    // 权限检查：只有订单所有者或管理员可以反馈
    if (!isAdmin && order.user_id !== userId) {
      return res.json(createErrorResponse('无权限操作此订单', ResponseCode.PERMISSION_DENIED));
    }

    // 插入反馈记录
    const insertQuery = `
      INSERT INTO fd_order_feedback (
        order_id, user_id, feedback_type, feedback_content,
        attachments, status, create_time
      ) VALUES (?, ?, ?, ?, ?, 'pending', NOW())
    `;

    const result = await executeQuery(insertQuery, [
      orderId,
      userId,
      feedback_type,
      feedback_content,
      JSON.stringify(attachments || [])
    ]);

    // 记录操作日志
    await logOrderOperation(Number(orderId), {
      operationType: 'feedback',
      operationDesc: '提交订单反馈',
      operatorId: userId || 0,
      operatorName: req.user?.userName || 'system',
      beforeData: null,
      afterData: {
        feedback_type,
        feedback_content,
        feedback_id: result.insertId
      }
    });

    return res.json(
      createSuccessResponse(
        {
          feedback_id: result.insertId,
          message: '反馈已提交，我们会尽快处理'
        },
        '反馈提交成功'
      )
    );
  } catch {
    return res.json(createErrorResponse('反馈提交失败: 系统异常', ResponseCode.ERROR));
  }
}

/** 获取服务商课程列表（29平台） */
export async function getProviderCourses(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;

    if (!providerId) {
      return res.json(createErrorResponse('服务商ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 调用29平台获取课程列表接口
    const result = await ProviderManager.getCourseList(Number(providerId));

    if (result.success) {
      return res.json(createSuccessResponse(result.data, '获取课程列表成功'));
    }
    return res.json(createErrorResponse(`获取课程列表失败: ${result.message}`, ResponseCode.ERROR));
  } catch {
    return res.json(createErrorResponse('获取课程列表失败: 系统异常', ResponseCode.ERROR));
  }
}

/**
 * 将服务商状态映射到本地状态
 */
function mapProviderStatusToLocal(providerStatus: string): number {
  // 根据29平台的状态文本映射到本地状态
  switch (providerStatus) {
    case '已完成':
    case '完成':
      return OrderStatus.COMPLETED;
    case '处理中':
    case '进行中':
      return OrderStatus.PROCESSING;
    case '失败':
    case '错误':
      return OrderStatus.FAILED;
    case '已取消':
      return OrderStatus.CANCELLED;
    default:
      return OrderStatus.PROCESSING; // 默认为处理中
  }
}

/** 获取指定用户的订单列表 */
export async function getUserOrders(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { userId } = req.params;
    const { limit = 10, offset = 0, status } = req.query;

    if (!userId) {
      return res.json(createErrorResponse('用户ID不能为空', ResponseCode.PARAM_ERROR));
    }

    console.log('📋 [订单管理] 获取用户订单列表:', { userId, limit, offset, status });

    // 构建查询条件
    let whereClause = 'WHERE o.user_id = ?';
    const queryParams = [userId];

    if (status) {
      whereClause += ' AND o.status = ?';
      queryParams.push(status as string);
    }

    // 查询订单列表
    const ordersQuery = `
      SELECT
        o.order_id as orderId,
        o.order_no as orderNo,
        o.amount,
        o.status,
        o.platform_account as platformAccount,
        o.course_name as courseName,
        o.create_time as createTime,
        o.update_time as updateTime,
        p.name as productName,
        pr.name as providerName
      FROM fd_order o
      LEFT JOIN fd_product p ON o.product_id = p.cid
      LEFT JOIN fd_provider pr ON o.provider_id = pr.provider_id
      ${whereClause}
      ORDER BY o.create_time DESC
      LIMIT ?
    `;

    queryParams.push(limit as string);
    const orders = await executeQuery(ordersQuery, queryParams);

    // 查询总数
    const countQuery = `SELECT COUNT(*) as total FROM fd_order o ${whereClause}`;
    const countParams = queryParams.slice(0, -1); // 移除 LIMIT 参数
    const countResult = await executeQuery(countQuery, countParams);
    const total = countResult[0]?.total || 0;

    return res.json(
      createSuccessResponse(
        {
          list: orders,
          total,
          limit: Number(limit),
          offset: Number(offset)
        },
        '获取用户订单成功'
      )
    );
  } catch (error: any) {
    console.error('📋 [订单管理] 获取用户订单失败:', error);
    return res.json(createErrorResponse(error.message || '获取用户订单失败', ResponseCode.ERROR));
  }
}
