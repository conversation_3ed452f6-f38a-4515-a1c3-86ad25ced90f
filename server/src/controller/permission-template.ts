import type { Response } from 'express';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import type { AuthenticatedRequest } from '../middleware/auth';

/** 获取权限模板列表 */
export async function getPermissionTemplates(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { category, keyword } = req.query;

    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    if (category) {
      whereClause += ' AND pt.template_category = ?';
      params.push(category);
    }

    if (keyword) {
      whereClause += ' AND (pt.template_name LIKE ? OR pt.template_description LIKE ?)';
      params.push(`%${keyword}%`, `%${keyword}%`);
    }

    const query = `
      SELECT 
        pt.template_id,
        pt.template_name,
        pt.template_description,
        pt.template_category,
        pt.is_system,
        pt.is_default,
        pt.sort_order,
        pt.create_time,
        pt.update_time,
        COUNT(ptp.permission_id) as permission_count
      FROM fd_permission_template pt
      LEFT JOIN fd_permission_template_permission ptp ON pt.template_id = ptp.template_id
      ${whereClause}
      GROUP BY pt.template_id
      ORDER BY pt.sort_order ASC, pt.create_time DESC
    `;

    const templates = await executeQuery(query, params);

    return res.json(createSuccessResponse(templates, '获取权限模板列表成功'));
  } catch (error) {
    console.error('🔐 [权限模板] 获取模板列表失败:', error);
    return res.json(createErrorResponse('获取权限模板列表失败', ResponseCode.ERROR));
  }
}

/** 获取单个权限模板 */
export async function getPermissionTemplateById(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const templateId = Number.parseInt(req.params.templateId);

    if (!templateId) {
      return res.json(createErrorResponse('模板ID不能为空', ResponseCode.PARAM_ERROR));
    }

    const query = `
      SELECT 
        pt.template_id,
        pt.template_name,
        pt.template_description,
        pt.template_category,
        pt.is_system,
        pt.is_default,
        pt.sort_order,
        pt.create_time,
        pt.update_time,
        COUNT(ptp.permission_id) as permission_count
      FROM fd_permission_template pt
      LEFT JOIN fd_permission_template_permission ptp ON pt.template_id = ptp.template_id
      WHERE pt.template_id = ?
      GROUP BY pt.template_id
    `;

    const [template] = await executeQuery(query, [templateId]);

    if (!template) {
      return res.json(createErrorResponse('权限模板不存在', ResponseCode.NOT_FOUND));
    }

    return res.json(createSuccessResponse(template, '获取权限模板成功'));
  } catch (error) {
    console.error('🔐 [权限模板] 获取模板失败:', error);
    return res.json(createErrorResponse('获取权限模板失败', ResponseCode.ERROR));
  }
}

/** 获取模板权限 */
export async function getPermissionTemplatePermissions(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const templateId = Number.parseInt(req.params.templateId);

    if (!templateId) {
      return res.json(createErrorResponse('模板ID不能为空', ResponseCode.PARAM_ERROR));
    }

    const query = `
      SELECT 
        p.permission_id,
        p.permission_code,
        p.permission_name,
        p.permission_description,
        p.permission_type,
        p.permission_group
      FROM fd_permission_template_permission ptp
      JOIN fd_permission p ON ptp.permission_id = p.permission_id
      WHERE ptp.template_id = ? AND p.status = 1
      ORDER BY p.permission_group ASC, p.sort_order ASC
    `;

    const permissions = await executeQuery(query, [templateId]);

    return res.json(createSuccessResponse(permissions, '获取模板权限成功'));
  } catch (error) {
    console.error('🔐 [权限模板] 获取模板权限失败:', error);
    return res.json(createErrorResponse('获取模板权限失败', ResponseCode.ERROR));
  }
}

/** 创建权限模板 */
export async function createPermissionTemplate(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { template_name, template_description, template_category, permission_ids, is_default, sort_order } = req.body;

    if (!template_name) {
      return res.json(createErrorResponse('模板名称不能为空', ResponseCode.PARAM_ERROR));
    }

    if (!permission_ids || permission_ids.length === 0) {
      return res.json(createErrorResponse('请选择至少一个权限', ResponseCode.PARAM_ERROR));
    }

    // 检查模板名称是否已存在
    const existQuery = 'SELECT template_id FROM fd_permission_template WHERE template_name = ?';
    const [existTemplate] = await executeQuery(existQuery, [template_name]);

    if (existTemplate) {
      return res.json(createErrorResponse('模板名称已存在', ResponseCode.PARAM_ERROR));
    }

    // 创建模板
    const insertQuery = `
      INSERT INTO fd_permission_template (
        template_name,
        template_description,
        template_category,
        is_system,
        is_default,
        sort_order,
        create_time,
        update_time
      ) VALUES (?, ?, ?, 0, ?, ?, NOW(), NOW())
    `;

    const result = await executeQuery(insertQuery, [
      template_name,
      template_description || '',
      template_category || 'custom',
      is_default ? 1 : 0,
      sort_order || 0
    ]);

    const templateId = (result as any).insertId;

    // 添加模板权限
    if (permission_ids.length > 0) {
      const permissionValues = permission_ids
        .map((permissionId: number) => `(${templateId}, ${permissionId}, NOW())`)
        .join(',');

      const permissionQuery = `
        INSERT INTO fd_permission_template_permission (template_id, permission_id, create_time)
        VALUES ${permissionValues}
      `;

      await executeQuery(permissionQuery);
    }

    return res.json(createSuccessResponse({ template_id: templateId }, '权限模板创建成功'));
  } catch (error) {
    console.error('🔐 [权限模板] 创建模板失败:', error);
    return res.json(createErrorResponse('创建权限模板失败', ResponseCode.ERROR));
  }
}

/** 更新权限模板 */
export async function updatePermissionTemplate(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const templateId = Number.parseInt(req.params.templateId);
    const { template_name, template_description, template_category, permission_ids, is_default, sort_order } = req.body;

    if (!templateId) {
      return res.json(createErrorResponse('模板ID不能为空', ResponseCode.PARAM_ERROR));
    }

    if (!template_name) {
      return res.json(createErrorResponse('模板名称不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查模板是否存在
    const existQuery = 'SELECT template_id, is_system FROM fd_permission_template WHERE template_id = ?';
    const [existTemplate] = await executeQuery(existQuery, [templateId]);

    if (!existTemplate) {
      return res.json(createErrorResponse('权限模板不存在', ResponseCode.NOT_FOUND));
    }

    if (existTemplate.is_system) {
      return res.json(createErrorResponse('系统模板不允许修改', ResponseCode.PERMISSION_DENIED));
    }

    // 检查模板名称是否已被其他模板使用
    const nameQuery = 'SELECT template_id FROM fd_permission_template WHERE template_name = ? AND template_id != ?';
    const [nameTemplate] = await executeQuery(nameQuery, [template_name, templateId]);

    if (nameTemplate) {
      return res.json(createErrorResponse('模板名称已存在', ResponseCode.PARAM_ERROR));
    }

    // 更新模板基本信息
    const updateQuery = `
      UPDATE fd_permission_template SET
        template_name = ?,
        template_description = ?,
        template_category = ?,
        is_default = ?,
        sort_order = ?,
        update_time = NOW()
      WHERE template_id = ?
    `;

    await executeQuery(updateQuery, [
      template_name,
      template_description || '',
      template_category || 'custom',
      is_default ? 1 : 0,
      sort_order || 0,
      templateId
    ]);

    // 更新模板权限
    if (permission_ids && Array.isArray(permission_ids)) {
      // 删除现有权限
      await executeQuery('DELETE FROM fd_permission_template_permission WHERE template_id = ?', [templateId]);

      // 添加新权限
      if (permission_ids.length > 0) {
        const permissionValues = permission_ids
          .map((permissionId: number) => `(${templateId}, ${permissionId}, NOW())`)
          .join(',');

        const permissionQuery = `
          INSERT INTO fd_permission_template_permission (template_id, permission_id, create_time)
          VALUES ${permissionValues}
        `;

        await executeQuery(permissionQuery);
      }
    }

    return res.json(createSuccessResponse(null, '权限模板更新成功'));
  } catch (error) {
    console.error('🔐 [权限模板] 更新模板失败:', error);
    return res.json(createErrorResponse('更新权限模板失败', ResponseCode.ERROR));
  }
}

/** 删除权限模板 */
export async function deletePermissionTemplate(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const templateId = Number.parseInt(req.params.templateId);

    if (!templateId) {
      return res.json(createErrorResponse('模板ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查模板是否存在
    const existQuery = 'SELECT template_id, is_system FROM fd_permission_template WHERE template_id = ?';
    const [existTemplate] = await executeQuery(existQuery, [templateId]);

    if (!existTemplate) {
      return res.json(createErrorResponse('权限模板不存在', ResponseCode.NOT_FOUND));
    }

    if (existTemplate.is_system) {
      return res.json(createErrorResponse('系统模板不允许删除', ResponseCode.PERMISSION_DENIED));
    }

    // 删除模板权限关联
    await executeQuery('DELETE FROM fd_permission_template_permission WHERE template_id = ?', [templateId]);

    // 删除模板
    await executeQuery('DELETE FROM fd_permission_template WHERE template_id = ?', [templateId]);

    return res.json(createSuccessResponse(null, '权限模板删除成功'));
  } catch (error) {
    console.error('🔐 [权限模板] 删除模板失败:', error);
    return res.json(createErrorResponse('删除权限模板失败', ResponseCode.ERROR));
  }
}

/** 应用权限模板 */
export async function applyPermissionTemplate(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { user_id, template_id, apply_type = 'add' } = req.body;

    if (!user_id || !template_id) {
      return res.json(createErrorResponse('用户ID和模板ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查用户是否存在
    const userQuery = 'SELECT user_id FROM fd_user WHERE user_id = ?';
    const [user] = await executeQuery(userQuery, [user_id]);

    if (!user) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.NOT_FOUND));
    }

    // 检查模板是否存在
    const templateQuery = 'SELECT template_id FROM fd_permission_template WHERE template_id = ?';
    const [template] = await executeQuery(templateQuery, [template_id]);

    if (!template) {
      return res.json(createErrorResponse('权限模板不存在', ResponseCode.NOT_FOUND));
    }

    // 获取模板权限
    const permissionQuery = `
      SELECT permission_id 
      FROM fd_permission_template_permission 
      WHERE template_id = ?
    `;
    const templatePermissions = await executeQuery(permissionQuery, [template_id]);

    if (templatePermissions.length === 0) {
      return res.json(createErrorResponse('模板中没有权限', ResponseCode.PARAM_ERROR));
    }

    // 根据应用类型处理权限
    if (apply_type === 'replace') {
      // 替换模式：先清除用户所有直接权限，再添加模板权限
      await executeQuery('DELETE FROM fd_user_permission WHERE user_id = ?', [user_id]);
    }

    // 添加模板权限到用户
    for (const permission of templatePermissions as any[]) {
      // 检查权限是否已存在
      const existQuery = `
        SELECT id FROM fd_user_permission 
        WHERE user_id = ? AND permission_id = ?
      `;
      const [existPermission] = await executeQuery(existQuery, [user_id, permission.permission_id]);

      if (!existPermission) {
        // 添加权限
        const insertQuery = `
          INSERT INTO fd_user_permission (
            user_id, 
            permission_id, 
            grant_type, 
            data_scope,
            status,
            create_time
          ) VALUES (?, ?, 'grant', 'all', 1, NOW())
        `;
        await executeQuery(insertQuery, [user_id, permission.permission_id]);
      }
    }

    return res.json(createSuccessResponse(null, '权限模板应用成功'));
  } catch (error) {
    console.error('🔐 [权限模板] 应用模板失败:', error);
    return res.json(createErrorResponse('应用权限模板失败', ResponseCode.ERROR));
  }
}
