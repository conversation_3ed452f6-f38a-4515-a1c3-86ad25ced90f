import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import type { Response } from 'express';
import { Request } from 'express';
import * as XLSX from 'xlsx';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import type { AuthenticatedRequest } from '../middleware/auth';

/**
 * 报表管理控制器
 * 遵循项目规范，提供完整的报表生成和管理功能
 */

// 报表存储目录
const REPORTS_DIR = path.join(__dirname, '../reports');

/** 生成用户报表 */
export async function generateUserReport(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { startDate, endDate, format = 'excel' } = req.body;

    // 构建查询条件
    let whereCondition = '1=1';
    const queryParams: any[] = [];

    if (startDate) {
      whereCondition += ' AND create_time >= ?';
      queryParams.push(startDate);
    }

    if (endDate) {
      whereCondition += ' AND create_time <= ?';
      queryParams.push(endDate);
    }

    // 查询用户数据
    const userDataQuery = `
      SELECT 
        user_id as '用户ID',
        username as '用户名',
        user_role as '用户角色',
        balance as '账户余额',
        invitation_code as '邀请码',
        status as '状态',
        create_time as '注册时间',
        last_login_time as '最后登录时间'
      FROM fd_user
      WHERE ${whereCondition}
      ORDER BY create_time DESC
    `;

    const userData = await executeQuery(userDataQuery, queryParams);

    // 处理数据格式
    const processedData = userData.map((user: any) => ({
      ...user,
      用户角色: user['用户角色'] === 'admin' ? '管理员' : user['用户角色'] === 'user' ? '普通用户' : '超级管理员',
      状态: user['状态'] === 1 ? '正常' : '禁用',
      注册时间: new Date(user['注册时间']).toLocaleString('zh-CN'),
      最后登录时间: user['最后登录时间'] ? new Date(user['最后登录时间']).toLocaleString('zh-CN') : '从未登录'
    }));

    // 生成报表文件
    const reportData = await generateReportFile('用户报表', processedData, format);

    return res.json(createSuccessResponse(reportData, '用户报表生成成功'));
  } catch (error) {
    console.error('📊 [报表管理] 生成用户报表失败:', error);
    return res.json(createErrorResponse('生成用户报表失败', ResponseCode.ERROR));
  }
}

/** 生成订单报表 */
export async function generateOrderReport(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { startDate, endDate, status, format = 'excel' } = req.body;

    // 构建查询条件
    let whereCondition = '1=1';
    const queryParams: any[] = [];

    if (startDate) {
      whereCondition += ' AND o.create_time >= ?';
      queryParams.push(startDate);
    }

    if (endDate) {
      whereCondition += ' AND o.create_time <= ?';
      queryParams.push(endDate);
    }

    if (status) {
      whereCondition += ' AND o.status = ?';
      queryParams.push(status);
    }

    // 查询订单数据
    const orderDataQuery = `
      SELECT 
        o.order_id as '订单ID',
        o.order_no as '订单号',
        u.username as '用户名',
        o.course_name as '课程名称',
        o.amount as '订单金额',
        o.cost_amount as '成本金额',
        (o.amount - o.cost_amount) as '利润',
        CASE 
          WHEN o.status = 1 THEN '待支付'
          WHEN o.status = 2 THEN '待处理'
          WHEN o.status = 3 THEN '处理中'
          WHEN o.status = 4 THEN '已完成'
          WHEN o.status = 5 THEN '已取消'
          WHEN o.status = 6 THEN '处理失败'
          ELSE '未知状态'
        END as '订单状态',
        o.create_time as '创建时间',
        o.update_time as '更新时间'
      FROM fd_order o
      LEFT JOIN fd_user u ON o.user_id = u.user_id
      WHERE ${whereCondition}
      ORDER BY o.create_time DESC
    `;

    const orderData = await executeQuery(orderDataQuery, queryParams);

    // 处理数据格式
    const processedData = orderData.map((order: any) => ({
      ...order,
      创建时间: new Date(order['创建时间']).toLocaleString('zh-CN'),
      更新时间: order['更新时间'] ? new Date(order['更新时间']).toLocaleString('zh-CN') : '-'
    }));

    // 生成统计汇总
    const summary = {
      订单总数: orderData.length,
      总金额: orderData.reduce((sum: number, order: any) => sum + (order['订单金额'] || 0), 0),
      总成本: orderData.reduce((sum: number, order: any) => sum + (order['成本金额'] || 0), 0),
      总利润: orderData.reduce((sum: number, order: any) => sum + (order['利润'] || 0), 0)
    };

    // 生成报表文件
    const reportData = await generateReportFile('订单报表', processedData, format, summary);

    return res.json(createSuccessResponse(reportData, '订单报表生成成功'));
  } catch (error) {
    console.error('📊 [报表管理] 生成订单报表失败:', error);
    return res.json(createErrorResponse('生成订单报表失败', ResponseCode.ERROR));
  }
}

/** 生成财务报表 */
export async function generateFinanceReport(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { startDate, endDate, groupBy = 'day', format = 'excel' } = req.body;

    // 构建查询条件
    let whereCondition = 'status != 5'; // 排除已取消订单
    const queryParams: any[] = [];

    if (startDate) {
      whereCondition += ' AND create_time >= ?';
      queryParams.push(startDate);
    }

    if (endDate) {
      whereCondition += ' AND create_time <= ?';
      queryParams.push(endDate);
    }

    // 根据分组方式设置日期格式
    let dateFormat = '';
    let groupByField = '';
    switch (groupBy) {
      case 'day':
        dateFormat = 'DATE(create_time)';
        groupByField = '日期';
        break;
      case 'week':
        dateFormat = 'CONCAT(YEAR(create_time), "-W", WEEK(create_time))';
        groupByField = '周';
        break;
      case 'month':
        dateFormat = 'DATE_FORMAT(create_time, "%Y-%m")';
        groupByField = '月份';
        break;
      default:
        dateFormat = 'DATE(create_time)';
        groupByField = '日期';
    }

    // 查询财务数据
    const financeDataQuery = `
      SELECT 
        ${dateFormat} as '${groupByField}',
        COUNT(*) as '订单数量',
        SUM(amount) as '总收入',
        SUM(cost_amount) as '总成本',
        SUM(amount - cost_amount) as '总利润',
        AVG(amount) as '平均订单金额'
      FROM fd_order
      WHERE ${whereCondition}
      GROUP BY ${dateFormat}
      ORDER BY ${dateFormat} ASC
    `;

    const financeData = await executeQuery(financeDataQuery, queryParams);

    // 处理数据格式
    const processedData = financeData.map((item: any) => ({
      ...item,
      平均订单金额: Number.parseFloat(item['平均订单金额'] || 0).toFixed(2)
    }));

    // 生成统计汇总
    const summary = {
      统计周期: `${startDate || '开始'} 至 ${endDate || '现在'}`,
      总订单数: financeData.reduce((sum: number, item: any) => sum + (item['订单数量'] || 0), 0),
      总收入: financeData.reduce((sum: number, item: any) => sum + (item['总收入'] || 0), 0),
      总成本: financeData.reduce((sum: number, item: any) => sum + (item['总成本'] || 0), 0),
      总利润: financeData.reduce((sum: number, item: any) => sum + (item['总利润'] || 0), 0)
    };

    // 生成报表文件
    const reportData = await generateReportFile('财务报表', processedData, format, summary);

    return res.json(createSuccessResponse(reportData, '财务报表生成成功'));
  } catch (error) {
    console.error('📊 [报表管理] 生成财务报表失败:', error);
    return res.json(createErrorResponse('生成财务报表失败', ResponseCode.ERROR));
  }
}

/** 获取报表列表 */
export async function getReportList(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    // 确保报表目录存在
    await fs.mkdir(REPORTS_DIR, { recursive: true });

    // 读取报表文件列表
    const files = await fs.readdir(REPORTS_DIR);
    const reportFiles = files.filter(file => file.endsWith('.xlsx') || file.endsWith('.csv'));

    const reports = await Promise.all(
      reportFiles.map(async file => {
        const filePath = path.join(REPORTS_DIR, file);
        const stats = await fs.stat(filePath);

        return {
          fileName: file,
          fileSize: stats.size,
          fileSizeMB: (stats.size / 1024 / 1024).toFixed(2),
          createTime: stats.birthtime.toLocaleString('zh-CN'),
          modifyTime: stats.mtime.toLocaleString('zh-CN')
        };
      })
    );

    // 按创建时间倒序排列
    reports.sort((a, b) => new Date(b.createTime).getTime() - new Date(a.createTime).getTime());

    return res.json(createSuccessResponse(reports, '获取报表列表成功'));
  } catch (error) {
    console.error('📊 [报表管理] 获取报表列表失败:', error);
    return res.json(createErrorResponse('获取报表列表失败', ResponseCode.ERROR));
  }
}

/** 下载报表文件 */
export async function downloadReport(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { fileName } = req.params;

    if (!fileName) {
      return res.json(createErrorResponse('文件名不能为空', ResponseCode.PARAM_ERROR));
    }

    const filePath = path.join(REPORTS_DIR, fileName);

    // 检查文件是否存在
    try {
      await fs.access(filePath);
    } catch {
      return res.json(createErrorResponse('报表文件不存在', ResponseCode.PARAM_ERROR));
    }

    // 设置响应头
    res.setHeader('Content-Type', 'application/octet-stream');
    res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(fileName)}"`);

    // 发送文件
    const fileBuffer = await fs.readFile(filePath);
    res.send(fileBuffer);
  } catch (error) {
    console.error('📊 [报表管理] 下载报表失败:', error);
    return res.json(createErrorResponse('下载报表失败', ResponseCode.ERROR));
  }
}

/** 删除报表文件 */
export async function deleteReport(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { fileName } = req.params;

    if (!fileName) {
      return res.json(createErrorResponse('文件名不能为空', ResponseCode.PARAM_ERROR));
    }

    const filePath = path.join(REPORTS_DIR, fileName);

    // 删除文件
    try {
      await fs.unlink(filePath);
    } catch {
      return res.json(createErrorResponse('报表文件不存在', ResponseCode.PARAM_ERROR));
    }

    return res.json(createSuccessResponse(null, '报表删除成功'));
  } catch (error) {
    console.error('📊 [报表管理] 删除报表失败:', error);
    return res.json(createErrorResponse('删除报表失败', ResponseCode.ERROR));
  }
}

// ===== 工具函数 =====

/** 生成报表文件 */
async function generateReportFile(
  reportName: string,
  data: any[],
  format: string,
  summary?: any
): Promise<{ fileName: string; filePath: string; fileSize: number }> {
  // 确保报表目录存在
  await fs.mkdir(REPORTS_DIR, { recursive: true });

  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const fileName = `${reportName}_${timestamp}.${format === 'csv' ? 'csv' : 'xlsx'}`;
  const filePath = path.join(REPORTS_DIR, fileName);

  if (format === 'csv') {
    // 生成CSV文件
    const csvContent = generateCSV(data, summary);
    await fs.writeFile(filePath, csvContent, 'utf8');
  } else {
    // 生成Excel文件
    const workbook = generateExcel(reportName, data, summary);
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    await fs.writeFile(filePath, buffer);
  }

  // 获取文件大小
  const stats = await fs.stat(filePath);

  return {
    fileName,
    filePath,
    fileSize: stats.size
  };
}

/** 生成CSV内容 */
function generateCSV(data: any[], summary?: any): string {
  if (data.length === 0) return '';

  const headers = Object.keys(data[0]);
  let csvContent = `${headers.join(',')}\n`;

  // 添加数据行
  data.forEach(row => {
    const values = headers.map(header => {
      const value = row[header];
      // 处理包含逗号的值
      return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
    });
    csvContent += `${values.join(',')}\n`;
  });

  // 添加汇总信息
  if (summary) {
    csvContent += '\n汇总信息\n';
    Object.entries(summary).forEach(([key, value]) => {
      csvContent += `${key},${value}\n`;
    });
  }

  return csvContent;
}

/** 生成Excel工作簿 */
function generateExcel(reportName: string, data: any[], summary?: any): XLSX.WorkBook {
  const workbook = XLSX.utils.book_new();

  // 创建数据工作表
  const worksheet = XLSX.utils.json_to_sheet(data);
  XLSX.utils.book_append_sheet(workbook, worksheet, '数据');

  // 如果有汇总信息，创建汇总工作表
  if (summary) {
    const summaryData = Object.entries(summary).map(([key, value]) => ({
      项目: key,
      值: value
    }));
    const summaryWorksheet = XLSX.utils.json_to_sheet(summaryData);
    XLSX.utils.book_append_sheet(workbook, summaryWorksheet, '汇总');
  }

  return workbook;
}
