import * as fs from 'node:fs/promises';
import * as path from 'node:path';
import { spawn } from 'node:child_process';
import type { Response } from 'express';
import { Request } from 'express';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import type { AuthenticatedRequest } from '../middleware/auth';

/**
 * 数据备份管理控制器
 * 遵循项目规范，提供完整的数据备份管理功能
 */

// 备份配置
const BACKUP_DIR = path.join(__dirname, '../backups/database');
const MAX_BACKUP_FILES = 10;

/** 创建数据库备份 */
export async function createDatabaseBackup(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { description = '手动备份' } = req.body;
    const userId = req.user?.userId;
    const username = req.user?.userName;

    // 确保备份目录存在
    await fs.mkdir(BACKUP_DIR, { recursive: true });

    // 生成备份文件名
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupFileName = `backup_${timestamp}.sql`;
    const backupFilePath = path.join(BACKUP_DIR, backupFileName);

    // 数据库连接信息
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || '3306',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'newfd'
    };

    console.log(`💾 [备份管理] 开始创建数据库备份: ${backupFileName}`);

    // 执行mysqldump命令
    const success = await createMySQLDump(dbConfig, backupFilePath);

    if (!success) {
      return res.json(createErrorResponse('数据库备份失败', ResponseCode.ERROR));
    }

    // 获取备份文件大小
    const stats = await fs.stat(backupFilePath);
    const fileSize = stats.size;

    // 记录备份信息到数据库
    const insertResult = await executeQuery(
      `
      INSERT INTO fd_backup_record (
        backup_name, backup_type, file_path, file_size,
        description, creator_id, creator_name, status, create_time
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `,
      [backupFileName, 'database', backupFilePath, fileSize, description, userId, username, 'completed']
    );

    // 清理旧备份
    await cleanupOldBackups();

    console.log(`💾 [备份管理] 数据库备份创建成功: ${backupFileName} (${(fileSize / 1024 / 1024).toFixed(2)}MB)`);

    return res.json(
      createSuccessResponse(
        {
          backupId: insertResult.insertId,
          backupName: backupFileName,
          fileSize,
          fileSizeMB: (fileSize / 1024 / 1024).toFixed(2),
          createTime: new Date().toLocaleString('zh-CN')
        },
        '数据库备份创建成功'
      )
    );
  } catch (error) {
    console.error('💾 [备份管理] 创建数据库备份失败:', error);
    return res.json(createErrorResponse('创建数据库备份失败', ResponseCode.ERROR));
  }
}

/** 获取备份列表 */
export async function getBackupList(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { page = 1, pageSize = 20, backupType = '', status = '' } = req.query;

    const offset = (Number(page) - 1) * Number(pageSize);
    const whereConditions = ['1=1'];
    const queryParams: any[] = [];

    // 备份类型筛选
    if (backupType) {
      whereConditions.push('backup_type = ?');
      queryParams.push(backupType);
    }

    // 状态筛选
    if (status) {
      whereConditions.push('status = ?');
      queryParams.push(status);
    }

    const whereClause = whereConditions.join(' AND ');

    // 查询备份列表
    const backupListQuery = `
      SELECT
        backup_id as backupId,
        backup_name as backupName,
        backup_type as backupType,
        file_path as filePath,
        file_size as fileSize,
        description,
        creator_id as creatorId,
        creator_name as creatorName,
        status,
        create_time as createTime,
        restore_time as restoreTime
      FROM fd_backup_record
      WHERE ${whereClause}
      ORDER BY create_time DESC
      LIMIT ?
    `;

    queryParams.push(Number(pageSize));
    const backupList = await executeQuery(backupListQuery, queryParams);

    // 查询总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM fd_backup_record
      WHERE ${whereClause}
    `;

    const countParams = queryParams.slice(0, -2);
    const countResult = await executeQuery(countQuery, countParams);
    const total = countResult[0]?.total || 0;

    // 处理备份数据
    const processedBackupList = await Promise.all(
      backupList.map(async (backup: any) => {
        // 检查文件是否存在
        let fileExists = false;
        try {
          await fs.access(backup.filePath);
          fileExists = true;
        } catch {
          fileExists = false;
        }

        return {
          ...backup,
          createTime: new Date(backup.createTime).toLocaleString('zh-CN'),
          restoreTime: backup.restoreTime ? new Date(backup.restoreTime).toLocaleString('zh-CN') : null,
          fileSizeMB: (backup.fileSize / 1024 / 1024).toFixed(2),
          fileExists
        };
      })
    );

    return res.json(
      createSuccessResponse(
        {
          list: processedBackupList,
          pagination: {
            page: Number(page),
            pageSize: Number(pageSize),
            total,
            totalPages: Math.ceil(total / Number(pageSize))
          }
        },
        '获取备份列表成功'
      )
    );
  } catch (error) {
    console.error('💾 [备份管理] 获取备份列表失败:', error);
    return res.json(createErrorResponse('获取备份列表失败', ResponseCode.ERROR));
  }
}

/** 恢复数据库备份 */
export async function restoreDatabaseBackup(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { backupId } = req.params;
    const userId = req.user?.userId;
    const username = req.user?.userName;

    if (!backupId) {
      return res.json(createErrorResponse('备份ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 查询备份记录
    const backupQuery = `
      SELECT * FROM fd_backup_record
      WHERE backup_id = ? AND backup_type = 'database' AND status = 'completed'
    `;
    const backupResult = await executeQuery(backupQuery, [backupId]);

    if (backupResult.length === 0) {
      return res.json(createErrorResponse('备份记录不存在或状态异常', ResponseCode.PARAM_ERROR));
    }

    const backup = backupResult[0];

    // 检查备份文件是否存在
    try {
      await fs.access(backup.file_path);
    } catch {
      return res.json(createErrorResponse('备份文件不存在', ResponseCode.PARAM_ERROR));
    }

    // 数据库连接信息
    const dbConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: process.env.DB_PORT || '3306',
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      database: process.env.DB_NAME || 'newfd'
    };

    console.log(`💾 [备份管理] 开始恢复数据库备份: ${backup.backup_name}`);

    // 执行mysql恢复命令
    const success = await restoreMySQLDump(dbConfig, backup.file_path);

    if (!success) {
      return res.json(createErrorResponse('数据库恢复失败', ResponseCode.ERROR));
    }

    // 更新备份记录
    await executeQuery(
      `
      UPDATE fd_backup_record
      SET restore_time = NOW(), restore_by = ?, restore_by_name = ?
      WHERE backup_id = ?
    `,
      [userId, username, backupId]
    );

    console.log(`💾 [备份管理] 数据库恢复成功: ${backup.backup_name}`);

    return res.json(
      createSuccessResponse(
        {
          backupName: backup.backup_name,
          restoreTime: new Date().toLocaleString('zh-CN')
        },
        '数据库恢复成功'
      )
    );
  } catch (error) {
    console.error('💾 [备份管理] 恢复数据库备份失败:', error);
    return res.json(createErrorResponse('恢复数据库备份失败', ResponseCode.ERROR));
  }
}

/** 删除备份 */
export async function deleteBackup(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { backupId } = req.params;

    if (!backupId) {
      return res.json(createErrorResponse('备份ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 查询备份记录
    const backupQuery = 'SELECT * FROM fd_backup_record WHERE backup_id = ?';
    const backupResult = await executeQuery(backupQuery, [backupId]);

    if (backupResult.length === 0) {
      return res.json(createErrorResponse('备份记录不存在', ResponseCode.PARAM_ERROR));
    }

    const backup = backupResult[0];

    // 删除备份文件
    try {
      await fs.unlink(backup.file_path);
    } catch (error) {
      console.warn('💾 [备份管理] 备份文件删除失败（可能已不存在）:', error);
    }

    // 删除数据库记录
    await executeQuery('DELETE FROM fd_backup_record WHERE backup_id = ?', [backupId]);

    console.log(`💾 [备份管理] 备份删除成功: ${backup.backup_name}`);

    return res.json(createSuccessResponse(null, '备份删除成功'));
  } catch (error) {
    console.error('💾 [备份管理] 删除备份失败:', error);
    return res.json(createErrorResponse('删除备份失败', ResponseCode.ERROR));
  }
}

/** 获取备份统计信息 */
export async function getBackupStats(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    // 总备份数
    const totalBackupsResult = await executeQuery('SELECT COUNT(*) as count FROM fd_backup_record');
    const totalBackups = totalBackupsResult[0].count;

    // 成功备份数
    const successBackupsResult = await executeQuery(
      'SELECT COUNT(*) as count FROM fd_backup_record WHERE status = "completed"'
    );
    const successBackups = successBackupsResult[0].count;

    // 今日备份数
    const todayBackupsResult = await executeQuery(
      'SELECT COUNT(*) as count FROM fd_backup_record WHERE DATE(create_time) = CURDATE()'
    );
    const todayBackups = todayBackupsResult[0].count;

    // 备份总大小
    const totalSizeResult = await executeQuery(
      'SELECT SUM(file_size) as totalSize FROM fd_backup_record WHERE status = "completed"'
    );
    const totalSize = totalSizeResult[0].totalSize || 0;

    // 最近备份时间
    const lastBackupResult = await executeQuery(
      'SELECT create_time FROM fd_backup_record ORDER BY create_time DESC LIMIT 1'
    );
    const lastBackupTime =
      lastBackupResult.length > 0 ? new Date(lastBackupResult[0].create_time).toLocaleString('zh-CN') : null;

    // 备份类型统计
    const typeStatsResult = await executeQuery(`
      SELECT
        backup_type,
        COUNT(*) as count,
        SUM(file_size) as totalSize
      FROM fd_backup_record
      WHERE status = 'completed'
      GROUP BY backup_type
    `);

    const stats = {
      overview: {
        totalBackups,
        successBackups,
        todayBackups,
        totalSizeMB: (totalSize / 1024 / 1024).toFixed(2),
        lastBackupTime
      },
      typeStats: typeStatsResult.map((stat: any) => ({
        type: stat.backup_type,
        count: stat.count,
        totalSizeMB: (stat.totalSize / 1024 / 1024).toFixed(2)
      }))
    };

    return res.json(createSuccessResponse(stats, '获取备份统计成功'));
  } catch (error) {
    console.error('💾 [备份管理] 获取备份统计失败:', error);
    return res.json(createErrorResponse('获取备份统计失败', ResponseCode.ERROR));
  }
}

// ===== 工具函数 =====

/** 执行MySQL备份 */
async function createMySQLDump(dbConfig: any, outputPath: string): Promise<boolean> {
  return new Promise(resolve => {
    const args = [
      '-h',
      dbConfig.host,
      '-P',
      dbConfig.port,
      '-u',
      dbConfig.user,
      '--single-transaction',
      '--routines',
      '--triggers',
      '--hex-blob',
      '--default-character-set=utf8mb4',
      dbConfig.database
    ];

    if (dbConfig.password) {
      args.splice(4, 0, `-p${dbConfig.password}`);
    }

    const mysqldump = spawn('mysqldump', args);
    const writeStream = require('node:fs').createWriteStream(outputPath);

    mysqldump.stdout.pipe(writeStream);

    let errorOutput = '';
    mysqldump.stderr.on('data', data => {
      errorOutput += data.toString();
    });

    mysqldump.on('close', code => {
      writeStream.end();
      if (code === 0) {
        console.log('💾 [备份管理] mysqldump执行成功');
        resolve(true);
      } else {
        console.error('💾 [备份管理] mysqldump执行失败:', errorOutput);
        resolve(false);
      }
    });

    mysqldump.on('error', error => {
      console.error('💾 [备份管理] mysqldump进程错误:', error);
      writeStream.end();
      resolve(false);
    });
  });
}

/** 执行MySQL恢复 */
async function restoreMySQLDump(dbConfig: any, inputPath: string): Promise<boolean> {
  return new Promise(resolve => {
    const args = [
      '-h',
      dbConfig.host,
      '-P',
      dbConfig.port,
      '-u',
      dbConfig.user,
      '--default-character-set=utf8mb4',
      dbConfig.database
    ];

    if (dbConfig.password) {
      args.splice(4, 0, `-p${dbConfig.password}`);
    }

    const mysql = spawn('mysql', args);
    const readStream = require('node:fs').createReadStream(inputPath);

    readStream.pipe(mysql.stdin);

    let errorOutput = '';
    mysql.stderr.on('data', data => {
      errorOutput += data.toString();
    });

    mysql.on('close', code => {
      if (code === 0) {
        console.log('💾 [备份管理] mysql恢复执行成功');
        resolve(true);
      } else {
        console.error('💾 [备份管理] mysql恢复执行失败:', errorOutput);
        resolve(false);
      }
    });

    mysql.on('error', error => {
      console.error('💾 [备份管理] mysql恢复进程错误:', error);
      resolve(false);
    });
  });
}

/** 清理旧备份文件 */
async function cleanupOldBackups(): Promise<any> {
  try {
    // 查询所有备份记录，按时间倒序
    const backupsResult = await executeQuery(`
      SELECT backup_id, file_path, backup_name
      FROM fd_backup_record
      WHERE backup_type = 'database' AND status = 'completed'
      ORDER BY create_time DESC
    `);

    if (backupsResult.length <= MAX_BACKUP_FILES) {
      return;
    }

    // 删除超出限制的备份
    const backupsToDelete = backupsResult.slice(MAX_BACKUP_FILES);

    for (const backup of backupsToDelete) {
      try {
        // 删除文件
        await fs.unlink(backup.file_path);
        // 删除数据库记录
        await executeQuery('DELETE FROM fd_backup_record WHERE backup_id = ?', [backup.backup_id]);
        console.log(`💾 [备份管理] 清理旧备份: ${backup.backup_name}`);
      } catch (error) {
        console.error(`💾 [备份管理] 清理备份失败: ${backup.backup_name}`, error);
      }
    }
  } catch (error) {
    console.error('💾 [备份管理] 清理旧备份失败:', error);
  }
}
