/**
 * 29平台对接控制器
 * 实现与29平台的API对接功能
 */

import axios from 'axios';
import type { Response } from 'express';
import { Request } from 'express';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import type { AuthenticatedRequest } from '../middleware/auth';

/**
 * 获取29平台配置
 */
async function get29PlatformConfig() {
  const query = 'SELECT * FROM fd_platform_config WHERE platform_name = ? AND status = 1';
  const result = await executeQuery(query, ['29平台']);
  return result[0] || null;
}

/**
 * 记录同步日志
 */
async function logSyncOperation(
  platformName: string,
  syncType: string,
  status: string,
  count: number = 0,
  errorMessage: string = '',
  syncData: any = null
) {
  const query = `
    INSERT INTO fd_platform_sync_log (platform_name, sync_type, sync_status, sync_count, error_message, sync_data)
    VALUES (?, ?, ?, ?, ?, ?)
  `;
  await executeQuery(query, [platformName, syncType, status, count, errorMessage, JSON.stringify(syncData)]);
}

/**
 * 获取29平台所有商品
 */
export async function get29PlatformProducts(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    console.log('🔗 [29平台对接] 开始获取29平台商品列表');

    // 获取29平台配置
    const config = await get29PlatformConfig();
    if (!config) {
      return res.json(createErrorResponse('29平台配置不存在', ResponseCode.CONFIG_ERROR));
    }

    // 调用29平台getclass接口
    const apiUrl = `${config.platform_url}/api.php?act=getclass`;
    const params = {
      uid: config.uid,
      key: config.api_key
    };

    console.log('🔗 [29平台对接] 请求URL:', apiUrl);
    console.log('🔗 [29平台对接] 请求参数:', { uid: params.uid, key: '***' });

    const response = await axios.post(apiUrl, params, {
      timeout: 30000,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    console.log('🔗 [29平台对接] 响应状态:', response.status);
    console.log('🔗 [29平台对接] 响应数据:', response.data);

    if (response.data && response.data.code === 1) {
      const products = response.data.data || [];

      // 记录成功日志
      await logSyncOperation('29平台', 'getclass', 'success', products.length, '', products);

      console.log(`🔗 [29平台对接] 获取商品成功，共 ${products.length} 个商品`);

      return res.json(
        createSuccessResponse(
          {
            products,
            total: products.length
          },
          '获取29平台商品成功'
        )
      );
    }
    const errorMsg = response.data?.msg || '获取商品失败';
    await logSyncOperation('29平台', 'getclass', 'failed', 0, errorMsg, response.data);

    return res.json(createErrorResponse(errorMsg, ResponseCode.EXTERNAL_API_ERROR));
  } catch (error: any) {
    console.error('🔗 [29平台对接] 获取商品失败:', error);

    const errorMsg = error.response?.data?.msg || error.message || '网络请求失败';
    await logSyncOperation('29平台', 'getclass', 'failed', 0, errorMsg, null);

    return res.json(createErrorResponse(`获取29平台商品失败: ${errorMsg}`, ResponseCode.ERROR));
  }
}

/**
 * 同步29平台商品到本地数据库
 */
export async function sync29PlatformProducts(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { selectedProducts, providerId = 3 } = req.body; // 默认使用29平台服务商ID

    if (!selectedProducts || !Array.isArray(selectedProducts) || selectedProducts.length === 0) {
      return res.json(createErrorResponse('请选择要同步的商品', ResponseCode.PARAM_ERROR));
    }

    console.log('🔗 [29平台对接] 开始同步商品到本地数据库');
    console.log('🔗 [29平台对接] 选择的商品数量:', selectedProducts.length);
    console.log('🔗 [29平台对接] 使用货源ID:', providerId);

    let successCount = 0;
    let failedCount = 0;
    const errors: string[] = [];

    for (const product of selectedProducts) {
      try {
        // 检查商品是否已存在（基于货源和CID的唯一性）
        const existQuery = 'SELECT cid FROM fd_product WHERE queryplat = ? AND getnoun = ?';
        const existResult = await executeQuery(existQuery, [providerId.toString(), product.cid]);

        if (existResult.length > 0) {
          // 更新现有商品
          const updateQuery = `
            UPDATE fd_product SET
              name = ?,
              price = ?,
              content = ?,
              noun = ?,
              docking = ?,
              fenlei = ?,
              update_time = NOW()
            WHERE queryplat = ? AND getnoun = ?
          `;

          await executeQuery(updateQuery, [
            product.name,
            product.price || 0,
            product.content || '',
            product.cid, // noun字段直接使用服务商返回的CID
            providerId.toString(), // docking字段使用货源ID
            product.fenlei || '1',
            providerId.toString(),
            product.cid
          ]);

          console.log(`🔗 [29平台对接] 更新商品: ${product.name} (CID: ${product.cid})`);
        } else {
          // 插入新商品
          const insertQuery = `
            INSERT INTO fd_product (
              name, price, content, fenlei, fenlei1,
              getnoun, noun, queryplat, docking,
              wck, kcid, api, nocheck, yunsuan,
              status, sort, addtime
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `;

          await executeQuery(insertQuery, [
            product.name,
            product.price || 0,
            product.content || '',
            product.fenlei || '1',
            product.fenlei1 || null,
            product.cid, // getnoun字段直接使用服务商返回的CID
            product.cid, // noun字段直接使用服务商返回的CID
            providerId.toString(), // queryplat字段使用货源ID
            providerId.toString(), // docking字段使用货源ID
            product.wck || 0,
            product.kcid || 1,
            product.api || 1,
            product.nocheck || 0,
            product.yunsuan || '*',
            1, // 启用状态
            product.sort || 10,
            new Date().toISOString().slice(0, 19).replace('T', ' ')
          ]);

          console.log(`🔗 [29平台对接] 新增商品: ${product.name} (CID: ${product.cid})`);
        }

        successCount++;
      } catch (error: any) {
        failedCount++;
        const errorMsg = `商品 ${product.name} 同步失败: ${error.message}`;
        errors.push(errorMsg);
        console.error('🔗 [29平台对接] 同步商品失败:', errorMsg);
      }
    }

    // 记录同步日志
    await logSyncOperation(
      '29平台',
      'product_sync',
      failedCount === 0 ? 'success' : 'partial_success',
      successCount,
      errors.join('; '),
      { selectedProducts, successCount, failedCount, errors }
    );

    console.log(`🔗 [29平台对接] 同步完成: 成功 ${successCount} 个，失败 ${failedCount} 个`);

    return res.json(
      createSuccessResponse(
        {
          successCount,
          failedCount,
          errors
        },
        `商品同步完成: 成功 ${successCount} 个，失败 ${failedCount} 个`
      )
    );
  } catch (error: any) {
    console.error('🔗 [29平台对接] 同步商品失败:', error);

    await logSyncOperation('29平台', 'product_sync', 'failed', 0, error.message, null);

    return res.json(createErrorResponse(`同步商品失败: ${error.message}`, ResponseCode.ERROR));
  }
}

/**
 * 获取29平台配置信息
 */
export async function get29PlatformConfig_API(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const config = await get29PlatformConfig();

    if (!config) {
      return res.json(createErrorResponse('29平台配置不存在', ResponseCode.CONFIG_ERROR));
    }

    // 隐藏敏感信息
    const safeConfig = {
      ...config,
      api_key: config.api_key ? `***${config.api_key.slice(-4)}` : ''
    };

    return res.json(createSuccessResponse(safeConfig, '获取配置成功'));
  } catch (error: any) {
    console.error('🔗 [29平台对接] 获取配置失败:', error);
    return res.json(createErrorResponse('获取配置失败', ResponseCode.ERROR));
  }
}

/**
 * 获取同步日志
 */
export async function getSyncLogs(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { page = 1, pageSize = 20 } = req.query;

    const offset = (Number(page) - 1) * Number(pageSize);

    // 获取总数
    const countQuery = 'SELECT COUNT(*) as total FROM fd_platform_sync_log WHERE platform_name = ?';
    const countResult = await executeQuery(countQuery, ['29平台']);
    const total = countResult[0].total;

    // 获取日志列表
    const listQuery = `
      SELECT * FROM fd_platform_sync_log
      WHERE platform_name = ?
      ORDER BY create_time DESC
      LIMIT ${Number(pageSize)} OFFSET ${offset}
    `;
    const logs = await executeQuery(listQuery, ['29平台']);

    return res.json(
      createSuccessResponse(
        {
          list: logs,
          total,
          page: Number(page),
          pageSize: Number(pageSize)
        },
        '获取同步日志成功'
      )
    );
  } catch (error: any) {
    console.error('🔗 [29平台对接] 获取同步日志失败:', error);
    return res.json(createErrorResponse('获取同步日志失败', ResponseCode.ERROR));
  }
}

/**
 * 获取已对接的商品列表
 */
export async function getExistingProducts(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.query;

    let query = 'SELECT cid, name, getnoun, noun, queryplat, docking FROM fd_product';
    const params: any[] = [];

    if (providerId) {
      query += ' WHERE queryplat = ?';
      params.push(providerId);
    }

    const products = await executeQuery(query, params);

    return res.json(
      createSuccessResponse(
        {
          products,
          total: products.length
        },
        '获取已对接商品成功'
      )
    );
  } catch (error: any) {
    console.error('❌ [29平台对接] 获取已对接商品失败:', error);
    return res.json(createErrorResponse(error.message || '获取已对接商品失败', ResponseCode.ERROR));
  }
}

/**
 * 高级同步29平台商品到本地数据库（支持分类分配）
 */
export async function advancedSync29PlatformProducts(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { selectedProducts, providerId = 3, categoryId, createNewCategory, newCategoryName } = req.body;

    if (!selectedProducts || !Array.isArray(selectedProducts) || selectedProducts.length === 0) {
      return res.json(createErrorResponse('请选择要同步的商品', ResponseCode.PARAM_ERROR));
    }

    console.log(`🔗 [29平台对接] 开始高级同步 ${selectedProducts.length} 个商品`);

    let targetCategoryId = categoryId;

    // 如果需要创建新分类
    if (createNewCategory && newCategoryName) {
      try {
        const createCategoryQuery = `
          INSERT INTO fd_category (name, sort_order, status, create_time, update_time)
          VALUES (?, 10, 1, NOW(), NOW())
        `;
        const categoryResult = await executeQuery(createCategoryQuery, [newCategoryName]);
        targetCategoryId = categoryResult.insertId;
        console.log(`🔗 [29平台对接] 创建新分类成功: ${newCategoryName} (ID: ${targetCategoryId})`);
      } catch (error) {
        console.error('🔗 [29平台对接] 创建分类失败:', error);
        return res.json(createErrorResponse('创建分类失败', ResponseCode.ERROR));
      }
    }

    let successCount = 0;
    let failedCount = 0;
    const errors: string[] = [];

    for (const product of selectedProducts) {
      try {
        // 检查商品是否已存在
        const existingQuery = 'SELECT cid FROM fd_product WHERE getnoun = ? AND queryplat = ?';
        const existing = await executeQuery(existingQuery, [product.cid, providerId]);

        if (existing.length > 0) {
          // 更新现有商品
          const updateQuery = `
            UPDATE fd_product
            SET name = ?, price = ?, content = ?, fenlei = ?, update_time = NOW()
            WHERE getnoun = ? AND queryplat = ?
          `;
          await executeQuery(updateQuery, [
            product.name,
            product.price,
            product.content || '',
            targetCategoryId || product.fenlei,
            product.cid,
            providerId
          ]);
          console.log(`🔗 [29平台对接] 更新商品: ${product.name}`);
        } else {
          // 插入新商品
          const insertQuery = `
            INSERT INTO fd_product (
              name, getnoun, noun, price, fenlei, content, queryplat, docking,
              sort, status, wck, kcid, api, nocheck, create_time, update_time
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
          `;

          await executeQuery(insertQuery, [
            product.name,
            product.cid,
            product.cid,
            product.price,
            targetCategoryId || product.fenlei,
            product.content || '',
            providerId,
            providerId,
            product.sort || 10,
            product.status || 1,
            product.wck || 0,
            product.kcid || 0,
            product.api || 0,
            product.nocheck || 0
          ]);
          console.log(`🔗 [29平台对接] 新增商品: ${product.name}`);
        }

        successCount++;
      } catch (error: any) {
        console.error(`🔗 [29平台对接] 同步商品失败: ${product.name}`, error);
        failedCount++;
        errors.push(`${product.name}: ${error.message}`);
      }
    }

    // 记录同步日志
    await logSyncOperation(
      '29平台',
      'advanced-sync',
      failedCount === 0 ? 'success' : 'partial',
      successCount,
      errors.join('; '),
      { selectedProducts, providerId, categoryId: targetCategoryId }
    );

    console.log(`🔗 [29平台对接] 高级同步完成: 成功 ${successCount}, 失败 ${failedCount}`);

    return res.json(
      createSuccessResponse(
        {
          successCount,
          failedCount,
          errors,
          categoryId: targetCategoryId
        },
        '高级同步完成'
      )
    );
  } catch (error: any) {
    console.error('🔗 [29平台对接] 高级同步失败:', error);
    await logSyncOperation('29平台', 'advanced-sync', 'failed', 0, error.message, null);
    return res.json(createErrorResponse(`高级同步失败: ${error.message}`, ResponseCode.ERROR));
  }
}

/**
 * 一键更新已对接商品
 */
export async function updateExistingProducts(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId, productIds, updateAll } = req.body;

    if (!providerId) {
      return res.json(createErrorResponse('货源ID不能为空', ResponseCode.PARAM_ERROR));
    }

    console.log(`🔗 [29平台对接] 开始更新已对接商品，货源ID: ${providerId}`);

    // 获取29平台最新商品数据
    const config = await get29PlatformConfig();
    if (!config) {
      return res.json(createErrorResponse('29平台配置不存在', ResponseCode.CONFIG_ERROR));
    }

    const apiUrl = `${config.platform_url}/api.php?act=getclass`;
    const params = {
      uid: config.uid,
      key: config.api_key
    };

    const response = await axios.post(apiUrl, params, {
      timeout: 30000,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    if (!response.data || response.data.code !== 1) {
      return res.json(createErrorResponse('获取29平台商品失败', ResponseCode.EXTERNAL_API_ERROR));
    }

    const latestProducts = response.data.data || [];
    const latestProductsMap = new Map();
    latestProducts.forEach((product: any) => {
      latestProductsMap.set(product.cid, product);
    });

    // 获取本地已对接的商品
    let query = 'SELECT cid, getnoun FROM fd_product WHERE queryplat = ?';
    const queryParams = [providerId];

    if (!updateAll && productIds && productIds.length > 0) {
      query += ` AND getnoun IN (${productIds.map(() => '?').join(',')})`;
      queryParams.push(...productIds);
    }

    const localProducts = await executeQuery(query, queryParams);

    let successCount = 0;
    let failedCount = 0;
    const errors: string[] = [];

    for (const localProduct of localProducts) {
      try {
        const latestProduct = latestProductsMap.get(localProduct.getnoun);

        if (latestProduct) {
          // 更新商品信息
          const updateQuery = `
            UPDATE fd_product
            SET name = ?, price = ?, content = ?, status = ?, update_time = NOW()
            WHERE cid = ?
          `;
          await executeQuery(updateQuery, [
            latestProduct.name,
            latestProduct.price,
            latestProduct.content || '',
            latestProduct.status || 1,
            localProduct.cid
          ]);
          successCount++;
        } else {
          // 商品在上游已不存在，标记为失效
          await executeQuery('UPDATE fd_product SET status = 0, update_time = NOW() WHERE cid = ?', [localProduct.cid]);
          failedCount++;
          errors.push(`商品 ${localProduct.getnoun} 在上游已不存在`);
        }
      } catch (error: any) {
        console.error(`🔗 [29平台对接] 更新商品失败: ${localProduct.getnoun}`, error);
        failedCount++;
        errors.push(`${localProduct.getnoun}: ${error.message}`);
      }
    }

    console.log(`🔗 [29平台对接] 更新完成: 成功 ${successCount}, 失败 ${failedCount}`);

    return res.json(
      createSuccessResponse(
        {
          successCount,
          failedCount,
          errors
        },
        '更新完成'
      )
    );
  } catch (error: any) {
    console.error('🔗 [29平台对接] 更新已对接商品失败:', error);
    return res.json(createErrorResponse(`更新失败: ${error.message}`, ResponseCode.ERROR));
  }
}

/**
 * 检测失效商品
 */
export async function detectInvalidProducts(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.query;

    if (!providerId) {
      return res.json(createErrorResponse('货源ID不能为空', ResponseCode.PARAM_ERROR));
    }

    console.log(`🔗 [29平台对接] 开始检测失效商品，货源ID: ${providerId}`);

    // 获取29平台最新商品数据
    const config = await get29PlatformConfig();
    if (!config) {
      return res.json(createErrorResponse('29平台配置不存在', ResponseCode.CONFIG_ERROR));
    }

    const apiUrl = `${config.platform_url}/api.php?act=getclass`;
    const params = {
      uid: config.uid,
      key: config.api_key
    };

    const response = await axios.post(apiUrl, params, {
      timeout: 30000,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    if (!response.data || response.data.code !== 1) {
      return res.json(createErrorResponse('获取29平台商品失败', ResponseCode.EXTERNAL_API_ERROR));
    }

    const latestProducts = response.data.data || [];
    const latestProductsMap = new Map();
    latestProducts.forEach((product: any) => {
      latestProductsMap.set(product.cid, {
        ...product,
        isValid: product.status === 1
      });
    });

    // 获取本地已对接的商品
    const localProducts = await executeQuery(
      'SELECT cid, name, getnoun, price, status FROM fd_product WHERE queryplat = ?',
      [providerId]
    );

    const invalidProducts: any[] = [];

    for (const localProduct of localProducts) {
      const latestProduct = latestProductsMap.get(localProduct.getnoun);

      if (!latestProduct) {
        // 商品在上游已不存在
        invalidProducts.push({
          ...localProduct,
          reason: '上游商品已下架'
        });
      } else if (!latestProduct.isValid) {
        // 商品状态异常
        invalidProducts.push({
          ...localProduct,
          reason: '上游商品状态异常'
        });
      }
    }

    console.log(`🔗 [29平台对接] 检测完成，发现 ${invalidProducts.length} 个失效商品`);

    return res.json(
      createSuccessResponse(
        {
          invalidProducts,
          total: invalidProducts.length
        },
        '检测完成'
      )
    );
  } catch (error: any) {
    console.error('🔗 [29平台对接] 检测失效商品失败:', error);
    return res.json(createErrorResponse(`检测失败: ${error.message}`, ResponseCode.ERROR));
  }
}

/**
 * 一键下架失效商品
 */
export async function deactivateInvalidProducts(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId, productIds } = req.body;

    if (!providerId || !productIds || !Array.isArray(productIds)) {
      return res.json(createErrorResponse('参数错误', ResponseCode.PARAM_ERROR));
    }

    console.log(`🔗 [29平台对接] 开始下架失效商品，数量: ${productIds.length}`);

    let successCount = 0;
    let failedCount = 0;
    const errors: string[] = [];

    for (const productId of productIds) {
      try {
        await executeQuery(
          'UPDATE fd_product SET status = 0, update_time = NOW() WHERE getnoun = ? AND queryplat = ?',
          [productId, providerId]
        );
        successCount++;
      } catch (error: any) {
        console.error(`🔗 [29平台对接] 下架商品失败: ${productId}`, error);
        failedCount++;
        errors.push(`${productId}: ${error.message}`);
      }
    }

    console.log(`🔗 [29平台对接] 下架完成: 成功 ${successCount}, 失败 ${failedCount}`);

    return res.json(
      createSuccessResponse(
        {
          successCount,
          failedCount,
          errors
        },
        '下架完成'
      )
    );
  } catch (error: any) {
    console.error('🔗 [29平台对接] 下架失效商品失败:', error);
    return res.json(createErrorResponse(`下架失败: ${error.message}`, ResponseCode.ERROR));
  }
}
