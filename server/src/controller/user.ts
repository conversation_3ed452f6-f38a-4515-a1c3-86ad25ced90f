import type { Request, Response } from 'express';
import { hashPassword } from '../utils/password';
import PasswordPolicy from '../utils/passwordPolicy';
import ConfigManager from '../utils/configManager';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse, sendError, sendSuccess } from '../utils/response';
import type { AuthenticatedRequest } from '../middleware/auth';
import { getUserPermissions as getPermissions } from '../middleware/auth';
import { logOperation, logUserOperation } from '../utils/logger';

/** 用户注册 */
export async function register(req: Request, res: Response): Promise<any> {
  try {
    const { username, password, email, inviteCode } = req.body;

    // 参数验证
    if (!username || !password) {
      return res.json(createErrorResponse('用户名和密码不能为空', ResponseCode.PARAM_ERROR));
      return;
    }

    // 检查注册功能是否启用
    const isRegistrationEnabled = await ConfigManager.isRegistrationEnabled();
    if (!isRegistrationEnabled) {
      return res.json(createErrorResponse('用户注册功能已关闭', ResponseCode.FORBIDDEN));
      return;
    }

    // 验证用户名格式（必须是QQ号）
    if (!/^\d{5,11}$/.test(username)) {
      return res.json(createErrorResponse('用户名必须是5-11位QQ号', ResponseCode.PARAM_ERROR));
      return;
    }

    // 验证邮箱格式
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return res.json(createErrorResponse('邮箱格式不正确', ResponseCode.PARAM_ERROR));
      return;
    }

    // 验证密码策略
    const passwordValidation = await PasswordPolicy.validatePassword(password);
    if (!passwordValidation.isValid) {
      return res.json(
        createErrorResponse(`密码不符合安全策略: ${passwordValidation.errors.join(', ')}`, ResponseCode.PARAM_ERROR)
      );
      return;
    }

    // 验证邀请码（如果提供）
    let parentUserId: number | null = null;
    if (inviteCode) {
      try {
        const inviteResult = await executeQuery('SELECT user_id FROM fd_user WHERE invite_code = ? AND status = 1', [
          inviteCode
        ]);

        if (inviteResult.length === 0) {
          return res.json(createErrorResponse('邀请码无效或已失效', ResponseCode.PARAM_ERROR));
          return;
        }

        parentUserId = inviteResult[0].user_id;
      } catch (error) {
        console.error('🔍 [注册] 验证邀请码失败:', error);
        return res.json(createErrorResponse('验证邀请码失败', ResponseCode.ERROR));
        return;
      }
    }

    // 检查用户名是否已存在
    try {
      const existingUser = await executeQuery('SELECT user_id FROM fd_user WHERE username = ?', [username]);

      if (existingUser.length > 0) {
        return res.json(createErrorResponse('用户名已存在', ResponseCode.PARAM_ERROR));
        return;
      }
    } catch (error) {
      console.error('🔍 [注册] 检查用户名失败:', error);
      return res.json(createErrorResponse('检查用户名失败', ResponseCode.ERROR));
      return;
    }

    // 检查邮箱是否已存在
    if (email) {
      try {
        const existingEmail = await executeQuery('SELECT user_id FROM fd_user WHERE email = ?', [email]);

        if (existingEmail.length > 0) {
          return res.json(createErrorResponse('邮箱已被使用', ResponseCode.PARAM_ERROR));
          return;
        }
      } catch (error) {
        console.error('🔍 [注册] 检查邮箱失败:', error);
        return res.json(createErrorResponse('检查邮箱失败', ResponseCode.ERROR));
        return;
      }
    }

    // 加密密码
    const hashedPassword = await hashPassword(password);

    // 生成用户的邀请码
    const userInviteCode = generateInviteCode();

    // 创建用户
    try {
      const insertResult = await executeQuery(
        `INSERT INTO fd_user (
          username, password, email, invite_code, sid,
          user_role, status, create_time, update_time
        ) VALUES (?, ?, ?, ?, ?, 'user', 1, NOW(), NOW())`,
        [username, hashedPassword, email || null, userInviteCode, parentUserId]
      );

      const userId = insertResult.insertId;

      console.log(`👤 [注册] 用户注册成功: ${username} (ID: ${userId})`);

      // 返回成功响应（不包含敏感信息）
      return res.json(
        createSuccessResponse(
          {
            userId,
            username,
            email: email || null,
            inviteCode: userInviteCode,
            parentUserId,
            message: '注册成功'
          },
          '用户注册成功'
        )
      );
    } catch (error) {
      console.error('🔍 [注册] 创建用户失败:', error);
      return res.json(createErrorResponse('创建用户失败', ResponseCode.ERROR));
      return;
    }
  } catch (error) {
    console.error('🔍 [注册] 用户注册失败:', error);
    return res.json(createErrorResponse('用户注册失败', ResponseCode.ERROR));
  }
}

/** 修改密码 */
export async function changePassword(req: Request, res: Response): Promise<any> {
  try {
    const { oldPassword, newPassword } = req.body;
    const userId = (req as any).user?.userId;

    if (!userId) {
      return res.json(createErrorResponse('用户未登录', ResponseCode.TOKEN_INVALID));
      return;
    }

    // 参数验证
    if (!oldPassword || !newPassword) {
      return res.json(createErrorResponse('旧密码和新密码不能为空', ResponseCode.PARAM_ERROR));
      return;
    }

    // 验证新密码策略
    const passwordValidation = await PasswordPolicy.validatePassword(newPassword);
    if (!passwordValidation.isValid) {
      return res.json(
        createErrorResponse(`新密码不符合安全策略: ${passwordValidation.errors.join(', ')}`, ResponseCode.PARAM_ERROR)
      );
      return;
    }

    // 获取用户当前密码
    try {
      const userResult = await executeQuery('SELECT password FROM fd_user WHERE user_id = ? AND status = 1', [userId]);

      if (userResult.length === 0) {
        return res.json(createErrorResponse('用户不存在', ResponseCode.PARAM_ERROR));
        return;
      }

      // 验证旧密码
      const { verifyPassword } = await import('../utils/password');
      const isOldPasswordValid = await verifyPassword(oldPassword, userResult[0].password);

      if (!isOldPasswordValid) {
        return res.json(createErrorResponse('旧密码错误', ResponseCode.PARAM_ERROR));
        return;
      }

      // 加密新密码
      const hashedNewPassword = await hashPassword(newPassword);

      // 更新密码
      await executeQuery('UPDATE fd_user SET password = ?, update_time = NOW() WHERE user_id = ?', [
        hashedNewPassword,
        userId
      ]);

      console.log(`🔒 [密码] 用户 ${userId} 修改密码成功`);

      return res.json(createSuccessResponse(null, '密码修改成功'));
    } catch (error) {
      console.error('🔒 [密码] 修改密码失败:', error);
      return res.json(createErrorResponse('修改密码失败', ResponseCode.ERROR));
    }
  } catch (error) {
    console.error('🔒 [密码] 修改密码失败:', error);
    return res.json(createErrorResponse('修改密码失败', ResponseCode.ERROR));
  }
}

/** 获取密码强度 */
export async function getPasswordStrength(req: Request, res: Response): Promise<any> {
  try {
    const { password } = req.body;

    if (!password) {
      return res.json(createErrorResponse('密码不能为空', ResponseCode.PARAM_ERROR));
      return;
    }

    const strength = await PasswordPolicy.getPasswordStrength(password);
    const validation = await PasswordPolicy.validatePassword(password);

    return res.json(
      createSuccessResponse(
        {
          ...strength,
          validation: {
            isValid: validation.isValid,
            errors: validation.errors,
            suggestions: validation.suggestions
          }
        },
        '密码强度分析完成'
      )
    );
  } catch (error) {
    console.error('🔒 [密码] 分析密码强度失败:', error);
    return res.json(createErrorResponse('分析密码强度失败', ResponseCode.ERROR));
  }
}

/** 生成邀请码 */
function generateInviteCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

/** 获取用户登录尝试信息 */
export async function getLoginAttemptInfo(req: Request, res: Response): Promise<any> {
  try {
    const { username } = req.query;

    if (!username || typeof username !== 'string') {
      return res.json(createErrorResponse('用户名不能为空', ResponseCode.PARAM_ERROR));
      return;
    }

    const { LoginAttemptManager } = await import('../utils/loginAttemptManager');
    const attemptInfo = await LoginAttemptManager.getUserAttemptInfo(username);

    return res.json(createSuccessResponse(attemptInfo, '获取登录尝试信息成功'));
  } catch (error) {
    console.error('🔒 [登录限制] 获取登录尝试信息失败:', error);
    return res.json(createErrorResponse('获取登录尝试信息失败', ResponseCode.ERROR));
  }
}

// ===== 用户管理功能 =====

/** 获取单个用户详情 */
export async function getUserById(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { userId } = req.params;

    if (!userId || isNaN(Number(userId))) {
      return res.json(createErrorResponse('用户ID无效', ResponseCode.PARAM_ERROR));
    }

    // 查询用户详细信息
    const userQuery = `
      SELECT
        u.user_id as userId,
        u.username,
        u.email,
        u.phone,
        u.balance,
        u.price_rate as rate,
        u.invite_code as inviteCode,
        u.status,
        u.level_id as isVip,
        u.create_time as createTime,
        u.login_info,
        inviter.username as inviterName,
        (SELECT COUNT(*) FROM fd_user WHERE referrer_id = u.user_id) as inviteeCount,
        (SELECT COUNT(*) FROM fd_order WHERE user_id = u.user_id) as orderCount
      FROM fd_user u
      LEFT JOIN fd_user inviter ON u.referrer_id = inviter.user_id
      WHERE u.user_id = ?
    `;

    const users = await executeQuery(userQuery, [userId]);

    if (!users || users.length === 0) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.NOT_FOUND));
    }

    const user = users[0];

    // 处理登录信息
    if (user.login_info) {
      try {
        const loginInfo = JSON.parse(user.login_info);
        user.lastLoginTime = loginInfo.lastLoginTime || null;
        user.lastLoginIp = loginInfo.lastLoginIp || null;
      } catch (e) {
        user.lastLoginTime = null;
        user.lastLoginIp = null;
      }
    } else {
      user.lastLoginTime = null;
      user.lastLoginIp = null;
    }

    // 移除敏感信息和临时字段
    delete user.password;
    delete user.salt;
    delete user.login_info;

    return res.json(createSuccessResponse(user, '获取用户详情成功'));
  } catch (error) {
    console.error('获取用户详情失败:', error);
    return res.json(createErrorResponse('获取用户详情失败', ResponseCode.SYSTEM_ERROR));
  }
}

/** 获取用户列表 */
export async function getUserList(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { page = 1, pageSize = 20, keyword = '', userRole = '', status = '' } = req.query;

    const offset = (Number(page) - 1) * Number(pageSize);
    const whereConditions = ['1=1'];
    const queryParams: any[] = [];

    // 关键词搜索（用户名、邮箱）
    if (keyword) {
      whereConditions.push('(u.username LIKE ? OR u.email LIKE ?)');
      queryParams.push(`%${keyword}%`, `%${keyword}%`);
    }

    // 角色筛选
    if (userRole) {
      whereConditions.push('u.user_role = ?');
      queryParams.push(userRole);
    }

    // 状态筛选
    if (status !== '') {
      whereConditions.push('u.status = ?');
      queryParams.push(Number(status));
    }

    const whereClause = whereConditions.join(' AND ');

    // 查询用户列表 - 修正字段映射
    const userListQuery = `
      SELECT
        u.user_id as userId,
        u.username,
        u.email,
        u.nickname,
        u.balance,
        u.price_rate as userRate,
        u.role,
        u.user_role as userRole,
        u.status,
        u.invite_code as inviteCode,
        u.sid,
        u.referrer_id as referrerId,
        u.total_recharge as totalRecharge,
        u.level_id as levelId,
        u.create_time as createTime,
        u.update_time as updateTime,
        parent.username as parentUsername
      FROM fd_user u
      LEFT JOIN fd_user parent ON u.sid = parent.user_id
      WHERE ${whereClause}
      ORDER BY u.create_time DESC
      LIMIT ? OFFSET ?
    `;

    // 使用直接拼接的LIMIT，避免参数化查询问题
    const finalQuery = userListQuery.replace('LIMIT ? OFFSET ?', `LIMIT ${Number(pageSize)} OFFSET ${offset}`);

    const userList = await executeQuery(finalQuery, queryParams);

    // 查询总数
    const countQuery = `
      SELECT COUNT(u.user_id) as total
      FROM fd_user u
      WHERE ${whereClause}
    `;

    // 由于已将 LIMIT/OFFSET 直接拼接进SQL，这里无需移除参数，直接使用过滤条件参数
    const countParams = queryParams;
    const countResult = await executeQuery(countQuery, countParams);
    const total = countResult[0]?.total || 0;

    // 处理用户数据 - 修正字段处理逻辑
    const processedUserList = userList.map((user: any) => ({
      ...user,
      // userRole 字段已经正确映射，无需额外处理
      roles: user.userRole ? [user.userRole] : ['user'], // 基于 user_role 字段构建角色数组
      createTime: new Date(user.createTime).toLocaleString('zh-CN'),
      updateTime: new Date(user.updateTime).toLocaleString('zh-CN')
    }));

    return res.json(
      createSuccessResponse(
        {
          list: processedUserList,
          pagination: {
            page: Number(page),
            pageSize: Number(pageSize),
            total,
            totalPages: Math.ceil(total / Number(pageSize))
          }
        },
        '获取用户列表成功'
      )
    );
  } catch (error) {
    console.error('👥 [用户管理] 获取用户列表失败:', error);
    return res.json(createErrorResponse('获取用户列表失败', ResponseCode.ERROR));
  }
}

/** 创建用户 */
export async function createUser(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { username, email, password, userRate = 0.8, userRole = 'user', balance = 0, status = 1 } = req.body;

    // 参数验证
    if (!username || !password) {
      return res.json(createErrorResponse('用户名和密码不能为空', ResponseCode.PARAM_ERROR));
    }

    // 验证用户名格式（必须是QQ号）
    if (!/^\d{5,11}$/.test(username)) {
      return res.json(createErrorResponse('用户名必须是5-11位QQ号', ResponseCode.PARAM_ERROR));
    }

    // 验证邮箱格式
    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return res.json(createErrorResponse('邮箱格式不正确', ResponseCode.PARAM_ERROR));
    }

    // 验证密码策略
    const passwordValidation = await PasswordPolicy.validatePassword(password);
    if (!passwordValidation.isValid) {
      return res.json(
        createErrorResponse(`密码不符合安全策略: ${passwordValidation.errors.join(', ')}`, ResponseCode.PARAM_ERROR)
      );
    }

    // 检查用户名是否已存在
    const existingUser = await executeQuery('SELECT user_id FROM fd_user WHERE username = ?', [username]);

    if (existingUser.length > 0) {
      return res.json(createErrorResponse('用户名已存在', ResponseCode.PARAM_ERROR));
    }

    // 检查邮箱是否已存在
    if (email) {
      const existingEmail = await executeQuery('SELECT user_id FROM fd_user WHERE email = ?', [email]);

      if (existingEmail.length > 0) {
        return res.json(createErrorResponse('邮箱已被使用', ResponseCode.PARAM_ERROR));
      }
    }

    // 加密密码
    const hashedPassword = await hashPassword(password);

    // 生成用户的邀请码
    const userInviteCode = generateInviteCode();

    // 创建用户
    const insertResult = await executeQuery(
      `INSERT INTO fd_user (
        username, password, email, nickname, balance, price_rate,
        invite_code, user_role, status, create_time, update_time
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
      [username, hashedPassword, email || null, username, balance, userRate, userInviteCode, userRole, status]
    );

    const userId = insertResult.insertId;

    console.log(`👤 [用户管理] 管理员创建用户成功: ${username} (ID: ${userId})`);

    return res.json(
      createSuccessResponse(
        {
          userId,
          username,
          email: email || null,
          inviteCode: userInviteCode,
          message: '用户创建成功'
        },
        '用户创建成功'
      )
    );
  } catch (error) {
    console.error('👥 [用户管理] 创建用户失败:', error);
    return res.json(createErrorResponse('创建用户失败', ResponseCode.ERROR));
  }
}

/** 更新用户信息 */
export async function updateUser(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { userId } = req.params;
    const { email, userRate, userRole, balance, status, password } = req.body;

    if (!userId) {
      return res.json(createErrorResponse('用户ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查用户是否存在
    const existingUser = await executeQuery('SELECT user_id, username FROM fd_user WHERE user_id = ?', [userId]);

    if (existingUser.length === 0) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.PARAM_ERROR));
    }

    const updateFields: string[] = [];
    const updateValues: any[] = [];

    // 更新邮箱
    if (email !== undefined) {
      if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
        return res.json(createErrorResponse('邮箱格式不正确', ResponseCode.PARAM_ERROR));
      }

      // 检查邮箱是否已被其他用户使用
      if (email) {
        const existingEmail = await executeQuery('SELECT user_id FROM fd_user WHERE email = ? AND user_id != ?', [
          email,
          userId
        ]);

        if (existingEmail.length > 0) {
          return res.json(createErrorResponse('邮箱已被其他用户使用', ResponseCode.PARAM_ERROR));
        }
      }

      updateFields.push('email = ?');
      updateValues.push(email || null);
    }

    // 更新费率
    if (userRate !== undefined) {
      updateFields.push('price_rate = ?');
      updateValues.push(userRate);
    }

    // 更新余额
    if (balance !== undefined) {
      updateFields.push('balance = ?');
      updateValues.push(balance);
    }

    // 更新角色
    if (userRole !== undefined) {
      updateFields.push('user_role = ?');
      updateValues.push(userRole);
    }

    // 更新状态
    if (status !== undefined) {
      updateFields.push('status = ?');
      updateValues.push(status);
    }

    // 更新密码
    if (password) {
      const passwordValidation = await PasswordPolicy.validatePassword(password);
      if (!passwordValidation.isValid) {
        return res.json(
          createErrorResponse(`密码不符合安全策略: ${passwordValidation.errors.join(', ')}`, ResponseCode.PARAM_ERROR)
        );
      }

      const hashedPassword = await hashPassword(password);
      updateFields.push('password = ?');
      updateValues.push(hashedPassword);
    }

    if (updateFields.length > 0) {
      updateFields.push('update_time = NOW()');
      updateValues.push(userId);

      await executeQuery(`UPDATE fd_user SET ${updateFields.join(', ')} WHERE user_id = ?`, updateValues);
    }

    console.log(`👤 [用户管理] 更新用户成功: ${existingUser[0].username} (ID: ${userId})`);

    return res.json(createSuccessResponse(null, '用户信息更新成功'));
  } catch (error) {
    console.error('👥 [用户管理] 更新用户失败:', error);
    return res.json(createErrorResponse('更新用户失败', ResponseCode.ERROR));
  }
}

/** 删除用户 */
export async function deleteUser(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.json(createErrorResponse('用户ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查用户是否存在
    const existingUser = await executeQuery('SELECT user_id, username FROM fd_user WHERE user_id = ?', [userId]);

    if (existingUser.length === 0) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.PARAM_ERROR));
    }

    // 检查是否为管理员用户（防止删除管理员）
    const adminCheck = await executeQuery(
      'SELECT user_role FROM fd_user WHERE user_id = ? AND user_role IN ("admin", "super_admin")',
      [userId]
    );

    if (adminCheck.length > 0) {
      return res.json(createErrorResponse('不能删除管理员用户', ResponseCode.FORBIDDEN));
    }

    // 检查用户是否有下级用户
    const childUsers = await executeQuery('SELECT COUNT(*) as count FROM fd_user WHERE sid = ?', [userId]);

    if (childUsers[0].count > 0) {
      return res.json(createErrorResponse('该用户有下级用户，无法删除', ResponseCode.FORBIDDEN));
    }

    // 删除用户
    await executeQuery('DELETE FROM fd_user WHERE user_id = ?', [userId]);

    console.log(`👤 [用户管理] 删除用户成功: ${existingUser[0].username} (ID: ${userId})`);

    return res.json(createSuccessResponse(null, '用户删除成功'));
  } catch (error) {
    console.error('👥 [用户管理] 删除用户失败:', error);
    return res.json(createErrorResponse('删除用户失败', ResponseCode.ERROR));
  }
}

/** 批量更新用户费率 */
export async function batchUpdateUserRate(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { userIds, userRate } = req.body;

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.json(createErrorResponse('用户ID列表不能为空', ResponseCode.PARAM_ERROR));
    }

    if (userRate === undefined || userRate < 0 || userRate > 1) {
      return res.json(createErrorResponse('费率必须在0-1之间', ResponseCode.PARAM_ERROR));
    }

    // 构建批量更新SQL
    const placeholders = userIds.map(() => '?').join(',');
    const updateQuery = `
      UPDATE fd_user
      SET price_rate = ?, update_time = NOW()
      WHERE user_id IN (${placeholders})
    `;

    const updateParams = [userRate, ...userIds];
    const result = await executeQuery(updateQuery, updateParams);

    console.log(`👥 [用户管理] 批量更新费率成功: ${result.affectedRows} 个用户`);

    return res.json(
      createSuccessResponse(
        {
          updatedCount: result.affectedRows,
          userRate
        },
        `成功更新 ${result.affectedRows} 个用户的费率`
      )
    );
  } catch (error) {
    console.error('👥 [用户管理] 批量更新费率失败:', error);
    return res.json(createErrorResponse('批量更新费率失败', ResponseCode.ERROR));
  }
}

/** 获取用户统计信息 */
export async function getUserStats(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    // 总用户数
    const totalUsersResult = await executeQuery('SELECT COUNT(*) as count FROM fd_user');
    const totalUsers = totalUsersResult[0].count;

    // 活跃用户数（状态为1）
    const activeUsersResult = await executeQuery('SELECT COUNT(*) as count FROM fd_user WHERE status = 1');
    const activeUsers = activeUsersResult[0].count;

    // 今日新增用户
    const todayUsersResult = await executeQuery(
      'SELECT COUNT(*) as count FROM fd_user WHERE DATE(create_time) = CURDATE()'
    );
    const todayUsers = todayUsersResult[0].count;

    // 角色分布
    const roleStatsResult = await executeQuery(`
      SELECT
        u.user_role as role_name,
        COUNT(*) as count
      FROM fd_user u
      WHERE u.status = 1
      GROUP BY u.user_role
    `);

    // 用户余额统计
    const balanceStatsResult = await executeQuery(`
      SELECT
        SUM(balance) as totalBalance,
        AVG(balance) as avgBalance,
        MAX(balance) as maxBalance,
        MIN(balance) as minBalance
      FROM fd_user
      WHERE status = 1
    `);

    const stats = {
      totalUsers,
      activeUsers,
      inactiveUsers: totalUsers - activeUsers,
      todayUsers,
      roleStats: roleStatsResult,
      balanceStats: balanceStatsResult[0] || {
        totalBalance: 0,
        avgBalance: 0,
        maxBalance: 0,
        minBalance: 0
      }
    };

    return res.json(createSuccessResponse(stats, '获取用户统计信息成功'));
  } catch (error) {
    console.error('👥 [用户管理] 获取统计信息失败:', error);
    return res.json(createErrorResponse('获取统计信息失败', ResponseCode.ERROR));
  }
}

/** 获取用户权限 */
export async function getUserPermissions(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const userId = Number.parseInt(req.params.userId);

    if (!userId) {
      return res.json(createErrorResponse('用户ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查用户是否存在
    const [user] = await executeQuery('SELECT user_id, username FROM fd_user WHERE user_id = ?', [userId]);
    if (!user) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.NOT_FOUND));
    }

    // 获取用户有效权限（包含来源信息）
    const permissionsSql = `
      SELECT DISTINCT
        p.permission_id,
        p.permission_code,
        p.permission_name,
        p.permission_description,
        p.permission_type,
        p.permission_group,
        CASE
          WHEN up.permission_id IS NOT NULL THEN 'direct'
          ELSE 'role'
        END as source,
        CASE
          WHEN up.permission_id IS NOT NULL THEN up.grant_type
          ELSE rp.grant_type
        END as grant_type,
        CASE
          WHEN up.permission_id IS NOT NULL THEN up.data_scope
          ELSE rp.data_scope
        END as data_scope
      FROM (
        -- 直接权限
        SELECT p.*, up.grant_type, up.data_scope, up.permission_id as up_permission_id
        FROM fd_user_permission up
        JOIN fd_permission p ON up.permission_id = p.permission_id
        WHERE up.user_id = ? AND up.status = 1 AND p.status = 1
        AND up.grant_type = 'grant'

        UNION

        -- 角色权限
        SELECT p.*, rp.grant_type, rp.data_scope, NULL as up_permission_id
        FROM fd_user_role ur
        JOIN fd_role r ON ur.role_id = r.role_id
        JOIN fd_role_permission rp ON r.role_id = rp.role_id
        JOIN fd_permission p ON rp.permission_id = p.permission_id
        WHERE ur.user_id = ? AND ur.status = 1 AND r.status = 1
        AND rp.status = 1 AND p.status = 1 AND rp.grant_type = 'grant'
        AND (ur.expire_time IS NULL OR ur.expire_time > NOW())
      ) permissions
      JOIN fd_permission p ON permissions.permission_id = p.permission_id
      LEFT JOIN fd_user_permission up ON permissions.permission_id = up.permission_id AND up.user_id = ?
      LEFT JOIN fd_user_role ur2 ON ur2.user_id = ?
      LEFT JOIN fd_role_permission rp ON ur2.role_id = rp.role_id AND rp.permission_id = permissions.permission_id
      ORDER BY p.permission_group ASC, p.sort_order ASC, p.permission_name ASC
    `;

    const effectivePermissions = await executeQuery(permissionsSql, [userId, userId, userId, userId]);

    return res.json(createSuccessResponse(effectivePermissions, '获取用户权限成功'));
  } catch (error) {
    console.error('👥 [用户管理] 获取用户权限失败:', error);
    return res.json(createErrorResponse('获取用户权限失败', ResponseCode.ERROR));
  }
}

/** 高级用户筛选 */
export async function advancedUserFilter(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const {
      keyword,
      status,
      createTimeRange,
      lastLoginRange,
      hasRoles,
      roleIds,
      roleOperator,
      hasPermissions,
      permissionIds,
      permissionOperator,
      balanceRange,
      rateRange,
      orderCountRange,
      hasInviter,
      inviterIds,
      hasInvitees,
      inviteeCountRange
    } = req.body;

    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    // 关键词搜索
    if (keyword) {
      whereClause += ' AND (u.username LIKE ? OR u.email LIKE ? OR u.phone LIKE ?)';
      params.push(`%${keyword}%`, `%${keyword}%`, `%${keyword}%`);
    }

    // 状态筛选
    if (status !== undefined) {
      whereClause += ' AND u.status = ?';
      params.push(status);
    }

    // 时间范围筛选
    if (createTimeRange && createTimeRange.length === 2) {
      whereClause += ' AND u.create_time BETWEEN ? AND ?';
      params.push(createTimeRange[0], createTimeRange[1]);
    }

    // 余额范围筛选
    if (balanceRange && balanceRange[0] !== balanceRange[1]) {
      whereClause += ' AND u.balance BETWEEN ? AND ?';
      params.push(balanceRange[0], balanceRange[1]);
    }

    // 费率范围筛选
    if (rateRange && rateRange[0] !== rateRange[1]) {
      whereClause += ' AND u.price_rate BETWEEN ? AND ?';
      params.push(rateRange[0], rateRange[1]);
    }

    // 基础查询
    let query = `
      SELECT
        u.user_id as userId,
        u.username,
        u.email,
        u.phone,
        u.balance,
        u.price_rate as rate,
        u.status,
        u.create_time,
        u.login_info,
        COUNT(DISTINCT ur.role_id) as role_count,
        COUNT(DISTINCT up.permission_id) as permission_count,
        COUNT(DISTINCT o.order_id) as order_count
      FROM fd_user u
      LEFT JOIN fd_user_role ur ON u.user_id = ur.user_id AND ur.status = 1
      LEFT JOIN fd_user_permission up ON u.user_id = up.user_id AND up.status = 1
      LEFT JOIN fd_order o ON u.user_id = o.user_id
      ${whereClause}
      GROUP BY u.user_id
    `;

    // 角色条件筛选
    if (hasRoles !== undefined || (roleIds && roleIds.length > 0)) {
      if (hasRoles === false) {
        query += ' HAVING role_count = 0';
      } else if (hasRoles === true) {
        query += ' HAVING role_count > 0';
      }

      if (roleIds && roleIds.length > 0) {
        // 这里需要更复杂的子查询来处理角色匹配逻辑
        // 暂时简化处理
        query += ` AND u.user_id IN (
          SELECT ur2.user_id FROM fd_user_role ur2
          WHERE ur2.role_id IN (${roleIds.map(() => '?').join(',')})
          AND ur2.status = 1
        )`;
        params.push(...roleIds);
      }
    }

    // 订单数量筛选
    if (orderCountRange && orderCountRange[0] !== orderCountRange[1]) {
      query += ` HAVING order_count BETWEEN ${orderCountRange[0]} AND ${orderCountRange[1]}`;
    }

    query += ' ORDER BY u.create_time DESC LIMIT 1000';

    const users = await executeQuery(query, params);

    // 获取总用户数
    const totalQuery = 'SELECT COUNT(*) as total FROM fd_user';
    const [totalResult] = await executeQuery(totalQuery);

    return res.json(
      createSuccessResponse(
        {
          users,
          total: totalResult.total
        },
        '用户筛选成功'
      )
    );
  } catch (error) {
    console.error('👥 [用户管理] 高级筛选失败:', error);
    return res.json(createErrorResponse('用户筛选失败', ResponseCode.ERROR));
  }
}

/** 保存筛选条件 */
export async function saveUserFilter(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { name, conditions } = req.body;
    const userId = req.user?.userId;

    if (!name || !conditions) {
      return res.json(createErrorResponse('筛选条件名称和条件不能为空', ResponseCode.PARAM_ERROR));
    }

    const query = `
      INSERT INTO fd_user_filter (user_id, filter_name, filter_conditions, create_time)
      VALUES (?, ?, ?, NOW())
    `;

    const result = await executeQuery(query, [userId, name, JSON.stringify(conditions)]);

    return res.json(createSuccessResponse({ id: (result as any).insertId }, '筛选条件保存成功'));
  } catch (error) {
    console.error('👥 [用户管理] 保存筛选条件失败:', error);
    return res.json(createErrorResponse('保存筛选条件失败', ResponseCode.ERROR));
  }
}

/** 获取保存的筛选条件 */
export async function getSavedUserFilters(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const userId = req.user?.userId;

    const query = `
      SELECT
        filter_id as id,
        filter_name as name,
        filter_conditions as conditions,
        create_time
      FROM fd_user_filter
      WHERE user_id = ?
      ORDER BY create_time DESC
    `;

    const filters = await executeQuery(query, [userId]);

    // 解析JSON条件
    const parsedFilters = (filters as any[]).map(filter => ({
      ...filter,
      conditions: JSON.parse(filter.conditions)
    }));

    return res.json(createSuccessResponse(parsedFilters, '获取筛选条件成功'));
  } catch (error) {
    console.error('👥 [用户管理] 获取筛选条件失败:', error);
    return res.json(createErrorResponse('获取筛选条件失败', ResponseCode.ERROR));
  }
}

/** 删除保存的筛选条件 */
export async function deleteSavedUserFilter(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const filterId = Number.parseInt(req.params.filterId);
    const userId = req.user?.userId;

    if (!filterId) {
      return res.json(createErrorResponse('筛选条件ID不能为空', ResponseCode.PARAM_ERROR));
    }

    const query = 'DELETE FROM fd_user_filter WHERE filter_id = ? AND user_id = ?';
    await executeQuery(query, [filterId, userId]);

    return res.json(createSuccessResponse(null, '筛选条件删除成功'));
  } catch (error) {
    console.error('👥 [用户管理] 删除筛选条件失败:', error);
    return res.json(createErrorResponse('删除筛选条件失败', ResponseCode.ERROR));
  }
}

/** 获取用户权限继承分析 */
export async function getUserPermissionInheritance(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const userId = Number.parseInt(req.params.userId);

    if (!userId) {
      return res.json(createErrorResponse('用户ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查用户是否存在
    const [user] = await executeQuery('SELECT user_id, username FROM fd_user WHERE user_id = ?', [userId]);
    if (!user) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.NOT_FOUND));
    }

    // 获取角色权限
    const rolePermissionsQuery = `
      SELECT DISTINCT
        p.permission_id,
        p.permission_code,
        p.permission_name,
        p.permission_description,
        p.permission_type,
        p.permission_group,
        rp.grant_type,
        rp.data_scope,
        r.role_name,
        r.role_id,
        'role' as source
      FROM fd_user_role ur
      JOIN fd_role r ON ur.role_id = r.role_id
      JOIN fd_role_permission rp ON r.role_id = rp.role_id
      JOIN fd_permission p ON rp.permission_id = p.permission_id
      WHERE ur.user_id = ? AND ur.status = 1 AND r.status = 1
      AND rp.status = 1 AND p.status = 1
      AND (ur.expire_time IS NULL OR ur.expire_time > NOW())
      ORDER BY r.role_level ASC, p.permission_group ASC, p.sort_order ASC
    `;

    const rolePermissions = await executeQuery(rolePermissionsQuery, [userId]);

    // 获取直接权限
    const directPermissionsQuery = `
      SELECT DISTINCT
        p.permission_id,
        p.permission_code,
        p.permission_name,
        p.permission_description,
        p.permission_type,
        p.permission_group,
        up.grant_type,
        up.data_scope,
        'direct' as source
      FROM fd_user_permission up
      JOIN fd_permission p ON up.permission_id = p.permission_id
      WHERE up.user_id = ? AND up.status = 1 AND p.status = 1
      ORDER BY p.permission_group ASC, p.sort_order ASC
    `;

    const directPermissions = await executeQuery(directPermissionsQuery, [userId]);

    // 计算有效权限（合并角色权限和直接权限，直接权限优先）
    const effectivePermissions = [];
    const permissionMap = new Map();

    // 先添加角色权限
    for (const rolePermission of rolePermissions as any[]) {
      const key = rolePermission.permission_id;
      if (!permissionMap.has(key)) {
        permissionMap.set(key, rolePermission);
      }
    }

    // 直接权限覆盖角色权限
    for (const directPermission of directPermissions as any[]) {
      const key = directPermission.permission_id;
      permissionMap.set(key, directPermission);
    }

    // 转换为数组
    for (const permission of permissionMap.values()) {
      if (permission.grant_type === 'grant') {
        effectivePermissions.push(permission);
      }
    }

    const result = {
      rolePermissions,
      directPermissions,
      effectivePermissions
    };

    return res.json(createSuccessResponse(result, '获取用户权限继承分析成功'));
  } catch (error) {
    console.error('👥 [用户管理] 获取权限继承分析失败:', error);
    return res.json(createErrorResponse('获取权限继承分析失败', ResponseCode.ERROR));
  }
}

/** 解决权限冲突 */
export async function resolvePermissionConflict(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { user_id, permission_id, conflict_type, resolution } = req.body;

    if (!user_id || !permission_id || !conflict_type || !resolution) {
      return res.json(createErrorResponse('参数不完整', ResponseCode.PARAM_ERROR));
    }

    // 检查用户是否存在
    const [user] = await executeQuery('SELECT user_id FROM fd_user WHERE user_id = ?', [user_id]);
    if (!user) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.NOT_FOUND));
    }

    // 根据解决方案处理冲突
    switch (resolution) {
      case 'use_direct':
        // 使用直接权限，确保直接权限存在且优先级高
        const [directPermission] = await executeQuery(
          'SELECT id FROM fd_user_permission WHERE user_id = ? AND permission_id = ?',
          [user_id, permission_id]
        );

        if (!directPermission) {
          // 如果直接权限不存在，创建一个
          await executeQuery(
            `INSERT INTO fd_user_permission (user_id, permission_id, grant_type, data_scope, status, create_time)
             VALUES (?, ?, 'grant', 'all', 1, NOW())`,
            [user_id, permission_id]
          );
        }
        break;

      case 'use_role':
        // 使用角色权限，删除直接权限
        await executeQuery('DELETE FROM fd_user_permission WHERE user_id = ? AND permission_id = ?', [
          user_id,
          permission_id
        ]);
        break;

      case 'merge':
        // 智能合并：使用更宽松的权限设置
        const rolePermissionQuery = `
          SELECT rp.grant_type, rp.data_scope
          FROM fd_user_role ur
          JOIN fd_role_permission rp ON ur.role_id = rp.role_id
          WHERE ur.user_id = ? AND rp.permission_id = ? AND ur.status = 1 AND rp.status = 1
          LIMIT 1
        `;
        const [rolePermission] = await executeQuery(rolePermissionQuery, [user_id, permission_id]);

        if (rolePermission) {
          // 更新或创建直接权限，使用更宽松的设置
          const mergedGrantType = 'grant'; // 优先授予权限
          const mergedDataScope = 'all'; // 使用最宽的数据范围

          await executeQuery(
            `INSERT INTO fd_user_permission (user_id, permission_id, grant_type, data_scope, status, create_time)
             VALUES (?, ?, ?, ?, 1, NOW())
             ON DUPLICATE KEY UPDATE
             grant_type = VALUES(grant_type),
             data_scope = VALUES(data_scope),
             update_time = NOW()`,
            [user_id, permission_id, mergedGrantType, mergedDataScope]
          );
        }
        break;

      default:
        return res.json(createErrorResponse('无效的解决方案', ResponseCode.PARAM_ERROR));
    }

    return res.json(createSuccessResponse(null, '权限冲突解决成功'));
  } catch (error) {
    console.error('👥 [用户管理] 解决权限冲突失败:', error);
    return res.json(createErrorResponse('解决权限冲突失败', ResponseCode.ERROR));
  }
}

/** 获取用户简单权限 */
export async function getUserSimplePermissions(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const userId = Number.parseInt(req.params.userId);

    if (!userId) {
      return res.json(createErrorResponse('用户ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查用户是否存在
    const [user] = await executeQuery('SELECT user_id, username FROM fd_user WHERE user_id = ?', [userId]);
    if (!user) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.NOT_FOUND));
    }

    // 获取用户的路由权限（基于现有权限系统）
    const routePermissions = await executeQuery(
      `
      SELECT DISTINCT p.permission_code, p.permission_name, p.permission_type
      FROM fd_user_permission up
      JOIN fd_permission p ON up.permission_id = p.permission_id
      WHERE up.user_id = ? AND up.status = 1 AND p.status = 1
      AND p.permission_type IN ('menu', 'api')
      AND up.grant_type = 'grant'
    `,
      [userId]
    );

    // 获取用户的功能权限
    const functionPermissions = await executeQuery(
      `
      SELECT DISTINCT p.permission_code, p.permission_name
      FROM fd_user_permission up
      JOIN fd_permission p ON up.permission_id = p.permission_id
      WHERE up.user_id = ? AND up.status = 1 AND p.status = 1
      AND p.permission_type = 'button'
      AND up.grant_type = 'grant'
    `,
      [userId]
    );

    // 转换为前端需要的格式
    const routes = (routePermissions as any[]).map(p => p.permission_code);
    const functions: Record<string, boolean> = {};
    (functionPermissions as any[]).forEach(p => {
      functions[p.permission_code] = true;
    });

    return res.json(
      createSuccessResponse(
        {
          routes,
          functions
        },
        '获取权限成功'
      )
    );
  } catch (error) {
    console.error('👥 [用户管理] 获取用户简单权限失败:', error);
    return res.json(createErrorResponse('获取用户简单权限失败', ResponseCode.ERROR));
  }
}

/** 保存用户简单权限 */
export async function saveUserSimplePermissions(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const userId = Number.parseInt(req.params.userId);
    const { routes, functions } = req.body;

    if (!userId) {
      return res.json(createErrorResponse('用户ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查用户是否存在
    const [user] = await executeQuery('SELECT user_id, username FROM fd_user WHERE user_id = ?', [userId]);
    if (!user) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.NOT_FOUND));
    }

    // 获取操作者信息
    const operatorId = req.user?.userId;

    // 先删除用户现有的直接权限
    await executeQuery('DELETE FROM fd_user_permission WHERE user_id = ?', [userId]);

    // 获取所有权限映射
    const allPermissions = await executeQuery(
      'SELECT permission_id, permission_code, permission_type FROM fd_permission WHERE status = 1'
    );
    const permissionMap = new Map();
    (allPermissions as any[]).forEach(p => {
      permissionMap.set(p.permission_code, p);
    });

    // 添加路由权限
    for (const routeCode of routes || []) {
      const permission = permissionMap.get(routeCode);
      if (permission) {
        await executeQuery(
          'INSERT INTO fd_user_permission (user_id, permission_id, grant_type, status, create_time, granted_by) VALUES (?, ?, ?, ?, NOW(), ?)',
          [userId, permission.permission_id, 'grant', 1, operatorId]
        );
      }
    }

    // 添加功能权限
    for (const [functionCode, enabled] of Object.entries(functions || {})) {
      if (enabled) {
        const permission = permissionMap.get(functionCode);
        if (permission) {
          await executeQuery(
            'INSERT INTO fd_user_permission (user_id, permission_id, grant_type, status, create_time, granted_by) VALUES (?, ?, ?, ?, NOW(), ?)',
            [userId, permission.permission_id, 'grant', 1, operatorId]
          );
        }
      }
    }

    // 使用日志记录模块记录权限操作
    await logUserOperation(
      'update_permissions',
      userId,
      { routes: [], functions: {} }, // 简化的操作前数据
      { routes: routes || [], functions: functions || {} },
      operatorId,
      'admin'
    );

    return res.json(createSuccessResponse(null, '权限保存成功'));
  } catch (error) {
    console.error('👥 [用户管理] 保存用户简单权限失败:', error);
    return res.json(createErrorResponse('保存用户简单权限失败', ResponseCode.ERROR));
  }
}

/** 获取用户功能限制 */
export async function getUserLimitations(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const userId = Number.parseInt(req.params.userId);

    if (!userId) {
      return res.json(createErrorResponse('用户ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查用户是否存在
    const [user] = await executeQuery('SELECT user_id, username FROM fd_user WHERE user_id = ?', [userId]);
    if (!user) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.NOT_FOUND));
    }

    // 获取用户功能限制配置
    const [limitation] = await executeQuery('SELECT limitations FROM fd_user_limitations WHERE user_id = ?', [userId]);

    let limitations: Record<string, any> = {};

    if (limitation) {
      limitations = limitation.limitations ? JSON.parse(limitation.limitations) : {};
    }

    return res.json(
      createSuccessResponse(
        {
          limitations
        },
        '获取功能限制成功'
      )
    );
  } catch (error) {
    console.error('👥 [用户管理] 获取用户功能限制失败:', error);
    return res.json(createErrorResponse('获取用户功能限制失败', ResponseCode.ERROR));
  }
}

/** 保存用户功能限制 */
export async function saveUserLimitations(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const userId = Number.parseInt(req.params.userId);
    const { limitations } = req.body;

    if (!userId) {
      return res.json(createErrorResponse('用户ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查用户是否存在
    const [user] = await executeQuery('SELECT user_id, username FROM fd_user WHERE user_id = ?', [userId]);
    if (!user) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.NOT_FOUND));
    }

    // 检查限制记录是否存在
    const [existing] = await executeQuery('SELECT id FROM fd_user_limitations WHERE user_id = ?', [userId]);

    const limitationsJson = JSON.stringify(limitations || {});

    if (existing) {
      // 更新现有限制
      await executeQuery('UPDATE fd_user_limitations SET limitations = ?, updated_at = NOW() WHERE user_id = ?', [
        limitationsJson,
        userId
      ]);
    } else {
      // 创建新限制记录
      await executeQuery(
        'INSERT INTO fd_user_limitations (user_id, limitations, created_at, updated_at) VALUES (?, ?, NOW(), NOW())',
        [userId, limitationsJson]
      );
    }

    return res.json(createSuccessResponse(null, '功能限制保存成功'));
  } catch (error) {
    console.error('👥 [用户管理] 保存用户功能限制失败:', error);
    return res.json(createErrorResponse('保存用户功能限制失败', ResponseCode.ERROR));
  }
}

/** 用户充值 */
export async function rechargeUserBalance(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { user_id, amount, remark } = req.body;

    if (!user_id || !amount || amount <= 0) {
      return res.json(createErrorResponse('参数错误', ResponseCode.PARAM_ERROR));
    }

    // 检查用户是否存在
    const [user] = await executeQuery('SELECT user_id, username, balance FROM fd_user WHERE user_id = ?', [user_id]);
    if (!user) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.NOT_FOUND));
    }

    const oldBalance = user.balance;
    const newBalance = oldBalance + amount;
    const operatorId = req.user?.userId;

    // 更新用户余额
    await executeQuery('UPDATE fd_user SET balance = ? WHERE user_id = ?', [newBalance, user_id]);

    // 使用日志记录模块记录操作
    await logOperation({
      user_id: operatorId || 0,
      operator_type: 'admin',
      module: 'finance',
      action: 'recharge',
      target_type: 'user',
      target_id: user_id,
      content: `用户充值：${amount}元，备注：${remark || '管理员充值'}`,
      data_before: { balance: oldBalance },
      data_after: { balance: newBalance, amount, remark },
      result: 'success',
      request: req
    });

    return res.json(
      createSuccessResponse(
        {
          user_id,
          username: user.username,
          amount,
          balance_before: oldBalance,
          balance_after: newBalance
        },
        '充值成功'
      )
    );
  } catch (error) {
    console.error('👥 [用户管理] 用户充值失败:', error);
    return res.json(createErrorResponse('充值失败', ResponseCode.ERROR));
  }
}

/** 用户扣费 */
export async function deductUserBalance(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { user_id, amount, remark } = req.body;

    if (!user_id || !amount || amount <= 0) {
      return res.json(createErrorResponse('参数错误', ResponseCode.PARAM_ERROR));
    }

    // 检查用户是否存在
    const [user] = await executeQuery('SELECT user_id, username, balance FROM fd_user WHERE user_id = ?', [user_id]);
    if (!user) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.NOT_FOUND));
    }

    const oldBalance = user.balance;

    // 检查余额是否足够
    if (oldBalance < amount) {
      return res.json(createErrorResponse('用户余额不足', ResponseCode.PARAM_ERROR));
    }

    const newBalance = oldBalance - amount;
    const operatorId = req.user?.userId;

    // 更新用户余额
    await executeQuery('UPDATE fd_user SET balance = ? WHERE user_id = ?', [newBalance, user_id]);

    // 使用日志记录模块记录操作
    await logOperation({
      user_id: operatorId || 0,
      operator_type: 'admin',
      module: 'finance',
      action: 'deduct',
      target_type: 'user',
      target_id: user_id,
      content: `用户扣费：${amount}元，备注：${remark || '管理员扣费'}`,
      data_before: { balance: oldBalance },
      data_after: { balance: newBalance, amount, remark },
      result: 'success',
      request: req
    });

    return res.json(
      createSuccessResponse(
        {
          user_id,
          username: user.username,
          amount,
          balance_before: oldBalance,
          balance_after: newBalance
        },
        '扣费成功'
      )
    );
  } catch (error) {
    console.error('👥 [用户管理] 用户扣费失败:', error);
    return res.json(createErrorResponse('扣费失败', ResponseCode.ERROR));
  }
}

/** 获取用户余额记录 */
/** 获取用户的下级用户（邀请的用户） */
export async function getUserInvitees(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { userId } = req.params;
    const { limit = 10, offset = 0 } = req.query;

    if (!userId) {
      return res.json(createErrorResponse('用户ID不能为空', ResponseCode.PARAM_ERROR));
    }

    console.log('📊 [用户管理] 获取用户下级用户:', { userId, limit, offset });

    // 查询下级用户列表
    const inviteesQuery = `
      SELECT
        user_id as userId,
        username,
        nickname,
        email,
        status,
        create_time as createTime,
        balance
      FROM fd_user
      WHERE referrer_id = ?
      ORDER BY create_time DESC
      LIMIT ?
    `;

    const invitees = await executeQuery(inviteesQuery, [userId, limit]);

    // 查询总数
    const countQuery = `SELECT COUNT(*) as total FROM fd_user WHERE referrer_id = ?`;
    const countResult = await executeQuery(countQuery, [userId]);
    const total = countResult[0]?.total || 0;

    return res.json(
      createSuccessResponse(
        {
          list: invitees,
          total,
          limit: Number(limit),
          offset: Number(offset)
        },
        '获取下级用户成功'
      )
    );
  } catch (error: any) {
    console.error('📊 [用户管理] 获取下级用户失败:', error);
    return res.json(createErrorResponse(error.message || '获取下级用户失败', ResponseCode.ERROR));
  }
}

/** 获取用户统计信息 */
export async function getUserStatistics(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { userId } = req.params;

    if (!userId) {
      return res.json(createErrorResponse('用户ID不能为空', ResponseCode.PARAM_ERROR));
    }

    console.log('📊 [用户管理] 获取用户统计信息:', { userId });

    // 获取用户基本信息
    const userQuery = `
      SELECT
        user_id as userId,
        username,
        nickname,
        balance,
        total_recharge,
        create_time as createTime
      FROM fd_user
      WHERE user_id = ?
    `;
    const userResult = await executeQuery(userQuery, [Number(userId)]);

    if (userResult.length === 0) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.PARAM_ERROR));
    }

    const user = userResult[0];

    // 获取订单统计
    const orderStatsQuery = `
      SELECT
        COUNT(*) as totalOrders,
        SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completedOrders,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pendingOrders,
        SUM(amount) as totalAmount
      FROM fd_order
      WHERE user_id = ?
    `;
    const orderStats = await executeQuery(orderStatsQuery, [Number(userId)]);

    // 获取下级用户统计
    const inviteStatsQuery = `
      SELECT
        COUNT(*) as totalInvitees,
        SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as activeInvitees
      FROM fd_user
      WHERE referrer_id = ?
    `;
    const inviteStats = await executeQuery(inviteStatsQuery, [Number(userId)]);

    const stats = {
      user,
      orderStats: orderStats[0] || { totalOrders: 0, completedOrders: 0, pendingOrders: 0, totalAmount: 0 },
      inviteStats: inviteStats[0] || { totalInvitees: 0, activeInvitees: 0 }
    };

    return res.json(createSuccessResponse(stats, '获取用户统计成功'));
  } catch (error: any) {
    console.error('📊 [用户管理] 获取用户统计失败:', error);
    return res.json(createErrorResponse(error.message || '获取用户统计失败', ResponseCode.ERROR));
  }
}

export async function getUserBalanceRecords(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const userId = Number.parseInt(req.params.userId);
    const { page = 1, size = 10 } = req.query;

    if (!userId) {
      return res.json(createErrorResponse('用户ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查用户是否存在
    const [user] = await executeQuery('SELECT user_id, username, balance FROM fd_user WHERE user_id = ?', [userId]);
    if (!user) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.NOT_FOUND));
    }

    const offset = (Number(page) - 1) * Number(size);

    // 获取余额记录（从日志表中查询）
    const records = await executeQuery(
      `SELECT
        action as operation_type,
        content,
        data_before,
        data_after,
        create_time,
        operator_id
      FROM fd_log
      WHERE user_id = ? AND module = 'finance' AND action IN ('recharge', 'deduct', 'consume', 'refund')
      ORDER BY create_time DESC
      LIMIT ? OFFSET ?`,
      [userId, Number(size), offset]
    );

    // 获取总数
    const [countResult] = await executeQuery(
      `SELECT COUNT(*) as total FROM fd_log
       WHERE user_id = ? AND module = 'finance' AND action IN ('recharge', 'deduct', 'consume', 'refund')`,
      [userId]
    );

    // 处理记录数据
    const processedRecords = (records as any[]).map(record => {
      const dataBefore = record.data_before ? JSON.parse(record.data_before) : {};
      const dataAfter = record.data_after ? JSON.parse(record.data_after) : {};

      return {
        operation_type: record.operation_type,
        amount: dataAfter.amount || 0,
        balance_before: dataBefore.balance || 0,
        balance_after: dataAfter.balance || 0,
        remark: dataAfter.remark || record.content,
        created_at: record.create_time,
        operator_id: record.operator_id
      };
    });

    return res.json(
      createSuccessResponse(
        {
          records: processedRecords,
          total: countResult.total,
          current_balance: user.balance
        },
        '获取余额记录成功'
      )
    );
  } catch (error) {
    console.error('👥 [用户管理] 获取用户余额记录失败:', error);
    return res.json(createErrorResponse('获取用户余额记录失败', ResponseCode.ERROR));
  }
}
