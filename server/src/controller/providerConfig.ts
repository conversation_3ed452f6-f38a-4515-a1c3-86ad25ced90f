/**
 * 货源配置管理控制器
 * 提供货源字段映射、接口配置等管理功能
 */

import type { Request, Response } from 'express';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import { UnifiedMappingEngine } from '../service/provider/UnifiedMappingEngine';
import { ConfigValidator } from '../service/provider/ConfigValidator';

interface AuthenticatedRequest extends Request {
  user?: {
    userId: number;
    userName: string;
    role: number;
  };
}

/** 获取货源统一配置 */
export async function getProviderUnifiedConfig(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;

    if (!providerId) {
      return res.json(createErrorResponse('货源ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 获取统一配置
    const unifiedConfig = await UnifiedMappingEngine.getProviderConfig(Number(providerId));

    if (!unifiedConfig) {
      return res.json(createErrorResponse('货源配置不存在或未迁移到统一配置', ResponseCode.PARAM_ERROR));
    }

    // 验证配置
    const validation = ConfigValidator.validateProviderConfig(unifiedConfig);

    return res.json(
      createSuccessResponse({
        config: unifiedConfig,
        validation,
        report: ConfigValidator.generateConfigReport(unifiedConfig)
      })
    );
  } catch (error: any) {
    console.error('获取货源统一配置失败:', error);
    return res.json(createErrorResponse(error.message || '获取配置失败', ResponseCode.SYSTEM_ERROR));
  }
}

/** 保存货源统一配置 */
export async function saveProviderUnifiedConfig(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;
    const { config } = req.body;

    if (!providerId) {
      return res.json(createErrorResponse('货源ID不能为空', ResponseCode.PARAM_ERROR));
    }

    if (!config) {
      return res.json(createErrorResponse('配置数据不能为空', ResponseCode.PARAM_ERROR));
    }

    // 验证配置
    const validation = ConfigValidator.validateProviderConfig(config);
    if (!validation.valid) {
      return res.json(
        createErrorResponse(
          `配置验证失败: ${validation.errors.map(e => e.message).join(', ')}`,
          ResponseCode.PARAM_ERROR
        )
      );
    }

    // 更新配置
    config.updated_at = new Date().toISOString();

    const updateQuery = `
      UPDATE fd_provider
      SET unified_config = ?, config_version = ?, is_unified_config = 1, update_time = NOW()
      WHERE provider_id = ?
    `;

    await executeQuery(updateQuery, [JSON.stringify(config), config.version, providerId]);

    return res.json(
      createSuccessResponse({
        message: '配置保存成功',
        validation
      })
    );
  } catch (error: any) {
    console.error('保存货源统一配置失败:', error);
    return res.json(createErrorResponse(error.message || '保存配置失败', ResponseCode.SYSTEM_ERROR));
  }
}

/** 获取货源接口配置列表（兼容旧版本） */
export async function getProviderInterfaces(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;

    if (!providerId) {
      return res.json(createErrorResponse('货源ID不能为空', ResponseCode.PARAM_ERROR));
    }

    const query = `
      SELECT
        pi.interface_id,
        pi.interface_type,
        pi.endpoint_url,
        pi.http_method,
        pi.request_template,
        pi.response_mapping,
        pi.custom_code,
        pi.is_enabled,
        pi.create_time,
        pi.update_time,
        p.name as provider_name,
        p.code as provider_code,
        p.field_mapping
      FROM fd_provider_interface pi
      JOIN fd_provider p ON pi.provider_id = p.provider_id
      WHERE pi.provider_id = ?
      ORDER BY pi.interface_type
    `;

    const interfaces = await executeQuery(query, [providerId]);

    // 处理JSON字段
    const processedInterfaces = interfaces.map((item: any) => ({
      ...item,
      request_template: item.request_template
        ? typeof item.request_template === 'string'
          ? JSON.parse(item.request_template)
          : item.request_template
        : null,
      response_mapping: item.response_mapping
        ? typeof item.response_mapping === 'string'
          ? JSON.parse(item.response_mapping)
          : item.response_mapping
        : null,
      field_mapping: item.field_mapping
        ? typeof item.field_mapping === 'string'
          ? JSON.parse(item.field_mapping)
          : item.field_mapping
        : null
    }));

    return res.json(createSuccessResponse(processedInterfaces, '获取接口配置成功'));
  } catch {
    return res.json(createErrorResponse('获取接口配置失败', ResponseCode.ERROR));
  }
}

/** 创建或更新货源接口配置 */
export async function saveProviderInterface(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;
    const {
      interfaceType,
      endpointUrl,
      httpMethod = 'POST',
      requestTemplate,
      responseMapping,
      fieldMapping,
      customCode,
      isEnabled = 1
    } = req.body;

    if (!providerId || !interfaceType || !endpointUrl) {
      return res.json(createErrorResponse('必填参数不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查是否已存在
    const existingQuery = `
      SELECT interface_id FROM fd_provider_interface
      WHERE provider_id = ? AND interface_type = ?
    `;
    const existing = await executeQuery(existingQuery, [providerId, interfaceType]);

    if (existing.length > 0) {
      // 更新
      await executeQuery(
        `UPDATE fd_provider_interface SET
          endpoint_url = ?, http_method = ?, request_template = ?,
          response_mapping = ?, custom_code = ?, is_enabled = ?,
          update_time = NOW()
         WHERE interface_id = ?`,
        [
          endpointUrl,
          httpMethod,
          requestTemplate ? JSON.stringify(requestTemplate) : null,
          responseMapping ? JSON.stringify(responseMapping) : null,
          customCode || null,
          isEnabled,
          existing[0].interface_id
        ]
      );

      // 如果有字段映射，保存到provider表中
      if (fieldMapping && Object.keys(fieldMapping).length > 0) {
        await executeQuery(`UPDATE fd_provider SET field_mapping = ? WHERE provider_id = ?`, [
          JSON.stringify(fieldMapping),
          providerId
        ]);
      }
    } else {
      // 创建
      await executeQuery(
        `INSERT INTO fd_provider_interface
         (provider_id, interface_type, endpoint_url, http_method,
          request_template, response_mapping, custom_code, is_enabled,
          create_time, update_time)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())`,
        [
          providerId,
          interfaceType,
          endpointUrl,
          httpMethod,
          requestTemplate ? JSON.stringify(requestTemplate) : null,
          responseMapping ? JSON.stringify(responseMapping) : null,
          customCode || null,
          isEnabled
        ]
      );

      // 如果有字段映射，保存到provider表中
      if (fieldMapping && Object.keys(fieldMapping).length > 0) {
        await executeQuery(`UPDATE fd_provider SET field_mapping = ? WHERE provider_id = ?`, [
          JSON.stringify(fieldMapping),
          providerId
        ]);
      }
    }

    return res.json(createSuccessResponse(null, '接口配置保存成功'));
  } catch {
    return res.json(createErrorResponse('保存接口配置失败', ResponseCode.ERROR));
  }
}

/** 获取字段映射模板 */
export async function getFieldMappingTemplates(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { templateType } = req.query;

    let query = `
      SELECT
        template_id,
        template_name,
        template_type,
        standard_fields,
        description,
        is_system,
        create_time
      FROM fd_field_mapping_template
    `;

    const params: any[] = [];

    if (templateType) {
      query += ' WHERE template_type = ?';
      params.push(templateType);
    }

    query += ' ORDER BY is_system DESC, template_name';

    const templates = await executeQuery(query, params);

    return res.json(createSuccessResponse(templates, '获取字段映射模板成功'));
  } catch {
    return res.json(createErrorResponse('获取字段映射模板失败', ResponseCode.ERROR));
  }
}

/** 测试货源接口 */
export async function testProviderInterface(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;
    const { interfaceType, testData } = req.body;

    console.log('测试接口参数:', { providerId, interfaceType, testData });

    if (!providerId || !interfaceType || !testData) {
      return res.json(createErrorResponse('测试参数不能为空', ResponseCode.PARAM_ERROR));
    }

    // 先检查接口配置是否存在
    const checkQuery = `
      SELECT interface_id, endpoint_url, http_method, is_enabled
      FROM fd_provider_interface
      WHERE provider_id = ? AND interface_type = ?
    `;
    const checkResult = await executeQuery(checkQuery, [providerId, interfaceType]);
    console.log('接口配置查询结果:', checkResult);

    if (checkResult.length === 0) {
      return res.json(
        createErrorResponse(
          `接口配置不存在 - providerId: ${providerId}, interfaceType: ${interfaceType}`,
          ResponseCode.PARAM_ERROR
        )
      );
    }

    if (checkResult[0].is_enabled !== 1) {
      return res.json(createErrorResponse('接口配置已禁用', ResponseCode.PARAM_ERROR));
    }

    // 获取统一配置
    const unifiedConfig = await UnifiedMappingEngine.getProviderConfig(Number(providerId));

    if (!unifiedConfig) {
      return res.json(createErrorResponse('货源配置不存在或未迁移到统一配置', ResponseCode.PARAM_ERROR));
    }

    // 获取接口配置
    const interfaceConfig = unifiedConfig.interfaces[interfaceType as keyof typeof unifiedConfig.interfaces];
    if (!interfaceConfig || !interfaceConfig.enabled) {
      return res.json(createErrorResponse('接口配置不存在或已禁用', ResponseCode.PARAM_ERROR));
    }

    // 构建请求数据
    const requestMappingResult = UnifiedMappingEngine.mapStandardToProvider(
      testData,
      interfaceConfig.request_mapping,
      unifiedConfig.auth
    );

    if (!requestMappingResult.success) {
      return res.json(createErrorResponse(`请求映射失败: ${requestMappingResult.error}`, ResponseCode.PARAM_ERROR));
    }

    const requestData = requestMappingResult.data;

    // 获取接口配置
    const interfaceQuery = `
      SELECT endpoint_url, http_method
      FROM fd_provider_interface
      WHERE provider_id = ? AND interface_type = ? AND is_enabled = 1
    `;
    const interfaceResult = await executeQuery(interfaceQuery, [providerId, interfaceType]);

    if (interfaceResult.length === 0) {
      return res.json(createErrorResponse('接口配置不存在', ResponseCode.PARAM_ERROR));
    }

    const legacyInterfaceConfig = interfaceResult[0];

    // 检查是否为测试URL，提供模拟响应
    if (legacyInterfaceConfig.endpoint_url.includes('example.com')) {
      console.log('检测到测试URL，返回模拟响应');

      const mockResponse = {
        code: 0,
        msg: '模拟测试成功',
        data: {
          courses: [
            { id: '001', name: '测试课程1', price: 100 },
            { id: '002', name: '测试课程2', price: 200 }
          ]
        }
      };

      const responseMappingResult = UnifiedMappingEngine.mapProviderToStandard(
        mockResponse,
        legacyInterfaceConfig.response_mapping
      );

      const mappedResponse = responseMappingResult.success ? responseMappingResult.data : mockResponse;

      return res.json(
        createSuccessResponse(
          {
            success: true,
            responseTime: 100,
            requestData,
            rawResponse: mockResponse,
            mappedResponse,
            statusCode: 200,
            note: '这是模拟响应，因为使用了测试URL (example.com)'
          },
          '接口测试完成'
        )
      );
    }

    // 发送真实测试请求
    const axios = require('axios');
    const startTime = Date.now();

    try {
      console.log('发送测试请求:', {
        method: legacyInterfaceConfig.http_method,
        url: legacyInterfaceConfig.endpoint_url,
        data: requestData
      });

      // 根据HTTP方法决定参数传递方式
      let axiosConfig;

      if (legacyInterfaceConfig.http_method.toUpperCase() === 'GET') {
        // GET请求：参数放在URL查询字符串中
        axiosConfig = {
          method: 'GET',
          url: legacyInterfaceConfig.endpoint_url,
          params: requestData,
          timeout: 30000,
          headers: {
            'User-Agent': 'FD-Provider-Test/1.0'
          }
        };
      } else {
        // POST请求：使用表单格式而不是JSON
        const URLSearchParams = require('node:url').URLSearchParams;
        const formData = new URLSearchParams();

        // 将请求数据转换为表单格式
        for (const [key, value] of Object.entries(requestData)) {
          formData.append(key, String(value || ''));
        }

        axiosConfig = {
          method: legacyInterfaceConfig.http_method,
          url: legacyInterfaceConfig.endpoint_url,
          data: formData.toString(),
          timeout: 30000,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'User-Agent': 'FD-Provider-Test/1.0'
          }
        };
      }

      console.log('实际发送的请求配置:', axiosConfig);

      const response = await axios({
        ...axiosConfig,
        validateStatus: () => true
      });

      const responseTime = Date.now() - startTime;
      const isSuccess = response.status === 200;

      // 记录测试日志
      await executeQuery(
        `INSERT INTO fd_provider_test_log
         (provider_id, interface_type, test_data, request_data, response_data,
          test_result, response_time, test_time)
         VALUES (?, ?, ?, ?, ?, ?, ?, NOW())`,
        [
          providerId,
          interfaceType,
          JSON.stringify(testData),
          JSON.stringify(requestData),
          JSON.stringify(response.data),
          isSuccess ? 'success' : 'failed',
          responseTime
        ]
      );

      // 解析响应
      const responseMappingResult = UnifiedMappingEngine.mapProviderToStandard(
        response.data,
        legacyInterfaceConfig.response_mapping
      );

      const mappedResponse = responseMappingResult.success ? responseMappingResult.data : response.data;

      return res.json(
        createSuccessResponse(
          {
            success: isSuccess,
            responseTime,
            requestData,
            rawResponse: response.data,
            mappedResponse,
            statusCode: response.status
          },
          '接口测试完成'
        )
      );
    } catch (error: any) {
      const responseTime = Date.now() - startTime;

      // 记录错误日志
      await executeQuery(
        `INSERT INTO fd_provider_test_log
         (provider_id, interface_type, test_data, request_data,
          test_result, error_message, response_time, test_time)
         VALUES (?, ?, ?, ?, ?, ?, ?, NOW())`,
        [
          providerId,
          interfaceType,
          JSON.stringify(testData),
          JSON.stringify(requestData),
          'error',
          error.message,
          responseTime
        ]
      );

      return res.json(
        createSuccessResponse(
          {
            success: false,
            responseTime,
            requestData,
            error: error.message
          },
          '接口测试失败'
        )
      );
    }
  } catch {
    return res.json(createErrorResponse('测试接口失败', ResponseCode.ERROR));
  }
}

/** 获取货源测试日志 */
export async function getProviderTestLogs(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;
    const { page = 1, pageSize = 20, interfaceType } = req.query;

    const offset = (Number(page) - 1) * Number(pageSize);

    let whereClause = 'WHERE ptl.provider_id = ?';
    const params = [providerId];

    if (interfaceType) {
      whereClause += ' AND ptl.interface_type = ?';
      params.push(String(interfaceType));
    }

    // 查询日志列表
    const logsQuery = `
      SELECT
        ptl.log_id,
        ptl.interface_type,
        ptl.test_result,
        ptl.error_message,
        ptl.response_time,
        ptl.test_time,
        p.name as provider_name
      FROM fd_provider_test_log ptl
      JOIN fd_provider p ON ptl.provider_id = p.provider_id
      ${whereClause}
      ORDER BY ptl.test_time DESC
      LIMIT ? OFFSET ?
    `;

    const logs = await executeQuery(logsQuery, [...params, Number(pageSize), offset]);

    // 查询总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM fd_provider_test_log ptl
      ${whereClause}
    `;
    const countResult = await executeQuery(countQuery, params);
    const total = countResult[0]?.total || 0;

    return res.json(
      createSuccessResponse(
        {
          list: logs,
          pagination: {
            page: Number(page),
            pageSize: Number(pageSize),
            total,
            totalPages: Math.ceil(total / Number(pageSize))
          }
        },
        '获取测试日志成功'
      )
    );
  } catch {
    return res.json(createErrorResponse('获取测试日志失败', ResponseCode.ERROR));
  }
}

/** 更新货源字段映射 */
export async function updateProviderFieldMapping(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;
    const { fieldMapping } = req.body;

    if (!providerId || !fieldMapping) {
      return res.json(createErrorResponse('参数不能为空', ResponseCode.PARAM_ERROR));
    }

    await executeQuery('UPDATE fd_provider SET field_mapping = ?, update_time = NOW() WHERE provider_id = ?', [
      JSON.stringify(fieldMapping),
      providerId
    ]);

    return res.json(createSuccessResponse(null, '字段映射更新成功'));
  } catch {
    return res.json(createErrorResponse('更新字段映射失败', ResponseCode.ERROR));
  }
}

/** 获取接口类型定义 */
export async function getInterfaceDefinitions(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    // 返回所有接口类型的定义信息
    const definitions = [
      {
        type: 'query',
        name: '查课接口',
        description: '查询学生的课程信息和进度',
        category: 'basic',
        icon: 'Search',
        color: '#409EFF'
      },
      {
        type: 'order',
        name: '下单接口',
        description: '创建新的刷课订单',
        category: 'basic',
        icon: 'Plus',
        color: '#67C23A'
      },
      {
        type: 'sync',
        name: '同步接口',
        description: '同步订单状态和进度',
        category: 'basic',
        icon: 'Refresh',
        color: '#E6A23C'
      },
      {
        type: 'refill',
        name: '补刷接口',
        description: '重新执行未完成的订单',
        category: 'basic',
        icon: 'RefreshRight',
        color: '#F56C6C'
      },
      {
        type: 'change_password',
        name: '改密接口',
        description: '修改学生账户密码',
        category: 'basic',
        icon: 'Key',
        color: '#909399'
      },
      {
        type: 'get_balance',
        name: '余额查询',
        description: '查询货源账户余额',
        category: 'management',
        icon: 'Money',
        color: '#67C23A'
      },
      {
        type: 'pause_order',
        name: '暂停订单',
        description: '暂停正在执行的订单',
        category: 'management',
        icon: 'VideoPause',
        color: '#E6A23C'
      },
      {
        type: 'get_order_logs',
        name: '订单日志',
        description: '获取订单执行日志',
        category: 'management',
        icon: 'Document',
        color: '#909399'
      }
    ];

    return res.json(createSuccessResponse(definitions, '获取接口定义成功'));
  } catch {
    return res.json(createErrorResponse('获取接口定义失败', ResponseCode.ERROR));
  }
}

/** 保存向导配置 */
export async function saveWizardConfig(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;
    const { interfaceType, endpointUrl, httpMethod, fieldMapping, requestTemplate, responseMapping, customCode } =
      req.body;

    if (!providerId || !interfaceType || !endpointUrl) {
      return res.json(createErrorResponse('参数不完整', ResponseCode.ERROR));
    }

    // 检查接口是否已存在
    const existingInterface = await executeQuery(
      'SELECT interface_id FROM fd_provider_interface WHERE provider_id = ? AND interface_type = ?',
      [providerId, interfaceType]
    );

    let result;
    if (existingInterface.length > 0) {
      // 更新现有接口
      result = await executeQuery(
        `UPDATE fd_provider_interface
         SET endpoint_url = ?, http_method = ?, request_template = ?, response_mapping = ?,
             custom_code = ?, is_enabled = 1, update_time = NOW()
         WHERE provider_id = ? AND interface_type = ?`,
        [
          endpointUrl,
          httpMethod || 'POST',
          JSON.stringify(requestTemplate),
          JSON.stringify(responseMapping),
          customCode || '',
          providerId,
          interfaceType
        ]
      );
    } else {
      // 创建新接口
      result = await executeQuery(
        `INSERT INTO fd_provider_interface
         (provider_id, interface_type, endpoint_url, http_method, request_template, response_mapping, custom_code, is_enabled, create_time, update_time)
         VALUES (?, ?, ?, ?, ?, ?, ?, 1, NOW(), NOW())`,
        [
          providerId,
          interfaceType,
          endpointUrl,
          httpMethod || 'POST',
          JSON.stringify(requestTemplate),
          JSON.stringify(responseMapping),
          customCode || ''
        ]
      );
    }

    return res.json(
      createSuccessResponse(
        {
          interfaceId: existingInterface.length > 0 ? existingInterface[0].interface_id : result.insertId
        },
        '配置保存成功'
      )
    );
  } catch (error: any) {
    console.error('保存向导配置失败:', error);
    return res.json(createErrorResponse('保存配置失败', ResponseCode.ERROR));
  }
}

// 调试接口 - 检查数据库状态
export async function debugProviderConfig(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId } = req.params;

    // 检查货源是否存在
    const providerQuery = `SELECT provider_id, name, code FROM fd_provider WHERE provider_id = ?`;
    const providerResult = await executeQuery(providerQuery, [providerId]);

    // 检查接口配置
    const interfaceQuery = `SELECT * FROM fd_provider_interface WHERE provider_id = ?`;
    const interfaceResult = await executeQuery(interfaceQuery, [providerId]);

    // 检查表结构
    const tableStructureQuery = `DESCRIBE fd_provider_interface`;
    const tableStructure = await executeQuery(tableStructureQuery, []);

    return res.json(
      createSuccessResponse(
        {
          provider: providerResult,
          interfaces: interfaceResult,
          tableStructure
        },
        '调试信息'
      )
    );
  } catch (error) {
    console.error('调试接口失败:', error);
    return res.json(createErrorResponse('调试接口失败', ResponseCode.ERROR));
  }
}

/** 测试统一配置接口 */
export async function testUnifiedConfig(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { providerId, interfaceType } = req.params;
    const { testData } = req.body;

    if (!providerId || !interfaceType) {
      return res.json(createErrorResponse('货源ID和接口类型不能为空', ResponseCode.PARAM_ERROR));
    }

    if (!testData) {
      return res.json(createErrorResponse('测试数据不能为空', ResponseCode.PARAM_ERROR));
    }

    // 获取统一配置
    const unifiedConfig = await UnifiedMappingEngine.getProviderConfig(Number(providerId));

    if (!unifiedConfig) {
      return res.json(createErrorResponse('货源配置不存在或未迁移到统一配置', ResponseCode.PARAM_ERROR));
    }

    // 获取接口配置
    const interfaceConfig = unifiedConfig.interfaces[interfaceType as keyof typeof unifiedConfig.interfaces];
    if (!interfaceConfig || !interfaceConfig.enabled) {
      return res.json(createErrorResponse('接口配置不存在或已禁用', ResponseCode.PARAM_ERROR));
    }

    // 验证接口配置
    const configValidation = UnifiedMappingEngine.validateMappingConfig(interfaceConfig);
    if (!configValidation.valid) {
      return res.json(
        createErrorResponse(`接口配置无效: ${configValidation.errors.join(', ')}`, ResponseCode.PARAM_ERROR)
      );
    }

    // 构建请求数据
    const requestMappingResult = UnifiedMappingEngine.mapStandardToProvider(
      testData,
      interfaceConfig.request_mapping,
      unifiedConfig.auth
    );

    if (!requestMappingResult.success) {
      return res.json(createErrorResponse(`请求映射失败: ${requestMappingResult.error}`, ResponseCode.PARAM_ERROR));
    }

    const requestData = requestMappingResult.data;

    // 构建请求URL
    const baseUrl = unifiedConfig.provider_info.base_url || '';
    const url = interfaceConfig.http.endpoint.startsWith('http')
      ? interfaceConfig.http.endpoint
      : baseUrl + interfaceConfig.http.endpoint;

    // 检查是否为测试URL，提供模拟响应
    if (url.includes('example.com') || url.includes('test.com')) {
      console.log('检测到测试URL，返回模拟响应');

      const mockResponse = {
        code: 0,
        msg: '模拟测试成功',
        data: {
          courses: [
            { id: '001', name: '测试课程1', price: 100 },
            { id: '002', name: '测试课程2', price: 200 }
          ]
        }
      };

      const responseMappingResult = UnifiedMappingEngine.mapProviderToStandard(
        mockResponse,
        interfaceConfig.response_mapping
      );

      const mappedResponse = responseMappingResult.success ? responseMappingResult.data : mockResponse;

      return res.json(
        createSuccessResponse({
          success: true,
          responseTime: 100,
          requestData,
          rawResponse: mockResponse,
          mappedResponse,
          isMockResponse: true
        })
      );
    }

    // 发送真实HTTP请求
    const startTime = Date.now();

    try {
      const httpConfig: any = {
        method: interfaceConfig.http.method,
        url,
        timeout: interfaceConfig.http.timeout || 30000,
        headers: {
          'Content-Type': getContentType(interfaceConfig.http.content_type),
          ...interfaceConfig.http.headers
        }
      };

      if (interfaceConfig.http.method === 'GET') {
        httpConfig.params = requestData;
      } else if (interfaceConfig.http.content_type === 'json') {
        httpConfig.data = requestData;
      } else {
        // form格式
        const formData = new URLSearchParams();
        for (const [key, value] of Object.entries(requestData)) {
          formData.append(key, String(value));
        }
        httpConfig.data = formData;
      }

      const axios = require('axios');
      const response = await axios(httpConfig);
      const responseTime = Date.now() - startTime;

      // 解析响应
      const responseMappingResult = UnifiedMappingEngine.mapProviderToStandard(
        response.data,
        interfaceConfig.response_mapping
      );

      const mappedResponse = responseMappingResult.success ? responseMappingResult.data : response.data;

      return res.json(
        createSuccessResponse({
          success: mappedResponse.success || false,
          responseTime,
          requestData,
          rawResponse: response.data,
          mappedResponse,
          isMockResponse: false
        })
      );
    } catch (error: any) {
      const responseTime = Date.now() - startTime;

      return res.json(
        createSuccessResponse({
          success: false,
          responseTime,
          requestData,
          error: error.message || '请求失败',
          isMockResponse: false
        })
      );
    }
  } catch (error: any) {
    console.error('测试统一配置失败:', error);
    return res.json(createErrorResponse(error.message || '测试失败', ResponseCode.SYSTEM_ERROR));
  }
}

// 辅助函数
function getContentType(contentType?: string): string {
  switch (contentType) {
    case 'json':
      return 'application/json';
    case 'xml':
      return 'application/xml';
    case 'form':
    default:
      return 'application/x-www-form-urlencoded';
  }
}
