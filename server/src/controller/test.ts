import type { Response } from 'express';
import { Request } from 'express';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import { systemTester } from '../utils/systemTest';
import type { AuthenticatedRequest } from '../middleware/auth';

/**
 * 测试控制器
 * 提供系统测试和验证功能
 */

/** 运行完整系统测试 */
export async function runSystemTest(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    console.log('🧪 [测试控制器] 开始运行系统测试...');

    const testResults = await systemTester.runFullSystemTest();

    console.log('✅ [测试控制器] 系统测试完成');

    return res.json(createSuccessResponse(testResults, '系统测试完成'));
  } catch (error) {
    console.error('❌ [测试控制器] 系统测试失败:', error);
    return res.json(createErrorResponse('系统测试失败', ResponseCode.ERROR));
  }
}

/** 运行数据库测试 */
export async function runDatabaseTest(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    console.log('🧪 [测试控制器] 开始运行数据库测试...');

    const testResults = await systemTester.runDatabaseTests();

    console.log('✅ [测试控制器] 数据库测试完成');

    return res.json(createSuccessResponse(testResults, '数据库测试完成'));
  } catch (error) {
    console.error('❌ [测试控制器] 数据库测试失败:', error);
    return res.json(createErrorResponse('数据库测试失败', ResponseCode.ERROR));
  }
}

/** 运行缓存测试 */
export async function runCacheTest(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    console.log('🧪 [测试控制器] 开始运行缓存测试...');

    const testResults = await systemTester.runCacheTests();

    console.log('✅ [测试控制器] 缓存测试完成');

    return res.json(createSuccessResponse(testResults, '缓存测试完成'));
  } catch (error) {
    console.error('❌ [测试控制器] 缓存测试失败:', error);
    return res.json(createErrorResponse('缓存测试失败', ResponseCode.ERROR));
  }
}

/** 运行性能测试 */
export async function runPerformanceTest(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    console.log('🧪 [测试控制器] 开始运行性能测试...');

    const testResults = await systemTester.runPerformanceTests();

    console.log('✅ [测试控制器] 性能测试完成');

    return res.json(createSuccessResponse(testResults, '性能测试完成'));
  } catch (error) {
    console.error('❌ [测试控制器] 性能测试失败:', error);
    return res.json(createErrorResponse('性能测试失败', ResponseCode.ERROR));
  }
}

/** 运行安全测试 */
export async function runSecurityTest(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    console.log('🧪 [测试控制器] 开始运行安全测试...');

    const testResults = await systemTester.runSecurityTests();

    console.log('✅ [测试控制器] 安全测试完成');

    return res.json(createSuccessResponse(testResults, '安全测试完成'));
  } catch (error) {
    console.error('❌ [测试控制器] 安全测试失败:', error);
    return res.json(createErrorResponse('安全测试失败', ResponseCode.ERROR));
  }
}

/** 运行API测试 */
export async function runAPITest(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    console.log('🧪 [测试控制器] 开始运行API测试...');

    const testResults = await systemTester.runAPITests();

    console.log('✅ [测试控制器] API测试完成');

    return res.json(createSuccessResponse(testResults, 'API测试完成'));
  } catch (error) {
    console.error('❌ [测试控制器] API测试失败:', error);
    return res.json(createErrorResponse('API测试失败', ResponseCode.ERROR));
  }
}

/** 运行系统健康测试 */
export async function runHealthTest(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    console.log('🧪 [测试控制器] 开始运行系统健康测试...');

    const testResults = await systemTester.runSystemHealthTests();

    console.log('✅ [测试控制器] 系统健康测试完成');

    return res.json(createSuccessResponse(testResults, '系统健康测试完成'));
  } catch (error) {
    console.error('❌ [测试控制器] 系统健康测试失败:', error);
    return res.json(createErrorResponse('系统健康测试失败', ResponseCode.ERROR));
  }
}
