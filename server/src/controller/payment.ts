import type { Request, Response } from 'express';
import { ResultSetHeader } from 'mysql2';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import { Finance, FinanceType, Payment, PaymentStatus, RechargeConfig } from '../model/payment';

/**
 * 获取支付记录列表
 */
export async function getPaymentList(req: Request, res: Response) {
  try {
    const { page = 1, pageSize = 10, status, payment_type, user_id } = req.query;
    const offset = (Number(page) - 1) * Number(pageSize);

    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    if (status !== undefined) {
      whereClause += ' AND p.status = ?';
      params.push(status);
    }

    if (payment_type) {
      whereClause += ' AND p.payment_type = ?';
      params.push(payment_type);
    }

    if (user_id) {
      whereClause += ' AND p.user_id = ?';
      params.push(user_id);
    }

    // 获取总数
    const countSql = `
      SELECT COUNT(*) as total
      FROM fd_payment p
      ${whereClause}
    `;
    const countResult = await executeQuery(countSql, params);
    const total = countResult[0].total;

    // 获取列表数据
    const listSql = `
      SELECT
        p.*,
        u.username,
        u.nickname
      FROM fd_payment p
      LEFT JOIN fd_user u ON p.user_id = u.user_id
      ${whereClause}
      ORDER BY p.create_time DESC
      LIMIT ? OFFSET ?
    `;

    const rows = await executeQuery(listSql, [...params, Number(pageSize), offset]);

    return res.json(
      createSuccessResponse(
        {
          list: rows,
          pagination: {
            page: Number(page),
            pageSize: Number(pageSize),
            total
          }
        },
        '获取支付记录列表成功'
      )
    );
  } catch (error) {
    console.error('获取支付记录列表失败:', error);
    return res.json(createErrorResponse('获取支付记录列表失败', ResponseCode.ERROR));
  }
}

/**
 * 获取财务记录列表
 */
export async function getFinanceList(req: Request, res: Response) {
  try {
    const { page = 1, pageSize = 10, type, user_id } = req.query;
    const offset = (Number(page) - 1) * Number(pageSize);

    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    if (type) {
      whereClause += ' AND f.type = ?';
      params.push(type);
    }

    if (user_id) {
      whereClause += ' AND f.user_id = ?';
      params.push(user_id);
    }

    // 获取总数
    const countSql = `
      SELECT COUNT(*) as total
      FROM fd_finance f
      ${whereClause}
    `;
    const countResult = await executeQuery(countSql, params);
    const total = countResult[0].total;

    // 获取列表数据
    const listSql = `
      SELECT
        f.*,
        u.username,
        u.nickname,
        op.username as operator_name
      FROM fd_finance f
      LEFT JOIN fd_user u ON f.user_id = u.user_id
      LEFT JOIN fd_user op ON f.operator_id = op.user_id
      ${whereClause}
      ORDER BY f.create_time DESC
      LIMIT ? OFFSET ?
    `;

    const rows = await executeQuery(listSql, [...params, Number(pageSize), offset]);

    return res.json(
      createSuccessResponse(
        {
          list: rows,
          pagination: {
            page: Number(page),
            pageSize: Number(pageSize),
            total
          }
        },
        '获取财务记录列表成功'
      )
    );
  } catch (error) {
    console.error('获取财务记录列表失败:', error);
    return res.json(createErrorResponse('获取财务记录列表失败', ResponseCode.ERROR));
  }
}

/**
 * 创建充值订单
 */
export async function createRechargeOrder(req: Request, res: Response) {
  try {
    const { amount, payment_method } = req.body;
    const user_id = (req as any).user?.userId;

    if (!user_id) {
      return res.json(createErrorResponse('用户未登录', ResponseCode.UNAUTHORIZED));
    }

    if (!amount || amount <= 0) {
      return res.json(createErrorResponse('充值金额必须大于0', ResponseCode.PARAM_ERROR));
    }

    // 用户功能限制校验：禁止在线充值
    try {
      const [limitationRow] = await executeQuery('SELECT limitations FROM fd_user_limitations WHERE user_id = ?', [
        user_id
      ]);
      const limits = limitationRow?.limitations ? JSON.parse(limitationRow.limitations) : {};
      if (limits?.disable_online_recharge === true) {
        return res.json(createErrorResponse('已启用在线充值限制，无法创建充值订单', ResponseCode.FORBIDDEN));
      }
    } catch (e) {
      // 忽略读取限制失败
    }

    if (!payment_method) {
      return res.json(createErrorResponse('请选择支付方式', ResponseCode.PARAM_ERROR));
    }

    // 生成支付单号
    const payment_no = `R${Date.now()}${Math.random().toString(36).substr(2, 6).toUpperCase()}`;

    // 创建支付记录
    const insertSql = `
      INSERT INTO fd_payment (
        user_id, payment_no, payment_type, payment_method, amount, status
      ) VALUES (?, ?, 'recharge', ?, ?, 0)
    `;

    const result = await executeQuery(insertSql, [user_id, payment_no, payment_method, amount]);
    const payment_id = result.insertId;

    // 根据支付方式生成支付链接或二维码
    const paymentData = {
      payment_id,
      payment_no,
      amount,
      payment_method,
      // 这里应该调用对应的支付接口生成支付链接
      payment_url: `#/payment/${payment_no}`, // 临时链接
      qr_code: null
    };

    return res.json(createSuccessResponse(paymentData, '充值订单创建成功'));
  } catch (error) {
    console.error('创建充值订单失败:', error);
    return res.json(createErrorResponse('创建充值订单失败', ResponseCode.ERROR));
  }
}

/**
 * 获取充值配置
 */
export async function getRechargeConfig(req: Request, res: Response) {
  try {
    const sql = `
      SELECT config_id, payment_method, method_name, status, sort_order
      FROM fd_recharge_config
      WHERE status = 1
      ORDER BY sort_order ASC
    `;

    const rows = await executeQuery(sql);

    return res.json(createSuccessResponse(rows, '获取充值配置成功'));
  } catch (error) {
    console.error('获取充值配置失败:', error);
    return res.json(createErrorResponse('获取充值配置失败', ResponseCode.ERROR));
  }
}

/**
 * 更新充值配置（管理员）
 */
export async function updateRechargeConfig(req: Request, res: Response) {
  try {
    const { config_id } = req.params;
    const updateData = req.body;

    // 构建更新SQL
    const fields = Object.keys(updateData).filter(key => key !== 'config_id');
    if (fields.length === 0) {
      return res.json(createErrorResponse('没有要更新的数据', ResponseCode.PARAM_ERROR));
    }

    const setClause = fields.map(field => `${field} = ?`).join(', ');
    const values = fields.map(field => updateData[field]);

    const sql = `UPDATE fd_recharge_config SET ${setClause} WHERE config_id = ?`;
    await executeQuery(sql, [...values, config_id]);

    return res.json(createSuccessResponse(null, '充值配置更新成功'));
  } catch (error) {
    console.error('更新充值配置失败:', error);
    return res.json(createErrorResponse('更新充值配置失败', ResponseCode.ERROR));
  }
}

/**
 * 手动充值（管理员）
 */
export async function manualRecharge(req: Request, res: Response) {
  try {
    const { user_id, amount, description } = req.body;
    const operator_id = (req as any).user?.userId;

    if (!user_id || !amount || amount <= 0) {
      return res.json(createErrorResponse('参数错误', ResponseCode.PARAM_ERROR));
    }

    // 获取用户当前余额
    const userResult = await executeQuery('SELECT balance FROM fd_user WHERE user_id = ?', [user_id]);

    if (userResult.length === 0) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.PARAM_ERROR));
    }

    const currentBalance = userResult[0].balance;
    const newBalance = Number(currentBalance) + Number(amount);

    // 更新用户余额
    await executeQuery('UPDATE fd_user SET balance = ?, total_recharge = total_recharge + ? WHERE user_id = ?', [
      newBalance,
      amount,
      user_id
    ]);

    // 创建财务记录
    await executeQuery(
      `
      INSERT INTO fd_finance (
        user_id, type, amount, balance_before, balance_after,
        related_type, description, operator_id
      ) VALUES (?, 'recharge', ?, ?, ?, 'manual', ?, ?)
    `,
      [user_id, amount, currentBalance, newBalance, description || '管理员手动充值', operator_id]
    );

    return res.json(
      createSuccessResponse(
        {
          user_id,
          amount,
          balance_before: currentBalance,
          balance_after: newBalance
        },
        '充值成功'
      )
    );
  } catch (error) {
    console.error('手动充值失败:', error);
    return res.json(createErrorResponse(error instanceof Error ? error.message : '充值失败', ResponseCode.ERROR));
  }
}

/**
 * 支付回调处理（支付宝）
 */
export async function alipayCallback(req: Request, res: Response) {
  try {
    const callbackData = req.body;
    console.log('🔔 [支付回调] 支付宝回调数据:', callbackData);

    // 这里应该验证支付宝的签名
    // 简化处理，实际项目中需要严格验证
    const { out_trade_no, trade_no, trade_status, total_amount } = callbackData;

    if (trade_status === 'TRADE_SUCCESS') {
      // 查询支付记录
      const paymentQuery = `
        SELECT * FROM fd_payment
        WHERE payment_no = ? AND status = 0
      `;
      const paymentResult = await executeQuery(paymentQuery, [out_trade_no]);

      if (paymentResult.length > 0) {
        const payment = paymentResult[0];

        // 更新支付状态
        await executeQuery(
          `
          UPDATE fd_payment
          SET status = 1, trade_no = ?, pay_time = NOW(), notify_data = ?
          WHERE payment_id = ?
        `,
          [trade_no, JSON.stringify(callbackData), payment.payment_id]
        );

        // 如果是充值，更新用户余额
        if (payment.payment_type === 'recharge') {
          await processRechargeSuccess(payment.user_id, Number(total_amount), payment.payment_id);
        }

        console.log(`🔔 [支付回调] 支付成功处理完成: ${out_trade_no}`);
      }
    }

    // 返回成功响应给支付宝
    res.send('success');
  } catch (error) {
    console.error('🔔 [支付回调] 处理失败:', error);
    res.send('fail');
  }
}

/**
 * 支付回调处理（微信支付）
 */
export async function wechatCallback(req: Request, res: Response) {
  try {
    const callbackData = req.body;
    console.log('🔔 [支付回调] 微信支付回调数据:', callbackData);

    // 这里应该验证微信支付的签名
    // 简化处理，实际项目中需要严格验证
    const { out_trade_no, transaction_id, trade_state, total_fee } = callbackData;

    if (trade_state === 'SUCCESS') {
      // 查询支付记录
      const paymentQuery = `
        SELECT * FROM fd_payment
        WHERE payment_no = ? AND status = 0
      `;
      const paymentResult = await executeQuery(paymentQuery, [out_trade_no]);

      if (paymentResult.length > 0) {
        const payment = paymentResult[0];

        // 更新支付状态
        await executeQuery(
          `
          UPDATE fd_payment
          SET status = 1, trade_no = ?, pay_time = NOW(), notify_data = ?
          WHERE payment_id = ?
        `,
          [transaction_id, JSON.stringify(callbackData), payment.payment_id]
        );

        // 如果是充值，更新用户余额
        if (payment.payment_type === 'recharge') {
          await processRechargeSuccess(payment.user_id, Number(total_fee) / 100, payment.payment_id);
        }

        console.log(`🔔 [支付回调] 支付成功处理完成: ${out_trade_no}`);
      }
    }

    // 返回成功响应给微信支付
    res.send('<xml><return_code><![CDATA[SUCCESS]]></return_code></xml>');
  } catch (error) {
    console.error('🔔 [支付回调] 处理失败:', error);
    res.send('<xml><return_code><![CDATA[FAIL]]></return_code></xml>');
  }
}

/**
 * 处理充值成功
 */
async function processRechargeSuccess(userId: number, amount: number, paymentId: number) {
  try {
    // 获取用户当前余额
    const userResult = await executeQuery('SELECT balance FROM fd_user WHERE user_id = ?', [userId]);

    if (userResult.length === 0) {
      throw new Error('用户不存在');
    }

    const currentBalance = userResult[0].balance;
    const newBalance = Number(currentBalance) + Number(amount);

    // 更新用户余额
    await executeQuery('UPDATE fd_user SET balance = ?, total_recharge = total_recharge + ? WHERE user_id = ?', [
      newBalance,
      amount,
      userId
    ]);

    // 创建财务记录
    await executeQuery(
      `
      INSERT INTO fd_finance (
        user_id, type, amount, balance_before, balance_after,
        related_id, related_type, description
      ) VALUES (?, 'recharge', ?, ?, ?, ?, 'payment', '在线充值')
    `,
      [userId, amount, currentBalance, newBalance, paymentId]
    );

    console.log(`💰 [充值处理] 用户 ${userId} 充值成功: ${amount}`);
  } catch (error) {
    console.error('💰 [充值处理] 处理充值成功失败:', error);
    throw error;
  }
}
