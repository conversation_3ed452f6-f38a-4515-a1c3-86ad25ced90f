/**
 * 商品管理控制器
 * 实现商品的CRUD操作和业务逻辑
 */

import type { Response } from 'express';
import { Request } from 'express';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import type { AuthenticatedRequest } from '../middleware/auth';

/**
 * 获取商品列表
 */
export async function getProductList(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { page = 1, pageSize = 20, categoryId, platformType, status, keyword } = req.query;

    console.log('📦 [商品管理] 获取商品列表:', {
      page,
      pageSize,
      categoryId,
      platformType,
      status,
      keyword
    });

    // 先测试简单查询
    console.log('📦 [商品管理] 开始执行数据库查询...');
    console.log('📦 [商品管理] 数据库配置检查:', {
      host: process.env.DB_HOST,
      user: process.env.DB_USER,
      database: process.env.DB_NAME,
      passwordSet: Boolean(process.env.DB_PASSWORD)
    });

    const testQuery = 'SELECT COUNT(*) as count FROM fd_product';
    console.log('📦 [商品管理] 测试查询:', testQuery);
    const testResult = await executeQuery(testQuery, []);
    console.log('📦 [商品管理] 测试查询结果:', testResult);

    // 构建查询条件
    const whereConditions = ['1=1'];
    const queryParams: any[] = [];

    if (categoryId) {
      whereConditions.push('p.fenlei = ?');
      queryParams.push(categoryId);
    }

    if (platformType) {
      whereConditions.push('p.queryplat = ?');
      queryParams.push(platformType);
    }

    if (status !== undefined) {
      whereConditions.push('p.status = ?');
      queryParams.push(status);
    }

    if (keyword) {
      whereConditions.push('(p.name LIKE ? OR p.content LIKE ?)');
      queryParams.push(`%${keyword}%`, `%${keyword}%`);
    }

    const whereClause = whereConditions.join(' AND ');

    // 获取总数
    const countQuery = `
      SELECT COUNT(*) as total
      FROM fd_product p
      WHERE ${whereClause}
    `;

    const countResult = await executeQuery(countQuery, queryParams);
    const total = countResult[0].total;

    // 获取分页数据
    const offset = (Number(page) - 1) * Number(pageSize);
    const listQuery = `
      SELECT
        p.cid as product_id,
        p.name as product_name,
        p.fenlei as category_id,
        c.name as category_name,
        p.queryplat as provider_id,
        pr.name as provider_name,
        p.price,
        p.content as description,
        p.getnoun,
        p.noun,
        p.docking,
        p.status,
        p.sort as sort_order,
        p.wck,
        p.kcid,
        p.api,
        p.nocheck,
        p.addtime,
        p.create_time,
        p.update_time,
        'network_course' as platform_type,
        'course' as service_type,
        p.price as cost_price
      FROM fd_product p
      LEFT JOIN fd_category c ON p.fenlei = c.category_id
      LEFT JOIN fd_provider pr ON p.queryplat = pr.provider_id
      WHERE ${whereClause}
      ORDER BY p.sort ASC, p.cid DESC
      LIMIT ${Number(pageSize)} OFFSET ${offset}
    `;

    console.log('📦 [商品管理] 列表查询SQL:', listQuery);
    console.log('📦 [商品管理] 列表查询参数:', queryParams);

    const list = await executeQuery(listQuery, queryParams);

    console.log(`📦 [商品管理] 获取商品列表成功，共 ${total} 条记录`);

    return res.json(
      createSuccessResponse(
        {
          list,
          total,
          page: Number(page),
          pageSize: Number(pageSize),
          totalPages: Math.ceil(total / Number(pageSize))
        },
        '获取商品列表成功'
      )
    );
  } catch (error) {
    console.error('📦 [商品管理] 获取商品列表失败:', error);
    console.error('📦 [商品管理] 错误详情:', error instanceof Error ? error.message : error);
    console.error('📦 [商品管理] 错误堆栈:', error instanceof Error ? error.stack : 'No stack trace');
    return res.json(createErrorResponse('获取商品列表失败', ResponseCode.ERROR));
  }
}

/**
 * 获取商品详情
 */
export async function getProductDetail(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { productId } = req.params;

    if (!productId) {
      return res.json(createErrorResponse('商品ID不能为空', ResponseCode.PARAM_ERROR));
    }

    console.log('📦 [商品管理] 获取商品详情:', productId);

    const query = `
      SELECT
        p.*,
        c.name as category_name,
        pr.name as provider_name,
        pr.api_url as provider_api_url
      FROM fd_product p
      LEFT JOIN fd_category c ON p.fenlei = c.category_id
      LEFT JOIN fd_provider pr ON p.queryplat = pr.provider_id
      WHERE p.cid = ?
    `;

    const result = await executeQuery(query, [productId]);

    if (result.length === 0) {
      return res.json(createErrorResponse('商品不存在', ResponseCode.DATA_NOT_FOUND));
    }

    console.log('📦 [商品管理] 获取商品详情成功');
    return res.json(createSuccessResponse(result[0], '获取商品详情成功'));
  } catch (error) {
    console.error('📦 [商品管理] 获取商品详情失败:', error);
    return res.json(createErrorResponse('获取商品详情失败', ResponseCode.ERROR));
  }
}

/**
 * 创建商品（管理员）
 */
export async function createProduct(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const {
      productName,
      categoryId,
      platformType,
      providerId,
      price,
      costPrice,
      serviceType = '代刷服务',
      description,
      status = 1,
      sortOrder = 10
    } = req.body;

    console.log('📦 [商品管理] 创建商品:', { productName, categoryId, platformType });

    // 参数验证
    if (!productName || !categoryId || !platformType || !price) {
      return res.json(createErrorResponse('商品名称、分类、平台类型和价格不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查分类是否存在
    const categoryCheck = await executeQuery('SELECT category_id FROM fd_category WHERE category_id = ?', [categoryId]);
    if (categoryCheck.length === 0) {
      return res.json(createErrorResponse('分类不存在', ResponseCode.PARAM_ERROR));
    }

    // 检查服务商是否存在（如果提供了）
    if (providerId) {
      const providerCheck = await executeQuery('SELECT provider_id FROM fd_provider WHERE provider_id = ?', [
        providerId
      ]);
      if (providerCheck.length === 0) {
        return res.json(createErrorResponse('服务商不存在', ResponseCode.PARAM_ERROR));
      }
    }

    const insertQuery = `
      INSERT INTO fd_product (
        product_name, category_id, platform_type, provider_id,
        price, cost_price, service_type, description,
        status, sort_order, create_time, update_time
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `;

    const result = await executeQuery(insertQuery, [
      productName,
      categoryId,
      platformType,
      providerId || 1, // 默认使用第一个服务商
      price,
      costPrice || 0,
      serviceType,
      description,
      status,
      sortOrder
    ]);

    console.log('📦 [商品管理] 创建商品成功:', result.insertId);
    return res.json(createSuccessResponse({ productId: result.insertId }, '创建商品成功'));
  } catch (error) {
    console.error('📦 [商品管理] 创建商品失败:', error);
    return res.json(createErrorResponse('创建商品失败', ResponseCode.ERROR));
  }
}

/**
 * 更新商品（管理员）
 */
export async function updateProduct(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { productId } = req.params;
    const updateData = req.body;

    if (!productId) {
      return res.json(createErrorResponse('商品ID不能为空', ResponseCode.PARAM_ERROR));
    }

    console.log('📦 [商品管理] 更新商品:', productId, updateData);

    // 检查商品是否存在
    const existCheck = await executeQuery('SELECT cid FROM fd_product WHERE cid = ?', [productId]);
    if (existCheck.length === 0) {
      return res.json(createErrorResponse('商品不存在', ResponseCode.DATA_NOT_FOUND));
    }

    // 构建更新字段 - 使用正确的数据库字段名
    const allowedFields = [
      'name',
      'fenlei',
      'queryplat',
      'price',
      'content',
      'getnoun',
      'noun',
      'docking',
      'status',
      'sort',
      'wck',
      'kcid',
      'api',
      'nocheck'
    ];

    const updateFields: string[] = [];
    const updateValues: any[] = [];

    for (const field of allowedFields) {
      if (updateData[field] !== undefined) {
        updateFields.push(`${field} = ?`);
        updateValues.push(updateData[field]);
      }
    }

    if (updateFields.length === 0) {
      return res.json(createErrorResponse('没有需要更新的字段', ResponseCode.PARAM_ERROR));
    }

    updateFields.push('update_time = NOW()');
    updateValues.push(productId);

    const updateQuery = `UPDATE fd_product SET ${updateFields.join(', ')} WHERE cid = ?`;
    await executeQuery(updateQuery, updateValues);

    console.log('📦 [商品管理] 更新商品成功');
    return res.json(createSuccessResponse(null, '更新商品成功'));
  } catch (error) {
    console.error('📦 [商品管理] 更新商品失败:', error);
    return res.json(createErrorResponse('更新商品失败', ResponseCode.ERROR));
  }
}

/**
 * 删除商品（管理员）
 */
export async function deleteProduct(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { productId } = req.params;

    if (!productId) {
      return res.json(createErrorResponse('商品ID不能为空', ResponseCode.PARAM_ERROR));
    }

    console.log('📦 [商品管理] 删除商品:', productId);

    // 检查是否有关联的订单
    const orderCheck = await executeQuery('SELECT COUNT(*) as count FROM fd_order WHERE product_id = ?', [productId]);
    if (orderCheck[0].count > 0) {
      return res.json(createErrorResponse('该商品存在关联订单，无法删除', ResponseCode.DATA_EXISTS));
    }

    const deleteQuery = 'DELETE FROM fd_product WHERE product_id = ?';
    const result = await executeQuery(deleteQuery, [productId]);

    if (result.affectedRows === 0) {
      return res.json(createErrorResponse('商品不存在', ResponseCode.DATA_NOT_FOUND));
    }

    console.log('📦 [商品管理] 删除商品成功');
    return res.json(createSuccessResponse(null, '删除商品成功'));
  } catch (error) {
    console.error('📦 [商品管理] 删除商品失败:', error);
    return res.json(createErrorResponse('删除商品失败', ResponseCode.ERROR));
  }
}

/**
 * 批量更新商品
 */
export async function batchUpdateProducts(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { productIds, updateData } = req.body;

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return res.json(createErrorResponse('商品ID列表不能为空', ResponseCode.PARAM_ERROR));
    }

    if (!updateData || typeof updateData !== 'object') {
      return res.json(createErrorResponse('更新数据不能为空', ResponseCode.PARAM_ERROR));
    }

    console.log('📦 [商品管理] 批量更新商品:', { productIds, updateData });

    // 构建更新字段
    const allowedFields = ['name', 'fenlei', 'price', 'content', 'status', 'sort', 'getnoun', 'noun', 'docking'];
    const updateFields = [];
    const updateValues = [];

    for (const [key, value] of Object.entries(updateData)) {
      if (allowedFields.includes(key) && value !== undefined) {
        updateFields.push(`${key} = ?`);
        updateValues.push(value);
      }
    }

    if (updateFields.length === 0) {
      return res.json(createErrorResponse('没有有效的更新字段', ResponseCode.PARAM_ERROR));
    }

    // 添加更新时间
    updateFields.push('update_time = NOW()');

    // 构建SQL
    const placeholders = productIds.map(() => '?').join(',');
    const updateQuery = `
      UPDATE fd_product
      SET ${updateFields.join(', ')}
      WHERE cid IN (${placeholders})
    `;

    const queryParams = [...updateValues, ...productIds];
    const result = await executeQuery(updateQuery, queryParams);

    console.log('📦 [商品管理] 批量更新完成:', result.affectedRows);

    return res.json(
      createSuccessResponse(
        {
          affectedRows: result.affectedRows
        },
        '批量更新成功'
      )
    );
  } catch (error: any) {
    console.error('📦 [商品管理] 批量更新商品失败:', error);
    return res.json(createErrorResponse(error.message || '批量更新失败', ResponseCode.ERROR));
  }
}

/**
 * 批量删除商品
 */
export async function batchDeleteProducts(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { productIds } = req.body;

    if (!productIds || !Array.isArray(productIds) || productIds.length === 0) {
      return res.json(createErrorResponse('商品ID列表不能为空', ResponseCode.PARAM_ERROR));
    }

    console.log('📦 [商品管理] 批量删除商品:', productIds);

    // 检查商品是否存在
    const placeholders = productIds.map(() => '?').join(',');
    const existQuery = `SELECT cid FROM fd_product WHERE cid IN (${placeholders})`;
    const existingProducts = await executeQuery(existQuery, productIds);

    if (existingProducts.length !== productIds.length) {
      return res.json(createErrorResponse('部分商品不存在', ResponseCode.DATA_NOT_FOUND));
    }

    // 执行批量删除
    const deleteQuery = `DELETE FROM fd_product WHERE cid IN (${placeholders})`;
    const result = await executeQuery(deleteQuery, productIds);

    console.log('📦 [商品管理] 批量删除完成:', result.affectedRows);

    return res.json(
      createSuccessResponse(
        {
          affectedRows: result.affectedRows
        },
        '批量删除成功'
      )
    );
  } catch (error: any) {
    console.error('📦 [商品管理] 批量删除商品失败:', error);
    return res.json(createErrorResponse(error.message || '批量删除失败', ResponseCode.ERROR));
  }
}

/**
 * 一键设置排序
 */
export async function autoSetSort(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    const { sortType, categoryId } = req.body;

    console.log('📦 [商品管理] 一键设置排序:', { sortType, categoryId });

    let updateQuery = '';
    let queryParams: any[] = [];

    switch (sortType) {
      case 'by_cid_asc':
        // 按CID从小到大排序
        updateQuery = `
          UPDATE fd_product p1
          JOIN (
            SELECT cid, ROW_NUMBER() OVER (ORDER BY cid ASC) as new_sort
            FROM fd_product
            ${categoryId ? 'WHERE fenlei = ?' : ''}
          ) p2 ON p1.cid = p2.cid
          SET p1.sort = p2.new_sort, p1.update_time = NOW()
          ${categoryId ? 'WHERE p1.fenlei = ?' : ''}
        `;
        if (categoryId) {
          queryParams = [categoryId, categoryId];
        }
        break;

      case 'by_cid_desc':
        // 按CID从大到小排序
        updateQuery = `
          UPDATE fd_product p1
          JOIN (
            SELECT cid, ROW_NUMBER() OVER (ORDER BY cid DESC) as new_sort
            FROM fd_product
            ${categoryId ? 'WHERE fenlei = ?' : ''}
          ) p2 ON p1.cid = p2.cid
          SET p1.sort = p2.new_sort, p1.update_time = NOW()
          ${categoryId ? 'WHERE p1.fenlei = ?' : ''}
        `;
        if (categoryId) {
          queryParams = [categoryId, categoryId];
        }
        break;

      case 'by_name_asc':
        // 按名称从A到Z排序
        updateQuery = `
          UPDATE fd_product p1
          JOIN (
            SELECT cid, ROW_NUMBER() OVER (ORDER BY name ASC) as new_sort
            FROM fd_product
            ${categoryId ? 'WHERE fenlei = ?' : ''}
          ) p2 ON p1.cid = p2.cid
          SET p1.sort = p2.new_sort, p1.update_time = NOW()
          ${categoryId ? 'WHERE p1.fenlei = ?' : ''}
        `;
        if (categoryId) {
          queryParams = [categoryId, categoryId];
        }
        break;

      case 'by_price_asc':
        // 按价格从低到高排序
        updateQuery = `
          UPDATE fd_product p1
          JOIN (
            SELECT cid, ROW_NUMBER() OVER (ORDER BY price ASC) as new_sort
            FROM fd_product
            ${categoryId ? 'WHERE fenlei = ?' : ''}
          ) p2 ON p1.cid = p2.cid
          SET p1.sort = p2.new_sort, p1.update_time = NOW()
          ${categoryId ? 'WHERE p1.fenlei = ?' : ''}
        `;
        if (categoryId) {
          queryParams = [categoryId, categoryId];
        }
        break;

      case 'by_price_desc':
        // 按价格从高到低排序
        updateQuery = `
          UPDATE fd_product p1
          JOIN (
            SELECT cid, ROW_NUMBER() OVER (ORDER BY price DESC) as new_sort
            FROM fd_product
            ${categoryId ? 'WHERE fenlei = ?' : ''}
          ) p2 ON p1.cid = p2.cid
          SET p1.sort = p2.new_sort, p1.update_time = NOW()
          ${categoryId ? 'WHERE p1.fenlei = ?' : ''}
        `;
        if (categoryId) {
          queryParams = [categoryId, categoryId];
        }
        break;

      case 'reset_default':
        // 重置为默认排序（统一设置为10）
        updateQuery = `
          UPDATE fd_product
          SET sort = 10, update_time = NOW()
          ${categoryId ? 'WHERE fenlei = ?' : ''}
        `;
        if (categoryId) {
          queryParams = [categoryId];
        }
        break;

      default:
        return res.json(createErrorResponse('不支持的排序类型', ResponseCode.PARAM_ERROR));
    }

    const result = await executeQuery(updateQuery, queryParams);

    console.log('📦 [商品管理] 一键设置排序完成:', result.affectedRows);

    return res.json(
      createSuccessResponse(
        {
          affectedRows: result.affectedRows,
          sortType,
          categoryId
        },
        `一键设置排序完成，影响 ${result.affectedRows} 个商品`
      )
    );
  } catch (error: any) {
    console.error('📦 [商品管理] 一键设置排序失败:', error);
    return res.json(createErrorResponse(error.message || '一键设置排序失败', ResponseCode.ERROR));
  }
}

/**
 * 获取商品分类列表
 */
export async function getProductCategories(req: AuthenticatedRequest, res: Response): Promise<any> {
  try {
    console.log('📦 [商品管理] 获取商品分类列表');

    const query = `
      SELECT
        c.category_id,
        c.name,
        c.sort_order,
        c.status,
        COUNT(p.cid) as product_count
      FROM fd_category c
      LEFT JOIN fd_product p ON c.category_id = p.fenlei AND p.status = 1
      WHERE c.status = 1
      GROUP BY c.category_id, c.name, c.sort_order, c.status
      ORDER BY c.sort_order ASC, c.category_id ASC
    `;

    const result = await executeQuery(query);

    console.log('📦 [商品管理] 获取商品分类列表成功');
    return res.json(createSuccessResponse(result, '获取商品分类列表成功'));
  } catch (error) {
    console.error('📦 [商品管理] 获取商品分类列表失败:', error);
    return res.json(createErrorResponse('获取商品分类列表失败', ResponseCode.ERROR));
  }
}
