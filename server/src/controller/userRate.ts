import type { Request, Response } from 'express';
import { executeQuery } from '../utils/db';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';

/** 用户费率等级定义 */
export const USER_RATE_LEVELS = [
  { rate: 0.2, level: 'VIP', role: 'vip', description: '最高等级，最低费率' },
  { rate: 0.5, level: '代理商', role: 'agent', description: '代理商等级' },
  { rate: 0.8, level: '会员', role: 'member', description: '会员等级' },
  { rate: 1.0, level: '普通用户', role: 'user', description: '标准费率' }
];

/** 根据费率获取用户角色 */
export function getUserRoleByRate(rate: number): string {
  if (rate <= 0.2) return 'vip';
  if (rate <= 0.5) return 'agent';
  if (rate <= 0.8) return 'member';
  return 'user';
}

/** 根据费率获取等级信息 */
export function getRateLevelInfo(rate: number) {
  for (const level of USER_RATE_LEVELS) {
    if (rate <= level.rate) {
      return level;
    }
  }
  return USER_RATE_LEVELS[USER_RATE_LEVELS.length - 1];
}

/** 计算用户实际价格 */
export function calculateUserPrice(basePrice: number, userRate: number): number {
  return Math.round(basePrice * userRate * 100) / 100; // 保留两位小数
}

/** 获取用户费率信息 */
export async function getUserRateInfo(req: Request, res: Response): Promise<any> {
  try {
    console.log('💰 [费率] 获取用户费率信息');

    const { userId } = req.params;
    const currentUserId = (req as any).user?.userId;

    // 权限检查：只能查看自己的费率或管理员查看所有
    if (
      userId &&
      userId !== currentUserId.toString() &&
      (req as any).user?.userRole !== 'admin' &&
      (req as any).user?.userRole !== 'super'
    ) {
      return res.json(createErrorResponse('无权限查看该用户费率信息', ResponseCode.PERMISSION_DENIED));
      return;
    }

    const targetUserId = userId || currentUserId;

    const sql = `
      SELECT
        user_id,
        username,
        user_rate,
        user_role,
        balance,
        status
      FROM fd_user
      WHERE user_id = ?
    `;

    const result = await executeQuery(sql, [targetUserId]);

    if (result.length === 0) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.DATA_NOT_FOUND));
      return;
    }

    const user = result[0];
    const levelInfo = getRateLevelInfo(user.user_rate);

    const rateInfo = {
      userId: user.user_id,
      username: user.username,
      userRate: user.user_rate,
      userRole: user.user_role,
      balance: user.balance,
      levelInfo: {
        level: levelInfo.level,
        description: levelInfo.description,
        rate: user.user_rate
      },
      priceExample: {
        basePrice: 100,
        userPrice: calculateUserPrice(100, user.user_rate),
        discount: Math.round((1 - user.user_rate) * 100)
      }
    };

    return res.json(createSuccessResponse(rateInfo, '获取费率信息成功'));
  } catch (error) {
    console.error('💰 [费率] 获取用户费率信息失败:', error);
    return res.json(createErrorResponse('获取费率信息失败', ResponseCode.ERROR));
  }
}

/** 更新用户费率 */
export async function updateUserRate(req: Request, res: Response): Promise<any> {
  try {
    console.log('💰 [费率] 更新用户费率');

    const { userId } = req.params;
    const { userRate, reason } = req.body;

    // 权限检查：只有管理员可以修改费率
    if ((req as any).user?.userRole !== 'admin' && (req as any).user?.userRole !== 'super') {
      return res.json(createErrorResponse('无权限修改用户费率', ResponseCode.PERMISSION_DENIED));
      return;
    }

    // 参数验证
    if (!userId || userRate === undefined) {
      return res.json(createErrorResponse('用户ID和费率不能为空', ResponseCode.PARAM_ERROR));
      return;
    }

    if (userRate < 0.2 || userRate > 1.0) {
      return res.json(createErrorResponse('费率必须在0.2-1.0之间', ResponseCode.PARAM_ERROR));
      return;
    }

    // 检查用户是否存在
    const checkSql = 'SELECT user_id, username, user_rate FROM fd_user WHERE user_id = ?';
    const checkResult = await executeQuery(checkSql, [userId]);

    if (checkResult.length === 0) {
      return res.json(createErrorResponse('用户不存在', ResponseCode.DATA_NOT_FOUND));
      return;
    }

    const oldRate = checkResult[0].user_rate;
    const newRole = getUserRoleByRate(userRate);

    // 更新用户费率和角色
    const updateSql = `
      UPDATE fd_user
      SET user_rate = ?, user_role = ?, update_time = NOW()
      WHERE user_id = ?
    `;

    await executeQuery(updateSql, [userRate, newRole, userId]);

    // 记录费率变更日志
    const logSql = `
      INSERT INTO fd_user_rate_log (
        user_id, old_rate, new_rate, old_role, new_role,
        operator_id, reason, create_time
      ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
    `;

    await executeQuery(logSql, [
      userId,
      oldRate,
      userRate,
      checkResult[0].user_role || getUserRoleByRate(oldRate),
      newRole,
      (req as any).user?.userId,
      reason || '管理员调整'
    ]);

    const levelInfo = getRateLevelInfo(userRate);

    return res.json(
      createSuccessResponse(
        {
          userId,
          oldRate,
          newRate: userRate,
          newRole,
          levelInfo
        },
        '费率更新成功'
      )
    );
  } catch (error) {
    console.error('💰 [费率] 更新用户费率失败:', error);
    return res.json(createErrorResponse('更新费率失败', ResponseCode.ERROR));
  }
}

/** 获取费率等级列表 */
export async function getRateLevels(req: Request, res: Response): Promise<any> {
  try {
    console.log('💰 [费率] 获取费率等级列表');

    return res.json(createSuccessResponse(USER_RATE_LEVELS, '获取费率等级成功'));
  } catch (error) {
    console.error('💰 [费率] 获取费率等级失败:', error);
    return res.json(createErrorResponse('获取费率等级失败', ResponseCode.ERROR));
  }
}

/** 获取用户费率变更日志 */
export async function getUserRateLog(req: Request, res: Response): Promise<any> {
  try {
    console.log('💰 [费率] 获取费率变更日志');

    const { userId } = req.params;
    const { page = 1, pageSize = 20 } = req.query;

    // 权限检查
    if ((req as any).user?.userRole !== 'admin' && (req as any).user?.userRole !== 'super') {
      return res.json(createErrorResponse('无权限查看费率日志', ResponseCode.PERMISSION_DENIED));
      return;
    }

    let whereClause = '1=1';
    const params: any[] = [];

    if (userId) {
      whereClause = 'l.user_id = ?';
      params.push(userId);
    }

    // 查询总数
    const countSql = `
      SELECT COUNT(*) as total
      FROM fd_user_rate_log l
      WHERE ${whereClause}
    `;
    const countResult = await executeQuery(countSql, params);
    const total = countResult[0].total;

    // 查询日志列表
    const offset = (Number(page) - 1) * Number(pageSize);
    const listSql = `
      SELECT
        l.*,
        u.username,
        op.username as operator_name
      FROM fd_user_rate_log l
      LEFT JOIN fd_user u ON l.user_id = u.user_id
      LEFT JOIN fd_user op ON l.operator_id = op.user_id
      WHERE ${whereClause}
      ORDER BY l.create_time DESC
      LIMIT ? OFFSET ?
    `;

    const list = await executeQuery(listSql, [...params, Number(pageSize), offset]);

    const result = {
      list,
      pagination: {
        page: Number(page),
        pageSize: Number(pageSize),
        total,
        totalPages: Math.ceil(total / Number(pageSize))
      }
    };

    return res.json(createSuccessResponse(result, '获取费率日志成功'));
  } catch (error) {
    console.error('💰 [费率] 获取费率日志失败:', error);
    return res.json(createErrorResponse('获取费率日志失败', ResponseCode.ERROR));
  }
}

/** 批量更新用户费率 */
export async function batchUpdateUserRate(req: Request, res: Response): Promise<any> {
  try {
    console.log('💰 [费率] 批量更新用户费率');

    const { userIds, userRate, reason } = req.body;

    // 权限检查
    if ((req as any).user?.userRole !== 'super') {
      return res.json(createErrorResponse('只有超级管理员可以批量修改费率', ResponseCode.PERMISSION_DENIED));
      return;
    }

    // 参数验证
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return res.json(createErrorResponse('用户ID列表不能为空', ResponseCode.PARAM_ERROR));
      return;
    }

    if (userRate < 0.2 || userRate > 1.0) {
      return res.json(createErrorResponse('费率必须在0.2-1.0之间', ResponseCode.PARAM_ERROR));
      return;
    }

    const newRole = getUserRoleByRate(userRate);
    const operatorId = (req as any).user?.userId;

    // 获取用户当前费率信息
    const getUsersSql = `
      SELECT user_id, username, user_rate, user_role
      FROM fd_user
      WHERE user_id IN (${userIds.map(() => '?').join(',')})
    `;
    const users = await executeQuery(getUsersSql, userIds);

    if (users.length === 0) {
      return res.json(createErrorResponse('未找到有效用户', ResponseCode.DATA_NOT_FOUND));
      return;
    }

    // 批量更新费率
    const updateSql = `
      UPDATE fd_user
      SET user_rate = ?, user_role = ?, update_time = NOW()
      WHERE user_id IN (${userIds.map(() => '?').join(',')})
    `;
    await executeQuery(updateSql, [userRate, newRole, ...userIds]);

    // 批量记录日志
    const logValues = users.map((user: any) => [
      user.user_id,
      user.user_rate,
      userRate,
      user.user_role,
      newRole,
      operatorId,
      reason || '批量调整'
    ]);

    const logSql = `
      INSERT INTO fd_user_rate_log (
        user_id, old_rate, new_rate, old_role, new_role,
        operator_id, reason, create_time
      ) VALUES ?
    `;
    await executeQuery(logSql, [logValues]);

    return res.json(
      createSuccessResponse(
        {
          updatedCount: users.length,
          newRate: userRate,
          newRole,
          users: users.map((u: any) => ({ userId: u.user_id, username: u.username }))
        },
        '批量更新费率成功'
      )
    );
  } catch (error) {
    console.error('💰 [费率] 批量更新费率失败:', error);
    return res.json(createErrorResponse('批量更新费率失败', ResponseCode.ERROR));
  }
}
