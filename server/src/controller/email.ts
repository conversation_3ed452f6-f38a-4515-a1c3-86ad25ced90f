import type { Request, Response } from 'express';
import type { RowDataPacket } from 'mysql2';
import { ResponseCode } from '../middleware/response';
import type { EmailConfig } from '../utils/emailService';
import { emailService } from '../utils/emailService';
import { emailTemplateManager } from '../utils/emailTemplateManager';
import ConfigManager from '../utils/configManager';
import { pool } from '../database/connection';

// 创建响应的辅助函数
function createErrorResponse(message: string, code: ResponseCode) {
  return {
    code,
    msg: message,
    data: null
  };
}

function createSuccessResponse(data: any, message = '操作成功') {
  return {
    code: ResponseCode.SUCCESS,
    msg: message,
    data
  };
}

/** 测试邮件配置 */
export async function testEmailConfig(req: Request, res: Response): Promise<any> {
  try {
    console.log('📧 [邮件] 测试邮件配置');

    const { smtpHost, smtpPort, emailFrom, emailPassword, emailSSL, testEmail } = req.body;

    // 验证必要参数
    if (!smtpHost || !emailFrom || !testEmail) {
      return res.json(createErrorResponse('SMTP服务器、发件人邮箱和测试邮箱不能为空', ResponseCode.PARAM_ERROR));
      return;
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(emailFrom) || !emailRegex.test(testEmail)) {
      return res.json(createErrorResponse('邮箱格式不正确', ResponseCode.PARAM_ERROR));
      return;
    }

    // 创建测试配置
    const testConfig: EmailConfig = {
      smtpHost,
      smtpPort: smtpPort || 587,
      emailFrom,
      emailPassword,
      emailSSL: emailSSL !== false
    };

    // 测试邮件配置
    await emailService.testConfig(testConfig, testEmail);

    return res.json(createSuccessResponse(null, '测试邮件发送成功'));
  } catch (error) {
    console.error('📧 [邮件] 测试邮件配置失败:', error);

    let errorMessage = '测试邮件发送失败';
    if (error instanceof Error) {
      if (error.message.includes('EAUTH')) {
        errorMessage = '邮箱认证失败，请检查用户名和密码';
      } else if (error.message.includes('ECONNECTION')) {
        errorMessage = '无法连接到SMTP服务器，请检查服务器地址和端口';
      } else if (error.message.includes('ETIMEDOUT')) {
        errorMessage = '连接超时，请检查网络连接和服务器设置';
      } else {
        errorMessage = `测试邮件发送失败: ${error.message}`;
      }
    }

    return res.json(createErrorResponse(errorMessage, ResponseCode.ERROR));
  }
}

/** 发送邮件 */
export async function sendEmail(req: Request, res: Response): Promise<any> {
  try {
    console.log('📧 [邮件] 发送邮件');

    const { to, subject, text, html, template, variables } = req.body;

    // 验证必要参数
    if (!to || (!subject && !template)) {
      return res.json(createErrorResponse('收件人和主题（或模板）不能为空', ResponseCode.PARAM_ERROR));
      return;
    }

    // 验证邮箱格式
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const recipients = Array.isArray(to) ? to : [to];

    for (const email of recipients) {
      if (!emailRegex.test(email)) {
        return res.json(createErrorResponse(`邮箱格式不正确: ${email}`, ResponseCode.PARAM_ERROR));
        return;
      }
    }

    // 检查邮件服务是否可用
    if (!emailService.isAvailable()) {
      return res.json(createErrorResponse('邮件服务不可用，请检查邮件配置', ResponseCode.SERVICE_UNAVAILABLE));
      return;
    }

    // 发送邮件
    await emailService.sendEmail({
      to,
      subject,
      text,
      html,
      template,
      variables
    });

    return res.json(createSuccessResponse(null, '邮件发送成功'));
  } catch (error) {
    console.error('📧 [邮件] 发送邮件失败:', error);
    return res.json(createErrorResponse('邮件发送失败', ResponseCode.ERROR));
  }
}

/** 获取邮件服务状态 */
export async function getEmailStatus(_req: Request, res: Response): Promise<any> {
  try {
    console.log('📧 [邮件] 获取邮件服务状态');

    const config = await ConfigManager.getConfig();
    const isAvailable = emailService.isAvailable();

    const status = {
      available: isAvailable,
      configured: Boolean(config.smtpHost && config.emailFrom),
      smtpHost: config.smtpHost || '',
      smtpPort: config.smtpPort || 587,
      emailFrom: config.emailFrom || '',
      emailSSL: config.emailSSL,
      hasPassword: Boolean(config.emailPassword),
      lastTest: null // 可以从数据库获取最后测试时间
    };

    return res.json(createSuccessResponse(status, '获取邮件状态成功'));
  } catch (error) {
    console.error('📧 [邮件] 获取邮件状态失败:', error);
    return res.json(createErrorResponse('获取邮件状态失败', ResponseCode.ERROR));
  }
}

/** 重新加载邮件配置 */
export async function reloadEmailConfig(_req: Request, res: Response): Promise<any> {
  try {
    console.log('📧 [邮件] 重新加载邮件配置');

    await emailService.reloadConfig();

    const isAvailable = emailService.isAvailable();
    return res.json(createSuccessResponse({ available: isAvailable }, '邮件配置重新加载成功'));
  } catch (error) {
    console.error('📧 [邮件] 重新加载邮件配置失败:', error);
    return res.json(createErrorResponse('重新加载邮件配置失败', ResponseCode.ERROR));
  }
}

/** 获取邮件模板列表 */
export async function getEmailTemplates(_req: Request, res: Response): Promise<any> {
  try {
    console.log('📧 [邮件] 获取邮件模板列表');

    // 从数据库获取模板列表
    const [rows] = await pool.execute<RowDataPacket[]>(
      `SELECT template_id, template_key, template_name, template_description, category,
              subject, text_content, html_content, variables, is_active, is_system,
              create_time, update_time
       FROM fd_email_template
       ORDER BY is_system DESC, create_time DESC`
    );

    const templates = rows.map(row => {
      let variables = [];
      try {
        if (row.variables) {
          if (typeof row.variables === 'string') {
            variables = JSON.parse(row.variables);
          } else {
            variables = row.variables;
          }
        }
      } catch (error) {
        console.error('📧 [邮件] 解析模板变量失败:', error, 'variables:', row.variables);
        variables = [];
      }

      return {
        id: row.template_key,
        templateId: row.template_id,
        name: row.template_name,
        description: row.template_description,
        category: row.category,
        subject: row.subject,
        text: row.text_content,
        html: row.html_content,
        variables,
        isActive: Boolean(row.is_active),
        isSystem: Boolean(row.is_system),
        createTime: row.create_time,
        updateTime: row.update_time
      };
    });

    return res.json(createSuccessResponse(templates, '获取邮件模板成功'));
  } catch (error) {
    console.error('📧 [邮件] 获取邮件模板失败:', error);
    return res.json(createErrorResponse('获取邮件模板失败', ResponseCode.ERROR));
  }
}

/** 预览邮件模板 */
export async function previewEmailTemplate(req: Request, res: Response): Promise<any> {
  try {
    console.log('📧 [邮件] 预览邮件模板');

    const { template, variables } = req.body;

    if (!template) {
      return res.json(createErrorResponse('模板名称不能为空', ResponseCode.PARAM_ERROR));
      return;
    }

    // 获取系统信息作为默认变量
    const systemInfo = await ConfigManager.getSystemInfo();
    const defaultVariables = {
      systemName: systemInfo.systemName,
      userName: '示例用户',
      userEmail: '<EMAIL>',
      registerTime: new Date().toLocaleString('zh-CN'),
      resetLink: 'https://example.com/reset-password',
      expireTime: '24小时',
      title: '系统通知标题',
      content: '这是一个示例通知内容',
      startTime: '2024-01-01 02:00:00',
      endTime: '2024-01-01 06:00:00',
      reason: '系统升级维护',
      sendTime: new Date().toLocaleString('zh-CN'),
      ...variables
    };

    // 使用模板管理器渲染模板
    const preview = await emailTemplateManager.renderTemplate(template, defaultVariables);

    if (!preview) {
      return res.json(createErrorResponse('模板不存在', ResponseCode.PARAM_ERROR));
      return;
    }

    return res.json(createSuccessResponse(preview, '模板预览生成成功'));
  } catch (error) {
    console.error('📧 [邮件] 预览邮件模板失败:', error);
    return res.json(createErrorResponse('预览邮件模板失败', ResponseCode.ERROR));
  }
}

/** 获取单个邮件模板 */
export async function getEmailTemplate(req: Request, res: Response): Promise<any> {
  try {
    console.log('📧 [邮件] 获取邮件模板详情');

    const { templateId } = req.params;

    if (!templateId) {
      return res.json(createErrorResponse('模板ID不能为空', ResponseCode.PARAM_ERROR));
      return;
    }

    const template = await emailTemplateManager.getTemplate(templateId);

    if (!template) {
      return res.json(createErrorResponse('模板不存在', ResponseCode.DATA_NOT_FOUND));
      return;
    }

    return res.json(createSuccessResponse(template, '获取模板详情成功'));
  } catch (error) {
    console.error('📧 [邮件] 获取模板详情失败:', error);
    return res.json(createErrorResponse('获取模板详情失败', ResponseCode.ERROR));
  }
}

/** 获取模板变量定义 */
export async function getTemplateVariables(_req: Request, res: Response): Promise<any> {
  try {
    console.log('📧 [邮件] 获取模板变量定义');

    const variables = emailTemplateManager.getTemplateVariables();

    return res.json(createSuccessResponse(variables, '获取模板变量成功'));
  } catch (error) {
    console.error('📧 [邮件] 获取模板变量失败:', error);
    return res.json(createErrorResponse('获取模板变量失败', ResponseCode.ERROR));
  }
}

/** 保存邮件模板 */
export async function saveEmailTemplate(req: Request, res: Response): Promise<any> {
  try {
    console.log('📧 [邮件] 保存邮件模板');

    const template = req.body;
    const { templateId } = req.params;

    // 验证必要字段
    const requiredFields = ['template_key', 'template_name', 'subject', 'text_content', 'html_content'];
    for (const field of requiredFields) {
      if (!template[field]) {
        return res.json(createErrorResponse(`${field} 不能为空`, ResponseCode.PARAM_ERROR));
        return;
      }
    }

    // 设置默认值
    template.category ||= 'custom';
    template.template_description ||= '';
    template.is_active = template.is_active !== false ? 1 : 0;
    template.is_system = 0; // 用户创建的模板都不是系统模板

    // 处理变量数组
    let variablesJson = '[]';
    if (Array.isArray(template.variables)) {
      variablesJson = JSON.stringify(template.variables);
    } else if (typeof template.variables === 'string') {
      variablesJson = template.variables;
    }

    if (templateId) {
      // 更新模板
      await pool.execute(
        `UPDATE fd_email_template
         SET template_name = ?, template_description = ?, category = ?,
             subject = ?, text_content = ?, html_content = ?, variables = ?,
             is_active = ?, update_time = CURRENT_TIMESTAMP
         WHERE template_id = ?`,
        [
          template.template_name,
          template.template_description,
          template.category,
          template.subject,
          template.text_content,
          template.html_content,
          variablesJson,
          template.is_active,
          templateId
        ]
      );
    } else {
      // 创建新模板
      await pool.execute(
        `INSERT INTO fd_email_template
         (template_key, template_name, template_description, category, subject,
          text_content, html_content, variables, is_active, is_system)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          template.template_key,
          template.template_name,
          template.template_description,
          template.category,
          template.subject,
          template.text_content,
          template.html_content,
          variablesJson,
          template.is_active,
          template.is_system
        ]
      );
    }

    return res.json(createSuccessResponse(template, '模板保存成功'));
  } catch (error) {
    console.error('📧 [邮件] 保存模板失败:', error);
    return res.json(createErrorResponse('保存模板失败', ResponseCode.ERROR));
  }
}

/** 删除邮件模板 */
export async function deleteEmailTemplate(req: Request, res: Response): Promise<any> {
  try {
    console.log('📧 [邮件] 删除邮件模板');

    const { templateId } = req.params;

    if (!templateId) {
      return res.json(createErrorResponse('模板ID不能为空', ResponseCode.PARAM_ERROR));
      return;
    }

    // 检查模板是否存在以及是否为系统模板
    const [rows] = await pool.execute<RowDataPacket[]>(
      'SELECT template_id, is_system FROM fd_email_template WHERE template_id = ?',
      [templateId]
    );

    if (rows.length === 0) {
      return res.json(createErrorResponse('模板不存在', ResponseCode.DATA_NOT_FOUND));
      return;
    }

    if (rows[0].is_system) {
      return res.json(createErrorResponse('系统默认模板不能删除', ResponseCode.FORBIDDEN));
      return;
    }

    // 删除模板
    await pool.execute('DELETE FROM fd_email_template WHERE template_id = ?', [templateId]);

    return res.json(createSuccessResponse(null, '模板删除成功'));
  } catch (error) {
    console.error('📧 [邮件] 删除模板失败:', error);
    return res.json(createErrorResponse('删除模板失败', ResponseCode.ERROR));
  }
}

/** 获取邮件发送记录 */
export async function getEmailRecords(req: Request, res: Response): Promise<any> {
  try {
    console.log('📧 [邮件] 获取邮件发送记录');

    const { page = 1, pageSize = 20, status, templateId, email, startDate, endDate } = req.query;
    const offset = (Number(page) - 1) * Number(pageSize);

    console.log('📧 [调试] 分页参数:', { page, pageSize, offset });

    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    if (status) {
      whereClause += ' AND r.status = ?';
      params.push(status);
    }

    if (templateId) {
      whereClause += ' AND r.template_id = ?';
      params.push(templateId);
    }

    if (email) {
      whereClause += ' AND r.recipient_email LIKE ?';
      params.push(`%${email}%`);
    }

    if (startDate) {
      whereClause += ' AND r.create_time >= ?';
      params.push(startDate);
    }

    if (endDate) {
      whereClause += ' AND r.create_time <= ?';
      params.push(endDate);
    }

    // 获取记录总数
    const [countRows] = await pool.execute<RowDataPacket[]>(
      `SELECT COUNT(*) as total FROM fd_email_record r ${whereClause}`,
      params
    );
    const total = countRows[0].total;

    // 获取记录列表 - 使用简单的LIMIT语法
    const [rows] = await pool.execute<RowDataPacket[]>(
      `SELECT * FROM fd_email_record
       ${whereClause}
       ORDER BY create_time DESC
       LIMIT ${Number(pageSize)} OFFSET ${Number(offset)}`
    );

    const records = rows.map(row => ({
      recordId: row.record_id,
      templateId: row.template_id,
      templateName: row.template_name,
      senderId: row.sender_id,
      senderName: row.sender_name,
      recipientEmail: row.recipient_email,
      recipientName: row.recipient_name,
      subject: row.subject,
      status: row.status,
      errorMessage: row.error_message,
      sendTime: row.send_time,
      deliveryTime: row.delivery_time,
      openTime: row.open_time,
      clickTime: row.click_time,
      bounceTime: row.bounce_time,
      createTime: row.create_time
    }));

    return res.json(
      createSuccessResponse(
        {
          records,
          pagination: {
            page: Number(page),
            pageSize: Number(pageSize),
            total,
            totalPages: Math.ceil(total / Number(pageSize))
          }
        },
        '获取邮件记录成功'
      )
    );
  } catch (error) {
    console.error('📧 [邮件] 获取邮件记录失败:', error);
    return res.json(createErrorResponse('获取邮件记录失败', ResponseCode.ERROR));
  }
}

/** 获取邮件统计 */
export async function getEmailStats(req: Request, res: Response): Promise<any> {
  try {
    console.log('📧 [邮件] 获取邮件统计');

    const { startDate, endDate } = req.query;
    let dateFilter = '';
    const params: any[] = [];

    if (startDate && endDate) {
      dateFilter = 'WHERE create_time BETWEEN ? AND ?';
      params.push(startDate, endDate);
    } else {
      // 默认查询最近30天
      dateFilter = 'WHERE create_time >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
    }

    // 获取总体统计
    const [totalStats] = await pool.execute<RowDataPacket[]>(
      `SELECT
         COUNT(*) as totalSent,
         SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as totalDelivered,
         SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as totalFailed,
         SUM(CASE WHEN status = 'bounced' THEN 1 ELSE 0 END) as totalBounced
       FROM fd_email_record ${dateFilter}`,
      params
    );

    const stats = totalStats[0];
    const deliveryRate = stats.totalSent > 0 ? ((stats.totalDelivered / stats.totalSent) * 100).toFixed(2) : 0;

    // 获取每日统计
    const [dailyStats] = await pool.execute<RowDataPacket[]>(
      `SELECT
         DATE(create_time) as date,
         COUNT(*) as sent,
         SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as delivered,
         SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed
       FROM fd_email_record ${dateFilter}
       GROUP BY DATE(create_time)
       ORDER BY date DESC
       LIMIT 30`,
      params
    );

    return res.json(
      createSuccessResponse(
        {
          overview: {
            totalSent: stats.totalSent,
            totalDelivered: stats.totalDelivered,
            totalFailed: stats.totalFailed,
            totalBounced: stats.totalBounced,
            deliveryRate: Number(deliveryRate)
          },
          dailyStats
        },
        '获取邮件统计成功'
      )
    );
  } catch (error) {
    console.error('📧 [邮件] 获取邮件统计失败:', error);
    return res.json(createErrorResponse('获取邮件统计失败', ResponseCode.ERROR));
  }
}
