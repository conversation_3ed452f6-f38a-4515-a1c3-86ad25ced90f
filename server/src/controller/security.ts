import type { Request, Response } from 'express';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import { securityMonitor } from '../utils/securityMonitor';
import { getCurrentLicenseInfo, markLicenseAsSuspicious, regenerateDataSignature } from '../model/license';

/** 获取安全监控报告 */
export async function getSecurityReport(req: Request, res: Response) {
  try {
    const stats = securityMonitor.getSecurityStats();
    const report = securityMonitor.generateSecurityReport();

    return res.json(
      createSuccessResponse(
        {
          stats,
          report,
          timestamp: new Date().toISOString()
        },
        '安全报告获取成功'
      )
    );
  } catch (error) {
    console.error('获取安全报告失败:', error);
    return res.json(createErrorResponse('服务器内部错误', ResponseCode.ERROR));
  }
}

/** 获取安全事件列表 */
export async function getSecurityEvents(req: Request, res: Response) {
  try {
    const { domain, limit = 50, severity } = req.query;

    let events;
    if (domain) {
      events = securityMonitor.getEventsByDomain(domain as string, Number(limit));
    } else {
      events = securityMonitor.getRecentEvents(Number(limit));
    }

    // 按严重程度过滤
    if (severity) {
      events = events.filter(event => event.severity === severity);
    }

    return res.json(
      createSuccessResponse(
        {
          events,
          total: events.length,
          filters: { domain, limit, severity }
        },
        '安全事件获取成功'
      )
    );
  } catch (error) {
    console.error('获取安全事件失败:', error);
    return res.json(createErrorResponse('服务器内部错误', ResponseCode.ERROR));
  }
}

/** 手动触发授权验证 */
export async function triggerLicenseValidation(req: Request, res: Response) {
  try {
    const { domain } = req.body;

    if (!domain) {
      return res.json(createErrorResponse('域名参数不能为空', ResponseCode.PARAM_ERROR));
    }

    console.log(`🔒 [安全管理] 手动触发授权验证: ${domain}`);

    // 获取当前授权信息
    const licenseInfo = await getCurrentLicenseInfo(domain);

    if (!licenseInfo) {
      return res.json(createErrorResponse('未找到授权信息', ResponseCode.ERROR));
    }

    // 这里可以添加重新验证逻辑
    // 例如：调用外部授权系统重新验证

    securityMonitor.logExternalValidationRequired(domain, '管理员手动触发验证');

    return res.json(
      createSuccessResponse(
        {
          domain,
          status: licenseInfo.status,
          message: '验证请求已提交'
        },
        '授权验证已触发'
      )
    );
  } catch (error) {
    console.error('触发授权验证失败:', error);
    return res.json(createErrorResponse('服务器内部错误', ResponseCode.ERROR));
  }
}

/** 重新生成数据签名 */
export async function regenerateSignature(req: Request, res: Response) {
  try {
    const { domain } = req.body;

    if (!domain) {
      return res.json(createErrorResponse('域名参数不能为空', ResponseCode.PARAM_ERROR));
    }

    console.log(`🔒 [安全管理] 重新生成数据签名: ${domain}`);

    const success = await regenerateDataSignature(domain);

    if (success) {
      securityMonitor.logSecurityEvent({
        type: 'DATA_TAMPERING',
        domain,
        details: '管理员重新生成数据签名',
        severity: 'LOW'
      });

      return res.json(
        createSuccessResponse(
          {
            domain,
            regenerated: true
          },
          '数据签名重新生成成功'
        )
      );
    }
    return res.json(createErrorResponse('未找到授权信息或重新生成失败', ResponseCode.ERROR));
  } catch (error) {
    console.error('重新生成数据签名失败:', error);
    return res.json(createErrorResponse('服务器内部错误', ResponseCode.ERROR));
  }
}

/** 标记授权为可疑 */
export async function markAsSuspicious(req: Request, res: Response) {
  try {
    const { domain, reason } = req.body;

    if (!domain || !reason) {
      return res.json(createErrorResponse('域名和原因参数不能为空', ResponseCode.PARAM_ERROR));
    }

    console.log(`🔒 [安全管理] 标记授权为可疑: ${domain}, 原因: ${reason}`);

    const success = await markLicenseAsSuspicious(domain, reason);

    if (success) {
      securityMonitor.detectSuspiciousAccess(domain, `管理员标记为可疑: ${reason}`);

      return res.json(
        createSuccessResponse(
          {
            domain,
            marked: true,
            reason
          },
          '授权已标记为可疑'
        )
      );
    }
    return res.json(createErrorResponse('未找到授权信息或标记失败', ResponseCode.ERROR));
  } catch (error) {
    console.error('标记授权为可疑失败:', error);
    return res.json(createErrorResponse('服务器内部错误', ResponseCode.ERROR));
  }
}

/** 获取授权安全状态 */
export async function getLicenseSecurityStatus(req: Request, res: Response) {
  try {
    const domain = req.params.domain;

    if (!domain) {
      return res.json(createErrorResponse('域名参数不能为空', ResponseCode.PARAM_ERROR));
    }

    const licenseInfo = await getCurrentLicenseInfo(domain);

    if (!licenseInfo) {
      return res.json(createErrorResponse('未找到授权信息', ResponseCode.ERROR));
    }

    // 获取该域名的安全事件
    const securityEvents = securityMonitor.getEventsByDomain(domain, 10);

    // 计算安全评分
    const securityScore = calculateSecurityScore(licenseInfo, securityEvents);

    return res.json(
      createSuccessResponse(
        {
          domain,
          securityScore,
          hasDataSignature: Boolean(licenseInfo.data_signature),
          lastExternalValidation: licenseInfo.last_external_validation,
          validationCount: licenseInfo.validation_count,
          recentSecurityEvents: securityEvents.length,
          status: licenseInfo.status,
          recommendations: generateSecurityRecommendations(licenseInfo, securityEvents)
        },
        '授权安全状态获取成功'
      )
    );
  } catch (error) {
    console.error('获取授权安全状态失败:', error);
    return res.json(createErrorResponse('服务器内部错误', ResponseCode.ERROR));
  }
}

/** 计算安全评分 */
function calculateSecurityScore(licenseInfo: any, securityEvents: any[]): number {
  let score = 100;

  // 没有数据签名 -30分
  if (!licenseInfo.data_signature) {
    score -= 30;
  }

  // 没有外部验证记录 -20分
  if (!licenseInfo.last_external_validation) {
    score -= 20;
  }

  // 验证次数过少 -10分
  if ((licenseInfo.validation_count || 0) < 1) {
    score -= 10;
  }

  // 最近有安全事件 -5分每个
  const recentCriticalEvents = securityEvents.filter(
    event => event.severity === 'CRITICAL' && new Date(event.timestamp) > new Date(Date.now() - 24 * 60 * 60 * 1000)
  );
  score -= recentCriticalEvents.length * 5;

  // 外部验证时间过久 -15分
  if (licenseInfo.last_external_validation) {
    const lastValidation = new Date(licenseInfo.last_external_validation);
    const hoursSince = (Date.now() - lastValidation.getTime()) / (1000 * 60 * 60);
    if (hoursSince > 24) {
      score -= 15;
    }
  }

  return Math.max(0, Math.min(100, score));
}

/** 生成安全建议 */
function generateSecurityRecommendations(licenseInfo: any, securityEvents: any[]): string[] {
  const recommendations: string[] = [];

  if (!licenseInfo.data_signature) {
    recommendations.push('建议重新生成数据签名以防止篡改');
  }

  if (!licenseInfo.last_external_validation) {
    recommendations.push('建议进行外部授权验证');
  }

  if (licenseInfo.last_external_validation) {
    const lastValidation = new Date(licenseInfo.last_external_validation);
    const hoursSince = (Date.now() - lastValidation.getTime()) / (1000 * 60 * 60);
    if (hoursSince > 24) {
      recommendations.push('建议更新外部验证（超过24小时）');
    }
  }

  const recentCriticalEvents = securityEvents.filter(event => event.severity === 'CRITICAL');
  if (recentCriticalEvents.length > 0) {
    recommendations.push('检测到关键安全事件，建议立即检查');
  }

  if (recommendations.length === 0) {
    recommendations.push('授权安全状态良好');
  }

  return recommendations;
}
