import fs from 'node:fs';
import path from 'node:path';
import process from 'node:process';
import type { AxiosError } from 'axios';
import axios from 'axios';
import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import cron from 'node-cron';
import routes from './routes';
import { createLicenseTable, fixAllDataSignatures } from './model/license';
import { createLicenseService } from './utils/licenseService';
import { checkDatabaseConnection, initDatabase } from './utils/initDatabase';
import { licenseManager } from './utils/licenseManager';
import { verifySystemLicense } from './middleware/license';
import { checkMaintenanceModeForAuthenticated } from './middleware/maintenance';
import { emailService } from './utils/emailService';
import { errorHandler, notFoundHandler, rateLimiter, requestLogger, responseFormatter } from './middleware/response';

// 设置时区为上海时区
process.env.TZ = 'Asia/Shanghai';

// 加载环境变量 - 指定绝对路径
const envPath = path.resolve(__dirname, '../.env');
dotenv.config({ path: envPath });

// 环境变量已加载

// PID文件路径
const PID_FILE = path.join(__dirname, '../server.pid');

/** 检查是否有重复的服务器实例 */
function checkDuplicateInstance(): boolean {
  try {
    if (fs.existsSync(PID_FILE)) {
      const oldPid = fs.readFileSync(PID_FILE, 'utf8').trim();

      try {
        // 检查进程是否仍在运行
        process.kill(Number.parseInt(oldPid, 10), 0);
        return true;
      } catch {
        // 进程不存在，删除旧的PID文件
        fs.unlinkSync(PID_FILE);
        return false;
      }
    }
    return false;
  } catch {
    return false;
  }
}

/** 写入当前进程PID */
function writePidFile(): void {
  try {
    fs.writeFileSync(PID_FILE, process.pid.toString());
  } catch {
    // 静默处理错误
  }
}

/** 清理PID文件 */
function cleanupPidFile(): void {
  try {
    if (fs.existsSync(PID_FILE)) {
      fs.unlinkSync(PID_FILE);
    }
  } catch {
    // 静默处理错误
  }
}

// 检查重复启动
if (checkDuplicateInstance()) {
  process.exit(1);
}

// 写入当前进程PID
writePidFile();

// 注册进程退出时的清理函数
process.on('exit', cleanupPidFile);
process.on('SIGINT', () => {
  cleanupPidFile();
  process.exit(0);
});
process.on('SIGTERM', () => {
  cleanupPidFile();
  process.exit(0);
});

// 授权状态文件路径
const LICENSE_STATUS_FILE = path.join(__dirname, '../data/license-status.json');

// 确保数据目录存在
const dataDir = path.dirname(LICENSE_STATUS_FILE);
if (!fs.existsSync(dataDir)) {
  fs.mkdirSync(dataDir, { recursive: true });
}

// 创建Express应用
const app = express();

// 配置CORS中间件，允许所有跨域请求
app.use(
  cors({
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-License-Key', 'X-Request-ID', 'API-Version']
  })
);

// 配置中间件 - 优化内存使用
app.use(express.json({ limit: '10mb' })); // 增加限制以支持文件上传
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 响应格式化中间件 - 必须在其他中间件之前注册
app.use(responseFormatter);

// 请求日志中间件
app.use(requestLogger);

// API路由（简化版本，不需要版本验证）

// 请求限流中间件（仅对/api路径生效）
app.use('/api', rateLimiter(300, 60000)); // 300次/分钟

// 定期验证授权（使用新的数据库授权服务）
async function performLicenseValidation(): Promise<void> {
  try {
    const domain = process.env.DOMAIN || 'localhost';
    const licenseService = createLicenseService(domain);

    // 获取本地授权状态
    await licenseService.getLocalLicenseStatus();
  } catch {
    // 静默处理错误
  }
}

// 授权系统API代理 - 处理前端的双重代理路径
app.use('/proxy-default/proxy-license', async (req, res) => {
  try {
    const licenseApiUrl = process.env.LICENSE_API_URL || 'https://shouquan.hhigq.luxe/api';
    const targetUrl = `${licenseApiUrl}${req.url}`;

    // 转发请求到授权系统
    const response = await axios({
      method: req.method,
      url: targetUrl,
      data: req.method !== 'GET' ? req.body : undefined,
      params: req.method === 'GET' ? req.query : undefined,
      headers: {
        'Content-Type': 'application/json',
        ...(req.headers.authorization && { Authorization: req.headers.authorization })
      }
    });

    // 返回响应
    return res.status(response.status).json(response.data);
  } catch (error: unknown) {
    // 如果是Axios错误，检查是否是业务错误响应
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;
      if (axiosError.response) {
        return res.status(axiosError.response.status).json(axiosError.response.data);
      }
    }

    // 如果是网络错误，返回500错误
    return res.status(500).json({
      success: false,
      message: '授权系统连接失败，请稍后再试'
    });
  }
});

// 授权系统API代理 - 兼容直接访问
app.use('/proxy-license', async (req, res) => {
  try {
    const licenseApiUrl = process.env.LICENSE_API_URL || 'https://shouquan.hhigq.luxe/api';
    const targetUrl = `${licenseApiUrl}${req.url}`;

    // 转发请求到授权系统
    const response = await axios({
      method: req.method,
      url: targetUrl,
      data: req.method !== 'GET' ? req.body : undefined,
      params: req.method === 'GET' ? req.query : undefined,
      headers: {
        'Content-Type': 'application/json',
        ...(req.headers.authorization && { Authorization: req.headers.authorization })
      }
    });

    // 返回响应
    return res.status(response.status).json(response.data);
  } catch (error: unknown) {
    // 如果是Axios错误，检查是否是业务错误响应
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;
      if (axiosError.response) {
        return res.status(axiosError.response.status).json(axiosError.response.data);
      }
    }

    // 如果是网络错误，返回500错误
    return res.status(500).json({
      success: false,
      message: '授权系统连接失败，请稍后再试'
    });
  }
});

// 健康检查接口已移除 - 减少不必要的资源消耗

// 注册路由
// 1. 先注册认证路由，确保认证API不受授权验证影响
app.use('/api/auth', routes.authRoutes);

// 2. 注册授权路由，不需要授权验证中间件（已在路由内部处理）
app.use('/api/license', routes.licenseRoutes);

// 3. 注册安全管理路由，不需要授权验证中间件
app.use('/api/security', routes.securityRoutes);

// 4. 应用全局授权验证中间件到其他所有API
app.use('/api', verifySystemLicense);

// 5. 应用维护模式检查到其他路由（在认证之后）
app.use('/api', checkMaintenanceModeForAuthenticated);

// 6. 注册其他需要授权验证的路由
app.use('/api', routes.otherRoutes);

// 404处理中间件
app.use(notFoundHandler);

// 全局错误处理中间件
app.use(errorHandler);

// 设置定时任务 - 每天凌晨2点验证一次授权
cron.schedule('0 2 * * *', async () => {
  await performLicenseValidation();
});

// 初始化数据库和授权系统
async function initializeSystem() {
  try {
    // 检查数据库连接
    const dbConnected = await checkDatabaseConnection();
    if (!dbConnected) {
      return;
    }

    // 初始化用户数据库（创建表和默认用户）
    await initDatabase();

    // 创建授权信息表
    await createLicenseTable();

    // 修复所有授权记录的数据签名
    await fixAllDataSignatures();

    // 初始化授权管理器
    await licenseManager.initialize();

    // 初始化邮件服务
    await emailService.initialize();

    // 启动时执行一次授权验证（保留兼容性）
    await performLicenseValidation();

    // 初始化并启动同步服务（暂时禁用自动启动）
    try {
      console.log('🔄 [同步服务] 同步服务模块已加载，可通过API手动启动');
      // 暂时注释自动启动，避免启动时错误
      // const { SyncScheduler } = await import('./service/sync/SyncScheduler');
      // const { SyncConfigManager } = await import('./service/sync/SyncConfigManager');
      //
      // const configManager = SyncConfigManager.getInstance();
      // await configManager.loadConfig();
      //
      // const scheduler = SyncScheduler.getInstance();
      // await scheduler.start();
      //
      // console.log('🔄 [同步服务] 自动同步服务已启动');
    } catch (error) {
      console.error('🔄 [同步服务] 启动失败:', error);
    }
  } catch {
    // 静默处理错误
  }
}

// 延迟初始化，确保服务器完全启动
setTimeout(initializeSystem, 5000);

// 设置端口
const PORT = Number.parseInt(process.env.PORT || '3000', 10);

// 自动启动同步系统
async function autoStartSyncSystem(): Promise<void> {
  try {
    console.log(`${new Date().toISOString()} 🚀 [服务器启动] 检查自动同步配置...`);

    // 动态导入避免启动时的循环依赖
    const { SyncConfigManager } = await import('./service/sync/SyncConfigManager');
    const { SyncScheduler } = await import('./service/sync/SyncScheduler');

    const configManager = SyncConfigManager.getInstance();
    await configManager.loadConfig();
    const config = configManager.getConfig();

    if (config.autoStartOnBoot) {
      console.log(`${new Date().toISOString()} 🔄 [服务器启动] 自动启动同步系统...`);

      const scheduler = SyncScheduler.getInstance();
      await scheduler.start();

      console.log(`${new Date().toISOString()} ✅ [服务器启动] 同步系统自动启动成功！`);
    } else {
      console.log(`${new Date().toISOString()} 💤 [服务器启动] 自动启动已禁用，需要手动启动同步`);
    }
  } catch (error) {
    console.error(`${new Date().toISOString()} ❌ [服务器启动] 自动启动同步系统失败:`, error);
  }
}

// 启动服务器，处理端口占用问题
function startServer(port: number, retryCount = 0): void {
  const server = app.listen(port);

  server.on('error', (err: any) => {
    if (err.code === 'EADDRINUSE') {
      if (retryCount < 5) {
        const nextPort = port + 1;
        startServer(nextPort, retryCount + 1);
      } else {
        process.exit(1);
      }
    } else {
      process.exit(1);
    }
  });

  server.on('listening', async () => {
    console.log(`${new Date().toISOString()} 🌟 [服务器启动] 服务器已启动，端口: ${port}`);

    // 延迟3秒后启动同步系统，确保所有模块都已加载
    setTimeout(async () => {
      await autoStartSyncSystem();
    }, 3000);
  });
}

// 启动服务器
startServer(PORT);
