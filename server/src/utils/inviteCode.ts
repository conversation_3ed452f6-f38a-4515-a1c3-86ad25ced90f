/**
 * 邀请码生成工具
 * 遵循项目规范，提供简单有效的邀请码生成功能
 */

/**
 * 生成邀请码
 * @returns 8位随机邀请码
 */
export function generateInviteCode(): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';

  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }

  return result;
}

/**
 * 验证邀请码格式
 * @param inviteCode 邀请码
 * @returns 验证结果
 */
export function validateInviteCodeFormat(inviteCode: string): { valid: boolean; message?: string } {
  if (!inviteCode) {
    return { valid: false, message: '邀请码不能为空' };
  }

  if (inviteCode.length !== 8) {
    return { valid: false, message: '邀请码必须为8位' };
  }

  if (!/^[A-Z0-9]+$/.test(inviteCode)) {
    return { valid: false, message: '邀请码只能包含大写字母和数字' };
  }

  return { valid: true };
}
