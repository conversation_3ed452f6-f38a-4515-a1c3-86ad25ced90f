import { getCurrentLicenseInfo, saveLicenseInfo } from '../model/license';
import { createLicenseService } from './licenseService';
import { formatDateTimeForLog } from './timeUtils';

/** 全局授权状态管理器 负责管理系统的授权状态和定期验证 */
class LicenseManager {
  private isActivated: boolean = false;
  private lastCheck: Date = new Date();
  private checkInterval: NodeJS.Timeout | null = null;
  private readonly CHECK_INTERVAL = 24 * 60 * 60 * 1000; // 24小时

  constructor() {
    // 授权管理器初始化
  }

  /** 启动授权管理器 检查当前授权状态并启动定期验证 */
  async initialize(): Promise<void> {
    try {
      // 检查数据库中的授权状态
      const domain = process.env.DOMAIN || 'localhost';
      const licenseInfo = await getCurrentLicenseInfo(domain);

      if (licenseInfo && licenseInfo.status === 'active') {
        // 检查是否过期
        const now = new Date();
        const isExpired = licenseInfo.expires_at && new Date(licenseInfo.expires_at) < now;

        if (!isExpired) {
          this.isActivated = true;
        }
      }

      // 启动定期检测
      this.startPeriodicCheck();
    } catch (error) {
      console.error(`${formatDateTimeForLog()} 🔒 [授权管理器] 初始化失败:`, error);
      this.isActivated = false;
    }
  }

  /**
   * 激活授权
   *
   * @param licenseKey 授权密钥
   * @returns 是否激活成功
   */
  async activate(licenseKey: string): Promise<{ success: boolean; message: string }> {
    console.log(`${formatDateTimeForLog()} 🔒 [授权管理器] 开始激活授权...`);

    try {
      const licenseService = createLicenseService();
      const domain = process.env.DOMAIN || 'localhost';

      // 验证授权密钥
      const result = await licenseService.validateLicenseKey(licenseKey);

      if (result.success && result.data?.status === 'active') {
        // 保存到数据库
        const now = new Date();
        const mysqlDateTime = now.toISOString().slice(0, 19).replace('T', ' ');

        const licenseInfo = {
          license_key: licenseKey,
          domain,
          status: 'active' as const,
          expires_at: result.data.expiryDate || undefined,
          validated_at: mysqlDateTime,
          plan: result.data.plan?.name || undefined
        };

        await saveLicenseInfo(licenseInfo);

        // 激活系统
        this.isActivated = true;
        this.lastCheck = now;

        console.log(`${formatDateTimeForLog()} 🔒 [授权管理器] 授权激活成功`);
        return {
          success: true,
          message: '授权激活成功，系统已启用所有功能'
        };
      }
      console.log(`${formatDateTimeForLog()} 🔒 [授权管理器] 授权验证失败:`, result.message);
      return {
        success: false,
        message: result.message || '授权密钥无效'
      };
    } catch (error: any) {
      console.error(`${formatDateTimeForLog()} 🔒 [授权管理器] 激活过程出错:`, error);
      return {
        success: false,
        message: error.message || '授权激活失败'
      };
    }
  }

  /** 停用授权 */
  deactivate(): void {
    console.log(`${formatDateTimeForLog()} 🔒 [授权管理器] 停用授权`);
    this.isActivated = false;
    this.stopPeriodicCheck();
  }

  /**
   * 检查授权状态
   *
   * @returns 是否已激活
   */
  isActive(): boolean {
    return this.isActivated;
  }

  /**
   * 直接设置授权状态
   *
   * @param activated 是否已激活
   */
  setActivated(activated: boolean): void {
    this.isActivated = activated;
    this.lastCheck = new Date();
  }

  /** 获取授权状态信息 */
  getStatus() {
    return {
      isActivated: this.isActivated,
      lastCheck: this.lastCheck,
      nextCheck: this.checkInterval ? new Date(Date.now() + this.CHECK_INTERVAL) : null
    };
  }

  /** 启动定期检测 */
  private startPeriodicCheck(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
    }

    this.checkInterval = setInterval(async () => {
      await this.performPeriodicCheck();
    }, this.CHECK_INTERVAL);
  }

  /** 停止定期检测 */
  private stopPeriodicCheck(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      console.log('🔒 [授权管理器] 定期检测已停止');
    }
  }

  /** 执行定期检测 */
  private async performPeriodicCheck(): Promise<void> {
    console.log('🔒 [授权管理器] 执行定期授权检测...');

    try {
      const domain = process.env.DOMAIN || 'localhost';
      const licenseInfo = await getCurrentLicenseInfo(domain);

      if (!licenseInfo) {
        console.log('🔒 [授权管理器] 定期检测：未找到授权信息，停用系统');
        this.isActivated = false;
        return;
      }

      // 检查是否过期
      const now = new Date();
      const isExpired = licenseInfo.expires_at && new Date(licenseInfo.expires_at) < now;

      if (isExpired) {
        console.log('🔒 [授权管理器] 定期检测：授权已过期，停用系统');
        this.isActivated = false;
        return;
      }

      // 如果有授权密钥，尝试重新验证
      if (licenseInfo.license_key) {
        const licenseService = createLicenseService();
        const result = await licenseService.validateLicenseKey(licenseInfo.license_key);

        if (result.success && result.data?.status === 'active') {
          console.log(`${formatDateTimeForLog()} 🔒 [授权管理器] 定期检测：授权验证通过`);
          this.isActivated = true;
          this.lastCheck = now;
        } else {
          console.log(`${formatDateTimeForLog()} 🔒 [授权管理器] 定期检测：授权验证失败，停用系统`);
          this.isActivated = false;
        }
      }
    } catch (error) {
      console.error(`${formatDateTimeForLog()} 🔒 [授权管理器] 定期检测失败:`, error);
      // 网络错误时保持当前状态，不立即停用
      console.log(`${formatDateTimeForLog()} 🔒 [授权管理器] 网络错误，保持当前状态`);
    }
  }

  /** 手动重新验证授权 */
  async revalidate(): Promise<{ success: boolean; message: string }> {
    console.log(`${formatDateTimeForLog()} 🔒 [授权管理器] 手动重新验证授权...`);

    try {
      const domain = process.env.DOMAIN || 'localhost';
      const licenseInfo = await getCurrentLicenseInfo(domain);

      if (!licenseInfo || !licenseInfo.license_key) {
        this.isActivated = false;
        return {
          success: false,
          message: '未找到授权信息'
        };
      }

      // 检查授权状态（不重新验证，避免破坏数据签名一致性）
      if (licenseInfo.status === 'active' && licenseInfo.expires_at && new Date(licenseInfo.expires_at) > new Date()) {
        this.isActivated = true;
        this.lastCheck = new Date();
        return {
          success: true,
          message: '授权验证通过'
        };
      }

      this.isActivated = false;
      return {
        success: false,
        message: '授权已过期或无效'
      };
    } catch (error: any) {
      this.isActivated = false;
      return {
        success: false,
        message: error.message || '验证过程出错'
      };
    }
  }
}

// 创建全局实例
export const licenseManager = new LicenseManager();

// 导出类型
export { LicenseManager };
