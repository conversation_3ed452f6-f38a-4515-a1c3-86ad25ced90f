import ConfigManager from './configManager';

/** 密码策略验证结果 */
export interface PasswordValidationResult {
  isValid: boolean;
  errors: string[];
  suggestions: string[];
}

/** 密码策略验证器 */
export class PasswordPolicy {
  /** 验证密码是否符合策略 */
  public static async validatePassword(password: string): Promise<PasswordValidationResult> {
    const errors: string[] = [];
    const suggestions: string[] = [];

    try {
      // 获取配置的密码策略
      const minLength = await ConfigManager.getPasswordMinLength();
      const enableTwoFactor = await ConfigManager.isTwoFactorEnabled();

      // 基本长度检查
      if (!password || password.length < minLength) {
        errors.push(`密码长度不能少于${minLength}位`);
        suggestions.push(`请输入至少${minLength}位字符的密码`);
      }

      // 最大长度检查（防止过长密码）
      if (password && password.length > 128) {
        errors.push('密码长度不能超过128位');
        suggestions.push('请输入不超过128位字符的密码');
      }

      // 字符类型检查
      const hasLowerCase = /[a-z]/.test(password);
      const hasUpperCase = /[A-Z]/.test(password);
      const hasNumbers = /\d/.test(password);
      const hasSpecialChars = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);

      // 根据密码长度要求不同的复杂度
      if (minLength >= 8) {
        // 8位以上密码要求更高复杂度
        let complexityScore = 0;
        if (hasLowerCase) complexityScore++;
        if (hasUpperCase) complexityScore++;
        if (hasNumbers) complexityScore++;
        if (hasSpecialChars) complexityScore++;

        if (complexityScore < 3) {
          errors.push('密码必须包含以下至少3种字符类型：小写字母、大写字母、数字、特殊字符');

          if (!hasLowerCase) suggestions.push('添加小写字母 (a-z)');
          if (!hasUpperCase) suggestions.push('添加大写字母 (A-Z)');
          if (!hasNumbers) suggestions.push('添加数字 (0-9)');
          if (!hasSpecialChars) suggestions.push('添加特殊字符 (!@#$%^&*等)');
        }
      } else {
        // 6-7位密码至少需要字母和数字
        if (!hasLowerCase && !hasUpperCase) {
          errors.push('密码必须包含字母');
          suggestions.push('添加字母字符');
        }
        if (!hasNumbers) {
          errors.push('密码必须包含数字');
          suggestions.push('添加数字字符');
        }
      }

      // 常见弱密码检查
      const weakPasswords = [
        '123456',
        '123456789',
        'password',
        'admin',
        'qwerty',
        'abc123',
        '111111',
        '000000',
        'password123',
        'admin123'
      ];

      if (weakPasswords.includes(password.toLowerCase())) {
        errors.push('不能使用常见的弱密码');
        suggestions.push('请使用更复杂的密码组合');
      }

      // 重复字符检查
      if (/(.)\1{2,}/.test(password)) {
        errors.push('密码不能包含连续3个或以上相同字符');
        suggestions.push('避免使用重复字符，如"aaa"或"111"');
      }

      // 连续字符检查
      if (this.hasSequentialChars(password)) {
        errors.push('密码不能包含连续的字符序列');
        suggestions.push('避免使用连续字符，如"123"或"abc"');
      }

      // 双因子认证提示
      if (enableTwoFactor && errors.length === 0) {
        suggestions.push('系统已启用双因子认证，将为您的账户提供额外安全保护');
      }

      return {
        isValid: errors.length === 0,
        errors,
        suggestions
      };
    } catch (error) {
      console.error('🔒 [密码策略] 验证密码时发生错误:', error);
      return {
        isValid: false,
        errors: ['密码验证失败，请稍后重试'],
        suggestions: []
      };
    }
  }

  /** 检查是否包含连续字符 */
  private static hasSequentialChars(password: string): boolean {
    const sequences = ['0123456789', 'abcdefghijklmnopqrstuvwxyz', 'qwertyuiop', 'asdfghjkl', 'zxcvbnm'];

    for (const sequence of sequences) {
      for (let i = 0; i <= sequence.length - 3; i++) {
        const subSeq = sequence.substring(i, i + 3);
        const reverseSeq = subSeq.split('').reverse().join('');

        if (password.toLowerCase().includes(subSeq) || password.toLowerCase().includes(reverseSeq)) {
          return true;
        }
      }
    }

    return false;
  }

  /** 生成密码强度评分 */
  public static async getPasswordStrength(password: string): Promise<{
    score: number;
    level: 'weak' | 'fair' | 'good' | 'strong';
    feedback: string;
  }> {
    const validation = await this.validatePassword(password);

    let score = 0;

    // 长度评分
    if (password.length >= 8) score += 25;
    else if (password.length >= 6) score += 15;

    // 字符类型评分
    if (/[a-z]/.test(password)) score += 15;
    if (/[A-Z]/.test(password)) score += 15;
    if (/\d/.test(password)) score += 15;
    if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) score += 20;

    // 长度奖励
    if (password.length >= 12) score += 10;

    // 扣分项
    if (/(.)\1{2,}/.test(password)) score -= 20;
    if (this.hasSequentialChars(password)) score -= 15;

    // 确保分数在0-100范围内
    score = Math.max(0, Math.min(100, score));

    let level: 'weak' | 'fair' | 'good' | 'strong';
    let feedback: string;

    if (score < 30) {
      level = 'weak';
      feedback = '密码强度较弱，建议增加复杂度';
    } else if (score < 60) {
      level = 'fair';
      feedback = '密码强度一般，建议添加更多字符类型';
    } else if (score < 80) {
      level = 'good';
      feedback = '密码强度良好';
    } else {
      level = 'strong';
      feedback = '密码强度很强';
    }

    return { score, level, feedback };
  }

  /** 生成符合策略的密码建议 */
  public static async generatePasswordSuggestion(): Promise<string> {
    const minLength = await ConfigManager.getPasswordMinLength();
    const length = Math.max(minLength, 12); // 至少12位

    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const symbols = '!@#$%^&*';

    let password = '';

    // 确保包含各种字符类型
    password += lowercase[Math.floor(Math.random() * lowercase.length)];
    password += uppercase[Math.floor(Math.random() * uppercase.length)];
    password += numbers[Math.floor(Math.random() * numbers.length)];
    password += symbols[Math.floor(Math.random() * symbols.length)];

    // 填充剩余长度
    const allChars = lowercase + uppercase + numbers + symbols;
    for (let i = password.length; i < length; i++) {
      password += allChars[Math.floor(Math.random() * allChars.length)];
    }

    // 打乱字符顺序
    return password
      .split('')
      .sort(() => Math.random() - 0.5)
      .join('');
  }
}

export default PasswordPolicy;
