/**
 * 配置一致性检查工具
 * 检查前后端配置是否一致，发现配置冲突和问题
 */

import SyncConfigManager from '../service/sync/SyncConfigManager';

interface ConfigCheckResult {
  isConsistent: boolean;
  issues: ConfigIssue[];
  recommendations: string[];
}

interface ConfigIssue {
  type: 'error' | 'warning' | 'info';
  category: string;
  message: string;
  currentValue?: any;
  recommendedValue?: any;
}

export class ConfigChecker {
  private configManager: SyncConfigManager;

  constructor() {
    this.configManager = SyncConfigManager.getInstance();
  }

  /**
   * 执行完整的配置检查
   */
  async checkConfiguration(): Promise<ConfigCheckResult> {
    const issues: ConfigIssue[] = [];
    const recommendations: string[] = [];

    // 1. 基础配置检查
    this.checkBasicConfig(issues, recommendations);

    // 2. 性能配置检查
    this.checkPerformanceConfig(issues, recommendations);

    // 3. 时间间隔配置检查
    this.checkIntervalConfig(issues, recommendations);

    // 4. 优先级配置检查
    this.checkPriorityConfig(issues, recommendations);

    // 5. 配置冲突检查
    this.checkConfigConflicts(issues, recommendations);

    const isConsistent = !issues.some(issue => issue.type === 'error');

    console.log('🔍 [配置检查] 检查完成:', {
      isConsistent,
      totalIssues: issues.length,
      errors: issues.filter(i => i.type === 'error').length,
      warnings: issues.filter(i => i.type === 'warning').length,
      recommendations: recommendations.length
    });

    return {
      isConsistent,
      issues,
      recommendations
    };
  }

  /**
   * 检查基础配置
   */
  private checkBasicConfig(issues: ConfigIssue[], recommendations: string[]): void {
    const config = this.configManager.getConfig();

    // 检查并发数与批处理大小的关系
    if (config.batchSize > config.maxConcurrency * 100) {
      issues.push({
        type: 'warning',
        category: '性能配置',
        message: '批处理大小可能过大，可能导致内存压力',
        currentValue: config.batchSize,
        recommendedValue: config.maxConcurrency * 50
      });
      recommendations.push(`建议将批处理大小调整为 ${config.maxConcurrency * 50} 以下`);
    }

    // 检查速率限制配置
    if (config.enableRateLimit && config.rateLimit > config.maxConcurrency * 3) {
      issues.push({
        type: 'warning',
        category: '速率控制',
        message: '速率限制设置过高，可能超过并发处理能力',
        currentValue: config.rateLimit,
        recommendedValue: config.maxConcurrency * 2
      });
      recommendations.push(`建议将速率限制调整为 ${config.maxConcurrency * 2} 以下`);
    }
  }

  /**
   * 检查性能配置
   */
  private checkPerformanceConfig(issues: ConfigIssue[], recommendations: string[]): void {
    const config = this.configManager.getConfig();

    // 检查重试配置
    if (config.maxRetries > 5) {
      issues.push({
        type: 'warning',
        category: '错误处理',
        message: '最大重试次数过高，可能导致长时间阻塞',
        currentValue: config.maxRetries,
        recommendedValue: 3
      });
    }

    if (config.retryDelaySeconds < 30) {
      issues.push({
        type: 'warning',
        category: '错误处理',
        message: '重试延迟过短，可能导致频繁重试',
        currentValue: config.retryDelaySeconds,
        recommendedValue: 60
      });
    }
  }

  /**
   * 检查时间间隔配置
   */
  private checkIntervalConfig(issues: ConfigIssue[], recommendations: string[]): void {
    const config = this.configManager.getConfig();

    if (!config.orderStatusConfigs || config.orderStatusConfigs.length === 0) {
      issues.push({
        type: 'error',
        category: '状态配置',
        message: '缺少订单状态同步配置'
      });
      return;
    }

    // 检查时间间隔设置
    config.orderStatusConfigs.forEach((statusConfig, index) => {
      if (statusConfig.intervalMinutes < 1) {
        issues.push({
          type: 'error',
          category: '时间间隔',
          message: `状态 ${statusConfig.name} 的同步间隔过短`,
          currentValue: statusConfig.intervalMinutes,
          recommendedValue: 5
        });
      }

      if (statusConfig.intervalMinutes > 1440) {
        issues.push({
          type: 'warning',
          category: '时间间隔',
          message: `状态 ${statusConfig.name} 的同步间隔超过24小时`,
          currentValue: statusConfig.intervalMinutes
        });
      }

      // 检查高优先级状态的间隔设置
      if (statusConfig.priority >= 8 && statusConfig.intervalMinutes > 60) {
        issues.push({
          type: 'warning',
          category: '优先级配置',
          message: `高优先级状态 ${statusConfig.name} 的同步间隔可能过长`,
          currentValue: statusConfig.intervalMinutes,
          recommendedValue: 30
        });
        recommendations.push(`建议将高优先级状态的同步间隔设置为30分钟以内`);
      }
    });
  }

  /**
   * 检查优先级配置
   */
  private checkPriorityConfig(issues: ConfigIssue[], recommendations: string[]): void {
    const config = this.configManager.getConfig();

    if (!config.orderStatusConfigs) return;

    // 检查优先级重复
    const priorities = config.orderStatusConfigs.map(c => c.priority);
    const duplicatePriorities = priorities.filter((p, i) => priorities.indexOf(p) !== i);

    if (duplicatePriorities.length > 0) {
      issues.push({
        type: 'warning',
        category: '优先级配置',
        message: `存在重复的优先级设置: ${[...new Set(duplicatePriorities)].join(', ')}`
      });
      recommendations.push('建议为每个状态设置不同的优先级以确保处理顺序明确');
    }

    // 检查优先级规则
    if (config.priorityRules) {
      const rulesByPriority = config.priorityRules.map(r => r.priority);
      const duplicateRulePriorities = rulesByPriority.filter((p, i) => rulesByPriority.indexOf(p) !== i);

      if (duplicateRulePriorities.length > 0) {
        issues.push({
          type: 'warning',
          category: '优先级规则',
          message: `优先级规则存在重复优先级: ${[...new Set(duplicateRulePriorities)].join(', ')}`
        });
      }
    }
  }

  /**
   * 检查配置冲突
   */
  private checkConfigConflicts(issues: ConfigIssue[], recommendations: string[]): void {
    const config = this.configManager.getConfig();

    // 检查速率限制与批处理的冲突
    if (config.enableRateLimit && config.rateLimit < config.batchSize / 10) {
      issues.push({
        type: 'warning',
        category: '配置冲突',
        message: '速率限制可能无法支持当前批处理大小的处理速度'
      });
      recommendations.push('建议调整速率限制或减小批处理大小');
    }

    // 检查并发数与系统资源的匹配
    if (config.maxConcurrency > 50) {
      issues.push({
        type: 'warning',
        category: '资源配置',
        message: '并发数设置较高，请确保系统资源充足'
      });
      recommendations.push('建议监控系统资源使用情况，必要时调整并发数');
    }
  }

  /**
   * 生成配置报告
   */
  generateReport(checkResult: ConfigCheckResult): string {
    const { isConsistent, issues, recommendations } = checkResult;

    let report = `\n📋 同步配置检查报告\n`;
    report += `==========================================\n`;
    report += `整体状态: ${isConsistent ? '✅ 配置一致' : '❌ 存在问题'}\n`;
    report += `问题总数: ${issues.length}\n`;
    report += `建议总数: ${recommendations.length}\n\n`;

    if (issues.length > 0) {
      report += `🔍 发现的问题:\n`;
      issues.forEach((issue, index) => {
        const icon = issue.type === 'error' ? '❌' : issue.type === 'warning' ? '⚠️' : 'ℹ️';
        report += `${index + 1}. ${icon} [${issue.category}] ${issue.message}\n`;
        if (issue.currentValue !== undefined) {
          report += `   当前值: ${issue.currentValue}\n`;
        }
        if (issue.recommendedValue !== undefined) {
          report += `   建议值: ${issue.recommendedValue}\n`;
        }
        report += `\n`;
      });
    }

    if (recommendations.length > 0) {
      report += `💡 优化建议:\n`;
      recommendations.forEach((rec, index) => {
        report += `${index + 1}. ${rec}\n`;
      });
    }

    return report;
  }
}

export default ConfigChecker;
