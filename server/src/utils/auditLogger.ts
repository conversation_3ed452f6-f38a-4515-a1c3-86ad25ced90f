import type { Request } from 'express';
import pool from './db';

// 审计日志类型
export enum AuditAction {
  // 用户操作
  USER_LOGIN = 'user_login',
  USER_LOGOUT = 'user_logout',
  USER_REGISTER = 'user_register',
  USER_CREATE = 'user_create',
  USER_UPDATE = 'user_update',
  USER_DELETE = 'user_delete',
  USER_RESET_PASSWORD = 'user_reset_password',
  USER_CHANGE_ROLE = 'user_change_role',
  USER_CHANGE_STATUS = 'user_change_status',

  // 权限操作
  PERMISSION_GRANT = 'permission_grant',
  PERMISSION_REVOKE = 'permission_revoke',
  ROLE_ASSIGN = 'role_assign',
  ROLE_REMOVE = 'role_remove',

  // 业务操作
  PLATFORM_CREATE = 'platform_create',
  PLATFORM_UPDATE = 'platform_update',
  PLATFORM_DELETE = 'platform_delete',
  PLATFORM_STATUS_CHANGE = 'platform_status_change',

  PROVIDER_CREATE = 'provider_create',
  PROVIDER_UPDATE = 'provider_update',
  PROVIDER_DELETE = 'provider_delete',
  PROVIDER_STATUS_CHANGE = 'provider_status_change',
  PROVIDER_API_TEST = 'provider_api_test',

  ORDER_CREATE = 'order_create',
  ORDER_UPDATE = 'order_update',
  ORDER_DELETE = 'order_delete',
  ORDER_CANCEL = 'order_cancel',
  ORDER_PROCESS = 'order_process',
  ORDER_COMPLETE = 'order_complete',
  ORDER_REFUND = 'order_refund',

  // 财务操作
  BALANCE_RECHARGE = 'balance_recharge',
  BALANCE_DEDUCT = 'balance_deduct',
  BALANCE_TRANSFER = 'balance_transfer',
  PRICE_UPDATE = 'price_update',

  // 系统操作
  SYSTEM_CONFIG_UPDATE = 'system_config_update',
  SYSTEM_BACKUP = 'system_backup',
  SYSTEM_RESTORE = 'system_restore',
  LICENSE_UPDATE = 'license_update',

  // 数据操作
  DATA_EXPORT = 'data_export',
  DATA_IMPORT = 'data_import',
  DATA_DELETE_BATCH = 'data_delete_batch',

  // 安全操作
  SECURITY_LOGIN_FAILED = 'security_login_failed',
  SECURITY_ACCESS_DENIED = 'security_access_denied',
  SECURITY_SUSPICIOUS_ACTIVITY = 'security_suspicious_activity'
}

// 审计日志级别
export enum AuditLevel {
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// 审计日志接口
export interface AuditLog {
  /** 操作用户ID */
  userId?: number;
  /** 操作用户名 */
  username?: string;
  /** 操作类型 */
  action: AuditAction;
  /** 日志级别 */
  level: AuditLevel;
  /** 操作描述 */
  description: string;
  /** 操作详情 */
  details?: Record<string, any>;
  /** 目标资源类型 */
  resourceType?: string;
  /** 目标资源ID */
  resourceId?: string | number;
  /** 操作前数据 */
  oldData?: Record<string, any>;
  /** 操作后数据 */
  newData?: Record<string, any>;
  /** 客户端IP */
  clientIp?: string;
  /** 用户代理 */
  userAgent?: string;
  /** 请求路径 */
  requestPath?: string;
  /** 请求方法 */
  requestMethod?: string;
  /** 操作结果 */
  result?: 'success' | 'failure' | 'partial';
  /** 错误信息 */
  errorMessage?: string;
  /** 操作时间 */
  timestamp?: Date;
}

/** 审计日志记录器 */
export class AuditLogger {
  /** 记录审计日志 */
  static async log(logData: AuditLog): Promise<void> {
    try {
      const sql = `
        INSERT INTO fd_audit_log (
          user_id, username, action, level, description, details,
          resource_type, resource_id, old_data, new_data,
          client_ip, user_agent, request_path, request_method,
          result, error_message, create_time
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
      `;

      const values = [
        logData.userId || null,
        logData.username || null,
        logData.action,
        logData.level,
        logData.description,
        logData.details ? JSON.stringify(logData.details) : null,
        logData.resourceType || null,
        logData.resourceId || null,
        logData.oldData ? JSON.stringify(logData.oldData) : null,
        logData.newData ? JSON.stringify(logData.newData) : null,
        logData.clientIp || null,
        logData.userAgent || null,
        logData.requestPath || null,
        logData.requestMethod || null,
        logData.result || 'success',
        logData.errorMessage || null
      ];

      await pool.execute(sql, values);

      // 如果是关键操作，同时记录到系统日志
      if (logData.level === AuditLevel.CRITICAL) {
        console.warn(`[CRITICAL AUDIT] ${logData.action}: ${logData.description}`, {
          userId: logData.userId,
          username: logData.username,
          details: logData.details
        });
      }
    } catch (error) {
      console.error('Failed to write audit log:', error);
      // 审计日志写入失败不应该影响业务流程，但需要记录错误
      console.error('Audit log data:', logData);
    }
  }

  /** 记录用户登录 */
  static async logUserLogin(req: Request, userId: number, username: string, success: boolean): Promise<void> {
    await this.log({
      userId: success ? userId : undefined,
      username,
      action: success ? AuditAction.USER_LOGIN : AuditAction.SECURITY_LOGIN_FAILED,
      level: success ? AuditLevel.INFO : AuditLevel.WARN,
      description: success ? '用户登录成功' : '用户登录失败',
      clientIp: this.getClientIp(req),
      userAgent: req.headers['user-agent'],
      requestPath: req.path,
      requestMethod: req.method,
      result: success ? 'success' : 'failure',
      errorMessage: success ? undefined : '用户名或密码错误'
    });
  }

  /** 记录用户注册 */
  static async logUserRegister(req: Request, userId: number, username: string, inviteCode: string): Promise<void> {
    await this.log({
      userId,
      username,
      action: AuditAction.USER_REGISTER,
      level: AuditLevel.INFO,
      description: '用户注册成功',
      details: { inviteCode },
      resourceType: 'user',
      resourceId: userId,
      clientIp: this.getClientIp(req),
      userAgent: req.headers['user-agent'],
      requestPath: req.path,
      requestMethod: req.method
    });
  }

  /** 记录数据变更 */
  static async logDataChange(
    req: Request,
    action: AuditAction,
    resourceType: string,
    resourceId: string | number,
    oldData?: Record<string, any>,
    newData?: Record<string, any>,
    description?: string
  ): Promise<void> {
    const user = (req as any).user;

    await this.log({
      userId: user?.userId,
      username: user?.userName,
      action,
      level: AuditLevel.INFO,
      description: description || `${resourceType} 数据变更`,
      resourceType,
      resourceId,
      oldData,
      newData,
      clientIp: this.getClientIp(req),
      userAgent: req.headers['user-agent'],
      requestPath: req.path,
      requestMethod: req.method
    });
  }

  /** 记录权限操作 */
  static async logPermissionChange(
    req: Request,
    action: AuditAction,
    targetUserId: number,
    targetUsername: string,
    permissions: string[],
    description: string
  ): Promise<void> {
    const user = (req as any).user;

    await this.log({
      userId: user?.userId,
      username: user?.userName,
      action,
      level: AuditLevel.WARN,
      description,
      details: { targetUserId, targetUsername, permissions },
      resourceType: 'user_permission',
      resourceId: targetUserId,
      clientIp: this.getClientIp(req),
      userAgent: req.headers['user-agent'],
      requestPath: req.path,
      requestMethod: req.method
    });
  }

  /** 记录安全事件 */
  static async logSecurityEvent(
    req: Request,
    action: AuditAction,
    description: string,
    details?: Record<string, any>
  ): Promise<void> {
    const user = (req as any).user;

    await this.log({
      userId: user?.userId,
      username: user?.userName,
      action,
      level: AuditLevel.ERROR,
      description,
      details,
      clientIp: this.getClientIp(req),
      userAgent: req.headers['user-agent'],
      requestPath: req.path,
      requestMethod: req.method,
      result: 'failure'
    });
  }

  /** 记录系统操作 */
  static async logSystemOperation(
    req: Request,
    action: AuditAction,
    description: string,
    details?: Record<string, any>
  ): Promise<void> {
    const user = (req as any).user;

    await this.log({
      userId: user?.userId,
      username: user?.userName,
      action,
      level: AuditLevel.CRITICAL,
      description,
      details,
      resourceType: 'system',
      clientIp: this.getClientIp(req),
      userAgent: req.headers['user-agent'],
      requestPath: req.path,
      requestMethod: req.method
    });
  }

  /** 获取客户端IP */
  private static getClientIp(req: Request): string {
    return (
      (req.headers['x-forwarded-for'] as string) ||
      (req.headers['x-real-ip'] as string) ||
      req.connection.remoteAddress ||
      req.socket.remoteAddress ||
      'unknown'
    )
      .split(',')[0]
      .trim();
  }

  /** 查询审计日志 */
  static async queryLogs(params: {
    userId?: number;
    action?: AuditAction;
    level?: AuditLevel;
    resourceType?: string;
    startTime?: Date;
    endTime?: Date;
    page?: number;
    pageSize?: number;
  }): Promise<{ logs: any[]; total: number }> {
    const { userId, action, level, resourceType, startTime, endTime, page = 1, pageSize = 20 } = params;

    let whereClause = 'WHERE 1=1';
    const whereParams: any[] = [];

    if (userId) {
      whereClause += ' AND user_id = ?';
      whereParams.push(userId);
    }

    if (action) {
      whereClause += ' AND action = ?';
      whereParams.push(action);
    }

    if (level) {
      whereClause += ' AND level = ?';
      whereParams.push(level);
    }

    if (resourceType) {
      whereClause += ' AND resource_type = ?';
      whereParams.push(resourceType);
    }

    if (startTime) {
      whereClause += ' AND create_time >= ?';
      whereParams.push(startTime);
    }

    if (endTime) {
      whereClause += ' AND create_time <= ?';
      whereParams.push(endTime);
    }

    // 查询总数
    const countSql = `SELECT COUNT(*) as total FROM fd_audit_log ${whereClause}`;
    const [countResult] = (await pool.execute(countSql, whereParams)) as any[];
    const total = countResult[0].total;

    // 查询数据
    const offset = (page - 1) * pageSize;
    const dataSql = `
      SELECT * FROM fd_audit_log 
      ${whereClause} 
      ORDER BY create_time DESC 
      LIMIT ? OFFSET ?
    `;
    const [logs] = (await pool.execute(dataSql, [...whereParams, pageSize, offset])) as any[];

    return { logs, total };
  }
}

// 创建审计日志表的SQL
export const createAuditLogTableSQL = `
CREATE TABLE IF NOT EXISTS fd_audit_log (
  log_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
  user_id BIGINT COMMENT '操作用户ID',
  username VARCHAR(100) COMMENT '操作用户名',
  action VARCHAR(50) NOT NULL COMMENT '操作类型',
  level ENUM('info', 'warn', 'error', 'critical') NOT NULL DEFAULT 'info' COMMENT '日志级别',
  description TEXT NOT NULL COMMENT '操作描述',
  details JSON COMMENT '操作详情',
  resource_type VARCHAR(50) COMMENT '资源类型',
  resource_id VARCHAR(100) COMMENT '资源ID',
  old_data JSON COMMENT '操作前数据',
  new_data JSON COMMENT '操作后数据',
  client_ip VARCHAR(45) COMMENT '客户端IP',
  user_agent TEXT COMMENT '用户代理',
  request_path VARCHAR(255) COMMENT '请求路径',
  request_method VARCHAR(10) COMMENT '请求方法',
  result ENUM('success', 'failure', 'partial') DEFAULT 'success' COMMENT '操作结果',
  error_message TEXT COMMENT '错误信息',
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  
  INDEX idx_user_id (user_id),
  INDEX idx_action (action),
  INDEX idx_level (level),
  INDEX idx_resource (resource_type, resource_id),
  INDEX idx_create_time (create_time),
  INDEX idx_user_action_time (user_id, action, create_time)
) ENGINE=InnoDB COMMENT='审计日志表';
`;
