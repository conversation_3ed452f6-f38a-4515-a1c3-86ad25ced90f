/**
 * 权限操作日志记录工具
 * 记录所有权限相关的操作日志
 */

import { executeQuery } from './db';

export interface PermissionLogData {
  operation_type:
    | 'role_create'
    | 'role_update'
    | 'role_delete'
    | 'permission_grant'
    | 'permission_revoke'
    | 'user_role_assign'
    | 'user_role_remove';
  target_type: 'user' | 'role' | 'permission';
  target_id: number;
  operator_id: number;
  operation_data?: any;
  operation_result?: 'success' | 'failed';
  error_message?: string;
  client_ip?: string;
  user_agent?: string;
}

/**
 * 记录权限操作日志
 */
export async function logPermissionOperation(data: PermissionLogData): Promise<void> {
  try {
    const sql = `
      INSERT INTO fd_permission_log (
        operation_type, target_type, target_id, operator_id,
        operation_data, operation_result, error_message,
        client_ip, user_agent
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    await executeQuery(sql, [
      data.operation_type,
      data.target_type,
      data.target_id,
      data.operator_id,
      data.operation_data ? JSON.stringify(data.operation_data) : null,
      data.operation_result || 'success',
      data.error_message || null,
      data.client_ip || null,
      data.user_agent || null
    ]);

    console.log(
      `📝 [权限日志] ${data.operation_type} - ${data.target_type}:${data.target_id} by user:${data.operator_id}`
    );
  } catch (error) {
    console.error('❌ 记录权限操作日志失败:', error);
    // 不抛出错误，避免影响主要业务流程
  }
}

/**
 * 获取权限操作日志
 */
export async function getPermissionLogs(options: {
  page?: number;
  limit?: number;
  operation_type?: string;
  target_type?: string;
  operator_id?: number;
  start_time?: string;
  end_time?: string;
}): Promise<{
  list: any[];
  total: number;
  page: number;
  limit: number;
}> {
  try {
    const { page = 1, limit = 20, operation_type, target_type, operator_id, start_time, end_time } = options;

    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    if (operation_type) {
      whereClause += ' AND pl.operation_type = ?';
      params.push(operation_type);
    }

    if (target_type) {
      whereClause += ' AND pl.target_type = ?';
      params.push(target_type);
    }

    if (operator_id) {
      whereClause += ' AND pl.operator_id = ?';
      params.push(operator_id);
    }

    if (start_time) {
      whereClause += ' AND pl.create_time >= ?';
      params.push(start_time);
    }

    if (end_time) {
      whereClause += ' AND pl.create_time <= ?';
      params.push(end_time);
    }

    // 查询总数
    const countSql = `SELECT COUNT(*) as total FROM fd_permission_log pl ${whereClause}`;
    const [countResult] = await executeQuery(countSql, params);
    const total = (countResult as any).total;

    // 查询日志列表
    const listSql = `
      SELECT 
        pl.*,
        u.username as operator_name,
        CASE pl.target_type
          WHEN 'user' THEN (SELECT username FROM fd_user WHERE user_id = pl.target_id)
          WHEN 'role' THEN (SELECT role_name FROM fd_role WHERE role_id = pl.target_id)
          WHEN 'permission' THEN (SELECT permission_name FROM fd_permission WHERE permission_id = pl.target_id)
          ELSE NULL
        END as target_name
      FROM fd_permission_log pl
      LEFT JOIN fd_user u ON pl.operator_id = u.user_id
      ${whereClause}
      ORDER BY pl.create_time DESC
      LIMIT ? OFFSET ?
    `;

    const logs = await executeQuery(listSql, [...params, limit, offset]);

    return {
      list: logs,
      total,
      page,
      limit
    };
  } catch (error) {
    console.error('❌ 获取权限操作日志失败:', error);
    throw error;
  }
}

/**
 * 清理过期日志
 */
export async function cleanExpiredLogs(daysToKeep: number = 90): Promise<number> {
  try {
    const sql = `
      DELETE FROM fd_permission_log 
      WHERE create_time < DATE_SUB(NOW(), INTERVAL ? DAY)
    `;

    const result = await executeQuery(sql, [daysToKeep]);
    const deletedCount = (result as any).affectedRows || 0;

    console.log(`🧹 [权限日志] 清理了 ${deletedCount} 条过期日志记录`);
    return deletedCount;
  } catch (error) {
    console.error('❌ 清理过期权限日志失败:', error);
    throw error;
  }
}
