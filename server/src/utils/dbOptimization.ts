import { DB_OPTIMIZATION_CONFIG } from '../config/performance';
import { executeQuery } from './db';

/**
 * 数据库优化工具
 * 遵循项目规范，提供完整的数据库优化功能
 */

interface QueryStats {
  query: string;
  executionTime: number;
  timestamp: Date;
  params?: any[];
}

class DatabaseOptimizer {
  private queryStats: QueryStats[] = [];
  private slowQueries: QueryStats[] = [];

  /**
   * 执行优化的查询
   */
  async executeOptimizedQuery(
    query: string,
    params?: any[],
    options?: {
      useIndex?: string;
      timeout?: number;
      cache?: boolean;
    }
  ): Promise<any> {
    const startTime = Date.now();

    try {
      // 添加索引提示
      let optimizedQuery = query;
      if (options?.useIndex) {
        optimizedQuery = this.addIndexHint(query, options.useIndex);
      }

      // 执行查询
      const result = await executeQuery(optimizedQuery, params);

      // 记录查询统计
      const executionTime = Date.now() - startTime;
      this.recordQueryStats(query, executionTime, params);

      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      this.recordQueryStats(query, executionTime, params);
      throw error;
    }
  }

  /**
   * 批量插入优化
   */
  async batchInsert(table: string, data: any[], batchSize: number = DB_OPTIMIZATION_CONFIG.batchSize): Promise<void> {
    if (data.length === 0) return;

    const columns = Object.keys(data[0]);
    const placeholders = columns.map(() => '?').join(', ');

    for (let i = 0; i < data.length; i += batchSize) {
      const batch = data.slice(i, i + batchSize);
      const values = batch.map(row => columns.map(col => row[col])).flat();
      const valueGroups = batch.map(() => `(${placeholders})`).join(', ');

      const query = `INSERT INTO ${table} (${columns.join(', ')}) VALUES ${valueGroups}`;
      await this.executeOptimizedQuery(query, values);
    }
  }

  /**
   * 批量更新优化
   */
  async batchUpdate(
    table: string,
    updates: Array<{ condition: string; data: any; params?: any[] }>,
    batchSize: number = DB_OPTIMIZATION_CONFIG.batchSize
  ): Promise<void> {
    for (let i = 0; i < updates.length; i += batchSize) {
      const batch = updates.slice(i, i + batchSize);

      for (const update of batch) {
        const setClause = Object.keys(update.data)
          .map(key => `${key} = ?`)
          .join(', ');

        const values = [...Object.values(update.data), ...(update.params || [])];
        const query = `UPDATE ${table} SET ${setClause} WHERE ${update.condition}`;

        await this.executeOptimizedQuery(query, values);
      }
    }
  }

  /**
   * 分页查询优化
   */
  async paginatedQuery(
    baseQuery: string,
    params: any[] = [],
    page: number = 1,
    pageSize: number = DB_OPTIMIZATION_CONFIG.defaultPageSize
  ): Promise<{ data: any[]; total: number; pagination: any }> {
    // 限制最大分页大小
    const limitedPageSize = Math.min(pageSize, DB_OPTIMIZATION_CONFIG.maxPageSize);
    const offset = (page - 1) * limitedPageSize;

    // 获取总数（优化：使用COUNT(1)而不是COUNT(*)）
    const countQuery = baseQuery.replace(/SELECT .+ FROM/, 'SELECT COUNT(1) as total FROM');
    const countResult = await this.executeOptimizedQuery(countQuery, params);
    const total = countResult[0]?.total || 0;

    // 获取分页数据
    const dataQuery = `${baseQuery} LIMIT ${limitedPageSize} OFFSET ${offset}`;
    const data = await this.executeOptimizedQuery(dataQuery, params);

    return {
      data,
      total,
      pagination: {
        page,
        pageSize: limitedPageSize,
        total,
        totalPages: Math.ceil(total / limitedPageSize)
      }
    };
  }

  /**
   * 获取慢查询
   */
  getSlowQueries(limit: number = 10): QueryStats[] {
    return this.slowQueries.sort((a, b) => b.executionTime - a.executionTime).slice(0, limit);
  }

  /**
   * 获取查询统计
   */
  getQueryStats(): {
    totalQueries: number;
    averageExecutionTime: number;
    slowQueries: number;
    recentQueries: QueryStats[];
  } {
    const totalQueries = this.queryStats.length;
    const averageExecutionTime =
      totalQueries > 0 ? this.queryStats.reduce((sum, stat) => sum + stat.executionTime, 0) / totalQueries : 0;
    const slowQueries = this.slowQueries.length;
    const recentQueries = this.queryStats.slice(-10);

    return {
      totalQueries,
      averageExecutionTime,
      slowQueries,
      recentQueries
    };
  }

  /**
   * 清理查询统计
   */
  clearStats(): void {
    this.queryStats = [];
    this.slowQueries = [];
  }

  /**
   * 分析表性能
   */
  async analyzeTablePerformance(tableName: string): Promise<any> {
    const queries = [
      // 表信息
      `SHOW TABLE STATUS LIKE '${tableName}'`,
      // 索引信息
      `SHOW INDEX FROM ${tableName}`,
      // 表大小
      `SELECT 
        table_name,
        ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'size_mb',
        ROUND((data_length / 1024 / 1024), 2) AS 'data_mb',
        ROUND((index_length / 1024 / 1024), 2) AS 'index_mb',
        table_rows
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() AND table_name = '${tableName}'`
    ];

    const [tableStatus, indexes, tableSize] = await Promise.all(
      queries.map(query => this.executeOptimizedQuery(query))
    );

    return {
      tableStatus: tableStatus[0],
      indexes,
      tableSize: tableSize[0]
    };
  }

  /**
   * 优化表
   */
  async optimizeTable(tableName: string): Promise<void> {
    await this.executeOptimizedQuery(`OPTIMIZE TABLE ${tableName}`);
    console.log(`🔧 [数据库优化] 优化表: ${tableName}`);
  }

  /**
   * 分析表
   */
  async analyzeTable(tableName: string): Promise<void> {
    await this.executeOptimizedQuery(`ANALYZE TABLE ${tableName}`);
    console.log(`📊 [数据库优化] 分析表: ${tableName}`);
  }

  /**
   * 检查表
   */
  async checkTable(tableName: string): Promise<any> {
    const result = await this.executeOptimizedQuery(`CHECK TABLE ${tableName}`);
    console.log(`✅ [数据库优化] 检查表: ${tableName}`);
    return result;
  }

  /**
   * 修复表
   */
  async repairTable(tableName: string): Promise<any> {
    const result = await this.executeOptimizedQuery(`REPAIR TABLE ${tableName}`);
    console.log(`🔧 [数据库优化] 修复表: ${tableName}`);
    return result;
  }

  /**
   * 获取数据库状态
   */
  async getDatabaseStatus(): Promise<any> {
    const queries = [
      'SHOW STATUS LIKE "Connections"',
      'SHOW STATUS LIKE "Threads_connected"',
      'SHOW STATUS LIKE "Threads_running"',
      'SHOW STATUS LIKE "Queries"',
      'SHOW STATUS LIKE "Slow_queries"',
      'SHOW STATUS LIKE "Uptime"',
      'SHOW VARIABLES LIKE "max_connections"'
    ];

    const results = await Promise.all(queries.map(query => this.executeOptimizedQuery(query)));

    const status: any = {};
    results.forEach(result => {
      result.forEach((row: any) => {
        status[row.Variable_name] = row.Value;
      });
    });

    return status;
  }

  /**
   * 获取进程列表
   */
  async getProcessList(): Promise<any> {
    return await this.executeOptimizedQuery('SHOW PROCESSLIST');
  }

  /**
   * 记录查询统计
   */
  private recordQueryStats(query: string, executionTime: number, params?: any[]): void {
    const stats: QueryStats = {
      query,
      executionTime,
      timestamp: new Date(),
      params
    };

    this.queryStats.push(stats);

    // 记录慢查询
    if (executionTime > DB_OPTIMIZATION_CONFIG.slowQueryThreshold) {
      this.slowQueries.push(stats);
      console.warn(`🐌 [数据库优化] 慢查询检测: ${executionTime}ms - ${query}`);
    }

    // 限制统计数据大小
    if (this.queryStats.length > 1000) {
      this.queryStats = this.queryStats.slice(-500);
    }

    if (this.slowQueries.length > 100) {
      this.slowQueries = this.slowQueries.slice(-50);
    }
  }

  /**
   * 添加索引提示
   */
  private addIndexHint(query: string, indexName: string): string {
    // 简单的索引提示添加逻辑
    const fromMatch = query.match(/FROM\s+(\w+)/i);
    if (fromMatch) {
      const tableName = fromMatch[1];
      return query.replace(new RegExp(`FROM\\s+${tableName}`, 'i'), `FROM ${tableName} USE INDEX (${indexName})`);
    }
    return query;
  }
}

// 创建数据库优化器实例
export const dbOptimizer = new DatabaseOptimizer();

// 优化装饰器
export function OptimizedQuery(options?: { useIndex?: string; timeout?: number }) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      if (args.length >= 2 && typeof args[0] === 'string') {
        // 如果是查询方法，使用优化查询
        return await dbOptimizer.executeOptimizedQuery(args[0], args[1], options);
      }

      // 否则执行原方法
      return await method.apply(this, args);
    };
  };
}

export default dbOptimizer;
