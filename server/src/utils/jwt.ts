import jwt from 'jsonwebtoken';
import dotenv from 'dotenv';
import ConfigManager from './configManager';

dotenv.config();

const JWT_SECRET = process.env.JWT_SECRET || 'your_jwt_secret';
const JWT_REFRESH_EXPIRES_IN = process.env.JWT_REFRESH_EXPIRES_IN || '7d';

export interface TokenPayload {
  userId: number;
  userName: string;
  roles: string[];
}

/** 生成访问令牌 */
export async function generateAccessToken(payload: TokenPayload): Promise<string> {
  // 从配置中获取Token过期时间
  const tokenExpiry = await ConfigManager.getTokenExpiry();
  const expiresIn = `${tokenExpiry}h`;

  return jwt.sign(payload, JWT_SECRET, { expiresIn } as jwt.SignOptions);
}

/** 生成刷新令牌 */
export function generateRefreshToken(payload: TokenPayload): string {
  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_REFRESH_EXPIRES_IN } as jwt.SignOptions);
}

/** 验证令牌 */
export function verifyToken(token: string): TokenPayload | null {
  try {
    return jwt.verify(token, JWT_SECRET) as TokenPayload;
  } catch (error) {
    return null;
  }
}

/** 从请求头中获取令牌 */
export function getTokenFromHeader(authHeader: string | undefined): string | null {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null;
  }
  return authHeader.split(' ')[1];
}
