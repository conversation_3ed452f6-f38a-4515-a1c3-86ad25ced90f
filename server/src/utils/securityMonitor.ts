import * as fs from 'node:fs';
import * as path from 'node:path';
import { formatDateTimeForLog, getCurrentISOString } from './timeUtils';

/** 安全监控和报告系统 */

interface SecurityEvent {
  timestamp: string;
  type: 'DATA_TAMPERING' | 'SUSPICIOUS_ACCESS' | 'VALIDATION_FAILURE' | 'EXTERNAL_VALIDATION_REQUIRED';
  domain: string;
  details: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  clientInfo?: {
    ip?: string;
    userAgent?: string;
    host?: string;
  };
}

class SecurityMonitor {
  private logFile: string;
  private events: SecurityEvent[] = [];
  private maxEvents = 1000; // 最多保存1000个事件

  constructor() {
    this.logFile = path.join(__dirname, '../../logs/security.log');
    this.ensureLogDirectory();
  }

  /** 确保日志目录存在 */
  private ensureLogDirectory(): void {
    const logDir = path.dirname(this.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }

  /** 记录安全事件 */
  logSecurityEvent(event: Omit<SecurityEvent, 'timestamp'>): void {
    const securityEvent: SecurityEvent = {
      ...event,
      timestamp: getCurrentISOString()
    };

    // 添加到内存
    this.events.unshift(securityEvent);
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(0, this.maxEvents);
    }

    // 写入文件
    const logLine = `${JSON.stringify(securityEvent)}\n`;
    fs.appendFileSync(this.logFile, logLine);

    // 控制台输出
    const severityEmoji = {
      LOW: '🟡',
      MEDIUM: '🟠',
      HIGH: '🔴',
      CRITICAL: '🚨'
    };

    console.log(
      `${formatDateTimeForLog()} ${severityEmoji[event.severity]} [安全监控] ${event.type}: ${event.details}`
    );

    // 关键事件立即处理
    if (event.severity === 'CRITICAL') {
      this.handleCriticalEvent(securityEvent);
    }
  }

  /** 处理关键安全事件 */
  private handleCriticalEvent(event: SecurityEvent): void {
    console.error('🚨 [关键安全事件] 检测到严重安全威胁:', event);

    // 这里可以添加：
    // 1. 发送邮件通知
    // 2. 调用webhook
    // 3. 自动禁用相关授权
    // 4. 生成安全报告
  }

  /** 检测数据篡改 */
  detectDataTampering(domain: string, details: string, clientInfo?: any): void {
    this.logSecurityEvent({
      type: 'DATA_TAMPERING',
      domain,
      details,
      severity: 'CRITICAL',
      clientInfo
    });
  }

  /** 检测可疑访问 */
  detectSuspiciousAccess(domain: string, details: string, clientInfo?: any): void {
    this.logSecurityEvent({
      type: 'SUSPICIOUS_ACCESS',
      domain,
      details,
      severity: 'HIGH',
      clientInfo
    });
  }

  /** 记录验证失败 */
  logValidationFailure(domain: string, details: string, clientInfo?: any): void {
    this.logSecurityEvent({
      type: 'VALIDATION_FAILURE',
      domain,
      details,
      severity: 'MEDIUM',
      clientInfo
    });
  }

  /** 记录需要外部验证 */
  logExternalValidationRequired(domain: string, details: string): void {
    this.logSecurityEvent({
      type: 'EXTERNAL_VALIDATION_REQUIRED',
      domain,
      details,
      severity: 'LOW'
    });
  }

  /** 获取最近的安全事件 */
  getRecentEvents(limit = 50): SecurityEvent[] {
    return this.events.slice(0, limit);
  }

  /** 获取特定域名的安全事件 */
  getEventsByDomain(domain: string, limit = 50): SecurityEvent[] {
    return this.events.filter(event => event.domain === domain).slice(0, limit);
  }

  /** 获取安全统计 */
  getSecurityStats(): {
    totalEvents: number;
    eventsByType: Record<string, number>;
    eventsBySeverity: Record<string, number>;
    recentCriticalEvents: SecurityEvent[];
  } {
    const eventsByType: Record<string, number> = {};
    const eventsBySeverity: Record<string, number> = {};

    this.events.forEach(event => {
      eventsByType[event.type] = (eventsByType[event.type] || 0) + 1;
      eventsBySeverity[event.severity] = (eventsBySeverity[event.severity] || 0) + 1;
    });

    const recentCriticalEvents = this.events.filter(event => event.severity === 'CRITICAL').slice(0, 10);

    return {
      totalEvents: this.events.length,
      eventsByType,
      eventsBySeverity,
      recentCriticalEvents
    };
  }

  /** 生成安全报告 */
  generateSecurityReport(): string {
    const stats = this.getSecurityStats();
    const now = new Date().toISOString();

    let report = `# 安全监控报告\n\n`;
    report += `生成时间: ${now}\n\n`;

    report += `## 总体统计\n`;
    report += `- 总事件数: ${stats.totalEvents}\n`;
    report += `- 关键事件数: ${stats.eventsBySeverity.CRITICAL || 0}\n`;
    report += `- 高危事件数: ${stats.eventsBySeverity.HIGH || 0}\n\n`;

    report += `## 事件类型分布\n`;
    Object.entries(stats.eventsByType).forEach(([type, count]) => {
      report += `- ${type}: ${count}\n`;
    });

    report += `\n## 严重程度分布\n`;
    Object.entries(stats.eventsBySeverity).forEach(([severity, count]) => {
      report += `- ${severity}: ${count}\n`;
    });

    if (stats.recentCriticalEvents.length > 0) {
      report += `\n## 最近关键事件\n`;
      stats.recentCriticalEvents.forEach(event => {
        report += `- ${event.timestamp}: ${event.details}\n`;
      });
    }

    return report;
  }

  /** 清理旧日志 */
  cleanupOldLogs(daysToKeep = 30): void {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    this.events = this.events.filter(event => new Date(event.timestamp) > cutoffDate);

    console.log(`${formatDateTimeForLog()} 🧹 [安全监控] 已清理 ${daysToKeep} 天前的安全日志`);
  }
}

// 单例实例
export const securityMonitor = new SecurityMonitor();

// 定期清理旧日志（每天执行一次）
setInterval(
  () => {
    securityMonitor.cleanupOldLogs();
  },
  24 * 60 * 60 * 1000
);

export default SecurityMonitor;
