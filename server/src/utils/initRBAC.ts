/**
 * RBAC权限管理系统初始化
 * 创建权限管理相关表和初始化数据
 */

import fs from 'node:fs';
import path from 'node:path';
import pool from './db';

/**
 * 初始化RBAC权限管理系统
 */
export async function initRBAC(): Promise<void> {
  try {
    console.log('🔐 开始初始化RBAC权限管理系统...');

    // 1. 创建RBAC表结构
    await createRBACTables();

    // 2. 初始化基础数据
    await initRBACData();

    console.log('✅ RBAC权限管理系统初始化完成');
  } catch (error) {
    console.error('❌ RBAC权限管理系统初始化失败:', error);
    throw error;
  }
}

/**
 * 创建RBAC表结构
 */
async function createRBACTables(): Promise<void> {
  try {
    console.log('📋 创建RBAC表结构...');

    const schemaPath = path.join(__dirname, '../database/rbac-schema.sql');
    const schemaSql = fs.readFileSync(schemaPath, 'utf8');

    // 分割SQL语句并执行
    const statements = schemaSql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    for (const statement of statements) {
      if (statement.trim()) {
        await pool.execute(statement);
      }
    }

    console.log('✅ RBAC表结构创建完成');
  } catch (error) {
    console.error('❌ 创建RBAC表结构失败:', error);
    throw error;
  }
}

/**
 * 初始化RBAC基础数据
 */
async function initRBACData(): Promise<void> {
  try {
    console.log('📊 初始化RBAC基础数据...');

    // 检查是否已经初始化过
    const [roleRows] = await pool.execute('SELECT COUNT(*) as count FROM fd_role');
    const roleCount = (roleRows as any)[0].count;

    if (roleCount > 0) {
      console.log('⚠️ RBAC数据已存在，跳过初始化');
      return;
    }

    const dataPath = path.join(__dirname, '../database/rbac-init-data.sql');
    const dataSql = fs.readFileSync(dataPath, 'utf8');

    // 分割SQL语句并执行
    const statements = dataSql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    for (const statement of statements) {
      if (statement.trim()) {
        await pool.execute(statement);
      }
    }

    console.log('✅ RBAC基础数据初始化完成');
  } catch (error) {
    console.error('❌ 初始化RBAC基础数据失败:', error);
    throw error;
  }
}

/**
 * 检查RBAC系统是否已初始化
 */
export async function isRBACInitialized(): Promise<boolean> {
  try {
    // 检查关键表是否存在
    const [tables] = await pool.execute(`
      SELECT COUNT(*) as count 
      FROM information_schema.tables 
      WHERE table_schema = DATABASE() 
      AND table_name IN ('fd_role', 'fd_permission', 'fd_user_role', 'fd_role_permission')
    `);

    const tableCount = (tables as any)[0].count;

    if (tableCount < 4) {
      return false;
    }

    // 检查是否有基础数据
    const [roleRows] = await pool.execute('SELECT COUNT(*) as count FROM fd_role');
    const roleCount = (roleRows as any)[0].count;

    return roleCount > 0;
  } catch (error) {
    console.error('检查RBAC初始化状态失败:', error);
    return false;
  }
}

/**
 * 重置RBAC系统（危险操作，仅用于开发环境）
 */
export async function resetRBAC(): Promise<void> {
  try {
    console.log('⚠️ 开始重置RBAC权限管理系统...');

    // 删除关联表数据（注意顺序）
    await pool.execute('DELETE FROM fd_permission_log');
    await pool.execute('DELETE FROM fd_user_permission');
    await pool.execute('DELETE FROM fd_role_permission');
    await pool.execute('DELETE FROM fd_user_role');
    await pool.execute('DELETE FROM fd_permission');
    await pool.execute('DELETE FROM fd_role');

    // 重新初始化
    await initRBACData();

    console.log('✅ RBAC权限管理系统重置完成');
  } catch (error) {
    console.error('❌ 重置RBAC权限管理系统失败:', error);
    throw error;
  }
}

/**
 * 获取RBAC系统统计信息
 */
export async function getRBACStats(): Promise<{
  roleCount: number;
  permissionCount: number;
  userRoleCount: number;
  rolePermissionCount: number;
}> {
  try {
    const [roleResult] = await pool.execute('SELECT COUNT(*) as count FROM fd_role');
    const [permissionResult] = await pool.execute('SELECT COUNT(*) as count FROM fd_permission');
    const [userRoleResult] = await pool.execute('SELECT COUNT(*) as count FROM fd_user_role');
    const [rolePermissionResult] = await pool.execute('SELECT COUNT(*) as count FROM fd_role_permission');

    return {
      roleCount: (roleResult as any)[0].count,
      permissionCount: (permissionResult as any)[0].count,
      userRoleCount: (userRoleResult as any)[0].count,
      rolePermissionCount: (rolePermissionResult as any)[0].count
    };
  } catch (error) {
    console.error('获取RBAC统计信息失败:', error);
    throw error;
  }
}
