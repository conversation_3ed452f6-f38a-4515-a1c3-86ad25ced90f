import type { Response } from 'express';

/**
 * 统一响应处理工具
 * 遵循项目规范，提供简单有效的响应处理方案
 */

// 响应状态码枚举
export enum ResponseCode {
  SUCCESS = '0000',
  ERROR = '9999',
  PARAM_ERROR = '1001',
  AUTH_ERROR = '1002',
  FORBIDDEN = '1003',
  TOKEN_INVALID = '1004',
  TOKEN_EXPIRED = '1005',
  LOGOUT_CODES = '1006',
  MODAL_LOGOUT_CODES = '1007',
  SYSTEM_ERROR = '5000',
  PERMISSION_DENIED = '1008',
  DATA_EXISTS = '1009',
  DATA_NOT_FOUND = '1010',
  RATE_LIMIT = '1011',
  NOT_LOGGED_IN = '1012',
  UNAUTHORIZED = '1013',
  CONFIG_ERROR = '1014',
  EXTERNAL_API_ERROR = '1015',
  SERVICE_UNAVAILABLE = '5001',
  NOT_FOUND = 'NOT_FOUND'
}

/**
 * 创建错误响应
 * @param message 错误消息
 * @param code 响应码
 * @param data 可选的错误数据
 * @returns 错误响应对象
 */
export function createErrorResponse(message: string, code: ResponseCode = ResponseCode.ERROR, data: any = null) {
  return {
    code,
    msg: message,
    data
  };
}

/**
 * 创建成功响应
 * @param data 响应数据
 * @param message 成功消息
 * @returns 成功响应对象
 */
export function createSuccessResponse(data: any = null, message = '操作成功') {
  return {
    code: ResponseCode.SUCCESS,
    msg: message,
    data
  };
}

/**
 * 发送错误响应
 * @param res Express响应对象
 * @param message 错误消息
 * @param code 响应码
 */
export function sendError(res: Response, message: string, code: ResponseCode = ResponseCode.ERROR) {
  return res.json(createErrorResponse(message, code));
}

/**
 * 发送成功响应
 * @param res Express响应对象
 * @param data 响应数据
 * @param message 成功消息
 */
export function sendSuccess(res: Response, data: any = null, message = '操作成功') {
  return res.json(createSuccessResponse(data, message));
}

/**
 * 统一异常处理装饰器
 * @param target 目标函数
 * @returns 包装后的函数
 */
export function handleErrors(target: any) {
  return async (req: any, res: Response, next: any) => {
    try {
      await target(req, res, next);
    } catch (error) {
      console.error('API错误:', error);
      res.json(createErrorResponse('服务器内部错误', ResponseCode.SYSTEM_ERROR));
    }
  };
}
