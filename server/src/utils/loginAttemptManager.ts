import ConfigManager from './configManager';

/** 登录尝试记录 */
interface LoginAttempt {
  username: string;
  attempts: number;
  lastAttempt: Date;
  lockedUntil?: Date;
}

/** 登录尝试管理器 */
export class LoginAttemptManager {
  private static attempts = new Map<string, LoginAttempt>();

  /** 记录登录失败 */
  public static async recordFailedAttempt(username: string): Promise<{
    isLocked: boolean;
    remainingAttempts: number;
    lockDuration?: number;
  }> {
    try {
      const maxAttempts = await ConfigManager.getMaxLoginAttempts();
      const now = new Date();

      // 获取或创建用户的尝试记录
      let attempt = this.attempts.get(username);
      if (!attempt) {
        attempt = {
          username,
          attempts: 0,
          lastAttempt: now
        };
      }

      // 检查是否仍在锁定期内
      if (attempt.lockedUntil && now < attempt.lockedUntil) {
        const lockDuration = Math.ceil((attempt.lockedUntil.getTime() - now.getTime()) / 1000);
        return {
          isLocked: true,
          remainingAttempts: 0,
          lockDuration
        };
      }

      // 如果距离上次尝试超过1小时，重置计数
      const hourAgo = new Date(now.getTime() - 60 * 60 * 1000);
      if (attempt.lastAttempt < hourAgo) {
        attempt.attempts = 0;
      }

      // 增加失败次数
      attempt.attempts++;
      attempt.lastAttempt = now;

      // 检查是否需要锁定
      if (attempt.attempts >= maxAttempts) {
        // 计算锁定时间：基础15分钟 + 额外尝试次数 * 5分钟
        const baseLockMinutes = 15;
        const extraMinutes = (attempt.attempts - maxAttempts) * 5;
        const lockMinutes = Math.min(baseLockMinutes + extraMinutes, 120); // 最多锁定2小时

        attempt.lockedUntil = new Date(now.getTime() + lockMinutes * 60 * 1000);

        console.warn(`🔒 [登录限制] 用户 ${username} 因连续失败 ${attempt.attempts} 次被锁定 ${lockMinutes} 分钟`);

        this.attempts.set(username, attempt);

        return {
          isLocked: true,
          remainingAttempts: 0,
          lockDuration: lockMinutes * 60
        };
      }

      this.attempts.set(username, attempt);

      const remainingAttempts = maxAttempts - attempt.attempts;
      console.log(`🔒 [登录限制] 用户 ${username} 登录失败，剩余尝试次数: ${remainingAttempts}`);

      return {
        isLocked: false,
        remainingAttempts
      };
    } catch (error) {
      console.error('🔒 [登录限制] 记录失败尝试时发生错误:', error);
      return {
        isLocked: false,
        remainingAttempts: 1
      };
    }
  }

  /** 记录登录成功，清除失败记录 */
  public static clearFailedAttempts(username: string): void {
    if (this.attempts.has(username)) {
      console.log(`🔒 [登录限制] 用户 ${username} 登录成功，清除失败记录`);
      this.attempts.delete(username);
    }
  }

  /** 检查用户是否被锁定 */
  public static isUserLocked(username: string): {
    isLocked: boolean;
    lockDuration?: number;
  } {
    const attempt = this.attempts.get(username);
    if (!attempt || !attempt.lockedUntil) {
      return { isLocked: false };
    }

    const now = new Date();
    if (now >= attempt.lockedUntil) {
      // 锁定期已过，清除锁定状态
      attempt.lockedUntil = undefined;
      this.attempts.set(username, attempt);
      return { isLocked: false };
    }

    const lockDuration = Math.ceil((attempt.lockedUntil.getTime() - now.getTime()) / 1000);
    return {
      isLocked: true,
      lockDuration
    };
  }

  /** 获取用户的登录尝试信息 */
  public static async getUserAttemptInfo(username: string): Promise<{
    attempts: number;
    maxAttempts: number;
    remainingAttempts: number;
    isLocked: boolean;
    lockDuration?: number;
  }> {
    try {
      const maxAttempts = await ConfigManager.getMaxLoginAttempts();
      const attempt = this.attempts.get(username);

      if (!attempt) {
        return {
          attempts: 0,
          maxAttempts,
          remainingAttempts: maxAttempts,
          isLocked: false
        };
      }

      const lockStatus = this.isUserLocked(username);
      const remainingAttempts = Math.max(0, maxAttempts - attempt.attempts);

      return {
        attempts: attempt.attempts,
        maxAttempts,
        remainingAttempts,
        isLocked: lockStatus.isLocked,
        lockDuration: lockStatus.lockDuration
      };
    } catch (error) {
      console.error('🔒 [登录限制] 获取用户尝试信息时发生错误:', error);
      return {
        attempts: 0,
        maxAttempts: 5,
        remainingAttempts: 5,
        isLocked: false
      };
    }
  }

  /** 管理员解锁用户 */
  public static unlockUser(username: string): boolean {
    const attempt = this.attempts.get(username);
    if (attempt && attempt.lockedUntil) {
      attempt.lockedUntil = undefined;
      attempt.attempts = 0;
      this.attempts.set(username, attempt);
      console.log(`🔒 [登录限制] 管理员解锁用户: ${username}`);
      return true;
    }
    return false;
  }

  /** 获取所有被锁定的用户 */
  public static getLockedUsers(): Array<{
    username: string;
    attempts: number;
    lockDuration: number;
  }> {
    const lockedUsers: Array<{
      username: string;
      attempts: number;
      lockDuration: number;
    }> = [];

    const now = new Date();

    for (const [username, attempt] of this.attempts.entries()) {
      if (attempt.lockedUntil && now < attempt.lockedUntil) {
        const lockDuration = Math.ceil((attempt.lockedUntil.getTime() - now.getTime()) / 1000);
        lockedUsers.push({
          username,
          attempts: attempt.attempts,
          lockDuration
        });
      }
    }

    return lockedUsers;
  }

  /** 清理过期的记录 */
  public static cleanupExpiredRecords(): void {
    const now = new Date();
    const dayAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

    for (const [username, attempt] of this.attempts.entries()) {
      // 清理24小时前的记录
      if (attempt.lastAttempt < dayAgo && (!attempt.lockedUntil || now >= attempt.lockedUntil)) {
        this.attempts.delete(username);
      }
    }
  }

  /** 格式化锁定时间显示 */
  public static formatLockDuration(seconds: number): string {
    if (seconds < 60) {
      return `${seconds}秒`;
    } else if (seconds < 3600) {
      const minutes = Math.ceil(seconds / 60);
      return `${minutes}分钟`;
    }
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.ceil((seconds % 3600) / 60);
    return hours > 0 ? `${hours}小时${minutes > 0 ? `${minutes}分钟` : ''}` : `${minutes}分钟`;
  }
}

// 定期清理过期记录（每小时执行一次）
setInterval(
  () => {
    LoginAttemptManager.cleanupExpiredRecords();
  },
  60 * 60 * 1000
);

export default LoginAttemptManager;
