import { createUser, findUserByUsername } from '../model/user';
import pool from './db';
import { hashPassword } from './password';

/** 初始化数据库 */
export async function initDatabase() {
  try {
    // 创建用户表
    await createUserTable();

    // 创建默认管理员用户
    await createDefaultAdmin();

    // 初始化29平台服务商配置
    await init29PlatformProvider();
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error);
    throw error;
  }
}

/** 创建用户表 */
async function createUserTable() {
  const createTableSQL = `
    CREATE TABLE IF NOT EXISTS fd_user (
      user_id INT AUTO_INCREMENT PRIMARY KEY,
      username VARCHAR(50) NOT NULL UNIQUE COMMENT 'QQ账号',
      password VARCHAR(255) NOT NULL,
      nickname VARCHAR(100) NOT NULL,
      email VARCHAR(100),
      phone VARCHAR(20),
      avatar VARCHAR(255) DEFAULT '',
      role INT DEFAULT 3 COMMENT '1:管理员 2:代理商 3:普通用户',
      status INT DEFAULT 1 COMMENT '0:禁用 1:启用',
      invite_code VARCHAR(20) UNIQUE COMMENT '用户的邀请码',
      sid INT DEFAULT NULL COMMENT '上级用户ID',
      create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX idx_username (username),
      INDEX idx_status (status),
      INDEX idx_invite_code (invite_code),
      INDEX idx_sid (sid),
      FOREIGN KEY (sid) REFERENCES fd_user(user_id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';
  `;

  try {
    await pool.execute(createTableSQL);

    // 添加新字段（兼容现有表）
    await addUserTableFields();
  } catch (error) {
    console.error('❌ 创建用户表失败:', error);
    throw error;
  }
}

/** 添加用户表新字段（兼容现有表） */
async function addUserTableFields() {
  const fields = [
    {
      name: 'invite_code',
      sql: "ALTER TABLE fd_user ADD COLUMN invite_code VARCHAR(20) UNIQUE COMMENT '用户的邀请码'"
    },
    {
      name: 'sid',
      sql: "ALTER TABLE fd_user ADD COLUMN sid INT DEFAULT NULL COMMENT '上级用户ID'"
    }
  ];

  for (const field of fields) {
    try {
      await pool.execute(field.sql);
    } catch (error: any) {
      if (error.code !== 'ER_DUP_FIELDNAME') {
        console.error(`❌ 添加用户表字段 ${field.name} 失败:`, error);
      }
    }
  }

  // 添加索引
  try {
    await pool.execute('ALTER TABLE fd_user ADD INDEX idx_invite_code (invite_code)');
  } catch (error: any) {
    if (error.code !== 'ER_DUP_KEYNAME') {
      console.error('添加邀请码索引失败:', error);
    }
  }

  try {
    await pool.execute('ALTER TABLE fd_user ADD INDEX idx_sid (sid)');
  } catch (error: any) {
    if (error.code !== 'ER_DUP_KEYNAME') {
      console.error('添加上级ID索引失败:', error);
    }
  }
}

/** 创建默认管理员用户 */
async function createDefaultAdmin() {
  try {
    // 检查是否已存在admin用户
    const existingAdmin = await findUserByUsername('admin');

    if (existingAdmin) {
      // 检查管理员是否有邀请码，如果没有则生成
      if (!existingAdmin.invite_code) {
        const { generateUniqueInviteCode } = await import('../model/user');
        const inviteCode = await generateUniqueInviteCode();
        await pool.execute('UPDATE fd_user SET invite_code = ? WHERE user_id = ?', [inviteCode, existingAdmin.user_id]);
      }
      return;
    }

    // 创建默认管理员用户
    const hashedPassword = await hashPassword('123456');

    const adminUser = await createUser({
      username: 'admin',
      password: hashedPassword,
      nickname: '系统管理员',
      email: '<EMAIL>',
      phone: null, // 使用 null 而不是空字符串避免唯一约束冲突
      role: 1, // 管理员角色
      user_role: 'admin', // 字符串角色
      status: 1 // 启用状态
    });

    // 管理员用户创建成功
  } catch (error) {
    console.error('❌ 创建默认管理员用户失败:', error);
    throw error;
  }
}

/** 检查数据库连接 */
export async function checkDatabaseConnection() {
  try {
    const connection = await pool.getConnection();
    await connection.ping();
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    return false;
  }
}

/** 初始化29平台服务商配置 */
async function init29PlatformProvider() {
  try {
    // 检查是否已存在29平台服务商
    const existingProvider = await pool.execute('SELECT provider_id FROM fd_provider WHERE code = ?', ['29pt']);

    if ((existingProvider[0] as any[]).length > 0) {
      return; // 已存在，跳过初始化
    }

    // 创建29平台服务商配置
    const apiConfig = {
      provider_type: '29pt',
      description: '29平台对接服务商',
      api_version: '1.0',
      supported_actions: ['getclass', 'get', 'add', 'budan', 'chadanoid', 'xgmm'],
      timeout: 30000,
      retry_count: 3
    };

    await pool.execute(
      `
      INSERT INTO fd_provider (
        code, name, logo_url, api_url, username, password, token,
        ip_whitelist, api_config, status, create_time, update_time
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
    `,
      ['29pt', '29平台', '', 'https://freedomp.icu', '5', 'YsIYr7lZ75plP8Y5', '', '', JSON.stringify(apiConfig), 1]
    );

    console.log('✅ 29平台服务商配置初始化成功');
  } catch (error) {
    console.error('❌ 初始化29平台服务商配置失败:', error);
    // 不抛出错误，避免影响整个初始化流程
  }
}
