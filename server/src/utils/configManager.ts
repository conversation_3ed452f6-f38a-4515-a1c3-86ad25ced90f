import fs from 'node:fs/promises';
import path from 'node:path';

// 配置文件路径
const CONFIG_FILE = path.join(__dirname, '../config/system.json');

// 默认配置
const DEFAULT_CONFIG = {
  // 基础配置
  systemName: 'SoybeanAdmin',
  systemDescription: '基于Vue3 + TypeScript + Element Plus的现代化管理系统',
  systemVersion: '2.0.0',
  maintenanceMode: false,

  // 安全配置
  tokenExpiry: 24, // 小时
  passwordMinLength: 6,
  enableTwoFactor: false,
  maxLoginAttempts: 5,
  sessionTimeout: 30, // 分钟

  // 邮件配置
  smtpHost: '',
  smtpPort: 587,
  emailFrom: '',
  emailSSL: true,

  // 存储配置
  maxUploadSize: 10, // MB
  autoBackup: true,
  backupInterval: 24, // 小时
  maxBackupFiles: 7,

  // 功能开关
  enableRegistration: false,
  enableGuestAccess: false,
  enableApiDocs: true,

  // 数据库配置（只读）
  dbHost: process.env.DB_HOST || 'localhost',
  dbPort: Number.parseInt(process.env.DB_PORT || '3306'),
  dbName: process.env.DB_NAME || 'newfd',
  dbPoolSize: 10,

  // 日志配置
  logLevel: 'info',
  logRetentionDays: 30,
  enableFileLog: true,

  // 监控配置
  enableMonitoring: true,
  monitoringInterval: 60, // 秒
  alertThreshold: 80 // 百分比
};

// 内存中的配置缓存
let configCache: any = null;
let lastConfigLoad = 0;
const CONFIG_CACHE_TTL = 5000; // 5秒缓存

/** 配置管理器类 */
export class ConfigManager {
  /** 确保配置目录存在 */
  private static async ensureConfigDir(): Promise<void> {
    const configDir = path.dirname(CONFIG_FILE);
    try {
      await fs.access(configDir);
    } catch {
      await fs.mkdir(configDir, { recursive: true });
    }
  }

  /** 读取配置文件 */
  private static async readConfigFile(): Promise<any> {
    try {
      await this.ensureConfigDir();
      const data = await fs.readFile(CONFIG_FILE, 'utf-8');
      const config = JSON.parse(data);
      return { ...DEFAULT_CONFIG, ...config };
    } catch (error) {
      console.log('🔧 [配置管理器] 配置文件不存在，使用默认配置');
      return DEFAULT_CONFIG;
    }
  }

  /** 获取配置（带缓存） */
  public static async getConfig(): Promise<any> {
    const now = Date.now();

    // 如果缓存有效，直接返回缓存
    if (configCache && now - lastConfigLoad < CONFIG_CACHE_TTL) {
      return configCache;
    }

    // 重新加载配置
    configCache = await this.readConfigFile();
    lastConfigLoad = now;

    return configCache;
  }

  /** 更新配置 */
  public static async updateConfig(newConfig: any): Promise<any> {
    try {
      await this.ensureConfigDir();

      // 读取当前配置
      const currentConfig = await this.readConfigFile();

      // 合并配置
      const mergedConfig = {
        ...currentConfig,
        ...newConfig,
        // 数据库配置只读，不允许修改
        dbHost: DEFAULT_CONFIG.dbHost,
        dbPort: DEFAULT_CONFIG.dbPort,
        dbName: DEFAULT_CONFIG.dbName,
        dbPoolSize: DEFAULT_CONFIG.dbPoolSize
      };

      // 检查配置是否真的发生了变化
      const hasChanges = this.hasConfigChanges(currentConfig, mergedConfig);

      if (hasChanges) {
        // 过滤敏感信息
        const safeConfig = { ...mergedConfig };
        delete safeConfig.jwtSecret;
        delete safeConfig.emailPassword;

        // 保存到文件
        await fs.writeFile(CONFIG_FILE, JSON.stringify(safeConfig, null, 2), 'utf-8');
        console.log('🔧 [配置管理器] 系统配置已更新并保存到文件');
      } else {
        console.log('🔧 [配置管理器] 配置无变化，跳过文件写入');
      }

      // 更新缓存
      configCache = mergedConfig;
      lastConfigLoad = Date.now();
      return mergedConfig;
    } catch (error) {
      console.error('🔧 [配置管理器] 更新配置失败:', error);
      throw error;
    }
  }

  /** 重置配置 */
  public static async resetConfig(): Promise<any> {
    try {
      // 删除配置文件
      try {
        await fs.unlink(CONFIG_FILE);
      } catch {
        // 文件不存在，忽略错误
      }

      // 重置缓存
      configCache = DEFAULT_CONFIG;
      lastConfigLoad = Date.now();

      console.log('🔧 [配置管理器] 系统配置已重置');
      return DEFAULT_CONFIG;
    } catch (error) {
      console.error('🔧 [配置管理器] 重置配置失败:', error);
      throw error;
    }
  }

  /** 检查配置是否有变化 */
  private static hasConfigChanges(oldConfig: any, newConfig: any): boolean {
    try {
      // 简单的深度比较（排除敏感字段和时间戳字段）
      const oldSafe = { ...oldConfig };
      const newSafe = { ...newConfig };

      // 排除敏感字段
      delete oldSafe.jwtSecret;
      delete oldSafe.emailPassword;
      delete newSafe.jwtSecret;
      delete newSafe.emailPassword;

      // 排除可能变化的时间戳字段
      delete oldSafe.lastModified;
      delete newSafe.lastModified;

      return JSON.stringify(oldSafe) !== JSON.stringify(newSafe);
    } catch (error) {
      // 如果比较出错，默认认为有变化
      return true;
    }
  }

  /** 清除缓存 */
  public static clearCache(): void {
    configCache = null;
    lastConfigLoad = 0;
    console.log('🔧 [配置管理器] 缓存已清理');
  }

  /** 获取特定配置项 */
  public static async getConfigValue(key: string, defaultValue?: any): Promise<any> {
    const config = await this.getConfig();
    return config[key] !== undefined ? config[key] : defaultValue;
  }

  /** 检查维护模式 */
  public static async isMaintenanceMode(): Promise<boolean> {
    return await this.getConfigValue('maintenanceMode', false);
  }

  /** 获取Token过期时间（小时） */
  public static async getTokenExpiry(): Promise<number> {
    return await this.getConfigValue('tokenExpiry', 24);
  }

  /** 获取密码最小长度 */
  public static async getPasswordMinLength(): Promise<number> {
    return await this.getConfigValue('passwordMinLength', 6);
  }

  /** 获取最大登录尝试次数 */
  public static async getMaxLoginAttempts(): Promise<number> {
    return await this.getConfigValue('maxLoginAttempts', 5);
  }

  /** 获取会话超时时间（分钟） */
  public static async getSessionTimeout(): Promise<number> {
    return await this.getConfigValue('sessionTimeout', 30);
  }

  /** 获取最大上传大小（MB） */
  public static async getMaxUploadSize(): Promise<number> {
    return await this.getConfigValue('maxUploadSize', 10);
  }

  /** 检查是否启用注册 */
  public static async isRegistrationEnabled(): Promise<boolean> {
    return await this.getConfigValue('enableRegistration', false);
  }

  /** 检查是否启用访客访问 */
  public static async isGuestAccessEnabled(): Promise<boolean> {
    return await this.getConfigValue('enableGuestAccess', false);
  }

  /** 检查是否启用API文档 */
  public static async isApiDocsEnabled(): Promise<boolean> {
    return await this.getConfigValue('enableApiDocs', true);
  }

  /** 检查是否启用双因子认证 */
  public static async isTwoFactorEnabled(): Promise<boolean> {
    return await this.getConfigValue('enableTwoFactor', false);
  }

  /** 获取邮件配置 */
  public static async getEmailConfig(): Promise<any> {
    const config = await this.getConfig();
    return {
      smtpHost: config.smtpHost || '',
      smtpPort: config.smtpPort || 587,
      emailFrom: config.emailFrom || '',
      emailSSL: config.emailSSL !== false
    };
  }

  /** 获取系统基本信息 */
  public static async getSystemInfo(): Promise<any> {
    const config = await this.getConfig();
    return {
      systemName: config.systemName || 'SoybeanAdmin',
      systemDescription: config.systemDescription || '',
      systemVersion: config.systemVersion || '1.0.0'
    };
  }
}

// 导出单例实例
export default ConfigManager;
