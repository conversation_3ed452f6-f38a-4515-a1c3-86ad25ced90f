import nodemailer from 'nodemailer';
import ConfigManager from './configManager';
import { emailTemplateManager } from './emailTemplateManager';

export interface EmailConfig {
  smtpHost: string;
  smtpPort: number;
  emailFrom: string;
  emailPassword?: string;
  emailSSL: boolean;
}

export interface EmailTemplate {
  subject: string;
  text: string;
  html: string;
}

export interface EmailOptions {
  to: string | string[];
  subject: string;
  text?: string;
  html?: string;
  template?: string;
  variables?: Record<string, any>;
}

class EmailService {
  private transporter: nodemailer.Transporter | null = null;
  private config: EmailConfig | null = null;

  /** 初始化邮件服务 */
  async initialize(): Promise<void> {
    try {
      const systemConfig = await ConfigManager.getConfig();

      this.config = {
        smtpHost: systemConfig.smtpHost,
        smtpPort: systemConfig.smtpPort,
        emailFrom: systemConfig.emailFrom,
        emailPassword: systemConfig.emailPassword,
        emailSSL: systemConfig.emailSSL
      };

      if (this.config.smtpHost && this.config.emailFrom) {
        await this.createTransporter();
      }

      // 初始化模板管理器
      await emailTemplateManager.initialize();
    } catch (error) {
      console.error('📧 [邮件服务] 初始化失败:', error);
    }
  }

  /** 创建邮件传输器 */
  private async createTransporter(): Promise<void> {
    if (!this.config) {
      throw new Error('邮件配置未初始化');
    }

    this.transporter = nodemailer.createTransport({
      host: this.config.smtpHost,
      port: this.config.smtpPort,
      secure: this.config.emailSSL,
      auth: this.config.emailPassword
        ? {
            user: this.config.emailFrom,
            pass: this.config.emailPassword
          }
        : undefined,
      pool: true, // 使用连接池
      maxConnections: 5,
      maxMessages: 100
    });

    // 验证连接
    if (this.transporter) {
      await this.transporter.verify();
    }
  }

  /** 重新加载配置 */
  async reloadConfig(): Promise<void> {
    this.transporter = null;
    this.config = null;
    await this.initialize();
  }

  /** 检查邮件服务是否可用 */
  isAvailable(): boolean {
    return this.transporter !== null && this.config !== null;
  }

  /** 发送邮件 */
  async sendEmail(options: EmailOptions): Promise<void> {
    if (!this.isAvailable()) {
      throw new Error('邮件服务不可用，请检查邮件配置');
    }

    if (!this.transporter || !this.config) {
      throw new Error('邮件传输器未初始化');
    }

    // 处理模板
    let { subject, text, html } = options;
    if (options.template) {
      const rendered = await emailTemplateManager.renderTemplate(options.template, options.variables || {});
      if (rendered) {
        subject = rendered.subject;
        text = rendered.text;
        html = rendered.html;
      }
    }

    const mailOptions = {
      from: this.config.emailFrom,
      to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
      subject: subject || options.subject,
      text: text || options.text,
      html: html || options.html
    };

    await this.transporter.sendMail(mailOptions);
    console.log('📧 [邮件服务] 邮件发送成功:', options.to);
  }

  /** 测试邮件配置 */
  async testConfig(config: EmailConfig, testEmail: string): Promise<void> {
    const testTransporter = nodemailer.createTransport({
      host: config.smtpHost,
      port: config.smtpPort,
      secure: config.emailSSL,
      auth: config.emailPassword
        ? {
            user: config.emailFrom,
            pass: config.emailPassword
          }
        : undefined
    });

    // 验证连接
    await testTransporter.verify();

    // 发送测试邮件
    const systemInfo = await ConfigManager.getSystemInfo();
    await testTransporter.sendMail({
      from: config.emailFrom,
      to: testEmail,
      subject: `${systemInfo.systemName} - 邮件配置测试`,
      text: `这是来自 ${systemInfo.systemName} 的测试邮件。\n\n发送时间：${new Date().toLocaleString('zh-CN')}`,
      html: this.generateTestEmailHtml(systemInfo.systemName)
    });
  }

  /** 生成测试邮件HTML */
  private generateTestEmailHtml(systemName: string): string {
    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #409eff; margin: 0;">${systemName}</h1>
          <p style="color: #666; margin: 10px 0;">邮件配置测试</p>
        </div>

        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h2 style="color: #333; margin-top: 0;">✅ 邮件配置测试成功</h2>
          <p style="color: #666; line-height: 1.6;">
            恭喜！您的邮件配置已经正确设置。系统现在可以正常发送邮件通知。
          </p>
        </div>

        <div style="border-top: 1px solid #eee; padding-top: 20px; text-align: center;">
          <p style="color: #999; font-size: 12px; margin: 0;">
            发送时间：${new Date().toLocaleString('zh-CN')}<br>
            这是一封自动生成的测试邮件，请勿回复。
          </p>
        </div>
      </div>
    `;
  }

  /** 关闭邮件服务 */
  async close(): Promise<void> {
    if (this.transporter) {
      this.transporter.close();
      this.transporter = null;
      console.log('📧 [邮件服务] 已关闭');
    }
  }
}

// 创建单例实例
export const emailService = new EmailService();

export default EmailService;
