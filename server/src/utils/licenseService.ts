import * as https from 'node:https';
import axios from 'axios';
import { licenseConfig } from '../config/license';
import type { LicenseInfo } from '../model/license';
import {
  calculateGracePeriodEnd,
  getCurrentLicenseInfo,
  isInGracePeriod,
  saveLicenseInfo,
  updateLicenseStatus
} from '../model/license';

/** 授权系统API响应接口 */
interface LicenseValidateResponse {
  success: boolean;
  message?: string;
  status?: number;
  timestamp?: number;
  version?: string;
  data?: {
    valid: boolean;
    status: boolean;
    isUsed: boolean;
    healthStatus?: string;
    // 直接在 data 下的字段
    startDate?: string;
    expireDate?: string;
    daysLeft?: number;
    createdAt?: string;
    licenseKey?: {
      id: string;
      key: string;
      status: boolean;
      domain?: string;
      note?: string;
      createdAt?: string;
      updatedAt?: string;
    };
    plan?: {
      id: string;
      name: string;
      description: string;
      maxProducts: number;
      maxUsers: number;
      features: string[];
      durationDays?: number;
      price?: string;
    };
    user?: {
      id: string;
      username: string;
      email: string;
      isAdmin: boolean;
      userType: string;
      role: string;
      lastLoginTime: string;
      createdAt: string;
    };
    binding?: {
      id: string;
      domain: string;
      ip: string;
      bindTime: string;
      status: boolean;
      createdAt: string;
      isCurrentDomain: boolean;
      isCurrentIp: boolean;
    };
    dates?: {
      startDate: string;
      expireDate: string;
      daysLeft: number;
      isExpired: boolean;
      isExpiringSoon: boolean;
      bindTime?: string;
      createdAt: string;
      updatedAt: string;
    };
    usage?: {
      totalValidations: number;
      todayValidations: number;
      weekValidations: number;
      lastValidation: string | null;
      firstUsed: string;
    };
    currentRequest?: {
      domain: string;
      ip: string;
      userAgent: string;
      timestamp: string;
      isNewBinding: boolean;
    };
  };
}

/** 授权服务类 */
export class LicenseService {
  private domain: string;

  constructor(domain: string = process.env.DOMAIN || 'localhost') {
    this.domain = domain;
  }

  /** 验证授权密钥（调用授权系统API） */
  async validateLicenseKey(licenseKey: string): Promise<{
    success: boolean;
    message: string;
    data?: any;
  }> {
    try {
      const url = `${licenseConfig.baseUrl}${licenseConfig.paths.validate}`;
      const requestData = {
        licenseKey,
        domain: this.domain
      };

      const response = await axios.post<LicenseValidateResponse>(url, requestData, {
        timeout: 30000, // 增加超时时间到30秒
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'SoybeanAdmin/1.0.0',
          Accept: 'application/json'
        },
        // 添加网络配置
        maxRedirects: 5,
        validateStatus: status => status >= 200 && status < 500, // 允许4xx状态码
        // 添加代理配置（如果需要）
        proxy: false,
        // 禁用SSL验证（仅用于调试，生产环境应该启用）
        httpsAgent: new https.Agent({
          rejectUnauthorized: false
        })
      });

      // 外部授权系统响应已接收

      if (response.data.success && response.data.data?.status === true) {
        // 验证成功，保存到数据库
        const now = new Date();
        // 格式化日期为MySQL支持的格式 (YYYY-MM-DD HH:mm:ss)
        const mysqlDateTime = now.toISOString().slice(0, 19).replace('T', ' ');

        // 解析外部授权系统的响应数据
        const responseData = response.data.data;
        // 优先从 dates.expireDate 获取，如果没有则从 expireDate 获取
        const expiryDate = responseData?.dates?.expireDate || responseData?.expireDate;
        const planName = responseData?.plan?.name;
        const startDate = responseData?.dates?.startDate || responseData?.startDate;
        const daysLeft = responseData?.dates?.daysLeft || responseData?.daysLeft;

        // 转换日期格式：从 "2026-06-08" 转换为 "2026-06-08 00:00:00"
        const formattedExpiryDate = expiryDate ? `${expiryDate} 00:00:00` : undefined;
        const formattedStartDate = startDate ? `${startDate} 00:00:00` : undefined;

        // 数据解析完成

        // 提取更多详细信息
        const planInfo = responseData?.plan;
        const bindingInfo = responseData?.binding;
        const usageInfo = responseData?.usage;
        const licenseKeyInfo = responseData?.licenseKey;
        const datesInfo = responseData?.dates;

        // 使用外部系统的原始时间数据，如果没有则使用当前时间
        const originalCreatedAt = licenseKeyInfo?.createdAt || datesInfo?.createdAt;
        const originalUpdatedAt = licenseKeyInfo?.updatedAt || datesInfo?.updatedAt;

        // 格式化外部系统的时间为MySQL格式
        const formatExternalTime = (timeStr: string | undefined): string => {
          if (!timeStr) return mysqlDateTime;
          // 如果已经是MySQL格式 (YYYY-MM-DD HH:mm:ss)，直接返回
          if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(timeStr)) {
            return timeStr;
          }
          // 否则尝试解析并转换
          try {
            return new Date(timeStr).toISOString().slice(0, 19).replace('T', ' ');
          } catch {
            return mysqlDateTime;
          }
        };

        const licenseInfo: Omit<LicenseInfo, 'id' | 'created_at' | 'updated_at'> = {
          license_key: licenseKey,
          domain: this.domain,
          status: 'active',
          expires_at: formattedExpiryDate,
          validated_at: mysqlDateTime, // 本地验证时间使用当前时间
          plan: planName,
          // 扩展字段
          start_date: formattedStartDate,
          days_left: daysLeft,
          plan_description: planInfo?.description,
          max_products: planInfo?.maxProducts,
          max_users: planInfo?.maxUsers,
          plan_features: planInfo?.features ? JSON.stringify(planInfo.features) : undefined,
          binding_info: bindingInfo ? JSON.stringify(bindingInfo) : undefined,
          usage_stats: usageInfo ? JSON.stringify(usageInfo) : undefined,
          external_data: JSON.stringify(responseData)
        };

        // 传递外部系统的原始时间
        await saveLicenseInfo(
          licenseInfo,
          formatExternalTime(originalCreatedAt),
          formatExternalTime(originalUpdatedAt)
        );
        console.log('✅ 授权验证成功');

        return {
          success: true,
          message: '授权验证成功',
          data: {
            status: 'active',
            expiryDate: formattedExpiryDate,
            plan: planName,
            domain: this.domain
          }
        };
      }
      return {
        success: false,
        message: response.data.message || '授权验证失败'
      };
    } catch (error: any) {
      console.error('🔒 授权验证请求失败:', {
        message: error.message,
        code: error.code,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data,
        url: error.config?.url,
        method: error.config?.method,
        timeout: error.config?.timeout,
        headers: error.config?.headers,
        // 网络相关错误信息
        errno: error.errno,
        syscall: error.syscall,
        hostname: error.hostname,
        port: error.port
      });

      // 如果是405错误，说明方法不被允许
      if (error.response?.status === 405) {
        return {
          success: false,
          message: '授权API不支持当前请求方法，请检查API文档'
        };
      }

      // 网络错误时检查是否有本地授权信息且在宽限期内
      const localLicense = await this.getLocalLicenseStatus();
      if (localLicense.success && localLicense.data?.inGracePeriod) {
        return {
          success: true,
          message: '网络验证失败，使用宽限期授权',
          data: localLicense.data
        };
      }

      return {
        success: false,
        message: error.response?.data?.message || '无法连接到授权服务器，请检查网络连接'
      };
    }
  }

  /** 获取本地授权状态 */
  async getLocalLicenseStatus(): Promise<{
    success: boolean;
    message: string;
    data?: any;
  }> {
    try {
      const licenseInfo = await getCurrentLicenseInfo(this.domain);

      if (!licenseInfo) {
        return {
          success: false,
          message: '未找到授权信息'
        };
      }

      const now = new Date();
      const validatedAt = new Date(licenseInfo.validated_at);
      const hoursSinceValidation = (now.getTime() - validatedAt.getTime()) / (1000 * 60 * 60);

      // 检查是否需要重新验证（超过1小时）
      const needsRevalidation = hoursSinceValidation > 1;

      // 检查是否在宽限期内
      const inGracePeriod = isInGracePeriod(licenseInfo);

      // 检查授权是否过期
      let isExpired = false;
      if (licenseInfo.expires_at) {
        const expiryDate = new Date(licenseInfo.expires_at);
        isExpired = now > expiryDate;
      }

      // 确定当前状态
      let currentStatus = licenseInfo.status;
      if (isExpired && !inGracePeriod) {
        currentStatus = 'expired';
      } else if (isExpired && inGracePeriod) {
        currentStatus = 'grace_period';
      }

      return {
        success: currentStatus === 'active' || currentStatus === 'grace_period',
        message: this.getStatusMessage(currentStatus, inGracePeriod),
        data: {
          status: currentStatus,
          licenseKey: licenseInfo.license_key,
          domain: licenseInfo.domain,
          expiryDate: licenseInfo.expires_at,
          plan: licenseInfo.plan,
          validatedAt: licenseInfo.validated_at,
          needsRevalidation,
          inGracePeriod,
          gracePeriodEnd: licenseInfo.grace_period_end
        }
      };
    } catch (error) {
      console.error('🔒 获取本地授权状态失败:', error);
      return {
        success: false,
        message: '获取授权状态失败'
      };
    }
  }

  /** 刷新授权状态（重新验证） */
  async refreshLicenseStatus(): Promise<{
    success: boolean;
    message: string;
    data?: any;
  }> {
    try {
      // 直接从数据库获取原始加密的授权密钥，避免使用解密后的密钥
      const { executeQuery } = require('../utils/db');
      const rows = await executeQuery('SELECT license_key FROM fd_license_info WHERE domain = ? LIMIT 1', [
        this.domain
      ]);

      if (!rows || rows.length === 0) {
        return {
          success: false,
          message: '未找到本地授权信息'
        };
      }

      const originalEncryptedKey = rows[0].license_key;

      // 使用原始加密的授权密钥重新验证
      return await this.validateLicenseKey(originalEncryptedKey);
    } catch (error) {
      console.error('🔒 刷新授权状态失败:', error);
      return {
        success: false,
        message: '刷新授权状态失败'
      };
    }
  }

  /** 开始宽限期 */
  async startGracePeriod(): Promise<void> {
    try {
      const now = new Date();
      const gracePeriodEnd = calculateGracePeriodEnd(now);

      await updateLicenseStatus(
        this.domain,
        'grace_period',
        this.formatDateForMySQL(now),
        this.formatDateForMySQL(now),
        this.formatDateForMySQL(gracePeriodEnd)
      );

      // 宽限期已开始
    } catch (error) {
      console.error('🔒 开始宽限期失败:', error);
    }
  }

  /** 格式化日期为MySQL支持的格式 */
  private formatDateForMySQL(date: Date): string {
    return date.toISOString().slice(0, 19).replace('T', ' ');
  }

  /** 获取状态消息 */
  private getStatusMessage(status: string, inGracePeriod: boolean): string {
    switch (status) {
      case 'active':
        return '授权有效';
      case 'grace_period':
        return '授权已过期，当前处于宽限期';
      case 'expired':
        return inGracePeriod ? '授权已过期，当前处于宽限期' : '授权已过期';
      case 'invalid':
        return '授权无效';
      default:
        return '未知状态';
    }
  }
}

/** 创建授权服务实例 */
export function createLicenseService(domain?: string): LicenseService {
  return new LicenseService(domain);
}
