/**
 * 服务器端时间工具
 * 统一处理时区和时间格式化
 */

/**
 * 获取当前时间的本地化字符串（上海时区）
 * @returns 格式化的时间字符串 YYYY-MM-DD HH:mm:ss
 */
export function getCurrentTimeString(): string {
  const now = new Date();
  return formatDateTimeToLocal(now);
}

/**
 * 获取当前时间的ISO字符串（上海时区）
 * @returns ISO格式的时间字符串
 */
export function getCurrentISOString(): string {
  const now = new Date();
  // 获取上海时区的时间偏移（UTC+8）
  const shanghaiOffset = 8 * 60; // 8小时 = 480分钟
  const localTime = new Date(now.getTime() + shanghaiOffset * 60 * 1000);
  return localTime.toISOString().replace('Z', '+08:00');
}

/**
 * 将日期格式化为本地时间字符串
 * @param date 日期对象或时间戳
 * @returns 格式化的时间字符串 YYYY-MM-DD HH:mm:ss
 */
export function formatDateTimeToLocal(date: Date | number | string): string {
  const dateObj = typeof date === 'string' || typeof date === 'number' ? new Date(date) : date;

  if (isNaN(dateObj.getTime())) {
    return 'Invalid Date';
  }

  // 使用中国时区格式化
  return dateObj.toLocaleString('zh-CN', {
    timeZone: 'Asia/Shanghai',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false
  });
}

/**
 * 将日期格式化为日志专用格式
 * @param date 日期对象或时间戳
 * @returns 格式化的时间字符串 [YYYY-MM-DD HH:mm:ss]
 */
export function formatDateTimeForLog(date?: Date | number | string): string {
  const dateObj = date ? (typeof date === 'string' || typeof date === 'number' ? new Date(date) : date) : new Date();
  return `[${formatDateTimeToLocal(dateObj)}]`;
}

/**
 * 获取当前时间戳（毫秒）
 * @returns 时间戳
 */
export function getCurrentTimestamp(): number {
  return Date.now();
}

/**
 * 将时间戳转换为本地时间字符串
 * @param timestamp 时间戳（毫秒）
 * @returns 格式化的时间字符串
 */
export function timestampToLocal(timestamp: number): string {
  return formatDateTimeToLocal(new Date(timestamp));
}

/**
 * 获取今天的开始时间（00:00:00）
 * @returns 今天开始时间的时间戳
 */
export function getTodayStart(): number {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  return today.getTime();
}

/**
 * 获取今天的结束时间（23:59:59.999）
 * @returns 今天结束时间的时间戳
 */
export function getTodayEnd(): number {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999);
  return today.getTime();
}

/**
 * 计算两个时间之间的差值
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @returns 差值对象 { days, hours, minutes, seconds, milliseconds }
 */
export function getTimeDifference(startTime: Date | number, endTime: Date | number) {
  const start = typeof startTime === 'number' ? startTime : startTime.getTime();
  const end = typeof endTime === 'number' ? endTime : endTime.getTime();
  const diff = Math.abs(end - start);

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
  const seconds = Math.floor((diff % (1000 * 60)) / 1000);
  const milliseconds = diff % 1000;

  return { days, hours, minutes, seconds, milliseconds };
}

/**
 * 格式化运行时间
 * @param seconds 秒数
 * @returns 格式化的运行时间字符串
 */
export function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (days > 0) {
    return `${days}天 ${hours}小时 ${minutes}分钟`;
  } else if (hours > 0) {
    return `${hours}小时 ${minutes}分钟`;
  }
  return `${minutes}分钟`;
}

/**
 * 检查是否为有效日期
 * @param date 要检查的日期
 * @returns 是否为有效日期
 */
export function isValidDate(date: any): boolean {
  return date instanceof Date && !isNaN(date.getTime());
}

/**
 * 获取指定天数前的时间戳
 * @param days 天数
 * @returns 时间戳
 */
export function getDaysAgo(days: number): number {
  return Date.now() - days * 24 * 60 * 60 * 1000;
}

/**
 * 获取指定小时前的时间戳
 * @param hours 小时数
 * @returns 时间戳
 */
export function getHoursAgo(hours: number): number {
  return Date.now() - hours * 60 * 60 * 1000;
}

/**
 * 获取指定分钟前的时间戳
 * @param minutes 分钟数
 * @returns 时间戳
 */
export function getMinutesAgo(minutes: number): number {
  return Date.now() - minutes * 60 * 1000;
}
