/**
 * HTTP连接池管理
 * 优化HTTP请求性能，支持连接复用和并发控制
 */

import { Agent as HttpAgent } from 'node:http';
import { Agent as HttpsAgent } from 'node:https';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import axios from 'axios';

interface PoolConfig {
  maxSockets?: number;
  maxFreeSockets?: number;
  timeout?: number;
  keepAlive?: boolean;
  keepAliveMsecs?: number;
  maxRedirects?: number;
  retryCount?: number;
  retryDelay?: number;
}

interface RequestStats {
  total: number;
  success: number;
  failed: number;
  avgResponseTime: number;
  totalResponseTime: number;
}

export class HttpPool {
  private httpAgent!: HttpAgent;
  private httpsAgent!: HttpsAgent;
  private axiosInstance!: AxiosInstance;
  private config: Required<PoolConfig>;
  private stats: RequestStats;
  private activeRequests: Map<string, number>;

  constructor(config: PoolConfig = {}) {
    this.config = {
      maxSockets: config.maxSockets || 50,
      maxFreeSockets: config.maxFreeSockets || 10,
      timeout: config.timeout || 30000,
      keepAlive: config.keepAlive !== false,
      keepAliveMsecs: config.keepAliveMsecs || 1000,
      maxRedirects: config.maxRedirects || 5,
      retryCount: config.retryCount || 3,
      retryDelay: config.retryDelay || 1000
    };

    this.stats = {
      total: 0,
      success: 0,
      failed: 0,
      avgResponseTime: 0,
      totalResponseTime: 0
    };

    this.activeRequests = new Map();

    this.initializeAgents();
    this.createAxiosInstance();
  }

  /**
   * 初始化HTTP代理
   */
  private initializeAgents(): void {
    const agentConfig = {
      maxSockets: this.config.maxSockets,
      maxFreeSockets: this.config.maxFreeSockets,
      keepAlive: this.config.keepAlive,
      keepAliveMsecs: this.config.keepAliveMsecs,
      timeout: this.config.timeout
    };

    this.httpAgent = new HttpAgent(agentConfig);
    this.httpsAgent = new HttpsAgent({
      ...agentConfig,
      rejectUnauthorized: false // 在生产环境中应该设为true
    });
  }

  /**
   * 创建Axios实例
   */
  private createAxiosInstance(): void {
    this.axiosInstance = axios.create({
      timeout: this.config.timeout,
      maxRedirects: this.config.maxRedirects,
      httpAgent: this.httpAgent,
      httpsAgent: this.httpsAgent,
      validateStatus: () => true // 不自动抛出HTTP错误
    });

    // 请求拦截器
    this.axiosInstance.interceptors.request.use(
      config => {
        const url = config.url || 'unknown';
        const count = this.activeRequests.get(url) || 0;
        this.activeRequests.set(url, count + 1);

        // 添加请求开始时间
        (config as any).metadata = { startTime: Date.now() };

        return config;
      },
      error => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.axiosInstance.interceptors.response.use(
      response => {
        this.updateStats(response, true);
        this.decrementActiveRequests(response.config.url);
        return response;
      },
      error => {
        this.updateStats(error.response || error.config, false);
        this.decrementActiveRequests(error.config?.url);
        return Promise.reject(error);
      }
    );
  }

  /**
   * 更新请求统计
   */
  private updateStats(response: any, success: boolean): void {
    this.stats.total++;

    if (success) {
      this.stats.success++;
    } else {
      this.stats.failed++;
    }

    // 计算响应时间
    const startTime = response.config?.metadata?.startTime;
    if (startTime) {
      const responseTime = Date.now() - startTime;
      this.stats.totalResponseTime += responseTime;
      this.stats.avgResponseTime = this.stats.totalResponseTime / this.stats.total;
    }
  }

  /**
   * 减少活跃请求计数
   */
  private decrementActiveRequests(url?: string): void {
    if (!url) return;

    const count = this.activeRequests.get(url) || 0;
    if (count > 1) {
      this.activeRequests.set(url, count - 1);
    } else {
      this.activeRequests.delete(url);
    }
  }

  /**
   * 发送HTTP请求（带重试机制）
   */
  async request<T = any>(config: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    let lastError: any;

    for (let attempt = 1; attempt <= this.config.retryCount; attempt++) {
      try {
        const response = await this.axiosInstance.request<T>(config);

        // 检查HTTP状态码
        if (response.status >= 200 && response.status < 300) {
          return response;
        }

        // HTTP错误状态码
        if (attempt === this.config.retryCount) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
      } catch (error: any) {
        lastError = error;

        // 如果是最后一次尝试，直接抛出错误
        if (attempt === this.config.retryCount) {
          throw error;
        }

        // 等待后重试
        await this.delay(this.config.retryDelay * attempt);
      }
    }

    throw lastError;
  }

  /**
   * GET请求
   */
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.request<T>({ ...config, method: 'GET', url });
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.request<T>({ ...config, method: 'POST', url, data });
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.request<T>({ ...config, method: 'PUT', url, data });
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.request<T>({ ...config, method: 'DELETE', url });
  }

  /**
   * 并发请求控制
   */
  async batchRequest<T = any>(requests: AxiosRequestConfig[], concurrency: number = 5): Promise<AxiosResponse<T>[]> {
    const results: AxiosResponse<T>[] = [];
    const executing: Promise<void>[] = [];

    for (const request of requests) {
      const promise = this.request<T>(request).then(
        response => {
          results.push(response);
        },
        error => {
          // 记录错误但不中断其他请求
          console.error('批量请求失败:', error.message);
          results.push(error.response || { status: 0, data: null });
        }
      );

      executing.push(promise);

      if (executing.length >= concurrency) {
        await Promise.race(executing);
        executing.splice(
          executing.findIndex(p => p === promise),
          1
        );
      }
    }

    await Promise.all(executing);
    return results;
  }

  /**
   * 获取连接池统计信息
   */
  getStats() {
    return {
      ...this.stats,
      successRate: this.stats.total > 0 ? this.stats.success / this.stats.total : 0,
      activeRequests: Array.from(this.activeRequests.entries()),
      totalActiveRequests: Array.from(this.activeRequests.values()).reduce((sum, count) => sum + count, 0),
      httpSockets: (this.httpAgent as any).getCurrentConnections?.() || 0,
      httpsSockets: (this.httpsAgent as any).getCurrentConnections?.() || 0
    };
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    this.stats = {
      total: 0,
      success: 0,
      failed: 0,
      avgResponseTime: 0,
      totalResponseTime: 0
    };
  }

  /**
   * 销毁连接池
   */
  destroy(): void {
    this.httpAgent.destroy();
    this.httpsAgent.destroy();
    this.activeRequests.clear();
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 全局HTTP连接池实例
export const globalHttpPool = new HttpPool({
  maxSockets: 100,
  maxFreeSockets: 20,
  timeout: 30000,
  retryCount: 3,
  retryDelay: 1000
});

// 货源专用HTTP连接池
export const providerHttpPool = new HttpPool({
  maxSockets: 50,
  maxFreeSockets: 10,
  timeout: 60000, // 货源接口可能较慢
  retryCount: 2,
  retryDelay: 2000
});

/**
 * HTTP连接池管理器
 */
export class HttpPoolManager {
  private static pools: Map<string, HttpPool> = new Map();

  /**
   * 创建命名连接池
   */
  static createPool(name: string, config: PoolConfig): HttpPool {
    const pool = new HttpPool(config);
    this.pools.set(name, pool);
    return pool;
  }

  /**
   * 获取连接池
   */
  static getPool(name: string): HttpPool | undefined {
    return this.pools.get(name);
  }

  /**
   * 销毁连接池
   */
  static destroyPool(name: string): boolean {
    const pool = this.pools.get(name);
    if (pool) {
      pool.destroy();
      this.pools.delete(name);
      return true;
    }
    return false;
  }

  /**
   * 获取所有连接池统计
   */
  static getAllStats() {
    const stats: { [key: string]: any } = {};
    for (const [name, pool] of this.pools.entries()) {
      stats[name] = pool.getStats();
    }
    return stats;
  }

  /**
   * 销毁所有连接池
   */
  static destroyAll(): void {
    for (const [name, pool] of this.pools.entries()) {
      pool.destroy();
    }
    this.pools.clear();
  }
}
