import * as mysql from 'mysql2/promise';
import * as dotenv from 'dotenv';
import { formatDateTimeForLog } from './timeUtils';

dotenv.config();

// 统一的数据库配置
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: Number.parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'newfd',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'newfd',
  charset: 'utf8mb4',
  timezone: '+08:00',
  socketPath: '/tmp/mysql.sock', // 添加socket路径
  // 连接池配置
  connectionLimit: 10,
  queueLimit: 0,
  waitForConnections: true
  // 移除无效的配置选项以避免警告
  // acquireTimeout, timeout, reconnect, handleDisconnects 在 mysql2 中无效
};

// 创建数据库连接池
const pool = mysql.createPool(dbConfig);

// 执行查询的辅助函数
export async function executeQuery(sql: string, params?: any[]): Promise<any> {
  try {
    // 如果没有参数，使用query方法；有参数则使用execute方法
    if (!params || params.length === 0) {
      const [rows] = await pool.query(sql);
      return rows;
    }
    const [rows] = await pool.execute(sql, params);
    return rows;
  } catch (error) {
    console.error(`${formatDateTimeForLog()} ❌ 数据库查询失败:`, error);
    console.error(`${formatDateTimeForLog()} SQL:`, sql);
    console.error(`${formatDateTimeForLog()} 参数:`, params);
    throw error;
  }
}

// 测试数据库连接
export async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('✅ 数据库连接成功');

    // 测试查询
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log('✅ 数据库查询测试成功');

    connection.release();
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error);
    return false;
  }
}

export default pool;
