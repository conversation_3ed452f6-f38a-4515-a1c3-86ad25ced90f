/**
 * 系统监控和告警工具
 * 提供性能监控、错误追踪和告警通知功能
 */

import { EventEmitter } from 'node:events';
import { executeQuery } from './db';

interface MetricData {
  name: string;
  value: number;
  timestamp: number;
  tags?: Record<string, string>;
}

interface AlertRule {
  name: string;
  metric: string;
  condition: 'gt' | 'lt' | 'eq' | 'gte' | 'lte';
  threshold: number;
  duration: number; // 持续时间（秒）
  enabled: boolean;
  notificationChannels: string[];
}

interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  uptime: number;
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  cpu: {
    usage: number;
  };
  database: {
    connected: boolean;
    responseTime: number;
  };
  cache: {
    hitRate: number;
    size: number;
  };
  httpPool: {
    activeConnections: number;
    successRate: number;
  };
}

export class SystemMonitor extends EventEmitter {
  private metrics: Map<string, MetricData[]> = new Map();
  private alertRules: AlertRule[] = [];
  private alertStates: Map<string, { triggered: boolean; since: number }> = new Map();
  private monitoringInterval: NodeJS.Timeout | null = null;

  constructor() {
    super();
    this.initializeDefaultAlerts();
    this.startMonitoring();
  }

  /**
   * 记录指标数据
   */
  recordMetric(name: string, value: number, tags?: Record<string, string>): void {
    const metric: MetricData = {
      name,
      value,
      timestamp: Date.now(),
      tags
    };

    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    const metricHistory = this.metrics.get(name)!;
    metricHistory.push(metric);

    // 保留最近1小时的数据
    const oneHourAgo = Date.now() - 60 * 60 * 1000;
    this.metrics.set(
      name,
      metricHistory.filter(m => m.timestamp > oneHourAgo)
    );

    // 检查告警规则
    this.checkAlertRules(name, value);

    this.emit('metric', metric);
  }

  /**
   * 获取指标数据
   */
  getMetrics(name: string, duration: number = 3600): MetricData[] {
    const metrics = this.metrics.get(name) || [];
    const since = Date.now() - duration * 1000;
    return metrics.filter(m => m.timestamp > since);
  }

  /**
   * 获取系统健康状态
   */
  async getSystemHealth(): Promise<SystemHealth> {
    const startTime = Date.now();

    // 检查数据库连接
    let dbConnected = true;
    let dbResponseTime = 0;
    try {
      const dbStart = Date.now();
      await executeQuery('SELECT 1');
      dbResponseTime = Date.now() - dbStart;
    } catch (error) {
      dbConnected = false;
      dbResponseTime = -1;
    }

    // 获取内存使用情况
    const memUsage = process.memoryUsage();
    const totalMemory = require('node:os').totalmem();
    const memoryPercentage = (memUsage.rss / totalMemory) * 100;

    // 获取CPU使用率（简化版）
    const cpuUsage = process.cpuUsage();
    const cpuPercentage = (cpuUsage.user + cpuUsage.system) / 1000000; // 转换为秒

    // 获取缓存统计
    const { globalCache } = require('./cache');
    const cacheStats = globalCache.getStats();

    // 获取HTTP连接池统计
    const { globalHttpPool } = require('./httpPool');
    const poolStats = globalHttpPool.getStats();

    // 确定整体健康状态
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';
    if (!dbConnected || memoryPercentage > 90 || poolStats.successRate < 0.9) {
      status = 'critical';
    } else if (memoryPercentage > 80 || dbResponseTime > 1000 || poolStats.successRate < 0.95) {
      status = 'warning';
    }

    const health: SystemHealth = {
      status,
      uptime: process.uptime(),
      memory: {
        used: memUsage.rss,
        total: totalMemory,
        percentage: Math.round(memoryPercentage * 100) / 100
      },
      cpu: {
        usage: Math.round(cpuPercentage * 100) / 100
      },
      database: {
        connected: dbConnected,
        responseTime: dbResponseTime
      },
      cache: {
        hitRate: cacheStats.hitRate,
        size: cacheStats.size
      },
      httpPool: {
        activeConnections: poolStats.totalActiveRequests,
        successRate: Math.round(poolStats.successRate * 10000) / 100
      }
    };

    // 记录健康检查指标
    this.recordMetric('system.health.memory_percentage', memoryPercentage);
    this.recordMetric('system.health.db_response_time', dbResponseTime);
    this.recordMetric('system.health.cache_hit_rate', cacheStats.hitRate);
    this.recordMetric('system.health.http_success_rate', poolStats.successRate);

    return health;
  }

  /**
   * 添加告警规则
   */
  addAlertRule(rule: AlertRule): void {
    this.alertRules.push(rule);
    this.alertStates.set(rule.name, { triggered: false, since: 0 });
  }

  /**
   * 检查告警规则
   */
  private checkAlertRules(metricName: string, value: number): void {
    const relevantRules = this.alertRules.filter(rule => rule.enabled && rule.metric === metricName);

    for (const rule of relevantRules) {
      const shouldTrigger = this.evaluateCondition(rule.condition, value, rule.threshold);
      const alertState = this.alertStates.get(rule.name)!;
      const now = Date.now();

      if (shouldTrigger) {
        if (!alertState.triggered) {
          alertState.since = now;
        } else if (now - alertState.since >= rule.duration * 1000) {
          // 告警持续时间达到阈值，触发通知
          this.triggerAlert(rule, value);
        }
        alertState.triggered = true;
      } else {
        if (alertState.triggered) {
          // 告警恢复
          this.resolveAlert(rule, value);
        }
        alertState.triggered = false;
        alertState.since = 0;
      }
    }
  }

  /**
   * 评估告警条件
   */
  private evaluateCondition(condition: string, value: number, threshold: number): boolean {
    switch (condition) {
      case 'gt':
        return value > threshold;
      case 'gte':
        return value >= threshold;
      case 'lt':
        return value < threshold;
      case 'lte':
        return value <= threshold;
      case 'eq':
        return value === threshold;
      default:
        return false;
    }
  }

  /**
   * 触发告警
   */
  private triggerAlert(rule: AlertRule, value: number): void {
    const alert = {
      rule: rule.name,
      metric: rule.metric,
      value,
      threshold: rule.threshold,
      timestamp: new Date().toISOString(),
      level: 'warning'
    };

    console.warn(`🚨 [告警] ${rule.name}: ${rule.metric} = ${value} (阈值: ${rule.threshold})`);

    this.emit('alert', alert);

    // 发送通知
    this.sendNotifications(rule.notificationChannels, alert);
  }

  /**
   * 告警恢复
   */
  private resolveAlert(rule: AlertRule, value: number): void {
    const alert = {
      rule: rule.name,
      metric: rule.metric,
      value,
      threshold: rule.threshold,
      timestamp: new Date().toISOString(),
      level: 'info'
    };

    console.info(`✅ [告警恢复] ${rule.name}: ${rule.metric} = ${value}`);

    this.emit('alert_resolved', alert);
  }

  /**
   * 发送通知
   */
  private async sendNotifications(channels: string[], alert: any): Promise<void> {
    for (const channel of channels) {
      try {
        switch (channel) {
          case 'console':
            console.log('📢 [通知]', JSON.stringify(alert, null, 2));
            break;
          case 'database':
            await this.saveAlertToDatabase(alert);
            break;
          case 'webhook':
            await this.sendWebhookNotification(alert);
            break;
          default:
            console.warn(`未知的通知渠道: ${channel}`);
        }
      } catch (error) {
        console.error(`发送通知失败 (${channel}):`, error);
      }
    }
  }

  /**
   * 保存告警到数据库
   */
  private async saveAlertToDatabase(alert: any): Promise<void> {
    try {
      await executeQuery(
        `INSERT INTO fd_system_alerts
         (rule_name, metric_name, metric_value, threshold_value, alert_level, alert_time)
         VALUES (?, ?, ?, ?, ?, NOW())`,
        [alert.rule, alert.metric, alert.value, alert.threshold, alert.level]
      );
    } catch (error) {
      console.error('保存告警到数据库失败:', error);
    }
  }

  /**
   * 发送Webhook通知
   */
  private async sendWebhookNotification(alert: any): Promise<void> {
    // 这里可以实现发送到钉钉、企业微信等
    const webhookUrl = process.env.ALERT_WEBHOOK_URL;
    if (!webhookUrl) return;

    const { globalHttpPool } = require('./httpPool');

    try {
      await globalHttpPool.post(webhookUrl, {
        msgtype: 'text',
        text: {
          content: `系统告警: ${alert.rule}\n指标: ${alert.metric}\n当前值: ${alert.value}\n阈值: ${alert.threshold}\n时间: ${alert.timestamp}`
        }
      });
    } catch (error) {
      console.error('发送Webhook通知失败:', error);
    }
  }

  /**
   * 初始化默认告警规则
   */
  private initializeDefaultAlerts(): void {
    // 内存使用率告警
    this.addAlertRule({
      name: '内存使用率过高',
      metric: 'system.health.memory_percentage',
      condition: 'gt',
      threshold: 80,
      duration: 300, // 5分钟
      enabled: true,
      notificationChannels: ['console', 'database']
    });

    // 数据库响应时间告警
    this.addAlertRule({
      name: '数据库响应时间过长',
      metric: 'system.health.db_response_time',
      condition: 'gt',
      threshold: 1000, // 1秒
      duration: 60, // 1分钟
      enabled: true,
      notificationChannels: ['console', 'database']
    });

    // HTTP成功率告警
    this.addAlertRule({
      name: 'HTTP请求成功率过低',
      metric: 'system.health.http_success_rate',
      condition: 'lt',
      threshold: 0.95, // 95%
      duration: 300, // 5分钟
      enabled: true,
      notificationChannels: ['console', 'database', 'webhook']
    });

    // 缓存命中率告警
    this.addAlertRule({
      name: '缓存命中率过低',
      metric: 'system.health.cache_hit_rate',
      condition: 'lt',
      threshold: 0.8, // 80%
      duration: 600, // 10分钟
      enabled: true,
      notificationChannels: ['console', 'database']
    });
  }

  /**
   * 开始监控
   */
  private startMonitoring(): void {
    // 移除定时健康检查，减少资源消耗
    // 健康检查改为按需调用
    console.log('🔍 系统监控已启动（按需模式）');
  }

  /**
   * 停止监控
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    console.log('🔍 系统监控已停止');
  }

  /**
   * 获取监控统计
   */
  getMonitoringStats() {
    return {
      metricsCount: Array.from(this.metrics.values()).reduce((sum, metrics) => sum + metrics.length, 0),
      alertRulesCount: this.alertRules.length,
      activeAlertsCount: Array.from(this.alertStates.values()).filter(state => state.triggered).length,
      uptime: process.uptime()
    };
  }
}

// 全局监控实例
export const systemMonitor = new SystemMonitor();

/**
 * 监控装饰器
 */
export function monitored(metricName: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();

      try {
        const result = await method.apply(this, args);
        const duration = Date.now() - startTime;

        systemMonitor.recordMetric(`${metricName}.duration`, duration);
        systemMonitor.recordMetric(`${metricName}.success`, 1);

        return result;
      } catch (error) {
        const duration = Date.now() - startTime;

        systemMonitor.recordMetric(`${metricName}.duration`, duration);
        systemMonitor.recordMetric(`${metricName}.error`, 1);

        throw error;
      }
    };
  };
}
