import fs from 'node:fs/promises';
import path from 'node:path';

export interface EmailTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  subject: string;
  text: string;
  html: string;
  variables: string[];
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TemplateVariable {
  name: string;
  description: string;
  type: 'string' | 'number' | 'date' | 'url';
  required: boolean;
  defaultValue?: string;
}

class EmailTemplateManager {
  private templatesDir: string;
  private templates: Map<string, EmailTemplate> = new Map();

  constructor() {
    this.templatesDir = path.join(__dirname, '../templates/email');
  }

  /** 初始化模板管理器 */
  async initialize(): Promise<void> {
    try {
      // 确保模板目录存在
      await this.ensureTemplateDir();

      // 加载默认模板
      await this.loadDefaultTemplates();

      // 加载自定义模板
      await this.loadCustomTemplates();
    } catch (error) {
      console.error('📧 [模板管理器] 初始化失败:', error);
    }
  }

  /** 确保模板目录存在 */
  private async ensureTemplateDir(): Promise<void> {
    try {
      await fs.access(this.templatesDir);
    } catch {
      await fs.mkdir(this.templatesDir, { recursive: true });
    }
  }

  /** 加载默认模板 */
  private async loadDefaultTemplates(): Promise<void> {
    const defaultTemplates: EmailTemplate[] = [
      {
        id: 'welcome',
        name: '欢迎邮件',
        description: '用户注册成功后发送的欢迎邮件',
        category: 'user',
        subject: '欢迎加入 {{systemName}}',
        text: `欢迎您，{{userName}}！

您已成功注册 {{systemName}} 账户。

账户信息：
- 用户名：{{userName}}
- 注册时间：{{registerTime}}

感谢您的加入！

此致
{{systemName}} 团队`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #409eff; margin: 0;">{{systemName}}</h1>
              <p style="color: #666; margin: 10px 0;">欢迎加入我们</p>
            </div>

            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h2 style="color: #333; margin-top: 0;">欢迎您，{{userName}}！</h2>
              <p style="color: #666; line-height: 1.6;">
                您已成功注册 {{systemName}} 账户。我们很高兴您能加入我们的社区。
              </p>

              <div style="margin: 20px 0;">
                <h3 style="color: #333; margin-bottom: 10px;">账户信息</h3>
                <ul style="color: #666; line-height: 1.6;">
                  <li>用户名：{{userName}}</li>
                  <li>注册时间：{{registerTime}}</li>
                </ul>
              </div>
            </div>

            <div style="border-top: 1px solid #eee; padding-top: 20px; text-align: center;">
              <p style="color: #999; font-size: 12px; margin: 0;">
                此致<br>
                {{systemName}} 团队
              </p>
            </div>
          </div>
        `,
        variables: ['systemName', 'userName', 'registerTime'],
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'resetPassword',
        name: '密码重置',
        description: '用户申请密码重置时发送的邮件',
        category: 'security',
        subject: '{{systemName}} - 密码重置请求',
        text: `您好，{{userName}}！

我们收到了您的密码重置请求。

请点击以下链接重置您的密码：
{{resetLink}}

此链接将在 {{expireTime}} 后失效。

如果您没有申请密码重置，请忽略此邮件。

此致
{{systemName}} 团队`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #409eff; margin: 0;">{{systemName}}</h1>
              <p style="color: #666; margin: 10px 0;">密码重置</p>
            </div>

            <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h2 style="color: #856404; margin-top: 0;">密码重置请求</h2>
              <p style="color: #856404; line-height: 1.6;">
                您好，{{userName}}！我们收到了您的密码重置请求。
              </p>

              <div style="text-align: center; margin: 30px 0;">
                <a href="{{resetLink}}" style="background: #409eff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">
                  重置密码
                </a>
              </div>

              <p style="color: #856404; font-size: 14px;">
                此链接将在 {{expireTime}} 后失效。如果您没有申请密码重置，请忽略此邮件。
              </p>
            </div>

            <div style="border-top: 1px solid #eee; padding-top: 20px; text-align: center;">
              <p style="color: #999; font-size: 12px; margin: 0;">
                此致<br>
                {{systemName}} 团队
              </p>
            </div>
          </div>
        `,
        variables: ['systemName', 'userName', 'resetLink', 'expireTime'],
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'notification',
        name: '系统通知',
        description: '系统重要通知邮件',
        category: 'system',
        subject: '{{systemName}} - {{title}}',
        text: `{{title}}

{{content}}

发送时间：{{sendTime}}

此致
{{systemName}} 团队`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #409eff; margin: 0;">{{systemName}}</h1>
              <p style="color: #666; margin: 10px 0;">系统通知</p>
            </div>

            <div style="background: #e3f2fd; border-left: 4px solid #2196f3; padding: 20px; margin-bottom: 20px;">
              <h2 style="color: #1976d2; margin-top: 0;">{{title}}</h2>
              <div style="color: #333; line-height: 1.6;">
                {{content}}
              </div>
            </div>

            <div style="border-top: 1px solid #eee; padding-top: 20px; text-align: center;">
              <p style="color: #999; font-size: 12px; margin: 0;">
                发送时间：{{sendTime}}<br>
                {{systemName}} 团队
              </p>
            </div>
          </div>
        `,
        variables: ['systemName', 'title', 'content', 'sendTime'],
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'maintenance',
        name: '维护通知',
        description: '系统维护通知邮件',
        category: 'system',
        subject: '{{systemName}} - 系统维护通知',
        text: `系统维护通知

尊敬的用户，

我们将于 {{startTime}} 至 {{endTime}} 进行系统维护。

维护原因：{{reason}}

维护期间，系统将暂时无法访问。我们会尽快完成维护工作，感谢您的理解。

此致
{{systemName}} 团队`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #409eff; margin: 0;">{{systemName}}</h1>
              <p style="color: #666; margin: 10px 0;">系统维护通知</p>
            </div>

            <div style="background: #fff3e0; border: 1px solid #ffcc02; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h2 style="color: #f57c00; margin-top: 0;">⚠️ 系统维护通知</h2>
              <p style="color: #e65100; line-height: 1.6;">
                尊敬的用户，我们将进行系统维护。
              </p>

              <div style="margin: 20px 0;">
                <h3 style="color: #f57c00; margin-bottom: 10px;">维护时间</h3>
                <p style="color: #e65100; font-weight: bold;">
                  {{startTime}} 至 {{endTime}}
                </p>

                <h3 style="color: #f57c00; margin-bottom: 10px;">维护原因</h3>
                <p style="color: #e65100;">
                  {{reason}}
                </p>
              </div>

              <p style="color: #e65100; font-size: 14px;">
                维护期间，系统将暂时无法访问。我们会尽快完成维护工作，感谢您的理解。
              </p>
            </div>

            <div style="border-top: 1px solid #eee; padding-top: 20px; text-align: center;">
              <p style="color: #999; font-size: 12px; margin: 0;">
                此致<br>
                {{systemName}} 团队
              </p>
            </div>
          </div>
        `,
        variables: ['systemName', 'startTime', 'endTime', 'reason'],
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    // 将默认模板添加到内存中
    for (const template of defaultTemplates) {
      this.templates.set(template.id, template);
    }
  }

  /** 加载自定义模板 */
  private async loadCustomTemplates(): Promise<void> {
    try {
      const files = await fs.readdir(this.templatesDir);
      const jsonFiles = files.filter(file => file.endsWith('.json'));

      for (const file of jsonFiles) {
        try {
          const filePath = path.join(this.templatesDir, file);
          const content = await fs.readFile(filePath, 'utf-8');
          const template: EmailTemplate = JSON.parse(content);

          // 验证模板格式
          if (this.validateTemplate(template)) {
            this.templates.set(template.id, template);
          }
        } catch (error) {
          console.error(`📧 [模板管理器] 加载模板文件失败: ${file}`, error);
        }
      }
    } catch (error) {
      console.error('📧 [模板管理器] 加载自定义模板失败:', error);
    }
  }

  /** 验证模板格式 */
  private validateTemplate(template: any): boolean {
    const requiredFields = ['id', 'name', 'subject', 'text', 'html', 'variables'];
    return requiredFields.every(field => template.hasOwnProperty(field));
  }

  /** 获取模板 */
  async getTemplate(templateId: string): Promise<EmailTemplate | null> {
    return this.templates.get(templateId) || null;
  }

  /** 获取所有模板 */
  async getAllTemplates(): Promise<EmailTemplate[]> {
    return Array.from(this.templates.values());
  }

  /** 获取模板列表（简化信息） */
  async getTemplateList(): Promise<
    Array<{
      id: string;
      name: string;
      description: string;
      category: string;
      variables: string[];
      isActive: boolean;
    }>
  > {
    return Array.from(this.templates.values()).map(template => ({
      id: template.id,
      name: template.name,
      description: template.description,
      category: template.category,
      variables: template.variables,
      isActive: template.isActive
    }));
  }

  /** 渲染模板 */
  async renderTemplate(
    templateId: string,
    variables: Record<string, any>
  ): Promise<{
    subject: string;
    text: string;
    html: string;
  } | null> {
    const template = await this.getTemplate(templateId);
    if (!template) {
      return null;
    }

    return {
      subject: this.replaceVariables(template.subject, variables),
      text: this.replaceVariables(template.text, variables),
      html: this.replaceVariables(template.html, variables)
    };
  }

  /** 替换模板变量 */
  private replaceVariables(template: string, variables: Record<string, any>): string {
    let result = template;

    for (const [key, value] of Object.entries(variables)) {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, String(value || ''));
    }

    return result;
  }

  /** 保存模板 */
  async saveTemplate(template: EmailTemplate): Promise<void> {
    // 更新内存中的模板
    template.updatedAt = new Date().toISOString();
    this.templates.set(template.id, template);

    // 保存到文件
    const filePath = path.join(this.templatesDir, `${template.id}.json`);
    await fs.writeFile(filePath, JSON.stringify(template, null, 2), 'utf-8');
  }

  /** 删除模板 */
  async deleteTemplate(templateId: string): Promise<boolean> {
    if (!this.templates.has(templateId)) {
      return false;
    }

    // 从内存中删除
    this.templates.delete(templateId);

    // 删除文件
    try {
      const filePath = path.join(this.templatesDir, `${templateId}.json`);
      await fs.unlink(filePath);
    } catch (error) {
      // 文件可能不存在（默认模板）
    }

    return true;
  }

  /** 获取模板变量定义 */
  getTemplateVariables(): Record<string, TemplateVariable[]> {
    return {
      user: [
        { name: 'userName', description: '用户名', type: 'string', required: true },
        { name: 'userEmail', description: '用户邮箱', type: 'string', required: true },
        { name: 'registerTime', description: '注册时间', type: 'date', required: false }
      ],
      system: [
        { name: 'systemName', description: '系统名称', type: 'string', required: true },
        { name: 'systemVersion', description: '系统版本', type: 'string', required: false },
        { name: 'sendTime', description: '发送时间', type: 'date', required: false }
      ],
      security: [
        { name: 'resetLink', description: '重置链接', type: 'url', required: true },
        { name: 'expireTime', description: '过期时间', type: 'date', required: true }
      ],
      maintenance: [
        { name: 'startTime', description: '开始时间', type: 'date', required: true },
        { name: 'endTime', description: '结束时间', type: 'date', required: true },
        { name: 'reason', description: '维护原因', type: 'string', required: true }
      ],
      notification: [
        { name: 'title', description: '通知标题', type: 'string', required: true },
        { name: 'content', description: '通知内容', type: 'string', required: true }
      ]
    };
  }
}

// 创建单例实例
export const emailTemplateManager = new EmailTemplateManager();

export default EmailTemplateManager;
