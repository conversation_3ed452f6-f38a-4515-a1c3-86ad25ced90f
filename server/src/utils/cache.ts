import NodeCache from 'node-cache';
import { CACHE_CONFIG } from '../config/performance';

/**
 * 缓存管理工具
 * 遵循项目规范，提供完整的缓存管理功能
 */

class CacheManager {
  private cache: NodeCache;
  private stats: {
    hits: number;
    misses: number;
    sets: number;
    deletes: number;
  };

  constructor() {
    this.cache = new NodeCache({
      stdTTL: CACHE_CONFIG.defaultTTL,
      checkperiod: CACHE_CONFIG.checkPeriod,
      maxKeys: CACHE_CONFIG.maxKeys,
      useClones: CACHE_CONFIG.useClones
    });

    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0
    };

    // 监听缓存事件
    this.cache.on('set', (key, value) => {
      this.stats.sets++;
      console.log(`🗄️ [缓存] 设置缓存: ${key}`);
    });

    this.cache.on('del', (key, value) => {
      this.stats.deletes++;
      console.log(`🗄️ [缓存] 删除缓存: ${key}`);
    });

    this.cache.on('expired', (key, value) => {
      console.log(`🗄️ [缓存] 缓存过期: ${key}`);
    });
  }

  /**
   * 获取缓存
   */
  get<T>(key: string): T | undefined {
    const value = this.cache.get<T>(this.getKey(key));
    if (value !== undefined) {
      this.stats.hits++;
      console.log(`🗄️ [缓存] 命中缓存: ${key}`);
    } else {
      this.stats.misses++;
      console.log(`🗄️ [缓存] 缓存未命中: ${key}`);
    }
    return value;
  }

  /**
   * 设置缓存
   */
  set<T>(key: string, value: T, ttl?: number): boolean {
    const cacheKey = this.getKey(key);
    const cacheTTL = ttl || this.getTTLByType(key);
    return this.cache.set(cacheKey, value, cacheTTL);
  }

  /**
   * 删除缓存
   */
  del(key: string | string[]): number {
    if (Array.isArray(key)) {
      return this.cache.del(key.map(k => this.getKey(k)));
    }
    return this.cache.del(this.getKey(key));
  }

  /**
   * 检查缓存是否存在
   */
  has(key: string): boolean {
    return this.cache.has(this.getKey(key));
  }

  /**
   * 获取缓存TTL
   */
  getTtl(key: string): number | undefined {
    return this.cache.getTtl(this.getKey(key));
  }

  /**
   * 获取所有缓存键
   */
  keys(): string[] {
    return this.cache.keys();
  }

  /**
   * 清空所有缓存
   */
  flushAll(): void {
    this.cache.flushAll();
    console.log('🗄️ [缓存] 清空所有缓存');
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const cacheStats = this.cache.getStats();
    return {
      ...this.stats,
      keys: cacheStats.keys,
      ksize: cacheStats.ksize,
      vsize: cacheStats.vsize,
      hitRate: this.stats.hits / (this.stats.hits + this.stats.misses) || 0
    };
  }

  /**
   * 获取或设置缓存（如果不存在则执行回调函数）
   */
  async getOrSet<T>(key: string, callback: () => Promise<T>, ttl?: number): Promise<T> {
    const cached = this.get<T>(key);
    if (cached !== undefined) {
      return cached;
    }

    const value = await callback();
    this.set(key, value, ttl);
    return value;
  }

  /**
   * 批量获取缓存
   */
  mget<T>(keys: string[]): { [key: string]: T } {
    const result: { [key: string]: T } = {};
    keys.forEach(key => {
      const value = this.get<T>(key);
      if (value !== undefined) {
        result[key] = value;
      }
    });
    return result;
  }

  /**
   * 批量设置缓存
   */
  mset<T>(data: { [key: string]: T }, ttl?: number): boolean {
    let success = true;
    Object.entries(data).forEach(([key, value]) => {
      if (!this.set(key, value, ttl)) {
        success = false;
      }
    });
    return success;
  }

  /**
   * 根据模式删除缓存
   */
  delByPattern(pattern: string): number {
    const keys = this.keys().filter(key => key.includes(pattern) || new RegExp(pattern).test(key));
    return this.del(keys);
  }

  /**
   * 获取缓存键（添加前缀）
   */
  private getKey(key: string): string {
    return `${CACHE_CONFIG.keyPrefix}${key}`;
  }

  /**
   * 根据键类型获取TTL
   */
  private getTTLByType(key: string): number {
    if (key.includes('user')) return CACHE_CONFIG.ttl.user;
    if (key.includes('config')) return CACHE_CONFIG.ttl.config;
    if (key.includes('stats')) return CACHE_CONFIG.ttl.stats;
    if (key.includes('course')) return CACHE_CONFIG.ttl.course;
    if (key.includes('provider')) return CACHE_CONFIG.ttl.provider;
    return CACHE_CONFIG.defaultTTL;
  }
}

// 创建缓存实例
export const cacheManager = new CacheManager();

// 缓存装饰器
export function Cacheable(key: string, ttl?: number) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const cacheKey = `${key}:${JSON.stringify(args)}`;

      // 尝试从缓存获取
      const cached = cacheManager.get(cacheKey);
      if (cached !== undefined) {
        return cached;
      }

      // 执行原方法
      const result = await method.apply(this, args);

      // 设置缓存
      cacheManager.set(cacheKey, result, ttl);

      return result;
    };
  };
}

// 缓存清理装饰器
export function CacheEvict(pattern: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      // 执行原方法
      const result = await method.apply(this, args);

      // 清理相关缓存
      cacheManager.delByPattern(pattern);

      return result;
    };
  };
}

// 常用缓存方法
export const cache = {
  // 用户缓存
  user: {
    get: (userId: number) => cacheManager.get(`user:${userId}`),
    set: (userId: number, user: any) => cacheManager.set(`user:${userId}`, user),
    del: (userId: number) => cacheManager.del(`user:${userId}`),
    clear: () => cacheManager.delByPattern('user:')
  },

  // 配置缓存
  config: {
    get: (key: string) => cacheManager.get(`config:${key}`),
    set: (key: string, value: any) => cacheManager.set(`config:${key}`, value),
    del: (key: string) => cacheManager.del(`config:${key}`),
    clear: () => cacheManager.delByPattern('config:')
  },

  // 统计缓存
  stats: {
    get: (key: string) => cacheManager.get(`stats:${key}`),
    set: (key: string, value: any) => cacheManager.set(`stats:${key}`, value),
    del: (key: string) => cacheManager.del(`stats:${key}`),
    clear: () => cacheManager.delByPattern('stats:')
  },

  // 课程缓存
  course: {
    get: (courseId: number) => cacheManager.get(`course:${courseId}`),
    set: (courseId: number, course: any) => cacheManager.set(`course:${courseId}`, course),
    del: (courseId: number) => cacheManager.del(`course:${courseId}`),
    clear: () => cacheManager.delByPattern('course:')
  },

  // 供应商缓存
  provider: {
    get: (providerId: number) => cacheManager.get(`provider:${providerId}`),
    set: (providerId: number, provider: any) => cacheManager.set(`provider:${providerId}`, provider),
    del: (providerId: number) => cacheManager.del(`provider:${providerId}`),
    clear: () => cacheManager.delByPattern('provider:')
  }
};

export default cacheManager;
