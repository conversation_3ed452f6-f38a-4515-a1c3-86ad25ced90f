/**
 * 后端日志记录工具
 * 统一记录操作日志到fd_log表
 */

import { executeQuery } from './db';
import { formatDateTimeForLog } from './timeUtils';

/**
 * 获取客户端真实IP地址
 */
function getRealClientIP(req?: any): string {
  if (!req) return 'unknown';

  // 按优先级获取真实IP
  const ip =
    req.headers['x-forwarded-for']?.split(',')[0]?.trim() ||
    req.headers['x-real-ip'] ||
    req.headers['x-client-ip'] ||
    req.headers['cf-connecting-ip'] || // Cloudflare
    req.headers['x-cluster-client-ip'] ||
    req.connection?.remoteAddress ||
    req.socket?.remoteAddress ||
    req.ip ||
    'unknown';

  // 清理IPv6映射的IPv4地址
  if (ip.startsWith('::ffff:')) {
    return ip.substring(7);
  }

  return ip;
}

/**
 * 生成详细的操作内容描述
 */
function generateDetailedContent(
  module: string,
  action: string,
  target_type?: string,
  target_id?: string | number,
  data_before?: any,
  data_after?: any
): string {
  const actionMap: Record<string, string> = {
    create: '创建',
    update: '更新',
    delete: '删除',
    sync: '同步',
    refill: '补刷',
    login: '登录',
    logout: '登出',
    view: '查看',
    export: '导出',
    import: '导入',
    upload: '上传',
    download: '下载',
    activate: '激活',
    deactivate: '停用',
    reset: '重置',
    change_password: '修改密码'
  };

  const moduleMap: Record<string, string> = {
    user: '用户',
    order: '订单',
    product: '商品',
    category: '分类',
    provider: '货源',
    system: '系统',
    auth: '认证',
    config: '配置',
    log: '日志',
    monitor: '监控'
  };

  const actionText = actionMap[action] || action;
  const moduleText = moduleMap[module] || module;
  const targetText = target_type ? `${target_type}` : '';
  const targetIdText = target_id ? `(ID: ${target_id})` : '';

  let content = `${actionText}${moduleText}${targetText}${targetIdText}`;

  // 添加具体变更信息
  if (data_before && data_after) {
    const changes = getDataChanges(data_before, data_after);
    if (changes.length > 0) {
      content += ` - 变更: ${changes.join(', ')}`;
    }
  } else if (data_after && action === 'create') {
    const details = getCreationDetails(data_after);
    if (details) {
      content += ` - ${details}`;
    }
  }

  return content;
}

/**
 * 获取数据变更详情
 */
function getDataChanges(dataBefore: any, dataAfter: any): string[] {
  const changes: string[] = [];

  if (!dataBefore || !dataAfter) return changes;

  const beforeObj = typeof dataBefore === 'string' ? JSON.parse(dataBefore) : dataBefore;
  const afterObj = typeof dataAfter === 'string' ? JSON.parse(dataAfter) : dataAfter;

  // 比较关键字段
  const keyFields = ['name', 'title', 'status', 'price', 'username', 'email', 'role'];

  for (const field of keyFields) {
    if (beforeObj[field] !== undefined && afterObj[field] !== undefined && beforeObj[field] !== afterObj[field]) {
      changes.push(`${field}: ${beforeObj[field]} → ${afterObj[field]}`);
    }
  }

  return changes;
}

/**
 * 获取创建操作的详情
 */
function getCreationDetails(data: any): string {
  if (!data) return '';

  const dataObj = typeof data === 'string' ? JSON.parse(data) : data;

  // 根据不同类型返回关键信息
  if (dataObj.name) return `名称: ${dataObj.name}`;
  if (dataObj.title) return `标题: ${dataObj.title}`;
  if (dataObj.username) return `用户名: ${dataObj.username}`;
  if (dataObj.order_id) return `订单号: ${dataObj.order_id}`;

  return '';
}

export interface LogData {
  user_id?: number;
  operator_type?: 'system' | 'admin' | 'user';
  module: string;
  action: string;
  target_type?: string;
  target_id?: string | number;
  content?: string;
  data_before?: any;
  data_after?: any;
  result?: 'success' | 'failed' | 'pending';
  error_message?: string;
  ip_address?: string;
  user_agent?: string;
  request?: any; // 添加请求对象以便获取真实IP
}

/**
 * 记录操作日志
 */
export async function logOperation(data: LogData): Promise<void> {
  try {
    const {
      user_id = 0,
      operator_type = 'system',
      module,
      action,
      target_type,
      target_id,
      content,
      data_before,
      data_after,
      result = 'success',
      error_message,
      ip_address,
      user_agent,
      request
    } = data;

    // 获取真实IP地址
    const realIP = ip_address || getRealClientIP(request);
    const realUserAgent = user_agent || request?.headers?.['user-agent'] || 'unknown';

    // 生成详细的操作内容
    let detailedContent = content;
    if (!detailedContent) {
      detailedContent = generateDetailedContent(module, action, target_type, target_id, data_before, data_after);
    }

    const insertQuery = `
      INSERT INTO fd_log (
        user_id, operator_id, operator_type, module, action, target_type, target_id,
        content, data_before, data_after, result, error_message, ip_address, user_agent, create_time
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
    `;

    await executeQuery(insertQuery, [
      user_id,
      user_id,
      operator_type,
      module,
      action,
      target_type || null,
      target_id ? String(target_id) : null,
      detailedContent || null,
      data_before ? JSON.stringify(data_before) : null,
      data_after ? JSON.stringify(data_after) : null,
      result,
      error_message || null,
      realIP,
      realUserAgent
    ]);

    console.log(`${formatDateTimeForLog()} 📝 [日志记录] 操作日志记录成功:`, {
      user_id,
      module,
      action,
      target_type,
      target_id,
      result
    });
  } catch (error) {
    console.error(`${formatDateTimeForLog()} 📝 [日志记录] 记录操作日志失败:`, error);
    // 日志记录失败不应该影响主要业务流程
  }
}

/**
 * 记录商品操作日志
 */
export async function logProductOperation(
  action: string,
  productId: number | string,
  data_before?: any,
  data_after?: any,
  user_id?: number,
  operator_type?: 'system' | 'admin' | 'user',
  error_message?: string
): Promise<void> {
  await logOperation({
    user_id,
    operator_type,
    module: 'product',
    action,
    target_type: 'product',
    target_id: productId,
    content: `商品操作: ${action}`,
    data_before,
    data_after,
    result: error_message ? 'failed' : 'success',
    error_message
  });
}

/**
 * 记录29平台对接日志
 */
export async function logPlatform29Operation(
  action: string,
  details: any,
  result: 'success' | 'failed' = 'success',
  error_message?: string,
  user_id?: number,
  operator_type?: 'system' | 'admin' | 'user'
): Promise<void> {
  await logOperation({
    user_id,
    operator_type,
    module: 'platform29',
    action,
    target_type: 'platform29',
    content: `29平台操作: ${action}`,
    data_after: details,
    result,
    error_message
  });
}

/**
 * 记录订单操作日志
 */
export async function logOrderOperation(
  action: string,
  orderId: number | string,
  data_before?: any,
  data_after?: any,
  user_id?: number,
  operator_type?: 'system' | 'admin' | 'user',
  error_message?: string,
  request?: any
): Promise<void> {
  await logOperation({
    user_id,
    operator_type,
    module: 'order',
    action,
    target_type: 'order',
    target_id: orderId,
    data_before,
    data_after,
    result: error_message ? 'failed' : 'success',
    error_message,
    request
  });
}

/**
 * 记录货源管理日志
 */
export async function logProviderOperation(
  action: string,
  providerId: number | string,
  data_before?: any,
  data_after?: any,
  user_id?: number,
  operator_type?: 'system' | 'admin' | 'user',
  error_message?: string
): Promise<void> {
  await logOperation({
    user_id,
    operator_type,
    module: 'provider',
    action,
    target_type: 'provider',
    target_id: providerId,
    content: `货源操作: ${action}`,
    data_before,
    data_after,
    result: error_message ? 'failed' : 'success',
    error_message
  });
}

/**
 * 记录用户操作日志
 */
export async function logUserOperation(
  action: string,
  targetUserId: number | string,
  data_before?: any,
  data_after?: any,
  user_id?: number,
  operator_type?: 'system' | 'admin' | 'user',
  error_message?: string
): Promise<void> {
  await logOperation({
    user_id,
    operator_type,
    module: 'user',
    action,
    target_type: 'user',
    target_id: targetUserId,
    content: `用户操作: ${action}`,
    data_before,
    data_after,
    result: error_message ? 'failed' : 'success',
    error_message
  });
}

/**
 * 记录系统操作日志
 */
export async function logSystemOperation(
  action: string,
  details: any,
  result: 'success' | 'failed' = 'success',
  error_message?: string,
  user_id?: number,
  operator_type?: 'system' | 'admin' | 'user'
): Promise<void> {
  await logOperation({
    user_id,
    operator_type,
    module: 'system',
    action,
    target_type: 'system',
    content: `系统操作: ${action}`,
    data_after: details,
    result,
    error_message
  });
}

/**
 * 从请求中提取用户信息
 */
export function extractUserInfo(req: any): {
  user_id: number;
  operator_type: 'system' | 'admin' | 'user';
  ip_address: string;
  user_agent: string;
} {
  const user_id = req.user?.userId || 0;
  const operator_type = user_id === 0 ? 'system' : req.user?.roles?.includes('admin') ? 'admin' : 'user';
  const ip_address = req.ip || req.connection.remoteAddress || 'unknown';
  const user_agent = req.get('User-Agent') || 'unknown';

  return {
    user_id,
    operator_type,
    ip_address,
    user_agent
  };
}
