/**
 * 接口标准定义
 * 定义不同接口类型的标准输入输出字段规范
 */

import type { ProjectStandardFields } from './StandardFields';
import { InterfaceType } from './StandardFields';

/**
 * 通用接口响应结构
 */
export interface StandardResponse<T = any> {
  success: boolean; // 是否成功
  message: string; // 响应消息
  data?: T; // 响应数据
  error?: string; // 错误代码
  timestamp?: string; // 时间戳
}

/**
 * 查课接口标准
 */
export interface QueryCourseStandard {
  input: {
    api_uid: string; // 货源用户ID
    api_key: string; // 货源密钥
    username: string; // 学生账号
    password: string; // 学生密码
    school: string; // 学校名称
    platform: string; // 平台标识
  };

  output: StandardResponse<{
    courses: Array<{
      course_id: string; // 课程ID
      course_name: string; // 课程名称
      teacher?: string; // 教师
      credit?: string; // 学分
      status?: string; // 状态
      type?: string; // 类型
    }>;
    total_count?: number; // 总数量
    account_info?: {
      // 账号信息
      username: string;
      school: string;
      platform: string;
    };
  }>;
}

/**
 * 下单接口标准
 */
export interface CreateOrderStandard {
  input: {
    api_uid: string; // 货源用户ID
    api_key: string; // 货源密钥
    username: string; // 学生账号
    password: string; // 学生密码
    school: string; // 学校名称
    platform: string; // 平台标识
    course_id: string; // 课程ID
    course_name: string; // 课程名称
    quantity?: number; // 数量（可选）
  };

  output: StandardResponse<{
    upstream_order_id: string; // 上游订单ID
    order_status?: string; // 订单状态
    estimated_time?: string; // 预计完成时间
    amount?: number; // 金额
    remarks?: string; // 备注
  }>;
}

/**
 * 同步接口标准
 */
export interface SyncOrderStandard {
  input: {
    api_uid: string; // 货源用户ID
    api_key: string; // 货源密钥
    upstream_order_id: string; // 上游订单ID
    username?: string; // 学生账号（可选）
    school?: string; // 学校名称（可选）
  };

  output: StandardResponse<{
    upstream_order_id: string; // 上游订单ID
    status: string; // 订单状态
    progress: string; // 进度信息
    remarks?: string; // 备注信息
    course_name?: string; // 课程名称
    username?: string; // 学生账号
    school?: string; // 学校名称
    platform_name?: string; // 平台名称
    course_start_time?: string; // 课程开始时间
    course_end_time?: string; // 课程结束时间
    exam_start_time?: string; // 考试开始时间
    exam_end_time?: string; // 考试结束时间
    add_time?: string; // 添加时间
  }>;
}

/**
 * 补刷接口标准
 */
export interface RefillOrderStandard {
  input: {
    api_uid: string; // 货源用户ID
    api_key: string; // 货源密钥
    upstream_order_id: string; // 上游订单ID
    reason?: string; // 补刷原因（可选）
  };

  output: StandardResponse<{
    new_upstream_order_id?: string; // 新的上游订单ID
    original_order_id: string; // 原订单ID
    refill_status: string; // 补刷状态
    remarks?: string; // 备注
  }>;
}

/**
 * 改密接口标准
 */
export interface ChangePasswordStandard {
  input: {
    api_uid: string; // 货源用户ID
    api_key: string; // 货源密钥
    upstream_order_id: string; // 上游订单ID
    new_password: string; // 新密码
  };

  output: StandardResponse<{
    upstream_order_id: string; // 上游订单ID
    change_status: string; // 修改状态
    remarks?: string; // 备注
  }>;
}

/**
 * 余额查询接口标准
 */
export interface GetBalanceStandard {
  input: {
    api_uid: string; // 货源用户ID
    api_key: string; // 货源密钥
  };

  output: StandardResponse<{
    balance: number; // 余额
    currency?: string; // 货币单位
    user_name?: string; // 用户名
    user_id?: string; // 用户ID
    last_update?: string; // 最后更新时间
  }>;
}

/**
 * 获取课程列表接口标准
 */
export interface GetCoursesStandard {
  input: {
    api_uid: string; // 货源用户ID
    api_key: string; // 货源密钥
    platform?: string; // 平台标识（可选）
    category?: string; // 分类（可选）
  };

  output: StandardResponse<{
    courses: Array<{
      platform_cid: string; // 平台CID
      platform_name: string; // 平台名称
      course_name: string; // 课程名称
      price?: number; // 价格
      status?: string; // 状态
      category?: string; // 分类
    }>;
    total_count?: number; // 总数量
  }>;
}

/**
 * 接口标准映射
 */
export const INTERFACE_STANDARDS: Record<InterfaceType, any> = {
  [InterfaceType.QUERY]: {} as QueryCourseStandard,
  [InterfaceType.ORDER]: {} as CreateOrderStandard,
  [InterfaceType.SYNC]: {} as SyncOrderStandard,
  [InterfaceType.REFILL]: {} as RefillOrderStandard,
  [InterfaceType.CHANGE_PASSWORD]: {} as ChangePasswordStandard,
  [InterfaceType.GET_BALANCE]: {} as GetBalanceStandard,
  [InterfaceType.GET_COURSES]: {} as GetCoursesStandard
};

/**
 * 接口描述信息
 */
export const INTERFACE_DESCRIPTIONS: Record<InterfaceType, { name: string; description: string }> = {
  [InterfaceType.QUERY]: {
    name: '查课接口',
    description: '查询学生账号下的课程列表'
  },
  [InterfaceType.ORDER]: {
    name: '下单接口',
    description: '创建新的代刷订单'
  },
  [InterfaceType.SYNC]: {
    name: '同步接口',
    description: '同步订单的最新状态和进度'
  },
  [InterfaceType.REFILL]: {
    name: '补刷接口',
    description: '对失败或异常的订单进行补刷'
  },
  [InterfaceType.CHANGE_PASSWORD]: {
    name: '改密接口',
    description: '修改订单关联账号的密码'
  },
  [InterfaceType.GET_BALANCE]: {
    name: '余额查询',
    description: '查询货源账号的余额信息'
  },
  [InterfaceType.GET_COURSES]: {
    name: '课程列表',
    description: '获取货源支持的课程/平台列表'
  }
};

/**
 * 获取接口的必填字段
 */
export function getRequiredFields(interfaceType: InterfaceType): string[] {
  const commonAuthFields = ['api_uid', 'api_key'];

  switch (interfaceType) {
    case InterfaceType.QUERY:
      return [...commonAuthFields, 'username', 'password', 'school', 'platform'];

    case InterfaceType.ORDER:
      return [...commonAuthFields, 'username', 'password', 'school', 'platform', 'course_id', 'course_name'];

    case InterfaceType.SYNC:
      return [...commonAuthFields, 'upstream_order_id'];

    case InterfaceType.REFILL:
      return [...commonAuthFields, 'upstream_order_id'];

    case InterfaceType.CHANGE_PASSWORD:
      return [...commonAuthFields, 'upstream_order_id', 'new_password'];

    case InterfaceType.GET_BALANCE:
      return commonAuthFields;

    case InterfaceType.GET_COURSES:
      return commonAuthFields;

    default:
      return commonAuthFields;
  }
}

/**
 * 获取接口的可选字段
 */
export function getOptionalFields(interfaceType: InterfaceType): string[] {
  switch (interfaceType) {
    case InterfaceType.QUERY:
      return ['course_id', 'teacher', 'semester'];

    case InterfaceType.ORDER:
      return ['quantity', 'teacher', 'semester'];

    case InterfaceType.SYNC:
      return ['username', 'school'];

    case InterfaceType.REFILL:
      return ['reason'];

    case InterfaceType.CHANGE_PASSWORD:
      return [];

    case InterfaceType.GET_BALANCE:
      return [];

    case InterfaceType.GET_COURSES:
      return ['platform', 'category'];

    default:
      return [];
  }
}

/**
 * 验证接口输入数据
 */
export function validateInterfaceInput(
  interfaceType: InterfaceType,
  inputData: Partial<ProjectStandardFields>
): { valid: boolean; missingFields: string[]; errors: string[] } {
  const requiredFields = getRequiredFields(interfaceType);
  const missingFields: string[] = [];
  const errors: string[] = [];

  // 检查必填字段
  for (const field of requiredFields) {
    if (!inputData[field] || inputData[field] === '') {
      missingFields.push(field);
    }
  }

  // 特殊验证规则
  if (interfaceType === InterfaceType.CHANGE_PASSWORD) {
    if (inputData.new_password && inputData.new_password.length < 6) {
      errors.push('新密码长度不能少于6位');
    }
  }

  if (interfaceType === InterfaceType.ORDER || interfaceType === InterfaceType.QUERY) {
    if (inputData.username && !/^[a-zA-Z0-9_@.-]+$/.test(inputData.username)) {
      errors.push('学生账号格式不正确');
    }
  }

  return {
    valid: missingFields.length === 0 && errors.length === 0,
    missingFields,
    errors
  };
}

/**
 * 获取接口示例数据
 */
export function getInterfaceExample(interfaceType: InterfaceType): any {
  const examples: Record<InterfaceType, any> = {
    [InterfaceType.QUERY]: {
      api_uid: '12345',
      api_key: 'abc123xyz',
      username: '18012345678',
      password: '123456',
      school: '北京大学',
      platform: 'xuetong'
    },
    [InterfaceType.ORDER]: {
      api_uid: '12345',
      api_key: 'abc123xyz',
      username: '18012345678',
      password: '123456',
      school: '北京大学',
      platform: 'xuetong',
      course_id: 'course_123',
      course_name: '高等数学'
    },
    [InterfaceType.SYNC]: {
      api_uid: '12345',
      api_key: 'abc123xyz',
      upstream_order_id: '210105'
    },
    [InterfaceType.REFILL]: {
      api_uid: '12345',
      api_key: 'abc123xyz',
      upstream_order_id: '210105',
      reason: '订单异常，需要补刷'
    },
    [InterfaceType.CHANGE_PASSWORD]: {
      api_uid: '12345',
      api_key: 'abc123xyz',
      upstream_order_id: '210105',
      new_password: 'newpass123'
    },
    [InterfaceType.GET_BALANCE]: {
      api_uid: '12345',
      api_key: 'abc123xyz'
    },
    [InterfaceType.GET_COURSES]: {
      api_uid: '12345',
      api_key: 'abc123xyz',
      platform: 'xuetong'
    }
  };

  return examples[interfaceType] || {};
}
