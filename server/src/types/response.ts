/** 统一响应结果类型 - SoybeanAdmin标准格式 */
export interface ResponseResult<T = any> {
  code: string;
  msg: string;
  data: T | null;
}

/** 创建成功响应 */
export function createSuccessResponse<T>(data: T, msg = '操作成功'): ResponseResult<T> {
  return {
    code: ResponseCode.SUCCESS,
    msg,
    data
  };
}

/** 创建失败响应 */
export function createErrorResponse(msg = '操作失败', code = ResponseCode.ERROR): ResponseResult<null> {
  return {
    code,
    msg,
    data: null
  };
}

/** 常用业务状态码 - SoybeanAdmin标准 */
export const ResponseCode = {
  SUCCESS: '0000', // 成功
  ERROR: '9999', // 服务器内部错误
  SYSTEM_ERROR: '9999', // 系统错误（别名）
  PARAM_ERROR: '1001', // 请求参数错误
  AUTH_ERROR: '1002', // 认证错误
  FORBIDDEN: '1003', // 禁止访问
  PERMISSION_DENIED: '1003', // 权限拒绝（别名）
  TOKEN_INVALID: '1004', // 令牌无效
  TOKEN_EXPIRED: '1005', // 令牌过期
  LOGOUT_REQUIRED: '1006', // 需要退出登录
  MODAL_LOGOUT: '1007', // 需要弹窗提示并退出登录
  DATA_EXISTS: '2001', // 数据已存在
  DATA_NOT_FOUND: '2002', // 数据不存在
  RATE_LIMIT: '4001', // 请求频率限制
  NOT_LOGGED_IN: '4002', // 未登录
  SERVICE_UNAVAILABLE: '5001' // 服务不可用
} as const;
