/**
 * 项目标准字段定义
 * 定义所有货源对接中使用的标准字段规范
 */

/**
 * 字段验证规则
 */
export interface FieldValidationRule {
  required?: boolean; // 是否必填
  type?: 'string' | 'number' | 'boolean' | 'array' | 'object'; // 数据类型
  minLength?: number; // 最小长度
  maxLength?: number; // 最大长度
  pattern?: string; // 正则表达式
  enum?: string[]; // 枚举值
  description?: string; // 字段描述
}

/**
 * 字段定义
 */
export interface FieldDefinition {
  name: string; // 字段名
  label: string; // 显示名称
  description: string; // 字段描述
  group: string; // 字段分组
  validation: FieldValidationRule; // 验证规则
  example?: string; // 示例值
  defaultValue?: any; // 默认值
}

/**
 * 认证字段组
 */
export interface AuthFields {
  api_uid: string; // 货源用户ID/账号
  api_key: string; // 货源密钥/密码
  token?: string; // 可选的访问令牌
  base_url?: string; // 货源基础URL
}

/**
 * 学生信息字段组
 */
export interface StudentFields {
  username: string; // 学生账号
  password: string; // 学生密码
  school: string; // 学校名称
  platform: string; // 平台标识/CID
  student_name?: string; // 学生姓名（可选）
  student_id?: string; // 学号（可选）
}

/**
 * 课程信息字段组
 */
export interface CourseFields {
  course_id: string; // 课程ID
  course_name: string; // 课程名称
  teacher?: string; // 教师姓名
  credit?: string; // 学分
  course_type?: string; // 课程类型
  semester?: string; // 学期
}

/**
 * 订单信息字段组
 */
export interface OrderFields {
  upstream_order_id: string; // 上游订单ID
  order_no?: string; // 本地订单号
  new_password?: string; // 新密码（改密用）
  quantity?: number; // 数量
  amount?: number; // 金额
}

/**
 * 时间信息字段组
 */
export interface TimeFields {
  course_start_time?: string; // 课程开始时间
  course_end_time?: string; // 课程结束时间
  exam_start_time?: string; // 考试开始时间
  exam_end_time?: string; // 考试结束时间
  add_time?: string; // 添加时间
}

/**
 * 状态信息字段组
 */
export interface StatusFields {
  status: string; // 订单状态
  progress: string; // 进度信息
  remarks?: string; // 备注信息
  process_status?: string; // 处理状态
}

/**
 * 项目标准字段集合
 */
export interface ProjectStandardFields
  extends AuthFields,
    StudentFields,
    CourseFields,
    OrderFields,
    TimeFields,
    StatusFields {
  // 支持任意额外字段
  [key: string]: any;
}

/**
 * 接口类型枚举
 */
export enum InterfaceType {
  QUERY = 'query', // 查课
  ORDER = 'order', // 下单
  SYNC = 'sync', // 同步
  REFILL = 'refill', // 补刷
  CHANGE_PASSWORD = 'change_password', // 改密
  GET_BALANCE = 'get_balance', // 查余额
  GET_COURSES = 'get_courses' // 获取课程列表
}

/**
 * 查课接口标准字段
 */
export interface QueryInterfaceFields {
  input: Pick<ProjectStandardFields, 'api_uid' | 'api_key' | 'username' | 'password' | 'school' | 'platform'>;
  output: {
    success: boolean;
    message: string;
    courses: Array<Pick<CourseFields, 'course_id' | 'course_name' | 'teacher' | 'credit'>>;
  };
}

/**
 * 下单接口标准字段
 */
export interface OrderInterfaceFields {
  input: Pick<
    ProjectStandardFields,
    'api_uid' | 'api_key' | 'username' | 'password' | 'school' | 'platform' | 'course_id' | 'course_name'
  >;
  output: {
    success: boolean;
    message: string;
    upstream_order_id?: string;
    estimated_time?: string;
  };
}

/**
 * 同步接口标准字段
 */
export interface SyncInterfaceFields {
  input: Pick<ProjectStandardFields, 'api_uid' | 'api_key' | 'upstream_order_id'>;
  output: {
    success: boolean;
    message: string;
    status: string;
    progress: string;
    remarks?: string;
    course_start_time?: string;
    course_end_time?: string;
    exam_start_time?: string;
    exam_end_time?: string;
  };
}

/**
 * 补刷接口标准字段
 */
export interface RefillInterfaceFields {
  input: Pick<ProjectStandardFields, 'api_uid' | 'api_key' | 'upstream_order_id'>;
  output: {
    success: boolean;
    message: string;
    new_upstream_order_id?: string;
  };
}

/**
 * 改密接口标准字段
 */
export interface ChangePasswordInterfaceFields {
  input: Pick<ProjectStandardFields, 'api_uid' | 'api_key' | 'upstream_order_id' | 'new_password'>;
  output: {
    success: boolean;
    message: string;
  };
}

/**
 * 余额查询接口标准字段
 */
export interface GetBalanceInterfaceFields {
  input: Pick<ProjectStandardFields, 'api_uid' | 'api_key'>;
  output: {
    success: boolean;
    message: string;
    balance: number;
    currency?: string;
  };
}

/**
 * 接口标准字段映射
 */
export const INTERFACE_STANDARD_FIELDS: Record<InterfaceType, any> = {
  [InterfaceType.QUERY]: {} as QueryInterfaceFields,
  [InterfaceType.ORDER]: {} as OrderInterfaceFields,
  [InterfaceType.SYNC]: {} as SyncInterfaceFields,
  [InterfaceType.REFILL]: {} as RefillInterfaceFields,
  [InterfaceType.CHANGE_PASSWORD]: {} as ChangePasswordInterfaceFields,
  [InterfaceType.GET_BALANCE]: {} as GetBalanceInterfaceFields,
  [InterfaceType.GET_COURSES]: {} as QueryInterfaceFields
};

/**
 * 字段分组定义
 */
export const FIELD_GROUPS = {
  AUTH: 'auth', // 认证字段
  STUDENT: 'student', // 学生信息
  COURSE: 'course', // 课程信息
  ORDER: 'order', // 订单信息
  TIME: 'time', // 时间信息
  STATUS: 'status' // 状态信息
} as const;

/**
 * 标准字段定义注册表
 */
export const STANDARD_FIELD_DEFINITIONS: Record<string, FieldDefinition> = {
  // 认证字段
  api_uid: {
    name: 'api_uid',
    label: '货源用户ID',
    description: '货源平台的用户ID或账号',
    group: FIELD_GROUPS.AUTH,
    validation: { required: true, type: 'string', minLength: 1 },
    example: '12345'
  },
  api_key: {
    name: 'api_key',
    label: '货源密钥',
    description: '货源平台的API密钥或密码',
    group: FIELD_GROUPS.AUTH,
    validation: { required: true, type: 'string', minLength: 1 },
    example: 'abc123xyz'
  },
  token: {
    name: 'token',
    label: '访问令牌',
    description: '可选的访问令牌',
    group: FIELD_GROUPS.AUTH,
    validation: { required: false, type: 'string' },
    example: 'eyJhbGciOiJIUzI1NiIs...'
  },

  // 学生信息字段
  username: {
    name: 'username',
    label: '学生账号',
    description: '学生的登录账号',
    group: FIELD_GROUPS.STUDENT,
    validation: { required: true, type: 'string', minLength: 1 },
    example: '18012345678'
  },
  password: {
    name: 'password',
    label: '学生密码',
    description: '学生的登录密码',
    group: FIELD_GROUPS.STUDENT,
    validation: { required: true, type: 'string', minLength: 1 },
    example: '123456'
  },
  school: {
    name: 'school',
    label: '学校名称',
    description: '学生所在学校的名称',
    group: FIELD_GROUPS.STUDENT,
    validation: { required: true, type: 'string' },
    example: '北京大学',
    defaultValue: '自动识别'
  },
  platform: {
    name: 'platform',
    label: '平台标识',
    description: '学习平台的标识或CID',
    group: FIELD_GROUPS.STUDENT,
    validation: { required: true, type: 'string' },
    example: 'xuetong'
  },

  // 课程信息字段
  course_id: {
    name: 'course_id',
    label: '课程ID',
    description: '课程的唯一标识',
    group: FIELD_GROUPS.COURSE,
    validation: { required: true, type: 'string' },
    example: 'course_123456'
  },
  course_name: {
    name: 'course_name',
    label: '课程名称',
    description: '课程的名称',
    group: FIELD_GROUPS.COURSE,
    validation: { required: true, type: 'string' },
    example: '高等数学'
  },

  // 订单信息字段
  upstream_order_id: {
    name: 'upstream_order_id',
    label: '上游订单ID',
    description: '货源平台返回的订单ID',
    group: FIELD_GROUPS.ORDER,
    validation: { required: true, type: 'string' },
    example: '210105'
  },
  new_password: {
    name: 'new_password',
    label: '新密码',
    description: '修改密码时的新密码',
    group: FIELD_GROUPS.ORDER,
    validation: { required: false, type: 'string' },
    example: 'newpass123'
  },

  // 状态信息字段
  status: {
    name: 'status',
    label: '订单状态',
    description: '订单的当前状态',
    group: FIELD_GROUPS.STATUS,
    validation: { required: false, type: 'string' },
    example: '队列中'
  },
  progress: {
    name: 'progress',
    label: '进度信息',
    description: '订单的进度信息',
    group: FIELD_GROUPS.STATUS,
    validation: { required: false, type: 'string' },
    example: '50%'
  },
  remarks: {
    name: 'remarks',
    label: '备注信息',
    description: '订单的备注或说明',
    group: FIELD_GROUPS.STATUS,
    validation: { required: false, type: 'string' },
    example: '正在处理中，请耐心等待'
  }
};
