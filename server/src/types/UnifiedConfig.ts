/**
 * 统一配置结构定义
 * 整合request_template、response_mapping、field_mapping等为一个统一配置
 */

import { InterfaceType } from './StandardFields';

/**
 * 数据转换函数类型
 */
export type TransformFunction =
  | 'toString'
  | 'toNumber'
  | 'toBoolean'
  | 'toUpperCase'
  | 'toLowerCase'
  | 'trim'
  | 'encodeBase64'
  | 'decodeBase64'
  | 'md5'
  | string; // 支持自定义转换函数

/**
 * 字段映射配置
 */
export interface FieldMappingConfig {
  provider_field: string; // 货源字段名
  required?: boolean; // 是否必填
  default_value?: any; // 默认值
  transform?: TransformFunction; // 数据转换函数
  description?: string; // 字段描述
}

/**
 * 请求映射配置
 */
export interface RequestMappingConfig {
  [standardField: string]: FieldMappingConfig;
}

/**
 * 响应成功条件
 */
export interface SuccessCondition {
  field: string; // 判断字段
  condition: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'contains'; // 条件类型
  value: any; // 期望值
}

/**
 * 响应字段映射
 */
export interface ResponseFieldMapping {
  [standardField: string]:
    | string
    | {
        provider_field: string;
        transform?: TransformFunction;
        default_value?: any;
      };
}

/**
 * 响应映射配置
 */
export interface ResponseMappingConfig {
  success_condition: SuccessCondition; // 成功判断条件
  message_field: string; // 消息字段
  data_field?: string; // 数据字段（可选）
  field_mapping: ResponseFieldMapping; // 字段映射
  error_mapping?: {
    // 错误映射（可选）
    [errorCode: string]: string;
  };
}

/**
 * HTTP配置
 */
export interface HttpConfig {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  endpoint: string; // 接口端点
  headers?: Record<string, string>; // 请求头
  timeout?: number; // 超时时间（毫秒）
  content_type?: 'json' | 'form' | 'xml'; // 内容类型
}

/**
 * 接口配置
 */
export interface InterfaceConfig {
  enabled: boolean; // 是否启用
  http: HttpConfig; // HTTP配置
  request_mapping: RequestMappingConfig; // 请求映射
  response_mapping: ResponseMappingConfig; // 响应映射
  custom_code?: string; // 自定义代码（可选）
  test_data?: any; // 测试数据（可选）
  description?: string; // 接口描述
}

/**
 * 货源统一配置
 */
export interface ProviderUnifiedConfig {
  version: string; // 配置版本
  provider_info: {
    provider_id: number;
    provider_name: string;
    provider_code: string;
    base_url?: string;
  };

  auth: {
    api_uid: string; // 货源用户ID
    api_key: string; // 货源密钥
    token?: string; // 可选令牌
  };

  interfaces: {
    [interfaceType in InterfaceType]?: InterfaceConfig;
  };

  global_settings?: {
    retry_count?: number; // 重试次数
    retry_delay?: number; // 重试延迟（毫秒）
    rate_limit?: number; // 速率限制（请求/秒）
    debug_mode?: boolean; // 调试模式
  };

  created_at: string; // 创建时间
  updated_at: string; // 更新时间
  created_by?: string; // 创建者
}

/**
 * 配置模板
 */
export interface ConfigTemplate {
  template_id: string;
  template_name: string;
  template_description: string;
  provider_type: string; // 货源类型（如：29platform）
  config: Omit<ProviderUnifiedConfig, 'provider_info' | 'auth' | 'created_at' | 'updated_at'>;
  tags?: string[]; // 标签
  is_official?: boolean; // 是否官方模板
}

/**
 * 29平台配置模板示例
 */
export const TEMPLATE_29_PLATFORM: ConfigTemplate = {
  template_id: '29platform_v1',
  template_name: '29平台标准配置',
  template_description: '29平台的标准接口配置模板',
  provider_type: '29platform',
  config: {
    version: '1.0.0',
    interfaces: {
      [InterfaceType.QUERY]: {
        enabled: true,
        http: {
          method: 'POST',
          endpoint: '/api.php?act=get',
          content_type: 'form'
        },
        request_mapping: {
          api_uid: { provider_field: 'uid', required: true },
          api_key: { provider_field: 'key', required: true },
          username: { provider_field: 'user', required: true },
          password: { provider_field: 'pass', required: true },
          school: { provider_field: 'school', default_value: '自动识别' },
          platform: { provider_field: 'platform', required: true }
        },
        response_mapping: {
          success_condition: { field: 'code', condition: 'gte', value: 0 },
          message_field: 'msg',
          data_field: 'data',
          field_mapping: {
            course_id: 'kcid',
            course_name: 'kcname',
            teacher: 'teacher',
            credit: 'credit'
          }
        }
      },

      [InterfaceType.ORDER]: {
        enabled: true,
        http: {
          method: 'POST',
          endpoint: '/api.php?act=add',
          content_type: 'form'
        },
        request_mapping: {
          api_uid: { provider_field: 'uid', required: true },
          api_key: { provider_field: 'key', required: true },
          username: { provider_field: 'user', required: true },
          password: { provider_field: 'pass', required: true },
          school: { provider_field: 'school', default_value: '自动识别' },
          platform: { provider_field: 'platform', required: true },
          course_id: { provider_field: 'kcid', required: true },
          course_name: { provider_field: 'kcname', required: true }
        },
        response_mapping: {
          success_condition: { field: 'code', condition: 'gte', value: 0 },
          message_field: 'msg',
          field_mapping: {
            upstream_order_id: 'id'
          }
        }
      },

      [InterfaceType.SYNC]: {
        enabled: true,
        http: {
          method: 'POST',
          endpoint: '/api.php?act=chadanoid',
          content_type: 'form'
        },
        request_mapping: {
          api_uid: { provider_field: 'uid', required: true },
          api_key: { provider_field: 'key', required: true },
          upstream_order_id: { provider_field: 'yid', required: true }
        },
        response_mapping: {
          success_condition: { field: 'code', condition: 'eq', value: 1 },
          message_field: 'msg',
          data_field: 'data',
          field_mapping: {
            upstream_order_id: 'id',
            status: 'status',
            progress: 'process',
            remarks: 'remarks',
            course_name: 'kcname',
            username: 'user',
            school: 'school',
            platform_name: 'ptname',
            add_time: 'addtime',
            course_start_time: 'courseStartTime',
            course_end_time: 'courseEndTime',
            exam_start_time: 'examStartTime',
            exam_end_time: 'examEndTime'
          }
        }
      },

      [InterfaceType.GET_BALANCE]: {
        enabled: true,
        http: {
          method: 'POST',
          endpoint: '/api.php?act=getmoney',
          content_type: 'form'
        },
        request_mapping: {
          api_uid: { provider_field: 'uid', required: true },
          api_key: { provider_field: 'key', required: true }
        },
        response_mapping: {
          success_condition: { field: 'code', condition: 'eq', value: 1 },
          message_field: 'msg',
          field_mapping: {
            balance: { provider_field: 'money', transform: 'toNumber' },
            user_name: 'name'
          }
        }
      },

      [InterfaceType.REFILL]: {
        enabled: true,
        http: {
          method: 'POST',
          endpoint: '/api.php?act=refill',
          content_type: 'form'
        },
        request_mapping: {
          api_uid: { provider_field: 'uid', required: true },
          api_key: { provider_field: 'key', required: true },
          upstream_order_id: { provider_field: 'dingdanhao', required: true }
        },
        response_mapping: {
          success_condition: { field: 'code', condition: 'gte', value: 0 },
          message_field: 'msg',
          field_mapping: {}
        }
      },

      [InterfaceType.CHANGE_PASSWORD]: {
        enabled: true,
        http: {
          method: 'POST',
          endpoint: '/api.php?act=changepass',
          content_type: 'form'
        },
        request_mapping: {
          api_uid: { provider_field: 'uid', required: true },
          api_key: { provider_field: 'key', required: true },
          upstream_order_id: { provider_field: 'dingdanhao', required: true },
          new_password: { provider_field: 'newpass', required: true }
        },
        response_mapping: {
          success_condition: { field: 'code', condition: 'gte', value: 0 },
          message_field: 'msg',
          field_mapping: {}
        }
      }
    },

    global_settings: {
      retry_count: 3,
      retry_delay: 1000,
      rate_limit: 10,
      debug_mode: false
    }
  },
  tags: ['29平台', '学习通', '官方'],
  is_official: true
};

/**
 * 配置验证结果
 */
export interface ConfigValidationResult {
  valid: boolean;
  errors: Array<{
    path: string;
    message: string;
    code: string;
  }>;
  warnings: Array<{
    path: string;
    message: string;
    code: string;
  }>;
}

/**
 * 配置工具类
 */
export class UnifiedConfigUtils {
  /**
   * 验证配置完整性
   */
  static validateConfig(config: ProviderUnifiedConfig): ConfigValidationResult {
    const errors: Array<{ path: string; message: string; code: string }> = [];
    const warnings: Array<{ path: string; message: string; code: string }> = [];

    // 验证基本信息
    if (!config.provider_info?.provider_id) {
      errors.push({ path: 'provider_info.provider_id', message: '货源ID不能为空', code: 'REQUIRED_FIELD' });
    }

    if (!config.auth?.api_uid) {
      errors.push({ path: 'auth.api_uid', message: '货源用户ID不能为空', code: 'REQUIRED_FIELD' });
    }

    if (!config.auth?.api_key) {
      errors.push({ path: 'auth.api_key', message: '货源密钥不能为空', code: 'REQUIRED_FIELD' });
    }

    // 验证接口配置
    if (!config.interfaces || Object.keys(config.interfaces).length === 0) {
      warnings.push({ path: 'interfaces', message: '没有配置任何接口', code: 'NO_INTERFACES' });
    } else {
      for (const [interfaceType, interfaceConfig] of Object.entries(config.interfaces)) {
        if (!interfaceConfig?.http?.endpoint) {
          errors.push({
            path: `interfaces.${interfaceType}.http.endpoint`,
            message: '接口端点不能为空',
            code: 'REQUIRED_FIELD'
          });
        }

        if (!interfaceConfig?.request_mapping) {
          warnings.push({
            path: `interfaces.${interfaceType}.request_mapping`,
            message: '没有配置请求映射',
            code: 'NO_REQUEST_MAPPING'
          });
        }

        if (!interfaceConfig?.response_mapping) {
          warnings.push({
            path: `interfaces.${interfaceType}.response_mapping`,
            message: '没有配置响应映射',
            code: 'NO_RESPONSE_MAPPING'
          });
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 合并配置
   */
  static mergeConfigs(
    baseConfig: ProviderUnifiedConfig,
    overrideConfig: Partial<ProviderUnifiedConfig>
  ): ProviderUnifiedConfig {
    return {
      ...baseConfig,
      ...overrideConfig,
      provider_info: { ...baseConfig.provider_info, ...overrideConfig.provider_info },
      auth: { ...baseConfig.auth, ...overrideConfig.auth },
      interfaces: { ...baseConfig.interfaces, ...overrideConfig.interfaces },
      global_settings: { ...baseConfig.global_settings, ...overrideConfig.global_settings },
      updated_at: new Date().toISOString()
    };
  }

  /**
   * 从模板创建配置
   */
  static createFromTemplate(
    template: ConfigTemplate,
    providerInfo: { provider_id: number; provider_name: string; provider_code: string },
    auth: { api_uid: string; api_key: string; token?: string }
  ): ProviderUnifiedConfig {
    const now = new Date().toISOString();

    return {
      ...template.config,
      provider_info: providerInfo,
      auth,
      created_at: now,
      updated_at: now
    };
  }
}
