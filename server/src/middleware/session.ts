import type { NextFunction, Request, Response } from 'express';
import ConfigManager from '../utils/configManager';
import { ResponseCode } from './response';

// 创建错误响应的辅助函数
function createErrorResponse(message: string, code: ResponseCode) {
  return {
    code,
    msg: message,
    data: null
  };
}

/** 用户会话信息 */
interface UserSession {
  userId: number;
  username: string;
  lastActivity: Date;
  loginTime: Date;
  ipAddress: string;
  userAgent: string;
}

/** 会话管理器 */
export class SessionManager {
  private static sessions = new Map<number, UserSession>();

  /** 创建用户会话 */
  public static createSession(userId: number, username: string, ipAddress: string, userAgent: string): void {
    const now = new Date();

    const session: UserSession = {
      userId,
      username,
      lastActivity: now,
      loginTime: now,
      ipAddress,
      userAgent
    };

    this.sessions.set(userId, session);

    console.log(`🔐 [会话] 创建用户会话: ${username} (${ipAddress})`);
    console.log(`🔐 [会话] 当前活跃会话数: ${this.sessions.size}`);
    console.log(`🔐 [会话] 会话详情:`, {
      userId,
      username,
      ipAddress,
      userAgent: `${userAgent.substring(0, 50)}...`,
      loginTime: now.toISOString()
    });
  }

  /** 更新会话活动时间 */
  public static updateActivity(userId: number): void {
    const session = this.sessions.get(userId);
    if (session) {
      session.lastActivity = new Date();
    }
  }

  /** 检查会话是否有效 */
  public static async isSessionValid(userId: number): Promise<{
    isValid: boolean;
    reason?: string;
    remainingTime?: number;
  }> {
    try {
      console.log(`🔐 [会话验证] 检查用户 ${userId} 的会话，当前活跃会话数: ${this.sessions.size}`);

      const session = this.sessions.get(userId);
      if (!session) {
        console.log(`🔐 [会话验证] 用户 ${userId} 的会话不存在`);
        console.log(`🔐 [会话验证] 当前所有会话:`, Array.from(this.sessions.keys()));
        return {
          isValid: false,
          reason: '会话不存在'
        };
      }

      // 获取配置的会话超时时间
      const sessionTimeout = await ConfigManager.getSessionTimeout();
      const timeoutMs = sessionTimeout * 60 * 1000; // 转换为毫秒

      const now = new Date();
      const timeSinceLastActivity = now.getTime() - session.lastActivity.getTime();

      if (timeSinceLastActivity > timeoutMs) {
        // 会话已超时，删除会话
        this.sessions.delete(userId);
        console.log(`🔐 [会话] 用户会话超时: ${session.username}`);

        return {
          isValid: false,
          reason: '会话已超时，请重新登录'
        };
      }

      // 计算剩余时间
      const remainingTime = Math.ceil((timeoutMs - timeSinceLastActivity) / 1000);

      return {
        isValid: true,
        remainingTime
      };
    } catch (error) {
      console.error('🔐 [会话] 检查会话有效性失败:', error);
      return {
        isValid: false,
        reason: '会话验证失败'
      };
    }
  }

  /** 销毁用户会话 */
  public static destroySession(userId: number): boolean {
    const session = this.sessions.get(userId);
    if (session) {
      this.sessions.delete(userId);
      console.log(`🔐 [会话] 销毁用户会话: ${session.username}`);
      return true;
    }
    return false;
  }

  /** 获取用户会话信息 */
  public static getSession(userId: number): UserSession | null {
    return this.sessions.get(userId) || null;
  }

  /** 获取所有活跃会话 */
  public static getActiveSessions(): UserSession[] {
    return Array.from(this.sessions.values());
  }

  /** 强制下线用户 */
  public static forceLogout(userId: number): boolean {
    return this.destroySession(userId);
  }

  /** 清理过期会话 */
  public static async cleanupExpiredSessions(): Promise<void> {
    try {
      const sessionTimeout = await ConfigManager.getSessionTimeout();
      const timeoutMs = sessionTimeout * 60 * 1000;
      const now = new Date();

      let cleanedCount = 0;

      for (const [userId, session] of this.sessions.entries()) {
        const timeSinceLastActivity = now.getTime() - session.lastActivity.getTime();

        if (timeSinceLastActivity > timeoutMs) {
          this.sessions.delete(userId);
          cleanedCount++;
          console.log(`🔐 [会话] 清理过期会话: ${session.username}`);
        }
      }

      if (cleanedCount > 0) {
        console.log(`🔐 [会话] 清理了 ${cleanedCount} 个过期会话`);
      }
    } catch (error) {
      console.error('🔐 [会话] 清理过期会话失败:', error);
    }
  }

  /** 获取会话统计信息 */
  public static getSessionStats(): {
    totalSessions: number;
    activeSessions: number;
    sessionsByHour: { [hour: string]: number };
  } {
    const now = new Date();
    const sessions = Array.from(this.sessions.values());

    // 按小时统计会话
    const sessionsByHour: { [hour: string]: number } = {};

    sessions.forEach(session => {
      const hour = session.loginTime.getHours().toString().padStart(2, '0');
      sessionsByHour[hour] = (sessionsByHour[hour] || 0) + 1;
    });

    return {
      totalSessions: sessions.length,
      activeSessions: sessions.length, // 当前所有会话都是活跃的
      sessionsByHour
    };
  }
}

/** 会话验证中间件 */
export async function validateSession(req: Request, res: Response, next: NextFunction) {
  try {
    const user = (req as any).user;

    if (!user || !user.userId) {
      // 没有用户信息，跳过会话验证
      return next();
    }

    // 检查会话是否有效
    const sessionCheck = await SessionManager.isSessionValid(user.userId);

    if (!sessionCheck.isValid) {
      console.log(`🔐 [会话] 会话无效: ${user.username} - ${sessionCheck.reason}`);

      return res.json(createErrorResponse(sessionCheck.reason || '会话已失效，请重新登录', ResponseCode.TOKEN_INVALID));
    }

    // 更新会话活动时间
    SessionManager.updateActivity(user.userId);

    // 将剩余时间添加到响应头
    if (sessionCheck.remainingTime) {
      res.setHeader('X-Session-Remaining', sessionCheck.remainingTime.toString());
    }

    next();
  } catch (error) {
    console.error('🔐 [会话] 会话验证中间件失败:', error);
    // 出错时不阻塞请求，但记录错误
    next();
  }
}

/** 获取客户端IP地址 */
export function getClientIP(req: Request): string {
  return (
    (req.headers['x-forwarded-for'] as string) ||
    (req.headers['x-real-ip'] as string) ||
    req.connection.remoteAddress ||
    req.socket.remoteAddress ||
    'unknown'
  )
    .split(',')[0]
    .trim();
}

/** 获取用户代理信息 */
export function getUserAgent(req: Request): string {
  return req.headers['user-agent'] || 'unknown';
}

// 定期清理过期会话（每10分钟执行一次）
setInterval(
  () => {
    SessionManager.cleanupExpiredSessions();
  },
  10 * 60 * 1000
);

export default SessionManager;
