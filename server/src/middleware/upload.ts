import path from 'node:path';
import fs from 'node:fs';
import type { NextFunction, Request, Response } from 'express';
import multer from 'multer';
import ConfigManager from '../utils/configManager';
import { ResponseCode } from './response';

// 创建错误响应的辅助函数
function createErrorResponse(message: string, code: ResponseCode) {
  return {
    code,
    msg: message,
    data: null
  };
}

/** 文件上传配置 */
interface UploadConfig {
  maxSize: number; // MB
  allowedTypes: string[];
  uploadDir: string;
}

/** 默认上传配置 */
const DEFAULT_UPLOAD_CONFIG: UploadConfig = {
  maxSize: 10,
  allowedTypes: [
    // 图片
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    // 文档
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-powerpoint',
    'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    // 文本
    'text/plain',
    'text/csv',
    // 压缩包
    'application/zip',
    'application/x-rar-compressed',
    'application/x-7z-compressed'
  ],
  uploadDir: path.join(__dirname, '../../uploads')
};

/** 确保上传目录存在 */
function ensureUploadDir(uploadDir: string): void {
  if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
    console.log(`📁 [上传] 创建上传目录: ${uploadDir}`);
  }
}

/** 创建动态上传中间件 */
export function createUploadMiddleware(options: Partial<UploadConfig> = {}) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      // 获取配置的最大上传大小
      const maxUploadSize = await ConfigManager.getMaxUploadSize();

      // 合并配置
      const config: UploadConfig = {
        ...DEFAULT_UPLOAD_CONFIG,
        ...options,
        maxSize: maxUploadSize // 使用系统配置的大小
      };

      // 确保上传目录存在
      ensureUploadDir(config.uploadDir);

      // 配置multer存储
      const storage = multer.diskStorage({
        destination: (req, file, cb) => {
          // 根据文件类型创建子目录
          let subDir = 'others';
          if (file.mimetype.startsWith('image/')) {
            subDir = 'images';
          } else if (file.mimetype.includes('pdf') || file.mimetype.includes('document')) {
            subDir = 'documents';
          } else if (file.mimetype.includes('zip') || file.mimetype.includes('rar')) {
            subDir = 'archives';
          }

          const fullPath = path.join(config.uploadDir, subDir);
          ensureUploadDir(fullPath);
          cb(null, fullPath);
        },
        filename: (req, file, cb) => {
          // 生成唯一文件名
          const timestamp = Date.now();
          const randomStr = Math.random().toString(36).substring(2, 8);
          const ext = path.extname(file.originalname);
          const filename = `${timestamp}_${randomStr}${ext}`;
          cb(null, filename);
        }
      });

      // 文件过滤器
      const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
        // 检查文件类型
        if (!config.allowedTypes.includes(file.mimetype)) {
          const error = new Error(`不支持的文件类型: ${file.mimetype}`);
          (error as any).code = 'UNSUPPORTED_FILE_TYPE';
          return cb(error);
        }

        cb(null, true);
      };

      // 创建multer实例
      const upload = multer({
        storage,
        fileFilter,
        limits: {
          fileSize: config.maxSize * 1024 * 1024, // 转换为字节
          files: 10 // 最多10个文件
        }
      });

      // 根据上传类型选择处理方式
      const uploadType = (req.query.type as string) || 'single';

      let uploadHandler: any;
      switch (uploadType) {
        case 'multiple':
          uploadHandler = upload.array('files', 10);
          break;
        case 'fields':
          uploadHandler = upload.fields([
            { name: 'avatar', maxCount: 1 },
            { name: 'documents', maxCount: 5 }
          ]);
          break;
        default:
          uploadHandler = upload.single('file');
      }

      // 执行上传
      uploadHandler(req, res, (error: any) => {
        if (error) {
          console.error('📁 [上传] 文件上传失败:', error);

          let errorMessage = '文件上传失败';
          let errorCode = ResponseCode.ERROR;

          if (error.code === 'LIMIT_FILE_SIZE') {
            errorMessage = `文件大小超过限制 (${config.maxSize}MB)`;
            errorCode = ResponseCode.PARAM_ERROR;
          } else if (error.code === 'LIMIT_FILE_COUNT') {
            errorMessage = '上传文件数量超过限制';
            errorCode = ResponseCode.PARAM_ERROR;
          } else if (error.code === 'UNSUPPORTED_FILE_TYPE') {
            errorMessage = error.message;
            errorCode = ResponseCode.PARAM_ERROR;
          } else if (error.code === 'LIMIT_UNEXPECTED_FILE') {
            errorMessage = '意外的文件字段';
            errorCode = ResponseCode.PARAM_ERROR;
          }

          return res.json(createErrorResponse(errorMessage, errorCode));
        }

        // 记录上传信息
        const files = req.files;
        const file = req.file;

        if (file) {
          console.log(`📁 [上传] 单文件上传成功: ${file.filename} (${(file.size / 1024).toFixed(2)}KB)`);
        } else if (files && Array.isArray(files)) {
          console.log(`📁 [上传] 多文件上传成功: ${files.length} 个文件`);
        } else if (files && typeof files === 'object') {
          const fileCount = Object.values(files).flat().length;
          console.log(`📁 [上传] 字段文件上传成功: ${fileCount} 个文件`);
        }

        next();
      });
    } catch (error) {
      console.error('📁 [上传] 创建上传中间件失败:', error);
      res.json(createErrorResponse('上传服务初始化失败', ResponseCode.ERROR));
    }
  };
}

/** 单文件上传中间件 */
export const uploadSingle = createUploadMiddleware();

/** 多文件上传中间件 */
export const uploadMultiple = createUploadMiddleware();

/** 图片上传中间件 */
export const uploadImage = createUploadMiddleware({
  allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  uploadDir: path.join(__dirname, '../../uploads/images')
});

/** 文档上传中间件 */
export const uploadDocument = createUploadMiddleware({
  allowedTypes: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'text/plain',
    'text/csv'
  ],
  uploadDir: path.join(__dirname, '../../uploads/documents')
});

/** 获取文件信息的辅助函数 */
export function getFileInfo(file: Express.Multer.File) {
  return {
    originalName: file.originalname,
    filename: file.filename,
    mimetype: file.mimetype,
    size: file.size,
    path: file.path,
    url: `/uploads/${path.basename(path.dirname(file.path))}/${file.filename}`
  };
}

/** 删除文件的辅助函数 */
export function deleteFile(filePath: string): Promise<void> {
  return new Promise((resolve, reject) => {
    fs.unlink(filePath, error => {
      if (error && error.code !== 'ENOENT') {
        console.error('📁 [上传] 删除文件失败:', error);
        reject(error);
      } else {
        console.log(`📁 [上传] 文件已删除: ${filePath}`);
        resolve();
      }
    });
  });
}
