import os from 'node:os';
import process from 'node:process';
import type { NextFunction, Request, Response } from 'express';

// 性能指标接口
interface PerformanceMetrics {
  timestamp: number;
  requestId: string;
  method: string;
  url: string;
  statusCode: number;
  responseTime: number;
  memoryUsage: NodeJS.MemoryUsage;
  cpuUsage: NodeJS.CpuUsage;
  userAgent?: string;
  ip: string;
}

// 性能统计
interface PerformanceStats {
  totalRequests: number;
  averageResponseTime: number;
  requestsPerSecond: number;
  errorRate: number;
  memoryUsage: {
    rss: number;
    heapUsed: number;
    heapTotal: number;
    external: number;
  };
  cpuUsage: {
    user: number;
    system: number;
  };
  uptime: number;
  loadAverage: number[];
}

/** 性能监控管理器 */
export class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private maxMetrics = 10000; // 最大保存的指标数量
  private startTime = Date.now();
  private requestCount = 0;
  private errorCount = 0;
  private totalResponseTime = 0;
  private lastCleanup = Date.now();
  private cleanupInterval = 5 * 60 * 1000; // 5分钟清理一次

  /** 记录性能指标 */
  recordMetric(metric: PerformanceMetrics): void {
    this.metrics.push(metric);
    this.requestCount++;
    this.totalResponseTime += metric.responseTime;

    if (metric.statusCode >= 400) {
      this.errorCount++;
    }

    // 定期清理旧数据
    if (Date.now() - this.lastCleanup > this.cleanupInterval) {
      this.cleanup();
    }

    // 限制内存使用
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics / 2);
    }
  }

  /** 获取性能统计 */
  getStats(): PerformanceStats {
    const now = Date.now();
    const uptime = (now - this.startTime) / 1000;
    const requestsPerSecond = this.requestCount / uptime;
    const errorRate = this.requestCount > 0 ? (this.errorCount / this.requestCount) * 100 : 0;
    const averageResponseTime = this.requestCount > 0 ? this.totalResponseTime / this.requestCount : 0;

    return {
      totalRequests: this.requestCount,
      averageResponseTime,
      requestsPerSecond,
      errorRate,
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      uptime,
      loadAverage: os.loadavg()
    };
  }

  /** 获取最近的指标 */
  getRecentMetrics(limit: number = 100): PerformanceMetrics[] {
    return this.metrics.slice(-limit);
  }

  /** 获取慢请求 */
  getSlowRequests(threshold: number = 1000): PerformanceMetrics[] {
    return this.metrics.filter(metric => metric.responseTime > threshold);
  }

  /** 获取错误请求 */
  getErrorRequests(): PerformanceMetrics[] {
    return this.metrics.filter(metric => metric.statusCode >= 400);
  }

  /** 清理旧数据 */
  private cleanup(): void {
    const oneHourAgo = Date.now() - 60 * 60 * 1000;
    this.metrics = this.metrics.filter(metric => metric.timestamp > oneHourAgo);
    this.lastCleanup = Date.now();
  }

  /** 重置统计 */
  reset(): void {
    this.metrics = [];
    this.requestCount = 0;
    this.errorCount = 0;
    this.totalResponseTime = 0;
    this.startTime = Date.now();
  }

  /** 检查性能告警 */
  checkAlerts(): string[] {
    const stats = this.getStats();
    const alerts: string[] = [];

    // 内存使用率告警
    const memoryUsagePercent = (stats.memoryUsage.heapUsed / stats.memoryUsage.heapTotal) * 100;
    if (memoryUsagePercent > 80) {
      alerts.push(`内存使用率过高: ${memoryUsagePercent.toFixed(2)}%`);
    }

    // 响应时间告警
    if (stats.averageResponseTime > 2000) {
      alerts.push(`平均响应时间过长: ${stats.averageResponseTime.toFixed(2)}ms`);
    }

    // 错误率告警
    if (stats.errorRate > 5) {
      alerts.push(`错误率过高: ${stats.errorRate.toFixed(2)}%`);
    }

    // CPU负载告警
    const loadAverage = stats.loadAverage[0];
    const cpuCount = os.cpus().length;
    if (loadAverage > cpuCount * 0.8) {
      alerts.push(`CPU负载过高: ${loadAverage.toFixed(2)}`);
    }

    return alerts;
  }
}

// 全局性能监控实例
export const performanceMonitor = new PerformanceMonitor();

/** 性能监控中间件 */
export function performanceMiddleware(req: Request, res: Response, next: NextFunction): void {
  const startTime = process.hrtime.bigint();
  const startCpuUsage = process.cpuUsage();
  const requestId = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  // 添加请求ID到请求对象
  (req as any).requestId = requestId;

  // 监听响应结束事件
  res.on('finish', () => {
    const endTime = process.hrtime.bigint();
    const responseTime = Number(endTime - startTime) / 1000000; // 转换为毫秒
    const endCpuUsage = process.cpuUsage(startCpuUsage);

    const metric: PerformanceMetrics = {
      timestamp: Date.now(),
      requestId,
      method: req.method,
      url: req.originalUrl || req.url,
      statusCode: res.statusCode,
      responseTime,
      memoryUsage: process.memoryUsage(),
      cpuUsage: endCpuUsage,
      userAgent: req.headers['user-agent'],
      ip: req.ip || req.connection.remoteAddress || 'unknown'
    };

    performanceMonitor.recordMetric(metric);

    // 记录慢请求
    if (responseTime > 1000) {
      console.warn(`🐌 慢请求检测: ${req.method} ${req.url} - ${responseTime.toFixed(2)}ms`);
    }

    // 记录错误请求
    if (res.statusCode >= 400) {
      console.error(`❌ 错误请求: ${req.method} ${req.url} - ${res.statusCode}`);
    }
  });

  next();
}

/** 性能报告中间件 */
export function performanceReportMiddleware(req: Request, res: Response, next: NextFunction): void {
  // 只处理性能报告相关的路由
  if (!req.path.startsWith('/api/performance')) {
    return next();
  }

  const action = req.path.split('/').pop();

  switch (action) {
    case 'stats':
      res.json({
        code: '0000',
        msg: '获取性能统计成功',
        data: performanceMonitor.getStats()
      });
      break;

    case 'metrics':
      const limit = Number.parseInt(req.query.limit as string) || 100;
      res.json({
        code: '0000',
        msg: '获取性能指标成功',
        data: performanceMonitor.getRecentMetrics(limit)
      });
      break;

    case 'slow-requests':
      const threshold = Number.parseInt(req.query.threshold as string) || 1000;
      res.json({
        code: '0000',
        msg: '获取慢请求成功',
        data: performanceMonitor.getSlowRequests(threshold)
      });
      break;

    case 'error-requests':
      res.json({
        code: '0000',
        msg: '获取错误请求成功',
        data: performanceMonitor.getErrorRequests()
      });
      break;

    case 'alerts':
      res.json({
        code: '0000',
        msg: '获取性能告警成功',
        data: performanceMonitor.checkAlerts()
      });
      break;

    case 'reset':
      if (req.method === 'POST') {
        performanceMonitor.reset();
        res.json({
          code: '0000',
          msg: '重置性能统计成功',
          data: null
        });
      } else {
        next();
      }
      break;

    default:
      next();
  }
}

/** 系统健康检查 */
export function healthCheck(): {
  status: 'healthy' | 'warning' | 'critical';
  checks: Record<string, any>;
} {
  const stats = performanceMonitor.getStats();
  const alerts = performanceMonitor.checkAlerts();

  let status: 'healthy' | 'warning' | 'critical' = 'healthy';

  if (alerts.length > 0) {
    status = alerts.length > 2 ? 'critical' : 'warning';
  }

  return {
    status,
    checks: {
      uptime: stats.uptime,
      memoryUsage: stats.memoryUsage,
      cpuUsage: stats.cpuUsage,
      loadAverage: stats.loadAverage,
      requestsPerSecond: stats.requestsPerSecond,
      errorRate: stats.errorRate,
      averageResponseTime: stats.averageResponseTime,
      alerts
    }
  };
}
