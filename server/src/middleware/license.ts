import type { NextFunction, Request, Response } from 'express';
import { licenseManager } from '../utils/licenseManager';
import { formatDateTimeForLog } from '../utils/timeUtils';

/** 全局授权验证中间件 检查系统是否已激活，允许登录和授权相关接口在未授权状态下访问 */
export function verifySystemLicense(req: Request, res: Response, next: NextFunction) {
  // 定义允许在未授权状态下访问的接口
  const allowedPaths = [
    '/api/auth/login', // 登录接口
    '/api/auth/getUserInfo', // 获取用户信息
    '/api/auth/refreshToken', // 刷新token
    '/api/license/' // 所有授权相关接口
  ];

  // 检查当前请求是否在允许列表中
  const isAllowed = allowedPaths.some(path => req.path.startsWith(path));

  if (isAllowed) {
    console.log(`${formatDateTimeForLog()} 🔒 [授权中间件] 允许未授权访问:`, req.method, req.path);
    return next();
  }

  // 检查系统是否已激活
  if (!licenseManager.isActive()) {
    console.log(`${formatDateTimeForLog()} 🔒 [授权中间件] 系统未激活，拒绝API请求:`, req.method, req.path);
    return res.status(402).json({
      error: 'SYSTEM_NOT_LICENSED',
      message: '系统需要授权才能使用，请联系管理员激活',
      code: 'LICENSE_REQUIRED'
    });
  }

  // 系统已激活，继续处理请求
  next();
}

/** 授权管理接口专用中间件 允许授权相关的API在系统未激活时也能访问 */
export function allowLicenseManagement(req: Request, res: Response, next: NextFunction) {
  // 授权管理接口始终允许访问
  console.log(`${formatDateTimeForLog()} 🔒 [授权中间件] 允许授权管理接口访问:`, req.method, req.path);
  next();
}

// 保留原有的授权验证中间件（用于向后兼容）
export async function verifyLicense(req: Request, res: Response, next: NextFunction) {
  // 这个中间件现在主要用于向后兼容
  // 实际的授权检查由 verifySystemLicense 处理
  next();
}
