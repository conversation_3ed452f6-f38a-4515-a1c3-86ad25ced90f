import type { NextFunction, Request, Response } from 'express';
import ConfigManager from '../utils/configManager';
import { ResponseCode } from './response';

// 创建错误响应的辅助函数
function createErrorResponse(message: string, code: ResponseCode) {
  return {
    code,
    msg: message,
    data: null
  };
}

/** 维护模式检查中间件 */
export async function checkMaintenanceMode(req: Request, res: Response, next: NextFunction) {
  try {
    // 检查是否开启维护模式
    const isMaintenanceMode = await ConfigManager.isMaintenanceMode();

    // 如果未开启维护模式，直接放行
    if (!isMaintenanceMode) {
      return next();
    }

    console.log('🚧 [维护模式] 系统处于维护模式，检查路径:', req.path);

    // 维护模式下，始终允许的系统核心接口
    const alwaysAllowedPaths = [
      '/api/auth/login', // 登录接口
      '/api/auth/getUserInfo', // 获取用户信息
      '/api/auth/refreshToken', // 刷新令牌
      '/api/config', // 系统配置（管理员需要）
      '/api/license' // 授权验证
    ];

    // 检查是否是始终允许的路径
    const isAlwaysAllowed = alwaysAllowedPaths.some(path => req.path.startsWith(path));
    if (isAlwaysAllowed) {
      console.log('🚧 [维护模式] 允许系统核心接口:', req.path);
      return next();
    }

    // 对于其他接口，需要检查用户是否是管理员
    // 注意：这里的user信息来自JWT认证中间件
    const user = (req as any).user;

    if (user && user.roles) {
      const isAdmin = user.roles.includes('admin') || user.roles.includes('super-admin');
      if (isAdmin) {
        console.log('🚧 [维护模式] 管理员用户，允许访问:', req.path, '用户:', user.userName);
        return next();
      }
    }

    // 普通用户或未认证用户，阻止访问
    console.log('🚧 [维护模式] 阻止普通用户访问:', req.path);
    return res.json(createErrorResponse('系统正在维护中，请稍后再试', ResponseCode.SYSTEM_ERROR));
  } catch (error) {
    console.error('🚧 [维护模式] 检查失败:', error);
    // 出错时允许访问，避免系统完全不可用
    next();
  }
}

/** 维护模式检查中间件（用于已认证的路由） */
export async function checkMaintenanceModeForAuthenticated(req: Request, res: Response, next: NextFunction) {
  try {
    // 检查是否开启维护模式
    const isMaintenanceMode = await ConfigManager.isMaintenanceMode();

    // 如果未开启维护模式，直接放行
    if (!isMaintenanceMode) {
      return next();
    }

    console.log('🚧 [维护模式] 系统处于维护模式，检查已认证用户:', req.path);

    // 配置相关接口对管理员始终开放（因为管理员需要关闭维护模式）
    if (req.path.startsWith('/config')) {
      console.log('🚧 [维护模式] 配置接口，允许访问:', req.path);
      return next();
    }

    // 获取已认证的用户信息
    const user = (req as any).user;

    if (user && user.roles) {
      const isAdmin = user.roles.includes('admin') || user.roles.includes('super-admin');
      if (isAdmin) {
        console.log('🚧 [维护模式] 管理员用户，允许访问:', req.path, '用户:', user.userName);
        return next();
      }
    }

    // 普通用户，阻止访问
    console.log('🚧 [维护模式] 阻止普通用户访问:', req.path);
    return res.json(createErrorResponse('系统正在维护中，管理员正在处理，请稍后再试', ResponseCode.SYSTEM_ERROR));
  } catch (error) {
    console.error('🚧 [维护模式] 检查失败:', error);
    // 出错时允许访问，避免系统完全不可用
    next();
  }
}

/** 检查功能开关的中间件工厂 */
export function checkFeatureEnabled(featureName: string, errorMessage: string) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      let isEnabled = false;

      switch (featureName) {
        case 'registration':
          isEnabled = await ConfigManager.isRegistrationEnabled();
          break;
        case 'guestAccess':
          isEnabled = await ConfigManager.isGuestAccessEnabled();
          break;
        case 'apiDocs':
          isEnabled = await ConfigManager.isApiDocsEnabled();
          break;
        default:
          console.warn('🔧 [功能开关] 未知的功能名称:', featureName);
          return next();
      }

      if (!isEnabled) {
        console.log(`🔧 [功能开关] 功能 ${featureName} 已禁用`);
        return res.json(createErrorResponse(errorMessage, ResponseCode.FORBIDDEN));
      }

      next();
    } catch (error) {
      console.error(`🔧 [功能开关] 检查功能 ${featureName} 失败:`, error);
      // 出错时允许访问
      next();
    }
  };
}

/** 检查注册功能是否启用 */
export const checkRegistrationEnabled = checkFeatureEnabled('registration', '用户注册功能已关闭');

/** 检查访客访问是否启用 */
export const checkGuestAccessEnabled = checkFeatureEnabled('guestAccess', '访客访问功能已关闭');

/** 检查API文档是否启用 */
export const checkApiDocsEnabled = checkFeatureEnabled('apiDocs', 'API文档功能已关闭');
