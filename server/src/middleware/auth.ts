import type { NextFunction, Request, Response } from 'express';
import { getTokenFromHeader, verifyToken } from '../utils/jwt';
import { formatDateTimeForLog } from '../utils/timeUtils';
import { executeQuery } from '../utils/db';
import { getUserClaims } from '../service/permissionService';
import SessionManager from './session';
import { ResponseCode } from './response';

// 创建错误响应的辅助函数
function createErrorResponse(message: string, code: ResponseCode) {
  return {
    code,
    msg: message,
    data: null
  };
}

// 扩展Request接口以包含用户信息
export interface AuthenticatedRequest extends Request {
  user?: {
    userId: number;
    userName: string;
    roles: string[];
    permissions: string[];
  };
}

/** 认证中间件 验证请求头中的令牌，并将用户信息添加到请求对象中 */
export async function authenticate(req: AuthenticatedRequest, res: Response, next: NextFunction) {
  try {
    // 获取请求头中的授权信息
    const authHeader = req.headers.authorization;

    // 从授权头中获取令牌
    const token = getTokenFromHeader(authHeader);

    if (!token) {
      return res.json(createErrorResponse('未授权，请先登录', ResponseCode.TOKEN_INVALID));
    }

    // 验证令牌
    const payload = verifyToken(token);

    if (!payload) {
      return res.json(createErrorResponse('令牌无效或已过期，请重新登录', ResponseCode.TOKEN_INVALID));
    }

    // 将用户信息添加到请求对象中（载入完整 claims）
    const claims = await getUserClaims(payload.userId);
    req.user = {
      userId: payload.userId,
      userName: payload.userName,
      roles: claims.roles,
      permissions: claims.permissions
    };

    // 验证会话（可选，不阻塞请求）
    try {
      const sessionCheck = await SessionManager.isSessionValid(payload.userId);
      if (!sessionCheck.isValid) {
        console.warn(`${formatDateTimeForLog()} 🔐 [认证] 用户会话已失效:`, payload.userName);
        // 可以选择是否要强制用户重新登录
        // return res.json(createErrorResponse('会话已失效，请重新登录', ResponseCode.TOKEN_INVALID));
      } else {
        // 更新会话活动时间
        SessionManager.updateActivity(payload.userId);
      }
    } catch (error) {
      console.error(`${formatDateTimeForLog()} 🔐 [认证] 会话验证失败:`, error);
      // 会话验证失败不阻塞请求
    }

    console.log(`${formatDateTimeForLog()} 🔐 [认证] 用户认证成功:`, {
      userId: payload.userId,
      userName: payload.userName,
      roles: payload.roles
    });

    // 继续处理请求
    next();
  } catch (error) {
    console.error(`${formatDateTimeForLog()} 🔐 [认证] 认证失败:`, error);
    return res.json(createErrorResponse('服务器内部错误', ResponseCode.ERROR));
  }
}

/** 角色检查（全部满足） */
export function requireRole(...roles: string[]) {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) return res.json(createErrorResponse('未授权，请先登录', ResponseCode.TOKEN_INVALID));
      const hasAll = roles.every(r => req.user!.roles?.includes(r));
      if (!hasAll) return res.json(createErrorResponse('权限不足', ResponseCode.FORBIDDEN));
      next();
    } catch (error) {
      return res.json(createErrorResponse('服务器内部错误', ResponseCode.ERROR));
    }
  };
}

/** 角色检查（任一满足） */
export function requireAnyRole(...roles: string[]) {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) return res.json(createErrorResponse('未授权，请先登录', ResponseCode.TOKEN_INVALID));
      const hasAny = roles.some(r => req.user!.roles?.includes(r));
      if (!hasAny) return res.json(createErrorResponse('权限不足', ResponseCode.FORBIDDEN));
      next();
    } catch (error) {
      return res.json(createErrorResponse('服务器内部错误', ResponseCode.ERROR));
    }
  };
}

/** 兼容旧接口：requireAdmin 等价于 requireRole('admin') */
export const requireAdmin = requireRole('admin');

/** 可选的认证中间件 如果有token则验证，没有token则跳过 */
export async function optionalAuthenticate(req: AuthenticatedRequest, res: Response, next: NextFunction) {
  try {
    // 获取请求头中的授权信息
    const authHeader = req.headers.authorization;

    // 从授权头中获取令牌
    const token = getTokenFromHeader(authHeader);

    if (!token) {
      // 没有token，跳过认证
      return next();
    }

    // 验证令牌
    const payload = verifyToken(token);

    if (payload) {
      // 将用户信息添加到请求对象中（载入完整 claims）
      const claims = await (await import('../service/permissionService')).getUserClaims(payload.userId);
      req.user = {
        userId: payload.userId,
        userName: payload.userName,
        roles: claims.roles,
        permissions: claims.permissions
      };
      console.log(`${formatDateTimeForLog()} 🔐 [可选认证] 用户认证成功:`, payload.userName);
    }

    // 继续处理请求（无论是否认证成功）
    next();
  } catch (error) {
    console.error(`${formatDateTimeForLog()} 🔐 [可选认证] 认证失败:`, error);
    // 可选认证失败时不阻塞请求
    next();
  }
}

/**
 * 权限验证中间件工厂函数
 * 基于RBAC权限系统验证用户是否具有指定权限
 */
export function requirePermission(permissionCode: string) {
  return async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
    try {
      // 确保用户已认证
      if (!req.user) {
        return res.json(createErrorResponse('未授权，请先登录', ResponseCode.TOKEN_INVALID));
      }

      const userId = req.user.userId;

      // 检查用户是否具有指定权限
      const hasPermission = await checkUserPermission(userId, permissionCode);

      if (!hasPermission) {
        console.log(`${formatDateTimeForLog()} ❌ [权限验证] 用户 ${req.user.userName} 缺少权限: ${permissionCode}`);
        return res.json(createErrorResponse('权限不足，无法访问该资源', ResponseCode.PERMISSION_DENIED));
      }

      console.log(`${formatDateTimeForLog()} ✅ [权限验证] 用户 ${req.user.userName} 权限验证通过: ${permissionCode}`);
      next();
    } catch (error) {
      console.error(`${formatDateTimeForLog()} ❌ [权限验证] 权限验证失败:`, error);
      return res.json(createErrorResponse('权限验证失败', ResponseCode.ERROR));
    }
  };
}

/**
 * 检查用户是否具有指定权限
 */
export async function checkUserPermission(userId: number, permissionCode: string): Promise<boolean> {
  try {
    // 精简策略：仅检查角色授权（grant），忽略用户直授/拒绝/过期/数据范围
    const rolePermissionSql = `
      SELECT 1
      FROM fd_user_role ur
      JOIN fd_role r ON ur.role_id = r.role_id
      JOIN fd_role_permission rp ON r.role_id = rp.role_id
      JOIN fd_permission p ON rp.permission_id = p.permission_id
      WHERE ur.user_id = ? AND p.permission_code = ?
      AND ur.status = 1 AND r.status = 1 AND rp.status = 1 AND p.status = 1
      AND rp.grant_type = 'grant'
      AND (ur.expire_time IS NULL OR ur.expire_time > NOW())
      LIMIT 1
    `;

    const [rolePermission] = await executeQuery(rolePermissionSql, [userId, permissionCode]);

    if (rolePermission) {
      return true;
    }

    // 3. 检查是否为超级管理员（拥有所有权限）
    const superAdminSql = `
      SELECT 1
      FROM fd_user_role ur
      JOIN fd_role r ON ur.role_id = r.role_id
      WHERE ur.user_id = ? AND r.role_code = 'super_admin'
      AND ur.status = 1 AND r.status = 1
      AND (ur.expire_time IS NULL OR ur.expire_time > NOW())
      LIMIT 1
    `;

    const [superAdmin] = await executeQuery(superAdminSql, [userId]);

    if (superAdmin) {
      console.log(`${formatDateTimeForLog()} 👑 [权限验证] 超级管理员用户 ${userId} 拥有所有权限`);
      return true;
    }

    return false;
  } catch (error) {
    console.error('❌ 检查用户权限失败:', error);
    return false;
  }
}

/**
 * 获取用户所有权限
 */
export async function getUserPermissions(userId: number): Promise<string[]> {
  try {
    // 检查是否为超级管理员
    const superAdminSql = `
      SELECT 1
      FROM fd_user_role ur
      JOIN fd_role r ON ur.role_id = r.role_id
      WHERE ur.user_id = ? AND r.role_code = 'super_admin'
      AND ur.status = 1 AND r.status = 1
      AND (ur.expire_time IS NULL OR ur.expire_time > NOW())
      LIMIT 1
    `;

    const [superAdmin] = await executeQuery(superAdminSql, [userId]);

    if (superAdmin) {
      // 超级管理员拥有所有权限
      const allPermissionsSql = `
        SELECT permission_code
        FROM fd_permission
        WHERE status = 1
        ORDER BY permission_group, sort_order
      `;
      const allPermissions = await executeQuery(allPermissionsSql);
      return (allPermissions as any[]).map(p => p.permission_code);
    }

    // 获取用户的所有权限（直接权限 + 角色权限）
    const permissionsSql = `
      SELECT DISTINCT p.permission_code
      FROM (
        -- 用户直接权限
        SELECT p.permission_code, up.grant_type, 1 as priority
        FROM fd_user_permission up
        JOIN fd_permission p ON up.permission_id = p.permission_id
        WHERE up.user_id = ? AND up.status = 1 AND p.status = 1
        AND (up.expire_time IS NULL OR up.expire_time > NOW())

        UNION ALL

        -- 角色权限
        SELECT p.permission_code, rp.grant_type, 2 as priority
        FROM fd_user_role ur
        JOIN fd_role r ON ur.role_id = r.role_id
        JOIN fd_role_permission rp ON r.role_id = rp.role_id
        JOIN fd_permission p ON rp.permission_id = p.permission_id
        WHERE ur.user_id = ? AND ur.status = 1 AND r.status = 1
        AND rp.status = 1 AND p.status = 1
        AND (ur.expire_time IS NULL OR ur.expire_time > NOW())
      ) permissions
      WHERE grant_type = 'grant'
      ORDER BY permission_code
    `;

    const permissions = await executeQuery(permissionsSql, [userId, userId]);
    return (permissions as any[]).map(p => p.permission_code);
  } catch (error) {
    console.error('❌ 获取用户权限失败:', error);
    return [];
  }
}
