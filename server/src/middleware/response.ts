import type { NextFunction, Request, Response } from 'express';
import { ResponseCode } from '../utils/response';
import { formatDateTimeForLog, getCurrentTimestamp } from '../utils/timeUtils';

// 重新导出ResponseCode以供其他模块使用
export { ResponseCode };

// 响应消息映射
export const ResponseMessage = {
  [ResponseCode.SUCCESS]: '操作成功',
  [ResponseCode.ERROR]: '服务器内部错误',
  [ResponseCode.PARAM_ERROR]: '参数错误',
  [ResponseCode.DATA_NOT_FOUND]: '数据不存在',
  [ResponseCode.DATA_EXISTS]: '数据已存在',
  [ResponseCode.AUTH_ERROR]: '认证错误',
  [ResponseCode.FORBIDDEN]: '禁止访问',
  [ResponseCode.TOKEN_INVALID]: '令牌无效',
  [ResponseCode.TOKEN_EXPIRED]: '登录过期',
  [ResponseCode.SYSTEM_ERROR]: '系统错误',
  [ResponseCode.PERMISSION_DENIED]: '权限不足',
  [ResponseCode.RATE_LIMIT]: '请求过于频繁',
  [ResponseCode.NOT_LOGGED_IN]: '未登录',
  [ResponseCode.UNAUTHORIZED]: '未授权'
};

// 标准响应接口
export interface ApiResponse<T = any> {
  code: string;
  msg: string;
  data: T | null;
  timestamp?: number;
  requestId?: string;
}

// 分页响应接口
export interface PaginationResponse<T = any> {
  records: T[];
  current: number;
  size: number;
  total: number;
}

// 扩展Response对象
declare global {
  namespace Express {
    interface Response {
      success<T>(data?: T, message?: string): void;
      error(code: ResponseCode, message?: string, data?: any): void;
      paginate<T>(records: T[], current: number, size: number, total: number): void;
    }
  }
}

/** 响应格式化中间件 为Response对象添加标准化的响应方法 */
export const responseFormatter = (req: Request, res: Response, next: NextFunction) => {
  // 生成请求ID
  const requestId =
    (req.headers['x-request-id'] as string) || `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  /** 成功响应 */
  res.success = function <T>(data?: T, message?: string) {
    const response: ApiResponse<T> = {
      code: ResponseCode.SUCCESS,
      msg: message || ResponseMessage[ResponseCode.SUCCESS],
      data: data || null,
      timestamp: getCurrentTimestamp(),
      requestId
    };

    this.status(200).json(response);
  };

  /** 错误响应 */
  res.error = function (code: ResponseCode, message?: string, data?: any) {
    const response: ApiResponse = {
      code,
      msg: message || '未知错误',
      data: data || null,
      timestamp: getCurrentTimestamp(),
      requestId
    };

    // 根据错误码设置HTTP状态码
    let httpStatus = 200; // 默认200，业务错误通过code区分

    switch (code) {
      case ResponseCode.PARAM_ERROR:
        httpStatus = 400;
        break;
      case ResponseCode.NOT_LOGGED_IN:
      case ResponseCode.TOKEN_EXPIRED:
        httpStatus = 401;
        break;
      case ResponseCode.PERMISSION_DENIED:
        httpStatus = 403;
        break;
      case ResponseCode.DATA_NOT_FOUND:
        httpStatus = 404;
        break;
      case ResponseCode.RATE_LIMIT:
        httpStatus = 429;
        break;
      case ResponseCode.SYSTEM_ERROR:
        httpStatus = 500;
        break;
    }

    this.status(httpStatus).json(response);
  };

  /** 分页响应 */
  res.paginate = function <T>(records: T[], current: number, size: number, total: number) {
    const paginationData: PaginationResponse<T> = {
      records,
      current,
      size,
      total
    };

    this.success(paginationData);
  };

  next();
};

/** 全局错误处理中间件 */
export const errorHandler = (error: any, req: Request, res: Response, next: NextFunction) => {
  console.error('Global Error Handler:', error);

  // 记录错误日志
  const errorLog = {
    timestamp: formatDateTimeForLog(),
    method: req.method,
    url: req.url,
    userAgent: req.headers['user-agent'],
    ip: req.ip,
    error: {
      message: error.message,
      stack: error.stack,
      code: error.code
    }
  };

  // 这里可以集成日志系统
  console.error('Error Log:', JSON.stringify(errorLog, null, 2));

  // 数据库错误
  if (error.code && error.code.startsWith('ER_')) {
    return res.error(ResponseCode.SYSTEM_ERROR, '数据库操作失败');
  }

  // JWT错误
  if (error.name === 'JsonWebTokenError') {
    return res.error(ResponseCode.TOKEN_EXPIRED, 'Token无效');
  }

  if (error.name === 'TokenExpiredError') {
    return res.error(ResponseCode.TOKEN_EXPIRED, 'Token已过期');
  }

  // 参数验证错误
  if (error.name === 'ValidationError') {
    return res.error(ResponseCode.PARAM_ERROR, error.message);
  }

  // 默认系统错误
  res.error(ResponseCode.SYSTEM_ERROR, '系统内部错误');
};

/** 404处理中间件 */
export const notFoundHandler = (req: Request, res: Response) => {
  res.error(ResponseCode.DATA_NOT_FOUND, `接口 ${req.method} ${req.url} 不存在`);
};

/** 请求日志中间件 */
export const requestLogger = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  const requestId =
    (req.headers['x-request-id'] as string) || `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  // 记录请求开始
  console.log(`${formatDateTimeForLog()} ${requestId} ${req.method} ${req.url} - START`);

  // 监听响应结束
  res.on('finish', () => {
    const duration = Date.now() - startTime;
    console.log(`${formatDateTimeForLog()} ${requestId} ${req.method} ${req.url} - ${res.statusCode} - ${duration}ms`);
  });

  next();
};

/** API版本验证中间件 */
export const apiVersionValidator = (supportedVersions: string[] = ['v1']) => {
  return (req: Request, res: Response, next: NextFunction) => {
    const version = (req.headers['api-version'] as string) || 'v1';

    if (!supportedVersions.includes(version)) {
      return res.error(ResponseCode.PARAM_ERROR, `不支持的API版本: ${version}`);
    }

    // 将版本信息添加到请求对象
    (req as any).apiVersion = version;
    next();
  };
};

/** 请求限流中间件 */
export const rateLimiter = (maxRequests: number = 100, windowMs: number = 60000) => {
  const requests = new Map<string, { count: number; resetTime: number }>();

  return (req: Request, res: Response, next: NextFunction) => {
    const clientId = req.ip || 'unknown';
    const now = Date.now();

    // 清理过期记录
    for (const [key, value] of requests.entries()) {
      if (now > value.resetTime) {
        requests.delete(key);
      }
    }

    // 获取或创建客户端记录
    let clientRecord = requests.get(clientId);
    if (!clientRecord || now > clientRecord.resetTime) {
      clientRecord = { count: 0, resetTime: now + windowMs };
      requests.set(clientId, clientRecord);
    }

    // 检查限流
    if (clientRecord.count >= maxRequests) {
      const retryAfter = Math.ceil((clientRecord.resetTime - now) / 1000);
      res.setHeader('Retry-After', retryAfter);
      return res.error(ResponseCode.RATE_LIMIT, '请求过于频繁，请稍后重试', { retryAfter });
    }

    // 增加计数
    clientRecord.count++;

    // 设置响应头
    res.setHeader('X-RateLimit-Limit', maxRequests);
    res.setHeader('X-RateLimit-Remaining', maxRequests - clientRecord.count);
    res.setHeader('X-RateLimit-Reset', Math.ceil(clientRecord.resetTime / 1000));

    next();
  };
};
