import type { ResultSetHeader, RowDataPacket } from 'mysql2';
import pool, { executeQuery } from '../utils/db';

export interface User extends RowDataPacket {
  user_id: number;
  username: string; // QQ账号
  password: string;
  nickname: string;
  email: string;
  phone: string | null;
  avatar: string;
  role: number; // tinyint 数值角色 (1=管理员, 2=代理商, 3=普通用户)
  user_role: string; // varchar 角色字符串 (admin/agent/user)
  status: number; // 状态 (0=禁用, 1=启用)
  invite_code: string; // 用户的邀请码
  sid?: number; // 上级用户ID
  referrer_id?: number; // 推荐人ID
  balance: number; // 账户余额
  price_rate: number; // 用户费率
  total_recharge: number; // 总充值金额
  level_id?: number; // 用户等级ID
  order_limit: number; // 订单限制
  query_limit: number; // 查询限制
  api_key?: string; // API密钥
  qq?: string; // QQ号
  wechat?: string; // 微信号
  today_stats?: any; // 今日统计 (JSON)
  notification_config?: any; // 通知配置 (JSON)
  user_meta?: any; // 用户元数据 (JSON)
  login_info?: any; // 登录信息 (JSON)
  create_time: Date; // 创建时间
  update_time: Date; // 更新时间
}

export interface UserWithRoles extends User {
  roles: string[];
  permissions: string[];
}

export interface CreateUserParams {
  username: string; // QQ账号
  password: string;
  nickname: string;
  email: string;
  phone: string | null;
  role: number; // 数值角色
  user_role: string; // 字符串角色
  status: number;
  avatar?: string;
  balance?: number; // 初始余额
  price_rate?: number; // 用户费率
  inviteCode?: string; // 使用的邀请码（注册时）
  sid?: number; // 上级用户ID
  referrer_id?: number; // 推荐人ID
  level_id?: number; // 用户等级ID
}

/** 根据用户名查找用户 */
export async function findUserByUsername(username: string): Promise<User | null> {
  try {
    const [rows] = await pool.execute<User[]>('SELECT * FROM fd_user WHERE username = ?', [username]);
    return rows.length > 0 ? rows[0] : null;
  } catch (error) {
    console.error('查询用户失败:', error);
    throw error;
  }
}

/** 根据用户ID查找用户 */
export async function findUserByUserId(userId: number): Promise<User | null> {
  try {
    const [rows] = await pool.execute<User[]>('SELECT * FROM fd_user WHERE user_id = ?', [userId]);
    return rows.length > 0 ? rows[0] : null;
  } catch (error) {
    console.error('查询用户失败:', error);
    throw error;
  }
}

/** 根据邀请码查找用户 */
export async function findUserByInviteCode(inviteCode: string): Promise<User | null> {
  try {
    const [rows] = await pool.execute<User[]>('SELECT * FROM fd_user WHERE invite_code = ?', [inviteCode]);
    return rows.length > 0 ? rows[0] : null;
  } catch (error) {
    console.error('根据邀请码查询用户失败:', error);
    throw error;
  }
}

/** 生成唯一邀请码 */
export async function generateUniqueInviteCode(): Promise<string> {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let attempts = 0;
  const maxAttempts = 10;

  while (attempts < maxAttempts) {
    // 生成8位邀请码
    let code = '';
    for (let i = 0; i < 8; i++) {
      code += chars.charAt(Math.floor(Math.random() * chars.length));
    }

    // 检查是否已存在
    const existingUser = await findUserByInviteCode(code);
    if (!existingUser) {
      return code;
    }

    attempts++;
  }

  throw new Error('生成唯一邀请码失败，请重试');
}

/** 创建用户 */
export async function createUser(params: CreateUserParams): Promise<User> {
  try {
    const { username, password, nickname, email, phone, role, status, avatar = '', inviteCode, sid } = params;

    // 生成用户的邀请码
    const userInviteCode = await generateUniqueInviteCode();

    const [result] = await pool.execute<ResultSetHeader>(
      'INSERT INTO fd_user (username, password, nickname, email, phone, role, status, avatar, invite_code, sid) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
      [username, password, nickname, email, phone, role, status, avatar, userInviteCode, sid || null]
    );

    const userId = result.insertId;

    // 查询并返回创建的用户
    const [rows] = await pool.execute<User[]>('SELECT * FROM fd_user WHERE user_id = ?', [userId]);

    if (rows.length === 0) {
      throw new Error('创建用户后未能查询到用户信息');
    }

    console.log(`✅ 用户创建成功: ${username}, 邀请码: ${userInviteCode}${sid ? `, 上级ID: ${sid}` : ''}`);
    return rows[0];
  } catch (error) {
    console.error('创建用户失败:', error);
    throw error;
  }
}

/** 更新用户密码 */
export async function updateUserPassword(userId: number, newPassword: string): Promise<boolean> {
  try {
    const [result] = await pool.execute<ResultSetHeader>('UPDATE fd_user SET password = ? WHERE user_id = ?', [
      newPassword,
      userId
    ]);

    return result.affectedRows > 0;
  } catch (error) {
    console.error('更新密码失败:', error);
    throw error;
  }
}

/** 获取用户角色 */
export async function getUserRoles(userId: number): Promise<string[]> {
  try {
    // 这里简化处理，实际应该查询用户角色关联表
    // 假设用户角色存储在用户表的role字段中
    const [rows] = await pool.execute<RowDataPacket[]>('SELECT role FROM fd_user WHERE user_id = ?', [userId]);

    if (rows.length === 0) return [];

    // 根据角色ID获取角色代码
    // 这里简化处理，实际应该关联角色表
    const roleId = rows[0].role;
    if (roleId === 1) return ['admin'];
    if (roleId === 2) return ['agent'];
    return ['user'];
  } catch (error) {
    console.error('获取用户角色失败:', error);
    throw error;
  }
}

/** 获取用户权限 */
export async function getUserPermissions(userId: number): Promise<string[]> {
  try {
    // 1. 获取用户直接权限
    const directPermissionsSql = `
      SELECT DISTINCT p.permission_code
      FROM fd_user_permission up
      JOIN fd_permission p ON up.permission_id = p.permission_id
      WHERE up.user_id = ? AND up.status = 1 AND p.status = 1
      AND up.grant_type = 'grant'
      AND (up.expire_time IS NULL OR up.expire_time > NOW())
    `;

    const directPermissions = await executeQuery(directPermissionsSql, [userId]);

    // 2. 获取用户角色权限
    const rolePermissionsSql = `
      SELECT DISTINCT p.permission_code
      FROM fd_user_role ur
      JOIN fd_role r ON ur.role_id = r.role_id
      JOIN fd_role_permission rp ON r.role_id = rp.role_id
      JOIN fd_permission p ON rp.permission_id = p.permission_id
      WHERE ur.user_id = ? AND ur.status = 1 AND r.status = 1
      AND rp.status = 1 AND p.status = 1
      AND rp.grant_type = 'grant'
      AND (ur.expire_time IS NULL OR ur.expire_time > NOW())
    `;

    const rolePermissions = await executeQuery(rolePermissionsSql, [userId]);

    // 3. 合并权限并去重
    const allPermissions = new Set<string>();

    directPermissions.forEach((perm: any) => {
      allPermissions.add(perm.permission_code);
    });

    rolePermissions.forEach((perm: any) => {
      allPermissions.add(perm.permission_code);
    });

    return Array.from(allPermissions);
  } catch (error) {
    console.error('❌ 获取用户权限失败:', error);
    return [];
  }
}

/** 验证QQ账号格式 */
export function validateQQAccount(username: string): { valid: boolean; message: string } {
  // QQ号规则：5-11位数字，不能以0开头
  const qqRegex = /^[1-9][0-9]{4,10}$/;

  if (!username) {
    return { valid: false, message: 'QQ账号不能为空' };
  }

  if (!/^[0-9]+$/.test(username)) {
    return { valid: false, message: 'QQ账号只能包含数字' };
  }

  if (!qqRegex.test(username)) {
    return { valid: false, message: 'QQ账号格式不正确，应为5-11位数字且不能以0开头' };
  }

  return { valid: true, message: '' };
}

/** 验证密码格式 */
export function validatePassword(password: string): { valid: boolean; message: string } {
  if (!password) {
    return { valid: false, message: '密码不能为空' };
  }

  if (password.length < 6 || password.length > 20) {
    return { valid: false, message: '密码长度应为6-20位' };
  }

  // 检查是否包含数字
  if (!/\d/.test(password)) {
    return { valid: false, message: '密码必须包含数字' };
  }

  // 检查是否包含小写字母
  if (!/[a-z]/.test(password)) {
    return { valid: false, message: '密码必须包含小写字母' };
  }

  // 检查是否包含大写字母
  if (!/[A-Z]/.test(password)) {
    return { valid: false, message: '密码必须包含大写字母' };
  }

  return { valid: true, message: '' };
}

/** 验证邀请码 */
export async function validateInviteCode(
  inviteCode: string
): Promise<{ valid: boolean; message: string; inviter?: User }> {
  if (!inviteCode) {
    return { valid: false, message: '邀请码不能为空' };
  }

  if (!/^[A-Z0-9]{8}$/.test(inviteCode)) {
    return { valid: false, message: '邀请码格式不正确' };
  }

  try {
    const inviter = await findUserByInviteCode(inviteCode);
    if (!inviter) {
      return { valid: false, message: '邀请码不存在' };
    }

    if (inviter.status === 0) {
      return { valid: false, message: '邀请人账号已被禁用' };
    }

    return { valid: true, message: '', inviter };
  } catch (error) {
    console.error('验证邀请码失败:', error);
    return { valid: false, message: '验证邀请码时发生错误' };
  }
}

/** 获取用户的下级列表 */
export async function getUserSubordinates(userId: number): Promise<User[]> {
  try {
    const [rows] = await pool.execute<User[]>('SELECT * FROM fd_user WHERE sid = ? ORDER BY create_time DESC', [
      userId
    ]);
    return rows;
  } catch (error) {
    console.error('获取下级用户失败:', error);
    throw error;
  }
}

/** 获取用户的上级信息 */
export async function getUserSuperior(userId: number): Promise<User | null> {
  try {
    const user = await findUserByUserId(userId);
    if (!user || !user.sid) {
      return null;
    }

    return await findUserByUserId(user.sid);
  } catch (error) {
    console.error('获取上级用户失败:', error);
    throw error;
  }
}
