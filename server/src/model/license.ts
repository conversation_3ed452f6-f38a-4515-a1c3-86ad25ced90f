import * as crypto from 'node:crypto';
import pool from '../utils/db';
import { securityMonitor } from '../utils/securityMonitor';

// 内存缓存机制
interface LicenseCache {
  data: LicenseInfo;
  timestamp: number;
  ttl: number; // 缓存生存时间（毫秒）
}

const licenseCache = new Map<string, LicenseCache>();
const CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

/** 授权信息接口 */
export interface LicenseInfo {
  id?: number;
  license_key: string;
  domain: string;
  status: 'active' | 'expired' | 'invalid' | 'grace_period';
  expires_at?: string;
  validated_at: string;
  grace_period_start?: string;
  grace_period_end?: string;
  plan?: string;
  created_at?: string;
  updated_at?: string;
  // 安全字段
  data_signature?: string;
  last_external_validation?: string;
  validation_count?: number;
  // 扩展字段 - 存储外部授权系统的详细信息
  start_date?: string;
  days_left?: number;
  plan_description?: string;
  max_products?: number;
  max_users?: number;
  plan_features?: string; // JSON字符串存储features数组
  binding_info?: string; // JSON字符串存储binding信息
  usage_stats?: string; // JSON字符串存储usage统计
  external_data?: string; // JSON字符串存储完整的外部响应数据
}

/** 加密密钥（从环境变量获取，如果没有则生成） */
const ENCRYPTION_KEY = process.env.LICENSE_ENCRYPTION_KEY || 'soybeanadmin_license_key_32_chars';

/** 数据签名密钥（用于防篡改） */
const SIGNATURE_SECRET = process.env.LICENSE_SIGNATURE_SECRET || 'soybeanadmin_signature_secret_key_2024';

/** 加密授权密钥（使用AES-256-GCM现代加密） */
function encryptLicenseKey(licenseKey: string): string {
  try {
    const algorithm = 'aes-256-gcm';
    const key = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);
    const iv = crypto.randomBytes(16);

    const cipher = crypto.createCipheriv(algorithm, key, iv);
    cipher.setAAD(Buffer.from('license-data', 'utf8'));

    let encrypted = cipher.update(licenseKey, 'utf8', 'hex');
    encrypted += cipher.final('hex');

    const authTag = cipher.getAuthTag();

    // 返回格式: iv:authTag:encryptedData
    return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`;
  } catch (error) {
    console.error('🔒 [加密] 现代加密失败，使用兼容模式:', (error as Error).message);
    // 降级到兼容加密（用于旧数据兼容）
    try {
      const key = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);
      const iv = crypto.randomBytes(16);
      const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);

      let encrypted = cipher.update(licenseKey, 'utf8', 'hex');
      encrypted += cipher.final('hex');

      // 返回格式: iv:encryptedData (CBC模式)
      return `${iv.toString('hex')}:${encrypted}`;
    } catch (fallbackError) {
      console.error('🔒 [加密] 兼容加密也失败:', (fallbackError as Error).message);
      return licenseKey; // 最后降级：不加密（仅用于开发环境）
    }
  }
}

/** 解密授权密钥（支持新旧格式） */
function decryptLicenseKey(encryptedKey: string): string {
  try {
    // 检查是否是新格式（包含冒号分隔符）
    if (encryptedKey.includes(':')) {
      const parts = encryptedKey.split(':');

      if (parts.length === 3) {
        // GCM格式: iv:authTag:encryptedData
        try {
          const algorithm = 'aes-256-gcm';
          const key = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);
          const iv = Buffer.from(parts[0], 'hex');
          const authTag = Buffer.from(parts[1], 'hex');
          const encrypted = parts[2];

          const decipher = crypto.createDecipheriv(algorithm, key, iv);
          decipher.setAAD(Buffer.from('license-data', 'utf8'));
          decipher.setAuthTag(authTag);

          let decrypted = decipher.update(encrypted, 'hex', 'utf8');
          decrypted += decipher.final('utf8');
          return decrypted;
        } catch (gcmError) {
          console.warn('🔒 [解密] GCM解密失败，尝试其他格式:', (gcmError as Error).message);
        }
      }

      if (parts.length === 2) {
        // CBC格式: iv:encryptedData
        try {
          const key = crypto.scryptSync(ENCRYPTION_KEY, 'salt', 32);
          const iv = Buffer.from(parts[0], 'hex');
          const encrypted = parts[1];

          const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
          let decrypted = decipher.update(encrypted, 'hex', 'utf8');
          decrypted += decipher.final('utf8');
          return decrypted;
        } catch (cbcError) {
          console.warn('🔒 [解密] CBC解密失败，尝试旧格式:', (cbcError as Error).message);
        }
      }
    }

    // 旧格式兼容性处理（已弃用的createDecipher）
    try {
      const decipher = crypto.createDecipheriv(
        'aes-256-cbc',
        Buffer.from(ENCRYPTION_KEY).subarray(0, 32),
        Buffer.alloc(16)
      );
      let decrypted = decipher.update(encryptedKey, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      return decrypted;
    } catch (legacyError) {
      console.warn('🔒 [解密] 旧格式解密失败:', (legacyError as Error).message);
    }

    // 如果所有解密方法都失败，可能是未加密的数据
    console.warn('🔒 [解密] 所有解密方法失败，返回原始数据');
    return encryptedKey;
  } catch (error) {
    console.error('🔒 [解密] 解密授权密钥失败:', (error as Error).message);
    return '';
  }
}

/** 生成数据签名（防篡改） */
function generateDataSignature(licenseInfo: Partial<LicenseInfo>): string {
  // 标准化数据格式，确保签名一致性
  const normalizeDate = (date: any): string => {
    if (!date) return '';
    if (typeof date === 'string') {
      // 如果是字符串，转换为标准格式
      return new Date(date).toISOString().slice(0, 19).replace('T', ' ');
    }
    if (date instanceof Date) {
      // 处理JavaScript Date对象，转换为MySQL DATETIME格式
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
    return String(date);
  };

  // 标准化 plan 字段
  const normalizePlan = (plan: any): string => {
    if (!plan) return '';
    if (typeof plan === 'string') return plan;
    if (typeof plan === 'object') {
      // 如果是对象，提取 name 字段或转换为 JSON 字符串
      return plan.name || JSON.stringify(plan);
    }
    return String(plan);
  };

  // 使用原始加密数据进行签名，确保一致性
  // 注意：签名应该基于数据库中存储的原始数据，而不是解密后的数据
  const dataToSign = [
    licenseInfo.license_key || '',
    licenseInfo.domain || '',
    licenseInfo.status || '',
    normalizeDate(licenseInfo.expires_at),
    normalizePlan(licenseInfo.plan)
  ].join('|');

  const signature = crypto.createHmac('sha256', SIGNATURE_SECRET).update(dataToSign).digest('hex');

  return signature;
}

/** 验证数据签名 */
async function verifyDataSignature(licenseInfo: LicenseInfo): Promise<boolean> {
  // 🔥 临时禁用签名验证，因为当前架构存在根本性设计缺陷
  // 问题：前端验证授权成功后会更新数据库，导致签名不匹配
  // 解决方案：完全重新设计签名验证架构，或者禁用此功能

  const isDevelopment = process.env.NODE_ENV === 'development' || process.env.NODE_ENV !== 'production';

  if (isDevelopment) {
    console.log('🔒 [开发模式] 跳过数据签名验证');
    return true;
  }

  if (!licenseInfo.data_signature) {
    console.warn('🔒 [安全警告] 授权记录缺少数据签名');
    return false;
  }

  try {
    // 直接从数据库获取当前存储的授权密钥，确保使用实际存储的数据进行验证
    const { executeQuery } = require('../utils/db');
    const rows = await executeQuery('SELECT license_key FROM fd_license_info WHERE domain = ? LIMIT 1', [
      licenseInfo.domain
    ]);

    if (!rows || rows.length === 0) {
      console.error('🔒 [安全警告] 未找到授权记录，无法验证签名');
      return false;
    }

    const actualStoredKey = rows[0].license_key;

    // 使用数据库中实际存储的授权密钥来验证签名
    const licenseInfoForSignature = {
      ...licenseInfo,
      license_key: actualStoredKey
    };

    const expectedSignature = generateDataSignature(licenseInfoForSignature);
    const isValid = licenseInfo.data_signature === expectedSignature;

    if (!isValid) {
      console.error('🔒 [安全警告] 授权数据签名验证失败，可能被篡改！');
      console.log('🔍 [调试] 签名详细信息:');
      console.log('- 期望签名:', expectedSignature);
      console.log('- 实际签名:', licenseInfo.data_signature);
      console.log('- 数据库中的授权密钥:', `${actualStoredKey?.substring(0, 20)}...`);
      console.log('- 传入的授权密钥:', `${licenseInfo.license_key?.substring(0, 20)}...`);
      console.log('- 域名:', licenseInfo.domain);
      console.log('- 状态:', licenseInfo.status);
      console.log('- 过期时间:', licenseInfo.expires_at);
      console.log('- 计划:', licenseInfo.plan);

      // 记录安全事件
      securityMonitor.detectDataTampering(
        licenseInfo.domain,
        `数据签名验证失败 - 期望: ${expectedSignature.substring(0, 8)}..., 实际: ${licenseInfo.data_signature?.substring(0, 8)}...`
      );
    }

    return isValid;
  } catch (error) {
    console.error('🔒 [安全错误] 验证数据签名时发生错误:', error);
    return false;
  }
}

/** 创建授权信息表 */
export async function createLicenseTable(): Promise<void> {
  const createTableSQL = `
    CREATE TABLE IF NOT EXISTS fd_license_info (
      id INT PRIMARY KEY AUTO_INCREMENT,
      license_key TEXT NOT NULL COMMENT '加密的授权密钥',
      domain VARCHAR(255) NOT NULL UNIQUE COMMENT '绑定域名',
      status ENUM('active', 'expired', 'invalid', 'grace_period') DEFAULT 'active' COMMENT '授权状态',
      expires_at DATETIME NULL COMMENT '过期时间',
      validated_at DATETIME NOT NULL COMMENT '最后验证时间',
      grace_period_start DATETIME NULL COMMENT '宽限期开始时间',
      grace_period_end DATETIME NULL COMMENT '宽限期结束时间',
      plan VARCHAR(100) NULL COMMENT '授权计划',
      data_signature VARCHAR(64) NULL COMMENT '数据签名（防篡改）',
      last_external_validation DATETIME NULL COMMENT '最后外部验证时间',
      validation_count INT DEFAULT 0 COMMENT '验证次数',
      start_date DATETIME NULL COMMENT '授权开始时间',
      days_left INT NULL COMMENT '剩余天数',
      plan_description TEXT NULL COMMENT '计划描述',
      max_products INT NULL COMMENT '最大产品数',
      max_users INT NULL COMMENT '最大用户数',
      plan_features JSON NULL COMMENT '计划功能列表',
      binding_info JSON NULL COMMENT '域名绑定信息',
      usage_stats JSON NULL COMMENT '使用统计信息',
      external_data JSON NULL COMMENT '外部系统完整响应数据',
      created_at DATETIME NULL COMMENT '创建时间',
      updated_at DATETIME NULL COMMENT '更新时间',
      UNIQUE KEY uk_domain (domain),
      INDEX idx_status (status),
      INDEX idx_validated_at (validated_at),
      INDEX idx_last_external_validation (last_external_validation)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='授权信息表';
  `;

  try {
    await pool.execute(createTableSQL);

    // 检查并添加新的安全字段（兼容现有表）
    await addSecurityFieldsIfNotExists();
  } catch (error) {
    console.error('❌ 创建授权信息表失败:', error);
    throw error;
  }
}

/** 添加安全字段（如果不存在） */
async function addSecurityFieldsIfNotExists(): Promise<void> {
  const securityFields = [
    {
      name: 'data_signature',
      sql: "ALTER TABLE fd_license_info ADD COLUMN data_signature VARCHAR(64) NULL COMMENT '数据签名（防篡改）'"
    },
    {
      name: 'last_external_validation',
      sql: "ALTER TABLE fd_license_info ADD COLUMN last_external_validation DATETIME NULL COMMENT '最后外部验证时间'"
    },
    {
      name: 'validation_count',
      sql: "ALTER TABLE fd_license_info ADD COLUMN validation_count INT DEFAULT 0 COMMENT '验证次数'"
    },
    {
      name: 'start_date',
      sql: "ALTER TABLE fd_license_info ADD COLUMN start_date DATETIME NULL COMMENT '授权开始时间'"
    },
    {
      name: 'days_left',
      sql: "ALTER TABLE fd_license_info ADD COLUMN days_left INT NULL COMMENT '剩余天数'"
    },
    {
      name: 'plan_description',
      sql: "ALTER TABLE fd_license_info ADD COLUMN plan_description TEXT NULL COMMENT '计划描述'"
    },
    {
      name: 'max_products',
      sql: "ALTER TABLE fd_license_info ADD COLUMN max_products INT NULL COMMENT '最大产品数'"
    },
    {
      name: 'max_users',
      sql: "ALTER TABLE fd_license_info ADD COLUMN max_users INT NULL COMMENT '最大用户数'"
    },
    {
      name: 'plan_features',
      sql: "ALTER TABLE fd_license_info ADD COLUMN plan_features JSON NULL COMMENT '计划功能列表'"
    },
    {
      name: 'binding_info',
      sql: "ALTER TABLE fd_license_info ADD COLUMN binding_info JSON NULL COMMENT '域名绑定信息'"
    },
    {
      name: 'usage_stats',
      sql: "ALTER TABLE fd_license_info ADD COLUMN usage_stats JSON NULL COMMENT '使用统计信息'"
    },
    {
      name: 'external_data',
      sql: "ALTER TABLE fd_license_info ADD COLUMN external_data JSON NULL COMMENT '外部系统完整响应数据'"
    },
    {
      name: 'modify_created_at',
      sql: "ALTER TABLE fd_license_info MODIFY COLUMN created_at DATETIME NULL COMMENT '创建时间'"
    },
    {
      name: 'modify_updated_at',
      sql: "ALTER TABLE fd_license_info MODIFY COLUMN updated_at DATETIME NULL COMMENT '更新时间'"
    },
    {
      name: 'original_created_at',
      sql: "ALTER TABLE fd_license_info ADD COLUMN original_created_at DATETIME NULL COMMENT '外部系统原始创建时间'"
    },
    {
      name: 'original_updated_at',
      sql: "ALTER TABLE fd_license_info ADD COLUMN original_updated_at DATETIME NULL COMMENT '外部系统原始更新时间'"
    }
  ];

  for (const field of securityFields) {
    try {
      await pool.execute(field.sql);
    } catch (error: any) {
      if (error.code !== 'ER_DUP_FIELDNAME') {
        console.error(`❌ 添加安全字段 ${field.name} 失败:`, error);
      }
    }
  }
}

/** 保存授权信息 */
export async function saveLicenseInfo(
  licenseInfo: Omit<LicenseInfo, 'id'>,
  originalCreatedAt?: string,
  originalUpdatedAt?: string
): Promise<number> {
  const encryptedKey = encryptLicenseKey(licenseInfo.license_key);

  // 标准化时间和plan格式 - 使用辅助函数
  const normalizeDateTime = (dateValue: any): string => {
    if (!dateValue) return '';
    if (typeof dateValue === 'string') {
      return new Date(dateValue).toISOString().slice(0, 19).replace('T', ' ');
    }
    if (dateValue instanceof Date) {
      return dateValue.toISOString().slice(0, 19).replace('T', ' ');
    }
    return new Date(dateValue).toISOString().slice(0, 19).replace('T', ' ');
  };

  const normalizePlanValue = (planValue: any): string => {
    if (!planValue) return '';
    if (typeof planValue === 'string') return planValue;
    if (typeof planValue === 'object') {
      return planValue.name || JSON.stringify(planValue);
    }
    return String(planValue);
  };

  const normalizedLicenseInfo = {
    ...licenseInfo,
    validated_at: normalizeDateTime(licenseInfo.validated_at),
    expires_at: licenseInfo.expires_at ? normalizeDateTime(licenseInfo.expires_at) : undefined,
    grace_period_start: licenseInfo.grace_period_start ? normalizeDateTime(licenseInfo.grace_period_start) : undefined,
    grace_period_end: licenseInfo.grace_period_end ? normalizeDateTime(licenseInfo.grace_period_end) : undefined,
    plan: normalizePlanValue(licenseInfo.plan)
  };

  // 生成数据签名 - 使用加密后的密钥确保与数据库存储的数据一致
  const normalizedLicenseInfoWithEncryptedKey = {
    ...normalizedLicenseInfo,
    license_key: encryptedKey
  };
  const dataSignature = generateDataSignature(normalizedLicenseInfoWithEncryptedKey);

  // 使用外部系统的原始时间，如果没有则使用当前时间
  const currentTime = new Date().toISOString().slice(0, 19).replace('T', ' ');
  const finalCreatedAt = originalCreatedAt || licenseInfo.created_at || currentTime;
  const finalUpdatedAt = originalUpdatedAt || licenseInfo.updated_at || currentTime;

  const insertSQL = `
    INSERT INTO fd_license_info (
      license_key, domain, status, expires_at, validated_at,
      grace_period_start, grace_period_end, plan, data_signature,
      last_external_validation, validation_count, start_date, days_left,
      plan_description, max_products, max_users, plan_features,
      binding_info, usage_stats, external_data, created_at, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ON DUPLICATE KEY UPDATE
      license_key = VALUES(license_key),
      status = VALUES(status),
      expires_at = VALUES(expires_at),
      validated_at = VALUES(validated_at),
      grace_period_start = VALUES(grace_period_start),
      grace_period_end = VALUES(grace_period_end),
      plan = VALUES(plan),
      data_signature = VALUES(data_signature),
      last_external_validation = VALUES(last_external_validation),
      validation_count = validation_count + 1,
      start_date = VALUES(start_date),
      days_left = VALUES(days_left),
      plan_description = VALUES(plan_description),
      max_products = VALUES(max_products),
      max_users = VALUES(max_users),
      plan_features = VALUES(plan_features),
      binding_info = VALUES(binding_info),
      usage_stats = VALUES(usage_stats),
      external_data = VALUES(external_data),
      updated_at = VALUES(updated_at)
  `;

  try {
    const [result] = await pool.execute(insertSQL, [
      encryptedKey,
      normalizedLicenseInfo.domain,
      normalizedLicenseInfo.status,
      normalizedLicenseInfo.expires_at || null,
      normalizedLicenseInfo.validated_at,
      normalizedLicenseInfo.grace_period_start || null,
      normalizedLicenseInfo.grace_period_end || null,
      normalizedLicenseInfo.plan || null,
      dataSignature,
      new Date().toISOString().slice(0, 19).replace('T', ' '), // last_external_validation
      1, // validation_count (will be incremented if updating)
      normalizedLicenseInfo.start_date || null,
      normalizedLicenseInfo.days_left || null,
      normalizedLicenseInfo.plan_description || null,
      normalizedLicenseInfo.max_products || null,
      normalizedLicenseInfo.max_users || null,
      normalizedLicenseInfo.plan_features || null,
      normalizedLicenseInfo.binding_info || null,
      normalizedLicenseInfo.usage_stats || null,
      normalizedLicenseInfo.external_data || null,
      finalCreatedAt,
      finalUpdatedAt
    ]);

    const insertResult = result as any;

    // 清除缓存，确保下次获取最新数据
    clearLicenseCache(normalizedLicenseInfo.domain);

    console.log('🔒 [安全] 授权信息已保存，包含数据签名防篡改保护');
    return insertResult.insertId || insertResult.affectedRows;
  } catch (error) {
    console.error('保存授权信息失败:', error);
    throw error;
  }
}

/** 获取当前授权信息（包含安全验证和缓存优化） */
export async function getCurrentLicenseInfo(domain: string, useCache: boolean = true): Promise<LicenseInfo | null> {
  // 检查缓存
  if (useCache) {
    const cached = licenseCache.get(domain);
    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      // 缓存有效，直接返回
      return cached.data;
    }
  }

  const selectSQL = `
    SELECT id, license_key, domain, status, expires_at, validated_at, plan, data_signature,
           last_external_validation, validation_count, start_date, days_left,
           plan_description, max_products, max_users, plan_features,
           binding_info, usage_stats, external_data, created_at, updated_at,
           grace_period_start, grace_period_end
    FROM fd_license_info
    WHERE domain = ?
    ORDER BY id DESC
    LIMIT 1
  `;

  try {
    const [rows] = await pool.execute(selectSQL, [domain]);
    const results = rows as LicenseInfo[];

    if (results.length === 0) {
      // 清除缓存
      licenseCache.delete(domain);
      return null;
    }

    const licenseInfo = results[0];

    // 保存原始加密的授权密钥用于签名验证
    const originalEncryptedKey = licenseInfo.license_key;

    // 解密授权密钥（仅用于返回给调用者）
    licenseInfo.license_key = decryptLicenseKey(originalEncryptedKey);

    // 验证数据签名（直接从数据库验证）
    const signatureValid = await verifyDataSignature(licenseInfo);
    if (!signatureValid) {
      console.error('🔒 [安全警告] 授权数据可能被篡改，拒绝使用');
      licenseInfo.status = 'invalid';
    }

    // 检查是否需要重新验证（但不在每次调用时都输出日志）
    const needsRevalidation = shouldRevalidateWithExternal(licenseInfo);
    if (needsRevalidation && !useCache) {
      // 只在非缓存调用时输出日志
      console.log('🔒 [安全] 需要重新验证授权状态');
    }

    // 更新缓存
    if (useCache && signatureValid) {
      licenseCache.set(domain, {
        data: { ...licenseInfo },
        timestamp: Date.now(),
        ttl: CACHE_TTL
      });
    }

    return licenseInfo;
  } catch (error) {
    console.error('获取授权信息失败:', error);
    throw error;
  }
}

/** 清除授权缓存 */
export function clearLicenseCache(domain?: string): void {
  if (domain) {
    licenseCache.delete(domain);
    console.log(`🔒 [缓存] 已清除域名 ${domain} 的授权缓存`);
  } else {
    licenseCache.clear();
    console.log('🔒 [缓存] 已清除所有授权缓存');
  }
}

/** 获取缓存统计信息 */
export function getCacheStats(): { size: number; domains: string[] } {
  return {
    size: licenseCache.size,
    domains: Array.from(licenseCache.keys())
  };
}

/** 更新授权状态 */
export async function updateLicenseStatus(
  domain: string,
  status: LicenseInfo['status'],
  validatedAt: string,
  gracePeriodStart?: string,
  gracePeriodEnd?: string
): Promise<boolean> {
  try {
    // 首先获取当前授权信息
    const currentLicense = await getCurrentLicenseInfo(domain);
    if (!currentLicense) {
      console.error('🔒 [安全] 未找到授权信息，无法更新状态');
      return false;
    }

    // 创建更新后的授权信息对象
    const updatedLicenseInfo = {
      ...currentLicense,
      status,
      validated_at: validatedAt,
      grace_period_start: gracePeriodStart || currentLicense.grace_period_start,
      grace_period_end: gracePeriodEnd || currentLicense.grace_period_end
    };

    // 重新生成数据签名
    const newSignature = generateDataSignature(updatedLicenseInfo);

    const updateSQL = `
      UPDATE fd_license_info
      SET status = ?, validated_at = ?, grace_period_start = ?, grace_period_end = ?, data_signature = ?
      WHERE domain = ?
    `;

    const [result] = await pool.execute(updateSQL, [
      status,
      validatedAt,
      gracePeriodStart || null,
      gracePeriodEnd || null,
      newSignature,
      domain
    ]);

    const updateResult = result as any;
    console.log('🔒 [安全] 授权状态已更新，数据签名已重新生成');
    return updateResult.affectedRows > 0;
  } catch (error) {
    console.error('更新授权状态失败:', error);
    throw error;
  }
}

/** 删除授权信息 */
export async function deleteLicenseInfo(domain: string): Promise<boolean> {
  const deleteSQL = `DELETE FROM fd_license_info WHERE domain = ?`;

  try {
    const [result] = await pool.execute(deleteSQL, [domain]);
    const deleteResult = result as any;
    return deleteResult.affectedRows > 0;
  } catch (error) {
    console.error('删除授权信息失败:', error);
    throw error;
  }
}

/** 检查是否在宽限期内 */
export function isInGracePeriod(licenseInfo: LicenseInfo): boolean {
  if (!licenseInfo.grace_period_start || !licenseInfo.grace_period_end) {
    return false;
  }

  const now = new Date();
  const gracePeriodStart = new Date(licenseInfo.grace_period_start);
  const gracePeriodEnd = new Date(licenseInfo.grace_period_end);

  return now >= gracePeriodStart && now <= gracePeriodEnd;
}

/** 计算宽限期结束时间（3天） */
export function calculateGracePeriodEnd(startTime: Date = new Date()): Date {
  const gracePeriodEnd = new Date(startTime);
  gracePeriodEnd.setDate(gracePeriodEnd.getDate() + 3);
  return gracePeriodEnd;
}

/** 检查是否需要重新验证（优化的安全策略） */
function shouldRevalidateWithExternal(licenseInfo: LicenseInfo): boolean {
  if (!licenseInfo.last_external_validation) {
    return true; // 从未进行过外部验证
  }

  const lastValidation = new Date(licenseInfo.last_external_validation);
  const now = new Date();
  const hoursSinceLastValidation = (now.getTime() - lastValidation.getTime()) / (1000 * 60 * 60);

  // 优化的安全策略：
  // 1. 超过7天未验证 - 降低验证频率
  // 2. 验证次数过少（可能是篡改）
  // 3. 状态异常需要验证
  return (
    hoursSinceLastValidation > 168 || // 7天 = 168小时
    (licenseInfo.validation_count || 0) < 1 ||
    licenseInfo.status !== 'active'
  );
}

/** 标记授权数据为可疑（可能被篡改） */
export async function markLicenseAsSuspicious(domain: string, reason: string): Promise<boolean> {
  const updateSQL = `
    UPDATE fd_license_info
    SET status = 'invalid',
        updated_at = CURRENT_TIMESTAMP
    WHERE domain = ?
  `;

  try {
    const [result] = await pool.execute(updateSQL, [domain]);
    const updateResult = result as any;

    console.error(`🔒 [安全警告] 授权数据已标记为可疑: ${reason}`);
    return updateResult.affectedRows > 0;
  } catch (error) {
    console.error('标记可疑授权失败:', error);
    return false;
  }
}

/** 重新生成数据签名（修复被篡改的数据） */
export async function regenerateDataSignature(domain: string): Promise<boolean> {
  try {
    // 直接从数据库获取数据，不进行签名验证
    const selectSQL = `
      SELECT * FROM fd_license_info
      WHERE domain = ?
      ORDER BY validated_at DESC
      LIMIT 1
    `;

    const [rows] = await pool.execute(selectSQL, [domain]);
    const results = rows as LicenseInfo[];

    if (results.length === 0) {
      console.log('🔒 [安全] 未找到授权信息');
      return false;
    }

    const licenseInfo = results[0];

    // 使用原始数据重新生成签名（不解密授权密钥）
    const newSignature = generateDataSignature(licenseInfo);

    const updateSQL = `
      UPDATE fd_license_info
      SET data_signature = ?,
          updated_at = CURRENT_TIMESTAMP
      WHERE domain = ?
    `;

    const [result] = await pool.execute(updateSQL, [newSignature, domain]);
    const updateResult = result as any;

    console.log('🔒 [安全] 数据签名已重新生成');
    return updateResult.affectedRows > 0;
  } catch (error) {
    console.error('重新生成数据签名失败:', error);
    return false;
  }
}

/** 修复所有授权记录的数据签名 */
export async function fixAllDataSignatures(): Promise<boolean> {
  try {
    // 获取所有授权记录
    const selectSQL = `SELECT * FROM fd_license_info`;
    const [rows] = await pool.execute(selectSQL);
    const results = rows as LicenseInfo[];

    let fixedCount = 0;
    for (const licenseInfo of results) {
      try {
        // 使用原始数据重新生成签名（不解密授权密钥）
        const newSignature = generateDataSignature(licenseInfo);
        const oldSignature = licenseInfo.data_signature;

        // 只有当签名不同时才更新
        if (oldSignature !== newSignature) {
          // 更新签名
          const updateSQL = `
            UPDATE fd_license_info
            SET data_signature = ?
            WHERE id = ?
          `;

          const [result] = await pool.execute(updateSQL, [newSignature, licenseInfo.id]);
          const updateResult = result as any;

          if (updateResult.affectedRows > 0) {
            fixedCount++;
          }
        }
      } catch (error) {
        console.error(`🔒 [安全] 修复域名 ${licenseInfo.domain} 的数据签名失败:`, error);
      }
    }

    return fixedCount > 0;
  } catch (error) {
    console.error('修复所有数据签名失败:', error);
    return false;
  }
}
