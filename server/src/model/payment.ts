import type { RowDataPacket } from 'mysql2';

/**
 * 支付记录模型
 */
export interface Payment extends RowDataPacket {
  payment_id: number;
  user_id: number;
  order_no?: string;
  payment_no: string;
  trade_no?: string;
  payment_type: 'recharge' | 'order';
  payment_method: 'alipay' | 'wechat' | 'balance';
  amount: number;
  status: 0 | 1 | 2 | 3; // 0-待支付, 1-已支付, 2-支付失败, 3-已退款
  pay_time?: Date;
  notify_data?: any;
  remark?: string;
  create_time: Date;
  update_time: Date;
}

/**
 * 财务记录模型
 */
export interface Finance extends RowDataPacket {
  finance_id: number;
  user_id: number;
  type: 'recharge' | 'consume' | 'refund' | 'commission';
  amount: number;
  balance_before: number;
  balance_after: number;
  related_id?: number;
  related_type?: 'order' | 'payment' | 'manual';
  description?: string;
  operator_id?: number;
  create_time: Date;
}

/**
 * 充值配置模型
 */
export interface RechargeConfig extends RowDataPacket {
  config_id: number;
  payment_method: string;
  method_name: string;
  app_id?: string;
  merchant_id?: string;
  private_key?: string;
  public_key?: string;
  notify_url?: string;
  return_url?: string;
  gateway_url?: string;
  is_sandbox: 0 | 1;
  status: 0 | 1;
  sort_order: number;
  config_data?: any;
  create_time: Date;
  update_time: Date;
}

/**
 * 支付状态枚举
 */
export enum PaymentStatus {
  PENDING = 0, // 待支付
  PAID = 1, // 已支付
  FAILED = 2, // 支付失败
  REFUNDED = 3 // 已退款
}

/**
 * 财务记录类型枚举
 */
export enum FinanceType {
  RECHARGE = 'recharge', // 充值
  CONSUME = 'consume', // 消费
  REFUND = 'refund', // 退款
  COMMISSION = 'commission' // 佣金
}

/**
 * 支付方式枚举
 */
export enum PaymentMethod {
  ALIPAY = 'alipay', // 支付宝
  WECHAT = 'wechat', // 微信支付
  BALANCE = 'balance' // 余额支付
}

/**
 * 支付类型枚举
 */
export enum PaymentType {
  RECHARGE = 'recharge', // 充值
  ORDER = 'order' // 订单支付
}
