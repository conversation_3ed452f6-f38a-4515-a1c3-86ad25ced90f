import mysql from 'mysql2/promise';
import dotenv from 'dotenv';
import { formatDateTimeForLog } from '../utils/timeUtils';

// 加载环境变量
dotenv.config();

// 数据库配置 - 统一使用newfd用户
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: Number.parseInt(process.env.DB_PORT || '3306'),
  user: process.env.DB_USER || 'newfd', // 统一使用newfd用户
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'newfd',
  charset: 'utf8mb4',
  timezone: '+08:00',
  socketPath: '/tmp/mysql.sock', // 添加socket路径
  // 连接池配置
  connectionLimit: 10,
  queueLimit: 0,
  waitForConnections: true
  // 移除无效的配置选项以避免警告
  // acquireTimeout, timeout, reconnect, handleDisconnects 在 mysql2 中无效
};

// 创建连接池
export const pool = mysql.createPool(dbConfig);

// 测试数据库连接
export async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log(`${formatDateTimeForLog()} ✅ 数据库连接成功`);

    // 测试查询
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log(`${formatDateTimeForLog()} ✅ 数据库查询测试成功`);

    connection.release();
    return true;
  } catch (error) {
    console.error(`${formatDateTimeForLog()} ❌ 数据库连接失败:`, error);
    return false;
  }
}

// 优雅关闭连接池
export async function closePool() {
  try {
    await pool.end();
    console.log(`${formatDateTimeForLog()} ✅ 数据库连接池已关闭`);
  } catch (error) {
    console.error(`${formatDateTimeForLog()} ❌ 关闭数据库连接池失败:`, error);
  }
}

// 进程退出时关闭连接池
process.on('SIGINT', async () => {
  console.log(`${formatDateTimeForLog()} 🔄 正在关闭数据库连接...`);
  await closePool();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log(`${formatDateTimeForLog()} 🔄 正在关闭数据库连接...`);
  await closePool();
  process.exit(0);
});

// 导出默认连接
export default pool;
