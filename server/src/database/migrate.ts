import fs from 'node:fs';
import path from 'node:path';
import type { ResultSetHeader, RowDataPacket } from 'mysql2';
import { pool } from './connection';

interface Migration {
  id: number;
  filename: string;
  executed_at: string;
}

/** 创建迁移记录表 */
async function createMigrationsTable() {
  const sql = `
    CREATE TABLE IF NOT EXISTS migrations (
      id INT AUTO_INCREMENT PRIMARY KEY,
      filename VARCHAR(255) NOT NULL UNIQUE,
      executed_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
      INDEX idx_filename (filename),
      INDEX idx_executed_at (executed_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据库迁移记录表'
  `;

  await pool.execute(sql);
  console.log('✅ 迁移记录表已创建或已存在');
}

/** 获取已执行的迁移 */
async function getExecutedMigrations(): Promise<string[]> {
  try {
    const [rows] = await pool.execute<RowDataPacket[]>('SELECT filename FROM migrations ORDER BY id');
    return rows.map((row: any) => row.filename);
  } catch (error) {
    // 如果表不存在，返回空数组
    return [];
  }
}

/** 记录迁移执行 */
async function recordMigration(filename: string) {
  const sql = 'INSERT INTO migrations (filename) VALUES (?)';
  await pool.execute<ResultSetHeader>(sql, [filename]);
}

/** 执行单个迁移文件 */
async function executeMigration(filename: string, filepath: string) {
  console.log(`🔄 执行迁移: ${filename}`);

  try {
    const sql = fs.readFileSync(filepath, 'utf8');

    // 分割SQL语句（以分号分隔）
    const statements = sql
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0);

    // 执行每个SQL语句
    for (const statement of statements) {
      if (statement.trim()) {
        await pool.execute(statement);
      }
    }

    // 记录迁移执行
    await recordMigration(filename);
    console.log(`✅ 迁移完成: ${filename}`);
  } catch (error) {
    console.error(`❌ 迁移失败: ${filename}`, error);
    throw error;
  }
}

/** 运行所有待执行的迁移 */
export async function runMigrations() {
  console.log('🚀 开始数据库迁移...');

  try {
    // 创建迁移记录表
    await createMigrationsTable();

    // 获取已执行的迁移
    const executedMigrations = await getExecutedMigrations();
    console.log(`📋 已执行的迁移数量: ${executedMigrations.length}`);

    // 获取迁移文件目录 - 从源码目录读取
    const migrationsDir = path.join(__dirname, '../../src/database/migrations');

    if (!fs.existsSync(migrationsDir)) {
      console.log('📁 迁移目录不存在，创建目录...');
      fs.mkdirSync(migrationsDir, { recursive: true });
      return;
    }

    // 读取所有迁移文件
    const migrationFiles = fs
      .readdirSync(migrationsDir)
      .filter(file => file.endsWith('.sql'))
      .sort(); // 按文件名排序确保执行顺序

    console.log(`📁 发现迁移文件数量: ${migrationFiles.length}`);

    // 执行未执行的迁移
    let executedCount = 0;
    for (const filename of migrationFiles) {
      if (!executedMigrations.includes(filename)) {
        const filepath = path.join(migrationsDir, filename);
        await executeMigration(filename, filepath);
        executedCount++;
      } else {
        console.log(`⏭️  跳过已执行的迁移: ${filename}`);
      }
    }

    if (executedCount === 0) {
      console.log('✨ 所有迁移都已执行，无需更新');
    } else {
      console.log(`🎉 成功执行 ${executedCount} 个新迁移`);
    }
  } catch (error) {
    console.error('💥 数据库迁移失败:', error);
    throw error;
  }
}

/** 回滚最后一个迁移（谨慎使用） */
export async function rollbackLastMigration() {
  console.log('⚠️  开始回滚最后一个迁移...');

  try {
    const [rows] = await pool.execute<RowDataPacket[]>('SELECT filename FROM migrations ORDER BY id DESC LIMIT 1');

    if (rows.length === 0) {
      console.log('📋 没有可回滚的迁移');
      return;
    }

    const lastMigration = rows[0].filename;
    console.log(`🔄 回滚迁移: ${lastMigration}`);

    // 删除迁移记录
    await pool.execute<ResultSetHeader>('DELETE FROM migrations WHERE filename = ?', [lastMigration]);

    console.log(`✅ 已回滚迁移记录: ${lastMigration}`);
    console.log('⚠️  注意: 请手动检查并清理相关数据库结构');
  } catch (error) {
    console.error('💥 回滚迁移失败:', error);
    throw error;
  }
}

/** 获取迁移状态 */
export async function getMigrationStatus() {
  try {
    const [rows] = await pool.execute<RowDataPacket[]>('SELECT filename, executed_at FROM migrations ORDER BY id');

    const migrationsDir = path.join(__dirname, '../../src/database/migrations');
    const migrationFiles = fs.existsSync(migrationsDir)
      ? fs
          .readdirSync(migrationsDir)
          .filter(file => file.endsWith('.sql'))
          .sort()
      : [];

    console.log('\n📊 迁移状态:');
    console.log('='.repeat(60));

    migrationFiles.forEach(filename => {
      const executed = rows.find((row: any) => row.filename === filename);
      if (executed) {
        console.log(`✅ ${filename} (执行时间: ${executed.executed_at})`);
      } else {
        console.log(`⏳ ${filename} (待执行)`);
      }
    });

    console.log('='.repeat(60));
    console.log(`总计: ${migrationFiles.length} 个迁移文件, ${rows.length} 个已执行\n`);
  } catch (error) {
    console.error('💥 获取迁移状态失败:', error);
    throw error;
  }
}

// 如果直接运行此文件，执行迁移
if (require.main === module) {
  runMigrations()
    .then(() => {
      console.log('🎉 数据库迁移完成');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 数据库迁移失败:', error);
      process.exit(1);
    });
}
