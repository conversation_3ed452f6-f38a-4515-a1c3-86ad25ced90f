import { executeQuery } from '../utils/db';

/**
 * 权限策略服务（完全RBAC）
 * 统一从 RBAC 表计算有效角色与权限，供认证中间件与业务使用
 */
export async function getEffectiveRoles(userId: number): Promise<string[]> {
  // 仅从 fd_user_role 读取有效角色（状态=1，未过期）
  const sql = `
    SELECT r.role_code
    FROM fd_user_role ur
    JOIN fd_role r ON ur.role_id = r.role_id
    WHERE ur.user_id = ?
      AND ur.status = 1
      AND r.status = 1
      AND (ur.expire_time IS NULL OR ur.expire_time > NOW())
    ORDER BY r.role_level ASC
  `;
  const rows = (await executeQuery(sql, [userId])) as Array<{ role_code: string }>;
  return rows.map(r => r.role_code);
}

/**
 * 合并直接授权与角色授权，处理 grant/deny，过滤停用/过期
 */
export async function getEffectivePermissions(userId: number): Promise<string[]> {
  // 超级管理员：返回所有权限码（仅启用的 menu/button），确保前端 v-permission 全显示
  const superAdminRows = (await executeQuery(
    `SELECT 1 FROM fd_user_role ur JOIN fd_role r ON ur.role_id = r.role_id
     WHERE ur.user_id = ? AND r.status = 1 AND ur.status = 1 AND r.role_code = 'super_admin'
     AND (ur.expire_time IS NULL OR ur.expire_time > NOW()) LIMIT 1`,
    [userId]
  )) as any[];
  if (superAdminRows && superAdminRows.length > 0) {
    const all = (await executeQuery(
      `SELECT permission_code FROM fd_permission WHERE status = 1 AND permission_type IN ('menu','button') ORDER BY permission_group, sort_order`
    )) as Array<{ permission_code: string }>;
    return all.map(p => p.permission_code);
  }

  // 精简策略：仅根据用户的角色授权汇总（grant），忽略用户直授/拒绝/过期/数据范围
  const sql = `
    SELECT DISTINCT p.permission_code
    FROM fd_user_role ur
    JOIN fd_role r ON ur.role_id = r.role_id
    JOIN fd_role_permission rp ON r.role_id = rp.role_id
    JOIN fd_permission p ON rp.permission_id = p.permission_id
    WHERE ur.user_id = ?
      AND ur.status = 1 AND r.status = 1 AND rp.status = 1 AND p.status = 1
      AND rp.grant_type = 'grant'
      AND (ur.expire_time IS NULL OR ur.expire_time > NOW())
      AND p.permission_type IN ('menu','button')
    ORDER BY p.permission_group, p.sort_order
  `;

  const rows = (await executeQuery(sql, [userId])) as Array<{ permission_code: string }>;
  return rows.map(r => r.permission_code);
}

export async function getUserClaims(userId: number) {
  const roles = await getEffectiveRoles(userId);
  const permissions = await getEffectivePermissions(userId);
  return { roles, permissions };
}
