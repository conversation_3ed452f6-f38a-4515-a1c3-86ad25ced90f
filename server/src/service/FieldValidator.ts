/**
 * 字段验证器
 * 负责验证标准字段的数据类型、格式和完整性
 */

import type { FieldDefinition, ProjectStandardFields } from '../types/StandardFields';
import { FieldValidationRule, InterfaceType } from '../types/StandardFields';

/**
 * 验证结果
 */
export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

/**
 * 验证错误
 */
export interface ValidationError {
  field: string;
  message: string;
  code: string;
}

/**
 * 验证警告
 */
export interface ValidationWarning {
  field: string;
  message: string;
  code: string;
}

/**
 * 字段验证器类
 */
export class FieldValidator {
  /**
   * 验证单个字段
   */
  static validateField(fieldName: string, value: any, definition: FieldDefinition): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    const rule = definition.validation;

    // 检查必填字段
    if (rule.required && (value === undefined || value === null || value === '')) {
      errors.push({
        field: fieldName,
        message: `${definition.label}是必填字段`,
        code: 'REQUIRED_FIELD_MISSING'
      });
      return { valid: false, errors, warnings };
    }

    // 如果字段为空且非必填，跳过其他验证
    if (value === undefined || value === null || value === '') {
      return { valid: true, errors, warnings };
    }

    // 类型验证
    if (rule.type) {
      const typeValid = this.validateType(value, rule.type);
      if (!typeValid) {
        errors.push({
          field: fieldName,
          message: `${definition.label}的数据类型应为${rule.type}`,
          code: 'INVALID_TYPE'
        });
      }
    }

    // 字符串长度验证
    if (rule.type === 'string' && typeof value === 'string') {
      if (rule.minLength && value.length < rule.minLength) {
        errors.push({
          field: fieldName,
          message: `${definition.label}长度不能少于${rule.minLength}个字符`,
          code: 'MIN_LENGTH_VIOLATION'
        });
      }

      if (rule.maxLength && value.length > rule.maxLength) {
        errors.push({
          field: fieldName,
          message: `${definition.label}长度不能超过${rule.maxLength}个字符`,
          code: 'MAX_LENGTH_VIOLATION'
        });
      }
    }

    // 正则表达式验证
    if (rule.pattern && typeof value === 'string') {
      const regex = new RegExp(rule.pattern);
      if (!regex.test(value)) {
        errors.push({
          field: fieldName,
          message: `${definition.label}格式不正确`,
          code: 'PATTERN_MISMATCH'
        });
      }
    }

    // 枚举值验证
    if (rule.enum && rule.enum.length > 0) {
      if (!rule.enum.includes(value)) {
        errors.push({
          field: fieldName,
          message: `${definition.label}必须是以下值之一：${rule.enum.join(', ')}`,
          code: 'ENUM_VIOLATION'
        });
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 验证数据类型
   */
  private static validateType(value: any, expectedType: string): boolean {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      default:
        return true;
    }
  }

  /**
   * 验证接口输入字段
   */
  static validateInterfaceInput(
    interfaceType: InterfaceType,
    inputData: Partial<ProjectStandardFields>,
    fieldDefinitions: Record<string, FieldDefinition>
  ): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 根据接口类型获取必填字段
    const requiredFields = this.getRequiredFieldsByInterface(interfaceType);

    // 验证必填字段
    for (const fieldName of requiredFields) {
      const definition = fieldDefinitions[fieldName];
      if (!definition) {
        warnings.push({
          field: fieldName,
          message: `字段${fieldName}没有定义`,
          code: 'FIELD_DEFINITION_MISSING'
        });
        continue;
      }

      const result = this.validateField(fieldName, inputData[fieldName], definition);
      errors.push(...result.errors);
      warnings.push(...result.warnings);
    }

    // 验证其他提供的字段
    for (const [fieldName, value] of Object.entries(inputData)) {
      if (requiredFields.includes(fieldName)) continue; // 已经验证过

      const definition = fieldDefinitions[fieldName];
      if (definition) {
        const result = this.validateField(fieldName, value, definition);
        errors.push(...result.errors);
        warnings.push(...result.warnings);
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 根据接口类型获取必填字段
   */
  private static getRequiredFieldsByInterface(interfaceType: InterfaceType): string[] {
    const commonAuthFields = ['api_uid', 'api_key'];

    switch (interfaceType) {
      case InterfaceType.QUERY:
        return [...commonAuthFields, 'username', 'password', 'school', 'platform'];

      case InterfaceType.ORDER:
        return [...commonAuthFields, 'username', 'password', 'school', 'platform', 'course_id', 'course_name'];

      case InterfaceType.SYNC:
        return [...commonAuthFields, 'upstream_order_id'];

      case InterfaceType.REFILL:
        return [...commonAuthFields, 'upstream_order_id'];

      case InterfaceType.CHANGE_PASSWORD:
        return [...commonAuthFields, 'upstream_order_id', 'new_password'];

      case InterfaceType.GET_BALANCE:
        return commonAuthFields;

      case InterfaceType.GET_COURSES:
        return commonAuthFields;

      default:
        return commonAuthFields;
    }
  }

  /**
   * 验证字段映射配置
   */
  static validateFieldMapping(
    standardField: string,
    providerField: string,
    definition?: FieldDefinition
  ): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    // 检查标准字段是否有定义
    if (!definition) {
      warnings.push({
        field: standardField,
        message: `标准字段${standardField}没有定义`,
        code: 'STANDARD_FIELD_UNDEFINED'
      });
    }

    // 检查货源字段名是否有效
    if (!providerField || providerField.trim() === '') {
      errors.push({
        field: standardField,
        message: '货源字段名不能为空',
        code: 'PROVIDER_FIELD_EMPTY'
      });
    }

    // 检查字段名格式
    const fieldNamePattern = /^[a-zA-Z_][a-zA-Z0-9_]*$/;
    if (providerField && !fieldNamePattern.test(providerField)) {
      warnings.push({
        field: standardField,
        message: '货源字段名建议使用字母、数字和下划线',
        code: 'PROVIDER_FIELD_FORMAT_WARNING'
      });
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 批量验证字段映射
   */
  static validateFieldMappings(
    mappings: Record<string, string>,
    fieldDefinitions: Record<string, FieldDefinition>
  ): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];

    for (const [standardField, providerField] of Object.entries(mappings)) {
      const definition = fieldDefinitions[standardField];
      const result = this.validateFieldMapping(standardField, providerField, definition);

      errors.push(...result.errors);
      warnings.push(...result.warnings);
    }

    // 检查重复的货源字段名
    const providerFields = Object.values(mappings);
    const duplicates = providerFields.filter((field, index) => providerFields.indexOf(field) !== index);

    for (const duplicate of [...new Set(duplicates)]) {
      warnings.push({
        field: duplicate,
        message: `货源字段${duplicate}被多个标准字段映射`,
        code: 'DUPLICATE_PROVIDER_FIELD'
      });
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 应用默认值
   */
  static applyDefaultValues(
    inputData: Partial<ProjectStandardFields>,
    fieldDefinitions: Record<string, FieldDefinition>
  ): ProjectStandardFields {
    const result = { ...inputData } as ProjectStandardFields;

    for (const [fieldName, definition] of Object.entries(fieldDefinitions)) {
      if (
        definition.defaultValue !== undefined &&
        (result[fieldName] === undefined || result[fieldName] === null || result[fieldName] === '')
      ) {
        result[fieldName] = definition.defaultValue;
      }
    }

    return result;
  }

  /**
   * 获取字段分组
   */
  static getFieldsByGroup(group: string, fieldDefinitions: Record<string, FieldDefinition>): FieldDefinition[] {
    return Object.values(fieldDefinitions).filter(def => def.group === group);
  }

  /**
   * 获取所有字段分组
   */
  static getAllGroups(fieldDefinitions: Record<string, FieldDefinition>): string[] {
    const groups = new Set<string>();
    Object.values(fieldDefinitions).forEach(def => groups.add(def.group));
    return Array.from(groups);
  }
}
