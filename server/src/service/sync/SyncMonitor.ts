/**
 * 同步监控和指标收集器
 * 提供实时监控和历史数据分析
 */

import { executeQuery } from '../../utils/db';
import { formatDateTimeForLog } from '../../utils/timeUtils';

export interface SyncEvent {
  id?: string;
  type: 'start' | 'success' | 'error' | 'retry' | 'skip';
  orderId: string;
  strategy: string;
  duration?: number;
  error?: string;
  timestamp: Date;
  metadata?: any;
}

export interface SyncStats {
  period: string;
  totalOrders: number;
  successCount: number;
  errorCount: number;
  skipCount: number;
  retryCount: number;
  averageDuration: number;
  errorRate: number;
  throughput: number; // 每分钟处理数
  topErrors: Array<{ error: string; count: number }>;
  strategyStats: Array<{ strategy: string; count: number; successRate: number }>;
}

export interface PerformanceMetrics {
  queueSize: number;
  activeWorkers: number;
  memoryUsage: number;
  cpuUsage: number;
  networkLatency: number;
  databaseLatency: number;
  timestamp: Date;
}

export class SyncMonitor {
  private static instance: SyncMonitor;
  private events: SyncEvent[] = [];
  private maxEvents = 10000; // 内存中保留的最大事件数
  private metricsInterval?: NodeJS.Timeout;

  private constructor() {
    this.startMetricsCollection();
  }

  static getInstance(): SyncMonitor {
    if (!SyncMonitor.instance) {
      SyncMonitor.instance = new SyncMonitor();
    }
    return SyncMonitor.instance;
  }

  /**
   * 记录同步事件
   */
  recordEvent(event: Omit<SyncEvent, 'id' | 'timestamp'>): void {
    const fullEvent: SyncEvent = {
      ...event,
      id: this.generateEventId(),
      timestamp: new Date()
    };

    this.events.push(fullEvent);

    // 限制内存中的事件数量
    if (this.events.length > this.maxEvents) {
      this.events = this.events.slice(-this.maxEvents);
    }

    // 异步保存到数据库（如果需要持久化）
    this.saveEventToDatabase(fullEvent).catch(error => {
      console.error('保存同步事件失败:', error);
    });

    // 实时日志
    this.logEvent(fullEvent);
  }

  /**
   * 生成事件ID
   */
  private generateEventId(): string {
    return `sync_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 记录事件日志
   */
  private logEvent(event: SyncEvent): void {
    const emoji = {
      start: '🔄',
      success: '✅',
      error: '❌',
      retry: '🔁',
      skip: '⏭️'
    };

    const message = `${emoji[event.type]} [同步监控] ${event.type.toUpperCase()} - 订单: ${event.orderId}, 策略: ${event.strategy}`;

    if (event.duration) {
      console.log(`${formatDateTimeForLog()} ${message}, 耗时: ${event.duration}ms`);
    } else if (event.error) {
      console.error(`${formatDateTimeForLog()} ${message}, 错误: ${event.error}`);
    } else {
      console.log(`${formatDateTimeForLog()} ${message}`);
    }
  }

  /**
   * 保存事件到数据库
   */
  private async saveEventToDatabase(event: SyncEvent): Promise<void> {
    try {
      // 使用现有的配置表存储事件（简化方案）
      const eventKey = `sync_event_${event.id}`;
      const eventData = {
        type: event.type,
        orderId: event.orderId,
        strategy: event.strategy,
        duration: event.duration,
        error: event.error,
        timestamp: event.timestamp.toISOString(),
        metadata: event.metadata
      };

      await executeQuery(
        `INSERT INTO fd_config (config_key, config_value, config_group, description, is_system, create_time, update_time)
         VALUES (?, ?, 'sync_events', '同步事件记录', 1, NOW(), NOW())`,
        [eventKey, JSON.stringify(eventData)]
      );
    } catch (error) {
      // 静默失败，避免影响主流程
    }
  }

  /**
   * 获取实时统计
   */
  getRealTimeStats(minutes: number = 60): SyncStats {
    const cutoff = new Date(Date.now() - minutes * 60 * 1000);
    const recentEvents = this.events.filter(e => e.timestamp > cutoff);

    const totalOrders = new Set(recentEvents.map(e => e.orderId)).size;
    const successCount = recentEvents.filter(e => e.type === 'success').length;
    const errorCount = recentEvents.filter(e => e.type === 'error').length;
    const skipCount = recentEvents.filter(e => e.type === 'skip').length;
    const retryCount = recentEvents.filter(e => e.type === 'retry').length;

    const durations = recentEvents.filter(e => e.duration).map(e => e.duration!);
    const averageDuration = durations.length > 0 ? durations.reduce((a, b) => a + b, 0) / durations.length : 0;

    const errorRate = totalOrders > 0 ? errorCount / totalOrders : 0;
    const throughput = totalOrders / (minutes || 1);

    // 统计错误类型
    const errorMap = new Map<string, number>();
    recentEvents
      .filter(e => e.type === 'error' && e.error)
      .forEach(e => {
        const error = e.error!.substring(0, 100); // 截断长错误信息
        errorMap.set(error, (errorMap.get(error) || 0) + 1);
      });

    const topErrors = Array.from(errorMap.entries())
      .map(([error, count]) => ({ error, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 5);

    // 统计策略性能
    const strategyMap = new Map<string, { total: number; success: number }>();
    recentEvents.forEach(e => {
      if (!strategyMap.has(e.strategy)) {
        strategyMap.set(e.strategy, { total: 0, success: 0 });
      }
      const stats = strategyMap.get(e.strategy)!;
      if (e.type === 'success' || e.type === 'error') {
        stats.total++;
        if (e.type === 'success') {
          stats.success++;
        }
      }
    });

    const strategyStats = Array.from(strategyMap.entries())
      .map(([strategy, stats]) => ({
        strategy,
        count: stats.total,
        successRate: stats.total > 0 ? stats.success / stats.total : 0
      }))
      .sort((a, b) => b.count - a.count);

    return {
      period: `${minutes}分钟`,
      totalOrders,
      successCount,
      errorCount,
      skipCount,
      retryCount,
      averageDuration: Math.round(averageDuration),
      errorRate: Math.round(errorRate * 100) / 100,
      throughput: Math.round(throughput * 100) / 100,
      topErrors,
      strategyStats
    };
  }

  /**
   * 获取历史统计
   */
  async getHistoricalStats(days: number = 7): Promise<SyncStats[]> {
    try {
      const query = `
        SELECT config_value, create_time
        FROM fd_config
        WHERE config_group = 'sync_events'
          AND create_time > DATE_SUB(NOW(), INTERVAL ? DAY)
        ORDER BY create_time DESC
      `;

      const results = await executeQuery(query, [days]);
      const events: SyncEvent[] = results.map((row: any) => {
        const data = JSON.parse(row.config_value);
        return {
          ...data,
          timestamp: new Date(data.timestamp)
        };
      });

      // 按天分组统计
      const dailyStats: SyncStats[] = [];
      const dayGroups = new Map<string, SyncEvent[]>();

      events.forEach(event => {
        const day = event.timestamp.toISOString().split('T')[0];
        if (!dayGroups.has(day)) {
          dayGroups.set(day, []);
        }
        dayGroups.get(day)!.push(event);
      });

      for (const [day, dayEvents] of dayGroups) {
        // 使用相同的统计逻辑
        const totalOrders = new Set(dayEvents.map(e => e.orderId)).size;
        const successCount = dayEvents.filter(e => e.type === 'success').length;
        const errorCount = dayEvents.filter(e => e.type === 'error').length;
        const skipCount = dayEvents.filter(e => e.type === 'skip').length;
        const retryCount = dayEvents.filter(e => e.type === 'retry').length;

        const durations = dayEvents.filter(e => e.duration).map(e => e.duration!);
        const averageDuration = durations.length > 0 ? durations.reduce((a, b) => a + b, 0) / durations.length : 0;

        dailyStats.push({
          period: day,
          totalOrders,
          successCount,
          errorCount,
          skipCount,
          retryCount,
          averageDuration: Math.round(averageDuration),
          errorRate: totalOrders > 0 ? Math.round((errorCount / totalOrders) * 100) / 100 : 0,
          throughput: totalOrders / (24 * 60), // 每分钟
          topErrors: [],
          strategyStats: []
        });
      }

      return dailyStats.sort((a, b) => b.period.localeCompare(a.period));
    } catch (error) {
      console.error('获取历史统计失败:', error);
      return [];
    }
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics(): PerformanceMetrics {
    const memUsage = process.memoryUsage();

    return {
      queueSize: 0, // 由调度器提供
      activeWorkers: 0, // 由调度器提供
      memoryUsage: Math.round(memUsage.heapUsed / 1024 / 1024), // MB
      cpuUsage: 0, // 需要额外计算
      networkLatency: 0, // 需要测试网络
      databaseLatency: 0, // 需要测试数据库
      timestamp: new Date()
    };
  }

  /**
   * 开始指标收集
   */
  private startMetricsCollection(): void {
    // 每分钟收集一次性能指标
    this.metricsInterval = setInterval(() => {
      this.collectPerformanceMetrics();
    }, 60000);
  }

  /**
   * 收集性能指标
   */
  private async collectPerformanceMetrics(): Promise<void> {
    try {
      const metrics = this.getPerformanceMetrics();

      // 保存到配置表
      await executeQuery(
        `INSERT INTO fd_config (config_key, config_value, config_group, description, is_system, create_time, update_time)
         VALUES (?, ?, 'sync_metrics', '同步性能指标', 1, NOW(), NOW())`,
        [`sync_metrics_${Date.now()}`, JSON.stringify(metrics)]
      );
    } catch (error) {
      // 静默失败
    }
  }

  /**
   * 清理旧数据
   */
  async cleanup(retentionDays: number = 30): Promise<void> {
    try {
      await executeQuery(
        `DELETE FROM fd_config
         WHERE config_group IN ('sync_events', 'sync_metrics')
           AND create_time < DATE_SUB(NOW(), INTERVAL ? DAY)`,
        [retentionDays]
      );

      console.log(`${formatDateTimeForLog()} 🧹 [同步监控] 清理了 ${retentionDays} 天前的监控数据`);
    } catch (error) {
      console.error('清理监控数据失败:', error);
    }
  }

  /**
   * 停止监控
   */
  stop(): void {
    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
      this.metricsInterval = undefined;
    }
  }

  /**
   * 获取事件流（用于实时监控）
   */
  getEventStream(limit: number = 100): SyncEvent[] {
    return this.events.slice(-limit).reverse();
  }

  /**
   * 检查系统健康状态
   */
  getHealthStatus(): { status: 'healthy' | 'warning' | 'critical'; issues: string[] } {
    const stats = this.getRealTimeStats(30); // 最近30分钟
    const issues: string[] = [];
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    // 检查错误率
    if (stats.errorRate > 0.5) {
      issues.push(`错误率过高: ${(stats.errorRate * 100).toFixed(1)}%`);
      status = 'critical';
    } else if (stats.errorRate > 0.2) {
      issues.push(`错误率偏高: ${(stats.errorRate * 100).toFixed(1)}%`);
      status = 'warning';
    }

    // 检查吞吐量
    if (stats.throughput < 1 && stats.totalOrders > 0) {
      issues.push(`吞吐量过低: ${stats.throughput.toFixed(2)}/分钟`);
      if (status !== 'critical') status = 'warning';
    }

    // 检查平均响应时间
    if (stats.averageDuration > 30000) {
      // 30秒
      issues.push(`响应时间过长: ${(stats.averageDuration / 1000).toFixed(1)}秒`);
      if (status !== 'critical') status = 'warning';
    }

    return { status, issues };
  }
}

export default SyncMonitor;
