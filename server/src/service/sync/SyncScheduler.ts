/**
 * 智能订单同步调度器
 * 实现多种同步策略和性能优化
 */

import { executeQuery } from '../../utils/db';
import { formatDateTimeForLog } from '../../utils/timeUtils';
import type { SyncStrategy } from './SyncConfigManager';
import { SyncConfigManager } from './SyncConfigManager';

export interface SyncTask {
  orderId: string;
  priority: number;
  strategy: string;
  retryCount: number;
  lastAttempt?: Date;
  addedAt?: Date;
  estimatedDuration?: number;
}

export interface SyncMetrics {
  totalProcessed: number;
  successCount: number;
  errorCount: number;
  skipCount: number;
  retryCount: number;
  averageTime: number;
  averageProcessingTime: number;
  currentQueueSize: number;
  lastSyncTime: Date;
  lastProcessedAt: Date | null;
  startTime: Date;
  errorRate: number;
}

export class SyncScheduler {
  private static instance: SyncScheduler;
  private configManager: SyncConfigManager;
  private monitor: any; // 临时类型，避免循环依赖
  private syncQueue: SyncTask[] = [];
  private activeTasks: Map<string, SyncTask> = new Map();
  private metrics: SyncMetrics;
  private syncTimer?: NodeJS.Timeout;
  private isRunning = false;
  private rateLimiter: Map<string, number> = new Map();

  private constructor() {
    this.configManager = SyncConfigManager.getInstance();
    this.metrics = this.initMetrics();
    // 延迟初始化monitor，避免循环依赖
    this.initMonitor();
  }

  private async initMonitor() {
    try {
      const { SyncMonitor } = await import('./SyncMonitor');
      this.monitor = SyncMonitor.getInstance();
    } catch (error) {
      console.warn('Monitor初始化失败，使用空实现:', error);
      this.monitor = {
        recordEvent: () => {},
        getRealTimeStats: () => ({}),
        getHealthStatus: () => ({ status: 'unknown', issues: [] })
      };
    }
  }

  static getInstance(): SyncScheduler {
    if (!SyncScheduler.instance) {
      SyncScheduler.instance = new SyncScheduler();
    }
    return SyncScheduler.instance;
  }

  private initMetrics(): SyncMetrics {
    return {
      totalProcessed: 0,
      successCount: 0,
      errorCount: 0,
      skipCount: 0,
      retryCount: 0,
      averageTime: 0,
      averageProcessingTime: 0,
      currentQueueSize: 0,
      lastSyncTime: new Date(),
      lastProcessedAt: null,
      startTime: new Date(),
      errorRate: 0
    };
  }

  /**
   * 启动自动同步 - 重构版本，增强日志和错误处理
   */
  async start(): Promise<void> {
    console.log(`${formatDateTimeForLog()} 🚀 [同步调度器] 开始启动自动同步...`);

    if (this.isRunning) {
      console.log(`${formatDateTimeForLog()} ⚠️ [同步调度器] 已在运行中，跳过启动`);
      return;
    }

    try {
      // 加载配置
      console.log(`${formatDateTimeForLog()} 📋 [同步调度器] 加载同步配置...`);
      await this.configManager.loadConfig();
      const config = this.configManager.getConfig();

      console.log(`${formatDateTimeForLog()} 📋 [同步调度器] 配置加载完成:`, {
        enabled: config.enabled,
        autoStartOnBoot: config.autoStartOnBoot,
        maxConcurrency: config.maxConcurrency,
        batchSize: config.batchSize
      });

      if (!config.enabled) {
        console.log(`${formatDateTimeForLog()} 🔄 [同步调度器] 自动同步已禁用，启动取消`);
        return;
      }

      // 初始化状态
      this.isRunning = true;
      this.metrics.startTime = new Date();

      console.log(`${formatDateTimeForLog()} ✅ [同步调度器] 自动同步启动成功！`);
      console.log(`${formatDateTimeForLog()} 🔧 [同步调度器] 最大并发: ${config.maxConcurrency}`);
      console.log(`${formatDateTimeForLog()} 📦 [同步调度器] 批处理大小: ${config.batchSize}`);
      console.log(`${formatDateTimeForLog()} ⏰ [同步调度器] 使用智能间隔策略`);

      // 立即执行一次同步
      console.log(`${formatDateTimeForLog()} 🔄 [同步调度器] 执行首次同步...`);
      await this.performSync();

      // 设置定时同步 - 使用智能间隔
      console.log(`${formatDateTimeForLog()} ⏰ [同步调度器] 设置智能定时同步任务...`);
      this.syncTimer = setInterval(async () => {
        console.log(`${formatDateTimeForLog()} 🔄 [同步调度器] 定时同步触发`);
        await this.performSync();
      }, 60 * 1000); // 每分钟检查一次，根据状态配置决定是否同步

      console.log(`${formatDateTimeForLog()} 🎉 [同步调度器] 自动同步系统完全启动！`);
    } catch (error) {
      this.isRunning = false;
      console.error(`${formatDateTimeForLog()} ❌ [同步调度器] 启动失败:`, error);
      throw error;
    }
  }

  /**
   * 停止自动同步
   */
  stop(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer);
      this.syncTimer = undefined;
    }
    this.isRunning = false;
    console.log(`${formatDateTimeForLog()} 🔄 [同步调度器] 已停止自动同步`);
  }

  /** 获取当前是否在运行 */
  public getIsRunning(): boolean {
    return this.isRunning;
  }


  /**
   * 执行同步任务 - 重构版本，高性能和详细日志
   */
  private async performSync(): Promise<void> {
    const startTime = Date.now();
    console.log(`${formatDateTimeForLog()} 🔄 [同步调度器] ========== 开始执行同步任务 ==========`);

    try {
      const config = this.configManager.getConfig();

      // 验证配置完整性
      console.log(`${formatDateTimeForLog()} 🔧 [同步调度器] 当前配置:`, {
        enabled: config.enabled,
        maxConcurrency: config.maxConcurrency,
        batchSize: config.batchSize,
        enableRateLimit: config.enableRateLimit,
        rateLimit: config.rateLimit,
        orderStatusConfigs: config.orderStatusConfigs?.map(c => ({
          status: c.status,
          name: c.name,
          enabled: c.enabled,
          intervalMinutes: c.intervalMinutes,
          priority: c.priority
        }))
      });

      // 检查时间控制
      if (!this.isWithinWorkingTime(config)) {
        console.log(`${formatDateTimeForLog()} ⏰ [同步调度器] 当前时间不在工作时间范围内，跳过同步`);
        return;
      }

      // 检查系统状态
      console.log(`${formatDateTimeForLog()} 🔍 [同步调度器] 检查系统状态...`);

      // 检查错误率
      const currentErrorRate = this.calculateErrorRate();
      console.log(`${formatDateTimeForLog()} 📊 [同步调度器] 当前错误率: ${(currentErrorRate * 100).toFixed(1)}%`);

      if (currentErrorRate > config.errorThreshold) {
        console.warn(
          `${formatDateTimeForLog()} ⚠️ [同步调度器] 错误率过高 (${(currentErrorRate * 100).toFixed(1)}% > ${(config.errorThreshold * 100).toFixed(1)}%)，暂停同步`
        );
        return;
      }

      // 检查熔断器状态
      if (this.isCircuitBreakerOpen(config)) {
        console.warn(`${formatDateTimeForLog()} 🔌 [同步调度器] 熔断器已开启，暂停同步`);
        return;
      }

      // 检查队列状态
      const queueSize = this.syncQueue.length;
      console.log(`${formatDateTimeForLog()} 📋 [同步调度器] 当前队列大小: ${queueSize}`);

      // 构建同步队列
      console.log(`${formatDateTimeForLog()} 🏗️ [同步调度器] 构建同步队列...`);
      const queueBuildStart = Date.now();
      await this.buildSyncQueue();
      const queueBuildTime = Date.now() - queueBuildStart;

      const newQueueSize = this.syncQueue.length;
      console.log(
        `${formatDateTimeForLog()} ✅ [同步调度器] 队列构建完成: ${newQueueSize}个任务 (耗时: ${queueBuildTime}ms)`
      );

      if (newQueueSize === 0) {
        console.log(`${formatDateTimeForLog()} 💤 [同步调度器] 没有需要同步的订单`);
        return;
      }

      // 执行同步任务
      console.log(`${formatDateTimeForLog()} 🚀 [同步调度器] 开始处理同步队列...`);
      const processStart = Date.now();
      await this.processSyncQueue();
      const processTime = Date.now() - processStart;

      console.log(`${formatDateTimeForLog()} ✅ [同步调度器] 队列处理完成 (耗时: ${processTime}ms)`);

      // 更新指标
      this.updateMetrics();

      const totalTime = Date.now() - startTime;
      console.log(`${formatDateTimeForLog()} 🎉 [同步调度器] 同步任务完成！总耗时: ${totalTime}ms`);
      console.log(
        `${formatDateTimeForLog()} 📊 [同步调度器] 当前指标: 成功${this.metrics.successCount} 错误${this.metrics.errorCount} 跳过${this.metrics.skipCount}`
      );
    } catch (error) {
      const totalTime = Date.now() - startTime;
      console.error(`${formatDateTimeForLog()} ❌ [同步调度器] 同步执行失败 (耗时: ${totalTime}ms):`, error);
      this.metrics.errorCount++;
      this.updateMetrics();
    } finally {
      console.log(`${formatDateTimeForLog()} 🔄 [同步调度器] ========== 同步任务结束 ==========`);
    }
  }

  /**
   * 计算当前错误率
   */
  private calculateErrorRate(): number {
    const total = this.metrics.successCount + this.metrics.errorCount;
    if (total === 0) return 0;
    return this.metrics.errorCount / total;
  }

  /**
   * 检查是否在工作时间内
   */
  private isWithinWorkingTime(config: any): boolean {
    if (!config.workingHours?.enabled) {
      return true; // 未启用工作时间限制
    }

    const now = new Date();
    const currentDay = now.getDay(); // 0=周日, 1=周一, ..., 6=周六

    // 检查周末
    if ((currentDay === 0 || currentDay === 6) && !config.weekendEnabled) {
      console.log(`${formatDateTimeForLog()} 📅 [时间控制] 周末未启用同步`);
      return false;
    }

    // 检查工作时间
    const currentTime = now.toTimeString().slice(0, 5); // "HH:MM"
    const startTime = config.workingHours.start;
    const endTime = config.workingHours.end;

    if (currentTime < startTime || currentTime > endTime) {
      console.log(
        `${formatDateTimeForLog()} ⏰ [时间控制] 当前时间 ${currentTime} 不在工作时间 ${startTime}-${endTime} 内`
      );
      return false;
    }

    return true;
  }

  /**
   * 检查熔断器状态
   */
  private isCircuitBreakerOpen(config: any): boolean {
    if (!config.circuitBreaker?.enabled) {
      return false; // 未启用熔断器
    }

    // 这里可以实现熔断器逻辑
    // 简化实现：连续失败次数超过阈值时开启熔断器
    const recentErrors = this.metrics.errorCount;
    const threshold = config.circuitBreaker.failureThreshold;

    if (recentErrors >= threshold) {
      console.log(`${formatDateTimeForLog()} 🔌 [熔断器] 连续错误 ${recentErrors} 次，超过阈值 ${threshold}`);
      return true;
    }

    return false;
  }

  /**
   * 构建同步队列
   */
  private async buildSyncQueue(): Promise<void> {
    console.log(`${formatDateTimeForLog()} 🏗️ [队列构建] 开始构建同步队列...`);

    const config = this.configManager.getConfig();

    // 清空现有队列
    const oldQueueSize = this.syncQueue.length;
    this.syncQueue = [];
    console.log(`${formatDateTimeForLog()} 🗑️ [队列构建] 清空旧队列 (${oldQueueSize}个任务)`);

    const processedOrderIds = new Set<string>();
    let totalOrdersFound = 0;

    // 1. 按订单状态构建队列 - 只处理已提交的订单
    console.log(`${formatDateTimeForLog()} 📋 [队列构建] 处理订单状态策略...`);

    // 首先检查是否有已提交的订单
    const submittedOrders = await this.getSubmittedOrders();
    console.log(`${formatDateTimeForLog()} 📊 [队列构建] 找到 ${submittedOrders.length} 个已提交订单`);

    if (submittedOrders.length === 0) {
      console.log(`${formatDateTimeForLog()} 💤 [队列构建] 没有已提交的订单需要同步`);
      return;
    }

    // 按状态分组处理已提交的订单
    const ordersByStatus = this.groupOrdersByStatus(submittedOrders);

    for (const statusConfig of config.orderStatusConfigs) {
      if (!statusConfig.enabled) {
        console.log(
          `${formatDateTimeForLog()} ⏭️ [队列构建] 跳过禁用状态: ${statusConfig.name} (${statusConfig.status})`
        );
        continue;
      }

      const statusOrders = ordersByStatus[statusConfig.status] || [];
      console.log(
        `${formatDateTimeForLog()} 🔍 [队列构建] 处理状态: ${statusConfig.name} (${statusConfig.status}) - ${statusOrders.length}个订单`
      );

      if (statusOrders.length === 0) {
        continue;
      }

      // 检查时间间隔
      if (!this.shouldSyncStatus(statusConfig)) {
        console.log(
          `${formatDateTimeForLog()} ⏰ [队列构建] 状态 ${statusConfig.name} 距离上次同步不足 ${statusConfig.intervalMinutes} 分钟，跳过`
        );
        continue;
      }

      let addedCount = 0;
      for (const order of statusOrders) {
        if (!processedOrderIds.has(order.order_id)) {
          this.syncQueue.push({
            orderId: order.order_id,
            priority: statusConfig.priority,
            strategy: `status_${statusConfig.status}`,
            addedAt: new Date(),
            retryCount: 0
          });
          processedOrderIds.add(order.order_id);
          addedCount++;
        }
      }

      // 更新该状态的最后同步时间 - 无论是否有订单都更新，确保时间间隔控制生效
      this.updateLastSyncTime(statusConfig.status, new Date());

      console.log(
        `${formatDateTimeForLog()} ✅ [队列构建] 状态 ${statusConfig.name} 添加 ${addedCount} 个新任务，已更新同步时间`
      );
      totalOrdersFound += statusOrders.length;

      // 更新最后同步时间
      this.updateLastSyncTime(statusConfig.status, new Date());
    }

    // 2. 应用优先级规则（基于金额）
    console.log(`${formatDateTimeForLog()} 📋 [队列构建] 应用优先级规则...`);
    await this.applyPriorityRules(config.priorityRules);

    // 按优先级排序队列
    console.log(`${formatDateTimeForLog()} 🔄 [队列构建] 按优先级排序队列...`);
    this.syncQueue.sort((a, b) => b.priority - a.priority);

    // 限制队列大小，避免内存过载
    const maxQueueSize = config.batchSize * 10;
    if (this.syncQueue.length > maxQueueSize) {
      const originalSize = this.syncQueue.length;
      this.syncQueue = this.syncQueue.slice(0, maxQueueSize);
      console.log(`${formatDateTimeForLog()} ✂️ [队列构建] 队列大小限制: ${originalSize} -> ${maxQueueSize}`);
    }

    console.log(`${formatDateTimeForLog()} 🎉 [队列构建] 队列构建完成！`);
    console.log(
      `${formatDateTimeForLog()} 📊 [队列构建] 最终统计: 总订单${totalOrdersFound} 队列任务${this.syncQueue.length} 去重${totalOrdersFound - this.syncQueue.length}`
    );

    // 显示队列优先级分布
    const priorityStats = this.syncQueue.reduce(
      (acc, task) => {
        acc[task.priority] = (acc[task.priority] || 0) + 1;
        return acc;
      },
      {} as Record<number, number>
    );

    console.log(`${formatDateTimeForLog()} 📊 [队列构建] 优先级分布:`, priorityStats);
  }

  /**
   * 获取所有已提交的订单（有upstream_order_id的订单）
   */
  private async getSubmittedOrders(): Promise<any[]> {
    const config = this.configManager.getConfig();

    const query = `
      SELECT order_id, order_no, amount, course_name, status, create_time, update_time, upstream_order_id
      FROM fd_order
      WHERE upstream_order_id IS NOT NULL
        AND upstream_order_id != ""
        AND create_time > DATE_SUB(NOW(), INTERVAL ? HOUR)
      ORDER BY create_time DESC
      LIMIT 1000
    `;

    const params = [config.maxOrderAgeHours];

    console.log(`${formatDateTimeForLog()} 🔄 [已提交订单] 查询已提交订单`);
    console.log(`${formatDateTimeForLog()} 🔄 [已提交订单] 参数:`, params);

    const { executeQuery } = await import('../../utils/db');
    return await executeQuery(query, params);
  }

  /**
   * 按状态分组订单
   */
  private groupOrdersByStatus(orders: any[]): Record<number, any[]> {
    const grouped: Record<number, any[]> = {};

    for (const order of orders) {
      const status = order.status;
      if (!grouped[status]) {
        grouped[status] = [];
      }
      grouped[status].push(order);
    }

    console.log(
      `${formatDateTimeForLog()} 📊 [订单分组] 按状态分组完成:`,
      Object.keys(grouped)
        .map(status => `状态${status}: ${grouped[Number.parseInt(status)].length}个`)
        .join(', ')
    );

    return grouped;
  }

  /**
   * 检查状态是否应该同步（基于时间间隔）
   */
  private shouldSyncStatus(statusConfig: any): boolean {
    const lastSyncTime = this.getLastSyncTime(statusConfig.status);
    const now = new Date();
    const intervalMs = statusConfig.intervalMinutes * 60 * 1000;

    console.log(`${formatDateTimeForLog()} 🔍 [时间检查] 状态 ${statusConfig.name}:`, {
      intervalMinutes: statusConfig.intervalMinutes,
      intervalMs,
      lastSyncTime: lastSyncTime?.toISOString(),
      now: now.toISOString()
    });

    if (!lastSyncTime) {
      console.log(`${formatDateTimeForLog()} ✅ [时间检查] 状态 ${statusConfig.name} 从未同步过，允许同步`);
      return true; // 从未同步过
    }

    const timeSinceLastSync = now.getTime() - lastSyncTime.getTime();
    const shouldSync = timeSinceLastSync >= intervalMs;

    console.log(`${formatDateTimeForLog()} 📊 [时间检查] 状态 ${statusConfig.name}:`, {
      timeSinceLastSyncMs: timeSinceLastSync,
      timeSinceLastSyncMin: Math.round(timeSinceLastSync / 60000),
      requiredIntervalMin: statusConfig.intervalMinutes,
      shouldSync
    });

    return shouldSync;
  }

  /**
   * 根据订单状态获取订单 - 保留兼容性
   */
  private async getOrdersByStatus(statusConfig: any): Promise<any[]> {
    const config = this.configManager.getConfig();

    // 检查是否需要同步（基于时间间隔）
    const lastSyncTime = this.getLastSyncTime(statusConfig.status);
    const now = new Date();
    const intervalMs = statusConfig.intervalMinutes * 60 * 1000;

    if (lastSyncTime && now.getTime() - lastSyncTime.getTime() < intervalMs) {
      console.log(
        `${formatDateTimeForLog()} ⏰ [状态查询] 状态 ${statusConfig.name} 距离上次同步不足 ${statusConfig.intervalMinutes} 分钟，跳过`
      );
      return [];
    }

    const query = `
      SELECT order_id, order_no, amount, course_name, status, create_time, update_time, upstream_order_id
      FROM fd_order
      WHERE status = ?
        AND upstream_order_id IS NOT NULL
        AND upstream_order_id != ""
        AND create_time > DATE_SUB(NOW(), INTERVAL ? HOUR)
      ORDER BY create_time DESC
      LIMIT 100
    `;

    const params = [statusConfig.status, config.maxOrderAgeHours];

    console.log(`${formatDateTimeForLog()} 🔄 [状态查询] 状态 ${statusConfig.name} 执行查询`);
    console.log(`${formatDateTimeForLog()} 🔄 [状态查询] 参数:`, params);

    const { executeQuery } = await import('../../utils/db');
    const result = await executeQuery(query, params);

    // 更新最后同步时间
    this.updateLastSyncTime(statusConfig.status, now);

    return result;
  }

  /**
   * 根据自定义规则获取订单（从已提交订单中过滤）
   */
  private async getOrdersByCustomRule(rule: any, submittedOrders?: any[]): Promise<any[]> {
    // 如果提供了已提交订单列表，直接从中过滤
    if (submittedOrders) {
      return this.filterOrdersByRule(submittedOrders, rule);
    }

    // 否则查询数据库（保留兼容性）
    const config = this.configManager.getConfig();

    const whereConditions = [
      'upstream_order_id IS NOT NULL',
      'upstream_order_id != ""',
      `create_time > DATE_SUB(NOW(), INTERVAL ? HOUR)`
    ];
    const params: any[] = [config.maxOrderAgeHours];

    // 金额条件
    if (rule.conditions.minAmount) {
      whereConditions.push('amount >= ?');
      params.push(rule.conditions.minAmount);
    }

    if (rule.conditions.maxAmount) {
      whereConditions.push('amount <= ?');
      params.push(rule.conditions.maxAmount);
    }

    // 时间条件
    if (rule.conditions.maxAgeHours) {
      whereConditions.push('create_time > DATE_SUB(NOW(), INTERVAL ? HOUR)');
      params.push(rule.conditions.maxAgeHours);
    }

    // 关键词条件
    if (rule.conditions.keywords && rule.conditions.keywords.length > 0) {
      const keywordConditions = rule.conditions.keywords.map(() => 'course_name LIKE ?').join(' OR ');
      whereConditions.push(`(${keywordConditions})`);
      rule.conditions.keywords.forEach((keyword: string) => {
        params.push(`%${keyword}%`);
      });
    }

    const query = `
      SELECT order_id, order_no, amount, course_name, status, create_time, update_time, upstream_order_id
      FROM fd_order
      WHERE ${whereConditions.join(' AND ')}
      ORDER BY create_time DESC
      LIMIT 100
    `;

    console.log(`${formatDateTimeForLog()} 🔄 [自定义规则] 规则 ${rule.name} 执行查询`);
    console.log(`${formatDateTimeForLog()} 🔄 [自定义规则] 参数:`, params);

    const { executeQuery } = await import('../../utils/db');
    return await executeQuery(query, params);
  }

  /**
   * 应用优先级规则（基于商品订单量调整优先级）
   */
  private async applyPriorityRules(priorityRules: any[]): Promise<void> {
    if (!priorityRules || priorityRules.length === 0) {
      console.log(`${formatDateTimeForLog()} ⏭️ [优先级规则] 没有启用的优先级规则`);
      return;
    }

    const enabledRules = priorityRules.filter(rule => rule.enabled);
    if (enabledRules.length === 0) {
      console.log(`${formatDateTimeForLog()} ⏭️ [优先级规则] 所有优先级规则都已禁用`);
      return;
    }

    console.log(`${formatDateTimeForLog()} 🔍 [优先级规则] 启用的规则: ${enabledRules.map(r => r.name).join(', ')}`);

    let adjustedCount = 0;

    // 获取订单商品信息
    const orderIds = this.syncQueue.map(task => task.orderId);
    if (orderIds.length === 0) {
      return;
    }

    try {
      const { executeQuery } = await import('../../utils/db');

      // 获取订单的商品ID
      const orderProducts = await executeQuery(
        `SELECT order_id, product_id FROM fd_order WHERE order_id IN (${orderIds.map(() => '?').join(',')})`,
        orderIds
      );

      // 获取每个商品的总订单数量
      const productIds = [...new Set(orderProducts.map((order: any) => order.product_id))];
      if (productIds.length === 0) {
        console.log(`${formatDateTimeForLog()} 📊 [优先级规则] 没有找到商品信息`);
        return;
      }

      const productOrderCounts = await executeQuery(
        `SELECT product_id, COUNT(*) as order_count FROM fd_order WHERE product_id IN (${productIds.map(() => '?').join(',')}) GROUP BY product_id`,
        productIds
      );

      const productCountMap = new Map();
      productOrderCounts.forEach((product: any) => {
        productCountMap.set(product.product_id, Number.parseInt(product.order_count) || 0);
      });

      console.log(`${formatDateTimeForLog()} 📊 [优先级规则] 获取了 ${productOrderCounts.length} 个商品的订单量信息`);

      // 按商品订单量规则调整优先级
      for (const task of this.syncQueue) {
        const orderProduct = orderProducts.find((order: any) => order.order_id === task.orderId);
        if (!orderProduct) continue;

        const productOrderCount = productCountMap.get(orderProduct.product_id) || 0;

        // 找到适用的最高优先级规则
        let bestRule = null;
        for (const rule of enabledRules) {
          if (productOrderCount >= rule.minOrderCount) {
            if (!bestRule || rule.priority > bestRule.priority) {
              bestRule = rule;
            }
          }
        }

        // 应用规则
        if (bestRule && task.priority < bestRule.priority) {
          const oldPriority = task.priority;
          task.priority = bestRule.priority;
          adjustedCount++;
          console.log(
            `${formatDateTimeForLog()} 🔝 [优先级规则] 订单 ${task.orderId} (商品订单量: ${productOrderCount}) 优先级: ${oldPriority} -> ${bestRule.priority} (${bestRule.name})`
          );
        }
      }

      if (adjustedCount > 0) {
        console.log(`${formatDateTimeForLog()} 🔝 [优先级规则] 共调整了 ${adjustedCount} 个任务的优先级`);
        // 重新排序队列
        this.syncQueue.sort((a, b) => b.priority - a.priority);
      } else {
        console.log(`${formatDateTimeForLog()} 📊 [优先级规则] 没有订单需要调整优先级`);
      }
    } catch (error) {
      console.error(`${formatDateTimeForLog()} ❌ [优先级规则] 获取商品订单量失败:`, error);
    }
  }

  /**
   * 从订单列表中按规则过滤 - 保留兼容性
   */
  private filterOrdersByRule(orders: any[], rule: any): any[] {
    return orders.filter(order => {
      // 金额条件
      if (rule.conditions.minAmount && order.amount < rule.conditions.minAmount) {
        return false;
      }

      if (rule.conditions.maxAmount && order.amount > rule.conditions.maxAmount) {
        return false;
      }

      // 时间条件
      if (rule.conditions.maxAgeHours) {
        const orderTime = new Date(order.create_time);
        const now = new Date();
        const ageHours = (now.getTime() - orderTime.getTime()) / (1000 * 60 * 60);
        if (ageHours > rule.conditions.maxAgeHours) {
          return false;
        }
      }

      // 关键词条件
      if (rule.conditions.keywords && rule.conditions.keywords.length > 0) {
        const courseName = order.course_name || '';
        const hasKeyword = rule.conditions.keywords.some((keyword: string) => courseName.includes(keyword));
        if (!hasKeyword) {
          return false;
        }
      }

      return true;
    });
  }

  // 简单的内存缓存来跟踪最后同步时间
  private lastSyncTimes = new Map<number, Date>();

  private getLastSyncTime(status: number): Date | null {
    const lastTime = this.lastSyncTimes.get(status);
    console.log(
      `${formatDateTimeForLog()} 🕒 [时间记录] 获取状态 ${status} 最后同步时间:`,
      lastTime?.toISOString() || 'undefined'
    );
    return lastTime || null;
  }

  private updateLastSyncTime(status: number, time: Date): void {
    this.lastSyncTimes.set(status, time);
    console.log(`${formatDateTimeForLog()} 🕒 [时间记录] 更新状态 ${status} 同步时间:`, time.toISOString());
  }

  /**
   * 批量更新多个状态的同步时间
   */
  private updateMultipleLastSyncTimes(statuses: number[]): void {
    const now = new Date();
    statuses.forEach(status => {
      this.lastSyncTimes.set(status, now);
      console.log(`${formatDateTimeForLog()} 🕒 [时间记录] 批量更新状态 ${status} 同步时间:`, now.toISOString());
    });
  }

  /**
   * 根据策略获取订单 - 保留兼容性
   */
  private async getOrdersByStrategy(
    strategy: SyncStrategy,
    baseConditions: string[],
    baseParams: any[]
  ): Promise<any[]> {
    try {
      // 使用简单的固定查询，避免复杂的参数构建
      let query = '';
      let params: any[] = [];

      switch (strategy.id) {
        case 'high_value':
          // 先查询所有订单，了解数据情况
          const debugQuery = `
            SELECT COUNT(*) as total_orders,
                   COUNT(CASE WHEN upstream_order_id IS NOT NULL AND upstream_order_id != "" THEN 1 END) as with_upstream_id,
                   COUNT(CASE WHEN status IN (2, 3, 6) THEN 1 END) as valid_status,
                   COUNT(CASE WHEN amount >= ? THEN 1 END) as high_value,
                   COUNT(CASE WHEN create_time > DATE_SUB(NOW(), INTERVAL 72 HOUR) THEN 1 END) as recent
            FROM fd_order
          `;
          const debugResult = await executeQuery(debugQuery, [strategy.config.minAmount || 100]);
          console.log(`${formatDateTimeForLog()} 🔍 [调试] 订单数据统计:`, debugResult[0]);

          // 放宽查询条件，先找到有upstream_order_id的订单
          query = `
            SELECT order_id, order_no, amount, course_name, status, create_time, update_time, upstream_order_id
            FROM fd_order
            WHERE upstream_order_id IS NOT NULL
              AND upstream_order_id != ""
              AND amount >= ?
            ORDER BY create_time DESC
            LIMIT 100
          `;
          params = [strategy.config.minAmount || 50]; // 降低金额门槛
          break;

        case 'recent_orders':
          // 放宽条件，查找最近的订单
          query = `
            SELECT order_id, order_no, amount, course_name, status, create_time, update_time, upstream_order_id
            FROM fd_order
            WHERE upstream_order_id IS NOT NULL
              AND upstream_order_id != ""
              AND create_time > DATE_SUB(NOW(), INTERVAL ? HOUR)
            ORDER BY create_time DESC
            LIMIT 100
          `;
          params = [strategy.config.maxAgeHours || 168]; // 扩展到7天
          break;

        case 'failed_orders':
          // 查找所有有upstream_order_id的订单，不限制状态
          query = `
            SELECT order_id, order_no, amount, course_name, status, create_time, update_time, upstream_order_id
            FROM fd_order
            WHERE upstream_order_id IS NOT NULL
              AND upstream_order_id != ""
            ORDER BY create_time DESC
            LIMIT 100
          `;
          params = [];
          break;

        case 'keyword_match':
          if (strategy.config.keywords && strategy.config.keywords.length > 0) {
            const keywordConditions = strategy.config.keywords.map(() => 'course_name LIKE ?').join(' OR ');
            query = `
              SELECT order_id, order_no, amount, course_name, status, create_time, update_time
              FROM fd_order
              WHERE status IN (2, 3, 6)
                AND upstream_order_id IS NOT NULL
                AND upstream_order_id != ""
                AND (${keywordConditions})
                AND create_time > DATE_SUB(NOW(), INTERVAL 72 HOUR)
              ORDER BY create_time DESC
              LIMIT 100
            `;
            params = strategy.config.keywords.map((keyword: string) => `%${keyword}%`);
          } else {
            return []; // 没有关键词就返回空
          }
          break;

        default:
          query = `
            SELECT order_id, order_no, amount, course_name, status, create_time, update_time
            FROM fd_order
            WHERE status IN (2, 3, 6)
              AND upstream_order_id IS NOT NULL
              AND upstream_order_id != ""
              AND create_time > DATE_SUB(NOW(), INTERVAL 72 HOUR)
            ORDER BY create_time DESC
            LIMIT 100
          `;
          params = [];
          break;
      }

      console.log(`🔄 [同步调度器] 策略 ${strategy.id} 执行查询`);
      console.log(`🔄 [同步调度器] 参数:`, params);

      return await executeQuery(query, params);
    } catch (error) {
      console.error(`❌ [同步调度器] 策略 ${strategy.id} 查询失败:`, error);
      return []; // 返回空数组，不影响其他策略
    }
  }

  /**
   * 处理同步队列 - 重构版本，高性能并发处理
   */
  private async processSyncQueue(): Promise<void> {
    const config = this.configManager.getConfig();
    const totalTasks = this.syncQueue.length;
    const batchSize = Math.min(config.batchSize, totalTasks);

    console.log(`${formatDateTimeForLog()} 🚀 [队列处理] 开始处理同步队列`);
    console.log(
      `${formatDateTimeForLog()} 📊 [队列处理] 队列大小: ${totalTasks}, 批处理大小: ${batchSize}, 最大并发: ${config.maxConcurrency}`
    );

    if (batchSize === 0) {
      console.log(`${formatDateTimeForLog()} 💤 [队列处理] 队列为空，跳过处理`);
      return;
    }

    // 分批处理，避免内存过载
    const batch = this.syncQueue.splice(0, batchSize);
    console.log(`${formatDateTimeForLog()} 📦 [队列处理] 提取批次: ${batch.length} 个任务`);

    // 显示批次任务详情
    const strategyStats = batch.reduce(
      (acc, task) => {
        acc[task.strategy] = (acc[task.strategy] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );
    console.log(`${formatDateTimeForLog()} 📊 [队列处理] 批次策略分布:`, strategyStats);

    // 高性能并发处理
    const startTime = Date.now();
    const results = await this.processBatchConcurrently(batch, config.maxConcurrency);
    const processTime = Date.now() - startTime;

    // 统计处理结果
    const successCount = results.filter(r => r.status === 'fulfilled').length;
    const errorCount = results.filter(r => r.status === 'rejected').length;

    console.log(
      `${formatDateTimeForLog()} ✅ [队列处理] 批次处理完成: 成功${successCount} 失败${errorCount} (耗时: ${processTime}ms)`
    );
    console.log(
      `${formatDateTimeForLog()} 📊 [队列处理] 平均处理时间: ${(processTime / batch.length).toFixed(2)}ms/任务`
    );
    console.log(`${formatDateTimeForLog()} 📊 [队列处理] 剩余队列大小: ${this.syncQueue.length}`);
  }

  /**
   * 高性能并发处理批次任务
   */
  private async processBatchConcurrently(
    batch: SyncTask[],
    maxConcurrency: number
  ): Promise<PromiseSettledResult<void>[]> {
    console.log(
      `${formatDateTimeForLog()} 🔄 [并发处理] 启动并发处理: ${batch.length}个任务, 最大并发${maxConcurrency}`
    );

    // 使用信号量控制并发
    const semaphore = new Array(maxConcurrency).fill(null);
    const promises: Promise<void>[] = [];

    for (let i = 0; i < batch.length; i++) {
      const task = batch[i];
      const taskIndex = i + 1;

      const promise = this.waitForSlot(semaphore).then(async slotIndex => {
        const taskStart = Date.now();
        console.log(
          `${formatDateTimeForLog()} 🔄 [任务${taskIndex}] 开始处理订单 ${task.orderId} (槽位${slotIndex + 1})`
        );

        try {
          await this.processSingleTask(task);
          const taskTime = Date.now() - taskStart;
          console.log(
            `${formatDateTimeForLog()} ✅ [任务${taskIndex}] 订单 ${task.orderId} 处理成功 (耗时: ${taskTime}ms)`
          );
        } catch (error) {
          const taskTime = Date.now() - taskStart;
          console.error(
            `${formatDateTimeForLog()} ❌ [任务${taskIndex}] 订单 ${task.orderId} 处理失败 (耗时: ${taskTime}ms):`,
            error
          );
          throw error;
        } finally {
          semaphore[slotIndex] = null; // 释放槽位
          console.log(`${formatDateTimeForLog()} 🔓 [任务${taskIndex}] 释放槽位${slotIndex + 1}`);
        }
      });

      promises.push(promise);
    }

    return await Promise.allSettled(promises);
  }

  /**
   * 等待可用槽位 - 优化版本
   */
  private async waitForSlot(semaphore: any[]): Promise<number> {
    return new Promise(resolve => {
      const checkSlot = () => {
        const freeIndex = semaphore.findIndex(slot => slot === null);
        if (freeIndex !== -1) {
          semaphore[freeIndex] = true; // 占用槽位
          resolve(freeIndex);
        } else {
          setTimeout(checkSlot, 10); // 10ms后重试
        }
      };
      checkSlot();
    });
  }

  /**
   * 处理单个同步任务 - 重构版本，详细日志和错误处理
   */
  private async processSingleTask(task: SyncTask): Promise<void> {
    const startTime = Date.now();
    console.log(
      `${formatDateTimeForLog()} 🔄 [任务处理] 开始处理订单 ${task.orderId} (策略: ${task.strategy}, 优先级: ${task.priority}, 重试: ${task.retryCount})`
    );

    try {
      // 速率限制
      console.log(`${formatDateTimeForLog()} ⏱️ [任务处理] 订单 ${task.orderId} 应用速率限制...`);
      await this.applyRateLimit();

      // 执行同步
      console.log(`${formatDateTimeForLog()} 🔄 [任务处理] 订单 ${task.orderId} 开始同步...`);
      const { syncSingleOrderStatus } = await import('../../controller/order');
      const result = await syncSingleOrderStatus(task.orderId);

      const processingTime = Date.now() - startTime;

      if (result.success) {
        this.metrics.successCount++;
        this.metrics.totalProcessed++;
        this.metrics.lastProcessedAt = new Date();

        console.log(
          `${formatDateTimeForLog()} ✅ [任务处理] 订单 ${task.orderId} 同步成功 (耗时: ${processingTime}ms)`
        );

        // 记录成功事件
        this.monitor.recordEvent({
          type: 'success',
          orderId: task.orderId,
          strategy: task.strategy,
          processingTime,
          retryCount: task.retryCount
        });
      } else {
        throw new Error(result.message || '同步失败，未知原因');
      }
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.metrics.errorCount++;
      this.metrics.totalProcessed++;
      task.retryCount++;
      task.lastAttempt = new Date();

      console.error(
        `${formatDateTimeForLog()} ❌ [任务处理] 订单 ${task.orderId} 同步失败 (耗时: ${processingTime}ms, 重试: ${task.retryCount}):`,
        error
      );

      // 记录错误事件
      this.monitor.recordEvent({
        type: 'error',
        orderId: task.orderId,
        strategy: task.strategy,
        processingTime,
        retryCount: task.retryCount,
        error: error instanceof Error ? error.message : String(error)
      });

      // 重试逻辑
      const config = this.configManager.getConfig();
      if (task.retryCount < config.maxRetries) {
        // 降低优先级并重新加入队列
        task.priority = Math.max(1, task.priority - 1);
        const retryDelay = config.retryDelaySeconds * 1000 * 2 ** (task.retryCount - 1); // 指数退避

        console.log(
          `${formatDateTimeForLog()} 🔄 [任务处理] 订单 ${task.orderId} 将在 ${retryDelay / 1000}秒后重试 (第${task.retryCount}次重试)`
        );

        setTimeout(() => {
          this.syncQueue.push(task);
          console.log(`${formatDateTimeForLog()} 🔄 [任务处理] 订单 ${task.orderId} 已重新加入队列`);
        }, retryDelay);

        this.metrics.retryCount++;
      } else {
        console.error(`${formatDateTimeForLog()} ❌ [任务处理] 订单 ${task.orderId} 达到最大重试次数，放弃处理`);
      }
    } finally {
      this.metrics.totalProcessed++;

      // 更新平均时间
      const duration = Date.now() - startTime;
      this.metrics.averageTime = (this.metrics.averageTime + duration) / 2;
    }
  }

  /**
   * 应用速率限制
   */
  private async applyRateLimit(): Promise<void> {
    const config = this.configManager.getConfig();
    if (!config.enableRateLimit) return;

    const now = Date.now();
    const windowStart = Math.floor(now / 1000) * 1000; // 1秒窗口
    const currentCount = this.rateLimiter.get(windowStart.toString()) || 0;

    if (currentCount >= config.rateLimit) {
      // 等待到下一秒
      const waitTime = 1000 - (now % 1000);
      await new Promise(resolve => setTimeout(resolve, waitTime));
    } else {
      this.rateLimiter.set(windowStart.toString(), currentCount + 1);
    }

    // 清理旧的计数器
    for (const [key] of this.rateLimiter) {
      if (Number.parseInt(key) < now - 5000) {
        // 保留5秒内的记录
        this.rateLimiter.delete(key);
      }
    }
  }

  /**
   * 更新指标
   */
  private updateMetrics(): void {
    this.metrics.currentQueueSize = this.syncQueue.length;
    this.metrics.lastSyncTime = new Date();
    this.metrics.errorRate =
      this.metrics.totalProcessed > 0 ? this.metrics.errorCount / this.metrics.totalProcessed : 0;
  }

  /**
   * 获取同步指标
   */
  getMetrics(): SyncMetrics {
    return { ...this.metrics };
  }

  /**
   * 获取运行状态
   */
  getRunningStatus(): boolean {
    return this.isRunning;
  }

  /**
   * 重置指标
   */
  resetMetrics(): void {
    this.metrics = this.initMetrics();
  }

  /**
   * 手动添加同步任务
   */
  addTask(orderId: string, priority: number = 5, strategy: string = 'manual'): void {
    if (!this.syncQueue.find(task => task.orderId === orderId)) {
      this.syncQueue.push({
        orderId,
        priority,
        strategy,
        retryCount: 0
      });

      // 重新排序
      this.syncQueue.sort((a, b) => b.priority - a.priority);
    }
  }

  /**
   * 获取队列状态
   */
  getQueueStatus(): { total: number; byStrategy: Record<string, number> } {
    const byStrategy: Record<string, number> = {};

    for (const task of this.syncQueue) {
      byStrategy[task.strategy] = (byStrategy[task.strategy] || 0) + 1;
    }

    return {
      total: this.syncQueue.length,
      byStrategy
    };
  }
}

export default SyncScheduler;
