/**
 * 订单同步配置管理器
 * 管理自动同步的各种策略和配置
 */

export interface SyncStrategy {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  priority: number; // 1-10，数字越大优先级越高
  config: any;
}

// 订单状态配置
export interface OrderStatusConfig {
  status: number; // 订单状态
  name: string; // 状态名称
  enabled: boolean; // 是否同步
  priority: number; // 优先级 (1-10, 数字越大优先级越高)
  intervalMinutes: number; // 同步间隔（分钟）
  description: string; // 描述
}

// 基于商品订单量的优先级规则
export interface PriorityRule {
  id: string;
  name: string;
  enabled: boolean;
  priority: number;
  minOrderCount: number; // 最小订单数量阈值
  description: string;
}

export interface SyncConfig {
  // 基础配置
  enabled: boolean;
  autoStartOnBoot: boolean; // 服务器启动时自动开启
  maxConcurrency: number; // 最大并发数
  batchSize: number; // 批处理大小

  // 订单状态策略 - 核心功能
  orderStatusConfigs: OrderStatusConfig[];

  // 简化的优先级规则
  priorityRules: PriorityRule[];

  // 性能控制
  enableRateLimit: boolean;
  rateLimit: number; // 每秒请求数

  // 错误处理
  maxRetries: number;
  retryDelaySeconds: number;
  errorThreshold: number; // 错误率阈值

  // 时间限制
  maxOrderAgeHours: number; // 只同步多少小时内的订单
  minUpdateIntervalMinutes: number; // 最小更新间隔
}

export class SyncConfigManager {
  private static instance: SyncConfigManager;
  private config: SyncConfig;

  private constructor() {
    this.config = this.getDefaultConfig();
  }

  static getInstance(): SyncConfigManager {
    if (!SyncConfigManager.instance) {
      SyncConfigManager.instance = new SyncConfigManager();
    }
    return SyncConfigManager.instance;
  }

  private getDefaultConfig(): SyncConfig {
    return {
      // 基础配置
      enabled: true,
      autoStartOnBoot: true, // 默认启动时自动开启
      maxConcurrency: 10,
      batchSize: 50,

      // 订单状态策略配置 - 用户可自定义
      orderStatusConfigs: [
        {
          status: 2,
          name: '待考试',
          enabled: true,
          priority: 5,
          intervalMinutes: 120, // 2小时间隔
          description: '已提交待考试，同步间隔较长'
        },
        {
          status: 3,
          name: '进行中',
          enabled: true,
          priority: 9, // 最高优先级
          intervalMinutes: 30, // 30分钟间隔
          description: '考试进行中，需要频繁同步状态'
        },
        {
          status: 6,
          name: '处理失败',
          enabled: true,
          priority: 8,
          intervalMinutes: 15, // 15分钟间隔
          description: '处理失败需要频繁重试'
        }
        // 用户可以在前端添加更多自定义状态
      ],

      // 基于商品订单量的优先级规则
      priorityRules: [
        {
          id: 'popular_products',
          name: '热门商品',
          enabled: true,
          priority: 10,
          minOrderCount: 50,
          description: '订单量≥50的热门商品优先处理'
        },
        {
          id: 'standard_products',
          name: '普通商品',
          enabled: true,
          priority: 7,
          minOrderCount: 10,
          description: '订单量10-50的普通商品正常处理'
        },
        {
          id: 'new_products',
          name: '新商品',
          enabled: true,
          priority: 5,
          minOrderCount: 1,
          description: '订单量1-10的新商品低优先级'
        }
      ],

      // 性能控制
      enableRateLimit: true,
      rateLimit: 5, // 每秒5个请求

      // 错误处理
      maxRetries: 3,
      retryDelaySeconds: 60,
      errorThreshold: 0.15, // 15%错误率

      // 时间限制
      maxOrderAgeHours: 168, // 只同步7天内的订单
      minUpdateIntervalMinutes: 10 // 最小10分钟更新间隔
    };
  }

  getConfig(): SyncConfig {
    return { ...this.config };
  }

  /**
   * 临时更新配置数据（用于验证）
   */
  updateConfigData(newConfig: Partial<SyncConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('🔧 [配置管理] 临时更新配置数据用于验证:', {
      enableRateLimit: this.config.enableRateLimit,
      rateLimit: this.config.rateLimit,
      rateLimitType: typeof this.config.rateLimit
    });
  }

  async updateConfig(newConfig: Partial<SyncConfig>): Promise<{ schedulerRestarted: boolean; currentStatus: boolean }> {
    const oldConfig = { ...this.config };
    this.config = { ...this.config, ...newConfig };
    this.saveConfig();

    console.log('🔧 [配置管理] 配置已更新:', {
      enabled: { old: oldConfig.enabled, new: this.config.enabled },
      maxConcurrency: { old: oldConfig.maxConcurrency, new: this.config.maxConcurrency },
      batchSize: { old: oldConfig.batchSize, new: this.config.batchSize },
      rateLimit: { old: oldConfig.rateLimit, new: this.config.rateLimit }
    });

    // 检查是否需要重启调度器
    const needsRestart = this.shouldRestartScheduler(oldConfig, this.config);
    let schedulerRestarted = false;
    let currentStatus = false;

    if (needsRestart) {
      console.log('🔄 [配置管理] 配置变更需要重启调度器...');
      try {
        // 动态导入避免循环依赖
        const { default: SyncScheduler } = await import('./SyncScheduler');
        const scheduler = SyncScheduler.getInstance();

        // 停止当前调度器
        await scheduler.stop();
        console.log('⏹️ [配置管理] 调度器已停止');

        // 如果新配置启用了同步，重新启动
        if (this.config.enabled) {
          await scheduler.start();
          currentStatus = scheduler.getRunningStatus();
          console.log('▶️ [配置管理] 调度器已重启，状态:', currentStatus);
        }

        schedulerRestarted = true;
      } catch (error) {
        console.error('❌ [配置管理] 重启调度器失败:', error);
      }
    } else {
      console.log('ℹ️ [配置管理] 配置变更不需要重启调度器');
    }

    return { schedulerRestarted, currentStatus };
  }

  /**
   * 判断是否需要重启调度器
   */
  private shouldRestartScheduler(oldConfig: SyncConfig, newConfig: SyncConfig): boolean {
    // 关键配置变更需要重启
    const criticalChanges: (keyof SyncConfig)[] = [
      'enabled',
      'maxConcurrency',
      'batchSize',
      'enableRateLimit',
      'rateLimit',
      'orderStatusConfigs'
    ];

    for (const key of criticalChanges) {
      if (JSON.stringify(oldConfig[key]) !== JSON.stringify(newConfig[key])) {
        console.log(`🔍 [配置管理] 检测到关键配置变更: ${key}`);
        return true;
      }
    }

    return false;
  }

  // 兼容性方法 - 已废弃
  updateStrategy(strategyId: string, updates: Partial<SyncStrategy>): void {
    console.warn('updateStrategy方法已废弃，请使用新的配置管理');
  }

  addStrategy(strategy: SyncStrategy): void {
    console.warn('addStrategy方法已废弃，请使用新的配置管理');
  }

  removeStrategy(strategyId: string): void {
    console.warn('removeStrategy方法已废弃，请使用新的配置管理');
  }

  getEnabledStrategies(): SyncStrategy[] {
    // 返回空数组，使用新的状态配置
    return [];
  }

  private async saveConfig(): Promise<void> {
    try {
      // 保存到数据库配置表
      const { executeQuery } = await import('../../utils/db');
      await executeQuery(
        `INSERT INTO fd_config (config_key, config_value, config_group, description, is_system, create_time, update_time)
         VALUES ('sync_config', ?, 'sync', '订单同步配置', 0, NOW(), NOW())
         ON DUPLICATE KEY UPDATE config_value = VALUES(config_value), update_time = VALUES(update_time)`,
        [JSON.stringify(this.config)]
      );
    } catch (error) {
      console.error('保存同步配置失败:', error);
    }
  }

  async loadConfig(): Promise<void> {
    try {
      const { executeQuery } = await import('../../utils/db');
      const result = await executeQuery('SELECT config_value FROM fd_config WHERE config_key = ?', ['sync_config']);

      if (result.length > 0) {
        const savedConfig = JSON.parse(result[0].config_value);
        this.config = { ...this.getDefaultConfig(), ...savedConfig };
      }
    } catch (error) {
      console.error('加载同步配置失败:', error);
    }
  }

  // 验证配置有效性
  validateConfig(): { valid: boolean; errors: string[]; warnings: string[] } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 验证基础配置
    if (this.config.maxConcurrency < 1 || this.config.maxConcurrency > 100) {
      errors.push('最大并发数必须在1-100之间');
    }

    if (this.config.batchSize < 1 || this.config.batchSize > 1000) {
      errors.push('批处理大小必须在1-1000之间');
    }

    // 验证速率限制配置
    if (this.config.enableRateLimit) {
      if (this.config.rateLimit < 1 || this.config.rateLimit > 100) {
        errors.push('速率限制必须在1-100之间');
      }

      // 检查速率限制与并发数的合理性
      if (this.config.rateLimit > this.config.maxConcurrency * 2) {
        warnings.push(
          `速率限制(${this.config.rateLimit}/s)可能过高，建议不超过并发数(${this.config.maxConcurrency})的2倍`
        );
      }
    }

    // 验证批处理大小与并发数的合理性
    if (this.config.batchSize > this.config.maxConcurrency * 50) {
      warnings.push(
        `批处理大小(${this.config.batchSize})可能过大，建议不超过并发数(${this.config.maxConcurrency})的50倍`
      );
    }

    if (this.config.maxOrderAgeHours < 1) {
      errors.push('最大订单年龄必须大于0');
    }

    // 验证订单状态配置
    if (this.config.orderStatusConfigs) {
      const statusValues = new Set<number>();

      this.config.orderStatusConfigs.forEach((statusConfig, index) => {
        if (!statusConfig.status || statusConfig.status < 1) {
          errors.push(`状态配置${index + 1}: 状态值无效`);
        }

        // 检查重复状态
        if (statusValues.has(statusConfig.status)) {
          errors.push(`状态配置${index + 1}: 状态值${statusConfig.status}重复`);
        }
        statusValues.add(statusConfig.status);

        if (!statusConfig.intervalMinutes || statusConfig.intervalMinutes < 1) {
          errors.push(`状态配置${index + 1}: 同步间隔必须大于0分钟`);
        }

        if (statusConfig.intervalMinutes > 1440) {
          warnings.push(`状态配置${index + 1}: 同步间隔(${statusConfig.intervalMinutes}分钟)超过24小时，可能过长`);
        }

        if (statusConfig.priority < 1 || statusConfig.priority > 10) {
          errors.push(`状态配置${index + 1}: 优先级必须在1-10之间`);
        }
      });
    }

    console.log('🔍 [配置验证] 验证结果:', {
      valid: errors.length === 0,
      errors: errors.length,
      warnings: warnings.length,
      currentConfig: {
        maxConcurrency: this.config.maxConcurrency,
        batchSize: this.config.batchSize,
        rateLimit: this.config.rateLimit,
        enableRateLimit: this.config.enableRateLimit
      }
    });

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }
}

export default SyncConfigManager;
