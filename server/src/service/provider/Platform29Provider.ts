/**
 * 29平台服务商实现
 */

import type {
  ChangePasswordParams,
  ChangePasswordResult,
  CreateOrderParams,
  CreateOrderResult,
  GetCourseListResult,
  QueryCoursesParams,
  QueryCoursesResult,
  RefillOrderParams,
  RefillOrderResult,
  SyncOrderParams,
  SyncOrderResult
} from './BaseProvider';
import { BaseProvider } from './BaseProvider';

export class Platform29Provider extends BaseProvider {
  /**
   * 获取课程列表
   */
  async getCourseList(): Promise<GetCourseListResult> {
    try {
      this.log('info', '开始获取29平台课程列表');

      const url = `${this.config.api_url}/api.php?act=getclass`;
      const data = new URLSearchParams({
        uid: this.config.username,
        key: this.config.password
      });

      const response = await this.sendRequest(url, 'POST', data.toString(), {
        'Content-Type': 'application/x-www-form-urlencoded'
      });

      if (response.status !== 200) {
        throw new Error(`HTTP错误: ${response.status}`);
      }

      this.log('info', '29平台课程列表获取成功');
      return {
        success: true,
        message: '获取课程列表成功',
        data: response.data || []
      };
    } catch (error: any) {
      this.log('error', '获取29平台课程列表失败', error);
      return {
        success: false,
        message: error.message || '获取课程列表失败',
        error: 'API_ERROR'
      };
    }
  }

  /**
   * 查课接口
   */
  async queryCourses(params: QueryCoursesParams): Promise<QueryCoursesResult> {
    try {
      this.log('info', '开始29平台查课', { username: params.username, school: params.school });

      const url = `${this.config.api_url}/api.php?act=get`;
      const platformType = this.determinePlatformType(params);

      const schoolParam = params.school || '自动识别';

      this.log('info', '29平台查课参数', {
        platform: platformType,
        productInfo: params.product,
        username: params.username,
        school: schoolParam,
        originalSchool: params.school
      });

      // 验证必需参数
      if (!platformType || !params.username || !params.password) {
        throw new Error('缺少必需参数: platform, username, password');
      }

      const requestData = new URLSearchParams({
        uid: this.config.username,
        key: this.config.password,
        platform: platformType,
        user: params.username,
        pass: params.password,
        school: schoolParam,
        type: 'FD' // 添加必需的type参数
      });

      if (params.course_id) {
        requestData.append('kcid', params.course_id);
      }

      const response = await this.sendRequest(url, 'POST', requestData.toString(), {
        'Content-Type': 'application/x-www-form-urlencoded'
      });

      if (response.status !== 200) {
        throw new Error(`HTTP错误: ${response.status}`);
      }

      const result = this.parseQueryCoursesResponse(response.data);
      this.log('info', '29平台查课完成', { success: result.success });

      return result;
    } catch (error: any) {
      this.log('error', '29平台查课失败', error);
      return {
        success: false,
        message: error.message || '查课失败',
        error: 'QUERY_ERROR'
      };
    }
  }

  /**
   * 创建订单
   */
  async createOrder(params: CreateOrderParams): Promise<CreateOrderResult> {
    try {
      this.log('info', '开始29平台下单', {
        username: params.username,
        course_name: params.course_name,
        course_id: params.course_id,
        school: params.school
      });

      const platformType = this.determinePlatformType(params);
      const url = `${this.config.api_url}/api.php?act=add`;
      const requestData = new URLSearchParams({
        uid: this.config.username,
        key: this.config.password,
        platform: platformType,
        school: params.school || '自动识别',
        user: params.username,
        pass: params.password,
        kcname: params.course_name,
        kcid: params.course_id
      });

      this.log('info', '29平台下单请求参数', {
        url,
        platform: platformType,
        school: params.school || '自动识别',
        user: params.username,
        kcname: params.course_name,
        kcid: params.course_id,
        uid: this.config.username
      });

      const response = await this.sendRequest(url, 'POST', requestData.toString(), {
        'Content-Type': 'application/x-www-form-urlencoded'
      });

      this.log('info', '29平台下单HTTP响应', {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
        dataType: typeof response.data
      });

      if (response.status !== 200) {
        throw new Error(`HTTP错误: ${response.status}`);
      }

      const result = this.parseCreateOrderResponse(response.data);
      this.log('info', '29平台下单完成', {
        success: result.success,
        message: result.message,
        upstream_order_id: result.data?.upstream_order_id,
        error: result.error
      });

      return result;
    } catch (error: any) {
      this.log('error', '29平台下单异常', {
        error: error.message,
        stack: error.stack,
        params
      });
      return {
        success: false,
        message: error.message || '下单失败',
        error: 'CREATE_ORDER_ERROR'
      };
    }
  }

  /**
   * 同步订单状态
   */
  async syncOrderStatus(params: SyncOrderParams): Promise<SyncOrderResult> {
    try {
      this.log('info', '开始29平台同步订单状态', { upstream_order_id: params.upstream_order_id });

      const url = `${this.config.api_url}/api.php?act=chadanoid`;
      const requestData = new URLSearchParams({
        uid: this.config.username,
        key: this.config.password,
        yid: params.upstream_order_id
      });

      if (params.username) {
        requestData.append('username', params.username);
      }
      if (params.school) {
        requestData.append('school', params.school);
      }

      const response = await this.sendRequest(url, 'POST', requestData.toString(), {
        'Content-Type': 'application/x-www-form-urlencoded'
      });

      if (response.status !== 200) {
        throw new Error(`HTTP错误: ${response.status}`);
      }

      const result = this.parseSyncOrderResponse(response.data);
      this.log('info', '29平台同步订单状态完成', {
        success: result.success,
        status: result.data?.status,
        progress: result.data?.progress
      });

      return result;
    } catch (error: any) {
      this.log('error', '29平台同步订单状态失败', error);
      return {
        success: false,
        message: error.message || '同步订单状态失败',
        error: 'SYNC_ERROR'
      };
    }
  }

  /**
   * 补刷订单
   */
  async refillOrder(params: RefillOrderParams): Promise<RefillOrderResult> {
    try {
      this.log('info', '开始29平台补刷订单', { upstream_order_id: params.upstream_order_id });

      const url = `${this.config.api_url}/api.php?act=budan`;
      const requestData = new URLSearchParams({
        uid: this.config.username,
        key: this.config.password,
        id: params.upstream_order_id
      });

      const response = await this.sendRequest(url, 'POST', requestData.toString(), {
        'Content-Type': 'application/x-www-form-urlencoded'
      });

      if (response.status !== 200) {
        throw new Error(`HTTP错误: ${response.status}`);
      }

      const result = this.parseRefillOrderResponse(response.data);
      this.log('info', '29平台补刷订单完成', { success: result.success });

      return result;
    } catch (error: any) {
      this.log('error', '29平台补刷订单失败', error);
      return {
        success: false,
        message: error.message || '补刷订单失败',
        error: 'REFILL_ERROR'
      };
    }
  }

  /**
   * 修改密码
   */
  async changePassword(params: ChangePasswordParams): Promise<ChangePasswordResult> {
    try {
      this.log('info', '开始29平台修改密码', { upstream_order_id: params.upstream_order_id });

      const url = `${this.config.api_url}/api.php?act=xgmm`;
      const requestData = new URLSearchParams({
        uid: this.config.username,
        key: this.config.password,
        yid: params.upstream_order_id,
        pwd: params.new_password
      });

      const response = await this.sendRequest(url, 'POST', requestData.toString(), {
        'Content-Type': 'application/x-www-form-urlencoded'
      });

      if (response.status !== 200) {
        throw new Error(`HTTP错误: ${response.status}`);
      }

      const result = this.parseChangePasswordResponse(response.data);
      this.log('info', '29平台修改密码完成', { success: result.success });

      return result;
    } catch (error: any) {
      this.log('error', '29平台修改密码失败', error);
      return {
        success: false,
        message: error.message || '修改密码失败',
        error: 'CHANGE_PASSWORD_ERROR'
      };
    }
  }

  /**
   * 确定平台类型
   */
  private determinePlatformType(params: any): string {
    // 调试日志：显示接收到的参数
    this.log('info', '确定平台类型 - 接收到的参数', {
      hasProduct: Boolean(params.product),
      product: params.product,
      productNoun: params.product?.noun,
      productType: typeof params.product?.noun,
      productName: params.product?.name,
      allParams: params
    });

    // 优先使用商品的noun字段（对接参数）作为platform参数
    if (params.product && params.product.noun && params.product.noun !== '' && params.product.noun !== null) {
      const platformType = String(params.product.noun);
      this.log('info', '使用商品noun字段作为platform参数', { noun: platformType });
      return platformType;
    }

    // 其次使用统一的platform_cid作为platform参数
    if (
      params.product &&
      params.product.platform_cid &&
      params.product.platform_cid !== '' &&
      params.product.platform_cid !== null
    ) {
      const platformType = String(params.product.platform_cid);
      this.log('info', '使用platform_cid作为platform参数', { platform_cid: platformType });
      return platformType;
    }

    // 检查是否有getnoun字段可以使用
    if (params.product && params.product.getnoun && params.product.getnoun !== '' && params.product.getnoun !== null) {
      const platformType = String(params.product.getnoun);
      this.log('info', '使用商品getnoun字段作为platform参数', { getnoun: platformType });
      return platformType;
    }

    // 默认返回智慧树
    this.log('warn', '未找到有效的platform参数，使用默认值', {
      params,
      productFields: params.product ? Object.keys(params.product) : 'no product'
    });
    return 'zhihuishu';
  }

  /**
   * 解析查课响应
   */
  private parseQueryCoursesResponse(data: any): QueryCoursesResult {
    try {
      const responseData = typeof data === 'string' ? JSON.parse(data) : data;

      // 确保响应数据是数组格式
      const dataArray = Array.isArray(responseData) ? responseData : [responseData];

      // 检查是否有成功的课程数据
      const successCourses = dataArray.filter(item => item.code === 1 || item.code === '1');
      const failedCourses = dataArray.filter(item => item.code !== 1 && item.code !== '1');

      this.log('info', '29平台查课响应解析', {
        total: dataArray.length,
        success: successCourses.length,
        failed: failedCourses.length,
        failedMessages: failedCourses.map(item => item.msg)
      });

      // 如果有成功的课程，返回成功结果
      if (successCourses.length > 0) {
        return {
          success: true,
          message: `查课成功，找到 ${successCourses.length} 门课程`,
          data: successCourses
        };
      }

      // 如果没有成功的课程，返回失败结果
      const errorMessage = failedCourses.length > 0 ? failedCourses[0].msg || '查课失败' : '未找到课程数据';

      return {
        success: false,
        message: errorMessage,
        error: 'NO_COURSES_FOUND',
        data: dataArray // 仍然返回原始数据供调试
      };
    } catch (error) {
      this.log('error', '29平台查课响应解析失败', error);
      return {
        success: false,
        message: '查课响应解析失败',
        error: 'PARSE_ERROR'
      };
    }
  }

  /**
   * 解析下单响应
   */
  private parseCreateOrderResponse(data: any): CreateOrderResult {
    try {
      // 记录原始响应数据用于调试
      this.log('info', '29平台下单原始响应', { rawData: data, dataType: typeof data });

      const responseData = typeof data === 'string' ? JSON.parse(data) : data;

      this.log('info', '29平台下单解析后响应', {
        code: responseData.code,
        msg: responseData.msg,
        id: responseData.id,
        fullResponse: responseData
      });

      if (responseData.code === '0' || responseData.code === 0) {
        return {
          success: true,
          message: responseData.msg || '下单成功',
          data: {
            upstream_order_id: responseData.id
          }
        };
      }
      return {
        success: false,
        message: responseData.msg || '下单失败',
        error: 'ORDER_FAILED'
      };
    } catch (error) {
      this.log('error', '29平台下单响应解析失败', { error, rawData: data });
      return {
        success: false,
        message: '下单响应解析失败',
        error: 'PARSE_ERROR'
      };
    }
  }

  /**
   * 解析同步订单响应
   */
  private parseSyncOrderResponse(data: any): SyncOrderResult {
    try {
      const responseData = typeof data === 'string' ? JSON.parse(data) : data;

      if (responseData.code === '1' || responseData.code === 1) {
        const orderData = Array.isArray(responseData.data) ? responseData.data[0] : responseData.data;
        return {
          success: true,
          message: '同步成功',
          data: {
            status: orderData?.status || '未知',
            progress: Number.parseInt(orderData?.process) || 0
          }
        };
      }
      return {
        success: false,
        message: responseData.msg || '同步失败',
        error: 'SYNC_FAILED'
      };
    } catch {
      return {
        success: false,
        message: '同步响应解析失败',
        error: 'PARSE_ERROR'
      };
    }
  }

  /**
   * 解析补刷响应
   */
  private parseRefillOrderResponse(data: any): RefillOrderResult {
    try {
      const responseData = typeof data === 'string' ? JSON.parse(data) : data;

      return {
        success: responseData.code === '0' || responseData.code === 0,
        message: responseData.msg || (responseData.code === '0' ? '补刷成功' : '补刷失败'),
        data: responseData
      };
    } catch {
      return {
        success: false,
        message: '补刷响应解析失败',
        error: 'PARSE_ERROR'
      };
    }
  }

  /**
   * 解析修改密码响应
   */
  private parseChangePasswordResponse(data: any): ChangePasswordResult {
    try {
      const responseData = typeof data === 'string' ? JSON.parse(data) : data;

      return {
        success: responseData.code === '0' || responseData.code === 0,
        message: responseData.msg || (responseData.code === '0' ? '修改密码成功' : '修改密码失败'),
        data: responseData
      };
    } catch {
      return {
        success: false,
        message: '修改密码响应解析失败',
        error: 'PARSE_ERROR'
      };
    }
  }
}
