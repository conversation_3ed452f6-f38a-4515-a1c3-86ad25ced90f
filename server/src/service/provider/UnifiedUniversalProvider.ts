/**
 * 统一通用货源适配器
 * 基于统一配置的货源对接实现，替代原有的UniversalProvider
 */

import type { InterfaceConfig, ProviderUnifiedConfig } from '../../types/UnifiedConfig';
import type { ProjectStandardFields } from '../../types/StandardFields';
import { InterfaceType } from '../../types/StandardFields';
import { providerHttpPool } from '../../utils/httpPool';
import { formatDateTimeForLog } from '../../utils/timeUtils';
import { UnifiedMappingEngine } from './UnifiedMappingEngine';
import { BaseProvider } from './BaseProvider';
import type {
  ChangePasswordParams,
  ChangePasswordResult,
  CreateOrderParams,
  CreateOrderResult,
  GetBalanceResult,
  GetCourseListResult,
  ProviderConfig,
  QueryCoursesParams,
  QueryCoursesResult,
  RefillOrderParams,
  RefillOrderResult,
  SyncOrderParams,
  SyncOrderResult
} from './BaseProvider';

export class UnifiedUniversalProvider extends BaseProvider {
  private unifiedConfig: ProviderUnifiedConfig | null = null;
  private customCodeCache: Map<string, Function> = new Map();

  constructor(config: ProviderConfig) {
    super(config);
  }

  /**
   * 加载统一配置
   */
  private async loadUnifiedConfig(): Promise<void> {
    if (this.unifiedConfig) return; // 已加载

    try {
      console.log(
        `${formatDateTimeForLog()} 加载统一配置，货源ID:`,
        this.config.provider_id,
        '类型:',
        typeof this.config.provider_id
      );
      this.unifiedConfig = await UnifiedMappingEngine.getProviderConfig(this.config.provider_id);

      if (!this.unifiedConfig) {
        console.error(`${formatDateTimeForLog()} 货源 ${this.config.provider_id} 的统一配置不存在或未迁移`);
        throw new Error(`货源 ${this.config.provider_id} 的统一配置不存在或未迁移`);
      }

      // 预编译自定义代码
      for (const [interfaceType, interfaceConfig] of Object.entries(this.unifiedConfig.interfaces)) {
        if (interfaceConfig.custom_code) {
          try {
            const customFunction = new Function('params', 'config', 'utils', interfaceConfig.custom_code);
            this.customCodeCache.set(interfaceType, customFunction);
          } catch (error) {
            console.error(`${formatDateTimeForLog()} 编译自定义代码失败 (${interfaceType}):`, error);
          }
        }
      }

      console.log(`加载了货源 ${this.unifiedConfig.provider_info.provider_name} 的统一配置`);
    } catch (error) {
      console.error('加载统一配置失败:', error);
      throw error;
    }
  }

  /**
   * 获取接口配置
   */
  private async getInterfaceConfig(interfaceType: InterfaceType): Promise<InterfaceConfig> {
    await this.loadUnifiedConfig();

    if (!this.unifiedConfig?.interfaces[interfaceType]) {
      throw new Error(`接口 ${interfaceType} 未配置或未启用`);
    }

    const config = this.unifiedConfig.interfaces[interfaceType];
    if (!config.enabled) {
      throw new Error(`接口 ${interfaceType} 已禁用`);
    }

    return config;
  }

  /**
   * 执行通用接口调用
   */
  private async executeInterface(
    interfaceType: InterfaceType,
    standardFields: Partial<ProjectStandardFields>
  ): Promise<any> {
    try {
      // 1. 获取接口配置
      const interfaceConfig = await this.getInterfaceConfig(interfaceType);

      // 2. 获取认证信息
      const authInfo = this.unifiedConfig?.auth;
      if (!authInfo) {
        throw new Error('认证信息未配置');
      }

      // 3. 标准字段映射为货源字段
      const requestMappingResult = UnifiedMappingEngine.mapStandardToProvider(
        standardFields,
        interfaceConfig.request_mapping,
        authInfo
      );

      if (!requestMappingResult.success) {
        throw new Error(`请求映射失败: ${requestMappingResult.error}`);
      }

      const providerFields = requestMappingResult.data;

      // 4. 执行自定义代码（如果有）
      if (interfaceConfig.custom_code && this.customCodeCache.has(interfaceType)) {
        const customFn = this.customCodeCache.get(interfaceType)!;
        try {
          const customResult = customFn(providerFields, interfaceConfig, {
            http: providerHttpPool,
            console
          });

          if (customResult && typeof customResult === 'object') {
            Object.assign(providerFields, customResult);
          }
        } catch (error) {
          console.warn(`自定义代码执行失败 (${interfaceType}):`, error);
        }
      }

      // 5. 构建请求URL
      const baseUrl = this.unifiedConfig?.provider_info.base_url || '';
      const url = interfaceConfig.http.endpoint.startsWith('http')
        ? interfaceConfig.http.endpoint
        : baseUrl + interfaceConfig.http.endpoint;

      // 6. 发送HTTP请求
      const httpConfig: any = {
        method: interfaceConfig.http.method,
        url,
        timeout: interfaceConfig.http.timeout || 30000,
        headers: {
          'Content-Type': this.getContentType(interfaceConfig.http.content_type),
          ...interfaceConfig.http.headers
        }
      };

      if (interfaceConfig.http.method === 'GET') {
        httpConfig.params = providerFields;
      } else if (interfaceConfig.http.content_type === 'json') {
        httpConfig.data = providerFields;
      } else {
        // form格式
        const formData = new URLSearchParams();
        for (const [key, value] of Object.entries(providerFields)) {
          formData.append(key, String(value));
        }
        httpConfig.data = formData;
      }

      const response = await providerHttpPool.request(httpConfig);

      // 7. 货源响应映射为标准字段
      const responseMappingResult = UnifiedMappingEngine.mapProviderToStandard(
        response.data,
        interfaceConfig.response_mapping
      );

      if (!responseMappingResult.success) {
        throw new Error(`响应映射失败: ${responseMappingResult.error}`);
      }

      // 返回映射后的数据和原始数据
      return {
        ...responseMappingResult.data,
        rawData: response.data // 保留原始响应数据
      };
    } catch (error: any) {
      console.error(`接口调用失败 (${interfaceType}):`, error.message);

      // 返回标准化的错误响应
      return {
        success: false,
        message: error.message || '接口调用失败',
        error: error.message,
        data: null
      };
    }
  }

  /**
   * 获取Content-Type
   */
  private getContentType(contentType?: string): string {
    switch (contentType) {
      case 'json':
        return 'application/json';
      case 'xml':
        return 'application/xml';
      case 'form':
      default:
        return 'application/x-www-form-urlencoded';
    }
  }

  /**
   * 查询课程
   */
  async queryCourses(params: QueryCoursesParams): Promise<QueryCoursesResult> {
    const standardFields: Partial<ProjectStandardFields> = {
      username: params.username,
      password: params.password,
      school: params.school,
      // 添加平台标识
      platform: params.product?.platform_cid || params.product?.noun
    };

    const result = await this.executeInterface(InterfaceType.QUERY, standardFields);

    return {
      success: result.success,
      message: result.message,
      data: result.data || []
    };
  }

  /**
   * 创建订单
   */
  async createOrder(params: CreateOrderParams): Promise<CreateOrderResult> {
    console.log('UnifiedUniversalProvider.createOrder 调用参数:', JSON.stringify(params, null, 2));

    const standardFields: Partial<ProjectStandardFields> = {
      username: params.username,
      password: params.password,
      school: params.school,
      course_id: params.course_id,
      course_name: params.course_name,
      // 添加平台标识
      platform: params.product?.platform_cid || params.product?.noun
    };

    console.log(`${formatDateTimeForLog()} 下单标准字段:`, JSON.stringify(standardFields, null, 2));

    const result = await this.executeInterface(InterfaceType.ORDER, standardFields);

    console.log(`${formatDateTimeForLog()} 下单executeInterface 返回结果:`, JSON.stringify(result, null, 2));

    return {
      success: result.success,
      message: result.message,
      data: {
        upstream_order_id: result.data?.upstream_order_id,
        estimated_time: result.data?.estimated_time
      }
    };
  }

  /**
   * 同步订单状态
   */
  async syncOrderStatus(params: SyncOrderParams): Promise<SyncOrderResult> {
    const standardFields: Partial<ProjectStandardFields> = {
      upstream_order_id: params.upstream_order_id,
      username: params.username,
      school: params.school
    };

    const result = await this.executeInterface(InterfaceType.SYNC, standardFields);

    // 返回完整的原始数据和处理后的数据
    return {
      success: result.success,
      message: result.message,
      data: result.data, // 返回完整的原始数据
      rawData: result.rawData // 返回未处理的原始响应
    };
  }

  /**
   * 补刷订单
   */
  async refillOrder(params: RefillOrderParams): Promise<RefillOrderResult> {
    const standardFields: Partial<ProjectStandardFields> = {
      upstream_order_id: params.upstream_order_id
    };

    const result = await this.executeInterface(InterfaceType.REFILL, standardFields);

    return {
      success: result.success,
      message: result.message,
      data: {
        new_upstream_order_id: result.data?.new_upstream_order_id
      }
    };
  }

  /**
   * 修改密码
   */
  async changePassword(params: ChangePasswordParams): Promise<ChangePasswordResult> {
    const standardFields: Partial<ProjectStandardFields> = {
      upstream_order_id: params.upstream_order_id,
      new_password: params.new_password
    };

    const result = await this.executeInterface(InterfaceType.CHANGE_PASSWORD, standardFields);

    return {
      success: result.success,
      message: result.message
    };
  }

  /**
   * 查询余额
   */
  async getBalance(): Promise<GetBalanceResult> {
    const result = await this.executeInterface(InterfaceType.GET_BALANCE, {});

    return {
      success: result.success,
      message: result.message,
      data: {
        balance: result.data?.balance || 0,
        currency: result.data?.currency || 'CNY'
      }
    };
  }

  /**
   * 获取课程列表
   */
  async getCourseList(): Promise<GetCourseListResult> {
    const result = await this.executeInterface(InterfaceType.GET_COURSES, {});

    return {
      success: result.success,
      message: result.message,
      data: result.data || []
    };
  }
}
