/**
 * 统一字段映射引擎
 * 负责标准字段与货源字段之间的双向映射转换
 * 支持统一配置格式，替代原有的FieldMappingEngine
 */

import { executeQuery } from '../../utils/db';
import type { InterfaceType, ProjectStandardFields } from '../../types/StandardFields';
import type {
  InterfaceConfig,
  ProviderUnifiedConfig,
  RequestMappingConfig,
  ResponseMappingConfig,
  SuccessCondition,
  TransformFunction
} from '../../types/UnifiedConfig';
import { FieldMappingConfig } from '../../types/UnifiedConfig';

/**
 * 映射结果
 */
export interface MappingResult {
  success: boolean;
  data?: any;
  error?: string;
  warnings?: string[];
}

/**
 * 统一映射引擎类
 */
export class UnifiedMappingEngine {
  // 数据转换函数注册表
  private static transformFunctions: Map<string, (value: any) => any> = new Map([
    ['toString', (value: any) => String(value)],
    ['toNumber', (value: any) => Number(value)],
    ['toBoolean', (value: any) => Boolean(value)],
    ['toUpperCase', (value: string) => value?.toUpperCase()],
    ['toLowerCase', (value: string) => value?.toLowerCase()],
    ['trim', (value: string) => value?.trim()],
    ['encodeBase64', (value: string) => Buffer.from(value).toString('base64')],
    ['decodeBase64', (value: string) => Buffer.from(value, 'base64').toString()],
    [
      'md5',
      (value: string) => {
        const crypto = require('node:crypto');
        return crypto.createHash('md5').update(value).digest('hex');
      }
    ],
    [
      'sha256',
      (value: string) => {
        const crypto = require('node:crypto');
        return crypto.createHash('sha256').update(value).digest('hex');
      }
    ],
    ['urlEncode', (value: string) => encodeURIComponent(value)],
    ['urlDecode', (value: string) => decodeURIComponent(value)],
    ['timestamp', () => Math.floor(Date.now() / 1000)],
    [
      'uuid',
      () => {
        const crypto = require('node:crypto');
        return crypto.randomUUID();
      }
    ]
  ]);

  /**
   * 获取货源的统一配置
   */
  static async getProviderConfig(providerId: number): Promise<ProviderUnifiedConfig | null> {
    try {
      const query = `
        SELECT unified_config, is_unified_config, config_version
        FROM fd_provider
        WHERE provider_id = ? AND status = 1
      `;

      const result = await executeQuery(query, [providerId]);

      if (result.length === 0) {
        return null;
      }

      const row = result[0];

      if (!row.is_unified_config || !row.unified_config) {
        // 如果没有统一配置，返回null（应该先执行迁移）
        console.warn(`货源 ${providerId} 尚未迁移到统一配置`);
        return null;
      }

      return typeof row.unified_config === 'string' ? JSON.parse(row.unified_config) : row.unified_config;
    } catch (error) {
      console.error('获取货源统一配置失败:', error);
      return null;
    }
  }

  /**
   * 获取接口配置
   */
  static async getInterfaceConfig(providerId: number, interfaceType: InterfaceType): Promise<InterfaceConfig | null> {
    const providerConfig = await this.getProviderConfig(providerId);

    if (!providerConfig || !providerConfig.interfaces) {
      return null;
    }

    return providerConfig.interfaces[interfaceType] || null;
  }

  /**
   * 标准字段映射为货源字段（请求映射）
   */
  static mapStandardToProvider(
    standardFields: Partial<ProjectStandardFields>,
    requestMapping: RequestMappingConfig,
    authInfo?: { api_uid: string; api_key: string; token?: string }
  ): MappingResult {
    try {
      const providerFields: any = {};
      const warnings: string[] = [];

      // 合并认证信息
      const allFields = {
        ...standardFields,
        ...(authInfo || {})
      };

      // 应用字段映射
      for (const [standardField, value] of Object.entries(allFields)) {
        if (value === undefined || value === null || value === '') continue;

        const mapping = requestMapping[standardField];
        if (!mapping) continue;

        let mappedValue = value;

        // 应用数据转换
        if (mapping.transform) {
          try {
            mappedValue = this.applyTransform(value, mapping.transform);
          } catch (error) {
            warnings.push(`字段 ${standardField} 转换失败: ${error}`);
            continue;
          }
        }

        providerFields[mapping.provider_field] = mappedValue;
      }

      // 应用默认值
      for (const [standardField, mapping] of Object.entries(requestMapping)) {
        if (mapping.default_value !== undefined && !providerFields[mapping.provider_field]) {
          let defaultValue = mapping.default_value;

          // 处理动态默认值
          if (typeof defaultValue === 'string' && defaultValue.includes('${')) {
            defaultValue = this.resolveDynamicValue(defaultValue, allFields);
          }

          providerFields[mapping.provider_field] = defaultValue;
        }
      }

      return {
        success: true,
        data: providerFields,
        warnings: warnings.length > 0 ? warnings : undefined
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || '请求映射失败'
      };
    }
  }

  /**
   * 货源响应映射为标准字段（响应映射）
   */
  static mapProviderToStandard(providerResponse: any, responseMapping: ResponseMappingConfig): MappingResult {
    try {
      // 检查响应是否成功
      const isSuccess = this.checkSuccessCondition(providerResponse, responseMapping.success_condition);

      // 获取消息
      const message = this.getNestedValue(providerResponse, responseMapping.message_field) || '';

      const result: any = {
        success: isSuccess,
        message
      };

      if (isSuccess) {
        // 获取数据
        let sourceData = providerResponse;

        if (responseMapping.data_field) {
          sourceData = this.getNestedValue(providerResponse, responseMapping.data_field);

          // 如果数据字段是数组，处理数组中的每个元素
          if (Array.isArray(sourceData)) {
            result.data = sourceData.map(item => this.mapResponseFields(item, responseMapping.field_mapping));
          } else if (sourceData && typeof sourceData === 'object') {
            result.data = this.mapResponseFields(sourceData, responseMapping.field_mapping);
          } else {
            result.data = sourceData;
          }
        } else {
          // 直接映射根级字段
          result.data = this.mapResponseFields(providerResponse, responseMapping.field_mapping);
        }
      }

      return {
        success: true,
        data: result
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || '响应映射失败'
      };
    }
  }

  /**
   * 映射响应字段
   */
  private static mapResponseFields(sourceData: any, fieldMapping: any): any {
    const mappedData: any = {};

    for (const [standardField, mapping] of Object.entries(fieldMapping)) {
      if (typeof mapping === 'string') {
        // 简单字符串映射
        const value = this.getNestedValue(sourceData, mapping);
        if (value !== undefined) {
          mappedData[standardField] = value;
        }
      } else if (mapping && typeof mapping === 'object') {
        // 复杂对象映射
        const mappingObj = mapping as any;
        const value = this.getNestedValue(sourceData, mappingObj.provider_field);

        if (value !== undefined) {
          let mappedValue = value;

          // 应用转换
          if (mappingObj.transform) {
            try {
              mappedValue = this.applyTransform(value, mappingObj.transform);
            } catch (error) {
              console.warn(`字段 ${standardField} 转换失败:`, error);
              mappedValue = mappingObj.default_value !== undefined ? mappingObj.default_value : value;
            }
          }

          mappedData[standardField] = mappedValue;
        } else if (mappingObj.default_value !== undefined) {
          mappedData[standardField] = mappingObj.default_value;
        }
      }
    }

    return mappedData;
  }

  /**
   * 检查成功条件
   */
  private static checkSuccessCondition(response: any, condition: SuccessCondition): boolean {
    const actualValue = this.getNestedValue(response, condition.field);
    const expectedValue = condition.value;

    switch (condition.condition) {
      case 'eq':
        return actualValue === expectedValue;
      case 'ne':
        return actualValue !== expectedValue;
      case 'gt':
        return Number(actualValue) > Number(expectedValue);
      case 'gte':
        return Number(actualValue) >= Number(expectedValue);
      case 'lt':
        return Number(actualValue) < Number(expectedValue);
      case 'lte':
        return Number(actualValue) <= Number(expectedValue);
      case 'in':
        return Array.isArray(expectedValue) && expectedValue.includes(actualValue);
      case 'contains':
        return String(actualValue).includes(String(expectedValue));
      default:
        return actualValue === expectedValue;
    }
  }

  /**
   * 获取嵌套字段值
   */
  private static getNestedValue(obj: any, path: string): any {
    if (!obj || !path) return undefined;

    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  /**
   * 应用数据转换
   */
  private static applyTransform(value: any, transform: TransformFunction): any {
    if (typeof transform === 'string') {
      const transformFn = this.transformFunctions.get(transform);
      if (transformFn) {
        return transformFn(value);
      }
    }

    // 如果是自定义转换函数代码 - 安全检查
    if (typeof transform === 'string' && transform.includes('function')) {
      try {
        // 安全检查：禁止危险的代码模式
        const dangerousPatterns = [
          /require\s*\(/,
          /process\./,
          /global\./,
          /eval\s*\(/,
          /Function\s*\(/,
          /setTimeout|setInterval/,
          /fs\.|path\.|os\./,
          /import\s+/,
          /export\s+/,
          /console\./,
          /Buffer\./
        ];

        const hasDangerousPattern = dangerousPatterns.some(pattern => pattern.test(transform));
        if (hasDangerousPattern) {
          console.warn('自定义转换函数包含危险代码模式，已拒绝执行');
          return value;
        }

        // 限制代码长度
        if (transform.length > 1000) {
          console.warn('自定义转换函数代码过长，已拒绝执行');
          return value;
        }

        const fn = new Function('value', transform);
        return fn(value);
      } catch (error) {
        console.warn('自定义转换函数执行失败:', error);
      }
    }

    return value;
  }

  /**
   * 解析动态值
   */
  private static resolveDynamicValue(template: string, context: any): any {
    return template.replace(/\$\{([^}]+)\}/g, (match, key) => {
      return this.getNestedValue(context, key) || match;
    });
  }

  /**
   * 注册自定义转换函数
   */
  static registerTransformFunction(name: string, fn: (value: any) => any): void {
    this.transformFunctions.set(name, fn);
  }

  /**
   * 验证映射配置
   */
  static validateMappingConfig(config: InterfaceConfig): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 验证HTTP配置
    if (!config.http?.endpoint) {
      errors.push('HTTP端点不能为空');
    }

    if (!config.http?.method) {
      errors.push('HTTP方法不能为空');
    }

    // 验证请求映射
    if (!config.request_mapping) {
      errors.push('请求映射配置不能为空');
    }

    // 验证响应映射
    if (!config.response_mapping) {
      errors.push('响应映射配置不能为空');
    } else {
      if (!config.response_mapping.success_condition) {
        errors.push('成功条件配置不能为空');
      }

      if (!config.response_mapping.message_field) {
        errors.push('消息字段配置不能为空');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }
}
