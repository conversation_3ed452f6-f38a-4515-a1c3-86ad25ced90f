/**
 * 服务商对接基类
 * 定义所有服务商必须实现的标准接口
 */

export interface ProviderConfig {
  provider_id: number;
  code: string;
  name: string;
  api_url: string;
  username: string;
  password: string;
  token?: string;
  api_config?: any;
  status: number;
}

export interface QueryCoursesParams {
  school?: string; // 学校名称（可选）
  username: string; // 学生账号
  password: string; // 学生密码
  course_id?: string; // 课程ID（可选）
  product?: {
    // 商品信息（可选）
    platform_cid?: string;
    noun?: string;
  };
}

export interface QueryCoursesResult {
  success: boolean;
  message: string;
  data?: CourseInfo[];
  error?: string;
}

export interface CourseInfo {
  course_id: string;
  course_name: string;
  platform_type: string;
  progress?: number;
  status?: string;
  [key: string]: any;
}

export interface CreateOrderParams {
  school?: string;
  username: string;
  password: string;
  course_id: string;
  course_name: string;
  product?: {
    // 商品信息（可选）
    platform_cid?: string;
    noun?: string;
    getnoun?: string;
    name?: string;
  };
}

export interface CreateOrderResult {
  success: boolean;
  message: string;
  data?: {
    upstream_order_id: string;
    [key: string]: any;
  };
  error?: string;
}

export interface SyncOrderParams {
  upstream_order_id: string;
  username?: string;
  school?: string;
}

export interface SyncOrderResult {
  success: boolean;
  message: string;
  data?: {
    status: string;
    progress: number;
    [key: string]: any;
  };
  rawData?: any;
  error?: string;
}

export interface RefillOrderParams {
  upstream_order_id: string;
}

export interface RefillOrderResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export interface ChangePasswordParams {
  upstream_order_id: string;
  new_password: string;
}

export interface ChangePasswordResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export interface GetBalanceResult {
  success: boolean;
  message: string;
  data?: {
    balance: number;
    userName?: string;
    lastUpdate?: string;
    platform?: string;
    [key: string]: any;
  };
  error?: string;
}

export interface GetCourseListResult {
  success: boolean;
  message: string;
  data?: CourseInfo[];
  error?: string;
}

/**
 * 服务商对接抽象基类
 * 所有服务商实现都必须继承此类并实现所有抽象方法
 */
export abstract class BaseProvider {
  protected config: ProviderConfig;

  constructor(config: ProviderConfig) {
    this.config = config;
  }

  /**
   * 获取服务商代码
   */
  getProviderCode(): string {
    return this.config.code;
  }

  /**
   * 获取服务商名称
   */
  getProviderName(): string {
    return this.config.name;
  }

  /**
   * 检查服务商是否可用
   */
  isAvailable(): boolean {
    return this.config.status === 1;
  }

  /**
   * 获取课程列表（可选实现）
   */
  async getCourseList(): Promise<GetCourseListResult> {
    return {
      success: false,
      message: '该服务商不支持获取课程列表',
      error: 'NOT_SUPPORTED'
    };
  }

  /**
   * 查询余额（可选实现）
   */
  async getBalance(): Promise<GetBalanceResult> {
    return {
      success: false,
      message: '该服务商不支持余额查询',
      error: 'NOT_SUPPORTED'
    };
  }

  /**
   * 查课接口（必须实现）
   */
  abstract queryCourses(params: QueryCoursesParams): Promise<QueryCoursesResult>;

  /**
   * 创建订单接口（必须实现）
   */
  abstract createOrder(params: CreateOrderParams): Promise<CreateOrderResult>;

  /**
   * 同步订单状态接口（必须实现）
   */
  abstract syncOrderStatus(params: SyncOrderParams): Promise<SyncOrderResult>;

  /**
   * 补刷订单接口（必须实现）
   */
  abstract refillOrder(params: RefillOrderParams): Promise<RefillOrderResult>;

  /**
   * 修改密码接口（必须实现）
   */
  abstract changePassword(params: ChangePasswordParams): Promise<ChangePasswordResult>;

  /**
   * 发送HTTP请求的通用方法
   */
  protected async sendRequest(
    url: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    data?: any,
    headers?: Record<string, string>,
    timeout: number = 30000
  ): Promise<any> {
    // 使用连接池发送请求
    const { providerHttpPool } = require('../../utils/httpPool');

    try {
      const response = await providerHttpPool.request({
        method,
        url,
        data,
        headers: {
          'User-Agent': 'FreeDom/2.0',
          ...headers
        },
        timeout
      });

      return response;
    } catch (error: any) {
      throw new Error(`网络请求失败: ${error.message}`);
    }
  }

  /**
   * 记录日志的通用方法
   */
  protected log(level: 'info' | 'warn' | 'error', message: string, data?: any): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${this.config.code}] ${message}`;

    if (data) {
      console[level](logMessage, data);
    } else {
      console[level](logMessage);
    }
  }
}
