/**
 * 服务商工厂类
 * 负责创建和管理不同类型的服务商实例
 */

import { executeQuery } from '../../utils/db';
import { formatDateTimeForLog } from '../../utils/timeUtils';
import type { BaseProvider, ProviderConfig } from './BaseProvider';
import { Platform29Provider } from './Platform29Provider';
import { UnifiedUniversalProvider } from './UnifiedUniversalProvider';

/**
 * 支持的服务商类型枚举
 */
export enum ProviderType {
  PLATFORM_29 = '29pt',
  UNIVERSAL = 'universal' // 通用适配器
  // 后续可以添加更多服务商类型
  // PLATFORM_XX = 'xxpt',
  // PLATFORM_YY = 'yypt'
}

/**
 * 服务商工厂类
 */
export class ProviderFactory {
  private static providerInstances: Map<number, BaseProvider> = new Map();

  /**
   * 根据服务商ID创建服务商实例
   */
  static async createProvider(providerId: number): Promise<BaseProvider | null> {
    try {
      // 检查缓存
      if (this.providerInstances.has(providerId)) {
        const cachedProvider = this.providerInstances.get(providerId)!;
        if (cachedProvider.isAvailable()) {
          return cachedProvider;
        }
        // 如果缓存的服务商不可用，移除缓存
        this.providerInstances.delete(providerId);
      }

      // 从数据库获取服务商配置
      const config = await this.getProviderConfig(providerId);
      if (!config) {
        console.error(`服务商配置不存在: ${providerId}`);
        return null;
      }

      // 根据服务商代码创建对应的实例
      const provider = this.createProviderInstance(config);
      if (!provider) {
        console.error(`不支持的服务商类型: ${config.code}`);
        return null;
      }

      // 缓存实例
      this.providerInstances.set(providerId, provider);

      console.log(`✅ 服务商实例创建成功: ${config.name} (${config.code})`);
      return provider;
    } catch (error) {
      console.error(`创建服务商实例失败 (ID: ${providerId}):`, error);
      return null;
    }
  }

  /**
   * 根据服务商代码创建服务商实例
   */
  static async createProviderByCode(providerCode: string): Promise<BaseProvider | null> {
    try {
      const query = 'SELECT * FROM fd_provider WHERE code = ? AND status = 1';
      const result = await executeQuery(query, [providerCode]);

      if (result.length === 0) {
        console.error(`服务商不存在或已禁用: ${providerCode}`);
        return null;
      }

      return this.createProvider(result[0].provider_id);
    } catch (error) {
      console.error(`根据代码创建服务商实例失败 (${providerCode}):`, error);
      return null;
    }
  }

  /**
   * 获取所有可用的服务商实例
   */
  static async getAllProviders(): Promise<BaseProvider[]> {
    try {
      const query = 'SELECT provider_id FROM fd_provider WHERE status = 1';
      const result = await executeQuery(query);

      const providers: BaseProvider[] = [];
      for (const row of result) {
        const provider = await this.createProvider(row.provider_id);
        if (provider) {
          providers.push(provider);
        }
      }

      return providers;
    } catch (error) {
      console.error(`${formatDateTimeForLog()} 获取所有服务商实例失败:`, error);
      return [];
    }
  }

  /**
   * 清除服务商实例缓存
   */
  static clearCache(providerId?: number): void {
    if (providerId) {
      this.providerInstances.delete(providerId);
      console.log(`${formatDateTimeForLog()} 清除服务商缓存: ${providerId}`);
    } else {
      this.providerInstances.clear();
      console.log(`${formatDateTimeForLog()} 清除所有服务商缓存`);
    }
  }

  /**
   * 获取缓存统计信息
   */
  static getCacheStats(): { total: number; providers: string[] } {
    const providers: string[] = [];
    this.providerInstances.forEach((provider, id) => {
      providers.push(`${id}:${provider.getProviderCode()}`);
    });

    return {
      total: this.providerInstances.size,
      providers
    };
  }

  /**
   * 从数据库获取服务商配置
   */
  private static async getProviderConfig(providerId: number): Promise<ProviderConfig | null> {
    try {
      const query = `
        SELECT
          provider_id, code, name, api_url, username, password,
          token, api_config, status
        FROM fd_provider
        WHERE provider_id = ?
      `;

      const result = await executeQuery(query, [providerId]);

      if (result.length === 0) {
        return null;
      }

      const row = result[0];

      let apiConfig = null;
      if (row.api_config) {
        try {
          // 如果已经是对象，直接使用；如果是字符串，则解析
          apiConfig = typeof row.api_config === 'string' ? JSON.parse(row.api_config) : row.api_config;
        } catch (error) {
          console.error(`解析服务商 ${row.provider_id} 的 api_config 失败:`, error);
          console.error('原始数据:', row.api_config);
          // 如果解析失败，使用默认配置
          apiConfig = {
            timeout: 30000,
            retry_count: 3
          };
        }
      }

      return {
        provider_id: row.provider_id,
        code: row.code,
        name: row.name,
        api_url: row.api_url,
        username: row.username,
        password: row.password,
        token: row.token,
        api_config: apiConfig,
        status: row.status
      };
    } catch (error) {
      console.error(`获取服务商配置失败 (ID: ${providerId}):`, error);
      return null;
    }
  }

  /**
   * 根据配置创建具体的服务商实例
   */
  private static createProviderInstance(config: ProviderConfig): BaseProvider | null {
    switch (config.code) {
      case ProviderType.PLATFORM_29:
        // 29平台现在也使用通用适配器
        console.log(`29平台使用通用适配器: ${config.name}`);
        return new UnifiedUniversalProvider(config);

      case ProviderType.UNIVERSAL:
        return new UnifiedUniversalProvider(config);

      // 后续添加更多服务商类型
      // case ProviderType.PLATFORM_XX:
      //   return new PlatformXXProvider(config);

      default:
        // 默认使用通用适配器
        console.log(`使用通用适配器处理服务商: ${config.code}`);
        return new UnifiedUniversalProvider(config);
    }
  }

  /**
   * 验证服务商配置
   */
  static validateProviderConfig(config: ProviderConfig): boolean {
    if (!config.code || !config.name || !config.api_url) {
      return false;
    }

    // 根据不同服务商类型进行特定验证
    switch (config.code) {
      case ProviderType.PLATFORM_29:
        return Boolean(config.username && config.password);

      // 后续添加更多服务商的验证逻辑

      default:
        return true;
    }
  }

  /**
   * 测试服务商连接
   */
  static async testProviderConnection(providerId: number): Promise<{
    success: boolean;
    message: string;
    latency?: number;
  }> {
    try {
      const startTime = Date.now();
      const provider = await this.createProvider(providerId);

      if (!provider) {
        return {
          success: false,
          message: '服务商实例创建失败'
        };
      }

      // 尝试获取课程列表来测试连接
      const result = await provider.getCourseList();
      const latency = Date.now() - startTime;

      return {
        success: result.success,
        message: result.message,
        latency
      };
    } catch (error: any) {
      return {
        success: false,
        message: error.message || '连接测试失败'
      };
    }
  }
}
