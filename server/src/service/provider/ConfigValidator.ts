/**
 * 映射配置验证器
 * 检查配置的完整性和正确性
 */

import type {
  ConfigValidationResult,
  InterfaceConfig,
  ProviderUnifiedConfig,
  RequestMappingConfig,
  ResponseMappingConfig
} from '../../types/UnifiedConfig';
import { InterfaceType, STANDARD_FIELD_DEFINITIONS } from '../../types/StandardFields';
import { getOptionalFields, getRequiredFields } from '../../types/InterfaceStandards';
import { FieldValidator } from '../FieldValidator';

/**
 * 配置验证器类
 */
export class ConfigValidator {
  /**
   * 验证完整的货源配置
   */
  static validateProviderConfig(config: ProviderUnifiedConfig): ConfigValidationResult {
    const errors: Array<{ path: string; message: string; code: string }> = [];
    const warnings: Array<{ path: string; message: string; code: string }> = [];

    // 验证基本信息
    this.validateBasicInfo(config, errors, warnings);

    // 验证认证信息
    this.validateAuthInfo(config, errors, warnings);

    // 验证接口配置
    this.validateInterfaces(config, errors, warnings);

    // 验证全局设置
    this.validateGlobalSettings(config, errors, warnings);

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 验证基本信息
   */
  private static validateBasicInfo(
    config: ProviderUnifiedConfig,
    errors: Array<{ path: string; message: string; code: string }>,
    warnings: Array<{ path: string; message: string; code: string }>
  ): void {
    if (!config.version) {
      errors.push({ path: 'version', message: '配置版本不能为空', code: 'REQUIRED_FIELD' });
    }

    if (!config.provider_info) {
      errors.push({ path: 'provider_info', message: '货源信息不能为空', code: 'REQUIRED_FIELD' });
      return;
    }

    if (!config.provider_info.provider_id) {
      errors.push({ path: 'provider_info.provider_id', message: '货源ID不能为空', code: 'REQUIRED_FIELD' });
    }

    if (!config.provider_info.provider_name) {
      errors.push({ path: 'provider_info.provider_name', message: '货源名称不能为空', code: 'REQUIRED_FIELD' });
    }

    if (!config.provider_info.provider_code) {
      errors.push({ path: 'provider_info.provider_code', message: '货源代码不能为空', code: 'REQUIRED_FIELD' });
    }

    // 验证货源代码格式
    if (config.provider_info.provider_code && !/^[a-zA-Z0-9_-]+$/.test(config.provider_info.provider_code)) {
      warnings.push({
        path: 'provider_info.provider_code',
        message: '货源代码建议使用字母、数字、下划线和连字符',
        code: 'FORMAT_WARNING'
      });
    }
  }

  /**
   * 验证认证信息
   */
  private static validateAuthInfo(
    config: ProviderUnifiedConfig,
    errors: Array<{ path: string; message: string; code: string }>,
    warnings: Array<{ path: string; message: string; code: string }>
  ): void {
    if (!config.auth) {
      errors.push({ path: 'auth', message: '认证信息不能为空', code: 'REQUIRED_FIELD' });
      return;
    }

    if (!config.auth.api_uid) {
      errors.push({ path: 'auth.api_uid', message: '货源用户ID不能为空', code: 'REQUIRED_FIELD' });
    }

    if (!config.auth.api_key) {
      errors.push({ path: 'auth.api_key', message: '货源密钥不能为空', code: 'REQUIRED_FIELD' });
    }

    // 检查认证信息的安全性
    if (config.auth.api_key && config.auth.api_key.length < 6) {
      warnings.push({
        path: 'auth.api_key',
        message: '货源密钥长度建议不少于6位',
        code: 'SECURITY_WARNING'
      });
    }
  }

  /**
   * 验证接口配置
   */
  private static validateInterfaces(
    config: ProviderUnifiedConfig,
    errors: Array<{ path: string; message: string; code: string }>,
    warnings: Array<{ path: string; message: string; code: string }>
  ): void {
    if (!config.interfaces || Object.keys(config.interfaces).length === 0) {
      warnings.push({ path: 'interfaces', message: '没有配置任何接口', code: 'NO_INTERFACES' });
      return;
    }

    // 检查必要接口
    const requiredInterfaces = [InterfaceType.QUERY, InterfaceType.ORDER, InterfaceType.SYNC];
    const configuredInterfaces = Object.keys(config.interfaces);

    for (const requiredInterface of requiredInterfaces) {
      if (!configuredInterfaces.includes(requiredInterface)) {
        warnings.push({
          path: `interfaces.${requiredInterface}`,
          message: `建议配置${requiredInterface}接口`,
          code: 'MISSING_RECOMMENDED_INTERFACE'
        });
      }
    }

    // 验证每个接口配置
    for (const [interfaceType, interfaceConfig] of Object.entries(config.interfaces)) {
      this.validateInterfaceConfig(interfaceType as InterfaceType, interfaceConfig, errors, warnings);
    }
  }

  /**
   * 验证单个接口配置
   */
  private static validateInterfaceConfig(
    interfaceType: InterfaceType,
    config: InterfaceConfig,
    errors: Array<{ path: string; message: string; code: string }>,
    warnings: Array<{ path: string; message: string; code: string }>
  ): void {
    const basePath = `interfaces.${interfaceType}`;

    // 验证HTTP配置
    if (!config.http) {
      errors.push({ path: `${basePath}.http`, message: 'HTTP配置不能为空', code: 'REQUIRED_FIELD' });
      return;
    }

    if (!config.http.endpoint) {
      errors.push({ path: `${basePath}.http.endpoint`, message: '接口端点不能为空', code: 'REQUIRED_FIELD' });
    }

    if (!config.http.method) {
      errors.push({ path: `${basePath}.http.method`, message: 'HTTP方法不能为空', code: 'REQUIRED_FIELD' });
    } else if (!['GET', 'POST', 'PUT', 'DELETE'].includes(config.http.method)) {
      errors.push({
        path: `${basePath}.http.method`,
        message: 'HTTP方法必须是GET、POST、PUT或DELETE',
        code: 'INVALID_VALUE'
      });
    }

    // 验证请求映射
    this.validateRequestMapping(interfaceType, config.request_mapping, `${basePath}.request_mapping`, errors, warnings);

    // 验证响应映射
    this.validateResponseMapping(config.response_mapping, `${basePath}.response_mapping`, errors, warnings);

    // 验证自定义代码
    if (config.custom_code) {
      try {
        new Function('params', 'config', 'utils', config.custom_code);
      } catch (error) {
        errors.push({
          path: `${basePath}.custom_code`,
          message: `自定义代码语法错误: ${error}`,
          code: 'SYNTAX_ERROR'
        });
      }
    }
  }

  /**
   * 验证请求映射
   */
  private static validateRequestMapping(
    interfaceType: InterfaceType,
    mapping: RequestMappingConfig,
    basePath: string,
    errors: Array<{ path: string; message: string; code: string }>,
    warnings: Array<{ path: string; message: string; code: string }>
  ): void {
    if (!mapping) {
      errors.push({ path: basePath, message: '请求映射配置不能为空', code: 'REQUIRED_FIELD' });
      return;
    }

    // 检查必填字段
    const requiredFields = getRequiredFields(interfaceType);
    const configuredFields = Object.keys(mapping);

    for (const requiredField of requiredFields) {
      if (!configuredFields.includes(requiredField)) {
        errors.push({
          path: `${basePath}.${requiredField}`,
          message: `必填字段${requiredField}未配置`,
          code: 'MISSING_REQUIRED_FIELD'
        });
      }
    }

    // 验证字段映射配置
    for (const [standardField, fieldConfig] of Object.entries(mapping)) {
      if (!fieldConfig.provider_field) {
        errors.push({
          path: `${basePath}.${standardField}.provider_field`,
          message: '货源字段名不能为空',
          code: 'REQUIRED_FIELD'
        });
      }

      // 检查字段定义
      const fieldDefinition = STANDARD_FIELD_DEFINITIONS[standardField];
      if (!fieldDefinition) {
        warnings.push({
          path: `${basePath}.${standardField}`,
          message: `标准字段${standardField}没有定义`,
          code: 'UNDEFINED_STANDARD_FIELD'
        });
      }

      // 验证转换函数
      if (fieldConfig.transform) {
        const validTransforms = [
          'toString',
          'toNumber',
          'toBoolean',
          'toUpperCase',
          'toLowerCase',
          'trim',
          'encodeBase64',
          'decodeBase64',
          'md5',
          'sha256',
          'urlEncode',
          'urlDecode'
        ];

        if (!validTransforms.includes(fieldConfig.transform) && !fieldConfig.transform.includes('function')) {
          warnings.push({
            path: `${basePath}.${standardField}.transform`,
            message: `未知的转换函数: ${fieldConfig.transform}`,
            code: 'UNKNOWN_TRANSFORM'
          });
        }
      }
    }
  }

  /**
   * 验证响应映射
   */
  private static validateResponseMapping(
    mapping: ResponseMappingConfig,
    basePath: string,
    errors: Array<{ path: string; message: string; code: string }>,
    warnings: Array<{ path: string; message: string; code: string }>
  ): void {
    if (!mapping) {
      errors.push({ path: basePath, message: '响应映射配置不能为空', code: 'REQUIRED_FIELD' });
      return;
    }

    // 验证成功条件
    if (!mapping.success_condition) {
      errors.push({ path: `${basePath}.success_condition`, message: '成功条件不能为空', code: 'REQUIRED_FIELD' });
    } else {
      if (!mapping.success_condition.field) {
        errors.push({
          path: `${basePath}.success_condition.field`,
          message: '成功条件字段不能为空',
          code: 'REQUIRED_FIELD'
        });
      }

      const validConditions = ['eq', 'ne', 'gt', 'gte', 'lt', 'lte', 'in', 'contains'];
      if (!validConditions.includes(mapping.success_condition.condition)) {
        errors.push({
          path: `${basePath}.success_condition.condition`,
          message: '无效的成功条件类型',
          code: 'INVALID_VALUE'
        });
      }
    }

    // 验证消息字段
    if (!mapping.message_field) {
      errors.push({ path: `${basePath}.message_field`, message: '消息字段不能为空', code: 'REQUIRED_FIELD' });
    }

    // 验证字段映射
    if (!mapping.field_mapping) {
      warnings.push({ path: `${basePath}.field_mapping`, message: '没有配置字段映射', code: 'NO_FIELD_MAPPING' });
    }
  }

  /**
   * 验证全局设置
   */
  private static validateGlobalSettings(
    config: ProviderUnifiedConfig,
    errors: Array<{ path: string; message: string; code: string }>,
    warnings: Array<{ path: string; message: string; code: string }>
  ): void {
    if (!config.global_settings) {
      return; // 全局设置是可选的
    }

    const settings = config.global_settings;

    if (settings.retry_count !== undefined && (settings.retry_count < 0 || settings.retry_count > 10)) {
      warnings.push({
        path: 'global_settings.retry_count',
        message: '重试次数建议在0-10之间',
        code: 'VALUE_OUT_OF_RANGE'
      });
    }

    if (settings.retry_delay !== undefined && (settings.retry_delay < 100 || settings.retry_delay > 10000)) {
      warnings.push({
        path: 'global_settings.retry_delay',
        message: '重试延迟建议在100-10000毫秒之间',
        code: 'VALUE_OUT_OF_RANGE'
      });
    }

    if (settings.rate_limit !== undefined && (settings.rate_limit < 1 || settings.rate_limit > 100)) {
      warnings.push({
        path: 'global_settings.rate_limit',
        message: '速率限制建议在1-100请求/秒之间',
        code: 'VALUE_OUT_OF_RANGE'
      });
    }
  }

  /**
   * 验证接口测试数据
   */
  static validateTestData(interfaceType: InterfaceType, testData: any): ConfigValidationResult {
    const errors: Array<{ path: string; message: string; code: string }> = [];
    const warnings: Array<{ path: string; message: string; code: string }> = [];

    const requiredFields = getRequiredFields(interfaceType);

    for (const field of requiredFields) {
      if (!testData[field]) {
        errors.push({
          path: field,
          message: `测试数据缺少必填字段: ${field}`,
          code: 'MISSING_TEST_FIELD'
        });
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 生成配置报告
   */
  static generateConfigReport(config: ProviderUnifiedConfig): string {
    const validation = this.validateProviderConfig(config);

    let report = `# 货源配置报告\n\n`;
    report += `**货源名称**: ${config.provider_info.provider_name}\n`;
    report += `**货源代码**: ${config.provider_info.provider_code}\n`;
    report += `**配置版本**: ${config.version}\n`;
    report += `**配置状态**: ${validation.valid ? '✅ 有效' : '❌ 无效'}\n\n`;

    if (config.interfaces) {
      report += `## 接口配置\n\n`;
      for (const [interfaceType, interfaceConfig] of Object.entries(config.interfaces)) {
        report += `- **${interfaceType}**: ${interfaceConfig.enabled ? '✅ 启用' : '❌ 禁用'}\n`;
      }
      report += '\n';
    }

    if (validation.errors.length > 0) {
      report += `## 错误 (${validation.errors.length})\n\n`;
      for (const error of validation.errors) {
        report += `- **${error.path}**: ${error.message}\n`;
      }
      report += '\n';
    }

    if (validation.warnings.length > 0) {
      report += `## 警告 (${validation.warnings.length})\n\n`;
      for (const warning of validation.warnings) {
        report += `- **${warning.path}**: ${warning.message}\n`;
      }
      report += '\n';
    }

    return report;
  }
}
