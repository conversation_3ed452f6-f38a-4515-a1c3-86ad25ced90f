/**
 * 服务商对接管理器 - 重构版本
 * 使用模块化设计，支持多种服务商对接
 */

import { ProviderFactory } from './provider/ProviderFactory';
import type {
  BaseProvider,
  ChangePasswordParams,
  ChangePasswordResult,
  CreateOrderParams,
  CreateOrderResult,
  GetBalanceResult,
  GetCourseListResult,
  QueryCoursesParams,
  QueryCoursesResult,
  RefillOrderParams,
  RefillOrderResult,
  SyncOrderParams,
  SyncOrderResult
} from './provider/BaseProvider';

/**
 * 服务商对接管理器
 * 提供统一的服务商对接接口
 */
export const ProviderManager = {
  /**
   * 获取服务商课程列表
   */
  async getCourseList(providerId: number): Promise<GetCourseListResult> {
    try {
      const provider = await ProviderFactory.createProvider(providerId);
      if (!provider) {
        return {
          success: false,
          message: '服务商不存在或不可用',
          error: 'PROVIDER_NOT_FOUND'
        };
      }

      return await provider.getCourseList();
    } catch (error: any) {
      console.error(`获取服务商课程列表失败 (ID: ${providerId}):`, error);
      return {
        success: false,
        message: error.message || '获取课程列表失败',
        error: 'SERVICE_ERROR'
      };
    }
  },

  /**
   * 查课接口
   */
  async queryCourses(providerId: number, params: QueryCoursesParams): Promise<QueryCoursesResult> {
    try {
      const provider = await ProviderFactory.createProvider(providerId);
      if (!provider) {
        return {
          success: false,
          message: '服务商不存在或不可用',
          error: 'PROVIDER_NOT_FOUND'
        };
      }

      return await provider.queryCourses(params);
    } catch (error: any) {
      console.error(`查课失败 (ID: ${providerId}):`, error);
      return {
        success: false,
        message: error.message || '查课失败',
        error: 'SERVICE_ERROR'
      };
    }
  },

  /**
   * 创建订单
   */
  async createOrder(providerId: number, params: CreateOrderParams): Promise<CreateOrderResult> {
    try {
      const provider = await ProviderFactory.createProvider(providerId);
      if (!provider) {
        return {
          success: false,
          message: '服务商不存在或不可用',
          error: 'PROVIDER_NOT_FOUND'
        };
      }

      return await provider.createOrder(params);
    } catch (error: any) {
      console.error(`创建订单失败 (ID: ${providerId}):`, error);
      return {
        success: false,
        message: error.message || '创建订单失败',
        error: 'SERVICE_ERROR'
      };
    }
  },

  /**
   * 同步订单状态
   */
  async syncOrderStatus(providerId: number, params: SyncOrderParams): Promise<SyncOrderResult> {
    try {
      const provider = await ProviderFactory.createProvider(providerId);
      if (!provider) {
        return {
          success: false,
          message: '服务商不存在或不可用',
          error: 'PROVIDER_NOT_FOUND'
        };
      }

      return await provider.syncOrderStatus(params);
    } catch (error: any) {
      console.error(`同步订单状态失败 (ID: ${providerId}):`, error);
      return {
        success: false,
        message: error.message || '同步订单状态失败',
        error: 'SERVICE_ERROR'
      };
    }
  },

  /**
   * 补刷订单
   */
  async refillOrder(providerId: number, params: RefillOrderParams): Promise<RefillOrderResult> {
    try {
      const provider = await ProviderFactory.createProvider(providerId);
      if (!provider) {
        return {
          success: false,
          message: '服务商不存在或不可用',
          error: 'PROVIDER_NOT_FOUND'
        };
      }

      return await provider.refillOrder(params);
    } catch (error: any) {
      console.error(`补刷订单失败 (ID: ${providerId}):`, error);
      return {
        success: false,
        message: error.message || '补刷订单失败',
        error: 'SERVICE_ERROR'
      };
    }
  },

  /**
   * 修改密码
   */
  async changePassword(providerId: number, params: ChangePasswordParams): Promise<ChangePasswordResult> {
    try {
      const provider = await ProviderFactory.createProvider(providerId);
      if (!provider) {
        return {
          success: false,
          message: '服务商不存在或不可用',
          error: 'PROVIDER_NOT_FOUND'
        };
      }

      return await provider.changePassword(params);
    } catch (error: any) {
      console.error(`修改密码失败 (ID: ${providerId}):`, error);
      return {
        success: false,
        message: error.message || '修改密码失败',
        error: 'SERVICE_ERROR'
      };
    }
  },

  /**
   * 批量查课
   */
  async batchQueryCourses(
    providerId: number,
    accountList: Array<{
      school?: string;
      username: string;
      password: string;
    }>,
    productInfo?: {
      platform_cid?: string;
      noun?: string;
    }
  ): Promise<{
    success: boolean;
    message: string;
    results: Array<{
      account: string;
      result: QueryCoursesResult;
    }>;
  }> {
    try {
      const provider = await ProviderFactory.createProvider(providerId);
      if (!provider) {
        return {
          success: false,
          message: '服务商不存在或不可用',
          results: []
        };
      }

      const promises = accountList.map(async account => {
        try {
          const queryParams = {
            ...account,
            product: productInfo
          };
          const result = await provider.queryCourses(queryParams);
          return {
            account: account.username,
            result
          };
        } catch (error: any) {
          return {
            account: account.username,
            result: {
              success: false,
              message: error.message || '查课失败',
              error: 'QUERY_ERROR'
            }
          };
        }
      });

      const results = await Promise.all(promises);

      return {
        success: true,
        message: `批量查课完成，共处理 ${accountList.length} 个账号`,
        results
      };
    } catch (error: any) {
      console.error(`批量查课失败 (ID: ${providerId}):`, error);
      return {
        success: false,
        message: error.message || '批量查课失败',
        results: []
      };
    }
  },

  /**
   * 查询余额
   */
  async getBalance(providerId: number): Promise<GetBalanceResult> {
    try {
      const provider = await ProviderFactory.createProvider(providerId);
      if (!provider) {
        return {
          success: false,
          message: '服务商不存在或不可用',
          error: 'PROVIDER_NOT_FOUND'
        };
      }

      return await provider.getBalance();
    } catch (error: any) {
      console.error(`余额查询失败 (ID: ${providerId}):`, error);
      return {
        success: false,
        message: error.message || '余额查询失败',
        error: 'SERVICE_ERROR'
      };
    }
  },

  /**
   * 测试服务商连接
   */
  async testConnection(providerId: number): Promise<{
    success: boolean;
    message: string;
    latency?: number;
  }> {
    return await ProviderFactory.testProviderConnection(providerId);
  },

  /**
   * 获取所有可用服务商
   */
  async getAllProviders(): Promise<BaseProvider[]> {
    return await ProviderFactory.getAllProviders();
  },

  /**
   * 清除服务商缓存
   */
  clearCache(providerId?: number): void {
    ProviderFactory.clearCache(providerId);
  },

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): { total: number; providers: string[] } {
    return ProviderFactory.getCacheStats();
  }
};
