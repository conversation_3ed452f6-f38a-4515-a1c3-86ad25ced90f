/**
 * 数据迁移脚本
 * 将现有的分散配置转换为新的统一配置格式
 */

import { executeQuery } from '../utils/db';
import type { InterfaceConfig, ProviderUnifiedConfig } from '../types/UnifiedConfig';
import { TEMPLATE_29_PLATFORM } from '../types/UnifiedConfig';
import type { InterfaceType } from '../types/StandardFields';

interface LegacyProvider {
  provider_id: number;
  code: string;
  name: string;
  username: string;
  password: string;
  token?: string;
  api_url?: string;
  field_mapping?: any;
}

interface LegacyInterface {
  interface_id: number;
  provider_id: number;
  interface_type: string;
  endpoint_url: string;
  http_method: string;
  request_template?: any;
  response_mapping?: any;
  is_enabled: boolean;
}

/**
 * 数据迁移器
 */
export class ConfigMigrator {
  /**
   * 执行完整迁移
   */
  static async migrateAll(): Promise<void> {
    console.log('开始数据迁移...');

    try {
      // 1. 获取所有需要迁移的货源
      const providers = await this.getLegacyProviders();
      console.log(`找到 ${providers.length} 个货源需要迁移`);

      // 2. 逐个迁移货源配置
      for (const provider of providers) {
        await this.migrateProvider(provider);
      }

      console.log('数据迁移完成！');
    } catch (error) {
      console.error('数据迁移失败:', error);
      throw error;
    }
  }

  /**
   * 获取需要迁移的货源
   */
  private static async getLegacyProviders(): Promise<LegacyProvider[]> {
    const query = `
      SELECT 
        provider_id, code, name, username, password, token, api_url, field_mapping
      FROM fd_provider 
      WHERE is_unified_config = 0 AND status = 1
    `;

    return await executeQuery(query);
  }

  /**
   * 迁移单个货源
   */
  private static async migrateProvider(provider: LegacyProvider): Promise<void> {
    console.log(`迁移货源: ${provider.name} (ID: ${provider.provider_id})`);

    try {
      // 1. 获取货源的接口配置
      const interfaces = await this.getLegacyInterfaces(provider.provider_id);

      // 2. 构建统一配置
      const unifiedConfig = await this.buildUnifiedConfig(provider, interfaces);

      // 3. 保存统一配置
      await this.saveUnifiedConfig(provider.provider_id, unifiedConfig);

      // 4. 标记为已迁移
      await this.markAsMigrated(provider.provider_id);

      console.log(`货源 ${provider.name} 迁移成功`);
    } catch (error) {
      console.error(`货源 ${provider.name} 迁移失败:`, error);
      throw error;
    }
  }

  /**
   * 获取货源的接口配置
   */
  private static async getLegacyInterfaces(providerId: number): Promise<LegacyInterface[]> {
    const query = `
      SELECT 
        interface_id, provider_id, interface_type, endpoint_url, 
        http_method, request_template, response_mapping, is_enabled
      FROM fd_provider_interface 
      WHERE provider_id = ? AND is_unified_config = 0
    `;

    return await executeQuery(query, [providerId]);
  }

  /**
   * 构建统一配置
   */
  private static async buildUnifiedConfig(
    provider: LegacyProvider,
    interfaces: LegacyInterface[]
  ): Promise<ProviderUnifiedConfig> {
    const now = new Date().toISOString();

    // 基础配置
    const config: ProviderUnifiedConfig = {
      version: '1.0.0',
      provider_info: {
        provider_id: provider.provider_id,
        provider_name: provider.name,
        provider_code: provider.code,
        base_url: provider.api_url || ''
      },
      auth: {
        api_uid: provider.username || '',
        api_key: provider.password || '',
        token: provider.token
      },
      interfaces: {},
      global_settings: {
        retry_count: 3,
        retry_delay: 1000,
        rate_limit: 10,
        debug_mode: false
      },
      created_at: now,
      updated_at: now
    };

    // 转换接口配置
    for (const iface of interfaces) {
      const interfaceConfig = this.convertInterface(iface, provider);
      if (interfaceConfig) {
        config.interfaces[iface.interface_type as InterfaceType] = interfaceConfig;
      }
    }

    // 如果是29平台，使用预设模板补充缺失的接口
    if (provider.code === '29pt' || provider.name.includes('29')) {
      this.applyTemplate29Platform(config);
    }

    return config;
  }

  /**
   * 转换单个接口配置
   */
  private static convertInterface(iface: LegacyInterface, provider: LegacyProvider): InterfaceConfig | null {
    try {
      const requestTemplate = iface.request_template
        ? typeof iface.request_template === 'string'
          ? JSON.parse(iface.request_template)
          : iface.request_template
        : {};

      const responseMapping = iface.response_mapping
        ? typeof iface.response_mapping === 'string'
          ? JSON.parse(iface.response_mapping)
          : iface.response_mapping
        : {};

      // 构建请求映射
      const requestMappingConfig: any = {};

      // 从请求模板中提取字段映射
      this.extractRequestMapping(requestTemplate, requestMappingConfig);

      // 从货源字段映射中补充
      if (provider.field_mapping) {
        const fieldMapping =
          typeof provider.field_mapping === 'string' ? JSON.parse(provider.field_mapping) : provider.field_mapping;
        this.applyFieldMapping(fieldMapping, requestMappingConfig);
      }

      // 构建响应映射
      const responseMappingConfig: any = {
        success_condition: {
          field: responseMapping.success_field || 'code',
          condition: responseMapping.success_condition || 'eq',
          value: responseMapping.success_value !== undefined ? responseMapping.success_value : 0
        },
        message_field: responseMapping.message_field || 'msg',
        field_mapping: responseMapping.data_mapping || {}
      };

      if (responseMapping.data_field) {
        responseMappingConfig.data_field = responseMapping.data_field;
      }

      return {
        enabled: iface.is_enabled,
        http: {
          method: (iface.http_method || 'POST') as any,
          endpoint: iface.endpoint_url,
          content_type: 'form'
        },
        request_mapping: requestMappingConfig,
        response_mapping: responseMappingConfig,
        description: `从旧配置迁移的${iface.interface_type}接口`
      };
    } catch (error) {
      console.error(`转换接口配置失败 (${iface.interface_type}):`, error);
      return null;
    }
  }

  /**
   * 从请求模板提取字段映射
   */
  private static extractRequestMapping(template: any, mapping: any): void {
    if (!template || typeof template !== 'object') return;

    const standardFields = [
      'api_uid',
      'api_key',
      'username',
      'password',
      'school',
      'platform',
      'course_id',
      'course_name',
      'upstream_order_id',
      'new_password'
    ];

    for (const [providerField, value] of Object.entries(template)) {
      if (typeof value === 'string' && value.startsWith('${') && value.endsWith('}')) {
        const standardField = value.slice(2, -1);
        if (standardFields.includes(standardField)) {
          mapping[standardField] = {
            provider_field: providerField,
            required: true
          };
        }
      }
    }
  }

  /**
   * 应用字段映射
   */
  private static applyFieldMapping(fieldMapping: any, requestMapping: any): void {
    if (!fieldMapping || typeof fieldMapping !== 'object') return;

    for (const [standardField, config] of Object.entries(fieldMapping)) {
      if (config && typeof config === 'object' && (config as any).provider_field) {
        requestMapping[standardField] = {
          provider_field: (config as any).provider_field,
          required: (config as any).required || false,
          default_value: (config as any).default_value,
          description: (config as any).description
        };
      }
    }
  }

  /**
   * 应用29平台模板
   */
  private static applyTemplate29Platform(config: ProviderUnifiedConfig): void {
    const template = TEMPLATE_29_PLATFORM.config;

    // 合并接口配置
    for (const [interfaceType, templateInterface] of Object.entries(template.interfaces || {})) {
      if (!config.interfaces[interfaceType as InterfaceType]) {
        // 应用模板接口，但使用当前货源的认证信息
        const interfaceConfig = JSON.parse(JSON.stringify(templateInterface));

        // 更新认证字段映射
        if (interfaceConfig.request_mapping) {
          if (interfaceConfig.request_mapping.api_uid) {
            interfaceConfig.request_mapping.api_uid.default_value = config.auth.api_uid;
          }
          if (interfaceConfig.request_mapping.api_key) {
            interfaceConfig.request_mapping.api_key.default_value = config.auth.api_key;
          }
        }

        config.interfaces[interfaceType as InterfaceType] = interfaceConfig;
      }
    }

    // 合并全局设置
    if (template.global_settings) {
      config.global_settings = { ...config.global_settings, ...template.global_settings };
    }
  }

  /**
   * 保存统一配置
   */
  private static async saveUnifiedConfig(providerId: number, config: ProviderUnifiedConfig): Promise<void> {
    const query = `
      UPDATE fd_provider 
      SET unified_config = ?, config_version = ?, update_time = NOW()
      WHERE provider_id = ?
    `;

    await executeQuery(query, [JSON.stringify(config), config.version, providerId]);
  }

  /**
   * 标记为已迁移
   */
  private static async markAsMigrated(providerId: number): Promise<void> {
    const query = `
      UPDATE fd_provider 
      SET is_unified_config = 1, update_time = NOW()
      WHERE provider_id = ?
    `;

    await executeQuery(query, [providerId]);

    // 同时标记接口为已迁移
    const interfaceQuery = `
      UPDATE fd_provider_interface 
      SET is_unified_config = 1, update_time = NOW()
      WHERE provider_id = ?
    `;

    await executeQuery(interfaceQuery, [providerId]);
  }

  /**
   * 回滚迁移
   */
  static async rollback(providerId?: number): Promise<void> {
    console.log('开始回滚迁移...');

    try {
      let query = `
        UPDATE fd_provider 
        SET is_unified_config = 0, unified_config = NULL, config_version = NULL, update_time = NOW()
      `;
      const params: any[] = [];

      if (providerId) {
        query += ' WHERE provider_id = ?';
        params.push(providerId);
        console.log(`回滚货源 ID: ${providerId}`);
      } else {
        console.log('回滚所有货源');
      }

      await executeQuery(query, params);

      // 回滚接口配置
      let interfaceQuery = `
        UPDATE fd_provider_interface 
        SET is_unified_config = 0, unified_config = NULL, config_version = NULL, update_time = NOW()
      `;

      if (providerId) {
        interfaceQuery += ' WHERE provider_id = ?';
      }

      await executeQuery(interfaceQuery, params);

      console.log('回滚完成');
    } catch (error) {
      console.error('回滚失败:', error);
      throw error;
    }
  }

  /**
   * 验证迁移结果
   */
  static async validateMigration(): Promise<void> {
    console.log('验证迁移结果...');

    try {
      // 检查迁移状态
      const statusQuery = `
        SELECT 
          COUNT(*) as total,
          COUNT(CASE WHEN is_unified_config = 1 THEN 1 END) as migrated,
          COUNT(CASE WHEN unified_config IS NOT NULL THEN 1 END) as has_config
        FROM fd_provider 
        WHERE status = 1
      `;

      const [status] = await executeQuery(statusQuery);
      console.log('迁移状态:', status);

      // 检查配置完整性
      const configQuery = `
        SELECT provider_id, name, unified_config
        FROM fd_provider 
        WHERE is_unified_config = 1 AND unified_config IS NOT NULL
      `;

      const configs = await executeQuery(configQuery);

      for (const config of configs) {
        try {
          const unifiedConfig = JSON.parse(config.unified_config);
          if (!unifiedConfig.version || !unifiedConfig.provider_info || !unifiedConfig.auth) {
            console.warn(`货源 ${config.name} (ID: ${config.provider_id}) 配置不完整`);
          }
        } catch (error) {
          console.error(`货源 ${config.name} (ID: ${config.provider_id}) 配置JSON无效`);
        }
      }

      console.log('验证完成');
    } catch (error) {
      console.error('验证失败:', error);
      throw error;
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const command = process.argv[2];
  const providerId = process.argv[3] ? Number.parseInt(process.argv[3]) : undefined;

  switch (command) {
    case 'migrate':
      ConfigMigrator.migrateAll().catch(console.error);
      break;
    case 'rollback':
      ConfigMigrator.rollback(providerId).catch(console.error);
      break;
    case 'validate':
      ConfigMigrator.validateMigration().catch(console.error);
      break;
    default:
      console.log('用法:');
      console.log('  npm run migrate-config migrate    # 执行迁移');
      console.log('  npm run migrate-config rollback   # 回滚所有');
      console.log('  npm run migrate-config rollback 3 # 回滚指定货源');
      console.log('  npm run migrate-config validate   # 验证迁移结果');
  }
}
