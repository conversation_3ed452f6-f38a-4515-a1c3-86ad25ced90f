import { Router } from 'express';
import {
  batchSyncOrderStatus,
  cancelOrder,
  changePassword,
  createOrder,
  feedbackOrder,
  getOrderDetail,
  getOrderList,
  getOrderStatistics,
  getOrderStats,
  getProviderCourses,
  getUserOrders,
  queryCourses,
  refillOrder,
  syncOrderStatus,
  updateOrderStatus
} from '../controller/order';
import { authenticate, requireAdmin } from '../middleware/auth';

const router = Router();

// 所有订单路由都需要认证
router.use(authenticate);

// ===== 订单管理接口 =====

// 获取订单列表（用户可查看自己的，管理员可查看所有）
router.get('/list', getOrderList);

// 获取订单详情（用户可查看自己的，管理员可查看所有）
router.get('/:orderId', getOrderDetail);

// 创建订单（所有登录用户）
router.post('/create', createOrder);

// 取消订单（用户可取消自己的，管理员可取消所有）
router.post('/:orderId/cancel', cancelOrder);

// 获取订单统计信息（用户查看自己的，管理员查看所有）
router.get('/stats/overview', getOrderStats);

// 获取详细订单统计数据
router.get('/stats/detailed', getOrderStatistics);

// 获取指定用户的订单列表（管理员专用）
router.get('/user/:userId', requireAdmin, getUserOrders);

// ===== 管理员专用接口 =====

// 更新订单状态（仅管理员）
router.put('/:orderId/status', requireAdmin, updateOrderStatus);

// 批量同步订单状态（仅管理员）
router.post('/batch/sync', requireAdmin, batchSyncOrderStatus);

// ===== 29平台专用接口 =====

// 查课接口（所有登录用户）
router.post('/query-courses', queryCourses);

// 获取服务商课程列表（所有登录用户）
router.get('/provider/:providerId/courses', getProviderCourses);

// 补刷订单（用户可操作自己的，管理员可操作所有）
router.post('/:orderId/refill', refillOrder);

// 同步订单状态（用户可操作自己的，管理员可操作所有）
router.post('/:orderId/sync', syncOrderStatus);

// 修改密码（用户可操作自己的，管理员可操作所有）
router.post('/:orderId/change-password', changePassword);

// 订单反馈（用户可操作自己的，管理员可操作所有）
router.post('/:orderId/feedback', feedbackOrder);

export default router;
