import { Router } from 'express';
import {
  deleteReport,
  downloadReport,
  generateFinanceReport,
  generateOrderReport,
  generateUserReport,
  getReportList
} from '../controller/report';
import { authenticate, requirePermission } from '../middleware/auth';

const router = Router();

// 所有报表路由都需要管理员权限
router.use(authenticate);

// ===== 报表管理接口 =====

// 生成用户报表
router.post('/user', requirePermission('system:report:create'), generateUserReport);

// 生成订单报表
router.post('/order', requirePermission('system:report:create'), generateOrderReport);

// 生成财务报表
router.post('/finance', requirePermission('system:report:create'), generateFinanceReport);

// 获取报表列表
router.get('/list', requirePermission('system:report:view'), getReportList);

// 下载报表文件
router.get('/download/:fileName', requirePermission('system:report:view'), downloadReport);

// 删除报表文件
router.delete('/:fileName', requirePermission('system:report:delete'), deleteReport);

export default router;
