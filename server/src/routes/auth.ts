import path from 'node:path';
import fs from 'node:fs';
import express, { Request } from 'express';
import multer from 'multer';
import {
  changePassword,
  getAdminInviteCode,
  getInviteStats,
  getSessionInfo,
  getUserInfo,
  getUserInviteStats,
  login,
  logout,
  refreshToken,
  register,
  updateInviteCode,
  updateUserInfo,
  uploadAvatar
} from '../controller/auth';
import { authenticate } from '../middleware/auth';

const router = express.Router();

// 配置文件上传
const uploadDir = path.join(__dirname, '../../public/uploads/avatars');

// 确保上传目录存在
if (!fs.existsSync(uploadDir)) {
  fs.mkdirSync(uploadDir, { recursive: true });
}

const storage = multer.diskStorage({
  destination(req: Express.Request, file: Express.Multer.File, cb: (error: Error | null, destination: string) => void) {
    cb(null, uploadDir);
  },
  filename(req: Express.Request, file: Express.Multer.File, cb: (error: Error | null, filename: string) => void) {
    const uniqueSuffix = `${Date.now()}-${Math.round(Math.random() * 1e9)}`;
    const ext = path.extname(file.originalname);
    cb(null, uniqueSuffix + ext);
  }
});

const upload = multer({
  storage,
  limits: {
    fileSize: 2 * 1024 * 1024 // 限制文件大小为2MB
  },
  fileFilter(req: Express.Request, file: Express.Multer.File, cb: multer.FileFilterCallback) {
    const allowedTypes = /jpeg|jpg|png/;
    const ext = path.extname(file.originalname).toLowerCase();
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && allowedTypes.test(ext)) {
      return cb(null, true);
    }
    cb(new Error('仅支持 JPG 或 PNG 格式的图片'));
  }
});

// 登录路由
router.post('/login', login as any);

// 注册路由
router.post('/register', register as any);

// 获取管理员邀请码路由（临时调试用）
router.get('/admin-invite-code', getAdminInviteCode as any);

// 获取用户信息路由（需要认证但不需要授权）
router.get('/getUserInfo', authenticate as any, getUserInfo as any);

// 刷新令牌路由
router.post('/refreshToken', refreshToken as any);

// 修改密码路由（需要认证）
router.post('/changePassword', authenticate as any, changePassword as any);

// 退出登录路由（需要认证）
router.post('/logout', authenticate as any, logout as any);

// 更新用户信息
router.put('/updateUserInfo', authenticate, updateUserInfo);

// 上传头像
router.post('/uploadAvatar', authenticate, upload.single('avatar'), uploadAvatar);

// 更新邀请码
router.post('/updateInviteCode', authenticate, updateInviteCode);

// 获取邀请统计
router.get('/inviteStats', authenticate, getInviteStats);

// 获取邀请统计
router.get('/inviteStats', authenticate, getUserInviteStats);

// 获取会话信息
router.get('/sessionInfo', authenticate, getSessionInfo);

export default router;
