import { Router } from 'express';
import {
  createCategory,
  deleteCategory,
  getCategoryDetail,
  getCategoryList,
  updateCategory
} from '../controller/category';
import { authenticate, requireAdmin } from '../middleware/auth';

const router = Router();

// 所有分类路由都需要认证
router.use(authenticate);

// ===== 公共接口（所有登录用户可访问） =====

// 获取分类列表
router.get('/list', getCategoryList);

// 获取分类详情
router.get('/:categoryId', getCategoryDetail);

// ===== 管理员接口 =====

// 创建分类
router.post('/', requireAdmin, createCategory);

// 更新分类
router.put('/:categoryId', requireAdmin, updateCategory);

// 删除分类
router.delete('/:categoryId', requireAdmin, deleteCategory);

export default router;
