import { Router } from 'express';
import { authenticate, requireAdmin } from '../middleware/auth';
import {
  deleteEmailTemplate,
  getEmailRecords,
  getEmailStats,
  getEmailStatus,
  getEmailTemplate,
  getEmailTemplates,
  getTemplateVariables,
  previewEmailTemplate,
  reloadEmailConfig,
  saveEmailTemplate,
  sendEmail,
  testEmailConfig
} from '../controller/email';

const router = Router();

// 所有邮件路由都需要认证
router.use(authenticate);

// 测试邮件配置（管理员权限）
router.post('/test', requireAdmin, testEmailConfig);

// 发送邮件（管理员权限）
router.post('/send', requireAdmin, sendEmail);

// 获取邮件服务状态
router.get('/status', getEmailStatus);

// 重新加载邮件配置（管理员权限）
router.post('/reload', requireAdmin, reloadEmailConfig);

// 获取邮件模板列表
router.get('/templates', getEmailTemplates);

// 获取单个邮件模板详情
router.get('/templates/:templateId', getEmailTemplate);

// 获取模板变量定义
router.get('/templates/variables/definitions', getTemplateVariables);

// 预览邮件模板
router.post('/templates/preview', previewEmailTemplate);

// 保存邮件模板（管理员权限）
router.post('/templates', requireAdmin, saveEmailTemplate);

// 更新邮件模板（管理员权限）
router.put('/templates/:templateId', requireAdmin, saveEmailTemplate);

// 删除邮件模板（管理员权限）
router.delete('/templates/:templateId', requireAdmin, deleteEmailTemplate);

// 获取邮件发送记录
router.get('/records', getEmailRecords);

// 获取邮件发送统计（管理员权限）
router.get('/stats', requireAdmin, getEmailStats);

export default router;
