import { Router } from 'express';
import { authenticate, requireAdmin } from '../middleware/auth';
import { batchDeleteLogs, createLog, deleteLog, getLogDetail, getLogList, getUserLogs } from '../controller/log';

const router = Router();

// 记录操作日志
router.post('/create', authenticate, createLog);

// 获取日志列表（管理员）
router.get('/list', authenticate, requireAdmin, getLogList);

// 获取用户操作日志（管理员）
router.get('/user/:userId', authenticate, requireAdmin, getUserLogs);

// 获取日志详情（管理员）
router.get('/:logId', authenticate, requireAdmin, getLogDetail);

// 删除日志（管理员）
router.delete('/:logId', authenticate, requireAdmin, deleteLog);

// 批量删除日志（管理员）
router.post('/batch-delete', authenticate, requireAdmin, batchDeleteLogs);

export default router;
