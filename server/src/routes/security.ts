import { Router } from 'express';
import {
  getLicenseSecurityStatus,
  getSecurityEvents,
  getSecurityReport,
  markAsSuspicious,
  regenerateSignature,
  triggerLicenseValidation
} from '../controller/security';

const router = Router();

/** 安全管理路由 这些接口应该只允许管理员访问 */

// 获取安全监控报告
router.get('/report', getSecurityReport);

// 获取安全事件列表
router.get('/events', getSecurityEvents);

// 获取特定域名的安全状态
router.get('/status/:domain', getLicenseSecurityStatus);

// 手动触发授权验证
router.post('/validate', triggerLicenseValidation);

// 重新生成数据签名
router.post('/regenerate-signature', regenerateSignature);

// 标记授权为可疑
router.post('/mark-suspicious', markAsSuspicious);

export default router;
