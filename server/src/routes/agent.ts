import { Router } from 'express';
import { authenticate } from '../middleware/auth';
import {
  getAgentInfo,
  getAgentStatistics,
  getSubordinateList,
  rechargeForSubordinate,
  updateInviteCode
} from '../controller/agent';

const router = Router();

// 所有代理路由都需要登录
router.use(authenticate);

// ===== 代理分销系统接口 =====

// 获取用户的代理关系信息
router.get('/info', getAgentInfo);

// 获取下级用户列表
router.get('/subordinates', getSubordinateList);

// 为下级用户充值
router.post('/recharge', rechargeForSubordinate);

// 修改邀请码
router.put('/invite-code', updateInviteCode);

// 获取代理统计数据
router.get('/statistics', getAgentStatistics);

export default router;
