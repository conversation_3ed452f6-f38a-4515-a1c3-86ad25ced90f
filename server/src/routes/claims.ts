import { Router } from 'express';
import type { AuthenticatedRequest } from '../middleware/auth';
import { authenticate } from '../middleware/auth';

const router = Router();

// 获取当前用户的角色与权限（claims）
router.get('/auth/claims', authenticate, async (req: AuthenticatedRequest, res) => {
  if (!req.user) return res.json({ code: 1006, msg: '未登录', data: null });
  return res.json({ code: '0000', msg: 'ok', data: { roles: req.user.roles, permissions: req.user.permissions } });
});

export default router;
