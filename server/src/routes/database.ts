import { Router } from 'express';
import {
  dropTable,
  executeSQL,
  exportTable,
  getDatabaseInfo,
  getTableList,
  getTableStructure,
  optimizeTable,
  repairTable,
  truncateTable
} from '../controller/database';
import { authenticate, requireAdmin } from '../middleware/auth';

const router = Router();

// 所有数据库管理路由都需要管理员权限
router.use(authenticate);
router.use(requireAdmin);

// ===== 数据库管理接口 =====

// 获取数据库信息
router.get('/info', getDatabaseInfo);

// 获取表列表
router.get('/tables', getTableList);

// 获取表结构
router.get('/tables/:tableName/structure', getTableStructure);

// 优化表
router.post('/tables/:tableName/optimize', optimizeTable);

// 修复表
router.post('/tables/:tableName/repair', repairTable);

// 清空表
router.post('/tables/:tableName/truncate', truncateTable);

// 删除表
router.delete('/tables/:tableName', dropTable);

// 导出表
router.get('/tables/:tableName/export', exportTable);

// 执行SQL
router.post('/execute-sql', executeSQL);

export default router;
