/**
 * 角色权限分配API路由
 * 提供角色权限分配、批量操作、权限继承等功能
 */

import express from 'express';
import { authenticate, requirePermission } from '../middleware/auth';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import { executeQuery } from '../utils/db';
import { logPermissionOperation } from '../utils/permissionLogger';

const router = express.Router();

// 应用认证中间件
router.use(authenticate);

/**
 * 获取角色权限列表
 * GET /api/role-permission/list/:roleId
 */
router.get('/list/:roleId', requirePermission('role:permission:assign'), async (req, res) => {
  try {
    const roleId = Number.parseInt(req.params.roleId);

    const sql = `
      SELECT 
        rp.*,
        p.permission_code,
        p.permission_name,
        p.permission_description,
        p.permission_type,
        p.permission_group,
        granter.username as granter_name
      FROM fd_role_permission rp
      JOIN fd_permission p ON rp.permission_id = p.permission_id
      LEFT JOIN fd_user granter ON rp.granted_by = granter.user_id
      WHERE rp.role_id = ? AND rp.status = 1
      ORDER BY p.permission_group ASC, p.sort_order ASC, p.permission_name ASC
    `;

    const rolePermissions = await executeQuery(sql, [roleId]);

    return res.json(createSuccessResponse(rolePermissions, '获取角色权限成功'));
  } catch (error) {
    console.error('❌ 获取角色权限失败:', error);
    return res.json(createErrorResponse('获取角色权限失败', ResponseCode.ERROR));
  }
});

/**
 * 为角色分配权限
 * POST /api/role-permission/assign
 */
router.post('/assign', requirePermission('role:permission:assign'), async (req, res) => {
  try {
    const { role_id, permission_id, grant_type = 'grant', data_scope = 'all', data_scope_rule, remark } = req.body;

    const operatorId = (req as any).user.userId;

    // 验证必填字段
    if (!role_id || !permission_id) {
      return res.json(createErrorResponse('角色ID和权限ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查角色是否存在
    const [role] = await executeQuery('SELECT role_id, role_name FROM fd_role WHERE role_id = ? AND status = 1', [
      role_id
    ]);
    if (!role) {
      return res.json(createErrorResponse('角色不存在或已禁用', ResponseCode.NOT_FOUND));
    }

    // 检查权限是否存在
    const [permission] = await executeQuery(
      'SELECT permission_id, permission_name FROM fd_permission WHERE permission_id = ? AND status = 1',
      [permission_id]
    );
    if (!permission) {
      return res.json(createErrorResponse('权限不存在或已禁用', ResponseCode.NOT_FOUND));
    }

    // 检查是否已经分配过该权限
    const [existingPermission] = await executeQuery(
      'SELECT id FROM fd_role_permission WHERE role_id = ? AND permission_id = ? AND status = 1',
      [role_id, permission_id]
    );

    if (existingPermission) {
      // 更新现有权限
      await executeQuery(
        `UPDATE fd_role_permission SET 
         grant_type = ?, data_scope = ?, data_scope_rule = ?, 
         granted_by = ?, remark = ?, update_time = CURRENT_TIMESTAMP
         WHERE role_id = ? AND permission_id = ?`,
        [grant_type, data_scope, data_scope_rule, operatorId, remark, role_id, permission_id]
      );
    } else {
      // 新增权限分配
      const sql = `
        INSERT INTO fd_role_permission (
          role_id, permission_id, grant_type, data_scope, data_scope_rule, granted_by, remark
        ) VALUES (?, ?, ?, ?, ?, ?, ?)
      `;

      await executeQuery(sql, [role_id, permission_id, grant_type, data_scope, data_scope_rule, operatorId, remark]);
    }

    // 记录操作日志
    await logPermissionOperation({
      operation_type: 'permission_grant',
      target_type: 'role',
      target_id: role_id,
      operator_id: operatorId,
      operation_data: {
        permission_id,
        permission_name: (permission as any).permission_name,
        grant_type,
        data_scope
      },
      client_ip: req.ip,
      user_agent: req.get('User-Agent')
    });

    return res.json(createSuccessResponse(null, '权限分配成功'));
  } catch (error) {
    console.error('❌ 权限分配失败:', error);
    return res.json(createErrorResponse('权限分配失败', ResponseCode.ERROR));
  }
});

/**
 * 移除角色权限
 * DELETE /api/role-permission/remove
 */
router.delete('/remove', requirePermission('role:permission:assign'), async (req, res) => {
  try {
    const { role_id, permission_id } = req.body;
    const operatorId = (req as any).user.userId;

    // 验证必填字段
    if (!role_id || !permission_id) {
      return res.json(createErrorResponse('角色ID和权限ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查角色权限是否存在
    const [rolePermission] = await executeQuery(
      `SELECT rp.id, p.permission_name 
       FROM fd_role_permission rp 
       JOIN fd_permission p ON rp.permission_id = p.permission_id 
       WHERE rp.role_id = ? AND rp.permission_id = ? AND rp.status = 1`,
      [role_id, permission_id]
    );

    if (!rolePermission) {
      return res.json(createErrorResponse('角色权限关系不存在', ResponseCode.NOT_FOUND));
    }

    // 移除权限（软删除）
    await executeQuery('UPDATE fd_role_permission SET status = 0 WHERE role_id = ? AND permission_id = ?', [
      role_id,
      permission_id
    ]);

    // 记录操作日志
    await logPermissionOperation({
      operation_type: 'permission_revoke',
      target_type: 'role',
      target_id: role_id,
      operator_id: operatorId,
      operation_data: {
        permission_id,
        permission_name: (rolePermission as any).permission_name
      },
      client_ip: req.ip,
      user_agent: req.get('User-Agent')
    });

    return res.json(createSuccessResponse(null, '权限移除成功'));
  } catch (error) {
    console.error('❌ 权限移除失败:', error);
    return res.json(createErrorResponse('权限移除失败', ResponseCode.ERROR));
  }
});

/**
 * 批量分配权限
 * POST /api/role-permission/batch-assign
 */
router.post('/batch-assign', requirePermission('role:permission:assign'), async (req, res) => {
  try {
    const { role_id, permission_ids, grant_type = 'grant', data_scope = 'all', data_scope_rule, remark } = req.body;
    const operatorId = (req as any).user.userId;

    // 验证必填字段
    if (!role_id) {
      return res.json(createErrorResponse('角色ID不能为空', ResponseCode.PARAM_ERROR));
    }

    if (!permission_ids || !Array.isArray(permission_ids) || permission_ids.length === 0) {
      return res.json(createErrorResponse('权限ID列表不能为空', ResponseCode.PARAM_ERROR));
    }

    const results = {
      success: 0,
      failed: 0,
      errors: [] as string[]
    };

    // 批量分配权限
    for (const permission_id of permission_ids) {
      try {
        // 检查是否已经分配过该权限
        const [existingPermission] = await executeQuery(
          'SELECT id FROM fd_role_permission WHERE role_id = ? AND permission_id = ? AND status = 1',
          [role_id, permission_id]
        );

        if (existingPermission) {
          // 更新现有权限
          await executeQuery(
            `UPDATE fd_role_permission SET 
             grant_type = ?, data_scope = ?, data_scope_rule = ?, 
             granted_by = ?, remark = ?, update_time = CURRENT_TIMESTAMP
             WHERE role_id = ? AND permission_id = ?`,
            [grant_type, data_scope, data_scope_rule, operatorId, remark, role_id, permission_id]
          );
        } else {
          // 新增权限分配
          const sql = `
            INSERT INTO fd_role_permission (
              role_id, permission_id, grant_type, data_scope, data_scope_rule, granted_by, remark
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
          `;

          await executeQuery(sql, [
            role_id,
            permission_id,
            grant_type,
            data_scope,
            data_scope_rule,
            operatorId,
            remark
          ]);
        }

        // 记录操作日志
        await logPermissionOperation({
          operation_type: 'permission_grant',
          target_type: 'role',
          target_id: role_id,
          operator_id: operatorId,
          operation_data: { permission_id, grant_type, data_scope, batch: true },
          client_ip: req.ip,
          user_agent: req.get('User-Agent')
        });

        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push(`权限${permission_id}分配失败: ${error}`);
      }
    }

    return res.json(createSuccessResponse(results, '批量权限分配完成'));
  } catch (error) {
    console.error('❌ 批量权限分配失败:', error);
    return res.json(createErrorResponse('批量权限分配失败', ResponseCode.ERROR));
  }
});

/**
 * 复制角色权限
 * POST /api/role-permission/copy
 */
router.post('/copy', requirePermission('role:copy'), async (req, res) => {
  try {
    const { source_role_id, target_role_id, overwrite = false } = req.body;
    const operatorId = (req as any).user.userId;

    // 验证必填字段
    if (!source_role_id || !target_role_id) {
      return res.json(createErrorResponse('源角色ID和目标角色ID不能为空', ResponseCode.PARAM_ERROR));
    }

    if (source_role_id === target_role_id) {
      return res.json(createErrorResponse('源角色和目标角色不能相同', ResponseCode.PARAM_ERROR));
    }

    // 检查源角色是否存在
    const [sourceRole] = await executeQuery('SELECT role_id, role_name FROM fd_role WHERE role_id = ? AND status = 1', [
      source_role_id
    ]);
    if (!sourceRole) {
      return res.json(createErrorResponse('源角色不存在或已禁用', ResponseCode.NOT_FOUND));
    }

    // 检查目标角色是否存在
    const [targetRole] = await executeQuery('SELECT role_id, role_name FROM fd_role WHERE role_id = ? AND status = 1', [
      target_role_id
    ]);
    if (!targetRole) {
      return res.json(createErrorResponse('目标角色不存在或已禁用', ResponseCode.NOT_FOUND));
    }

    // 如果需要覆盖，先清空目标角色的权限
    if (overwrite) {
      await executeQuery('UPDATE fd_role_permission SET status = 0 WHERE role_id = ?', [target_role_id]);
    }

    // 获取源角色的所有权限
    const sourcePermissions = await executeQuery(
      `SELECT permission_id, grant_type, data_scope, data_scope_rule, remark
       FROM fd_role_permission 
       WHERE role_id = ? AND status = 1`,
      [source_role_id]
    );

    const results = {
      success: 0,
      failed: 0,
      skipped: 0,
      errors: [] as string[]
    };

    // 复制权限
    for (const perm of sourcePermissions as any[]) {
      try {
        // 检查目标角色是否已有该权限
        const [existingPermission] = await executeQuery(
          'SELECT id FROM fd_role_permission WHERE role_id = ? AND permission_id = ? AND status = 1',
          [target_role_id, perm.permission_id]
        );

        if (existingPermission && !overwrite) {
          results.skipped++;
          continue;
        }

        if (existingPermission && overwrite) {
          // 更新现有权限
          await executeQuery(
            `UPDATE fd_role_permission SET 
             grant_type = ?, data_scope = ?, data_scope_rule = ?, 
             granted_by = ?, remark = ?, update_time = CURRENT_TIMESTAMP
             WHERE role_id = ? AND permission_id = ?`,
            [
              perm.grant_type,
              perm.data_scope,
              perm.data_scope_rule,
              operatorId,
              perm.remark,
              target_role_id,
              perm.permission_id
            ]
          );
        } else {
          // 新增权限
          const sql = `
            INSERT INTO fd_role_permission (
              role_id, permission_id, grant_type, data_scope, data_scope_rule, granted_by, remark
            ) VALUES (?, ?, ?, ?, ?, ?, ?)
          `;

          await executeQuery(sql, [
            target_role_id,
            perm.permission_id,
            perm.grant_type,
            perm.data_scope,
            perm.data_scope_rule,
            operatorId,
            perm.remark
          ]);
        }

        results.success++;
      } catch (error) {
        results.failed++;
        results.errors.push(`权限${perm.permission_id}复制失败: ${error}`);
      }
    }

    // 记录操作日志
    await logPermissionOperation({
      operation_type: 'permission_grant',
      target_type: 'role',
      target_id: target_role_id,
      operator_id: operatorId,
      operation_data: {
        source_role_id,
        source_role_name: (sourceRole as any).role_name,
        target_role_name: (targetRole as any).role_name,
        copy_operation: true,
        overwrite
      },
      client_ip: req.ip,
      user_agent: req.get('User-Agent')
    });

    return res.json(createSuccessResponse(results, '角色权限复制完成'));
  } catch (error) {
    console.error('❌ 角色权限复制失败:', error);
    return res.json(createErrorResponse('角色权限复制失败', ResponseCode.ERROR));
  }
});

export default router;
