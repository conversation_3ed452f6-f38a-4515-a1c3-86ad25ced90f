import { Router } from 'express';
import {
  changePassword,
  createProvider,
  createUpstreamOrder,
  deleteProvider,
  getCourseList,
  getProviderBalance,
  getProviderDetail,
  getProviderList,
  queryCourses,
  refillOrder,
  syncOrderStatus,
  testProviderConnection,
  updateProvider
} from '../controller/provider';
import {
  debugProviderConfig,
  getFieldMappingTemplates,
  getInterfaceDefinitions,
  getProviderInterfaces,
  getProviderTestLogs,
  getProviderUnifiedConfig,
  saveProviderInterface,
  saveProviderUnifiedConfig,
  saveWizardConfig,
  testProviderInterface,
  testUnifiedConfig,
  updateProviderFieldMapping
} from '../controller/providerConfig';
import { authenticate, requireAdmin } from '../middleware/auth';

const router = Router();

// 所有供应商路由都需要管理员权限
router.use(authenticate);
router.use(requireAdmin);

// ===== 供应商管理接口 =====

// 获取供应商列表
router.get('/list', getProviderList);

// 获取接口类型定义（必须在/:providerId之前）
router.get('/interface-definitions', getInterfaceDefinitions);

// 获取供应商详情
router.get('/:providerId', getProviderDetail);

// 创建供应商
router.post('/create', createProvider);

// 更新供应商
router.put('/:providerId', updateProvider);

// 删除供应商
router.delete('/:providerId', deleteProvider);

// 测试供应商API连接
router.post('/:providerId/test-connection', testProviderConnection);

// 查询供应商余额
router.get('/:providerId/balance', getProviderBalance);

// ===== 服务商对接接口 =====

// 查询课程
router.post('/:providerId/query', queryCourses);

// 获取课程列表
router.get('/:providerId/courses', getCourseList);

// 创建上游订单
router.post('/:providerId/order', createUpstreamOrder);

// 同步订单状态
router.post('/:providerId/sync', syncOrderStatus);

// 补刷订单
router.post('/:providerId/refill', refillOrder);

// 修改密码
router.post('/:providerId/password', changePassword);

// ===== 货源配置管理接口 =====

// 获取货源接口配置列表
router.get('/:providerId/interfaces', getProviderInterfaces);

// 保存货源接口配置
router.post('/:providerId/interface', saveProviderInterface);

// 获取字段映射模板
router.get('/templates/field-mapping', getFieldMappingTemplates);

// 测试货源接口
router.post('/:providerId/test', testProviderInterface);

// 获取货源测试日志
router.get('/:providerId/test-logs', getProviderTestLogs);

// 更新货源字段映射
router.put('/:providerId/field-mapping', updateProviderFieldMapping);

// 保存向导配置
router.post('/:providerId/wizard-config', saveWizardConfig);

// 调试接口
router.get('/:providerId/debug', debugProviderConfig);

// 统一配置相关接口
// 获取货源统一配置
router.get('/:providerId/unified-config', getProviderUnifiedConfig);

// 保存货源统一配置
router.put('/:providerId/unified-config', saveProviderUnifiedConfig);

// 测试统一配置接口
router.post('/:providerId/unified-config/test/:interfaceType', testUnifiedConfig);

export default router;
