import type { Request, Response } from 'express';
import { Router } from 'express';
import { authenticate } from '../middleware/auth';
import { getFileInfo, uploadDocument, uploadImage, uploadMultiple, uploadSingle } from '../middleware/upload';
import { ResponseCode } from '../middleware/response';

// 创建响应的辅助函数
function createErrorResponse(message: string, code: ResponseCode) {
  return {
    code,
    msg: message,
    data: null
  };
}

function createSuccessResponse(data: any, message = '操作成功') {
  return {
    code: ResponseCode.SUCCESS,
    msg: message,
    data
  };
}

const router = Router();

// 所有上传接口都需要认证
router.use(authenticate);

/** 单文件上传 */
router.post('/single', uploadSingle, (req: Request, res: Response) => {
  try {
    const file = req.file;

    if (!file) {
      return res.json(createErrorResponse('没有上传文件', ResponseCode.PARAM_ERROR));
    }

    const fileInfo = getFileInfo(file);

    res.json(
      createSuccessResponse(
        {
          file: fileInfo,
          message: '文件上传成功'
        },
        '文件上传成功'
      )
    );
  } catch (error) {
    console.error('📁 [上传] 单文件上传处理失败:', error);
    res.json(createErrorResponse('文件上传处理失败', ResponseCode.ERROR));
  }
});

/** 多文件上传 */
router.post('/multiple', uploadMultiple, (req: Request, res: Response) => {
  try {
    const files = req.files as Express.Multer.File[];

    if (!files || files.length === 0) {
      return res.json(createErrorResponse('没有上传文件', ResponseCode.PARAM_ERROR));
    }

    const filesInfo = files.map(file => getFileInfo(file));

    res.json(
      createSuccessResponse(
        {
          files: filesInfo,
          count: files.length,
          message: `成功上传 ${files.length} 个文件`
        },
        '文件上传成功'
      )
    );
  } catch (error) {
    console.error('📁 [上传] 多文件上传处理失败:', error);
    res.json(createErrorResponse('文件上传处理失败', ResponseCode.ERROR));
  }
});

/** 图片上传 */
router.post('/image', uploadImage, (req: Request, res: Response) => {
  try {
    const file = req.file;

    if (!file) {
      return res.json(createErrorResponse('没有上传图片', ResponseCode.PARAM_ERROR));
    }

    const fileInfo = getFileInfo(file);

    res.json(
      createSuccessResponse(
        {
          image: fileInfo,
          message: '图片上传成功'
        },
        '图片上传成功'
      )
    );
  } catch (error) {
    console.error('📁 [上传] 图片上传处理失败:', error);
    res.json(createErrorResponse('图片上传处理失败', ResponseCode.ERROR));
  }
});

/** 文档上传 */
router.post('/document', uploadDocument, (req: Request, res: Response) => {
  try {
    const file = req.file;

    if (!file) {
      return res.json(createErrorResponse('没有上传文档', ResponseCode.PARAM_ERROR));
    }

    const fileInfo = getFileInfo(file);

    res.json(
      createSuccessResponse(
        {
          document: fileInfo,
          message: '文档上传成功'
        },
        '文档上传成功'
      )
    );
  } catch (error) {
    console.error('📁 [上传] 文档上传处理失败:', error);
    res.json(createErrorResponse('文档上传处理失败', ResponseCode.ERROR));
  }
});

/** 头像上传 */
router.post('/avatar', uploadImage, (req: Request, res: Response) => {
  try {
    const file = req.file;
    const userId = (req as any).user?.userId;

    if (!file) {
      return res.json(createErrorResponse('没有上传头像', ResponseCode.PARAM_ERROR));
    }

    if (!userId) {
      return res.json(createErrorResponse('用户未登录', ResponseCode.TOKEN_INVALID));
    }

    const fileInfo = getFileInfo(file);

    // TODO: 这里可以将头像路径保存到用户表中
    // await updateUserAvatar(userId, fileInfo.url);

    res.json(
      createSuccessResponse(
        {
          avatar: fileInfo,
          userId,
          message: '头像上传成功'
        },
        '头像上传成功'
      )
    );
  } catch (error) {
    console.error('📁 [上传] 头像上传处理失败:', error);
    res.json(createErrorResponse('头像上传处理失败', ResponseCode.ERROR));
  }
});

export default router;
