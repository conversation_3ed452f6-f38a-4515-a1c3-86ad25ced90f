import { Router } from 'express';
import {
  autoSetSort,
  batchDeleteProducts,
  batchUpdateProducts,
  createProduct,
  deleteProduct,
  getProductCategories,
  getProductDetail,
  getProductList,
  updateProduct
} from '../controller/product';
import { authenticate, requireAdmin } from '../middleware/auth';

const router = Router();

// 所有商品路由都需要认证
router.use(authenticate);

// ===== 公共接口（所有登录用户可访问） =====

// 获取商品列表
router.get('/list', getProductList);

// 获取商品详情
router.get('/:productId', getProductDetail);

// 获取商品分类列表
router.get('/categories/list', getProductCategories);

// ===== 管理员接口 =====

// 创建商品
router.post('/', requireAdmin, createProduct);

// 更新商品
router.put('/:productId', requireAdmin, updateProduct);

// 删除商品
router.delete('/:productId', requireAdmin, deleteProduct);

// 批量更新商品
router.post('/batch-update', requireAdmin, batchUpdateProducts);

// 批量删除商品
router.post('/batch-delete', requireAdmin, batchDeleteProducts);

// 一键设置排序
router.post('/auto-sort', requireAdmin, autoSetSort);

export default router;
