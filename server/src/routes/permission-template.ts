import { Router } from 'express';
import {
  applyPermissionTemplate,
  createPermissionTemplate,
  deletePermissionTemplate,
  getPermissionTemplateById,
  getPermissionTemplatePermissions,
  getPermissionTemplates,
  updatePermissionTemplate
} from '../controller/permission-template';
import { authenticate, requirePermission } from '../middleware/auth';

const router = Router();

// 获取权限模板列表
router.get('/', authenticate, requirePermission('permission:view'), getPermissionTemplates);

// 获取单个权限模板
router.get('/:templateId', authenticate, requirePermission('permission:view'), getPermissionTemplateById);

// 获取模板权限
router.get(
  '/:templateId/permissions',
  authenticate,
  requirePermission('permission:view'),
  getPermissionTemplatePermissions
);

// 创建权限模板
router.post('/', authenticate, requirePermission('permission:create'), createPermissionTemplate);

// 更新权限模板
router.put('/:templateId', authenticate, requirePermission('permission:edit'), updatePermissionTemplate);

// 删除权限模板
router.delete('/:templateId', authenticate, requirePermission('permission:delete'), deletePermissionTemplate);

// 应用权限模板
router.post('/apply-template', authenticate, requirePermission('permission:edit'), applyPermissionTemplate);

export default router;
