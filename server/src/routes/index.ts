import { Router } from 'express';
import { verifyLicense } from '../middleware/license';
import { authenticate, requireAdmin } from '../middleware/auth';
import claimsRoutes from './claims';
import authRoutes from './auth';
import licenseRoutes from './license';
import securityRoutes from './security';
import configRoutes from './config';
import userRoutes from './user';
import uploadRoutes from './upload';
import emailRoutes from './email';
import orderRoutes from './order';
import paymentRoutes from './payment';
import agentRoutes from './agent';
import systemRoutes from './system';
import providerRoutes from './provider';
import logRoutes from './log';
import backupRoutes from './backup';
import statisticsRoutes from './statistics';
import monitorRoutes from './monitor';
import reportRoutes from './report';
import notificationRoutes from './notification';
import databaseRoutes from './database';
import ticketRoutes from './ticket';
import testRoutes from './test';
import queryRoutes from './query';
import productRoutes from './product';
import categoryRoutes from './category';
import platform29Routes from './platform29';
import syncRoutes from './sync';
import roleRoutes from './role';
import permissionRoutes from './permission';
import permissionTemplateRoutes from './permission-template';
// 多角色管理路由已删除 - 改为单角色模式
import rolePermissionRoutes from './rolePermission';
// import userPermissionRoutes from './user-permission';
// import permissionSyncRoutes from './permission-sync'; // 已下架
import userRoleOptimizationRoutes from './user-role-optimization';
// import userRateRoutes from './userRate';

// 创建认证路由
const authRouter = Router();

// 创建授权路由（不需要授权验证中间件）
const licenseRouter = Router();

// 创建安全管理路由（不需要授权验证中间件）
const securityRouter = Router();

// 创建其他路由
const otherRouter = Router();

// 将认证路由注册到认证路由器
// 注意：这里不需要'/auth'前缀，因为在主应用中已经添加了
authRouter.use('/', authRoutes);

// 将授权路由注册到授权路由器
// 注意：授权路由不需要授权验证中间件，因为它本身就是用来验证授权的
licenseRouter.use('/', licenseRoutes);

// 提供 claims 查询接口（需要认证）
otherRouter.use('/', claimsRoutes);

// 将安全管理路由注册到安全路由器
// 注意：安全路由不需要授权验证中间件，因为它用于管理授权安全
securityRouter.use('/', securityRoutes);

// 添加授权验证中间件到其他路由（暂时禁用）
// otherRouter.use(verifyLicense);

// 其他路由可以在这里添加
// otherRouter.use('/example', authenticate, exampleRoutes);

// 添加配置路由（需要授权验证）
otherRouter.use('/config', configRoutes);

// 添加用户路由
otherRouter.use('/user', userRoutes);

// 添加文件上传路由
otherRouter.use('/upload', uploadRoutes);

// 添加邮件路由
otherRouter.use('/email', emailRoutes);

// 添加订单路由
otherRouter.use('/order', orderRoutes);

// 添加支付路由
otherRouter.use('/payment', paymentRoutes);

// 添加代理路由
otherRouter.use('/agent', agentRoutes);

// 添加系统管理路由
otherRouter.use('/system', systemRoutes);

// 添加供应商路由
otherRouter.use('/provider', providerRoutes);

// 添加日志路由
otherRouter.use('/log', logRoutes);

// 添加备份路由
otherRouter.use('/backup', backupRoutes);

// 添加统计路由
otherRouter.use('/statistics', statisticsRoutes);

// 添加监控路由
otherRouter.use('/monitor', monitorRoutes);

// 添加报表路由
otherRouter.use('/report', reportRoutes);

// 添加通知路由
otherRouter.use('/notification', notificationRoutes);

// 添加数据库管理路由
otherRouter.use('/database', databaseRoutes);

// 添加工单路由
otherRouter.use('/ticket', ticketRoutes);

// 添加测试路由
otherRouter.use('/test', testRoutes);

// 添加查课路由
otherRouter.use('/query', queryRoutes);

// 添加商品路由
otherRouter.use('/product', productRoutes);

// 添加分类路由
otherRouter.use('/category', categoryRoutes);

// 添加29平台对接路由
otherRouter.use('/platform29', platform29Routes);

// 添加同步管理路由
otherRouter.use('/sync', syncRoutes);

// 添加角色管理路由
otherRouter.use('/role', roleRoutes);

// 添加权限管理路由
otherRouter.use('/permission', permissionRoutes);

// 添加权限模板路由
otherRouter.use('/permission/templates', permissionTemplateRoutes);

// 多角色管理路由已删除 - 改为单角色模式

// 添加角色权限管理路由
otherRouter.use('/role-permission', rolePermissionRoutes);

// 用户权限管理路由已移除（精简为仅角色授权）

// 添加权限同步路由（已下架）
// otherRouter.use('/permission-sync', permissionSyncRoutes);

// 添加用户角色优化路由
otherRouter.use('/user-role-optimization', userRoleOptimizationRoutes);

// 添加用户费率路由
// otherRouter.use('/user-rate', userRateRoutes);

// 导出路由
export default {
  authRoutes: authRouter,
  licenseRoutes: licenseRouter,
  securityRoutes: securityRouter,
  otherRoutes: otherRouter
};
