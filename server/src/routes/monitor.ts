import { Router } from 'express';
import {
  getApplicationMonitor,
  getDatabaseMonitor,
  getPerformanceMetrics,
  getSecurityMonitor,
  getSystemHealth,
  getSystemResources
} from '../controller/monitor';
import { authenticate, requirePermission } from '../middleware/auth';

const router = Router();

// 所有监控路由都需要管理员权限
router.use(authenticate);
router.use(requirePermission('system:monitor:view'));

// ===== 系统监控接口 =====

// 获取系统健康状态
router.get('/health', getSystemHealth);

// 获取性能指标
router.get('/performance', getPerformanceMetrics);

// 获取系统资源使用情况
router.get('/resources', getSystemResources);

// 获取安全监控数据
router.get('/security', getSecurityMonitor);

// 获取数据库监控信息
router.get('/database', getDatabaseMonitor);

// 获取应用监控信息
router.get('/application', getApplicationMonitor);

export default router;
