import { Router } from 'express';
import {
  advancedSync29PlatformProducts,
  deactivateInvalidProducts,
  detectInvalidProducts,
  get29PlatformConfig_API,
  get29PlatformProducts,
  getExistingProducts,
  getSyncLogs,
  sync29PlatformProducts,
  updateExistingProducts
} from '../controller/platform29';
import { authenticate, requirePermission } from '../middleware/auth';

const router = Router();

// 所有29平台路由都需要认证
router.use(authenticate);

// ===== 管理员接口 =====

// 获取29平台商品列表
router.get('/products', requirePermission('platform:view'), get29PlatformProducts);

// 同步29平台商品到本地
router.post('/sync', requirePermission('platform:config'), sync29PlatformProducts);

// 高级同步29平台商品（支持分类分配）
router.post('/advanced-sync', requirePermission('platform:config'), advancedSync29PlatformProducts);

// 一键更新已对接商品
router.post('/update-existing', requirePermission('platform:edit'), updateExistingProducts);

// 检测失效商品
router.get('/detect-invalid', requirePermission('platform:view'), detectInvalidProducts);

// 一键下架失效商品
router.post('/deactivate-invalid', requirePermission('platform:edit'), deactivateInvalidProducts);

// 获取29平台配置
router.get('/config', requirePermission('platform:view'), get29PlatformConfig_API);

// 获取同步日志
router.get('/sync-logs', requirePermission('platform:view'), getSyncLogs);

// 获取已对接的商品
router.get('/existing-products', requirePermission('platform:view'), getExistingProducts);

export default router;
