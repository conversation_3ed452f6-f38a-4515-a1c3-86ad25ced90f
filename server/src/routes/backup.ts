import { Router } from 'express';
import {
  createDatabaseBackup,
  deleteBackup,
  getBackupList,
  getBackupStats,
  restoreDatabaseBackup
} from '../controller/backup';
import { authenticate, requireAdmin } from '../middleware/auth';

const router = Router();

// 所有备份路由都需要管理员权限
router.use(authenticate);
router.use(requireAdmin);

// ===== 备份管理接口 =====

// 创建数据库备份
router.post('/database', createDatabaseBackup);

// 获取备份列表
router.get('/list', getBackupList);

// 恢复数据库备份
router.post('/:backupId/restore', restoreDatabaseBackup);

// 删除备份
router.delete('/:backupId', deleteBackup);

// 获取备份统计信息
router.get('/stats', getBackupStats);

export default router;
