import { Router } from 'express';
import {
  advancedUserFilter,
  batchUpdateUserRate,
  changePassword,
  createUser,
  deductUserBalance,
  deleteSavedUserFilter,
  deleteUser,
  getLoginAttemptInfo,
  getPasswordStrength,
  getSavedUserFilters,
  getUserBalanceRecords,
  getUserById,
  getUserInvitees,
  getUserLimitations,
  getUserList,
  getUserPermissionInheritance,
  getUserPermissions,
  getUserSimplePermissions,
  getUserStatistics,
  getUserStats,
  rechargeUserBalance,
  register,
  resolvePermissionConflict,
  saveUserFilter,
  saveUserLimitations,
  saveUserSimplePermissions,
  updateUser
} from '../controller/user';
import { authenticate, requirePermission } from '../middleware/auth';
import { checkRegistrationEnabled } from '../middleware/maintenance';

const router = Router();

// 用户注册（需要检查注册功能是否启用）
router.post('/register', checkRegistrationEnabled, register);

// 修改密码（需要认证）
router.post('/change-password', authenticate, changePassword);

// 获取密码强度（公开接口，用于前端实时验证）
router.post('/password-strength', getPasswordStrength);

// 获取登录尝试信息（管理员接口）
router.get('/login-attempts', authenticate, requirePermission('user:view'), getLoginAttemptInfo);

// ===== 用户管理接口（管理员权限） =====
// 获取用户列表
router.get('/list', authenticate, requirePermission('user:view'), getUserList);

// 获取单个用户详情
router.get('/:userId', authenticate, requirePermission('user:view'), getUserById);

// 获取用户权限
router.get('/permissions/:userId', authenticate, requirePermission('user:view'), getUserPermissions);

// 创建用户
router.post('/create', authenticate, requirePermission('user:create'), createUser);

// 更新用户信息
router.put('/update/:userId', authenticate, requirePermission('user:edit'), updateUser);

// 删除用户
router.delete('/delete/:userId', authenticate, requirePermission('user:delete'), deleteUser);

// 批量更新用户费率
router.post('/batch-rate', authenticate, requirePermission('user:edit'), batchUpdateUserRate);

// 高级用户筛选
router.post('/advanced-filter', authenticate, requirePermission('user:view'), advancedUserFilter);

// 保存筛选条件
router.post('/save-filter', authenticate, requirePermission('user:view'), saveUserFilter);

// 获取保存的筛选条件
router.get('/saved-filters', authenticate, requirePermission('user:view'), getSavedUserFilters);

// 删除保存的筛选条件
router.delete('/delete-filter/:filterId', authenticate, requirePermission('user:view'), deleteSavedUserFilter);

// 获取用户权限继承分析
router.get(
  '/permission-inheritance/:userId',
  authenticate,
  requirePermission('user:view'),
  getUserPermissionInheritance
);

// 解决权限冲突
router.post(
  '/resolve-permission-conflict',
  authenticate,
  requirePermission('user:permission:assign'),
  resolvePermissionConflict
);

// 获取用户统计信息
router.get('/stats', authenticate, requirePermission('user:view'), getUserStats);

// 获取用户简单权限
router.get('/:userId/simple-permissions', authenticate, requirePermission('user:view'), getUserSimplePermissions);

// 保存用户简单权限
router.post(
  '/:userId/simple-permissions',
  authenticate,
  requirePermission('user:permission:assign'),
  saveUserSimplePermissions
);

// 获取用户功能限制
router.get('/:userId/limitations', authenticate, requirePermission('user:view'), getUserLimitations);

// 保存用户功能限制
router.post('/:userId/limitations', authenticate, requirePermission('user:edit'), saveUserLimitations);

// 用户充值
router.post('/recharge', authenticate, requirePermission('user:edit'), rechargeUserBalance);

// 用户扣费
router.post('/deduct', authenticate, requirePermission('user:edit'), deductUserBalance);

// 获取用户余额记录
router.get('/:userId/balance-records', authenticate, requirePermission('user:view'), getUserBalanceRecords);

// 获取用户的下级用户（邀请的用户）
router.get('/invitees/:userId', authenticate, requirePermission('user:view'), getUserInvitees);

// 获取用户统计信息
router.get('/stats/:userId', authenticate, requirePermission('user:view'), getUserStatistics);

export default router;
