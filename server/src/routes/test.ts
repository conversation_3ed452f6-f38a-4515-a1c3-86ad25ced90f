import { Router } from 'express';
import {
  runAPITest,
  runCacheTest,
  runDatabaseTest,
  runHealthTest,
  runPerformanceTest,
  runSecurityTest,
  runSystemTest
} from '../controller/test';
import { authenticate, requireAdmin } from '../middleware/auth';

const router = Router();

// 所有测试路由都需要管理员权限
router.use(authenticate);
router.use(requireAdmin);

// ===== 系统测试接口 =====

// 运行完整系统测试
router.post('/system', runSystemTest);

// 运行数据库测试
router.post('/database', runDatabaseTest);

// 运行缓存测试
router.post('/cache', runCacheTest);

// 运行性能测试
router.post('/performance', runPerformanceTest);

// 运行安全测试
router.post('/security', runSecurityTest);

// 运行API测试
router.post('/api', runAPITest);

// 运行系统健康测试
router.post('/health', runHealthTest);

export default router;
