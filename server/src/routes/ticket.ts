import { Router } from 'express';
import {
  createTicket,
  getTicketDetail,
  getTicketList,
  getTicketStats,
  replyTicket,
  updateTicketStatus
} from '../controller/ticket';
import { authenticate, requireAdmin } from '../middleware/auth';

const router = Router();

// 所有工单路由都需要登录
router.use(authenticate);

// ===== 工单管理接口 =====

// 创建工单
router.post('/', createTicket);

// 获取工单列表
router.get('/', getTicketList);

// 获取工单详情
router.get('/:ticketId', getTicketDetail);

// 回复工单
router.post('/:ticketId/reply', replyTicket);

// 更新工单状态（需要管理员权限）
router.put('/:ticketId/status', requireAdmin, updateTicketStatus);

// 获取工单统计（需要管理员权限）
router.get('/admin/stats', requireAdmin, getTicketStats);

export default router;
