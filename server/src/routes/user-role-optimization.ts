import { Router } from 'express';
import { authenticate, requirePermission } from '../middleware/auth';
import {
  checkUserRoleConsistency,
  cleanupUserRoles,
  getUserSimpleRole,
  setUserPrimaryRole
} from '../controller/user-role-optimization';

const router = Router();

/**
 * 用户角色优化路由
 * 简化用户角色管理
 */

/**
 * 清理用户多余角色
 * POST /api/user-role-optimization/cleanup
 */
router.post('/cleanup', authenticate, requirePermission('user:edit'), cleanupUserRoles);

/**
 * 设置用户主要角色
 * POST /api/user-role-optimization/set-primary-role
 */
router.post('/set-primary-role', authenticate, requirePermission('user:role:assign'), setUserPrimaryRole);

/**
 * 获取用户简化角色信息
 * GET /api/user-role-optimization/user/:userId/role
 */
router.get('/user/:userId/role', authenticate, requirePermission('user:view'), getUserSimpleRole);

/**
 * 检查用户角色一致性
 * GET /api/user-role-optimization/check-consistency
 */
router.get('/check-consistency', authenticate, requirePermission('user:view'), checkUserRoleConsistency);

export default router;
