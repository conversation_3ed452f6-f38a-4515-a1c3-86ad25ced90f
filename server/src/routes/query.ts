import { Router } from 'express';
import { batchCreateOrdersFromQuery, batchQueryCourses, queryCourses } from '../controller/query';
import { authenticate } from '../middleware/auth';

const router = Router();

// 所有查课路由都需要认证
router.use(authenticate);

// ===== 查课接口 =====

// 单个账号查课
router.post('/courses', queryCourses);

// 批量查课
router.post('/batch-courses', batchQueryCourses);

// 根据查课结果批量下单
router.post('/batch-orders', batchCreateOrdersFromQuery);

export default router;
