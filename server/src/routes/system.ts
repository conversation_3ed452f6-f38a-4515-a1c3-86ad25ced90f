import { Router } from 'express';
import { authenticate, requireAdmin } from '../middleware/auth';
import {
  createAnnouncement,
  deleteAnnouncement,
  getAnnouncementList,
  getSystemConfig,
  getSystemStatistics,
  updateAnnouncement,
  updateSystemConfig
} from '../controller/system';

const router = Router();

// 所有系统管理路由都需要登录
router.use(authenticate);

// ===== 系统配置管理 =====

// 获取系统配置（所有用户可访问）
router.get('/config', getSystemConfig);

// 获取系统统计数据（所有用户可访问）
router.get('/statistics', getSystemStatistics);

// 获取系统公告列表（所有用户可访问）
router.get('/announcements', getAnnouncementList);

// ===== 管理员专用接口 =====

// 以下路由需要管理员权限
router.use(requireAdmin);

// 更新系统配置
router.put('/config', updateSystemConfig);

// 创建系统公告
router.post('/announcements', createAnnouncement);

// 更新系统公告
router.put('/announcements/:configId', updateAnnouncement);

// 删除系统公告
router.delete('/announcements/:configId', deleteAnnouncement);

export default router;
