import { Router } from 'express';
import {
  getOrderAnalytics,
  getRealTimeStats,
  getRevenueAnalytics,
  getSystemOverview,
  getUserAnalytics
} from '../controller/statistics';
import { authenticate, requireAdmin } from '../middleware/auth';

const router = Router();

// 所有统计路由都需要管理员权限
router.use(authenticate);
router.use(requireAdmin);

// ===== 统计分析接口 =====

// 获取系统概览统计
router.get('/overview', getSystemOverview);

// 获取用户统计分析
router.get('/users', getUserAnalytics);

// 获取订单统计分析
router.get('/orders', getOrderAnalytics);

// 获取收入统计分析
router.get('/revenue', getRevenueAnalytics);

// 获取实时统计数据
router.get('/realtime', getRealTimeStats);

export default router;
