import { Router } from 'express';
import { getSystemConfig, getSystemStatus, updateSystemConfig } from '../controller/system';
import { authenticate, requireAdmin } from '../middleware/auth';

const router = Router();

// 系统状态接口（需要认证但不需要管理员权限）
router.get('/status', authenticate, getSystemStatus);

// 所有其他配置接口都需要管理员权限
router.use(authenticate);
router.use(requireAdmin);

// 获取系统配置
router.get('/', getSystemConfig);

// 更新系统配置
router.put('/', updateSystemConfig);

export default router;
