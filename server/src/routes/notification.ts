import { Router } from 'express';
import {
  createAnnouncement,
  createUserNotification,
  deleteAnnouncement,
  getActiveAnnouncements,
  getAnnouncementList,
  getUserNotifications,
  markAllNotificationsAsRead,
  markNotificationAsRead,
  updateAnnouncement
} from '../controller/notification';
import { authenticate, requireAdmin } from '../middleware/auth';

const router = Router();

// ===== 系统公告管理接口（需要管理员权限） =====

// 创建系统公告
router.post('/announcement', authenticate, requireAdmin, createAnnouncement);

// 获取系统公告列表（管理员）
router.get('/announcement/admin', authenticate, requireAdmin, getAnnouncementList);

// 更新系统公告
router.put('/announcement/:announcementId', authenticate, requireAdmin, updateAnnouncement);

// 删除系统公告
router.delete('/announcement/:announcementId', authenticate, requireAdmin, deleteAnnouncement);

// ===== 用户通知管理接口（需要管理员权限） =====

// 创建用户通知
router.post('/user', authenticate, requireAdmin, createUserNotification);

// ===== 用户端通知接口（需要登录） =====

// 获取用户通知列表
router.get('/user', authenticate, getUserNotifications);

// 标记通知为已读
router.put('/user/:notificationId/read', authenticate, markNotificationAsRead);

// 批量标记通知为已读
router.put('/user/read-all', authenticate, markAllNotificationsAsRead);

// 获取有效的系统公告（用户端）
router.get('/announcement', authenticate, getActiveAnnouncements);

export default router;
