import { Router } from 'express';
import { authenticate, requirePermission } from '../middleware/auth';
import {
  batchUpdateUserRate,
  getRateLevels,
  getUserRateInfo,
  getUserRateLog,
  updateUserRate
} from '../controller/userRate';

const router = Router();

// 所有路由都需要登录
router.use(authenticate);

// 获取费率等级列表（所有用户可查看）
router.get('/levels', getRateLevels);

// 获取用户费率信息（用户可查看自己的，管理员可查看所有）
router.get('/info/:userId?', getUserRateInfo);

// 以下路由需要管理员权限
router.use(requirePermission('user:edit'));

// 更新用户费率
router.put('/:userId', updateUserRate);

// 获取费率变更日志
router.get('/log/:userId?', getUserRateLog);

// 批量更新用户费率（仅超级管理员）
router.post('/batch-update', batchUpdateUserRate);

export default router;
