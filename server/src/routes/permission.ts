/**
 * 权限管理API路由
 * 提供权限的CRUD操作、权限树结构等功能
 */

import express from 'express';
import { authenticate, requirePermission } from '../middleware/auth';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import { executeQuery } from '../utils/db';
import { logPermissionOperation } from '../utils/permissionLogger';

const router = express.Router();

// 应用认证中间件
router.use(authenticate);

/**
 * 获取权限列表
 * GET /api/permission/list
 */
router.get('/list', requirePermission('permission:view'), async (req, res) => {
  try {
    const page = Number.parseInt(req.query.page as string) || 1;
    const limit = Number.parseInt(req.query.limit as string) || 50;
    const search = (req.query.search as string) || '';
    const permission_type = req.query.permission_type as string;
    const permission_group = req.query.permission_group as string;
    const status = req.query.status as string;
    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    if (search) {
      whereClause += ' AND (permission_name LIKE ? OR permission_code LIKE ? OR permission_description LIKE ?)';
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    if (permission_type) {
      whereClause += ' AND permission_type = ?';
      params.push(permission_type);
    }

    if (permission_group) {
      whereClause += ' AND permission_group = ?';
      params.push(permission_group);
    }

    if (status !== undefined && status !== '') {
      whereClause += ' AND status = ?';
      params.push(Number.parseInt(status));
    }

    // 查询总数
    const countSql = `SELECT COUNT(*) as total FROM fd_permission ${whereClause}`;
    const [countResult] = await executeQuery(countSql, params);
    const total = (countResult as any).total;

    // 查询权限列表（简化版本）
    const listSql = `
      SELECT
        permission_id,
        permission_code,
        permission_name,
        permission_description,
        permission_type,
        permission_group,
        is_system,
        status,
        sort_order,
        create_time,
        update_time
      FROM fd_permission
      ${whereClause}
      ORDER BY permission_group ASC, sort_order ASC, create_time DESC
      LIMIT ${limit} OFFSET ${offset}
    `;

    const permissions = await executeQuery(listSql, params);

    return res.json(
      createSuccessResponse(
        {
          list: permissions,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        },
        '获取权限列表成功'
      )
    );
  } catch (error) {
    console.error('❌ 获取权限列表失败:', error);
    return res.json(createErrorResponse('获取权限列表失败', ResponseCode.ERROR));
  }
});

/**
 * 获取权限树结构
 * GET /api/permission/tree
 */
router.get('/tree', requirePermission('permission:view'), async (req, res) => {
  try {
    const permission_type = req.query.permission_type as string;
    const status = req.query.status as string;

    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    if (permission_type) {
      whereClause += ' AND permission_type = ?';
      params.push(permission_type);
    }

    if (status !== undefined && status !== '') {
      whereClause += ' AND status = ?';
      params.push(Number.parseInt(status));
    }

    // 查询所有权限
    const sql = `
      SELECT
        permission_id,
        permission_code,
        permission_name,
        permission_description,
        permission_type,
        parent_permission_id,
        permission_group,
        sort_order,
        status
      FROM fd_permission
      ${whereClause}
      ORDER BY permission_group ASC, sort_order ASC, permission_name ASC
    `;

    const permissions = await executeQuery(sql, params);

    // 构建树结构
    const permissionTree = buildPermissionTree(permissions as any[]);

    return res.json(createSuccessResponse(permissionTree, '获取权限树成功'));
  } catch (error) {
    console.error('❌ 获取权限树失败:', error);
    return res.json(createErrorResponse('获取权限树失败', ResponseCode.ERROR));
  }
});

/**
 * 获取权限分组
 * GET /api/permission/groups
 */
router.get('/groups', requirePermission('permission:view'), async (req, res) => {
  try {
    const sql = `
      SELECT
        permission_group,
        COUNT(*) as permission_count,
        COUNT(CASE WHEN status = 1 THEN 1 END) as active_count
      FROM fd_permission
      WHERE permission_group IS NOT NULL AND permission_group != ''
      GROUP BY permission_group
      ORDER BY permission_group ASC
    `;

    const groups = await executeQuery(sql);

    return res.json(createSuccessResponse(groups, '获取权限分组成功'));
  } catch (error) {
    console.error('❌ 获取权限分组失败:', error);
    return res.json(createErrorResponse('获取权限分组失败', ResponseCode.ERROR));
  }
});

/**
 * 获取权限详情
 * GET /api/permission/:id
 */
router.get('/:id', requirePermission('permission:view'), async (req, res) => {
  try {
    const permissionId = Number.parseInt(req.params.id);

    const sql = `
      SELECT
        p.*,
        pp.permission_name as parent_permission_name,
        creator.username as creator_name,
        updater.username as updater_name
      FROM fd_permission p
      LEFT JOIN fd_permission pp ON p.parent_permission_id = pp.permission_id
      LEFT JOIN fd_user creator ON p.created_by = creator.user_id
      LEFT JOIN fd_user updater ON p.updated_by = updater.user_id
      WHERE p.permission_id = ?
    `;

    const [permission] = await executeQuery(sql, [permissionId]);

    if (!permission) {
      return res.json(createErrorResponse('权限不存在', ResponseCode.NOT_FOUND));
    }

    // 获取使用该权限的角色
    const rolesSql = `
      SELECT
        r.role_id,
        r.role_name,
        r.role_code,
        rp.grant_type,
        rp.data_scope
      FROM fd_role_permission rp
      JOIN fd_role r ON rp.role_id = r.role_id
      WHERE rp.permission_id = ? AND rp.status = 1 AND r.status = 1
      ORDER BY r.role_level ASC
    `;

    const roles = await executeQuery(rolesSql, [permissionId]);

    return res.json(
      createSuccessResponse(
        {
          ...permission,
          roles
        },
        '获取权限详情成功'
      )
    );
  } catch (error) {
    console.error('❌ 获取权限详情失败:', error);
    return res.json(createErrorResponse('获取权限详情失败', ResponseCode.ERROR));
  }
});

/**
 * 创建权限
 * POST /api/permission
 */
router.post('/', requirePermission('permission:create'), async (req, res) => {
  try {
    const {
      permission_code,
      permission_name,
      permission_description,
      permission_type = 'button',
      parent_permission_id,
      resource_path,
      resource_method,
      permission_group,
      status = 1,
      sort_order = 0
    } = req.body;

    const userId = (req as any).user.userId;

    // 验证必填字段
    if (!permission_code || !permission_name) {
      return res.json(createErrorResponse('权限代码和权限名称不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查权限代码是否已存在
    const [existingPermission] = await executeQuery(
      'SELECT permission_id FROM fd_permission WHERE permission_code = ?',
      [permission_code]
    );

    if (existingPermission) {
      return res.json(createErrorResponse('权限代码已存在', ResponseCode.PARAM_ERROR));
    }

    // 创建权限
    const sql = `
      INSERT INTO fd_permission (
        permission_code, permission_name, permission_description, permission_type,
        parent_permission_id, resource_path, resource_method, permission_group,
        status, sort_order, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await executeQuery(sql, [
      permission_code,
      permission_name,
      permission_description,
      permission_type,
      parent_permission_id,
      resource_path,
      resource_method,
      permission_group,
      status,
      sort_order,
      userId
    ]);

    const permissionId = (result as any).insertId;

    // 记录操作日志
    await logPermissionOperation({
      operation_type: 'permission_grant',
      target_type: 'permission',
      target_id: permissionId,
      operator_id: userId,
      operation_data: { permission_code, permission_name, permission_type },
      client_ip: req.ip,
      user_agent: req.get('User-Agent')
    });

    return res.json(createSuccessResponse({ permission_id: permissionId }, '创建权限成功'));
  } catch (error) {
    console.error('❌ 创建权限失败:', error);
    return res.json(createErrorResponse('创建权限失败', ResponseCode.ERROR));
  }
});

/**
 * 构建权限树结构
 */
function buildPermissionTree(permissions: any[]): any[] {
  const permissionMap = new Map();
  const rootPermissions: any[] = [];

  // 创建权限映射
  permissions.forEach(permission => {
    permission.children = [];
    permissionMap.set(permission.permission_id, permission);
  });

  // 构建树结构
  permissions.forEach(permission => {
    if (permission.parent_permission_id) {
      const parent = permissionMap.get(permission.parent_permission_id);
      if (parent) {
        parent.children.push(permission);
      } else {
        rootPermissions.push(permission);
      }
    } else {
      rootPermissions.push(permission);
    }
  });

  // 按分组和排序整理
  const groupedPermissions = new Map();
  rootPermissions.forEach(permission => {
    const group = permission.permission_group || 'other';
    if (!groupedPermissions.has(group)) {
      groupedPermissions.set(group, []);
    }
    groupedPermissions.get(group).push(permission);
  });

  // 转换为数组格式
  const result: any[] = [];
  for (const [group, perms] of groupedPermissions.entries()) {
    result.push({
      permission_group: group,
      permissions: perms.sort((a: any, b: any) => a.sort_order - b.sort_order)
    });
  }

  return result.sort((a, b) => a.permission_group.localeCompare(b.permission_group));
}

export default router;
