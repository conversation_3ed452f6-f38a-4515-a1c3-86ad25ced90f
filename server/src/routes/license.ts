import { Router } from 'express';
import {
  activateSystem,
  getLicenseStatus,
  getSystemStatus,
  refreshLicense,
  resetLicense,
  revalidateSystem,
  validateLicense
} from '../controller/license';
import { authenticate, requireAdmin } from '../middleware/auth';
import { allowLicenseManagement } from '../middleware/license';

const router = Router();

// 授权管理接口允许在系统未激活时访问
router.use(allowLicenseManagement);

// 简化调试中间件
router.use((_req, _res, next) => {
  next();
});

/** 激活系统 POST /api/license/activate Body: { licenseKey: string } */
router.post('/activate', activateSystem);

/** 验证授权密钥（兼容性接口） POST /api/license/validate Body: { licenseKey: string } */
router.post('/validate', validateLicense);

/** 获取系统激活状态 GET /api/license/system-status */
router.get('/system-status', getSystemStatus);

/** 获取当前授权状态 GET /api/license/status */
router.get('/status', getLicenseStatus);

/** 手动重新验证 POST /api/license/revalidate */
router.post('/revalidate', revalidateSystem);

/** 刷新授权状态（重新验证） POST /api/license/refresh */
router.post('/refresh', refreshLicense);

/** 重置授权信息 DELETE /api/license/reset 需要管理员权限 */
router.delete('/reset', authenticate, requireAdmin, resetLicense);

/** 获取授权详细信息 - 已移除，管理功能已解耦 */
// router.get('/details', authenticate, requireAdmin, getLicenseDetails);

export default router;
