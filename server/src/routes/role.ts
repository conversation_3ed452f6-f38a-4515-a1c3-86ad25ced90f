/**
 * 角色管理API路由
 * 提供角色的CRUD操作、权限分配等功能
 */

import express from 'express';
import { authenticate, requirePermission } from '../middleware/auth';
import { ResponseCode, createErrorResponse, createSuccessResponse } from '../utils/response';
import { executeQuery } from '../utils/db';
import { logPermissionOperation } from '../utils/permissionLogger';

const router = express.Router();

// 应用认证中间件
router.use(authenticate);

/**
 * 获取角色列表
 * GET /api/role/list
 */
router.get('/list', requirePermission('role:view'), async (req, res) => {
  try {
    const page = Number.parseInt(req.query.page as string) || 1;
    const limit = Number.parseInt(req.query.limit as string) || 10;
    const search = (req.query.search as string) || '';
    const status = req.query.status as string;
    const offset = (page - 1) * limit;

    // 构建查询条件
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    if (search) {
      whereClause += ' AND (role_name LIKE ? OR role_code LIKE ? OR role_description LIKE ?)';
      params.push(`%${search}%`, `%${search}%`, `%${search}%`);
    }

    if (status !== undefined && status !== '') {
      whereClause += ' AND status = ?';
      params.push(Number.parseInt(status));
    }

    // 查询总数
    const countSql = `SELECT COUNT(*) as total FROM fd_role ${whereClause}`;
    const [countResult] = await executeQuery(countSql, params);
    const total = (countResult as any).total;

    // 查询角色列表（简化版本，避免复杂JOIN）
    const listSql = `
      SELECT
        role_id,
        role_code,
        role_name,
        role_description,
        role_level,
        is_system,
        is_default,
        status,
        sort_order,
        create_time,
        update_time
      FROM fd_role
      ${whereClause}
      ORDER BY role_level ASC, sort_order ASC, create_time DESC
      LIMIT ${limit} OFFSET ${offset}
    `;

    const roles = await executeQuery(listSql, params);

    return res.json(
      createSuccessResponse(
        {
          list: roles,
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit)
        },
        '获取角色列表成功'
      )
    );
  } catch (error) {
    console.error('❌ 获取角色列表失败:', error);
    return res.json(createErrorResponse('获取角色列表失败', ResponseCode.ERROR));
  }
});

/**
 * 获取角色详情
 * GET /api/role/:id
 */
router.get('/:id', requirePermission('role:view'), async (req, res) => {
  try {
    const roleId = Number.parseInt(req.params.id);

    const sql = `
      SELECT
        r.*,
        pr.role_name as parent_role_name,
        creator.username as creator_name,
        updater.username as updater_name
      FROM fd_role r
      LEFT JOIN fd_role pr ON r.parent_role_id = pr.role_id
      LEFT JOIN fd_user creator ON r.created_by = creator.user_id
      LEFT JOIN fd_user updater ON r.updated_by = updater.user_id
      WHERE r.role_id = ?
    `;

    const [role] = await executeQuery(sql, [roleId]);

    if (!role) {
      return res.json(createErrorResponse('角色不存在', ResponseCode.NOT_FOUND));
    }

    // 获取角色权限
    const permissionSql = `
      SELECT
        p.permission_id,
        p.permission_code,
        p.permission_name,
        p.permission_type,
        p.permission_group,
        rp.grant_type,
        rp.data_scope
      FROM fd_role_permission rp
      JOIN fd_permission p ON rp.permission_id = p.permission_id
      WHERE rp.role_id = ? AND rp.status = 1 AND p.status = 1
      ORDER BY p.permission_group, p.sort_order
    `;

    const permissions = await executeQuery(permissionSql, [roleId]);

    return res.json(
      createSuccessResponse(
        {
          ...role,
          permissions
        },
        '获取角色详情成功'
      )
    );
  } catch (error) {
    console.error('❌ 获取角色详情失败:', error);
    return res.json(createErrorResponse('获取角色详情失败', ResponseCode.ERROR));
  }
});

/**
 * 创建角色
 * POST /api/role
 */
router.post('/', requirePermission('role:create'), async (req, res) => {
  try {
    const {
      role_code,
      role_name,
      role_description,
      parent_role_id,
      role_level,
      is_default,
      status = 1,
      sort_order = 0
    } = req.body;

    const userId = (req as any).user.userId;

    // 验证必填字段
    if (!role_code || !role_name) {
      return res.json(createErrorResponse('角色代码和角色名称不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查角色代码是否已存在
    const [existingRole] = await executeQuery('SELECT role_id FROM fd_role WHERE role_code = ?', [role_code]);

    if (existingRole) {
      return res.json(createErrorResponse('角色代码已存在', ResponseCode.PARAM_ERROR));
    }

    // 创建角色
    const sql = `
      INSERT INTO fd_role (
        role_code, role_name, role_description, parent_role_id,
        role_level, is_default, status, sort_order, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await executeQuery(sql, [
      role_code,
      role_name,
      role_description,
      parent_role_id,
      role_level,
      is_default,
      status,
      sort_order,
      userId
    ]);

    const roleId = (result as any).insertId;

    // 记录操作日志
    await logPermissionOperation({
      operation_type: 'role_create',
      target_type: 'role',
      target_id: roleId,
      operator_id: userId,
      operation_data: { role_code, role_name, role_description },
      client_ip: req.ip,
      user_agent: req.get('User-Agent')
    });

    return res.json(createSuccessResponse({ role_id: roleId }, '创建角色成功'));
  } catch (error) {
    console.error('❌ 创建角色失败:', error);
    return res.json(createErrorResponse('创建角色失败', ResponseCode.ERROR));
  }
});

/**
 * 更新角色
 * PUT /api/role/:id
 */
router.put('/:id', requirePermission('role:edit'), async (req, res) => {
  try {
    const roleId = Number.parseInt(req.params.id);
    const { role_name, role_description, parent_role_id, role_level, is_default, status, sort_order } = req.body;

    const userId = (req as any).user.userId;

    // 检查角色是否存在
    const [existingRole] = await executeQuery('SELECT role_id, is_system FROM fd_role WHERE role_id = ?', [roleId]);

    if (!existingRole) {
      return res.json(createErrorResponse('角色不存在', ResponseCode.NOT_FOUND));
    }

    // 检查是否为系统角色
    if ((existingRole as any).is_system && status === 0) {
      return res.json(createErrorResponse('系统角色不能禁用', ResponseCode.PARAM_ERROR));
    }

    // 更新角色
    const sql = `
      UPDATE fd_role SET
        role_name = ?, role_description = ?, parent_role_id = ?,
        role_level = ?, is_default = ?, status = ?, sort_order = ?,
        updated_by = ?, update_time = CURRENT_TIMESTAMP
      WHERE role_id = ?
    `;

    await executeQuery(sql, [
      role_name,
      role_description,
      parent_role_id,
      role_level,
      is_default,
      status,
      sort_order,
      userId,
      roleId
    ]);

    // 记录操作日志
    await logPermissionOperation({
      operation_type: 'role_update',
      target_type: 'role',
      target_id: roleId,
      operator_id: userId,
      operation_data: { role_name, role_description, status },
      client_ip: req.ip,
      user_agent: req.get('User-Agent')
    });

    return res.json(createSuccessResponse(null, '更新角色成功'));
  } catch (error) {
    console.error('❌ 更新角色失败:', error);
    return res.json(createErrorResponse('更新角色失败', ResponseCode.ERROR));
  }
});

/**
 * 删除角色
 * DELETE /api/role/:id
 */
router.delete('/:id', requirePermission('role:delete'), async (req, res) => {
  try {
    const roleId = Number.parseInt(req.params.id);
    const userId = (req as any).user.userId;

    // 检查角色是否存在
    const [role] = await executeQuery('SELECT role_id, role_name, is_system FROM fd_role WHERE role_id = ?', [roleId]);

    if (!role) {
      return res.json(createErrorResponse('角色不存在', ResponseCode.NOT_FOUND));
    }

    // 检查是否为系统角色
    if ((role as any).is_system) {
      return res.json(createErrorResponse('系统角色不能删除', ResponseCode.PARAM_ERROR));
    }

    // 检查是否有用户使用该角色
    const [userRole] = await executeQuery('SELECT id FROM fd_user_role WHERE role_id = ? AND status = 1 LIMIT 1', [
      roleId
    ]);

    if (userRole) {
      return res.json(createErrorResponse('该角色正在被用户使用，无法删除', ResponseCode.PARAM_ERROR));
    }

    // 删除角色（级联删除相关权限）
    await executeQuery('DELETE FROM fd_role WHERE role_id = ?', [roleId]);

    // 记录操作日志
    await logPermissionOperation({
      operation_type: 'role_delete',
      target_type: 'role',
      target_id: roleId,
      operator_id: userId,
      operation_data: { role_name: (role as any).role_name },
      client_ip: req.ip,
      user_agent: req.get('User-Agent')
    });

    return res.json(createSuccessResponse(null, '删除角色成功'));
  } catch (error) {
    console.error('❌ 删除角色失败:', error);
    return res.json(createErrorResponse('删除角色失败', ResponseCode.ERROR));
  }
});

/**
 * 获取角色权限
 * GET /api/role/:id/permissions
 */
router.get('/:id/permissions', requirePermission('role:view'), async (req, res) => {
  try {
    const roleId = Number.parseInt(req.params.id);

    // 获取角色的所有权限
    const sql = `
      SELECT
        p.permission_id,
        p.permission_code,
        p.permission_name,
        p.permission_description,
        p.permission_type,
        p.permission_group,
        rp.grant_type,
        rp.data_scope,
        rp.data_scope_rule,
        rp.status as permission_status
      FROM fd_role_permission rp
      JOIN fd_permission p ON rp.permission_id = p.permission_id
      WHERE rp.role_id = ? AND rp.status = 1
      ORDER BY p.permission_group ASC, p.sort_order ASC, p.permission_name ASC
    `;

    const permissions = await executeQuery(sql, [roleId]);

    return res.json(createSuccessResponse(permissions, '获取角色权限成功'));
  } catch (error: any) {
    console.error('获取角色权限失败:', error);
    return res.json(createErrorResponse(error.message || '获取角色权限失败', ResponseCode.ERROR));
  }
});

/**
 * 设置角色权限
 * POST /api/role/:id/permissions
 */
router.post('/:id/permissions', requirePermission('role:edit'), async (req, res) => {
  try {
    const roleId = Number.parseInt(req.params.id);
    const { permissions } = req.body;
    const userId = (req as any).user.userId;

    if (!Array.isArray(permissions)) {
      return res.json(createErrorResponse('权限列表格式错误', ResponseCode.PARAM_ERROR));
    }

    // 检查角色是否存在
    const [role] = await executeQuery('SELECT role_id, is_system FROM fd_role WHERE role_id = ?', [roleId]);
    if (!role) {
      return res.json(createErrorResponse('角色不存在', ResponseCode.NOT_FOUND));
    }

    // 开始事务
    await executeQuery('START TRANSACTION');

    try {
      // 删除现有权限
      await executeQuery('DELETE FROM fd_role_permission WHERE role_id = ?', [roleId]);

      // 添加新权限
      if (permissions.length > 0) {
        // 使用循环插入每个权限，避免批量插入的语法问题
        for (const perm of permissions) {
          const insertSql = `
            INSERT INTO fd_role_permission (role_id, permission_id, grant_type, data_scope, data_scope_rule, granted_by, create_time)
            VALUES (?, ?, ?, ?, ?, ?, NOW())
          `;

          await executeQuery(insertSql, [
            roleId,
            perm.permission_id,
            perm.grant_type || 'grant',
            perm.data_scope || 'all',
            perm.data_scope_rule || null,
            userId
          ]);
        }
      }

      // 提交事务
      await executeQuery('COMMIT');

      return res.json(createSuccessResponse(null, '设置角色权限成功'));
    } catch (error) {
      // 回滚事务
      await executeQuery('ROLLBACK');
      throw error;
    }
  } catch (error: any) {
    console.error('设置角色权限失败:', error);
    return res.json(createErrorResponse(error.message || '设置角色权限失败', ResponseCode.ERROR));
  }
});

/**
 * 复制角色权限
 * POST /api/role/:id/copy-permissions
 */
router.post('/:id/copy-permissions', requirePermission('role:edit'), async (req, res) => {
  try {
    const targetRoleId = Number.parseInt(req.params.id);
    const { sourceRoleId } = req.body;
    const userId = (req as any).user.userId;

    if (!sourceRoleId) {
      return res.json(createErrorResponse('源角色ID不能为空', ResponseCode.PARAM_ERROR));
    }

    // 检查两个角色是否存在
    const [targetRole] = await executeQuery('SELECT role_id FROM fd_role WHERE role_id = ?', [targetRoleId]);
    const [sourceRole] = await executeQuery('SELECT role_id FROM fd_role WHERE role_id = ?', [sourceRoleId]);

    if (!targetRole || !sourceRole) {
      return res.json(createErrorResponse('角色不存在', ResponseCode.NOT_FOUND));
    }

    // 开始事务
    await executeQuery('START TRANSACTION');

    try {
      // 删除目标角色现有权限
      await executeQuery('DELETE FROM fd_role_permission WHERE role_id = ?', [targetRoleId]);

      // 复制源角色权限到目标角色
      const copySql = `
        INSERT INTO fd_role_permission (role_id, permission_id, grant_type, data_scope, data_scope_rule, granted_by, create_time)
        SELECT ?, permission_id, grant_type, data_scope, data_scope_rule, ?, NOW()
        FROM fd_role_permission
        WHERE role_id = ? AND status = 1
      `;

      await executeQuery(copySql, [targetRoleId, userId, sourceRoleId]);

      // 提交事务
      await executeQuery('COMMIT');

      return res.json(createSuccessResponse(null, '复制角色权限成功'));
    } catch (error) {
      // 回滚事务
      await executeQuery('ROLLBACK');
      throw error;
    }
  } catch (error: any) {
    console.error('复制角色权限失败:', error);
    return res.json(createErrorResponse(error.message || '复制角色权限失败', ResponseCode.ERROR));
  }
});

export default router;
