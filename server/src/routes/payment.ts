import { Router } from 'express';
import { authenticate, requireAdmin } from '../middleware/auth';
import {
  alipayCallback,
  createRechargeOrder,
  getFinanceList,
  getPaymentList,
  getRechargeConfig,
  manualRecharge,
  updateRechargeConfig,
  wechatCallback
} from '../controller/payment';

const router = Router();

// 所有路由都需要登录
router.use(authenticate);

// 获取支付记录列表
router.get('/list', getPaymentList);

// 获取财务记录列表
router.get('/finance/list', getFinanceList);

// 创建充值订单
router.post('/recharge', createRechargeOrder);

// 获取充值配置
router.get('/recharge/config', getRechargeConfig);

// ===== 支付回调接口（无需认证） =====

// 支付宝回调
router.post('/callback/alipay', alipayCallback);

// 微信支付回调
router.post('/callback/wechat', wechatCallback);

// 以下路由需要管理员权限
router.use(requireAdmin);

// 更新充值配置
router.put('/recharge/config/:config_id', updateRechargeConfig);

// 手动充值
router.post('/manual-recharge', manualRecharge);

export default router;
