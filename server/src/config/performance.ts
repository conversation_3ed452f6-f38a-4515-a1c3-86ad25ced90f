/**
 * 性能优化配置
 * 遵循项目规范，提供完整的性能优化配置
 */

// 数据库连接池配置
export const DB_POOL_CONFIG = {
  // 连接池大小
  connectionLimit: 10,
  // 获取连接超时时间
  acquireTimeout: 60000,
  // 连接超时时间
  timeout: 60000,
  // 重连配置
  reconnect: true,
  // 空闲连接超时时间
  idleTimeout: 300000,
  // 最大空闲连接数
  maxIdle: 5,
  // 最小空闲连接数
  minIdle: 2
};

// 缓存配置
export const CACHE_CONFIG = {
  // 默认缓存时间（秒）
  defaultTTL: 300,
  // 最大缓存条目数
  maxKeys: 1000,
  // 缓存检查间隔（秒）
  checkPeriod: 60,
  // 启用缓存统计
  useClones: false,
  // 缓存键前缀
  keyPrefix: 'fd_cache:',
  // 不同类型数据的缓存时间
  ttl: {
    user: 600, // 用户信息缓存10分钟
    config: 1800, // 系统配置缓存30分钟
    stats: 300, // 统计数据缓存5分钟
    course: 900, // 课程信息缓存15分钟
    provider: 1200 // 供应商信息缓存20分钟
  }
};

// 性能监控配置
export const PERFORMANCE_CONFIG = {
  // 启用性能监控
  enabled: true,
  // 慢请求阈值（毫秒）
  slowRequestThreshold: 1000,
  // 内存使用告警阈值（百分比）
  memoryWarningThreshold: 80,
  // CPU使用告警阈值（百分比）
  cpuWarningThreshold: 80,
  // 磁盘使用告警阈值（百分比）
  diskWarningThreshold: 85,
  // 错误率告警阈值（百分比）
  errorRateThreshold: 5,
  // 监控数据保留时间（小时）
  dataRetentionHours: 24,
  // 监控数据清理间隔（小时）
  cleanupIntervalHours: 6
};

// 请求限流配置
export const RATE_LIMIT_CONFIG = {
  // 全局限流
  global: {
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 1000, // 最大请求数
    message: '请求过于频繁，请稍后再试'
  },
  // API限流
  api: {
    windowMs: 1 * 60 * 1000, // 1分钟
    max: 100, // 最大请求数
    message: 'API请求过于频繁，请稍后再试'
  },
  // 登录限流
  login: {
    windowMs: 15 * 60 * 1000, // 15分钟
    max: 5, // 最大尝试次数
    message: '登录尝试过于频繁，请15分钟后再试'
  },
  // 注册限流
  register: {
    windowMs: 60 * 60 * 1000, // 1小时
    max: 3, // 最大注册次数
    message: '注册过于频繁，请1小时后再试'
  }
};

// 压缩配置
export const COMPRESSION_CONFIG = {
  // 启用压缩
  enabled: true,
  // 压缩级别（1-9）
  level: 6,
  // 压缩阈值（字节）
  threshold: 1024,
  // 压缩类型
  filter: (req: any, res: any) => {
    if (req.headers['x-no-compression']) {
      return false;
    }
    return true;
  }
};

// 静态资源配置
export const STATIC_CONFIG = {
  // 缓存时间（毫秒）
  maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
  // 启用ETag
  etag: true,
  // 启用Last-Modified
  lastModified: true,
  // 启用压缩
  compress: true,
  // 索引文件
  index: ['index.html'],
  // 重定向
  redirect: false
};

// 日志配置
export const LOG_CONFIG = {
  // 日志级别
  level: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
  // 日志格式
  format: 'combined',
  // 日志文件配置
  file: {
    // 启用文件日志
    enabled: true,
    // 日志文件路径
    path: './logs',
    // 文件名格式
    filename: 'access-%DATE%.log',
    // 日期格式
    datePattern: 'YYYY-MM-DD',
    // 最大文件大小
    maxSize: '20m',
    // 最大文件数
    maxFiles: '14d',
    // 压缩旧文件
    zippedArchive: true
  },
  // 控制台日志
  console: {
    enabled: process.env.NODE_ENV !== 'production',
    colorize: true
  }
};

// 安全配置
export const SECURITY_CONFIG = {
  // CORS配置
  cors: {
    origin: process.env.CORS_ORIGIN || '*',
    credentials: true,
    optionsSuccessStatus: 200
  },
  // Helmet配置
  helmet: {
    contentSecurityPolicy: false,
    crossOriginEmbedderPolicy: false
  },
  // JWT配置
  jwt: {
    expiresIn: '24h',
    refreshExpiresIn: '7d'
  },
  // 密码策略
  password: {
    minLength: 6,
    requireNumbers: true,
    requireUppercase: true,
    requireLowercase: true,
    requireSpecialChars: false
  }
};

// 数据库优化配置
export const DB_OPTIMIZATION_CONFIG = {
  // 查询超时时间（毫秒）
  queryTimeout: 30000,
  // 启用查询日志
  enableQueryLog: process.env.NODE_ENV !== 'production',
  // 慢查询阈值（毫秒）
  slowQueryThreshold: 1000,
  // 批量操作大小
  batchSize: 1000,
  // 分页默认大小
  defaultPageSize: 20,
  // 最大分页大小
  maxPageSize: 100,
  // 索引提示
  indexHints: {
    user: ['idx_username', 'idx_status'],
    order: ['idx_user_id', 'idx_status', 'idx_create_time'],
    course: ['idx_status', 'idx_platform_type'],
    provider: ['idx_status', 'idx_platform_type']
  }
};

// 内存优化配置
export const MEMORY_CONFIG = {
  // Node.js内存限制（MB）
  maxOldSpaceSize: 2048,
  // 垃圾回收配置
  gc: {
    // 启用增量标记
    incrementalMarking: true,
    // 启用并发标记
    concurrentMarking: true,
    // 启用并发清理
    concurrentSweeping: true
  },
  // 内存监控
  monitoring: {
    // 监控间隔（毫秒）
    interval: 30000,
    // 内存告警阈值（MB）
    warningThreshold: 1536,
    // 内存错误阈值（MB）
    errorThreshold: 1792
  }
};

// 前端优化配置
export const FRONTEND_CONFIG = {
  // 构建优化
  build: {
    // 启用代码分割
    codeSplitting: true,
    // 启用Tree Shaking
    treeShaking: true,
    // 启用压缩
    minify: true,
    // 启用Gzip
    gzip: true,
    // 启用Brotli
    brotli: true,
    // 资源内联阈值（字节）
    inlineThreshold: 4096
  },
  // 运行时优化
  runtime: {
    // 启用懒加载
    lazyLoading: true,
    // 启用预加载
    preloading: true,
    // 启用缓存
    caching: true,
    // 虚拟滚动阈值
    virtualScrollThreshold: 100
  }
};

// 监控告警配置
export const ALERT_CONFIG = {
  // 启用告警
  enabled: true,
  // 告警检查间隔（秒）
  checkInterval: 60,
  // 告警规则
  rules: {
    // 内存使用率告警
    memoryUsage: {
      threshold: 85,
      duration: 300, // 持续5分钟
      severity: 'warning'
    },
    // CPU使用率告警
    cpuUsage: {
      threshold: 90,
      duration: 300,
      severity: 'critical'
    },
    // 磁盘使用率告警
    diskUsage: {
      threshold: 90,
      duration: 0,
      severity: 'critical'
    },
    // 错误率告警
    errorRate: {
      threshold: 5,
      duration: 300,
      severity: 'warning'
    },
    // 响应时间告警
    responseTime: {
      threshold: 2000,
      duration: 300,
      severity: 'warning'
    }
  }
};
