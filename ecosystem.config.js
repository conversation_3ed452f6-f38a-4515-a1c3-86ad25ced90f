module.exports = {
  apps: [
    {
      name: 'soybeanadmin-frontend-dev',
      script: 'pnpm',
      args: 'dev',
      cwd: '/www/wwwroot/FDnew/SoybeanAdmin',
      env: {
        NODE_ENV: 'development',
        VITE_PORT: 5959
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      log_file: './logs/frontend-dev.log',
      out_file: './logs/frontend-dev-out.log',
      error_file: './logs/frontend-dev-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss'
    },
    {
      name: 'soybeanadmin-backend-dev',
      script: './server/start-dev.sh',
      cwd: '/www/wwwroot/FDnew/SoybeanAdmin',
      env: {
        NODE_ENV: 'development',
        PORT: 3000
      },
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      log_file: './logs/backend-dev.log',
      out_file: './logs/backend-dev-out.log',
      error_file: './logs/backend-dev-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss'
    }
  ]
};
