#!/usr/bin/env python3
import re

def fix_vue_templates():
    with open('/www/wwwroot/FDnew/SoybeanAdmin/src/views/user/index.vue', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 删除错误插入的template标签
    content = re.sub(r'\n\s*</template>\s*([^<\n])', r'\n\1', content)
    
    # 修复破坏的template结构
    fixes = [
        # 修复费率信息列
        (r'(<template #default="{ row }">\s*)<div class="flex items-center gap-4px">\s*</template>\s*<ElTag', 
         r'\1<div class="flex items-center gap-4px">\n              <ElTag'),
        
        # 修复角色信息列
        (r'(<template #default="{ row }">\s*)<div class="flex items-center gap-4px">\s*</template>\s*<ElTag', 
         r'\1<div class="flex items-center gap-4px">\n              <ElTag'),
    ]
    
    for pattern, replacement in fixes:
        content = re.sub(pattern, replacement, content, flags=re.MULTILINE | re.DOTALL)
    
    with open('/www/wwwroot/FDnew/SoybeanAdmin/src/views/user/index.vue', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("Template fixes applied")

if __name__ == "__main__":
    fix_vue_templates()
