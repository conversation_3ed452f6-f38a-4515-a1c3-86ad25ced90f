---
type: "always_apply"
---

这是一个基于SoybeanAdmin的ElementPlus 版本项目，严格按照SoybeanAdmin项目规范开发，参考开发文档：“https://docs.soybeanjs.cn/zh/guide/request/backend.html”
本项目部署在宝塔中，通过创建网页实现外网访问，部署在5959端口，通过代理转发到前端运行端口上。
数据库为newfd，密码为jx7KFPLnEsr4fzi7
后端环境变量文件地址：/www/wwwroot/FDnew/SoybeanAdmin/server/.env
前端环境变量文件地址：/www/wwwroot/FDnew/SoybeanAdmin/.env
回答前必须先了解项目架构后再进行修改。
开发前端要求：查看项目原有的相关组件和技术栈，确保样式和代码规范要符合项目
添加功能或者开发前端时必须查看“https://docs.soybeanjs.cn/zh/guide/request/backend.html”，制定详细计划，然后逐步完成任务。
