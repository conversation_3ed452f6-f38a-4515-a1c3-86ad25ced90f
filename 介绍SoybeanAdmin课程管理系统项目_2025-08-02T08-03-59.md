[x] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:修复会话频繁失效问题 DESCRIPTION:解决每次API请求都显示'用户会话已失效'的问题，优化会话管理机制
--[x] NAME:分析会话失效根因 DESCRIPTION:深入分析SessionManager会话验证逻辑，检查会话超时配置、会话创建和验证流程
--[x] NAME:检查会话超时配置 DESCRIPTION:检查ConfigManager中的sessionTimeout配置，确认是否设置过短导致频繁失效
--[x] NAME:优化会话验证逻辑 DESCRIPTION:修复auth.ts中的会话验证逻辑，确保会话正确创建和维护
--[x] NAME:测试会话管理修复 DESCRIPTION:验证会话管理修复后，API请求不再频繁显示会话失效
-[x] NAME:修复压力测试进度停滞问题 DESCRIPTION:解决压力测试进度一直停在24%不变的问题，确保订单状态能正确更新
--[x] NAME:分析订单状态停滞原因 DESCRIPTION:分析为什么订单状态一直保持在30个待处理、46个处理中、24个失败，没有变成已完成
--[x] NAME:检查同步调度器状态 DESCRIPTION:检查SyncScheduler是否正常运行，是否正确处理压力测试订单
--[x] NAME:检查29平台接口状态 DESCRIPTION:验证29平台的同步接口是否正常工作，能否正确返回订单状态更新
--[x] NAME:修复订单状态更新逻辑 DESCRIPTION:修复订单状态更新逻辑，确保同步调度器能正确处理订单状态变更
--[x] NAME:测试订单状态更新 DESCRIPTION:验证修复后压力测试订单状态能正常更新，进度不再停滞
-[x] NAME:删除同步压力测试模块 DESCRIPTION:完全删除前后端的同步压力测试相关代码，包括API接口、前端页面、测试文件等
--[x] NAME:分析压力测试相关代码 DESCRIPTION:全面扫描项目中所有与压力测试相关的文件和代码，包括前端、后端、API接口等
--[x] NAME:删除后端压力测试代码 DESCRIPTION:删除server目录下所有压力测试相关文件和API接口
--[x] NAME:删除前端压力测试代码 DESCRIPTION:删除前端目录下所有压力测试相关页面和组件
--[x] NAME:清理路由和导航 DESCRIPTION:从路由配置和导航菜单中移除压力测试相关项
-[x] NAME:彻底修复会话频繁失效问题 DESCRIPTION:深入排查并彻底解决用户会话频繁失效的问题，确保会话管理完全正常
--[x] NAME:深入分析会话失效原因 DESCRIPTION:分析为什么会话仍然频繁失效，检查JWT验证、会话存储、超时机制等
--[x] NAME:检查会话创建和验证流程 DESCRIPTION:验证会话是否正确创建、存储和验证，检查整个流程的一致性
--[x] NAME:优化会话管理机制 DESCRIPTION:重新设计和优化会话管理机制，确保稳定可靠
--[x] NAME:测试会话管理修复效果 DESCRIPTION:全面测试会话管理修复后的效果，确保不再出现频繁失效
-[/] NAME:完善用户管理界面 DESCRIPTION:全面升级用户管理系统，实现完整的权限管理、批量操作、角色分配等功能
--[x] NAME:数据库权限表设计 DESCRIPTION:设计并创建完整的权限管理数据库表结构：角色表、权限表、用户角色关联表、角色权限关联表
---[x] NAME:分析现有权限系统 DESCRIPTION:深入分析当前项目的权限系统架构，识别优势和不足，为升级方案制定基础
---[x] NAME:设计数据库表结构 DESCRIPTION:设计完整的RBAC权限管理数据库表结构，包括角色、权限、用户角色关联等表
---[x] NAME:创建数据库表 DESCRIPTION:执行数据库表创建脚本，初始化基础数据（默认角色和权限）
--[x] NAME:后端权限管理API DESCRIPTION:实现完整的后端权限管理API：角色CRUD、权限CRUD、用户角色分配、批量权限操作
---[x] NAME:角色管理API DESCRIPTION:实现角色的CRUD操作：创建、查询、更新、删除角色，支持角色复制和批量操作
---[x] NAME:权限管理API DESCRIPTION:实现权限的CRUD操作：创建、查询、更新、删除权限，支持权限分组和层级管理
---[x] NAME:用户角色分配API DESCRIPTION:实现用户角色分配管理：单个用户角色分配、批量用户角色分配、角色继承
---[x] NAME:角色权限分配API DESCRIPTION:实现角色权限分配管理：角色权限绑定、批量权限分配、权限继承和复制
--[x] NAME:权限验证中间件升级 DESCRIPTION:升级现有的权限验证中间件，支持细粒度权限控制：API权限、功能权限、数据权限
---[x] NAME:升级认证中间件 DESCRIPTION:升级现有的authenticate中间件，支持基于RBAC的权限验证
---[x] NAME:实现API权限控制 DESCRIPTION:实现细粒度的API权限控制，支持基于路由和操作的权限验证
---[x] NAME:实现功能权限控制 DESCRIPTION:实现功能级别的权限控制，支持按钮、菜单、操作等细粒度权限
---[x] NAME:实现数据权限控制 DESCRIPTION:实现数据级别的权限控制，支持基于用户、部门、组织的数据访问控制
--[x] NAME:用户管理界面升级 DESCRIPTION:全面升级前端用户管理界面：批量操作、角色分配、权限管理、高级筛选、数据导入导出
---[x] NAME:用户列表界面升级 DESCRIPTION:升级用户列表界面：添加批量选择、高级筛选、排序、导入导出功能
---[/] NAME:用户详情界面 DESCRIPTION:创建用户详情界面：显示用户完整信息、角色权限、操作日志、统计信息
---[x] NAME:批量操作功能 DESCRIPTION:实现批量操作功能：批量删除、批量启用禁用、批量角色分配、批量修改属性
---[x] NAME:角色分配界面 DESCRIPTION:创建用户角色分配界面：角色选择、权限预览、角色继承、临时权限
--[x] NAME:角色权限管理界面 DESCRIPTION:创建全新的角色权限管理界面：角色创建编辑、权限分配、权限继承、角色复制
---[x] NAME:角色管理主界面 DESCRIPTION:创建角色管理主界面：角色列表、角色创建编辑、角色复制、角色删除
---[x] NAME:权限分配界面 DESCRIPTION:创建权限分配界面：权限树展示、权限选择、权限继承、权限模板
---[ ] NAME:角色权限预览 DESCRIPTION:实现角色权限预览功能：权限清单、权限对比、权限分析、权限冲突检测
---[ ] NAME:角色模板管理 DESCRIPTION:实现角色模板管理：预设角色模板、自定义模板、模板导入导出、模板应用
--[ ] NAME:权限管理组件开发 DESCRIPTION:开发通用的权限管理组件：权限选择器、角色选择器、权限树组件、批量操作组件
---[ ] NAME:权限选择器组件 DESCRIPTION:开发通用的权限选择器组件：支持树形选择、多选、搜索、权限分组
---[ ] NAME:角色选择器组件 DESCRIPTION:开发通用的角色选择器组件：支持单选多选、角色搜索、角色分组、角色预览
---[ ] NAME:权限树组件 DESCRIPTION:开发权限树展示组件：支持层级展示、权限继承、权限冲突检测、权限搜索
---[ ] NAME:批量操作组件 DESCRIPTION:开发批量操作组件：支持批量选择、批量操作确认、进度显示、操作结果反馈
-[ ] NAME: DESCRIPTION: