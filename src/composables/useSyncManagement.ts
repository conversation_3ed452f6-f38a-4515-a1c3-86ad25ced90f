/**
 * 同步管理统一状态管理
 * 简化前后端交互，提升用户体验
 */

import { computed, onMounted, onUnmounted, reactive, ref } from 'vue';
import { ElMessage, ElNotification } from 'element-plus';
import {
  fetchOrderStats,
  fetchSyncConfig,
  fetchSyncHistory,
  fetchSyncStats,
  fetchSyncStatus,
  updateSyncConfig
} from '@/service/api/sync';

// 全局状态
const globalState = reactive({
  config: {} as any,
  status: {} as any,
  orderStats: {} as any,
  syncStats: {} as any,
  history: [] as any[],
  loading: false,
  lastUpdate: 0
});

// 自动刷新定时器
let refreshTimer: NodeJS.Timeout | undefined;

export function useSyncManagement() {
  const loading = ref(false);
  const autoRefresh = ref(true);

  // 计算属性
  const isRunning = computed(() => globalState.status.isRunning || false);
  const isEnabled = computed(() => globalState.config.enabled || false);
  const needsAttention = computed(() => !isEnabled.value || !isRunning.value);

  /**
   * 加载所有数据
   */
  async function loadAllData(showLoading = true) {
    if (showLoading) {
      loading.value = true;
      globalState.loading = true;
    }

    try {
      const promises = [loadConfig(), loadStatus(), loadOrderStats(), loadSyncStats(), loadHistory()];

      await Promise.allSettled(promises);
      globalState.lastUpdate = Date.now();

      console.log('✅ [状态管理] 数据加载完成');
    } catch (error) {
      console.error('❌ [状态管理] 数据加载失败:', error);
      ElMessage.error('数据加载失败');
    } finally {
      loading.value = false;
      globalState.loading = false;
    }
  }

  /**
   * 加载配置
   */
  async function loadConfig() {
    try {
      const response = await fetchSyncConfig();
      globalState.config = response || {};
      return response;
    } catch (error) {
      console.error('❌ [状态管理] 加载配置失败:', error);
      globalState.config = getDefaultConfig();
      throw error;
    }
  }

  /**
   * 加载状态
   */
  async function loadStatus() {
    try {
      const response = await fetchSyncStatus();
      globalState.status = response || {};
      return response;
    } catch (error) {
      console.error('❌ [状态管理] 加载状态失败:', error);
      globalState.status = getDefaultStatus();
      throw error;
    }
  }

  /**
   * 加载订单统计
   */
  async function loadOrderStats() {
    try {
      const response = await fetchOrderStats();
      globalState.orderStats = response || {};
      return response;
    } catch (error) {
      console.error('❌ [状态管理] 加载订单统计失败:', error);
      globalState.orderStats = getDefaultOrderStats();
      throw error;
    }
  }

  /**
   * 加载同步统计
   */
  async function loadSyncStats() {
    try {
      const response = await fetchSyncStats();
      globalState.syncStats = response || {};
      return response;
    } catch (error) {
      console.error('❌ [状态管理] 加载同步统计失败:', error);
      globalState.syncStats = getDefaultSyncStats();
      throw error;
    }
  }

  /**
   * 加载历史记录
   */
  async function loadHistory() {
    try {
      const response = await fetchSyncHistory({ days: 7 });
      globalState.history = response || [];
      return response;
    } catch (error) {
      console.error('❌ [状态管理] 加载历史记录失败:', error);
      globalState.history = [];
      throw error;
    }
  }

  /**
   * 保存配置 - 统一配置控制
   */
  async function saveConfig(config: any) {
    loading.value = true;

    try {
      console.log('🔧 [状态管理] 保存配置...', config);
      console.log('🔧 [状态管理] 配置详细信息:', {
        enableRateLimit: config.enableRateLimit,
        rateLimit: config.rateLimit,
        rateLimitType: typeof config.rateLimit,
        rateLimitValue: config.rateLimit,
        allKeys: Object.keys(config)
      });

      // 配置验证
      const validation = validateConfig(config);
      if (!validation.valid) {
        ElMessage.error(`配置验证失败: ${validation.errors.join(', ')}`);
        return false;
      }

      // 显示警告信息（如果有）
      if (validation.warnings && validation.warnings.length > 0) {
        ElMessage.warning(`配置警告: ${validation.warnings.join(', ')}`);
      }

      const result = await updateSyncConfig({ config });
      console.log('✅ [状态管理] 配置保存响应:', result);

      // 显示详细的保存结果
      if (result.data?.schedulerRestarted) {
        ElNotification.success({
          title: '配置已保存',
          message: '调度器已重启以应用新配置',
          duration: 3000
        });
      } else if (result.data?.currentStatus) {
        ElNotification.success({
          title: '配置已保存',
          message: '自动同步已启动',
          duration: 3000
        });
      } else if (config.enabled) {
        ElNotification.success({
          title: '配置已保存',
          message: '自动同步已启用',
          duration: 3000
        });
      } else {
        ElNotification.success({
          title: '配置已保存',
          message: '自动同步已禁用',
          duration: 3000
        });
      }

      // 刷新所有数据
      await loadAllData(false);

      console.log('✅ [状态管理] 配置保存完成');
      return true;
    } catch (error) {
      console.error('❌ [状态管理] 保存配置失败:', error);
      ElMessage.error(`保存配置失败: ${error instanceof Error ? error.message : '未知错误'}`);
      return false;
    } finally {
      loading.value = false;
    }
  }

  /**
   * 启动自动刷新
   */
  function startAutoRefresh() {
    if (refreshTimer) return;

    refreshTimer = setInterval(async () => {
      if (autoRefresh.value && !loading.value) {
        try {
          await loadStatus();
        } catch (error) {
          console.warn('⚠️ [状态管理] 自动刷新失败:', error);
        }
      }
    }, 10000); // 每10秒刷新状态

    console.log('📊 [状态管理] 自动刷新已启动');
  }

  /**
   * 停止自动刷新
   */
  function stopAutoRefresh() {
    if (refreshTimer) {
      clearInterval(refreshTimer);
      refreshTimer = undefined;
      console.log('📊 [状态管理] 自动刷新已停止');
    }
  }

  /**
   * 配置验证 - 与后端保持一致
   */
  function validateConfig(config: any) {
    const errors: string[] = [];
    const warnings: string[] = [];

    console.log('🔍 [前端验证] 开始验证配置:', config);

    // 基础配置验证
    if (typeof config.enabled !== 'boolean') {
      errors.push('启用状态必须是布尔值');
    }

    if (config.maxConcurrency !== undefined) {
      if (config.maxConcurrency < 1 || config.maxConcurrency > 100) {
        errors.push('最大并发数必须在1-100之间');
      }
    }

    if (config.batchSize !== undefined) {
      if (config.batchSize < 1 || config.batchSize > 1000) {
        errors.push('批处理大小必须在1-1000之间');
      }
    }

    // 速率限制验证 - 与后端保持一致
    if (config.enableRateLimit === true) {
      if (config.rateLimit === undefined || config.rateLimit === null) {
        errors.push('启用速率限制时，速率限制值不能为空');
      } else if (config.rateLimit < 1 || config.rateLimit > 100) {
        errors.push('速率限制必须在1-100之间');
      }

      // 检查速率限制与并发数的合理性
      if (config.maxConcurrency && config.rateLimit > config.maxConcurrency * 2) {
        warnings.push(`速率限制(${config.rateLimit}/s)可能过高，建议不超过并发数(${config.maxConcurrency})的2倍`);
      }
    }

    // 重试配置验证
    if (config.maxRetries !== undefined) {
      if (config.maxRetries < 0 || config.maxRetries > 10) {
        errors.push('最大重试次数必须在0-10之间');
      }
    }

    if (config.retryDelaySeconds !== undefined) {
      if (config.retryDelaySeconds < 1 || config.retryDelaySeconds > 300) {
        errors.push('重试延迟必须在1-300秒之间');
      }
    }

    // 时间限制验证
    if (config.maxOrderAgeHours !== undefined) {
      if (config.maxOrderAgeHours < 1 || config.maxOrderAgeHours > 720) {
        errors.push('最大订单年龄必须在1-720小时之间');
      }
    }

    if (config.minUpdateIntervalMinutes !== undefined) {
      if (config.minUpdateIntervalMinutes < 1 || config.minUpdateIntervalMinutes > 60) {
        errors.push('最小更新间隔必须在1-60分钟之间');
      }
    }

    console.log('🔍 [前端验证] 验证结果:', {
      valid: errors.length === 0,
      errors: errors.length,
      warnings: warnings.length,
      errorMessages: errors,
      warningMessages: warnings
    });

    return {
      valid: errors.length === 0,
      errors,
      warnings
    };
  }

  // 默认值函数 - 与后端保持一致
  function getDefaultConfig() {
    return {
      enabled: false,
      autoStartOnBoot: false,
      maxConcurrency: 10,
      batchSize: 50,

      // 速率限制配置
      enableRateLimit: true,
      rateLimit: 5,

      // 重试配置
      maxRetries: 3,
      retryDelaySeconds: 60,

      // 时间限制配置
      maxOrderAgeHours: 168,
      minUpdateIntervalMinutes: 5,

      // 订单状态配置
      orderStatusConfigs: [],

      // 优先级规则
      priorityRules: []
    };
  }

  function getDefaultStatus() {
    return {
      isRunning: false,
      metrics: {
        totalProcessed: 0,
        successCount: 0,
        errorCount: 0
      },
      queueStatus: { total: 0 },
      healthStatus: { status: 'unknown' }
    };
  }

  function getDefaultOrderStats() {
    return {
      totalOrders: 0,
      submittedOrders: 0,
      pendingSync: 0,
      statusDistribution: {}
    };
  }

  function getDefaultSyncStats() {
    return {
      todaySync: 0,
      avgProcessingTime: 0,
      successRate: 0,
      errorRate: 0,
      throughput: 0
    };
  }

  // 生命周期管理
  onMounted(() => {
    loadAllData();
    startAutoRefresh();
  });

  onUnmounted(() => {
    stopAutoRefresh();
  });

  return {
    // 状态
    config: computed(() => globalState.config),
    status: computed(() => globalState.status),
    orderStats: computed(() => globalState.orderStats),
    syncStats: computed(() => globalState.syncStats),
    history: computed(() => globalState.history),
    loading,
    autoRefresh,

    // 计算属性
    isRunning,
    isEnabled,
    needsAttention,

    // 方法
    loadAllData,
    loadConfig,
    loadStatus,
    saveConfig,
    startAutoRefresh,
    stopAutoRefresh,
    validateConfig
  };
}
