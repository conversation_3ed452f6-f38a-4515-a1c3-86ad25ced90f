import { onMounted, onBeforeUnmount, ref, computed } from 'vue';

/**
 * 移动端断点检测
 * - 默认断点: 768px 以下为移动端
 * - 提供 isMobile、width、setBreakpoint 能力
 */
export function useIsMobile(initialBreakpoint = 768) {
  const width = ref<number>(typeof window !== 'undefined' ? window.innerWidth : 1024);
  const breakpoint = ref<number>(initialBreakpoint);

  const isMobile = computed(() => width.value < breakpoint.value);

  function updateWidth() {
    if (typeof window === 'undefined') return;
    width.value = window.innerWidth;
  }

  function setBreakpoint(bp: number) {
    breakpoint.value = bp;
  }

  onMounted(() => {
    updateWidth();
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', updateWidth, { passive: true });
    }
  });

  onBeforeUnmount(() => {
    if (typeof window !== 'undefined') {
      window.removeEventListener('resize', updateWidth);
    }
  });

  return { isMobile, width, breakpoint, setBreakpoint };
}

