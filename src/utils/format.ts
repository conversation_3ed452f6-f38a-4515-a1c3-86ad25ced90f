/**
 * 数字格式化工具函数
 */

/**
 * 安全的数字格式化函数，防止 toFixed 调用错误
 * @param value 要格式化的值
 * @param decimals 小数位数，默认2位
 * @returns 格式化后的字符串
 */
export function safeToFixed(value: any, decimals: number = 2): string {
  const num = typeof value === 'number' ? value : Number(value) || 0;
  return num.toFixed(decimals);
}

/**
 * 格式化货币显示
 * @param value 金额值
 * @param currency 货币符号，默认为 ¥
 * @param decimals 小数位数，默认2位
 * @returns 格式化后的货币字符串
 */
export function formatCurrency(value: any, currency: string = '¥', decimals: number = 2): string {
  return `${currency}${safeToFixed(value, decimals)}`;
}

/**
 * 格式化百分比
 * @param value 百分比值（0-100）
 * @param decimals 小数位数，默认2位
 * @returns 格式化后的百分比字符串
 */
export function formatPercentage(value: any, decimals: number = 2): string {
  return `${safeToFixed(value, decimals)}%`;
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 * @returns 格式化后的文件大小字符串
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
}

/**
 * 格式化数字，添加千分位分隔符
 * @param value 数字值
 * @param decimals 小数位数，默认2位
 * @returns 格式化后的数字字符串
 */
export function formatNumber(value: any, decimals: number = 2): string {
  const num = typeof value === 'number' ? value : Number(value) || 0;
  return num.toLocaleString('zh-CN', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  });
}

/**
 * 格式化日期时间
 * @param value 日期值
 * @param format 格式类型
 * @returns 格式化后的日期字符串
 */
export function formatDateTime(
  value: string | Date | number,
  format: 'date' | 'datetime' | 'time' = 'datetime'
): string {
  const date = typeof value === 'string' || typeof value === 'number' ? new Date(value) : value;

  if (isNaN(date.getTime())) {
    return '-';
  }

  switch (format) {
    case 'date':
      return date.toLocaleDateString('zh-CN');
    case 'time':
      return date.toLocaleTimeString('zh-CN');
    case 'datetime':
    default:
      return date.toLocaleString('zh-CN');
  }
}

/**
 * 格式化运行时间
 * @param seconds 秒数
 * @returns 格式化后的运行时间字符串
 */
export function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (days > 0) {
    return `${days}天 ${hours}小时 ${minutes}分钟`;
  } else if (hours > 0) {
    return `${hours}小时 ${minutes}分钟`;
  }
  return `${minutes}分钟`;
}

/**
 * 安全的数字转换
 * @param value 要转换的值
 * @param defaultValue 默认值
 * @returns 转换后的数字
 */
export function safeNumber(value: any, defaultValue: number = 0): number {
  const num = typeof value === 'number' ? value : Number(value);
  return isNaN(num) ? defaultValue : num;
}

/**
 * 检查值是否为有效数字
 * @param value 要检查的值
 * @returns 是否为有效数字
 */
export function isValidNumber(value: any): boolean {
  return typeof value === 'number' && !isNaN(value) && isFinite(value);
}
