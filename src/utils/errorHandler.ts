/**
 * 统一错误处理工具
 * 提供友好的错误提示和处理机制
 */

import { ElMessage, ElMessageBox, ElNotification } from 'element-plus';

export interface ErrorInfo {
  code?: string;
  message: string;
  details?: any;
  action?: string;
  timestamp?: number;
}

export interface ErrorHandlerOptions {
  showMessage?: boolean;
  showNotification?: boolean;
  logToConsole?: boolean;
  reportToServer?: boolean;
  customHandler?: (error: ErrorInfo) => void;
}

class ErrorHandler {
  private errorHistory: ErrorInfo[] = [];
  private maxHistorySize = 100;

  /**
   * 处理错误
   */
  handle(error: any, options: ErrorHandlerOptions = {}) {
    const errorInfo = this.normalizeError(error);

    // 记录到历史
    this.addToHistory(errorInfo);

    // 默认选项
    const opts = {
      showMessage: true,
      showNotification: false,
      logToConsole: true,
      reportToServer: false,
      ...options
    };

    // 控制台日志
    if (opts.logToConsole) {
      console.error('❌ [错误处理]', errorInfo);
    }

    // 显示消息
    if (opts.showMessage) {
      this.showErrorMessage(errorInfo);
    }

    // 显示通知
    if (opts.showNotification) {
      this.showErrorNotification(errorInfo);
    }

    // 自定义处理
    if (opts.customHandler) {
      opts.customHandler(errorInfo);
    }

    // 上报服务器（可选）
    if (opts.reportToServer) {
      this.reportToServer(errorInfo);
    }

    return errorInfo;
  }

  /**
   * 处理网络错误
   */
  handleNetworkError(error: any, context?: string) {
    const errorInfo = this.normalizeError(error);

    if (this.isNetworkError(error)) {
      errorInfo.message = '网络连接失败，请检查网络设置';
      errorInfo.action = '重试';
    } else if (this.isTimeoutError(error)) {
      errorInfo.message = '请求超时，请稍后重试';
      errorInfo.action = '重试';
    } else if (this.isAuthError(error)) {
      errorInfo.message = '登录已过期，请重新登录';
      errorInfo.action = '重新登录';
    }

    if (context) {
      errorInfo.message = `${context}: ${errorInfo.message}`;
    }

    return this.handle(errorInfo, {
      showNotification: true,
      reportToServer: true
    });
  }

  /**
   * 处理配置错误
   */
  handleConfigError(error: any, suggestions?: string[]) {
    const errorInfo = this.normalizeError(error);

    let message = errorInfo.message;
    if (suggestions && suggestions.length > 0) {
      message += `\n\n建议：\n${suggestions.map(s => `• ${s}`).join('\n')}`;
    }

    ElMessageBox.alert(message, '配置错误', {
      type: 'error',
      confirmButtonText: '我知道了'
    });

    return this.handle(errorInfo, {
      showMessage: false,
      logToConsole: true
    });
  }

  /**
   * 处理业务逻辑错误
   */
  handleBusinessError(error: any, context?: string) {
    const errorInfo = this.normalizeError(error);

    if (context) {
      errorInfo.message = `${context}: ${errorInfo.message}`;
    }

    return this.handle(errorInfo, {
      showMessage: true,
      showNotification: false
    });
  }

  /**
   * 显示确认对话框
   */
  async showConfirmDialog(message: string, title: string = '确认操作', options: any = {}): Promise<boolean> {
    try {
      await ElMessageBox.confirm(message, title, {
        type: 'warning',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        ...options
      });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取错误历史
   */
  getErrorHistory(): ErrorInfo[] {
    return [...this.errorHistory];
  }

  /**
   * 清空错误历史
   */
  clearErrorHistory(): void {
    this.errorHistory = [];
  }

  /**
   * 标准化错误对象
   */
  private normalizeError(error: any): ErrorInfo {
    const timestamp = Date.now();

    if (typeof error === 'string') {
      return { message: error, timestamp };
    }

    if (error instanceof Error) {
      return {
        message: error.message,
        details: error.stack,
        timestamp
      };
    }

    if (error && typeof error === 'object') {
      return {
        code: error.code || error.status,
        message: error.message || error.msg || '未知错误',
        details: error.details || error.data,
        timestamp
      };
    }

    return {
      message: '未知错误',
      details: error,
      timestamp
    };
  }

  /**
   * 显示错误消息
   */
  private showErrorMessage(errorInfo: ErrorInfo) {
    ElMessage.error({
      message: errorInfo.message,
      duration: 5000,
      showClose: true
    });
  }

  /**
   * 显示错误通知
   */
  private showErrorNotification(errorInfo: ErrorInfo) {
    ElNotification.error({
      title: '操作失败',
      message: errorInfo.message,
      duration: 8000,
      position: 'top-right'
    });
  }

  /**
   * 添加到历史记录
   */
  private addToHistory(errorInfo: ErrorInfo) {
    this.errorHistory.unshift(errorInfo);

    // 限制历史记录大小
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory = this.errorHistory.slice(0, this.maxHistorySize);
    }
  }

  /**
   * 上报到服务器
   */
  private async reportToServer(errorInfo: ErrorInfo) {
    try {
      // 这里可以实现错误上报逻辑
      console.log('📊 [错误上报]', errorInfo);
    } catch (error) {
      console.warn('⚠️ [错误上报] 上报失败:', error);
    }
  }

  /**
   * 判断是否为网络错误
   */
  private isNetworkError(error: any): boolean {
    return (
      error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error') || error.message?.includes('网络错误')
    );
  }

  /**
   * 判断是否为超时错误
   */
  private isTimeoutError(error: any): boolean {
    return error.code === 'TIMEOUT' || error.message?.includes('timeout') || error.message?.includes('超时');
  }

  /**
   * 判断是否为认证错误
   */
  private isAuthError(error: any): boolean {
    return (
      error.code === '1004' ||
      error.status === 401 ||
      error.message?.includes('未授权') ||
      error.message?.includes('登录')
    );
  }
}

// 创建全局实例
export const errorHandler = new ErrorHandler();

// 便捷方法
export const handleError = (error: any, options?: ErrorHandlerOptions) => errorHandler.handle(error, options);

export const handleNetworkError = (error: any, context?: string) => errorHandler.handleNetworkError(error, context);

export const handleConfigError = (error: any, suggestions?: string[]) =>
  errorHandler.handleConfigError(error, suggestions);

export const handleBusinessError = (error: any, context?: string) => errorHandler.handleBusinessError(error, context);

export const showConfirmDialog = (message: string, title?: string, options?: any) =>
  errorHandler.showConfirmDialog(message, title, options);
