/**
 * 统一日志管理工具
 * 所有操作日志都记录到fd_log表中
 */

export interface LogEntry {
  user_id: number;
  username: string;
  action: string;
  module: string;
  target_type?: string;
  target_id?: number;
  details: Record<string, any>;
  ip_address?: string;
  user_agent?: string;
  status: 'success' | 'error' | 'warning' | 'info';
  error_message?: string;
}

export interface LogFilter {
  user_id?: number;
  username?: string;
  action?: string;
  module?: string;
  target_type?: string;
  status?: string;
  start_date?: string;
  end_date?: string;
  page?: number;
  pageSize?: number;
}

/**
 * 日志操作类型枚举
 */
export const LogActions = {
  // 用户操作
  USER_LOGIN: 'user_login',
  USER_LOGOUT: 'user_logout',
  USER_REGISTER: 'user_register',
  USER_UPDATE: 'user_update',
  USER_DELETE: 'user_delete',

  // 商品操作
  PRODUCT_CREATE: 'product_create',
  PRODUCT_UPDATE: 'product_update',
  PRODUCT_DELETE: 'product_delete',
  PRODUCT_BATCH_UPDATE: 'product_batch_update',
  PRODUCT_BATCH_DELETE: 'product_batch_delete',
  PRODUCT_AUTO_SORT: 'product_auto_sort',
  PRODUCT_STATUS_CHANGE: 'product_status_change',

  // 29平台对接操作
  PLATFORM29_SYNC: 'platform29_sync',
  PLATFORM29_GET_PRODUCTS: 'platform29_get_products',
  PLATFORM29_IMPORT_PRODUCT: 'platform29_import_product',
  PLATFORM29_UPDATE_PRODUCT: 'platform29_update_product',
  PLATFORM29_BATCH_IMPORT: 'platform29_batch_import',
  PLATFORM29_CATEGORY_SYNC: 'platform29_category_sync',

  // 订单操作
  ORDER_CREATE: 'order_create',
  ORDER_UPDATE: 'order_update',
  ORDER_DELETE: 'order_delete',
  ORDER_QUERY_COURSE: 'order_query_course',
  ORDER_SUBMIT: 'order_submit',
  ORDER_REFILL: 'order_refill',
  ORDER_SYNC: 'order_sync',
  ORDER_CHANGE_PASSWORD: 'order_change_password',
  ORDER_FEEDBACK: 'order_feedback',

  // 货源管理操作
  PROVIDER_CREATE: 'provider_create',
  PROVIDER_UPDATE: 'provider_update',
  PROVIDER_DELETE: 'provider_delete',
  PROVIDER_TEST_CONNECTION: 'provider_test_connection',
  PROVIDER_CHECK_BALANCE: 'provider_check_balance',

  // 分类操作
  CATEGORY_CREATE: 'category_create',
  CATEGORY_UPDATE: 'category_update',
  CATEGORY_DELETE: 'category_delete',

  // 系统操作
  SYSTEM_CONFIG_UPDATE: 'system_config_update',
  SYSTEM_BACKUP: 'system_backup',
  SYSTEM_RESTORE: 'system_restore',

  // 授权操作
  LICENSE_VALIDATE: 'license_validate',
  LICENSE_UPDATE: 'license_update'
} as const;

/**
 * 日志模块枚举
 */
export const LogModules = {
  USER: 'user',
  PRODUCT: 'product',
  ORDER: 'order',
  PROVIDER: 'provider',
  CATEGORY: 'category',
  PLATFORM29: 'platform29',
  SYSTEM: 'system',
  LICENSE: 'license'
} as const;

/**
 * 获取客户端IP地址
 */
export function getClientIP(): string {
  // 在浏览器环境中，无法直接获取真实IP，由后端处理
  return 'client';
}

/**
 * 获取用户代理信息
 */
export function getUserAgent(): string {
  return navigator.userAgent || 'Unknown';
}

/**
 * 创建日志条目
 */
export function createLogEntry(
  action: string,
  module: string,
  details: Record<string, any>,
  status: LogEntry['status'] = 'success',
  options: Partial<LogEntry> = {}
): Omit<LogEntry, 'user_id' | 'username'> {
  return {
    action,
    module,
    details,
    status,
    ip_address: getClientIP(),
    user_agent: getUserAgent(),
    ...options
  };
}

/**
 * 格式化日志详情显示
 */
export function formatLogDetails(details: Record<string, any>): string {
  if (!details || typeof details !== 'object') {
    return '';
  }

  const formatValue = (value: any): string => {
    if (value === null || value === undefined) {
      return '-';
    }
    if (typeof value === 'object') {
      return JSON.stringify(value, null, 2);
    }
    return String(value);
  };

  return Object.entries(details)
    .map(([key, value]) => `${key}: ${formatValue(value)}`)
    .join('\n');
}

/**
 * 获取操作描述
 */
export function getActionDescription(action: string): string {
  const descriptions: Record<string, string> = {
    // 用户操作
    [LogActions.USER_LOGIN]: '用户登录',
    [LogActions.USER_LOGOUT]: '用户登出',
    [LogActions.USER_REGISTER]: '用户注册',
    [LogActions.USER_UPDATE]: '更新用户信息',
    [LogActions.USER_DELETE]: '删除用户',

    // 商品操作
    [LogActions.PRODUCT_CREATE]: '创建商品',
    [LogActions.PRODUCT_UPDATE]: '更新商品',
    [LogActions.PRODUCT_DELETE]: '删除商品',
    [LogActions.PRODUCT_BATCH_UPDATE]: '批量更新商品',
    [LogActions.PRODUCT_BATCH_DELETE]: '批量删除商品',
    [LogActions.PRODUCT_AUTO_SORT]: '一键设置排序',
    [LogActions.PRODUCT_STATUS_CHANGE]: '切换商品状态',

    // 29平台对接操作
    [LogActions.PLATFORM29_SYNC]: '29平台同步',
    [LogActions.PLATFORM29_GET_PRODUCTS]: '获取29平台商品',
    [LogActions.PLATFORM29_IMPORT_PRODUCT]: '导入29平台商品',
    [LogActions.PLATFORM29_UPDATE_PRODUCT]: '更新29平台商品',
    [LogActions.PLATFORM29_BATCH_IMPORT]: '批量导入29平台商品',
    [LogActions.PLATFORM29_CATEGORY_SYNC]: '同步29平台分类',

    // 订单操作
    [LogActions.ORDER_CREATE]: '创建订单',
    [LogActions.ORDER_UPDATE]: '更新订单',
    [LogActions.ORDER_DELETE]: '删除订单',
    [LogActions.ORDER_QUERY_COURSE]: '查询课程',
    [LogActions.ORDER_SUBMIT]: '提交订单',
    [LogActions.ORDER_REFILL]: '订单补刷',
    [LogActions.ORDER_SYNC]: '订单同步',
    [LogActions.ORDER_CHANGE_PASSWORD]: '订单改密',
    [LogActions.ORDER_FEEDBACK]: '订单反馈',

    // 货源管理操作
    [LogActions.PROVIDER_CREATE]: '创建货源',
    [LogActions.PROVIDER_UPDATE]: '更新货源',
    [LogActions.PROVIDER_DELETE]: '删除货源',
    [LogActions.PROVIDER_TEST_CONNECTION]: '测试货源连接',
    [LogActions.PROVIDER_CHECK_BALANCE]: '查询货源余额',

    // 分类操作
    [LogActions.CATEGORY_CREATE]: '创建分类',
    [LogActions.CATEGORY_UPDATE]: '更新分类',
    [LogActions.CATEGORY_DELETE]: '删除分类',

    // 系统操作
    [LogActions.SYSTEM_CONFIG_UPDATE]: '更新系统配置',
    [LogActions.SYSTEM_BACKUP]: '系统备份',
    [LogActions.SYSTEM_RESTORE]: '系统恢复',

    // 授权操作
    [LogActions.LICENSE_VALIDATE]: '授权验证',
    [LogActions.LICENSE_UPDATE]: '更新授权信息'
  };

  return descriptions[action] || action;
}

/**
 * 获取模块描述
 */
export function getModuleDescription(module: string): string {
  const descriptions: Record<string, string> = {
    [LogModules.USER]: '用户管理',
    [LogModules.PRODUCT]: '商品管理',
    [LogModules.ORDER]: '订单管理',
    [LogModules.PROVIDER]: '货源管理',
    [LogModules.CATEGORY]: '分类管理',
    [LogModules.PLATFORM29]: '29平台对接',
    [LogModules.SYSTEM]: '系统管理',
    [LogModules.LICENSE]: '授权管理'
  };

  return descriptions[module] || module;
}

/**
 * 获取状态描述和颜色
 */
export function getStatusInfo(status: string): { text: string; type: string } {
  const statusMap: Record<string, { text: string; type: string }> = {
    success: { text: '成功', type: 'success' },
    error: { text: '失败', type: 'danger' },
    warning: { text: '警告', type: 'warning' },
    info: { text: '信息', type: 'info' }
  };

  return statusMap[status] || { text: status, type: 'info' };
}
