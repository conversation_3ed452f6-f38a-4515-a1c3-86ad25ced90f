/**
 * 平台类型处理工具
 * 遵循项目规范，提供统一的平台类型处理方案
 */

// Element Plus 标签类型
export type ElTagType = 'success' | 'warning' | 'info' | 'primary' | 'danger';

// 平台类型映射配置
const PLATFORM_TYPE_MAP: Record<string, ElTagType> = {
  // 学习平台
  xuexitong: 'primary',
  zhihuishu: 'success',
  icourse163: 'info',
  yuketang: 'warning',

  // 考试平台
  wenjuanxing: 'danger',
  chaoxing: 'primary',
  mooc: 'success',

  // 默认类型
  default: 'info'
};

// 平台类型中文名称映射
const PLATFORM_NAME_MAP: Record<string, string> = {
  xuexitong: '学习通',
  zhihuishu: '智慧树',
  icourse163: '中国大学MOOC',
  yuketang: '雨课堂',
  wenjuanxing: '问卷星',
  chaoxing: '超星',
  mooc: 'MOOC平台'
};

/**
 * 获取平台类型对应的Element Plus标签类型
 * @param platformType 平台类型标识
 * @returns Element Plus标签类型
 */
export function getPlatformType(platformType: string): ElTagType {
  if (!platformType) {
    return 'info';
  }

  const normalizedType = platformType.toLowerCase().trim();
  return PLATFORM_TYPE_MAP[normalizedType] || PLATFORM_TYPE_MAP.default;
}

/**
 * 获取平台类型的中文名称
 * @param platformType 平台类型标识
 * @returns 中文名称
 */
export function getPlatformName(platformType: string): string {
  if (!platformType) {
    return '未知平台';
  }

  const normalizedType = platformType.toLowerCase().trim();
  return PLATFORM_NAME_MAP[normalizedType] || platformType;
}

/**
 * 获取平台类型的完整信息
 * @param platformType 平台类型标识
 * @returns 包含类型、名称等信息的对象
 */
export function getPlatformInfo(platformType: string) {
  return {
    type: getPlatformType(platformType),
    name: getPlatformName(platformType),
    code: platformType
  };
}

/**
 * 注册新的平台类型
 * @param code 平台代码
 * @param name 平台名称
 * @param type 标签类型
 */
export function registerPlatformType(code: string, name: string, type: ElTagType) {
  const normalizedCode = code.toLowerCase().trim();
  PLATFORM_TYPE_MAP[normalizedCode] = type;
  PLATFORM_NAME_MAP[normalizedCode] = name;
}

/**
 * 获取所有已注册的平台类型
 * @returns 平台类型列表
 */
export function getAllPlatformTypes() {
  return Object.keys(PLATFORM_NAME_MAP)
    .filter(key => key !== 'default')
    .map(code => ({
      code,
      name: PLATFORM_NAME_MAP[code],
      type: PLATFORM_TYPE_MAP[code]
    }));
}

/**
 * 验证平台类型是否有效
 * @param platformType 平台类型
 * @returns 是否有效
 */
export function isValidPlatformType(platformType: string): boolean {
  if (!platformType) return false;
  const normalizedType = platformType.toLowerCase().trim();
  return normalizedType in PLATFORM_NAME_MAP;
}
