/**
 * 后端端口自动检测工具
 * 根据项目规范自动适配后端服务端口
 */

interface PortCheckResult {
  port: number;
  available: boolean;
  responseTime?: number;
}

/**
 * 检测端口是否可用
 */
async function checkPort(port: number, timeout = 2000): Promise<PortCheckResult> {
  const startTime = Date.now();

  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);

    const response = await fetch(`http://localhost:${port}/api/auth/getUserInfo`, {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    clearTimeout(timeoutId);
    const responseTime = Date.now() - startTime;

    // 验证响应内容确保是我们的后端服务
    if (response.ok) {
      try {
        const data = await response.json();

        const hasService = data.service === 'SoybeanAdmin Backend';
        const hasStatus = data.status === 'ok';
        const hasSuccess = data.success === true;

        const isOurBackend = hasService && hasStatus && hasSuccess;

        return {
          port,
          available: isOurBackend,
          responseTime
        };
      } catch (error) {
        // 如果不是JSON响应，可能不是我们的后端
        return {
          port,
          available: false,
          responseTime
        };
      }
    }

    return {
      port,
      available: false,
      responseTime
    };
  } catch (error) {
    return {
      port,
      available: false
    };
  }
}

/**
 * 自动检测可用的后端端口
 * 在开发环境中通过代理检测，生产环境中直接检测端口
 */
export async function detectBackendPort(): Promise<number> {
  console.log('🔍 [端口检测] 开始检测后端服务...');

  try {
    // 在开发环境中，通过代理检测后端服务
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 3000);

    const response = await fetch('/proxy-default/api/auth/getUserInfo', {
      method: 'GET',
      signal: controller.signal,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    clearTimeout(timeoutId);

    if (response.ok) {
      const data = await response.json();

      // 检查是否是有效的API响应（即使是401错误也说明服务在运行）
      const isValidResponse = response.status === 401 || (data && typeof data === 'object');

      if (isValidResponse) {
        const port = 3000; // 默认端口
        console.log(`✅ [端口检测] 后端服务通过代理检测成功，端口: ${port}`);
        return Number(port);
      }
    }
  } catch (error) {
    console.warn('🔍 [端口检测] 代理检测失败:', error);
  }

  // 如果代理检测失败，尝试直接检测常用端口（可能在生产环境中）
  const candidatePorts = [3000, 5960, 5959];

  for (const port of candidatePorts) {
    try {
      const result = await checkPort(port);
      if (result.available) {
        console.log(`✅ [端口检测] 后端服务已在端口 ${port} 启动 (响应时间: ${result.responseTime}ms)`);
        return port;
      }
    } catch (error) {
      console.log(`❌ [端口检测] 端口 ${port} 检测失败:`, error);
    }
  }

  console.warn('⚠️ [端口检测] 未检测到可用的后端服务，使用默认端口 3000');
  return 3000;
}

/**
 * 获取动态后端基础URL
 */
export async function getDynamicBackendURL(): Promise<string> {
  const port = await detectBackendPort();
  return `http://localhost:${port}`;
}

/**
 * 手动重新检测后端端口
 */
export async function manualReconnect(): Promise<boolean> {
  console.log('🔄 [手动重连] 开始重新检测后端服务...');

  try {
    const newPort = await detectBackendPort();
    const currentPort = (window as any).__BACKEND_PORT__;

    if (newPort !== currentPort) {
      updateServiceConfig(newPort);
      console.log('✅ [手动重连] 重连成功');
      return true;
    }
    console.log('ℹ️ [手动重连] 后端端口未发生变化');
    return true;
  } catch (error) {
    console.error('❌ [手动重连] 重连失败:', error);
    return false;
  }
}

/**
 * 获取当前后端连接状态
 */
export async function getBackendStatus(): Promise<{
  port: number;
  connected: boolean;
  responseTime?: number;
}> {
  const currentPort = (window as any).__BACKEND_PORT__ || 3000;
  const result = await checkPort(currentPort, 1000);

  return {
    port: currentPort,
    connected: result.available,
    responseTime: result.responseTime
  };
}

/**
 * 更新服务配置中的后端URL
 */
export function updateServiceConfig(newPort: number) {
  // 动态更新环境变量（仅在开发环境）
  if (import.meta.env.DEV) {
    const oldPort = (window as any).__BACKEND_PORT__;
    const newBaseURL = `http://localhost:${newPort}`;

    // 更新运行时配置
    (window as any).__BACKEND_PORT__ = newPort;
    (window as any).__BACKEND_BASE_URL__ = newBaseURL;

    if (oldPort && oldPort !== newPort) {
      console.log(`🔄 [配置更新] 后端端口从 ${oldPort} 切换到 ${newPort}`);

      // 显示用户友好的提示
      showBackendStatusNotification(newPort, 'switched');
    } else {
      console.log(`🔄 [配置更新] 后端服务配置: ${newBaseURL}`);
      showBackendStatusNotification(newPort, 'connected');
    }
  }
}

/**
 * 显示后端状态通知
 */
function showBackendStatusNotification(port: number, type: 'connected' | 'switched' | 'error') {
  // 这里可以集成项目的通知系统
  const messages = {
    connected: `✅ 已连接到后端服务 (端口: ${port})`,
    switched: `🔄 后端服务已切换到端口 ${port}`,
    error: `❌ 后端服务连接失败 (端口: ${port})`
  };

  console.log(`[状态通知] ${messages[type]}`);

  // 如果项目有全局通知系统，可以在这里调用
  // 例如：window.$message?.success(messages[type]);
}

/**
 * 监听后端服务状态（已禁用 - 减少不必要的资源消耗）
 * 原本定期检查后端服务状态，现已移除以避免频繁的getUserInfo请求
 */
export function startBackendMonitoring() {
  console.log('🔍 [后端监控] 后端监控已禁用，减少资源消耗');
  // 不再启动定时监控，避免频繁的getUserInfo请求
  // 如果需要检测后端状态，可以在用户操作时按需检测
}
