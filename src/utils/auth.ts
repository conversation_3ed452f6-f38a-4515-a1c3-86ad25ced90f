/** 授权相关的共享工具函数 用于减少重复代码，统一授权逻辑 */

import { localStg } from '@/utils/storage';

/**
 * 获取授权头
 *
 * @returns Authorization header string or null
 */
export function getAuthorization(): string | null {
  const token = getToken();
  return token ? `Bearer ${token}` : null;
}

/**
 * 获取令牌
 *
 * @returns token string or empty string
 */
export function getToken(): string {
  return localStg.get('token') || '';
}

/**
 * 获取刷新令牌
 *
 * @returns refresh token string or empty string
 */
export function getRefreshToken(): string {
  return localStg.get('refreshToken') || '';
}

/**
 * 设置令牌
 *
 * @param token - 访问令牌
 * @param refreshToken - 刷新令牌
 */
export function setTokens(token: string, refreshToken: string): void {
  localStg.set('token', token);
  localStg.set('refreshToken', refreshToken);
}

/** 清除授权存储 */
export function clearAuthStorage(): void {
  localStg.remove('token');
  localStg.remove('refreshToken');
}

/**
 * 检查是否已登录
 *
 * @returns boolean
 */
export function isLoggedIn(): boolean {
  return Boolean(getToken());
}

/**
 * 检查令牌是否即将过期
 *
 * @param token - JWT令牌
 * @param thresholdMinutes - 过期阈值（分钟）
 * @returns boolean
 */
export function isTokenExpiringSoon(token: string, thresholdMinutes: number = 5): boolean {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const expirationTime = payload.exp * 1000; // 转换为毫秒
    const currentTime = Date.now();
    const thresholdTime = thresholdMinutes * 60 * 1000; // 转换为毫秒

    return expirationTime - currentTime < thresholdTime;
  } catch {
    // 如果解析失败，认为令牌无效
    return true;
  }
}

/**
 * 从JWT令牌中提取用户信息
 *
 * @param token - JWT令牌
 * @returns 用户信息对象或null
 */
export function extractUserFromToken(token: string): any | null {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload;
  } catch {
    return null;
  }
}
