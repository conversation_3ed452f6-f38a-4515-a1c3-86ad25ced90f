import type { App, DirectiveBinding } from 'vue';
import { useAuthStore } from '@/store/modules/auth';

function hasPermission(required: string | string[], owned: string[]) {
  if (!required) return true;
  const reqs = Array.isArray(required) ? required : [required];
  if (!reqs.length) return true;
  return reqs.some(code => owned.includes(code));
}

export function setupPermissionDirective(app: App) {
  app.directive('permission', {
    mounted(el: HTMLElement, binding: DirectiveBinding<string | string[]>) {
      const auth = useAuthStore();
      if (!hasPermission(binding.value, auth.userInfo.permissions)) {
        el.parentNode && el.parentNode.removeChild(el);
      }
    },
    updated(el: HTMLElement, binding: DirectiveBinding<string | string[]>) {
      const auth = useAuthStore();
      if (!hasPermission(binding.value, auth.userInfo.permissions)) {
        el.parentNode && el.parentNode.removeChild(el);
      }
    }
  });
}
