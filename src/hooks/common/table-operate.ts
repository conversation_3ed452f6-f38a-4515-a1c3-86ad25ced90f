import { reactive, ref } from 'vue';
import type { Ref } from 'vue';

/** 表格操作hooks 用于减少重复的表格操作逻辑 */

type TableOperateType = 'add' | 'edit' | 'view';

export interface TableOperateConfig<T = Record<string, any>> {
  /** 创建默认表单数据的函数 */
  createDefaultFormModel: () => T;
  /** 获取数据列表的函数 */
  getData?: () => Promise<void> | void;
  /** 添加数据的API函数 */
  addApi?: (data: T) => Promise<any>;
  /** 更新数据的API函数 */
  updateApi?: (id: string | number, data: T) => Promise<any>;
  /** 删除数据的API函数 */
  deleteApi?: (id: string | number) => Promise<any>;
  /** 批量删除数据的API函数 */
  batchDeleteApi?: (ids: (string | number)[]) => Promise<any>;
}

export function useTableOperate<T extends Record<string, any> = Record<string, any>>(config: TableOperateConfig<T>) {
  const { createDefaultFormModel, getData, addApi, updateApi, deleteApi, batchDeleteApi } = config;

  // 抽屉/模态框状态
  const drawerVisible = ref(false);
  const operateType = ref<TableOperateType>('add');
  const editingData: Ref<T | null> = ref(null);

  // 表单数据
  const formModel = reactive(createDefaultFormModel()) as T;

  // 选中的行
  const checkedRowKeys = ref<(string | number)[]>([]);

  // 加载状态
  const submitLoading = ref(false);

  /** 重置表单数据 */
  function resetFormModel() {
    Object.assign(formModel, createDefaultFormModel());
  }

  /** 打开抽屉/模态框 */
  function openDrawer() {
    drawerVisible.value = true;
  }

  /** 关闭抽屉/模态框 */
  function closeDrawer() {
    drawerVisible.value = false;
    editingData.value = null;
    resetFormModel();
  }

  /** 处理添加操作 */
  function handleAdd() {
    operateType.value = 'add';
    resetFormModel();
    openDrawer();
  }

  /** 处理编辑操作 */
  function handleEdit(data: T) {
    operateType.value = 'edit';
    editingData.value = data;
    Object.assign(formModel, data);
    openDrawer();
  }

  /** 处理查看操作 */
  function handleView(data: T) {
    operateType.value = 'view';
    editingData.value = data;
    Object.assign(formModel, data);
    openDrawer();
  }

  /** 处理表单提交 */
  async function handleSubmit() {
    if (!addApi && !updateApi) {
      console.warn('未配置添加或更新API');
      return;
    }

    submitLoading.value = true;

    try {
      if (operateType.value === 'add' && addApi) {
        await addApi(formModel as T);
        window.$message?.success('添加成功');
      } else if (operateType.value === 'edit' && updateApi && editingData.value) {
        const id = (editingData.value as any).id || (editingData.value as any).item_id;
        await updateApi(id, formModel as T);
        window.$message?.success('更新成功');
      }

      closeDrawer();

      // 刷新数据
      if (getData) {
        await getData();
      }
    } catch (error) {
      console.error('操作失败:', error);
      window.$message?.error('操作失败');
    } finally {
      submitLoading.value = false;
    }
  }

  /** 处理删除操作 */
  async function handleDelete(id: string | number) {
    if (!deleteApi) {
      console.warn('未配置删除API');
      return;
    }

    try {
      const confirmed = confirm('确定要删除这条记录吗？');
      if (!confirmed) {
        return;
      }

      await deleteApi(id);
      window.$message?.success('删除成功');

      // 刷新数据
      if (getData) {
        await getData();
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除失败:', error);
        window.$message?.error('删除失败');
      }
    }
  }

  /** 处理批量删除操作 */
  async function handleBatchDelete() {
    if (!batchDeleteApi) {
      console.warn('未配置批量删除API');
      return;
    }

    if (checkedRowKeys.value.length === 0) {
      window.$message?.warning('请选择要删除的记录');
      return;
    }

    try {
      const confirmed = confirm(`确定要删除选中的 ${checkedRowKeys.value.length} 条记录吗？`);
      if (!confirmed) {
        return;
      }

      await batchDeleteApi(checkedRowKeys.value);
      window.$message?.success('批量删除成功');

      // 清空选中状态
      checkedRowKeys.value = [];

      // 刷新数据
      if (getData) {
        await getData();
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('批量删除失败:', error);
        window.$message?.error('批量删除失败');
      }
    }
  }

  /** 处理选中行变化 */
  function handleCheckedRowKeysChange(keys: (string | number)[]) {
    checkedRowKeys.value = keys;
  }

  return {
    // 状态
    drawerVisible,
    operateType,
    editingData,
    formModel,
    checkedRowKeys,
    submitLoading,

    // 方法
    handleAdd,
    handleEdit,
    handleView,
    handleSubmit,
    handleDelete,
    handleBatchDelete,
    handleCheckedRowKeysChange,
    closeDrawer,
    resetFormModel
  };
}
