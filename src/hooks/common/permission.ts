import { storeToRefs } from 'pinia';
import { useAuthStore } from '@/store/modules/auth';

export function usePermission() {
  const auth = useAuthStore();
  const { userInfo } = storeToRefs(auth);

  function has(code: string) {
    return userInfo.value.permissions.includes(code);
  }

  function hasAny(codes: string[]) {
    if (!codes || codes.length === 0) return true;
    return codes.some(code => has(code));
  }

  function hasAll(codes: string[]) {
    if (!codes || codes.length === 0) return true;
    return codes.every(code => has(code));
  }

  function hasRole(role: string) {
    return userInfo.value.roles.includes(role);
  }

  function hasAnyRole(roles: string[]) {
    if (!roles || roles.length === 0) return true;
    return roles.some(r => hasRole(r));
  }

  function hasAllRoles(roles: string[]) {
    if (!roles || roles.length === 0) return true;
    return roles.every(r => hasRole(r));
  }

  return { has, hasAny, hasAll, hasRole, hasAnyRole, hasAllRoles };
}
