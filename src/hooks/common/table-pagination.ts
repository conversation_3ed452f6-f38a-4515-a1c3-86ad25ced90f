import { reactive, ref } from 'vue';

/**
 * 表格分页通用Hook
 * 遵循项目规范，提供简单统一的分页处理方案
 */

export interface PaginationConfig {
  page: number;
  pageSize: number;
  total?: number;
}

export interface TablePaginationOptions {
  defaultPageSize?: number;
  onPageChange?: (page: number, pageSize: number) => void;
  onSizeChange?: (size: number) => void;
}

/**
 * 表格分页Hook
 * @param options 配置选项
 * @returns 分页相关状态和方法
 */
export function useTablePagination(options: TablePaginationOptions = {}) {
  const { defaultPageSize = 10, onPageChange, onSizeChange } = options;

  // 分页配置
  const pagination = reactive<PaginationConfig>({
    page: 1,
    pageSize: defaultPageSize,
    total: 0
  });

  /**
   * 处理页码变化
   * @param page 新页码
   */
  function handlePageChange(page: number) {
    pagination.page = page;
    onPageChange?.(page, pagination.pageSize);
  }

  /**
   * 处理每页条数变化
   * @param size 新的每页条数
   */
  function handleSizeChange(size: number) {
    pagination.pageSize = size;
    pagination.page = 1; // 重置到第一页
    onSizeChange?.(size);
    onPageChange?.(1, size);
  }

  /**
   * 重置分页到第一页
   */
  function resetPagination() {
    pagination.page = 1;
    pagination.total = 0;
  }

  /**
   * 设置总数
   * @param total 总条数
   */
  function setTotal(total: number) {
    pagination.total = total;
  }

  /**
   * 获取当前分页参数
   * @returns 分页参数对象
   */
  function getPaginationParams() {
    return {
      page: pagination.page,
      pageSize: pagination.pageSize
    };
  }

  return {
    pagination,
    handlePageChange,
    handleSizeChange,
    resetPagination,
    setTotal,
    getPaginationParams
  };
}

/**
 * 表格操作通用Hook
 * 提供统一的表格CRUD操作
 */
export interface TableOperateOptions<T = any> {
  fetchData: (params: any) => Promise<{ data: T[]; total: number }>;
  deleteItem?: (id: string | number) => Promise<void>;
  onError?: (error: any) => void;
}

export function useTableOperate<T = any>(options: TableOperateOptions<T>) {
  const { fetchData, deleteItem, onError } = options;

  const { pagination, handlePageChange, handleSizeChange, resetPagination, setTotal, getPaginationParams } =
    useTablePagination({
      onPageChange: fetchTableData,
      onSizeChange: fetchTableData
    });

  const loading = ref(false);
  const tableData = ref<T[]>([]);

  /**
   * 获取表格数据
   */
  async function fetchTableData() {
    try {
      loading.value = true;
      const params = getPaginationParams();
      const result = await fetchData(params);

      tableData.value = result.data;
      setTotal(result.total);
    } catch (error) {
      console.error('获取表格数据失败:', error);
      onError?.(error);
    } finally {
      loading.value = false;
    }
  }

  /**
   * 删除表格项
   * @param id 项目ID
   */
  async function handleDelete(id: string | number) {
    if (!deleteItem) {
      console.warn('未提供删除方法');
      return;
    }

    try {
      await deleteItem(id);
      // 删除成功后重新获取数据
      await fetchTableData();
    } catch (error) {
      console.error('删除失败:', error);
      onError?.(error);
    }
  }

  /**
   * 刷新表格数据
   */
  function refreshTable() {
    fetchTableData();
  }

  /**
   * 重置表格
   */
  function resetTable() {
    resetPagination();
    tableData.value = [];
  }

  return {
    // 分页相关
    pagination,
    handlePageChange,
    handleSizeChange,

    // 表格数据相关
    loading,
    tableData,
    fetchTableData,
    refreshTable,
    resetTable,

    // 操作相关
    handleDelete
  };
}
