<template>
  <component
    :is="asDrawer && isMobile ? 'ElDrawer' : 'ElDialog'"
    v-bind="dialogProps"
    v-model="modelValueProxy"
    :title="title"
    :width="dialogWidth"
    :top="dialogTop"
    :with-header="withHeader"
    :close-on-click-modal="closeOnClickModal"
    :destroy-on-close="destroyOnClose"
    :append-to-body="appendToBody"
  >
    <slot />

    <template v-if="!asDrawer || !isMobile" #footer>
      <slot name="footer" />
    </template>
  </component>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { useIsMobile } from '@/composables/useIsMobile';

interface Props {
  /** 对话框标题 */
  title?: string;
  /** 小屏使用 Drawer 模式 */
  asDrawer?: boolean;
  /** 小屏宽度/高度策略 */
  mobileMode?: 'fullscreen' | 'drawer';
  /** 桌面宽度，例如 '60%' 或 '800px' */
  width?: string | number;
  /** 顶部间距（ElDialog） */
  top?: string;
  /** v-model */
  modelValue: boolean;
  /** 是否显示头部 */
  withHeader?: boolean;
  /** 点击遮罩关闭 */
  closeOnClickModal?: boolean;
  /** 关闭即销毁 */
  destroyOnClose?: boolean;
  /** 挂载到body */
  appendToBody?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  asDrawer: true,
  mobileMode: 'fullscreen',
  width: '60%',
  top: '10vh',
  withHeader: true,
  closeOnClickModal: false,
  destroyOnClose: true,
  appendToBody: true
});

const emit = defineEmits<{ (e: 'update:modelValue', v: boolean): void }>();

const { isMobile } = useIsMobile();

const modelValueProxy = computed({
  get: () => props.modelValue,
  set: v => emit('update:modelValue', v)
});

const dialogWidth = computed(() => {
  if (isMobile.value) {
    return props.mobileMode === 'fullscreen' ? '100vw' : '90vw';
  }
  return props.width;
});

const dialogTop = computed(() => {
  if (isMobile.value && props.mobileMode === 'fullscreen') return '0';
  return props.top;
});

const dialogProps = computed(() => {
  if (props.asDrawer && isMobile.value) {
    return {
      size: '90%',
      direction: 'btt' as const
    };
  }
  return {};
});
</script>

<style scoped>
:deep(.el-dialog) {
  max-width: 1200px;
}
</style>

