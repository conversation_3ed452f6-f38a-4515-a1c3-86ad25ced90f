<template>
  <div>
    <!-- 工具栏插槽 -->
    <div class="mb-8px flex flex-wrap items-center gap-8px">
      <slot name="toolbar" />
    </div>

    <!-- 表格/卡片模式 -->
    <div v-if="!isMobile">
      <div class="overflow-auto">
        <ElTable :data="data" v-bind="$attrs">
          <slot />
        </ElTable>
      </div>
    </div>

    <div v-else class="space-y-8px">
      <div v-for="(row, idx) in data" :key="idx" class="rounded-8px border p-12px">
        <slot name="card" :row="row" :index="idx" />
      </div>
    </div>

    <!-- 分页插槽 -->
    <div class="mt-12px flex items-center justify-end">
      <slot name="pagination" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useIsMobile } from '@/composables/useIsMobile';

interface Props<T = any> {
  data: T[];
}

withDefaults(defineProps<Props>(), {});

const { isMobile } = useIsMobile();
</script>

