<template>
  <ElDrawer v-model="visibleProxy" :with-header="true" :append-to-body="true" :size="size">
    <template #header>
      <div class="flex items-center gap-8px">
        <icon-mdi-filter-variant />
        <span class="font-medium">筛选</span>
      </div>
    </template>

    <div class="space-y-12px">
      <slot />
    </div>

    <template #footer>
      <div class="flex justify-end gap-8px">
        <ElButton @click="handleReset">重置</ElButton>
        <ElButton type="primary" @click="handleApply">应用</ElButton>
      </div>
    </template>
  </ElDrawer>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  modelValue: boolean;
  size?: string | number; // e.g. '80%'
}

const props = withDefaults(defineProps<Props>(), {
  size: '80%'
});

const emit = defineEmits<{
  (e: 'update:modelValue', v: boolean): void;
  (e: 'apply'): void;
  (e: 'reset'): void;
}>();

const visibleProxy = computed({
  get: () => props.modelValue,
  set: v => emit('update:modelValue', v)
});

function handleApply() {
  emit('apply');
  visibleProxy.value = false;
}

function handleReset() {
  emit('reset');
}
</script>

