<script setup lang="ts">
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import type { UploadProps } from 'element-plus';

defineOptions({ name: 'AvatarUpload' });

interface Props {
  value?: string;
  size?: number;
}

const props = withDefaults(defineProps<Props>(), {
  value: '',
  size: 100
});

const emit = defineEmits(['update:value']);

// 文件上传前的验证
const beforeAvatarUpload: UploadProps['beforeUpload'] = file => {
  const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
  const isLt2M = file.size / 1024 / 1024 < 2;

  if (!isJPG) {
    ElMessage.error('头像图片只能是 JPG 或 PNG 格式!');
    return false;
  }

  if (!isLt2M) {
    ElMessage.error('头像图片大小不能超过 2MB!');
    return false;
  }

  return true;
};

// 图片上传成功
const handleAvatarSuccess: UploadProps['onSuccess'] = res => {
  if (res.code === '200') {
    emit('update:value', res.data.url);
    ElMessage.success('头像上传成功');
  } else {
    ElMessage.error(res.msg || '上传失败');
  }
};

// 图片上传失败
const handleAvatarError: UploadProps['onError'] = () => {
  ElMessage.error('上传失败，请重试');
};

// 图片预览
const avatarUrl = ref(props.value);

// 监听value变化
watch(
  () => props.value,
  newValue => {
    avatarUrl.value = newValue;
  }
);
</script>

<template>
  <ElUpload
    class="avatar-uploader"
    action="/api/auth/uploadAvatar"
    :show-file-list="false"
    :before-upload="beforeAvatarUpload"
    :on-success="handleAvatarSuccess"
    :on-error="handleAvatarError"
  >
    <ElAvatar v-if="value" :size="size" :src="value" />
    <ElIcon v-else class="avatar-uploader-icon" :style="{ width: `${size}px`, height: `${size}px` }">
      <i class="i-carbon:user-avatar" />
    </ElIcon>
    <div class="upload-mask">
      <div class="upload-mask-inner">
        <i class="i-carbon:edit" />
        点击上传
      </div>
    </div>
  </ElUpload>
</template>

<style scoped>
.avatar-uploader {
  position: relative;
  width: v-bind('`${props.size}px`');
  height: v-bind('`${props.size}px`');
  border-radius: 50%;
  overflow: hidden;
  display: inline-block;
  cursor: pointer;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  background-color: #f7f7f7;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.upload-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
  border-radius: 50%;
}

.upload-mask-inner {
  color: #fff;
  font-size: 12px;
  text-align: center;
}

.avatar-uploader:hover .upload-mask {
  opacity: 1;
}
</style>
