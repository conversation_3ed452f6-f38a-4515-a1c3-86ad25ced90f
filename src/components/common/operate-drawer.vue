<script setup lang="ts" generic="T extends Record<string, any>">
import { computed, ref, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { $t } from '@/locales';

type TableOperateType = 'add' | 'edit' | 'view';

interface Props {
  /** 抽屉是否可见 */
  visible: boolean;
  /** 操作类型 */
  operateType: TableOperateType;
  /** 编辑的数据 */
  editingData?: T | null;
  /** 抽屉标题 */
  title?: string;
  /** 抽屉大小 */
  size?: string | number;
  /** 图标 */
  icon?: string;
  /** 表单模型 */
  formModel: T;
  /** 表单验证规则 */
  rules?: FormRules;
  /** 提交加载状态 */
  submitLoading?: boolean;
}

interface Emits {
  (e: 'update:visible', visible: boolean): void;
  (e: 'submit', formModel: T): void;
  (e: 'close'): void;
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  size: '600px',
  icon: 'material-symbols:edit-outline',
  rules: () => ({}),
  submitLoading: false
});

const emit = defineEmits<Emits>();

const formRef = ref<FormInstance>();

const visible = computed({
  get() {
    return props.visible;
  },
  set(value) {
    emit('update:visible', value);
  }
});

const title = computed(() => {
  if (props.title) {
    return props.title;
  }

  const titles: Record<TableOperateType, string> = {
    add: '添加',
    edit: '编辑',
    view: '查看'
  };

  return titles[props.operateType];
});

// 监听编辑数据变化，更新表单模型
watch(
  () => props.editingData,
  newData => {
    if (newData && props.operateType === 'edit') {
      Object.assign(props.formModel, newData);
    }
  },
  { immediate: true, deep: true }
);

async function handleSubmit() {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    emit('submit', props.formModel);
  } catch (error) {
    // 验证失败
  }
}

function handleClose() {
  emit('close');
  emit('update:visible', false);
}
</script>

<template>
  <ElDrawer v-model="visible" :title="title" :size="size" :before-close="handleClose" destroy-on-close>
    <template #header>
      <div class="flex items-center gap-2">
        <SvgIcon :icon="icon" class="text-lg" />
        <span>{{ title }}</span>
      </div>
    </template>

    <ElForm ref="formRef" :model="formModel" :rules="rules" label-width="80px" label-position="left">
      <slot name="form" :form-model="formModel" :form-ref="formRef" />
    </ElForm>

    <template #footer>
      <div class="flex justify-end gap-3">
        <ElButton @click="handleClose">
          {{ $t('common.cancel') }}
        </ElButton>
        <ElButton type="primary" :loading="submitLoading" @click="handleSubmit">
          {{ $t('common.confirm') }}
        </ElButton>
      </div>
    </template>
  </ElDrawer>
</template>

<style scoped>
.el-drawer__header {
  margin-bottom: 0;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.el-drawer__footer {
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-light);
}
</style>
