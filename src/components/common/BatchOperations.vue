<script setup lang="ts">
import { computed, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

interface BatchOperation {
  key: string;
  label: string;
  icon?: string;
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info';
  disabled?: boolean;
  confirmMessage?: string;
}

interface Props {
  selectedItems: any[];
  operations: BatchOperation[];
  loading?: boolean;
  maxDisplayCount?: number;
  showProgress?: boolean; // 是否显示进度
  showResults?: boolean; // 是否显示结果反馈
}

interface Emits {
  (e: 'operation', operation: string, items: any[]): void;
  (e: 'clear-selection'): void;
  (e: 'operation-progress', progress: number): void;
  (e: 'operation-complete', result: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  selectedItems: () => [],
  operations: () => [],
  loading: false,
  maxDisplayCount: 3,
  showProgress: true,
  showResults: true
});

const emit = defineEmits<Emits>();

// 响应式数据
const dropdownVisible = ref(false);
const operationProgress = ref(0);
const operationStatus = ref<'idle' | 'running' | 'success' | 'error'>('idle');
const operationResult = ref<any>(null);
const showProgressDialog = ref(false);
const showResultDialog = ref(false);

// 计算属性
const selectedCount = computed(() => props.selectedItems.length);

const hasSelection = computed(() => selectedCount.value > 0);

const displayOperations = computed(() => props.operations.slice(0, props.maxDisplayCount));

const moreOperations = computed(() => props.operations.slice(props.maxDisplayCount));

// 执行批量操作
async function handleOperation(operation: BatchOperation) {
  if (operation.disabled || !hasSelection.value) return;

  try {
    // 如果有确认消息，显示确认对话框
    if (operation.confirmMessage) {
      await ElMessageBox.confirm(
        operation.confirmMessage.replace('{count}', selectedCount.value.toString()),
        '确认操作',
        {
          type: 'warning',
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }
      );
    }

    // 显示进度对话框
    if (props.showProgress) {
      operationProgress.value = 0;
      operationStatus.value = 'running';
      showProgressDialog.value = true;

      // 模拟进度更新
      simulateProgress();
    }

    emit('operation', operation.key, props.selectedItems);
  } catch (error) {
    // 用户取消操作
    console.log('用户取消操作');
    operationStatus.value = 'idle';
    showProgressDialog.value = false;
  }
}

// 模拟进度更新
function simulateProgress() {
  const interval = setInterval(() => {
    if (operationProgress.value < 90) {
      operationProgress.value += Math.random() * 20;
      emit('operation-progress', operationProgress.value);
    } else {
      clearInterval(interval);
    }
  }, 500);
}

// 完成操作
function completeOperation(result: any) {
  operationProgress.value = 100;
  operationStatus.value = result.success ? 'success' : 'error';
  operationResult.value = result;

  emit('operation-progress', 100);
  emit('operation-complete', result);

  setTimeout(() => {
    showProgressDialog.value = false;
    if (props.showResults) {
      showResultDialog.value = true;
    }
  }, 1000);
}

// 重置状态
function resetOperation() {
  operationProgress.value = 0;
  operationStatus.value = 'idle';
  operationResult.value = null;
  showProgressDialog.value = false;
  showResultDialog.value = false;
}

// 重试操作
function retryOperation() {
  showResultDialog.value = false;
  // 这里可以重新执行上次失败的操作
  // 具体实现需要根据业务需求来定制
}

// 暴露方法给父组件
defineExpose({
  completeOperation,
  resetOperation
});

// 清空选择
function clearSelection() {
  emit('clear-selection');
}

// 获取操作按钮类型
function getButtonType(operation: BatchOperation) {
  return operation.type || 'default';
}

// 获取操作按钮图标
function getButtonIcon(operation: BatchOperation) {
  return operation.icon || 'ic:round-settings';
}
</script>

<template>
  <div class="batch-operations">
    <Transition name="slide-up">
      <div
        v-if="hasSelection"
        class="fixed bottom-24px left-50% z-1000 transform border border-gray-200 rounded-12px bg-white px-24px py-16px shadow-lg -translate-x-50%"
      >
        <div class="flex items-center gap-16px">
          <!-- 选择信息 -->
          <div class="flex items-center gap-8px">
            <ElTag type="primary">已选择 {{ selectedCount }} 项</ElTag>
            <ElButton size="small" type="info" link :disabled="loading" @click="clearSelection">清空</ElButton>
          </div>

          <!-- 分隔线 -->
          <div class="h-24px w-1px bg-gray-200"></div>

          <!-- 操作按钮 -->
          <div class="flex items-center gap-8px">
            <!-- 主要操作按钮 -->
            <ElButton
              v-for="operation in displayOperations"
              :key="operation.key"
              :type="getButtonType(operation)"
              :loading="loading"
              :disabled="operation.disabled"
              @click="handleOperation(operation)"
            >
              <template #icon>
                <component :is="getButtonIcon(operation)" class="text-icon" />
              </template>
              {{ operation.label }}
            </ElButton>

            <!-- 更多操作下拉菜单 -->
            <ElDropdown
              v-if="moreOperations.length > 0"
              v-model:visible="dropdownVisible"
              trigger="click"
              placement="top"
            >
              <ElButton :loading="loading">
                更多操作
                <template #icon>
                  <icon-ic-round-keyboard-arrow-up v-if="dropdownVisible" class="text-icon" />
                  <icon-ic-round-keyboard-arrow-down v-else class="text-icon" />
                </template>
              </ElButton>

              <template #dropdown>
                <ElDropdownMenu>
                  <ElDropdownItem
                    v-for="operation in moreOperations"
                    :key="operation.key"
                    :disabled="operation.disabled || loading"
                    @click="handleOperation(operation)"
                  >
                    <div class="flex items-center gap-8px">
                      <component :is="getButtonIcon(operation)" class="text-icon" />
                      <span>{{ operation.label }}</span>
                    </div>
                  </ElDropdownItem>
                </ElDropdownMenu>
              </template>
            </ElDropdown>
          </div>
        </div>
      </div>
    </Transition>

    <!-- 进度对话框 -->
    <ElDialog
      v-model="showProgressDialog"
      title="批量操作进度"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="text-center space-y-16px">
        <div class="text-16px font-medium">正在处理 {{ selectedCount }} 项数据...</div>

        <ElProgress
          :percentage="Math.round(operationProgress)"
          :status="operationStatus === 'success' ? 'success' : operationStatus === 'error' ? 'exception' : undefined"
          :stroke-width="8"
        />

        <div class="text-14px text-gray-500">
          {{
            operationStatus === 'running'
              ? '请稍候，正在处理中...'
              : operationStatus === 'success'
                ? '操作完成！'
                : operationStatus === 'error'
                  ? '操作失败！'
                  : ''
          }}
        </div>
      </div>
    </ElDialog>

    <!-- 结果对话框 -->
    <ElDialog v-model="showResultDialog" title="操作结果" width="500px">
      <div v-if="operationResult" class="space-y-16px">
        <!-- 操作概览 -->
        <div class="flex items-center gap-12px">
          <ElIcon :size="24" :color="operationResult.success ? '#67C23A' : '#F56C6C'">
            <icon-ic-round-check-circle v-if="operationResult.success" />
            <icon-ic-round-error v-else />
          </ElIcon>
          <div>
            <div class="text-16px font-medium">
              {{ operationResult.success ? '操作成功' : '操作失败' }}
            </div>
            <div class="text-12px text-gray-500">
              {{ operationResult.message || '批量操作已完成' }}
            </div>
          </div>
        </div>

        <!-- 详细结果 -->
        <div v-if="operationResult.details" class="space-y-8px">
          <div class="text-14px font-medium">详细结果：</div>
          <div class="rounded-6px bg-gray-50 p-12px">
            <div v-if="operationResult.details.success" class="text-12px text-green-600">
              ✓ 成功处理：{{ operationResult.details.success }} 项
            </div>
            <div v-if="operationResult.details.failed" class="text-12px text-red-600">
              ✗ 处理失败：{{ operationResult.details.failed }} 项
            </div>
            <div v-if="operationResult.details.skipped" class="text-12px text-orange-600">
              ⚠ 跳过处理：{{ operationResult.details.skipped }} 项
            </div>
          </div>
        </div>

        <!-- 错误列表 -->
        <div v-if="operationResult.errors && operationResult.errors.length > 0" class="space-y-8px">
          <div class="text-14px text-red-600 font-medium">错误详情：</div>
          <div class="max-h-200px overflow-y-auto">
            <div
              v-for="(error, index) in operationResult.errors"
              :key="index"
              class="mb-4px rounded-4px bg-red-50 p-8px text-12px text-red-600"
            >
              {{ error }}
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end gap-12px">
          <ElButton @click="showResultDialog = false">关闭</ElButton>
          <ElButton v-if="operationResult && !operationResult.success" type="primary" @click="retryOperation">
            重试
          </ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<style scoped>
/* 动画效果 */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  opacity: 0;
  transform: translate(-50%, 20px);
}

.slide-up-leave-to {
  opacity: 0;
  transform: translate(-50%, 20px);
}

/* 确保在移动端也能正常显示 */
@media (max-width: 768px) {
  .batch-operations .fixed {
    left: 12px;
    right: 12px;
    transform: none;
    width: auto;
  }

  .batch-operations .flex {
    flex-wrap: wrap;
    gap: 8px;
  }

  .batch-operations .px-24px {
    padding-left: 16px;
    padding-right: 16px;
  }
}

/* 确保批量操作栏在最顶层 */
.batch-operations {
  pointer-events: none;
}

.batch-operations .fixed {
  pointer-events: auto;
}
</style>
