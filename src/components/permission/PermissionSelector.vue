<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { request } from '@/service/request';

interface Props {
  modelValue: number[];
  multiple?: boolean;
  disabled?: boolean;
  placeholder?: string;
  size?: 'large' | 'default' | 'small';
}

interface Emits {
  (e: 'update:modelValue', value: number[]): void;
  (e: 'change', value: number[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  multiple: true,
  disabled: false,
  placeholder: '请选择权限',
  size: 'default'
});

const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
const permissions = ref<any[]>([]);
const searchKeyword = ref('');
const expandedGroups = ref<string[]>([]);

// 计算属性
const selectedPermissions = computed({
  get: () => props.modelValue,
  set: value => {
    emit('update:modelValue', value);
    emit('change', value);
  }
});

// 按分组整理权限
const groupedPermissions = computed(() => {
  const groups: Record<string, any[]> = {};

  const filteredPermissions = permissions.value.filter(permission => {
    if (!searchKeyword.value) return true;
    return (
      permission.permission_name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      permission.permission_code.toLowerCase().includes(searchKeyword.value.toLowerCase())
    );
  });

  filteredPermissions.forEach(permission => {
    const groupName = getGroupDisplayName(permission.permission_group);
    if (!groups[groupName]) {
      groups[groupName] = [];
    }
    groups[groupName].push(permission);
  });

  return Object.entries(groups).map(([name, permissions]) => ({
    name,
    groupKey: permissions[0]?.permission_group || name,
    permissions: permissions.sort((a, b) => a.sort_order - b.sort_order)
  }));
});

// 获取权限列表
async function fetchPermissions() {
  loading.value = true;
  try {
    const response = await request({
      url: '/api/permission/list',
      method: 'get',
      params: { limit: 1000 }
    });

    if ((response as any)?.list) {
      permissions.value = (response as any).list;
      // 默认展开所有分组
      expandedGroups.value = groupedPermissions.value.map(group => group.groupKey);
    }
  } catch (error) {
    console.error('获取权限列表失败:', error);
  } finally {
    loading.value = false;
  }
}

// 获取权限类型标签
function getPermissionType(type: string): 'primary' | 'success' | 'info' | 'warning' | 'danger' {
  const typeMap: Record<string, 'primary' | 'success' | 'info' | 'warning' | 'danger'> = {
    menu: 'primary',
    button: 'success',
    api: 'warning',
    data: 'info'
  };
  return typeMap[type] || '';
}

function getPermissionTypeText(type: string) {
  const typeMap: Record<string, string> = {
    menu: '菜单',
    button: '按钮',
    api: 'API',
    data: '数据'
  };
  return typeMap[type] || type;
}

// 获取权限分组显示名称
function getGroupDisplayName(group: string) {
  const groupMap: Record<string, string> = {
    user: '用户管理',
    role: '角色管理',
    permission: '权限管理',
    system: '系统管理',
    order: '订单管理',
    course: '课程管理',
    supplier: '供应商管理',
    finance: '财务管理',
    report: '报表统计',
    config: '配置管理'
  };
  return groupMap[group] || group;
}

// 全选/取消全选分组
function toggleGroupSelection(group: any) {
  const groupPermissionIds = group.permissions.map((p: any) => p.permission_id);
  const allSelected = groupPermissionIds.every((id: number) => selectedPermissions.value.includes(id));

  if (allSelected) {
    // 取消选择该分组的所有权限
    selectedPermissions.value = selectedPermissions.value.filter(id => !groupPermissionIds.includes(id));
  } else {
    // 选择该分组的所有权限
    const newSelection = [...selectedPermissions.value];
    groupPermissionIds.forEach((id: number) => {
      if (!newSelection.includes(id)) {
        newSelection.push(id);
      }
    });
    selectedPermissions.value = newSelection;
  }
}

// 检查分组是否全选
function isGroupFullySelected(group: any) {
  const groupPermissionIds = group.permissions.map((p: any) => p.permission_id);
  return (
    groupPermissionIds.length > 0 && groupPermissionIds.every((id: number) => selectedPermissions.value.includes(id))
  );
}

// 检查分组是否部分选择
function isGroupPartiallySelected(group: any) {
  const groupPermissionIds = group.permissions.map((p: any) => p.permission_id);
  const selectedCount = groupPermissionIds.filter((id: number) => selectedPermissions.value.includes(id)).length;
  return selectedCount > 0 && selectedCount < groupPermissionIds.length;
}

// 全选/取消全选
function toggleSelectAll() {
  const allPermissionIds = permissions.value.map(p => p.permission_id);
  const allSelected = allPermissionIds.every(id => selectedPermissions.value.includes(id));

  if (allSelected) {
    selectedPermissions.value = [];
  } else {
    selectedPermissions.value = [...allPermissionIds];
  }
}

// 检查是否全选
function isAllSelected() {
  const allPermissionIds = permissions.value.map(p => p.permission_id);
  return allPermissionIds.length > 0 && allPermissionIds.every(id => selectedPermissions.value.includes(id));
}

// 检查是否部分选择
function isPartiallySelected() {
  const allPermissionIds = permissions.value.map(p => p.permission_id);
  const selectedCount = allPermissionIds.filter(id => selectedPermissions.value.includes(id)).length;
  return selectedCount > 0 && selectedCount < allPermissionIds.length;
}

// 生命周期
onMounted(() => {
  fetchPermissions();
});
</script>

<template>
  <div class="permission-selector">
    <!-- 搜索和操作栏 -->
    <div class="mb-16px space-y-12px">
      <ElInput v-model="searchKeyword" placeholder="搜索权限名称或代码" clearable :size="size">
        <template #prefix>
          <icon-ic-round-search class="text-icon" />
        </template>
      </ElInput>

      <div class="flex items-center justify-between">
        <div class="text-12px text-gray-500">
          已选择 {{ selectedPermissions.length }} / {{ permissions.length }} 个权限
        </div>
        <ElButton :size="size" type="primary" link :disabled="disabled" @click="toggleSelectAll">
          {{ isAllSelected() ? '取消全选' : '全选' }}
        </ElButton>
      </div>
    </div>

    <!-- 权限列表 -->
    <div v-loading="loading" class="permission-list">
      <div v-if="groupedPermissions.length === 0" class="py-32px text-center text-gray-500">
        {{ searchKeyword ? '未找到匹配的权限' : '暂无权限数据' }}
      </div>

      <ElCollapse v-else v-model="expandedGroups">
        <ElCollapseItem v-for="group in groupedPermissions" :key="group.groupKey" :name="group.groupKey">
          <template #title>
            <div class="w-full flex items-center justify-between pr-16px">
              <div class="flex items-center gap-8px">
                <ElCheckbox
                  :model-value="isGroupFullySelected(group)"
                  :indeterminate="isGroupPartiallySelected(group)"
                  :disabled="disabled"
                  @change="toggleGroupSelection(group)"
                  @click.stop
                />
                <span class="text-14px font-medium">{{ group.name }}</span>
              </div>
              <ElTag size="small" type="info">{{ group.permissions.length }} 个权限</ElTag>
            </div>
          </template>

          <ElCheckboxGroup v-model="selectedPermissions">
            <div class="grid grid-cols-1 gap-8px pl-24px">
              <div
                v-for="permission in group.permissions"
                :key="permission.permission_id"
                class="flex items-center gap-12px rounded-4px p-8px hover:bg-gray-50"
              >
                <ElCheckbox :label="permission.permission_id" :disabled="disabled" />
                <ElTag size="small" :type="getPermissionType(permission.permission_type)">
                  {{ getPermissionTypeText(permission.permission_type) }}
                </ElTag>
                <div class="flex-1">
                  <div class="text-13px font-medium">
                    {{ permission.permission_name }}
                  </div>
                  <div class="text-11px text-gray-500">
                    {{ permission.permission_code }}
                  </div>
                </div>
              </div>
            </div>
          </ElCheckboxGroup>
        </ElCollapseItem>
      </ElCollapse>
    </div>
  </div>
</template>

<style scoped>
.permission-selector {
  max-height: 400px;
  overflow-y: auto;
}

.permission-list {
  min-height: 200px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .grid-cols-1 {
    grid-template-columns: 1fr;
  }
}
</style>
