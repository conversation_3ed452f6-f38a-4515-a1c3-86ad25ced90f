<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { request } from '@/service/request';

interface Props {
  modelValue: number[];
  disabled?: boolean;
  showInheritance?: boolean; // 是否显示权限继承
  showConflicts?: boolean; // 是否显示权限冲突
  checkStrictly?: boolean; // 是否严格的树形选择
}

interface Emits {
  (e: 'update:modelValue', value: number[]): void;
  (e: 'change', value: number[]): void;
  (e: 'conflict-detected', conflicts: any[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
  disabled: false,
  showInheritance: true,
  showConflicts: true,
  checkStrictly: false
});

const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
// 懒加载：按需加载分组与类型；缓存每个分组的权限列表
const permissions = ref<any[]>([]);
const groups = ref<any[]>([]);
const groupPermCache = ref<Record<string, any[]>>({});
const searchKeyword = ref('');
const treeRef = ref();
const expandedKeys = ref<any[]>([]);

// 计算属性
const selectedPermissions = computed({
  get: () => props.modelValue,
  set: value => {
    emit('update:modelValue', value);
    emit('change', value);
  }
});

// 构建权限树结构（初始仅使用分组构建根节点，类型/权限叶子在展开时加载）
const permissionTree = computed(() => {
  const tree: any[] = [];

  const filteredGroups = (groups.value || []).filter(g => {
    if (!searchKeyword.value) return true;
    const label = getGroupDisplayName(g.permission_group || 'default');
    return label.toLowerCase().includes(searchKeyword.value.toLowerCase());
  });

  for (const g of filteredGroups) {
    const groupKey = g.permission_group || 'default';
    const groupNode: any = {
      id: `group_${groupKey}`,
      label: getGroupDisplayName(groupKey),
      isGroup: true,
      permission_group: groupKey,
      // 分组节点懒加载其类型子节点
      children: undefined,
      isLeaf: false,
      lazy: true
    };
    tree.push(groupNode);
  }

  return tree;
});

// 权限冲突检测
const permissionConflicts = computed(() => {
  if (!props.showConflicts) return [];

  const conflicts: any[] = [];
  const selectedPerms = permissions.value.filter(p => selectedPermissions.value.includes(p.permission_id));

  // 检测同一资源的冲突权限
  const resourceGroups = selectedPerms.reduce(
    (acc, perm) => {
      const key = `${perm.permission_group}_${perm.resource_type || 'default'}`;
      if (!acc[key]) acc[key] = [];
      acc[key].push(perm);
      return acc;
    },
    {} as Record<string, any[]>
  );

  Object.entries(resourceGroups).forEach(([resource, perms]) => {
    const grants = (perms as any[]).filter((p: any) => p.grant_type === 'grant');
    const denies = (perms as any[]).filter((p: any) => p.grant_type === 'deny');

    if (grants.length > 0 && denies.length > 0) {
      conflicts.push({
        type: 'grant_deny_conflict',
        resource,
        permissions: perms,
        message: `${resource} 存在允许和拒绝权限冲突`
      });
    }
  });

  return conflicts;
});

// 获取权限列表
async function fetchPermissions() {
  loading.value = true;
  try {
    const response = await request({
      url: '/api/permission/list',
      method: 'get',
      params: { limit: 1000 }
    });

    if ((response as any)?.list) {
      permissions.value = (response as any).list;
      // 默认展开所有分组
      expandedKeys.value = permissionTree.value.map(node => node.id);
    }
  } catch (error) {
    console.error('获取权限列表失败:', error);
  } finally {
    loading.value = false;
  }
}

// 获取权限分组显示名称
function getGroupDisplayName(group: string) {
  const groupMap: Record<string, string> = {
    user: '用户管理',
    role: '角色管理',
    permission: '权限管理',
    system: '系统管理',
    order: '订单管理',
    course: '课程管理',
    supplier: '供应商管理',
    finance: '财务管理',
    report: '报表统计',
    config: '配置管理'
  };
  return groupMap[group] || group;
}

// 获取权限类型文本
function getPermissionTypeText(type: string) {
  const typeMap: Record<string, string> = {
    menu: '菜单权限',
    button: '按钮权限',
    api: 'API权限',
    data: '数据权限'
  };
  return typeMap[type] || type;
}

// 获取权限类型标签
function getPermissionType(type: string): 'primary' | 'success' | 'info' | 'warning' | 'danger' {
  const typeMap: Record<string, 'primary' | 'success' | 'info' | 'warning' | 'danger'> = {
    menu: 'primary',
    button: 'success',
    api: 'warning',
    data: 'info'
  };
  return typeMap[type] || 'info';
}

// 树节点选择处理
function handleNodeCheck(data: any, checked: boolean, indeterminate: boolean) {
  if (data.isPermission) {
    // 权限节点选择
    const newSelection = [...selectedPermissions.value];
    const index = newSelection.indexOf(data.id);

    if (checked && index === -1) {
      newSelection.push(data.id);
    } else if (!checked && index > -1) {
      newSelection.splice(index, 1);
    }

    selectedPermissions.value = newSelection;
  }
}

// 搜索权限
function searchPermissions() {
  // 搜索时展开所有匹配的节点
  if (searchKeyword.value) {
    const allKeys = permissionTree.value.flatMap(group => [group.id, ...group.children.map((type: any) => type.id)]);
    expandedKeys.value = allKeys;
  }
}

// 全选/取消全选
function toggleSelectAll() {
  const allPermissionIds = permissions.value.map(p => p.permission_id);
  const allSelected = allPermissionIds.every(id => selectedPermissions.value.includes(id));

  if (allSelected) {
    selectedPermissions.value = [];
  } else {
    selectedPermissions.value = [...allPermissionIds];
  }
}

// 检查是否全选
function isAllSelected() {
  const allPermissionIds = permissions.value.map(p => p.permission_id);
  return allPermissionIds.length > 0 && allPermissionIds.every(id => selectedPermissions.value.includes(id));
}

// 监听冲突变化
watch(
  permissionConflicts,
  newConflicts => {
    if (newConflicts.length > 0) {
      emit('conflict-detected', newConflicts);
    }
  },
  { deep: true }
);

// 生命周期
onMounted(() => {
  // 恢复展开状态
  try {
    const saved = localStorage.getItem('permissionTree.expanded');
    if (saved) expandedKeys.value = JSON.parse(saved);
  } catch (e) {}
  fetchGroups();
});

async function fetchGroups() {
  loading.value = true;
  try {
    const res = await request({ url: '/api/permission/groups', method: 'get' });
    const list = (res as any)?.list || (res as any)?.data || (Array.isArray(res) ? res : []) || [];
    groups.value = list;
  } catch (error) {
    console.error('获取权限分组失败:', error);
  } finally {
    loading.value = false;
  }
}

// 懒加载：展开节点时加载子节点
async function loadNode(node: any, resolve: (children: any[]) => void) {
  const data = node?.data || {};

  // 展开分组：加载该分组的权限列表，并产出类型子节点
  if (data?.isGroup) {
    const groupKey = data.permission_group;
    try {
      if (!groupPermCache.value[groupKey]) {
        const res = await request({
          url: '/api/permission/list',
          method: 'get',
          params: { permission_group: groupKey, limit: 1000 }
        });
        const list = (res as any)?.list || (res as any)?.data || (Array.isArray(res) ? res : []) || [];
        groupPermCache.value[groupKey] = list;
      }
      const list = groupPermCache.value[groupKey] || [];
      // 生成类型节点（懒加载权限叶子）
      const typeSet = Array.from(new Set(list.map((p: any) => p.permission_type || 'other')));
      const children = typeSet.map(typeKey => ({
        id: `type_${groupKey}_${typeKey}`,
        label: getPermissionTypeText(typeKey),
        isType: true,
        permission_type: typeKey,
        permission_group: groupKey,
        isLeaf: false,
        lazy: true
      }));
      resolve(children);
    } catch (error) {
      console.error('加载分组权限失败:', error);
      resolve([]);
    }
    return;
  }

  // 展开类型：从缓存构建权限叶子
  if (data?.isType) {
    const groupKey = data.permission_group;
    const typeKey = data.permission_type;
    const list = groupPermCache.value[groupKey] || [];
    const children = list
      .filter((p: any) => (p.permission_type || 'other') === typeKey)
      .map((permission: any) => ({
        id: permission.permission_id,
        label: permission.permission_name,
        permission_code: permission.permission_code,
        permission_type: permission.permission_type,
        permission_group: permission.permission_group,
        data_scope: permission.data_scope,
        sort_order: permission.sort_order,
        isPermission: true,
        isLeaf: true,
        conflicts: [],
        inherited: false
      }))
      .sort((a: any, b: any) => (a.sort_order || 0) - (b.sort_order || 0));
    resolve(children);
    return;
  }

  resolve([]);
}

function persistExpanded() {
  try {
    localStorage.setItem('permissionTree.expanded', JSON.stringify(expandedKeys.value));
  } catch (e) {}
}

function onNodeExpand(data: any) {
  const key = data?.id;
  if (key != null && !expandedKeys.value.includes(key)) expandedKeys.value.push(key);
  persistExpanded();
}

function onNodeCollapse(data: any) {
  const key = data?.id;
  const idx = expandedKeys.value.indexOf(key);
  if (idx >= 0) expandedKeys.value.splice(idx, 1);
  persistExpanded();
}
</script>

<template>
  <div class="permission-tree">
    <!-- 搜索和操作栏 -->
    <div class="mb-16px space-y-12px">
      <ElInput v-model="searchKeyword" placeholder="搜索权限名称或代码" clearable @input="searchPermissions">
        <template #prefix>
          <icon-ic-round-search class="text-icon" />
        </template>
      </ElInput>

      <div class="flex items-center justify-between">
        <div class="text-12px text-gray-500">
          已选择 {{ selectedPermissions.length }} / {{ permissions.length }} 个权限
        </div>
        <ElButton type="primary" link :disabled="disabled" @click="toggleSelectAll">
          {{ isAllSelected() ? '取消全选' : '全选' }}
        </ElButton>
      </div>
    </div>

    <!-- 权限冲突提示 -->
    <div v-if="showConflicts && permissionConflicts.length > 0" class="mb-16px">
      <ElAlert
        v-for="(conflict, index) in permissionConflicts"
        :key="index"
        :title="conflict.message"
        type="warning"
        show-icon
        :closable="false"
        class="mb-8px"
      >
        <div class="text-12px">
          冲突权限：{{ (conflict.permissions as any[]).map((p: any) => p.permission_name).join(', ') }}
        </div>
      </ElAlert>
    </div>

    <!-- 权限树 -->
    <div v-loading="loading" class="permission-tree-container">
      <ElTree
        ref="treeRef"
        :data="permissionTree"
        :props="{ children: 'children', label: 'label', isLeaf: 'isLeaf' }"
        :lazy="true"
        :load="loadNode"
        :default-expanded-keys="expandedKeys"
        :check-strictly="checkStrictly"
        show-checkbox
        node-key="id"
        :disabled="disabled"
        @node-expand="onNodeExpand"
        @node-collapse="onNodeCollapse"
        @check="handleNodeCheck"
      >
        <template #default="{ node, data }">
          <div class="w-full flex items-center gap-8px">
            <!-- 分组节点 -->
            <template v-if="data.isGroup">
              <icon-ic-round-folder class="text-icon text-blue-500" />
              <span class="font-medium">{{ data.label }}</span>
              <ElTag size="small" type="info">{{ data.children?.length || 0 }} 类型</ElTag>
            </template>

            <!-- 类型节点 -->
            <template v-else-if="data.isType">
              <icon-ic-round-category class="text-icon text-green-500" />
              <span class="font-medium">{{ data.label }}</span>
              <ElTag size="small" :type="getPermissionType(data.permission_type)">
                {{ data.children?.length || 0 }} 权限
              </ElTag>
            </template>

            <!-- 权限节点 -->
            <template v-else-if="data.isPermission">
              <ElTag size="small" :type="getPermissionType(data.permission_type)">
                {{ getPermissionTypeText(data.permission_type) }}
              </ElTag>
              <span>{{ data.label }}</span>
              <span class="text-12px text-gray-500">({{ data.permission_code }})</span>

              <!-- 权限继承标识 -->
              <ElTag v-if="showInheritance && data.inherited" size="small" type="warning">继承</ElTag>

              <!-- 权限冲突标识 -->
              <ElTag v-if="showConflicts && data.conflicts?.length > 0" size="small" type="danger">冲突</ElTag>

              <!-- 数据范围 -->
              <ElTag v-if="data.data_scope && data.data_scope !== 'all'" size="small" type="info">
                {{ data.data_scope }}
              </ElTag>
            </template>
          </div>
        </template>
      </ElTree>

      <div v-if="permissionTree.length === 0" class="py-32px text-center text-gray-500">
        {{ searchKeyword ? '未找到匹配的权限' : '暂无权限数据' }}
      </div>
    </div>
  </div>
</template>

<style scoped>
.permission-tree {
  max-height: 500px;
  overflow-y: auto;
}

.permission-tree-container {
  min-height: 300px;
}

/* 树节点样式 */
:deep(.el-tree-node__content) {
  height: auto;
  padding: 4px 0;
}

:deep(.el-tree-node__label) {
  flex: 1;
}
</style>
