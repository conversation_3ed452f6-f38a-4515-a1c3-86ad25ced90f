<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { request } from '@/service/request';

interface Props {
  visible: boolean;
  userId?: number;
  conflicts?: any[];
  autoDetect?: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'resolved', conflicts: any[]): void;
  (e: 'refresh'): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  userId: undefined,
  conflicts: () => [],
  autoDetect: true
});

const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
const resolving = ref(false);
const detectedConflicts = ref<any[]>([]);
const selectedConflicts = ref<number[]>([]);
const resolutionMode = ref<'manual' | 'auto' | 'batch'>('manual');
const autoResolutionStrategy = ref<'prefer_direct' | 'prefer_role' | 'merge_smart'>('prefer_direct');

// 冲突解决统计
const resolutionStats = ref({
  total: 0,
  resolved: 0,
  pending: 0,
  failed: 0
});

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
});

const allConflicts = computed(() => {
  return props.conflicts.length > 0 ? props.conflicts : detectedConflicts.value;
});

const hasConflicts = computed(() => allConflicts.value.length > 0);

const selectedConflictItems = computed(() => {
  return allConflicts.value.filter((_, index) => selectedConflicts.value.includes(index));
});

const canBatchResolve = computed(() => {
  return selectedConflicts.value.length > 0 && resolutionMode.value === 'batch';
});

// 冲突严重程度配置
const severityConfig = {
  high: {
    color: 'danger' as const,
    text: '高危',
    description: '严重影响系统安全和功能',
    priority: 1
  },
  medium: {
    color: 'warning' as const,
    text: '中等',
    description: '可能影响部分功能',
    priority: 2
  },
  low: {
    color: 'info' as const,
    text: '低危',
    description: '轻微影响，建议处理',
    priority: 3
  }
};

// 解决方案配置
const resolutionOptions = {
  use_direct: {
    title: '使用直接权限',
    description: '优先使用直接分配给用户的权限',
    icon: 'ic:round-person',
    color: 'primary' as const,
    suitable: '适用于需要特殊权限的用户'
  },
  use_role: {
    title: '使用角色权限',
    description: '优先使用角色继承的权限',
    icon: 'ic:round-group',
    color: 'success' as const,
    suitable: '适用于标准化权限管理'
  },
  merge: {
    title: '智能合并',
    description: '智能合并冲突权限，选择最宽松的设置',
    icon: 'ic:round-merge',
    color: 'warning' as const,
    suitable: '适用于复杂的权限冲突'
  },
  remove_conflict: {
    title: '移除冲突权限',
    description: '移除导致冲突的权限配置',
    icon: 'ic:round-remove-circle',
    color: 'danger' as const,
    suitable: '适用于不需要的权限'
  }
};

// 检测权限冲突
async function detectConflicts() {
  if (!props.userId || !props.autoDetect) return;

  loading.value = true;
  try {
    const response = await request({
      url: `/api/user/permission-inheritance/${props.userId}`,
      method: 'get'
    });

    if (response && response.conflicts) {
      detectedConflicts.value = response.conflicts;
      updateResolutionStats();
    }
  } catch (error) {
    console.error('检测权限冲突失败:', error);
    ElMessage.error('检测权限冲突失败');
  } finally {
    loading.value = false;
  }
}

// 更新解决统计
function updateResolutionStats() {
  const total = allConflicts.value.length;
  const resolved = allConflicts.value.filter(c => c.resolved).length;
  const pending = total - resolved;

  resolutionStats.value = {
    total,
    resolved,
    pending,
    failed: 0
  };
}

// 解决单个冲突
async function resolveSingleConflict(conflict: any, resolution: string) {
  if (!props.userId) return;

  resolving.value = true;
  try {
    await request({
      url: '/api/user/resolve-permission-conflict',
      method: 'post',
      data: {
        user_id: props.userId,
        permission_id: conflict.permission_id,
        conflict_type: conflict.conflict_type,
        resolution
      }
    });

    // 标记为已解决
    conflict.resolved = true;
    conflict.resolution = resolution;
    conflict.resolvedAt = new Date().toISOString();

    updateResolutionStats();
    ElMessage.success(`权限冲突已解决: ${conflict.permission_name}`);

    emit('resolved', [conflict]);
  } catch (error) {
    console.error('解决权限冲突失败:', error);
    ElMessage.error('解决权限冲突失败');
    resolutionStats.value.failed++;
  } finally {
    resolving.value = false;
  }
}

// 批量解决冲突
async function batchResolveConflicts() {
  if (!canBatchResolve.value || !props.userId) return;

  const conflicts = selectedConflictItems.value;
  const strategy = autoResolutionStrategy.value;

  const confirmResult = await ElMessageBox.confirm(
    `确认批量解决 ${conflicts.length} 个权限冲突？\n解决策略：${getStrategyText(strategy)}`,
    '批量解决权限冲突',
    {
      type: 'warning',
      confirmButtonText: '确认解决',
      cancelButtonText: '取消'
    }
  );

  if (confirmResult !== 'confirm') return;

  resolving.value = true;
  let successCount = 0;
  let failCount = 0;

  try {
    for (const conflict of conflicts) {
      try {
        const resolution = getAutoResolution(conflict, strategy);
        await resolveSingleConflict(conflict, resolution);
        successCount++;
      } catch (error) {
        failCount++;
        console.error(`解决冲突失败: ${conflict.permission_name}`, error);
      }
    }

    ElMessage.success(`批量解决完成：成功 ${successCount} 个，失败 ${failCount} 个`);

    if (successCount > 0) {
      emit('resolved', conflicts.slice(0, successCount));
    }
  } finally {
    resolving.value = false;
    selectedConflicts.value = [];
  }
}

// 获取自动解决方案
function getAutoResolution(conflict: any, strategy: string): string {
  switch (strategy) {
    case 'prefer_direct':
      return 'use_direct';
    case 'prefer_role':
      return 'use_role';
    case 'merge_smart':
      return 'merge';
    default:
      return 'use_direct';
  }
}

// 获取策略文本
function getStrategyText(strategy: string): string {
  const strategyMap: Record<string, string> = {
    prefer_direct: '优先使用直接权限',
    prefer_role: '优先使用角色权限',
    merge_smart: '智能合并权限'
  };
  return strategyMap[strategy] || strategy;
}

// 获取冲突类型文本
function getConflictTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    grant_deny_conflict: '授权冲突',
    role_scope_conflict: '数据范围冲突',
    multiple_role_conflict: '多角色冲突',
    inheritance_conflict: '继承冲突'
  };
  return typeMap[type] || type;
}

// 获取冲突严重程度信息
function getSeverityInfo(severity: string) {
  return severityConfig[severity as keyof typeof severityConfig] || severityConfig.low;
}

// 获取解决方案信息
function getResolutionInfo(resolution: string) {
  return resolutionOptions[resolution as keyof typeof resolutionOptions];
}

// 全选/取消全选
function toggleSelectAll() {
  if (selectedConflicts.value.length === allConflicts.value.length) {
    selectedConflicts.value = [];
  } else {
    selectedConflicts.value = allConflicts.value.map((_, index) => index);
  }
}

// 关闭对话框
function closeDialog() {
  selectedConflicts.value = [];
  resolutionMode.value = 'manual';
  emit('update:visible', false);
}

// 刷新冲突数据
function refreshConflicts() {
  detectConflicts();
  emit('refresh');
}

// 监听用户ID变化
watch(
  () => props.userId,
  newUserId => {
    if (newUserId && props.autoDetect) {
      detectConflicts();
    }
  }
);

// 监听对话框显示状态
watch(
  () => props.visible,
  visible => {
    if (visible && props.userId && props.autoDetect) {
      detectConflicts();
    }
  }
);

// 组件挂载
onMounted(() => {
  if (props.userId && props.autoDetect) {
    detectConflicts();
  }
  updateResolutionStats();
});
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    title="权限冲突解决器"
    width="900px"
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <div v-loading="loading" class="space-y-16px">
      <!-- 冲突统计概览 -->
      <ElCard class="from-red-50 to-orange-50 bg-gradient-to-r">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-16px">
            <icon-ic-round-warning class="text-32px text-red-500" />
            <div>
              <h3 class="text-lg text-red-700 font-bold">权限冲突检测</h3>
              <p class="text-sm text-red-600">发现 {{ resolutionStats.total }} 个权限冲突需要处理</p>
            </div>
          </div>
          <div class="grid grid-cols-3 gap-12px text-center">
            <div class="rounded bg-white p-8px shadow-sm">
              <div class="text-lg text-red-600 font-bold">{{ resolutionStats.pending }}</div>
              <div class="text-xs text-gray-600">待解决</div>
            </div>
            <div class="rounded bg-white p-8px shadow-sm">
              <div class="text-lg text-green-600 font-bold">{{ resolutionStats.resolved }}</div>
              <div class="text-xs text-gray-600">已解决</div>
            </div>
            <div class="rounded bg-white p-8px shadow-sm">
              <div class="text-lg text-orange-600 font-bold">{{ resolutionStats.failed }}</div>
              <div class="text-xs text-gray-600">失败</div>
            </div>
          </div>
        </div>
      </ElCard>

      <!-- 解决模式选择 -->
      <ElCard v-if="hasConflicts">
        <template #header>
          <div class="flex items-center justify-between">
            <span class="font-medium">解决模式</span>
            <ElButton size="small" @click="refreshConflicts">
              <template #icon>
                <icon-ic-round-refresh />
              </template>
              刷新检测
            </ElButton>
          </div>
        </template>

        <div class="space-y-12px">
          <ElRadioGroup v-model="resolutionMode">
            <ElRadio value="manual">
              <div class="flex items-center gap-8px">
                <icon-ic-round-touch class="text-blue-500" />
                <span>手动解决</span>
                <span class="text-sm text-gray-500">逐个选择解决方案</span>
              </div>
            </ElRadio>
            <ElRadio value="batch">
              <div class="flex items-center gap-8px">
                <icon-ic-round-batch class="text-green-500" />
                <span>批量解决</span>
                <span class="text-sm text-gray-500">批量应用解决策略</span>
              </div>
            </ElRadio>
          </ElRadioGroup>

          <!-- 批量解决配置 -->
          <div v-if="resolutionMode === 'batch'" class="mt-12px rounded bg-blue-50 p-12px">
            <div class="flex items-center gap-16px">
              <span class="text-sm text-blue-700 font-medium">批量解决策略:</span>
              <ElSelect v-model="autoResolutionStrategy" size="small" class="w-200px">
                <ElOption value="prefer_direct" label="优先使用直接权限" />
                <ElOption value="prefer_role" label="优先使用角色权限" />
                <ElOption value="merge_smart" label="智能合并权限" />
              </ElSelect>
              <ElButton
                type="primary"
                size="small"
                :disabled="!canBatchResolve"
                :loading="resolving"
                @click="batchResolveConflicts"
              >
                批量解决 ({{ selectedConflicts.length }})
              </ElButton>
            </div>
          </div>
        </div>
      </ElCard>

      <!-- 冲突列表 -->
      <div v-if="hasConflicts" class="space-y-12px">
        <!-- 列表操作栏 -->
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-12px">
            <ElCheckbox
              :model-value="selectedConflicts.length === allConflicts.length"
              :indeterminate="selectedConflicts.length > 0 && selectedConflicts.length < allConflicts.length"
              @change="toggleSelectAll"
            >
              全选 ({{ allConflicts.length }})
            </ElCheckbox>
            <span class="text-sm text-gray-600">
              已选择 {{ selectedConflicts.length }} / {{ allConflicts.length }} 个冲突
            </span>
          </div>
          <div class="text-sm text-gray-600">按严重程度排序</div>
        </div>

        <!-- 冲突项列表 -->
        <div class="max-h-400px overflow-y-auto space-y-8px">
          <div
            v-for="(conflict, index) in allConflicts"
            :key="`conflict-${conflict.permission_id}-${conflict.conflict_type}`"
            class="conflict-item border rounded-8px p-16px"
            :class="{
              'border-red-200 bg-red-50': !conflict.resolved,
              'border-green-200 bg-green-50': conflict.resolved,
              selected: selectedConflicts.includes(index)
            }"
          >
            <div class="flex items-start gap-12px">
              <!-- 选择框 -->
              <ElCheckbox
                v-if="resolutionMode === 'batch' && !conflict.resolved"
                :model-value="selectedConflicts.includes(index)"
                @change="
                  checked => {
                    if (checked) {
                      selectedConflicts.push(index);
                    } else {
                      const idx = selectedConflicts.indexOf(index);
                      if (idx > -1) selectedConflicts.splice(idx, 1);
                    }
                  }
                "
              />

              <!-- 冲突信息 -->
              <div class="flex-1">
                <div class="mb-8px flex items-center justify-between">
                  <div class="flex items-center gap-8px">
                    <h4 class="text-gray-800 font-medium">{{ conflict.permission_name }}</h4>
                    <ElTag size="small" :type="getSeverityInfo(conflict.severity).color">
                      {{ getSeverityInfo(conflict.severity).text }}
                    </ElTag>
                    <ElTag size="small" type="info">
                      {{ getConflictTypeText(conflict.conflict_type) }}
                    </ElTag>
                    <ElTag v-if="conflict.resolved" size="small" type="success">已解决</ElTag>
                  </div>
                </div>

                <p class="mb-8px text-sm text-gray-600">{{ conflict.permission_code }}</p>
                <p class="mb-12px text-sm text-gray-700">{{ getSeverityInfo(conflict.severity).description }}</p>

                <!-- 冲突详情 -->
                <div class="text-sm space-y-4px">
                  <div v-if="conflict.roles && conflict.roles.length > 0">
                    <span class="text-gray-600">涉及角色:</span>
                    <span class="ml-4px">
                      {{ conflict.roles.map((r: any) => `${r.name}(${r.scope})`).join(', ') }}
                    </span>
                  </div>
                  <div v-if="conflict.resolved">
                    <span class="text-gray-600">解决方案:</span>
                    <span class="ml-4px text-green-600">{{ getResolutionInfo(conflict.resolution)?.title }}</span>
                    <span class="ml-8px text-xs text-gray-500">
                      {{ new Date(conflict.resolvedAt).toLocaleString() }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- 解决操作 -->
              <div v-if="!conflict.resolved && resolutionMode === 'manual'" class="flex flex-col gap-4px">
                <ElButton
                  v-for="(option, key) in resolutionOptions"
                  :key="key"
                  size="small"
                  :type="option.color"
                  :loading="resolving"
                  @click="resolveSingleConflict(conflict, key)"
                >
                  {{ option.title }}
                </ElButton>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 无冲突状态 -->
      <div v-else class="py-32px text-center">
        <icon-ic-round-check-circle class="mb-16px text-64px text-success" />
        <h3 class="mb-8px text-lg text-success font-medium">没有发现权限冲突</h3>
        <p class="text-gray-600">用户权限配置正常，无需处理</p>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-between">
        <div class="text-sm text-gray-600">
          <span v-if="hasConflicts">提示：建议优先解决高危冲突，确保系统安全</span>
        </div>
        <div class="flex gap-8px">
          <ElButton @click="closeDialog">关闭</ElButton>
          <ElButton v-if="hasConflicts" type="primary" @click="refreshConflicts">重新检测</ElButton>
        </div>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.conflict-item {
  transition: all 0.3s ease;
  position: relative;
}

.conflict-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.conflict-item.selected {
  border-color: var(--el-color-primary) !important;
  background-color: var(--el-color-primary-light-9) !important;
}

.conflict-item.selected::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--el-color-primary), var(--el-color-primary-light-3));
  border-radius: 8px 8px 0 0;
}

/* 统计卡片动画 */
.stats-card {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 解决按钮样式 */
.resolution-buttons .el-button {
  min-width: 100px;
  margin-bottom: 4px;
}

/* 滚动条样式 */
.max-h-400px::-webkit-scrollbar {
  width: 6px;
}

.max-h-400px::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.max-h-400px::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.max-h-400px::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 渐变背景 */
.gradient-bg-danger {
  background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
}

.gradient-bg-success {
  background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
}

/* 冲突严重程度指示器 */
.severity-indicator {
  position: relative;
}

.severity-indicator::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  border-radius: 2px;
}

.severity-indicator.high::before {
  background-color: var(--el-color-danger);
}

.severity-indicator.medium::before {
  background-color: var(--el-color-warning);
}

.severity-indicator.low::before {
  background-color: var(--el-color-info);
}
</style>
