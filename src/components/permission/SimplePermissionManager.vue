<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { request } from '@/service/request';

interface Props {
  visible: boolean;
  userId: number;
  userInfo?: any;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  userId: 0,
  userInfo: undefined
});

const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
const saving = ref(false);
const userPermissions = ref<any>({
  routes: [],
  functions: {}
});

// 路由权限配置（树状结构）
const routePermissions = ref([
  {
    key: 'home',
    name: '首页',
    description: '系统首页',
    icon: 'ic:round-home',
    children: []
  },
  {
    key: 'dashboard',
    name: '仪表盘',
    description: '数据统计仪表盘',
    icon: 'ic:round-dashboard',
    children: [
      {
        key: 'dashboard_statistics',
        name: '数据统计',
        description: '查看系统数据统计',
        icon: 'ic:round-bar-chart'
      }
    ]
  },
  {
    key: 'order',
    name: '订单管理',
    description: '订单相关功能',
    icon: 'ic:round-shopping-cart',
    children: [
      {
        key: 'order_create',
        name: '创建订单',
        description: '创建新订单',
        icon: 'ic:round-add'
      },
      {
        key: 'order_list',
        name: '订单列表',
        description: '查看订单列表',
        icon: 'ic:round-list'
      },
      {
        key: 'order_sync-management',
        name: '同步管理',
        description: '订单同步管理',
        icon: 'ic:round-sync'
      }
    ]
  },
  {
    key: 'product',
    name: '商品管理',
    description: '商品信息管理',
    icon: 'ic:round-shopping-bag',
    children: []
  },
  {
    key: 'platform29',
    name: '29平台',
    description: '29平台对接',
    icon: 'ic:round-api',
    children: []
  },
  {
    key: 'category',
    name: '分类管理',
    description: '商品分类管理',
    icon: 'ic:round-folder',
    children: []
  },
  {
    key: 'provider',
    name: '货源管理',
    description: '货源供应商管理',
    icon: 'ic:round-store',
    children: [
      {
        key: 'provider_list',
        name: '货源列表',
        description: '查看货源列表',
        icon: 'ic:round-list'
      },
      {
        key: 'provider_config',
        name: '货源配置',
        description: '货源配置管理',
        icon: 'ic:round-settings'
      }
    ]
  },
  {
    key: 'user',
    name: '用户管理',
    description: '系统用户管理',
    icon: 'ic:round-people',
    children: []
  },
  {
    key: 'payment',
    name: '支付管理',
    description: '支付相关功能',
    icon: 'ic:round-payment',
    children: [
      {
        key: 'payment_recharge',
        name: '在线充值',
        description: '用户在线充值',
        icon: 'ic:round-account-balance-wallet'
      },
      {
        key: 'payment_finance',
        name: '财务管理',
        description: '财务数据管理',
        icon: 'ic:round-account-balance'
      }
    ]
  },
  {
    key: 'system-center',
    name: '系统中心',
    description: '系统配置和管理',
    icon: 'ic:round-settings',
    children: [
      {
        key: 'system-center_license',
        name: '授权管理',
        description: '系统授权管理',
        icon: 'ic:round-verified'
      },
      {
        key: 'system-center_config',
        name: '系统配置',
        description: '系统参数配置',
        icon: 'ic:round-settings'
      },
      {
        key: 'system-center_database',
        name: '数据库管理',
        description: '数据库维护',
        icon: 'ic:round-storage'
      },
      {
        key: 'system-center_log',
        name: '日志管理',
        description: '系统日志查看',
        icon: 'ic:round-description'
      },
      {
        key: 'system-center_backup',
        name: '备份管理',
        description: '数据备份管理',
        icon: 'ic:round-cloud-upload'
      },
      {
        key: 'system-center_monitor',
        name: '系统监控',
        description: '系统运行监控',
        icon: 'ic:round-monitor'
      },
      {
        key: 'system-center_permission',
        name: '权限管理',
        description: '系统权限管理',
        icon: 'ic:round-security'
      },
      {
        key: 'system-center_permission-management',
        name: '权限配置',
        description: '权限详细配置',
        icon: 'ic:round-admin-panel-settings'
      },
      {
        key: 'system-center_role-management',
        name: '角色管理',
        description: '用户角色管理',
        icon: 'ic:round-group'
      },
      {
        key: 'system-center_email',
        name: '邮件管理',
        description: '邮件模板管理',
        icon: 'ic:round-email'
      },
      {
        key: 'system-center_report',
        name: '报表管理',
        description: '系统报表生成',
        icon: 'ic:round-bar-chart'
      }
    ]
  },
  {
    key: 'user-center',
    name: '个人中心',
    description: '用户个人信息',
    icon: 'ic:round-account-circle',
    children: []
  }
]);

// 功能权限配置
const functionPermissions = ref([
  {
    key: 'create_subordinate',
    name: '开通下级',
    description: '允许用户开通下级代理',
    category: 'user'
  },
  {
    key: 'online_recharge',
    name: '在线充值',
    description: '允许用户使用在线充值功能',
    category: 'finance'
  },
  {
    key: 'withdraw_funds',
    name: '提现功能',
    description: '允许用户申请提现',
    category: 'finance'
  },
  {
    key: 'view_statistics',
    name: '查看统计',
    description: '允许查看数据统计',
    category: 'data'
  },
  {
    key: 'export_data',
    name: '导出数据',
    description: '允许导出系统数据',
    category: 'data'
  },
  {
    key: 'modify_rate',
    name: '修改费率',
    description: '允许修改用户费率',
    category: 'user'
  }
]);

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
});

const functionPermissionsByCategory = computed(() => {
  const categories: Record<string, any[]> = {};
  functionPermissions.value.forEach(permission => {
    if (!categories[permission.category]) {
      categories[permission.category] = [];
    }
    categories[permission.category].push(permission);
  });
  return categories;
});

const categoryNames = computed(() => ({
  user: '用户管理',
  finance: '财务功能',
  data: '数据功能'
}));

// 获取用户权限
async function fetchUserPermissions() {
  if (!props.userId) return;

  loading.value = true;
  try {
    const response = await request({
      url: `/api/user/${props.userId}/simple-permissions`,
      method: 'get'
    });

    if (response) {
      userPermissions.value = {
        routes: response.routes || [],
        functions: response.functions || {}
      };
    }
  } catch (error) {
    console.error('获取用户权限失败:', error);
    ElMessage.error('获取用户权限失败');
  } finally {
    loading.value = false;
  }
}

// 保存权限设置
async function savePermissions() {
  if (!props.userId) return;

  try {
    await ElMessageBox.confirm('确定要保存权限设置吗？', '确认保存', {
      type: 'warning',
      confirmButtonText: '确认保存',
      cancelButtonText: '取消'
    });

    saving.value = true;

    await request({
      url: `/api/user/${props.userId}/simple-permissions`,
      method: 'post',
      data: {
        routes: userPermissions.value.routes,
        functions: userPermissions.value.functions
      }
    });

    ElMessage.success('权限设置保存成功');
    emit('success');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('保存权限设置失败:', error);
      ElMessage.error('保存权限设置失败');
    }
  } finally {
    saving.value = false;
  }
}

// 切换路由权限
function toggleRoutePermission(routeKey: string) {
  const index = userPermissions.value.routes.indexOf(routeKey);
  if (index > -1) {
    userPermissions.value.routes.splice(index, 1);
  } else {
    userPermissions.value.routes.push(routeKey);
  }
}

// 切换功能权限
function toggleFunctionPermission(functionKey: string) {
  userPermissions.value.functions[functionKey] = !userPermissions.value.functions[functionKey];
}

// 检查路由权限
function hasRoutePermission(routeKey: string): boolean {
  return userPermissions.value.routes.includes(routeKey);
}

// 检查功能权限
function hasFunctionPermission(functionKey: string): boolean {
  return Boolean(userPermissions.value.functions[functionKey]);
}

// 获取所有路由权限键
function getAllRouteKeys(): string[] {
  const keys: string[] = [];
  routePermissions.value.forEach(route => {
    keys.push(route.key);
    if (route.children) {
      route.children.forEach(child => {
        keys.push(child.key);
      });
    }
  });
  return keys;
}

// 全选/取消全选路由权限
function toggleAllRoutePermissions() {
  const allKeys = getAllRouteKeys();
  if (userPermissions.value.routes.length === allKeys.length) {
    userPermissions.value.routes = [];
  } else {
    userPermissions.value.routes = [...allKeys];
  }
}

// 全选/取消全选功能权限
function toggleAllFunctionPermissions() {
  const allFunctionKeys = functionPermissions.value.map(f => f.key);
  const hasAllPermissions = allFunctionKeys.every(key => userPermissions.value.functions[key]);

  allFunctionKeys.forEach(key => {
    userPermissions.value.functions[key] = !hasAllPermissions;
  });
}

// 关闭对话框
function closeDialog() {
  emit('update:visible', false);
}

// 监听对话框显示状态
watch(
  () => props.visible,
  visible => {
    if (visible && props.userId) {
      fetchUserPermissions();
    }
  }
);

// 组件挂载
onMounted(() => {
  if (props.visible && props.userId) {
    fetchUserPermissions();
  }
});
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    title="用户权限管理"
    width="700px"
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <div v-loading="loading" class="space-y-16px">
      <!-- 用户信息 -->
      <ElCard>
        <template #header>
          <div class="flex items-center gap-8px">
            <icon-ic-round-person class="text-blue-500" />
            <span class="font-medium">用户信息</span>
          </div>
        </template>
        <div class="flex items-center gap-16px">
          <div>
            <div class="text-lg font-medium">{{ userInfo?.username || `用户ID: ${userId}` }}</div>
            <div class="text-sm text-gray-600">{{ userInfo?.email || '未设置邮箱' }}</div>
          </div>
          <ElTag :type="userInfo?.status === 1 ? 'success' : 'danger'">
            {{ userInfo?.status === 1 ? '正常' : '禁用' }}
          </ElTag>
        </div>
      </ElCard>

      <!-- 路由权限 -->
      <ElCard>
        <template #header>
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-8px">
              <icon-ic-round-navigation class="text-green-500" />
              <span class="font-medium">路由权限</span>
            </div>
            <ElButton size="small" @click="toggleAllRoutePermissions">
              {{ userPermissions.routes.length === getAllRouteKeys().length ? '取消全选' : '全选' }}
            </ElButton>
          </div>
        </template>

        <div class="space-y-8px">
          <div
            v-for="route in routePermissions"
            :key="route.key"
            class="border rounded-8px transition-all duration-200"
            :class="{
              'border-green-300 bg-green-50': hasRoutePermission(route.key),
              'border-gray-200': !hasRoutePermission(route.key)
            }"
          >
            <!-- 父级路由 -->
            <div class="cursor-pointer p-12px hover:bg-gray-50" @click="toggleRoutePermission(route.key)">
              <div class="flex items-center gap-8px">
                <ElCheckbox :model-value="hasRoutePermission(route.key)" @click.stop />
                <component :is="`icon-${route.icon}`" class="text-20px" />
                <div class="flex-1">
                  <div class="font-medium">{{ route.name }}</div>
                  <div class="text-sm text-gray-600">{{ route.description }}</div>
                </div>
                <div v-if="route.children && route.children.length > 0" class="text-sm text-gray-500">
                  {{ route.children.filter(child => hasRoutePermission(child.key)).length }}/{{ route.children.length }}
                </div>
              </div>
            </div>

            <!-- 子级路由 -->
            <div v-if="route.children && route.children.length > 0" class="border-t border-gray-100">
              <div class="grid grid-cols-1 gap-4px p-8px md:grid-cols-2">
                <div
                  v-for="child in route.children"
                  :key="child.key"

                  class="cursor-pointer rounded-6px p-8px transition-all duration-200"
                  :class="{
                    'bg-blue-50 border border-blue-200': hasRoutePermission(child.key),
                    'hover:bg-gray-50': !hasRoutePermission(child.key)
                  }"
                  @click="toggleRoutePermission(child.key)"
                >
                  <div class="flex items-center gap-6px">
                    <ElCheckbox :model-value="hasRoutePermission(child.key)" size="small" @click.stop />
                    <component :is="`icon-${child.icon}`" class="text-16px" />
                    <div class="flex-1">
                      <div class="text-sm font-medium">{{ child.name }}</div>
                      <div class="text-xs text-gray-600">{{ child.description }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ElCard>

      <!-- 功能权限 -->
      <ElCard>
        <template #header>
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-8px">
              <icon-ic-round-settings class="text-purple-500" />
              <span class="font-medium">功能权限</span>
            </div>
            <ElButton size="small" @click="toggleAllFunctionPermissions">
              全选/取消全选
            </ElButton>
          </div>
        </template>

        <div class="space-y-16px">
          <div v-for="(permissions, category) in functionPermissionsByCategory" :key="category" class="space-y-8px">
            <div class="border-b pb-4px text-sm text-gray-700 font-medium">
              {{ categoryNames[category as keyof typeof categoryNames] }}
            </div>
            <div class="grid grid-cols-1 gap-8px md:grid-cols-2">
              <div
                v-for="permission in permissions"
                :key="permission.key"

                class="cursor-pointer border rounded-6px p-8px transition-all duration-200"
                :class="{
                  'border-blue-300 bg-blue-50': hasFunctionPermission(permission.key),
                  'border-gray-200 hover:border-gray-300': !hasFunctionPermission(permission.key)
                }"
                @click="toggleFunctionPermission(permission.key)"
              >
                <div class="flex items-center gap-8px">
                  <ElCheckbox :model-value="hasFunctionPermission(permission.key)" @click.stop />
                  <div class="flex-1">
                    <div class="text-sm font-medium">{{ permission.name }}</div>
                    <div class="text-xs text-gray-600">{{ permission.description }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ElCard>
    </div>

    <template #footer>
      <div class="flex justify-between">
        <div class="text-sm text-gray-600">
          路由权限: {{ userPermissions.routes.length }}/{{ getAllRouteKeys().length }} | 功能权限:
          {{ Object.values(userPermissions.functions).filter(Boolean).length }}/{{ functionPermissions.length }}
        </div>
        <div class="flex gap-8px">
          <ElButton @click="closeDialog">取消</ElButton>
          <ElButton type="primary" :loading="saving" @click="savePermissions">
            <template #icon>
              <icon-ic-round-check />
            </template>
            保存权限
          </ElButton>
        </div>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.permission-card {
  transition: all 0.2s ease;
}

.permission-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.permission-card.selected {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}
</style>
