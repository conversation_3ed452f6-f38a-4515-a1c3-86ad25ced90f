<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { request } from '@/service/request';

defineOptions({
  name: 'PermissionInheritanceViewer'
});

interface Props {
  visible: boolean;
  userId?: number;
  showConflicts?: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'resolve-conflict', conflict: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  showConflicts: true
});

const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
const activeTab = ref('inheritance');
const permissionTraceVisible = ref(false);

// 用户信息
const userInfo = ref<any>({});

// 权限继承数据
const inheritanceData = ref<any>({
  rolePermissions: [],
  directPermissions: [],
  effectivePermissions: [],
  conflicts: []
});

// 权限冲突
const permissionConflicts = ref<any[]>([]);

// 权限统计
const permissionStats = ref({
  total: 0,
  fromRoles: 0,
  direct: 0,
  conflicts: 0,
  coverage: 0,
  uniqueRoles: 0,
  permissionTypes: {
    menu: 0,
    button: 0,
    api: 0,
    data: 0
  }
});

// 权限继承树数据
const inheritanceTree = ref<any[]>([]);

// 权限追溯数据
const permissionTrace = ref<any>({});

// 视图模式
const viewMode = ref<'tree' | 'list' | 'graph'>('tree');

// 筛选条件
const filterOptions = ref({
  permissionType: '',
  grantType: '',
  source: '',
  conflictOnly: false
});

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
});

const hasConflicts = computed(() => permissionConflicts.value.length > 0);

// 筛选后的权限数据
const filteredPermissions = computed(() => {
  const { rolePermissions, directPermissions } = inheritanceData.value;
  let allPermissions = [
    ...rolePermissions.map((p: any) => ({ ...p, source: 'role' })),
    ...directPermissions.map((p: any) => ({ ...p, source: 'direct' }))
  ];

  // 应用筛选条件
  if (filterOptions.value.permissionType) {
    allPermissions = allPermissions.filter(p => p.permission_type === filterOptions.value.permissionType);
  }

  if (filterOptions.value.grantType) {
    allPermissions = allPermissions.filter(p => p.grant_type === filterOptions.value.grantType);
  }

  if (filterOptions.value.source) {
    allPermissions = allPermissions.filter(p => p.source === filterOptions.value.source);
  }

  if (filterOptions.value.conflictOnly) {
    const conflictPermissionIds = new Set(permissionConflicts.value.map(c => c.permission_id));
    allPermissions = allPermissions.filter(p => conflictPermissionIds.has(p.permission_id));
  }

  return allPermissions;
});

// 权限类型选项
const permissionTypeOptions = computed(() => {
  const types = new Set();
  const { rolePermissions, directPermissions } = inheritanceData.value;
  [...rolePermissions, ...directPermissions].forEach((p: any) => {
    types.add(p.permission_type);
  });
  return Array.from(types).map(type => ({ label: getPermissionTypeText(type as string), value: type as string }));
});

// 获取权限类型文本
function getPermissionTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    menu: '菜单权限',
    button: '按钮权限',
    api: 'API权限',
    data: '数据权限'
  };
  return typeMap[type] || type;
}

// 获取权限信息
function getPermissionInfo(permissionId: number) {
  const { rolePermissions, directPermissions } = inheritanceData.value;
  const allPermissions = [...rolePermissions, ...directPermissions];
  return allPermissions.find((p: any) => p.permission_id === permissionId);
}

// 获取权限类型颜色
function getPermissionTypeColor(type: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' {
  const colorMap: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    menu: 'primary',
    button: 'success',
    api: 'warning',
    data: 'danger'
  };
  return colorMap[type] || 'info';
}

// 获取授权类型颜色
function getGrantTypeColor(grantType: string): 'success' | 'danger' {
  return grantType === 'grant' ? 'success' : 'danger';
}

// 获取冲突严重程度颜色
function getConflictSeverityColor(severity: string): 'danger' | 'warning' | 'info' {
  const colorMap: Record<string, 'danger' | 'warning' | 'info'> = {
    high: 'danger',
    medium: 'warning',
    low: 'info'
  };
  return colorMap[severity] || 'info';
}

// 获取用户信息
async function fetchUserInfo() {
  if (!props.userId) return;

  try {
    const response = await request({
      url: `/api/user/${props.userId}`,
      method: 'get'
    });

    if (response) {
      userInfo.value = response;
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
  }
}

// 获取权限继承数据
async function fetchInheritanceData() {
  if (!props.userId) return;

  loading.value = true;
  try {
    const response = await request({
      url: `/api/user/permission-inheritance/${props.userId}`,
      method: 'get'
    });

    if (response) {
      inheritanceData.value = response;
      calculatePermissionStats();
      detectPermissionConflicts();
      buildInheritanceTree();
    }
  } catch (error) {
    console.error('获取权限继承数据失败:', error);
    ElMessage.error('获取权限继承数据失败');
  } finally {
    loading.value = false;
  }
}

// 计算权限统计
function calculatePermissionStats() {
  const { rolePermissions, directPermissions, effectivePermissions } = inheritanceData.value;

  const fromRoles = rolePermissions.length;
  const direct = directPermissions.length;
  const total = effectivePermissions.length;
  const conflicts = permissionConflicts.value.length;

  // 计算唯一角色数
  const uniqueRoles = new Set(rolePermissions.map((p: any) => p.role_id)).size;

  // 计算权限类型分布
  const permissionTypes = {
    menu: 0,
    button: 0,
    api: 0,
    data: 0
  };

  effectivePermissions.forEach((permission: any) => {
    const type = permission.permission_type;
    if (type in permissionTypes) {
      permissionTypes[type as keyof typeof permissionTypes]++;
    }
  });

  permissionStats.value = {
    total,
    fromRoles,
    direct,
    conflicts,
    coverage: total > 0 ? Math.round(((fromRoles + direct) / total) * 100) : 0,
    uniqueRoles,
    permissionTypes
  };
}

// 检测权限冲突
function detectPermissionConflicts() {
  const conflicts = [];
  const { rolePermissions, directPermissions } = inheritanceData.value;

  // 检测角色权限与直接权限的冲突
  for (const rolePermission of rolePermissions) {
    const directConflict = directPermissions.find((dp: any) => dp.permission_id === rolePermission.permission_id);

    if (directConflict) {
      // 检查是否有冲突（grant vs deny）
      if (rolePermission.grant_type !== directConflict.grant_type) {
        conflicts.push({
          permission_id: rolePermission.permission_id,
          permission_name: rolePermission.permission_name,
          permission_code: rolePermission.permission_code,
          role_grant: rolePermission.grant_type,
          direct_grant: directConflict.grant_type,
          role_source: rolePermission.role_name,
          conflict_type: 'grant_type',
          severity: 'high'
        });
      }

      // 检查数据范围冲突
      if (rolePermission.data_scope !== directConflict.data_scope) {
        conflicts.push({
          permission_id: rolePermission.permission_id,
          permission_name: rolePermission.permission_name,
          permission_code: rolePermission.permission_code,
          role_scope: rolePermission.data_scope,
          direct_scope: directConflict.data_scope,
          role_source: rolePermission.role_name,
          conflict_type: 'data_scope',
          severity: 'medium'
        });
      }
    }
  }

  // 检测多个角色之间的权限冲突
  const rolePermissionMap = new Map();
  for (const rolePermission of rolePermissions) {
    const key = rolePermission.permission_id;
    if (!rolePermissionMap.has(key)) {
      rolePermissionMap.set(key, []);
    }
    rolePermissionMap.get(key).push(rolePermission);
  }

  for (const [permissionId, permissions] of rolePermissionMap) {
    if (permissions.length > 1) {
      // 检查多个角色对同一权限的不同配置
      const grantTypes = new Set(permissions.map((p: any) => p.grant_type));
      const dataScopes = new Set(permissions.map((p: any) => p.data_scope));

      if (grantTypes.size > 1) {
        conflicts.push({
          permission_id: permissionId,
          permission_name: permissions[0].permission_name,
          permission_code: permissions[0].permission_code,
          roles: permissions.map((p: any) => ({ name: p.role_name, grant: p.grant_type })),
          conflict_type: 'role_grant_conflict',
          severity: 'high'
        });
      }

      if (dataScopes.size > 1) {
        conflicts.push({
          permission_id: permissionId,
          permission_name: permissions[0].permission_name,
          permission_code: permissions[0].permission_code,
          roles: permissions.map((p: any) => ({ name: p.role_name, scope: p.data_scope })),
          conflict_type: 'role_scope_conflict',
          severity: 'medium'
        });
      }
    }
  }

  permissionConflicts.value = conflicts;
}

// 构建权限继承树
function buildInheritanceTree() {
  const { rolePermissions, directPermissions } = inheritanceData.value;
  const tree: any[] = [];

  // 按角色分组
  const roleGroups = new Map();
  rolePermissions.forEach((permission: any) => {
    const roleId = permission.role_id;
    if (!roleGroups.has(roleId)) {
      roleGroups.set(roleId, {
        id: `role-${roleId}`,
        type: 'role',
        name: permission.role_name,
        description: permission.role_description,
        permissions: [],
        expanded: true
      });
    }
    roleGroups.get(roleId).permissions.push({
      ...permission,
      id: `role-${roleId}-${permission.permission_id}`,
      source: 'role'
    });
  });

  // 添加角色节点
  roleGroups.forEach(role => {
    tree.push(role);
  });

  // 添加直接权限节点
  if (directPermissions.length > 0) {
    tree.push({
      id: 'direct-permissions',
      type: 'direct',
      name: '直接权限',
      description: '直接分配给用户的权限',
      permissions: directPermissions.map((permission: any) => ({
        ...permission,
        id: `direct-${permission.permission_id}`,
        source: 'direct'
      })),
      expanded: true
    });
  }

  inheritanceTree.value = tree;
}

// 权限追溯
function tracePermission(permissionId: number) {
  const { rolePermissions, directPermissions } = inheritanceData.value;
  const trace: any = {
    permission_id: permissionId,
    sources: [],
    conflicts: [],
    effective: null
  };

  // 查找角色来源
  const roleSources = rolePermissions.filter((p: any) => p.permission_id === permissionId);
  roleSources.forEach((source: any) => {
    trace.sources.push({
      type: 'role',
      role_name: source.role_name,
      role_id: source.role_id,
      grant_type: source.grant_type,
      data_scope: source.data_scope,
      priority: 1
    });
  });

  // 查找直接权限来源
  const directSource = directPermissions.find((p: any) => p.permission_id === permissionId);
  if (directSource) {
    trace.sources.push({
      type: 'direct',
      grant_type: directSource.grant_type,
      data_scope: directSource.data_scope,
      priority: 2 // 直接权限优先级更高
    });
  }

  // 确定最终有效权限
  if (trace.sources.length > 0) {
    // 直接权限优先
    const directPerm = trace.sources.find((s: any) => s.type === 'direct');
    if (directPerm) {
      trace.effective = directPerm;
    } else {
      // 使用第一个角色权限
      trace.effective = trace.sources[0];
    }
  }

  // 检查冲突
  if (trace.sources.length > 1) {
    const grantTypes = new Set(trace.sources.map((s: any) => s.grant_type));
    const dataScopes = new Set(trace.sources.map((s: any) => s.data_scope));

    if (grantTypes.size > 1) {
      trace.conflicts.push({
        type: 'grant_type',
        description: '授权类型冲突'
      });
    }

    if (dataScopes.size > 1) {
      trace.conflicts.push({
        type: 'data_scope',
        description: '数据范围冲突'
      });
    }
  }

  permissionTrace.value = trace;
  permissionTraceVisible.value = true;
  return trace;
}

// 解决权限冲突
async function resolveConflict(conflict: any, resolution: string) {
  try {
    await request({
      url: '/api/user/resolve-permission-conflict',
      method: 'post',
      data: {
        user_id: props.userId,
        permission_id: conflict.permission_id,
        conflict_type: conflict.conflict_type,
        resolution
      }
    });

    ElMessage.success('权限冲突已解决');
    await fetchInheritanceData(); // 重新获取数据
    emit('resolve-conflict', conflict);
  } catch (error) {
    console.error('解决权限冲突失败:', error);
    ElMessage.error('解决权限冲突失败');
  }
}

// 获取权限来源描述
function getPermissionSource(permission: any) {
  if (permission.source === 'role') {
    return `角色: ${permission.role_name}`;
  } else if (permission.source === 'direct') {
    return '直接分配';
  }
  return '未知来源';
}

// 重置筛选条件
function resetFilters() {
  filterOptions.value = {
    permissionType: '',
    grantType: '',
    source: '',
    conflictOnly: false
  };
}

// 关闭对话框
function closeDialog() {
  emit('update:visible', false);
}

// 初始化数据
async function initData() {
  loading.value = true;
  try {
    await Promise.all([fetchUserInfo(), fetchInheritanceData()]);
  } finally {
    loading.value = false;
  }
}

// 监听对话框显示状态
watch(
  () => props.visible,
  visible => {
    if (visible && props.userId) {
      initData();
    }
  }
);

// 监听用户ID变化
watch(
  () => props.userId,
  userId => {
    if (userId && props.visible) {
      initData();
    }
  }
);
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="`权限继承分析 - ${userInfo.username || '未知用户'}`"
    width="1000px"
    :close-on-click-modal="false"
    destroy-on-close
    @close="closeDialog"
  >
    <div v-loading="loading">
      <!-- 用户信息和权限统计概览 -->
      <div class="mb-20px space-y-16px">
        <!-- 用户信息卡片 -->
        <ElCard class="from-blue-50 to-purple-50 bg-gradient-to-r">
          <div class="flex items-center gap-16px">
            <ElAvatar :size="60" :src="userInfo.avatar">
              {{ userInfo.username?.charAt(0)?.toUpperCase() }}
            </ElAvatar>
            <div class="flex-1">
              <h3 class="mb-4px text-xl font-bold">{{ userInfo.username }}</h3>
              <p class="mb-8px text-gray-600">{{ userInfo.email || '未设置邮箱' }}</p>
              <div class="flex items-center gap-16px text-sm">
                <div class="flex items-center gap-4px">
                  <icon-ic-round-group class="text-blue-500" />
                  <span>{{ permissionStats.uniqueRoles }} 个角色</span>
                </div>
                <div class="flex items-center gap-4px">
                  <icon-ic-round-security class="text-green-500" />
                  <span>{{ permissionStats.total }} 个权限</span>
                </div>
                <div v-if="hasConflicts" class="flex items-center gap-4px">
                  <icon-ic-round-warning class="text-red-500" />
                  <span class="text-red-600">{{ permissionStats.conflicts }} 个冲突</span>
                </div>
              </div>
            </div>
            <div class="text-right">
              <div class="mb-4px text-sm text-gray-500">权限覆盖率</div>
              <ElProgress
                :percentage="permissionStats.coverage"
                :color="
                  permissionStats.coverage >= 80 ? '#67c23a' : permissionStats.coverage >= 60 ? '#e6a23c' : '#f56c6c'
                "
                :stroke-width="8"
                class="w-120px"
              />
            </div>
          </div>
        </ElCard>

        <!-- 权限统计卡片 -->
        <div class="grid grid-cols-2 gap-12px lg:grid-cols-7 md:grid-cols-4">
          <ElCard class="stats-card">
            <div class="text-center">
              <icon-ic-round-security class="mb-8px text-32px text-primary" />
              <div class="text-xl text-primary font-bold">{{ permissionStats.total }}</div>
              <div class="text-xs text-gray-500">有效权限</div>
            </div>
          </ElCard>

          <ElCard class="stats-card">
            <div class="text-center">
              <icon-ic-round-group class="mb-8px text-32px text-success" />
              <div class="text-xl text-success font-bold">{{ permissionStats.fromRoles }}</div>
              <div class="text-xs text-gray-500">角色权限</div>
            </div>
          </ElCard>

          <ElCard class="stats-card">
            <div class="text-center">
              <icon-ic-round-person class="mb-8px text-32px text-info" />
              <div class="text-xl text-info font-bold">{{ permissionStats.direct }}</div>
              <div class="text-xs text-gray-500">直接权限</div>
            </div>
          </ElCard>

          <ElCard class="stats-card">
            <div class="text-center">
              <icon-ic-round-menu class="mb-8px text-32px text-blue-500" />
              <div class="text-xl text-blue-600 font-bold">{{ permissionStats.permissionTypes.menu }}</div>
              <div class="text-xs text-gray-500">菜单权限</div>
            </div>
          </ElCard>

          <ElCard class="stats-card">
            <div class="text-center">
              <icon-ic-round-smart-toy class="mb-8px text-32px text-green-500" />
              <div class="text-xl text-green-600 font-bold">{{ permissionStats.permissionTypes.button }}</div>
              <div class="text-xs text-gray-500">按钮权限</div>
            </div>
          </ElCard>

          <ElCard class="stats-card">
            <div class="text-center">
              <icon-ic-round-api class="mb-8px text-32px text-orange-500" />
              <div class="text-xl text-orange-600 font-bold">{{ permissionStats.permissionTypes.api }}</div>
              <div class="text-xs text-gray-500">API权限</div>
            </div>
          </ElCard>

          <ElCard class="stats-card">
            <div class="text-center">
              <icon-ic-round-warning class="mb-8px text-32px" :class="hasConflicts ? 'text-danger' : 'text-success'" />
              <div class="text-xl font-bold" :class="hasConflicts ? 'text-danger' : 'text-success'">
                {{ permissionStats.conflicts }}
              </div>
              <div class="text-xs text-gray-500">权限冲突</div>
            </div>
          </ElCard>
        </div>
      </div>

      <!-- 详细信息 -->
      <ElTabs v-model="activeTab">
        <!-- 权限继承分析 -->
        <ElTabPane name="inheritance">
          <template #label>
            <div class="flex items-center gap-8px">
              <icon-ic-round-account-tree />
              <span>继承分析</span>
            </div>
          </template>

          <!-- 筛选和视图控制 -->
          <div class="mb-16px space-y-12px">
            <!-- 筛选条件 -->
            <ElCard>
              <template #header>
                <div class="flex items-center justify-between">
                  <span class="font-medium">筛选条件</span>
                  <ElButton size="small" @click="resetFilters">重置筛选</ElButton>
                </div>
              </template>

              <div class="grid grid-cols-1 gap-12px md:grid-cols-4">
                <ElSelect v-model="filterOptions.permissionType" placeholder="权限类型" clearable>
                  <ElOption
                    v-for="option in permissionTypeOptions"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </ElSelect>

                <ElSelect v-model="filterOptions.grantType" placeholder="授权类型" clearable>
                  <ElOption label="授予" value="grant" />
                  <ElOption label="拒绝" value="deny" />
                </ElSelect>

                <ElSelect v-model="filterOptions.source" placeholder="权限来源" clearable>
                  <ElOption label="角色权限" value="role" />
                  <ElOption label="直接权限" value="direct" />
                </ElSelect>

                <div class="flex items-center gap-8px">
                  <ElCheckbox v-model="filterOptions.conflictOnly">仅显示冲突权限</ElCheckbox>
                </div>
              </div>
            </ElCard>

            <!-- 视图模式切换 -->
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-12px">
                <span class="text-sm text-gray-600">视图模式:</span>
                <ElRadioGroup v-model="viewMode" size="small">
                  <ElRadioButton value="tree">
                    <icon-ic-round-account-tree class="mr-4px" />
                    树形视图
                  </ElRadioButton>
                  <ElRadioButton value="list">
                    <icon-ic-round-list class="mr-4px" />
                    列表视图
                  </ElRadioButton>
                  <ElRadioButton value="graph">
                    <icon-ic-round-hub class="mr-4px" />
                    关系图
                  </ElRadioButton>
                </ElRadioGroup>
              </div>

              <div class="text-sm text-gray-600">
                显示 {{ filteredPermissions.length }} / {{ permissionStats.total }} 个权限
              </div>
            </div>
          </div>
          <!-- 权限继承内容 -->
          <div class="space-y-16px">
            <!-- 树形视图 -->
            <div v-if="viewMode === 'tree'">
              <ElTree
                :data="inheritanceTree"
                :props="{ label: 'name', children: 'permissions' }"
                default-expand-all
                node-key="id"
              >
                <template #default="{ node, data }">
                  <div v-if="data.type === 'role' || data.type === 'direct'" class="w-full flex items-center gap-8px">
                    <icon-ic-round-group v-if="data.type === 'role'" class="text-blue-500" />
                    <icon-ic-round-person v-else class="text-green-500" />
                    <span class="font-medium">{{ data.name }}</span>
                    <ElTag size="small" :type="data.type === 'role' ? 'primary' : 'success'">
                      {{ data.permissions.length }} 个权限
                    </ElTag>
                    <span class="flex-1 text-sm text-gray-500">{{ data.description }}</span>
                  </div>
                  <div v-else class="w-full flex items-center justify-between">
                    <div class="flex items-center gap-8px">
                      <ElTag size="small" :type="getPermissionTypeColor(data.permission_type)">
                        {{ getPermissionTypeText(data.permission_type) }}
                      </ElTag>
                      <span class="font-medium">{{ data.permission_name }}</span>
                      <span class="text-sm text-gray-500">{{ data.permission_code }}</span>
                    </div>
                    <div class="flex items-center gap-8px">
                      <ElTag size="small" :type="getGrantTypeColor(data.grant_type)">
                        {{ data.grant_type === 'grant' ? '授予' : '拒绝' }}
                      </ElTag>
                      <ElButton size="small" type="primary" link @click="tracePermission(data.permission_id)">
                        追溯
                      </ElButton>
                    </div>
                  </div>
                </template>
              </ElTree>
            </div>

            <!-- 列表视图 -->
            <div v-else-if="viewMode === 'list'">
              <ElTable :data="filteredPermissions" stripe border>
                <ElTableColumn prop="permission_name" label="权限名称" min-width="150">
                  <template #default="{ row }">
                    <div class="flex items-center gap-8px">
                      <ElTag size="small" :type="getPermissionTypeColor(row.permission_type)">
                        {{ getPermissionTypeText(row.permission_type) }}
                      </ElTag>
                      <span class="font-medium">{{ row.permission_name }}</span>
                    </div>
                  </template>
                </ElTableColumn>

                <ElTableColumn prop="permission_code" label="权限代码" width="200" />

                <ElTableColumn prop="grant_type" label="授权类型" width="100">
                  <template #default="{ row }">
                    <ElTag size="small" :type="getGrantTypeColor(row.grant_type)">
                      {{ row.grant_type === 'grant' ? '授予' : '拒绝' }}
                    </ElTag>
                  </template>
                </ElTableColumn>

                <ElTableColumn prop="source" label="权限来源" width="120">
                  <template #default="{ row }">
                    <ElTag size="small" :type="row.source === 'role' ? 'primary' : 'success'">
                      {{ row.source === 'role' ? '角色权限' : '直接权限' }}
                    </ElTag>
                  </template>
                </ElTableColumn>

                <ElTableColumn prop="role_name" label="来源角色" width="120">
                  <template #default="{ row }">
                    <span v-if="row.source === 'role'">{{ row.role_name }}</span>
                    <span v-else class="text-gray-400">-</span>
                  </template>
                </ElTableColumn>

                <ElTableColumn prop="data_scope" label="数据范围" width="100" />

                <ElTableColumn label="操作" width="100">
                  <template #default="{ row }">
                    <ElButton size="small" type="primary" link @click="tracePermission(row.permission_id)">
                      追溯
                    </ElButton>
                  </template>
                </ElTableColumn>
              </ElTable>
            </div>

            <!-- 关系图视图 -->
            <div v-else-if="viewMode === 'graph'" class="py-32px text-center">
              <icon-ic-round-hub class="mb-16px text-64px text-gray-400" />
              <h3 class="mb-8px text-lg text-gray-600 font-medium">权限关系图</h3>
              <p class="mb-16px text-gray-500">可视化展示用户权限的继承关系和依赖结构</p>
              <ElButton type="primary">
                <template #icon>
                  <icon-ic-round-visibility />
                </template>
                查看关系图
              </ElButton>
            </div>
          </div>
        </ElTabPane>

        <!-- 权限冲突 -->
        <ElTabPane v-if="props.showConflicts" label="权限冲突" name="conflicts">
          <div v-if="hasConflicts" class="space-y-16px">
            <div
              v-for="conflict in permissionConflicts"
              :key="`conflict-${conflict.permission_id}-${conflict.conflict_type}`"
              class="border border-red-200 rounded-8px p-16px"
            >
              <div class="mb-12px flex items-start justify-between">
                <div>
                  <h4 class="text-red-600 font-medium">{{ conflict.permission_name }}</h4>
                  <p class="text-sm text-gray-600">{{ conflict.permission_code }}</p>
                </div>
                <ElTag :type="getConflictSeverityColor(conflict.severity)">
                  {{ conflict.severity === 'high' ? '高' : conflict.severity === 'medium' ? '中' : '低' }}
                </ElTag>
              </div>

              <div class="space-y-8px">
                <div v-if="conflict.conflict_type === 'grant_type'" class="text-sm">
                  <div class="text-red-600">授权类型冲突：</div>
                  <div>
                    角色权限: {{ conflict.role_grant === 'grant' ? '授予' : '拒绝' }} (来源: {{ conflict.role_source }})
                  </div>
                  <div>直接权限: {{ conflict.direct_grant === 'grant' ? '授予' : '拒绝' }}</div>
                </div>

                <div v-if="conflict.conflict_type === 'data_scope'" class="text-sm">
                  <div class="text-orange-600">数据范围冲突：</div>
                  <div>角色权限: {{ conflict.role_scope }} (来源: {{ conflict.role_source }})</div>
                  <div>直接权限: {{ conflict.direct_scope }}</div>
                </div>

                <div v-if="conflict.conflict_type === 'role_grant_conflict'" class="text-sm">
                  <div class="text-red-600">角色间授权冲突：</div>
                  <div v-for="role in conflict.roles" :key="role.name">
                    {{ role.name }}: {{ role.grant === 'grant' ? '授予' : '拒绝' }}
                  </div>
                </div>

                <div v-if="conflict.conflict_type === 'role_scope_conflict'" class="text-sm">
                  <div class="text-orange-600">角色间范围冲突：</div>
                  <div v-for="role in conflict.roles" :key="role.name">{{ role.name }}: {{ role.scope }}</div>
                </div>
              </div>

              <div class="mt-12px flex gap-8px">
                <ElButton size="small" type="primary" @click="resolveConflict(conflict, 'use_direct')">
                  使用直接权限
                </ElButton>
                <ElButton size="small" type="success" @click="resolveConflict(conflict, 'use_role')">
                  使用角色权限
                </ElButton>
                <ElButton size="small" type="warning" @click="resolveConflict(conflict, 'merge')">智能合并</ElButton>
              </div>
            </div>
          </div>
          <div v-else class="py-32px text-center text-gray-500">
            <icon-ic-round-check-circle class="mb-8px text-48px text-success" />
            <div>没有发现权限冲突</div>
          </div>
        </ElTabPane>

        <!-- 有效权限 -->
        <ElTabPane label="有效权限" name="effective">
          <div v-if="inheritanceData.effectivePermissions.length > 0" class="grid grid-cols-1 gap-12px md:grid-cols-2">
            <ElCard
              v-for="permission in inheritanceData.effectivePermissions"
              :key="`effective-${permission.permission_id}`"
              class="permission-card"
              shadow="hover"
            >
              <div class="space-y-8px">
                <div class="flex items-center justify-between">
                  <span class="font-medium">{{ permission.permission_name }}</span>
                  <ElTag size="small" :type="getPermissionTypeColor(permission.permission_type)">
                    {{ permission.permission_type }}
                  </ElTag>
                </div>
                <div class="text-sm text-gray-500">{{ permission.permission_code }}</div>
                <div class="flex items-center justify-between">
                  <span class="text-sm">{{ getPermissionSource(permission) }}</span>
                  <div class="flex gap-4px">
                    <ElTag size="small" :type="getGrantTypeColor(permission.grant_type)">
                      {{ permission.grant_type === 'grant' ? '授予' : '拒绝' }}
                    </ElTag>
                    <ElTag size="small" type="info">
                      {{ permission.data_scope }}
                    </ElTag>
                  </div>
                </div>
              </div>
            </ElCard>
          </div>
          <div v-else class="py-32px text-center text-gray-500">该用户没有任何有效权限</div>
        </ElTabPane>
      </ElTabs>
    </div>

    <template #footer>
      <div class="flex justify-end">
        <ElButton @click="closeDialog">关闭</ElButton>
      </div>
    </template>
  </ElDialog>

  <!-- 权限追溯对话框 -->
  <ElDialog v-model="permissionTraceVisible" title="权限追溯分析" width="600px" :close-on-click-modal="false">
    <div v-if="permissionTrace.permission_id" class="space-y-16px">
      <!-- 权限基本信息 -->
      <ElCard>
        <template #header>
          <span class="font-medium">权限信息</span>
        </template>
        <div class="space-y-8px">
          <div class="flex items-center gap-8px">
            <span class="w-80px text-gray-600">权限名称:</span>
            <span class="font-medium">{{ getPermissionInfo(permissionTrace.permission_id)?.permission_name }}</span>
          </div>
          <div class="flex items-center gap-8px">
            <span class="w-80px text-gray-600">权限代码:</span>
            <span>{{ getPermissionInfo(permissionTrace.permission_id)?.permission_code }}</span>
          </div>
          <div class="flex items-center gap-8px">
            <span class="w-80px text-gray-600">权限类型:</span>
            <ElTag
              size="small"
              :type="getPermissionTypeColor(getPermissionInfo(permissionTrace.permission_id)?.permission_type)"
            >
              {{ getPermissionTypeText(getPermissionInfo(permissionTrace.permission_id)?.permission_type) }}
            </ElTag>
          </div>
        </div>
      </ElCard>

      <!-- 权限来源 -->
      <ElCard>
        <template #header>
          <span class="font-medium">权限来源 ({{ permissionTrace.sources?.length || 0 }})</span>
        </template>
        <div v-if="permissionTrace.sources?.length > 0" class="space-y-8px">
          <div
            v-for="(source, index) in permissionTrace.sources"
            :key="index"
            class="border rounded-8px p-12px"
            :class="source.type === 'direct' ? 'border-green-200 bg-green-50' : 'border-blue-200 bg-blue-50'"
          >
            <div class="mb-8px flex items-center justify-between">
              <div class="flex items-center gap-8px">
                <icon-ic-round-group v-if="source.type === 'role'" class="text-blue-500" />
                <icon-ic-round-person v-else class="text-green-500" />
                <span class="font-medium">
                  {{ source.type === 'role' ? source.role_name : '直接分配' }}
                </span>
                <ElTag size="small" :type="source.type === 'role' ? 'primary' : 'success'">
                  优先级 {{ source.priority }}
                </ElTag>
              </div>
              <ElTag size="small" :type="getGrantTypeColor(source.grant_type)">
                {{ source.grant_type === 'grant' ? '授予' : '拒绝' }}
              </ElTag>
            </div>
            <div class="text-sm text-gray-600">数据范围: {{ source.data_scope || '全部' }}</div>
          </div>
        </div>
        <div v-else class="py-16px text-center text-gray-500">未找到权限来源</div>
      </ElCard>

      <!-- 最终有效权限 -->
      <ElCard v-if="permissionTrace.effective">
        <template #header>
          <span class="font-medium">最终有效权限</span>
        </template>
        <div class="border border-yellow-200 rounded-8px bg-yellow-50 p-12px">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-8px">
              <icon-ic-round-check-circle class="text-yellow-600" />
              <span class="font-medium">
                {{ permissionTrace.effective.type === 'role' ? permissionTrace.effective.role_name : '直接分配' }}
              </span>
              <span class="text-sm text-gray-600">(优先级最高)</span>
            </div>
            <ElTag size="small" :type="getGrantTypeColor(permissionTrace.effective.grant_type)">
              {{ permissionTrace.effective.grant_type === 'grant' ? '授予' : '拒绝' }}
            </ElTag>
          </div>
          <div class="mt-4px text-sm text-gray-600">数据范围: {{ permissionTrace.effective.data_scope || '全部' }}</div>
        </div>
      </ElCard>

      <!-- 权限冲突 -->
      <ElCard v-if="permissionTrace.conflicts?.length > 0">
        <template #header>
          <div class="flex items-center gap-8px">
            <icon-ic-round-warning class="text-red-500" />
            <span class="text-red-600 font-medium">权限冲突 ({{ permissionTrace.conflicts.length }})</span>
          </div>
        </template>
        <div class="space-y-8px">
          <div
            v-for="(conflict, index) in permissionTrace.conflicts"
            :key="index"
            class="border border-red-200 rounded-8px bg-red-50 p-12px"
          >
            <div class="flex items-center gap-8px">
              <icon-ic-round-error class="text-red-500" />
              <span class="text-red-600 font-medium">{{ conflict.description }}</span>
            </div>
            <div class="mt-4px text-sm text-red-600">冲突类型: {{ conflict.type }}</div>
          </div>
        </div>
      </ElCard>
    </div>

    <template #footer>
      <div class="flex justify-end">
        <ElButton @click="permissionTraceVisible = false">关闭</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.permission-card {
  transition: all 0.3s ease;
}

.permission-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .grid-cols-2.md\:grid-cols-5 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-cols-1.md\:grid-cols-2 {
    grid-template-columns: 1fr;
  }
}
</style>
