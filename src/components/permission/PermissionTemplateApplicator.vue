<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { request } from '@/service/request';

interface Props {
  visible: boolean;
  userId?: number;
  userIds?: number[];
  mode?: 'single' | 'batch';
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'applied', result: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  userId: undefined,
  userIds: () => [],
  mode: 'single'
});

const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
const applying = ref(false);
const templates = ref<any[]>([]);
const selectedTemplateId = ref<number | null>(null);
const selectedTemplate = ref<any>(null);
const applyMode = ref<'replace' | 'merge' | 'add'>('merge');

// 应用选项
const applyOptions = [
  {
    value: 'replace',
    label: '替换权限',
    description: '完全替换用户现有权限',
    icon: 'ic:round-swap-horiz',
    color: 'danger'
  },
  {
    value: 'merge',
    label: '合并权限',
    description: '与现有权限合并，保留更宽松的权限',
    icon: 'ic:round-merge',
    color: 'warning'
  },
  {
    value: 'add',
    label: '添加权限',
    description: '在现有权限基础上添加新权限',
    icon: 'ic:round-add',
    color: 'success'
  }
];

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
});

const targetUsers = computed(() => {
  if (props.mode === 'single' && props.userId) {
    return [props.userId];
  }
  return props.userIds || [];
});

const canApply = computed(() => {
  return selectedTemplateId.value && targetUsers.value.length > 0;
});

const isBatchMode = computed(() => props.mode === 'batch');

// 获取权限模板列表
async function fetchTemplates() {
  loading.value = true;
  try {
    const response = await request({
      url: '/api/permission/templates',
      method: 'get',
      params: {
        status: 'active',
        include_permissions: true
      }
    });

    if (Array.isArray(response)) {
      templates.value = response;
    }
  } catch (error) {
    console.error('获取权限模板失败:', error);
    ElMessage.error('获取权限模板失败');
  } finally {
    loading.value = false;
  }
}

// 选择模板
function selectTemplate(template: any) {
  selectedTemplateId.value = template.template_id;
  selectedTemplate.value = template;
}

// 应用权限模板
async function applyTemplate() {
  if (!canApply.value) {
    ElMessage.warning('请选择模板和目标用户');
    return;
  }

  const confirmMessage = isBatchMode.value
    ? `确定要为 ${targetUsers.value.length} 个用户应用权限模板"${selectedTemplate.value.template_name}"吗？`
    : `确定要为用户应用权限模板"${selectedTemplate.value.template_name}"吗？`;

  try {
    await ElMessageBox.confirm(confirmMessage, '确认应用权限模板', {
      type: 'warning',
      confirmButtonText: '确认应用',
      cancelButtonText: '取消'
    });

    applying.value = true;

    const response = await request({
      url: '/api/permission/template/apply',
      method: 'post',
      data: {
        template_id: selectedTemplateId.value,
        user_ids: targetUsers.value,
        apply_mode: applyMode.value,
        batch_mode: isBatchMode.value
      }
    });

    ElMessage.success(isBatchMode.value ? `成功为 ${targetUsers.value.length} 个用户应用权限模板` : '权限模板应用成功');

    emit('applied', response);
    closeDialog();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('应用权限模板失败:', error);
      ElMessage.error('应用权限模板失败');
    }
  } finally {
    applying.value = false;
  }
}

// 获取模板分类颜色
function getCategoryColor(category: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' {
  const colorMap: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    role: 'primary',
    department: 'success',
    project: 'warning',
    custom: 'info'
  };
  return colorMap[category] || 'info';
}

// 获取分类标签
function getCategoryLabel(category: string): string {
  const labelMap: Record<string, string> = {
    role: '角色模板',
    department: '部门模板',
    project: '项目模板',
    custom: '自定义模板'
  };
  return labelMap[category] || category;
}

// 获取应用模式信息
function getApplyModeInfo(mode: string) {
  return applyOptions.find(option => option.value === mode);
}

// 关闭对话框
function closeDialog() {
  selectedTemplateId.value = null;
  selectedTemplate.value = null;
  applyMode.value = 'merge';
  emit('update:visible', false);
}

// 监听对话框显示状态
watch(
  () => props.visible,
  visible => {
    if (visible) {
      fetchTemplates();
    }
  }
);

// 组件挂载
onMounted(() => {
  if (props.visible) {
    fetchTemplates();
  }
});
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="isBatchMode ? '批量应用权限模板' : '应用权限模板'"
    width="800px"
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <div v-loading="loading" class="space-y-16px">
      <!-- 目标用户信息 -->
      <ElCard>
        <template #header>
          <div class="flex items-center gap-8px">
            <icon-ic-round-people class="text-blue-500" />
            <span class="font-medium">目标用户</span>
          </div>
        </template>
        <div class="flex items-center gap-8px">
          <span class="text-gray-600">
            {{ isBatchMode ? `已选择 ${targetUsers.length} 个用户` : '单个用户' }}
          </span>
          <ElTag type="info" size="small">
            {{ isBatchMode ? '批量模式' : '单用户模式' }}
          </ElTag>
        </div>
      </ElCard>

      <!-- 应用模式选择 -->
      <ElCard>
        <template #header>
          <div class="flex items-center gap-8px">
            <icon-ic-round-settings class="text-green-500" />
            <span class="font-medium">应用模式</span>
          </div>
        </template>
        <ElRadioGroup v-model="applyMode" class="w-full">
          <div class="grid grid-cols-1 gap-12px md:grid-cols-3">
            <div
              v-for="option in applyOptions"
              :key="option.value"
              class="cursor-pointer border rounded-8px p-12px transition-all duration-200"
              :class="{
                'border-primary bg-primary-light-9': applyMode === option.value,
                'border-gray-200 hover:border-gray-300': applyMode !== option.value
              }"
              @click="applyMode = option.value as 'replace' | 'merge' | 'add'"
            >
              <ElRadio :value="option.value" class="w-full">
                <div class="flex items-start gap-8px">
                  <component :is="`icon-${option.icon}`" :class="`text-${option.color} text-20px`" />
                  <div class="flex-1">
                    <div class="font-medium">{{ option.label }}</div>
                    <div class="mt-4px text-sm text-gray-600">{{ option.description }}</div>
                  </div>
                </div>
              </ElRadio>
            </div>
          </div>
        </ElRadioGroup>
      </ElCard>

      <!-- 模板选择 -->
      <ElCard>
        <template #header>
          <div class="flex items-center gap-8px">
            <icon-ic-round-settings class="text-purple-500" />
            <span class="font-medium">选择权限模板</span>
          </div>
        </template>

        <div v-if="templates.length > 0" class="max-h-400px overflow-y-auto space-y-8px">
          <div
            v-for="template in templates"
            :key="template.template_id"
            class="cursor-pointer border rounded-8px p-12px transition-all duration-200"
            :class="{
              'border-primary bg-primary-light-9': selectedTemplateId === template.template_id,
              'border-gray-200 hover:border-gray-300': selectedTemplateId !== template.template_id
            }"
            @click="selectTemplate(template)"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="mb-4px flex items-center gap-8px">
                  <span class="font-medium">{{ template.template_name }}</span>
                  <ElTag size="small" :type="getCategoryColor(template.template_category)">
                    {{ getCategoryLabel(template.template_category) }}
                  </ElTag>
                  <ElTag v-if="template.is_system" size="small" type="info">系统</ElTag>
                  <ElTag v-if="template.is_default" size="small" type="success">默认</ElTag>
                </div>
                <p class="mb-8px text-sm text-gray-600">{{ template.template_description || '无描述' }}</p>
                <div class="flex items-center gap-12px text-sm text-gray-500">
                  <span>{{ template.permissions?.length || 0 }} 个权限</span>
                  <span>创建时间: {{ new Date(template.created_at).toLocaleDateString() }}</span>
                </div>
              </div>
              <div v-if="selectedTemplateId === template.template_id" class="flex-shrink-0">
                <icon-ic-round-check-circle class="text-20px text-primary" />
              </div>
            </div>
          </div>
        </div>

        <div v-else class="py-32px text-center text-gray-500">
          <icon-ic-round-settings class="mb-8px text-48px" />
          <div>暂无可用的权限模板</div>
        </div>
      </ElCard>

      <!-- 选中模板预览 -->
      <ElCard v-if="selectedTemplate">
        <template #header>
          <div class="flex items-center gap-8px">
            <icon-ic-round-visibility class="text-orange-500" />
            <span class="font-medium">模板预览</span>
          </div>
        </template>

        <div class="space-y-12px">
          <div class="flex items-center justify-between">
            <span class="font-medium">{{ selectedTemplate.template_name }}</span>
            <div class="flex items-center gap-8px">
              <ElTag size="small" :type="getCategoryColor(selectedTemplate.template_category)">
                {{ getCategoryLabel(selectedTemplate.template_category) }}
              </ElTag>
              <span class="text-sm text-gray-600">{{ selectedTemplate.permissions?.length || 0 }} 个权限</span>
            </div>
          </div>

          <div
            v-if="selectedTemplate.permissions && selectedTemplate.permissions.length > 0"
            class="grid grid-cols-1 max-h-200px gap-8px overflow-y-auto md:grid-cols-2"
          >
            <div
              v-for="permission in selectedTemplate.permissions"
              :key="permission.permission_id"
              class="flex items-center gap-8px rounded bg-gray-50 p-8px"
            >
              <ElTag size="small" type="info">
                {{ permission.permission_type }}
              </ElTag>
              <span class="text-sm">{{ permission.permission_name }}</span>
            </div>
          </div>
        </div>
      </ElCard>
    </div>

    <template #footer>
      <div class="flex justify-between">
        <div class="text-sm text-gray-600">
          <span v-if="selectedTemplate">将以"{{ getApplyModeInfo(applyMode)?.label }}"模式应用模板</span>
        </div>
        <div class="flex gap-8px">
          <ElButton @click="closeDialog">取消</ElButton>
          <ElButton type="primary" :disabled="!canApply" :loading="applying" @click="applyTemplate">
            <template #icon>
              <icon-ic-round-check />
            </template>
            {{ isBatchMode ? '批量应用' : '应用模板' }}
          </ElButton>
        </div>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.template-card {
  transition: all 0.3s ease;
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.apply-mode-card {
  transition: all 0.2s ease;
}

.apply-mode-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
