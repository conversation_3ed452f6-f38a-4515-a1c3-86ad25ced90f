<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { request } from '@/service/request';
import PermissionTree from './PermissionTree.vue';

defineOptions({
  name: 'PermissionTemplateManager'
});

interface Props {
  visible: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'template-applied', template: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
const saving = ref(false);
const activeTab = ref('list');
const importing = ref(false);
const exporting = ref(false);
const duplicating = ref(false);

// 搜索和筛选
const searchKeyword = ref('');
const selectedCategory = ref('');
const showSystemTemplates = ref(true);
const showUserTemplates = ref(true);

// 模板统计
const templateStats = ref({
  total: 0,
  system: 0,
  user: 0,
  categories: {} as Record<string, number>
});

// 批量操作
const selectedTemplateIds = ref<number[]>([]);
const batchOperationVisible = ref(false);

// 模板预览
const previewTemplate = ref<any>(null);
const previewVisible = ref(false);

// 模板导入导出
const importFileInput = ref<HTMLInputElement>();
const exportFormat = ref<'json' | 'excel'>('json');

// 模板列表
const templates = ref<any[]>([]);
const selectedTemplate = ref<any>(null);

// 模板表单
const templateForm = ref({
  template_id: null as number | null,
  template_name: '',
  template_description: '',
  template_category: 'role',
  permission_ids: [] as number[],
  is_system: 0,
  is_default: 0,
  sort_order: 0
});

// 权限树
const permissionTree = ref<any[]>([]);
const selectedPermissions = ref<number[]>([]);

// 模板分类
const templateCategories = [
  { label: '角色模板', value: 'role' },
  { label: '部门模板', value: 'department' },
  { label: '项目模板', value: 'project' },
  { label: '自定义模板', value: 'custom' }
];

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
});

const isEdit = computed(() => templateForm.value.template_id !== null);

const canSave = computed(() => {
  return templateForm.value.template_name && selectedPermissions.value.length > 0;
});

// 筛选后的模板列表
const filteredTemplates = computed(() => {
  let filtered = templates.value;

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(
      template =>
        template.template_name.toLowerCase().includes(keyword) ||
        template.template_description.toLowerCase().includes(keyword)
    );
  }

  // 分类筛选
  if (selectedCategory.value) {
    filtered = filtered.filter(template => template.template_category === selectedCategory.value);
  }

  // 系统/用户模板筛选
  filtered = filtered.filter(template => {
    if (template.is_system && !showSystemTemplates.value) return false;
    if (!template.is_system && !showUserTemplates.value) return false;
    return true;
  });

  return filtered;
});

// 分类统计
const categoryStats = computed(() => {
  const stats: Record<string, number> = {};
  templates.value.forEach(template => {
    const category = template.template_category;
    stats[category] = (stats[category] || 0) + 1;
  });
  return stats;
});

// 是否可以批量操作
const canBatchOperate = computed(() => selectedTemplateIds.value.length > 0);

// 选中的模板信息
const selectedTemplatesInfo = computed(() => {
  return templates.value.filter(template => selectedTemplateIds.value.includes(template.template_id));
});

// 获取模板列表
async function fetchTemplates() {
  loading.value = true;
  try {
    const response = await request({
      url: '/api/permission/templates',
      method: 'get'
    });

    if (Array.isArray(response)) {
      templates.value = response;
      updateTemplateStats();
    }
  } catch (error) {
    console.error('获取权限模板失败:', error);
    ElMessage.error('获取权限模板失败');
  } finally {
    loading.value = false;
  }
}

// 获取权限树
async function fetchPermissionTree() {
  try {
    const response = await request({
      url: '/api/permission/tree',
      method: 'get'
    });

    if (Array.isArray(response)) {
      permissionTree.value = response;
    }
  } catch (error) {
    console.error('获取权限树失败:', error);
  }
}

// 创建新模板
function createTemplate() {
  templateForm.value = {
    template_id: null,
    template_name: '',
    template_description: '',
    template_category: 'role',
    permission_ids: [],
    is_system: 0,
    is_default: 0,
    sort_order: 0
  };
  selectedPermissions.value = [];
  activeTab.value = 'form';
}

// 编辑模板
async function editTemplate(template: any) {
  templateForm.value = {
    template_id: template.template_id,
    template_name: template.template_name,
    template_description: template.template_description,
    template_category: template.template_category,
    permission_ids: template.permission_ids || [],
    is_system: template.is_system,
    is_default: template.is_default,
    sort_order: template.sort_order
  };

  // 获取模板权限
  try {
    const response = await request({
      url: `/api/permission/template/${template.template_id}/permissions`,
      method: 'get'
    });

    if (Array.isArray(response)) {
      selectedPermissions.value = response.map(p => p.permission_id);
    }
  } catch (error) {
    console.error('获取模板权限失败:', error);
  }

  activeTab.value = 'form';
}

// 保存模板
async function saveTemplate() {
  saving.value = true;
  try {
    const data = {
      ...templateForm.value,
      permission_ids: selectedPermissions.value
    };

    if (isEdit.value) {
      await request({
        url: `/api/permission/template/${templateForm.value.template_id}`,
        method: 'put',
        data
      });
      ElMessage.success('模板更新成功');
    } else {
      await request({
        url: '/api/permission/template',
        method: 'post',
        data
      });
      ElMessage.success('模板创建成功');
    }

    await fetchTemplates();
    activeTab.value = 'list';
  } catch (error) {
    console.error('保存模板失败:', error);
    ElMessage.error('保存模板失败');
  } finally {
    saving.value = false;
  }
}

// 删除模板
async function deleteTemplate(template: any) {
  try {
    await ElMessageBox.confirm(`确定要删除模板"${template.template_name}"吗？`, '确认删除', { type: 'warning' });

    await request({
      url: `/api/permission/template/${template.template_id}`,
      method: 'delete'
    });

    ElMessage.success('模板删除成功');
    await fetchTemplates();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除模板失败:', error);
      ElMessage.error('删除模板失败');
    }
  }
}

// 应用模板
function applyTemplate(template: any) {
  emit('template-applied', template);
  ElMessage.success(`已应用模板：${template.template_name}`);
}

// 复制模板
function duplicateTemplate(template: any) {
  templateForm.value = {
    template_id: null,
    template_name: `${template.template_name} - 副本`,
    template_description: template.template_description,
    template_category: template.template_category,
    permission_ids: template.permission_ids || [],
    is_system: 0,
    is_default: 0,
    sort_order: template.sort_order
  };
  selectedPermissions.value = [...(template.permission_ids || [])];
  activeTab.value = 'form';
}

// 预览模板权限（旧版本，保留兼容性）
async function previewTemplateOld(template: any) {
  selectedTemplate.value = template;

  try {
    const response = await request({
      url: `/api/permission/template/${template.template_id}/permissions`,
      method: 'get'
    });

    if (Array.isArray(response)) {
      selectedTemplate.value.permissions = response;
    }
  } catch (error) {
    console.error('获取模板权限失败:', error);
  }
}

// 关闭对话框
function closeDialog() {
  emit('update:visible', false);
}

// 取消编辑
function cancelEdit() {
  activeTab.value = 'list';
}

// 获取分类标签
function getCategoryLabel(category: string) {
  const cat = templateCategories.find(c => c.value === category);
  return cat ? cat.label : category;
}

// 获取分类颜色
function getCategoryColor(category: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' {
  const colorMap: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    role: 'primary',
    department: 'success',
    project: 'warning',
    custom: 'info'
  };
  return colorMap[category] || 'info';
}

// 获取权限类型颜色
function getPermissionTypeColor(type: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' {
  const colorMap: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    menu: 'primary',
    button: 'success',
    api: 'warning',
    data: 'danger'
  };
  return colorMap[type] || 'info';
}

// 获取权限类型文本
function getPermissionTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    menu: '菜单权限',
    button: '按钮权限',
    api: 'API权限',
    data: '数据权限'
  };
  return typeMap[type] || type;
}

// 更新模板统计
function updateTemplateStats() {
  const stats = {
    total: templates.value.length,
    system: templates.value.filter(t => t.is_system).length,
    user: templates.value.filter(t => !t.is_system).length,
    categories: {} as Record<string, number>
  };

  templates.value.forEach(template => {
    const category = template.template_category;
    stats.categories[category] = (stats.categories[category] || 0) + 1;
  });

  templateStats.value = stats;
}

// 模板预览
async function showTemplatePreview(template: any) {
  previewTemplate.value = template;

  try {
    const response = await request({
      url: `/api/permission/template/${template.template_id}/permissions`,
      method: 'get'
    });

    if (Array.isArray(response)) {
      previewTemplate.value.permissions = response;
    }
  } catch (error) {
    console.error('获取模板权限失败:', error);
  }

  previewVisible.value = true;
}

// 复制模板（增强版）
async function duplicateTemplateAdvanced(template: any) {
  duplicating.value = true;
  try {
    const newTemplate = {
      template_name: `${template.template_name} - 副本`,
      template_description: `${template.template_description} (复制)`,
      template_category: template.template_category,
      permission_ids: template.permission_ids || [],
      is_system: 0,
      is_default: 0,
      sort_order: template.sort_order
    };

    await request({
      url: '/api/permission/template',
      method: 'post',
      data: newTemplate
    });

    ElMessage.success('模板复制成功');
    await fetchTemplates();
  } catch (error) {
    console.error('复制模板失败:', error);
    ElMessage.error('复制模板失败');
  } finally {
    duplicating.value = false;
  }
}

// 批量删除模板
async function batchDeleteTemplates() {
  if (selectedTemplateIds.value.length === 0) {
    ElMessage.warning('请选择要删除的模板');
    return;
  }

  try {
    await ElMessageBox.confirm(`确定要删除选中的 ${selectedTemplateIds.value.length} 个模板吗？`, '批量删除确认', {
      type: 'warning'
    });

    await request({
      url: '/api/permission/templates/batch-delete',
      method: 'post',
      data: { template_ids: selectedTemplateIds.value }
    });

    ElMessage.success(`成功删除 ${selectedTemplateIds.value.length} 个模板`);
    selectedTemplateIds.value = [];
    await fetchTemplates();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除模板失败:', error);
      ElMessage.error('批量删除模板失败');
    }
  }
}

// 批量导出模板
async function batchExportTemplates() {
  if (selectedTemplateIds.value.length === 0) {
    ElMessage.warning('请选择要导出的模板');
    return;
  }

  exporting.value = true;
  try {
    const response = await request({
      url: '/api/permission/templates/export',
      method: 'post',
      data: {
        template_ids: selectedTemplateIds.value,
        format: exportFormat.value
      },
      responseType: 'blob'
    });

    // 创建下载链接
    const blob = new Blob([response], {
      type:
        exportFormat.value === 'json'
          ? 'application/json'
          : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `permission_templates_${new Date().getTime()}.${exportFormat.value}`;
    link.click();
    window.URL.revokeObjectURL(url);

    ElMessage.success('模板导出成功');
  } catch (error) {
    console.error('导出模板失败:', error);
    ElMessage.error('导出模板失败');
  } finally {
    exporting.value = false;
  }
}

// 导入模板
async function importTemplates(file: File) {
  importing.value = true;
  try {
    const formData = new FormData();
    formData.append('file', file);

    const response = await request({
      url: '/api/permission/templates/import',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    ElMessage.success(`成功导入 ${response.imported_count} 个模板`);
    await fetchTemplates();
  } catch (error) {
    console.error('导入模板失败:', error);
    ElMessage.error('导入模板失败');
  } finally {
    importing.value = false;
  }
}

// 处理文件导入
function handleFileImport(event: Event) {
  const target = event.target as HTMLInputElement;
  const file = target.files?.[0];
  if (file) {
    importTemplates(file);
    target.value = ''; // 清空文件输入
  }
}

// 全选/取消全选
function toggleSelectAll() {
  if (selectedTemplateIds.value.length === filteredTemplates.value.length) {
    selectedTemplateIds.value = [];
  } else {
    selectedTemplateIds.value = filteredTemplates.value.map(t => t.template_id);
  }
}

// 重置筛选条件
function resetFilters() {
  searchKeyword.value = '';
  selectedCategory.value = '';
  showSystemTemplates.value = true;
  showUserTemplates.value = true;
}

// 切换模板选择状态
function toggleTemplateSelection(templateId: number, checked?: boolean) {
  const index = selectedTemplateIds.value.indexOf(templateId);
  const shouldSelect = checked !== undefined ? checked : index === -1;

  if (shouldSelect && index === -1) {
    selectedTemplateIds.value.push(templateId);
  } else if (!shouldSelect && index > -1) {
    selectedTemplateIds.value.splice(index, 1);
  }
}

// 处理模板操作
async function handleTemplateAction(command: string, template: any) {
  switch (command) {
    case 'preview':
      await showTemplatePreview(template);
      break;
    case 'apply':
      applyTemplate(template);
      break;
    case 'edit':
      await editTemplate(template);
      break;
    case 'duplicate':
      await duplicateTemplateAdvanced(template);
      break;
    case 'export':
      await exportSingleTemplate(template);
      break;
    case 'delete':
      await deleteTemplate(template);
      break;
    default:
      ElMessage.warning('未知操作');
  }
}

// 导出单个模板
async function exportSingleTemplate(template: any) {
  exporting.value = true;
  try {
    const response = await request({
      url: '/api/permission/templates/export',
      method: 'post',
      data: {
        template_ids: [template.template_id],
        format: exportFormat.value
      },
      responseType: 'blob'
    });

    // 创建下载链接
    const blob = new Blob([response], {
      type:
        exportFormat.value === 'json'
          ? 'application/json'
          : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${template.template_name}.${exportFormat.value}`;
    link.click();
    window.URL.revokeObjectURL(url);

    ElMessage.success('模板导出成功');
  } catch (error) {
    console.error('导出模板失败:', error);
    ElMessage.error('导出模板失败');
  } finally {
    exporting.value = false;
  }
}

// 初始化数据
async function initData() {
  loading.value = true;
  try {
    await Promise.all([fetchTemplates(), fetchPermissionTree()]);
  } finally {
    loading.value = false;
  }
}

// 监听对话框显示状态
watch(
  () => props.visible,
  visible => {
    if (visible) {
      initData();
    }
  }
);
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    title="权限模板管理"
    width="1000px"
    :close-on-click-modal="false"
    destroy-on-close
    @close="closeDialog"
  >
    <div v-loading="loading">
      <ElTabs v-model="activeTab">
        <!-- 模板列表 -->
        <ElTabPane label="模板列表" name="list">
          <div class="space-y-16px">
            <!-- 标题和操作栏 -->
            <div class="mb-16px flex items-center justify-between">
              <div class="flex items-center gap-16px">
                <h4 class="text-lg font-medium">权限模板</h4>
                <div class="flex items-center gap-8px text-sm text-gray-600">
                  <span>共 {{ templateStats.total }} 个模板</span>
                  <span>|</span>
                  <span>系统: {{ templateStats.system }}</span>
                  <span>|</span>
                  <span>用户: {{ templateStats.user }}</span>
                </div>
              </div>
              <div class="flex items-center gap-8px">
                <ElButton @click="resetFilters">
                  <template #icon>
                    <icon-ic-round-refresh />
                  </template>
                  重置筛选
                </ElButton>
                <ElButton type="primary" @click="createTemplate">
                  <template #icon>
                    <icon-ic-round-plus />
                  </template>
                  新建模板
                </ElButton>
              </div>
            </div>

            <!-- 搜索和筛选栏 -->
            <ElCard class="mb-16px">
              <div class="space-y-12px">
                <!-- 第一行：搜索和分类筛选 -->
                <div class="flex flex-wrap items-center gap-12px">
                  <ElInput v-model="searchKeyword" placeholder="搜索模板名称或描述" clearable class="w-280px">
                    <template #prefix>
                      <icon-ic-round-search />
                    </template>
                  </ElInput>

                  <ElSelect v-model="selectedCategory" placeholder="选择分类" clearable class="w-140px">
                    <ElOption
                      v-for="category in templateCategories"
                      :key="category.value"
                      :label="category.label"
                      :value="category.value"
                    />
                  </ElSelect>

                  <div class="flex items-center gap-12px">
                    <ElCheckbox v-model="showSystemTemplates">系统模板</ElCheckbox>
                    <ElCheckbox v-model="showUserTemplates">用户模板</ElCheckbox>
                  </div>
                </div>

                <!-- 第二行：批量操作和导入导出 -->
                <div class="flex items-center justify-between">
                  <div class="flex items-center gap-8px">
                    <ElCheckbox
                      :model-value="
                        selectedTemplateIds.length === filteredTemplates.length && filteredTemplates.length > 0
                      "
                      :indeterminate="
                        selectedTemplateIds.length > 0 && selectedTemplateIds.length < filteredTemplates.length
                      "
                      @change="toggleSelectAll"
                    >
                      全选 ({{ filteredTemplates.length }})
                    </ElCheckbox>

                    <div v-if="canBatchOperate" class="flex items-center gap-8px">
                      <span class="text-sm text-gray-600">已选择 {{ selectedTemplateIds.length }} 个</span>
                      <ElButton size="small" type="danger" @click="batchDeleteTemplates">批量删除</ElButton>
                      <ElButton size="small" type="primary" :loading="exporting" @click="batchExportTemplates">
                        批量导出
                      </ElButton>
                    </div>
                  </div>

                  <div class="flex items-center gap-8px">
                    <ElSelect v-model="exportFormat" size="small" class="w-100px">
                      <ElOption label="JSON" value="json" />
                      <ElOption label="Excel" value="excel" />
                    </ElSelect>
                    <ElButton size="small" :loading="importing" @click="importFileInput?.click()">
                      <template #icon>
                        <icon-ic-round-upload />
                      </template>
                      导入
                    </ElButton>
                    <input
                      ref="importFileInput"
                      type="file"
                      accept=".json,.xlsx,.xls"
                      style="display: none"
                      @change="handleFileImport"
                    />
                  </div>
                </div>
              </div>
            </ElCard>

            <!-- 模板网格 -->
            <div v-if="filteredTemplates.length > 0" class="grid grid-cols-1 gap-16px lg:grid-cols-3 md:grid-cols-2">
              <ElCard
                v-for="template in filteredTemplates"
                :key="template.template_id"
                class="template-card cursor-pointer transition-all duration-200"
                :class="{
                  'border-primary shadow-lg': selectedTemplateIds.includes(template.template_id),
                  'hover:shadow-md': !selectedTemplateIds.includes(template.template_id)
                }"
                shadow="hover"
                @click="toggleTemplateSelection(template.template_id)"
              >
                <template #header>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center gap-8px">
                      <ElCheckbox
                        :model-value="selectedTemplateIds.includes(template.template_id)"
                        @click.stop
                        @change="(checked: any) => toggleTemplateSelection(template.template_id, checked as boolean)"
                      />
                      <span class="font-medium">{{ template.template_name }}</span>
                      <ElTag size="small" :type="getCategoryColor(template.template_category)">
                        {{ getCategoryLabel(template.template_category) }}
                      </ElTag>
                    </div>
                    <ElDropdown @command="(cmd: string) => handleTemplateAction(cmd, template)" @click.stop>
                      <ElButton size="small" text>
                        <icon-ic-round-more-vert />
                      </ElButton>
                      <template #dropdown>
                        <ElDropdownMenu>
                          <ElDropdownItem command="preview">
                            <icon-ic-round-visibility class="mr-8px" />
                            预览
                          </ElDropdownItem>
                          <ElDropdownItem command="apply">
                            <icon-ic-round-check class="mr-8px" />
                            应用
                          </ElDropdownItem>
                          <ElDropdownItem command="edit">
                            <icon-ic-round-edit class="mr-8px" />
                            编辑
                          </ElDropdownItem>
                          <ElDropdownItem command="duplicate">
                            <icon-ic-round-content-copy class="mr-8px" />
                            复制
                          </ElDropdownItem>
                          <ElDropdownItem command="export" divided>
                            <icon-ic-round-download class="mr-8px" />
                            导出
                          </ElDropdownItem>
                          <ElDropdownItem v-if="!template.is_system" command="delete" divided>
                            <icon-ic-round-delete class="mr-8px" />
                            删除
                          </ElDropdownItem>
                        </ElDropdownMenu>
                      </template>
                    </ElDropdown>
                  </div>
                </template>

                <div class="space-y-8px">
                  <p class="text-sm text-gray-600">{{ template.template_description }}</p>
                  <div class="flex items-center justify-between text-sm">
                    <span class="text-gray-500">{{ template.permission_count }} 个权限</span>
                    <div class="flex gap-4px">
                      <ElTag v-if="template.is_system" size="small" type="info">系统</ElTag>
                      <ElTag v-if="template.is_default" size="small" type="success">默认</ElTag>
                    </div>
                  </div>
                </div>
              </ElCard>
            </div>

            <!-- 空状态 -->
            <div v-else class="py-32px text-center text-gray-500">
              <icon-ic-round-settings class="mb-8px text-48px" />
              <div>暂无权限模板</div>
              <ElButton type="primary" class="mt-16px" @click="createTemplate">创建第一个模板</ElButton>
            </div>
          </div>
        </ElTabPane>

        <!-- 模板表单 -->
        <ElTabPane label="编辑模板" name="form">
          <div class="space-y-16px">
            <!-- 基本信息 -->
            <ElCard>
              <template #header>
                <span class="text-lg font-medium">基本信息</span>
              </template>
              <ElForm :model="templateForm" label-width="100px">
                <ElRow :gutter="16">
                  <ElCol :span="12">
                    <ElFormItem label="模板名称" required>
                      <ElInput v-model="templateForm.template_name" placeholder="请输入模板名称" />
                    </ElFormItem>
                  </ElCol>
                  <ElCol :span="12">
                    <ElFormItem label="模板分类">
                      <ElSelect v-model="templateForm.template_category" style="width: 100%">
                        <ElOption
                          v-for="category in templateCategories"
                          :key="category.value"
                          :label="category.label"
                          :value="category.value"
                        />
                      </ElSelect>
                    </ElFormItem>
                  </ElCol>
                </ElRow>
                <ElFormItem label="模板描述">
                  <ElInput
                    v-model="templateForm.template_description"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入模板描述"
                  />
                </ElFormItem>
                <ElRow :gutter="16">
                  <ElCol :span="12">
                    <ElFormItem label="排序">
                      <ElInputNumber v-model="templateForm.sort_order" :min="0" />
                    </ElFormItem>
                  </ElCol>
                  <ElCol :span="12">
                    <ElFormItem label="设置">
                      <ElCheckbox v-model="templateForm.is_default">设为默认模板</ElCheckbox>
                    </ElFormItem>
                  </ElCol>
                </ElRow>
              </ElForm>
            </ElCard>

            <!-- 权限配置 -->
            <ElCard>
              <template #header>
                <div class="flex items-center justify-between">
                  <span class="text-lg font-medium">权限配置</span>
                  <div class="text-sm text-gray-600">已选择 {{ selectedPermissions.length }} 个权限</div>
                </div>
              </template>
              <PermissionTree
                v-model="selectedPermissions"
                :data="permissionTree"
                show-checkbox
                check-strictly
                :default-expand-all="false"
              />
            </ElCard>
          </div>
        </ElTabPane>
      </ElTabs>
    </div>

    <template #footer>
      <div class="flex items-center justify-between">
        <div v-if="activeTab === 'form'" class="text-sm text-gray-600">
          {{ isEdit ? '编辑模板' : '新建模板' }}
        </div>
        <div v-else></div>

        <div class="flex gap-12px">
          <ElButton @click="closeDialog">关闭</ElButton>
          <ElButton v-if="activeTab === 'form'" @click="cancelEdit">取消</ElButton>
          <ElButton
            v-if="activeTab === 'form'"
            type="primary"
            :loading="saving"
            :disabled="!canSave"
            @click="saveTemplate"
          >
            {{ isEdit ? '更新模板' : '创建模板' }}
          </ElButton>
        </div>
      </div>
    </template>

    <!-- 模板预览对话框 -->
    <ElDialog
      v-model="selectedTemplate"
      :title="`模板预览 - ${selectedTemplate?.template_name}`"
      width="600px"
      append-to-body
    >
      <div v-if="selectedTemplate" class="space-y-16px">
        <div>
          <h4 class="text-md mb-8px font-medium">模板信息</h4>
          <ElDescriptions :column="2" border>
            <ElDescriptionsItem label="模板名称">
              {{ selectedTemplate.template_name }}
            </ElDescriptionsItem>
            <ElDescriptionsItem label="模板分类">
              <ElTag :type="getCategoryColor(selectedTemplate.template_category)">
                {{ getCategoryLabel(selectedTemplate.template_category) }}
              </ElTag>
            </ElDescriptionsItem>
            <ElDescriptionsItem label="权限数量">{{ selectedTemplate.permission_count }} 个</ElDescriptionsItem>
            <ElDescriptionsItem label="创建时间">
              {{ selectedTemplate.create_time }}
            </ElDescriptionsItem>
          </ElDescriptions>
        </div>

        <div v-if="selectedTemplate.permissions">
          <h4 class="text-md mb-8px font-medium">包含权限</h4>
          <div class="grid grid-cols-1 max-h-300px gap-8px overflow-y-auto">
            <div
              v-for="permission in selectedTemplate.permissions"
              :key="permission.permission_id"
              class="flex items-center gap-8px border border-gray-200 rounded-6px p-8px"
            >
              <ElTag size="small" :type="getPermissionTypeColor(permission.permission_type)">
                {{ getPermissionTypeText(permission.permission_type) }}
              </ElTag>
              <span class="flex-1">{{ permission.permission_name }}</span>
              <span class="text-sm text-gray-500">{{ permission.permission_code }}</span>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end gap-12px">
          <ElButton @click="selectedTemplate = null">关闭</ElButton>
          <ElButton type="primary" @click="applyTemplate(selectedTemplate)">应用模板</ElButton>
        </div>
      </template>
    </ElDialog>
  </ElDialog>

  <!-- 模板预览对话框 -->
  <ElDialog v-model="previewVisible" title="模板预览" width="600px" :close-on-click-modal="false">
    <div v-if="previewTemplate" class="space-y-16px">
      <!-- 模板基本信息 -->
      <ElCard>
        <template #header>
          <span class="font-medium">模板信息</span>
        </template>
        <div class="space-y-8px">
          <div class="flex items-center gap-8px">
            <span class="w-80px text-gray-600">模板名称:</span>
            <span class="font-medium">{{ previewTemplate.template_name }}</span>
            <ElTag size="small" :type="getCategoryColor(previewTemplate.template_category)">
              {{ getCategoryLabel(previewTemplate.template_category) }}
            </ElTag>
          </div>
          <div class="flex items-center gap-8px">
            <span class="w-80px text-gray-600">模板描述:</span>
            <span>{{ previewTemplate.template_description || '无描述' }}</span>
          </div>
          <div class="flex items-center gap-8px">
            <span class="w-80px text-gray-600">权限数量:</span>
            <span>{{ previewTemplate.permissions?.length || 0 }} 个权限</span>
          </div>
          <div class="flex items-center gap-8px">
            <span class="w-80px text-gray-600">模板类型:</span>
            <ElTag size="small" :type="previewTemplate.is_system ? 'info' : 'primary'">
              {{ previewTemplate.is_system ? '系统模板' : '用户模板' }}
            </ElTag>
            <ElTag v-if="previewTemplate.is_default" size="small" type="success">默认模板</ElTag>
          </div>
        </div>
      </ElCard>

      <!-- 权限列表 -->
      <ElCard>
        <template #header>
          <span class="font-medium">包含权限</span>
        </template>
        <div
          v-if="previewTemplate.permissions && previewTemplate.permissions.length > 0"
          class="max-h-300px overflow-y-auto space-y-8px"
        >
          <div
            v-for="permission in previewTemplate.permissions"
            :key="permission.permission_id"
            class="flex items-center justify-between border border-gray-200 rounded p-8px"
          >
            <div class="flex items-center gap-8px">
              <ElTag size="small" :type="getPermissionTypeColor(permission.permission_type)">
                {{ getPermissionTypeText(permission.permission_type) }}
              </ElTag>
              <span class="font-medium">{{ permission.permission_name }}</span>
            </div>
            <span class="text-sm text-gray-500">{{ permission.permission_code }}</span>
          </div>
        </div>
        <div v-else class="py-16px text-center text-gray-500">该模板暂无权限配置</div>
      </ElCard>
    </div>

    <template #footer>
      <div class="flex justify-between">
        <div>
          <ElButton v-if="previewTemplate" type="primary" @click="applyTemplate(previewTemplate)">应用此模板</ElButton>
        </div>
        <div>
          <ElButton @click="previewVisible = false">关闭</ElButton>
        </div>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.template-card {
  transition: all 0.3s ease;
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3 {
    grid-template-columns: 1fr;
  }
}
</style>
