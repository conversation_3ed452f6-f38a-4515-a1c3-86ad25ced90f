<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { request } from '@/service/request';

interface Props {
  visible: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'template-applied', templateId: number): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
const templates = ref<any[]>([]);
const templateForm = ref({
  template_id: 0,
  template_name: '',
  template_description: '',
  role_permissions: [] as number[],
  is_system: 0,
  is_default: 0
});
const templateModalVisible = ref(false);
const isEdit = ref(false);
const selectedTemplate = ref<any>(null);

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
});

// 预设模板
const presetTemplates = ref([
  {
    name: '超级管理员模板',
    description: '拥有系统所有权限的超级管理员角色模板',
    permissions: ['*'] // 表示所有权限
  },
  {
    name: '普通管理员模板',
    description: '拥有基础管理权限的管理员角色模板',
    permissions: ['user:read', 'user:write', 'order:read', 'order:write']
  },
  {
    name: '客服模板',
    description: '客服人员角色模板，主要处理订单和用户咨询',
    permissions: ['user:read', 'order:read', 'order:update']
  },
  {
    name: '财务模板',
    description: '财务人员角色模板，管理财务相关功能',
    permissions: ['finance:read', 'finance:write', 'report:read']
  }
]);

// 获取模板列表
async function fetchTemplates() {
  loading.value = true;
  try {
    const response = await request({
      url: '/api/role-template/list',
      method: 'get'
    });

    if ((response as any)?.list) {
      templates.value = (response as any).list;
    }
  } catch (error) {
    console.error('获取模板列表失败:', error);
  } finally {
    loading.value = false;
  }
}

// 创建/更新模板
async function saveTemplate() {
  try {
    const apiUrl = isEdit.value ? `/api/role-template/${templateForm.value.template_id}` : '/api/role-template';
    const method = isEdit.value ? 'put' : 'post';

    const response = await request({
      url: apiUrl,
      method,
      data: templateForm.value
    });

    if (response) {
      ElMessage.success(isEdit.value ? '模板更新成功' : '模板创建成功');
      templateModalVisible.value = false;
      fetchTemplates();
    }
  } catch (error) {
    ElMessage.error('保存模板失败');
  }
}

// 删除模板
async function deleteTemplate(templateId: number) {
  try {
    await ElMessageBox.confirm('确定要删除这个模板吗？', '确认删除', {
      type: 'warning'
    });

    const response = await request({
      url: `/api/role-template/${templateId}`,
      method: 'delete'
    });

    if (response) {
      ElMessage.success('模板删除成功');
      fetchTemplates();
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除模板失败');
    }
  }
}

// 应用模板
async function applyTemplate(template: any) {
  try {
    await ElMessageBox.confirm(`确定要应用模板"${template.template_name}"吗？这将创建一个新的角色。`, '确认应用', {
      type: 'info'
    });

    const response = await request({
      url: '/api/role-template/apply',
      method: 'post',
      data: {
        template_id: template.template_id,
        role_name: `${template.template_name}_${Date.now()}`,
        role_description: `基于模板"${template.template_name}"创建的角色`
      }
    });

    if (response) {
      ElMessage.success('模板应用成功');
      emit('template-applied', template.template_id);
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('应用模板失败');
    }
  }
}

// 导出模板
function exportTemplate(template: any) {
  const data = {
    template_name: template.template_name,
    template_description: template.template_description,
    permissions: template.role_permissions,
    export_time: new Date().toISOString()
  };

  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `role_template_${template.template_name}.json`;
  a.click();
  URL.revokeObjectURL(url);
}

// 导入模板
function importTemplate(event: Event) {
  const file = (event.target as HTMLInputElement).files?.[0];
  if (!file) return;

  const reader = new FileReader();
  reader.onload = e => {
    try {
      const data = JSON.parse(e.target?.result as string);
      templateForm.value = {
        template_id: 0,
        template_name: `${data.template_name}_导入`,
        template_description: data.template_description,
        role_permissions: data.permissions || [],
        is_system: 0,
        is_default: 0
      };
      isEdit.value = false;
      templateModalVisible.value = true;
    } catch (error) {
      ElMessage.error('导入文件格式错误');
    }
  };
  reader.readAsText(file);
}

// 打开模板编辑
function openTemplateEdit(template?: any) {
  if (template) {
    templateForm.value = { ...template };
    isEdit.value = true;
  } else {
    templateForm.value = {
      template_id: 0,
      template_name: '',
      template_description: '',
      role_permissions: [],
      is_system: 0,
      is_default: 0
    };
    isEdit.value = false;
  }
  templateModalVisible.value = true;
}

// 应用预设模板
async function applyPresetTemplate(preset: any) {
  try {
    await ElMessageBox.confirm(`确定要创建"${preset.name}"模板吗？`, '确认创建', { type: 'info' });

    templateForm.value = {
      template_id: 0,
      template_name: preset.name,
      template_description: preset.description,
      role_permissions: [], // 这里需要根据权限代码获取实际的权限ID
      is_system: 1,
      is_default: 0
    };

    await saveTemplate();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('创建模板失败');
    }
  }
}

// 生命周期
onMounted(() => {
  if (props.visible) {
    fetchTemplates();
  }
});

// 监听visible变化
watch(
  () => props.visible,
  newVisible => {
    if (newVisible) {
      fetchTemplates();
    }
  }
);
</script>

<template>
  <ElDialog v-model="dialogVisible" title="角色模板管理" width="1000px" :close-on-click-modal="false">
    <div v-loading="loading" class="space-y-16px">
      <!-- 操作栏 -->
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-12px">
          <ElButton type="primary" @click="openTemplateEdit()">
            <template #icon>
              <icon-ic-round-add class="text-icon" />
            </template>
            新建模板
          </ElButton>
          <ElButton @click="($refs.fileInput as HTMLInputElement).click()">
            <template #icon>
              <icon-ic-round-upload class="text-icon" />
            </template>
            导入模板
          </ElButton>
          <input ref="fileInput" type="file" accept=".json" style="display: none" @change="importTemplate" />
        </div>
        <ElTag type="info">共 {{ templates.length }} 个模板</ElTag>
      </div>

      <!-- 预设模板 -->
      <ElCard>
        <template #header>
          <span class="text-14px font-medium">预设模板</span>
        </template>
        <div class="grid grid-cols-1 gap-12px lg:grid-cols-4 md:grid-cols-2">
          <div
            v-for="preset in presetTemplates"
            :key="preset.name"
            class="cursor-pointer border border-gray-200 rounded-8px p-12px hover:border-blue-300"
            @click="applyPresetTemplate(preset)"
          >
            <div class="mb-4px text-14px font-medium">{{ preset.name }}</div>
            <div class="mb-8px text-12px text-gray-500">{{ preset.description }}</div>
            <ElTag size="small" type="primary">{{ preset.permissions.length }} 个权限</ElTag>
          </div>
        </div>
      </ElCard>

      <!-- 自定义模板 -->
      <ElCard>
        <template #header>
          <span class="text-14px font-medium">自定义模板</span>
        </template>
        <ElTable :data="templates" style="width: 100%">
          <ElTableColumn prop="template_name" label="模板名称" />
          <ElTableColumn prop="template_description" label="模板描述" show-overflow-tooltip />
          <ElTableColumn prop="role_permissions" label="权限数量" width="100">
            <template #default="{ row }">
              {{ Array.isArray(row.role_permissions) ? row.role_permissions.length : 0 }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="is_system" label="类型" width="80">
            <template #default="{ row }">
              <ElTag :type="row.is_system ? 'warning' : 'info'" size="small">
                {{ row.is_system ? '系统' : '自定义' }}
              </ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="create_time" label="创建时间" width="160">
            <template #default="{ row }">
              {{ new Date(row.create_time).toLocaleString('zh-CN') }}
            </template>
          </ElTableColumn>
          <ElTableColumn label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <div class="flex gap-4px">
                <ElButton size="small" type="success" @click="applyTemplate(row)">应用</ElButton>
                <ElButton size="small" type="primary" @click="openTemplateEdit(row)">编辑</ElButton>
                <ElButton size="small" type="info" @click="exportTemplate(row)">导出</ElButton>
                <ElButton v-if="!row.is_system" size="small" type="danger" @click="deleteTemplate(row.template_id)">
                  删除
                </ElButton>
              </div>
            </template>
          </ElTableColumn>
        </ElTable>
      </ElCard>
    </div>

    <template #footer>
      <div class="flex justify-end">
        <ElButton @click="dialogVisible = false">关闭</ElButton>
      </div>
    </template>

    <!-- 模板编辑弹窗 -->
    <ElDialog
      v-model="templateModalVisible"
      :title="isEdit ? '编辑模板' : '新建模板'"
      width="600px"
      :close-on-click-modal="false"
      append-to-body
    >
      <ElForm :model="templateForm" label-width="100px">
        <ElFormItem label="模板名称" required>
          <ElInput v-model="templateForm.template_name" placeholder="请输入模板名称" />
        </ElFormItem>
        <ElFormItem label="模板描述">
          <ElInput v-model="templateForm.template_description" type="textarea" :rows="3" placeholder="请输入模板描述" />
        </ElFormItem>
        <ElFormItem label="权限配置">
          <div class="text-12px text-gray-500">权限配置功能需要集成权限选择器组件</div>
        </ElFormItem>
        <ElFormItem label="模板类型">
          <ElCheckbox v-model="templateForm.is_system" :true-value="1" :false-value="0">系统模板</ElCheckbox>
        </ElFormItem>
        <ElFormItem label="默认模板">
          <ElCheckbox v-model="templateForm.is_default" :true-value="1" :false-value="0">设为默认模板</ElCheckbox>
        </ElFormItem>
      </ElForm>

      <template #footer>
        <div class="flex justify-end gap-12px">
          <ElButton @click="templateModalVisible = false">取消</ElButton>
          <ElButton type="primary" @click="saveTemplate">
            {{ isEdit ? '更新' : '创建' }}
          </ElButton>
        </div>
      </template>
    </ElDialog>
  </ElDialog>
</template>

<style scoped>
/* 移动端适配 */
@media (max-width: 768px) {
  .grid-cols-1.md\:grid-cols-2.lg\:grid-cols-4 {
    grid-template-columns: 1fr;
  }
}
</style>
