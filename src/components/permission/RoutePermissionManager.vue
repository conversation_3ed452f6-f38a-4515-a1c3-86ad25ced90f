<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { request } from '@/service/request';
import { useRouterPush } from '@/hooks/common/router';

defineOptions({
  name: 'RoutePermissionManager'
});

interface Props {
  visible: boolean;
  userId?: number;
  roleId?: number;
  mode?: 'user' | 'role';
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  mode: 'user'
});

const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
const saving = ref(false);
const searchKeyword = ref('');

// 路由权限数据
const routeTree = ref<any[]>([]);
const selectedRoutes = ref<string[]>([]);
const currentRoutePermissions = ref<any[]>([]);

// 路由权限配置
const routePermissionConfig = ref<any[]>([]);

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
});

const filteredRouteTree = computed(() => {
  if (!searchKeyword.value) return routeTree.value;

  return filterRouteTree(routeTree.value, searchKeyword.value);
});

const hasChanges = computed(() => {
  const currentRoutes = new Set(currentRoutePermissions.value.map(p => p.route_path));
  const selected = new Set(selectedRoutes.value);

  if (currentRoutes.size !== selected.size) return true;

  for (const route of currentRoutes) {
    if (!selected.has(route)) return true;
  }

  return false;
});

// 获取系统路由树
async function fetchRouteTree() {
  loading.value = true;
  try {
    const response = await request({
      url: '/api/permission/routes',
      method: 'get'
    });

    if (Array.isArray(response)) {
      routeTree.value = response;
    }
  } catch (error) {
    console.error('获取路由树失败:', error);
    ElMessage.error('获取路由树失败');
  } finally {
    loading.value = false;
  }
}

// 获取当前路由权限
async function fetchCurrentRoutePermissions() {
  if (!props.userId && !props.roleId) return;

  try {
    const url =
      props.mode === 'user'
        ? `/api/user/route-permissions/${props.userId}`
        : `/api/role/route-permissions/${props.roleId}`;

    const response = await request({
      url,
      method: 'get'
    });

    if (Array.isArray(response)) {
      currentRoutePermissions.value = response;
      selectedRoutes.value = response.map(p => p.route_path);
    }
  } catch (error) {
    console.error('获取路由权限失败:', error);
  }
}

// 过滤路由树
function filterRouteTree(tree: any[], keyword: string): any[] {
  const filtered = [];

  for (const node of tree) {
    const matchesKeyword =
      node.name?.toLowerCase().includes(keyword.toLowerCase()) ||
      node.path?.toLowerCase().includes(keyword.toLowerCase()) ||
      node.title?.toLowerCase().includes(keyword.toLowerCase());

    if (matchesKeyword) {
      filtered.push(node);
    } else if (node.children && node.children.length > 0) {
      const filteredChildren = filterRouteTree(node.children, keyword);
      if (filteredChildren.length > 0) {
        filtered.push({
          ...node,
          children: filteredChildren
        });
      }
    }
  }

  return filtered;
}

// 处理路由选择
function handleRouteCheck(route: any, checked: boolean) {
  const routePath = route.path;

  if (checked) {
    if (!selectedRoutes.value.includes(routePath)) {
      selectedRoutes.value.push(routePath);
    }

    // 自动选择父级路由
    if (route.parentPath && !selectedRoutes.value.includes(route.parentPath)) {
      selectedRoutes.value.push(route.parentPath);
    }
  } else {
    const index = selectedRoutes.value.indexOf(routePath);
    if (index > -1) {
      selectedRoutes.value.splice(index, 1);
    }

    // 取消选择子级路由
    const childRoutes = getAllChildRoutes(route);
    childRoutes.forEach(childPath => {
      const childIndex = selectedRoutes.value.indexOf(childPath);
      if (childIndex > -1) {
        selectedRoutes.value.splice(childIndex, 1);
      }
    });
  }
}

// 获取所有子路由
function getAllChildRoutes(route: any): string[] {
  const childRoutes = [];

  if (route.children && route.children.length > 0) {
    for (const child of route.children) {
      childRoutes.push(child.path);
      childRoutes.push(...getAllChildRoutes(child));
    }
  }

  return childRoutes;
}

// 批量操作
function handleBatchOperation(operation: string) {
  switch (operation) {
    case 'selectAll':
      selectedRoutes.value = getAllRoutePaths(routeTree.value);
      break;
    case 'selectNone':
      selectedRoutes.value = [];
      break;
    case 'selectMenu':
      selectedRoutes.value = getRoutePathsByType(routeTree.value, 'menu');
      break;
    case 'selectPage':
      selectedRoutes.value = getRoutePathsByType(routeTree.value, 'page');
      break;
  }
}

// 获取所有路由路径
function getAllRoutePaths(tree: any[]): string[] {
  const paths = [];

  for (const node of tree) {
    if (node.path) {
      paths.push(node.path);
    }
    if (node.children && node.children.length > 0) {
      paths.push(...getAllRoutePaths(node.children));
    }
  }

  return paths;
}

// 根据类型获取路由路径
function getRoutePathsByType(tree: any[], type: string): string[] {
  const paths = [];

  for (const node of tree) {
    if (node.type === type && node.path) {
      paths.push(node.path);
    }
    if (node.children && node.children.length > 0) {
      paths.push(...getRoutePathsByType(node.children, type));
    }
  }

  return paths;
}

// 保存路由权限
async function saveRoutePermissions() {
  saving.value = true;
  try {
    const data = {
      target_id: props.mode === 'user' ? props.userId : props.roleId,
      target_type: props.mode,
      route_paths: selectedRoutes.value
    };

    await request({
      url: '/api/permission/route-permissions',
      method: 'post',
      data
    });

    ElMessage.success('路由权限保存成功');
    await fetchCurrentRoutePermissions();
    emit('success');
  } catch (error) {
    console.error('保存路由权限失败:', error);
    ElMessage.error('保存路由权限失败');
  } finally {
    saving.value = false;
  }
}

// 重置权限
function resetPermissions() {
  selectedRoutes.value = currentRoutePermissions.value.map(p => p.route_path);
}

// 关闭对话框
function closeDialog() {
  if (hasChanges.value) {
    ElMessageBox.confirm('您有未保存的路由权限更改，确定要关闭吗？', '确认关闭', { type: 'warning' })
      .then(() => {
        emit('update:visible', false);
      })
      .catch(() => {
        // 用户取消关闭
      });
  } else {
    emit('update:visible', false);
  }
}

// 获取路由类型图标
function getRouteTypeIcon(type: string) {
  const iconMap: Record<string, string> = {
    menu: 'ic:round-folder',
    page: 'ic:round-description',
    button: 'ic:round-smart-button',
    link: 'ic:round-link'
  };
  return iconMap[type] || 'ic:round-circle';
}

// 获取路由类型颜色
function getRouteTypeColor(type: string): 'primary' | 'success' | 'info' | 'warning' | 'danger' {
  const colorMap: Record<string, 'primary' | 'success' | 'info' | 'warning' | 'danger'> = {
    menu: 'primary',
    page: 'success',
    button: 'warning',
    link: 'info'
  };
  return colorMap[type] || 'default';
}

// 初始化数据
async function initData() {
  loading.value = true;
  try {
    await Promise.all([fetchRouteTree(), fetchCurrentRoutePermissions()]);
  } finally {
    loading.value = false;
  }
}

// 监听对话框显示状态
watch(
  () => props.visible,
  visible => {
    if (visible) {
      initData();
    }
  }
);

// 监听目标ID变化
watch(
  () => [props.userId, props.roleId],
  () => {
    if (props.visible) {
      fetchCurrentRoutePermissions();
    }
  }
);
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="`路由权限管理 - ${props.mode === 'user' ? '用户' : '角色'}权限`"
    width="90%"
    :style="{ maxWidth: '1000px' }"
    top="5vh"
    :close-on-click-modal="false"
    destroy-on-close
    class="route-permission-dialog"
    @close="closeDialog"
  >
    <div v-loading="loading">
      <!-- 操作栏 -->
      <div class="mb-16px space-y-12px">
        <!-- 搜索框 -->
        <ElInput v-model="searchKeyword" placeholder="搜索路由名称或路径" clearable>
          <template #prefix>
            <icon-ic-round-search class="text-icon" />
          </template>
        </ElInput>

        <!-- 批量操作 -->
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-600">已选择 {{ selectedRoutes.length }} 个路由权限</div>
          <div class="flex gap-8px">
            <ElDropdown @command="handleBatchOperation">
              <ElButton size="small">
                批量操作
                <icon-ic-round-keyboard-arrow-down class="ml-4px" />
              </ElButton>
              <template #dropdown>
                <ElDropdownMenu>
                  <ElDropdownItem command="selectAll">全选</ElDropdownItem>
                  <ElDropdownItem command="selectNone">全不选</ElDropdownItem>
                  <ElDropdownItem command="selectMenu">选择菜单</ElDropdownItem>
                  <ElDropdownItem command="selectPage">选择页面</ElDropdownItem>
                </ElDropdownMenu>
              </template>
            </ElDropdown>
            <ElButton size="small" @click="resetPermissions">重置</ElButton>
          </div>
        </div>
      </div>

      <!-- 路由权限树 -->
      <div class="max-h-400px overflow-y-auto border border-gray-200 rounded-8px p-16px">
        <ElTree
          :data="filteredRouteTree"
          :props="{ children: 'children', label: 'title' }"
          show-checkbox
          node-key="path"
          :default-expand-all="false"
          :check-strictly="false"
          @check="handleRouteCheck"
        >
          <template #default="{ node, data }">
            <div class="w-full flex items-center gap-8px">
              <component
                :is="getRouteTypeIcon(data.type)"
                class="text-icon"
                :class="`text-${getRouteTypeColor(data.type)}`"
              />
              <span class="flex-1">{{ data.title || data.name }}</span>
              <ElTag size="small" :type="getRouteTypeColor(data.type)">
                {{ data.type }}
              </ElTag>
              <span class="text-xs text-gray-500">{{ data.path }}</span>
            </div>
          </template>
        </ElTree>
      </div>

      <!-- 权限预览 -->
      <div v-if="selectedRoutes.length > 0" class="mt-16px">
        <h4 class="text-md mb-8px font-medium">已选择的路由权限</h4>
        <div class="flex flex-wrap gap-8px">
          <ElTag
            v-for="route in selectedRoutes"
            :key="route"
            closable
            @close="handleRouteCheck({ path: route }, false)"
          >
            {{ route }}
          </ElTag>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-600">
          <span v-if="hasChanges" class="text-warning">
            <icon-ic-round-warning class="mr-4px" />
            有未保存的更改
          </span>
        </div>
        <div class="flex gap-12px">
          <ElButton @click="closeDialog">取消</ElButton>
          <ElButton type="primary" :loading="saving" :disabled="!hasChanges" @click="saveRoutePermissions">
            保存路由权限
          </ElButton>
        </div>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
/* 路由权限弹窗样式 */
:deep(.route-permission-dialog) {
  .el-dialog {
    margin: 5vh auto;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
  }

  .el-dialog__body {
    flex: 1;
    overflow: hidden;
    padding: 20px;
  }

  .el-dialog__footer {
    flex-shrink: 0;
    padding: 15px 20px;
    border-top: 1px solid #e4e7ed;
  }
}

/* 移动端适配 */
@media (max-width: 768px) {
  :deep(.route-permission-dialog) {
    .el-dialog {
      margin: 2vh auto;
      max-height: 96vh;
      width: 95% !important;
    }
  }
}

/* 路由树样式 */
:deep(.el-tree-node__content) {
  height: 36px;
  padding: 0 8px;
}

:deep(.el-tree-node__content:hover) {
  background-color: var(--el-color-primary-light-9);
}

/* 标签样式 */
.el-tag + .el-tag {
  margin-left: 8px;
}
</style>
