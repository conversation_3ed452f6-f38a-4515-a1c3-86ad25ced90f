<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { request } from '@/service/request';
// RoleSelector组件已删除 - 改为单角色模式
import PermissionTree from './PermissionTree.vue';
import UserPermissionPreview from './UserPermissionPreview.vue';

defineOptions({
  name: 'PermissionAssignWizard'
});

interface Props {
  visible: boolean;
  userId?: number;
  mode?: 'role' | 'permission' | 'template';
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
  (e: 'view-permissions', userId: number): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  mode: 'role'
});

const emit = defineEmits<Emits>();

// 响应式数据
const currentStep = ref(0);
const loading = ref(false);
const saving = ref(false);

// 步骤配置
const steps = [
  { title: '选择分配方式', description: '选择角色分配或直接权限分配' },
  { title: '配置权限', description: '选择要分配的角色或权限' },
  { title: '预览确认', description: '预览权限变更并确认' },
  { title: '完成', description: '权限分配完成' }
];

// 分配方式配置
const assignModeConfig = {
  role: {
    title: '角色分配',
    description: '通过分配角色来授予权限，推荐使用',
    icon: 'ic:round-group',
    color: 'primary',
    advantages: ['管理简单', '权限集中', '易于维护'],
    suitable: '适合标准化权限管理'
  },
  permission: {
    title: '直接权限',
    description: '直接分配具体权限，精确控制',
    icon: 'ic:round-security',
    color: 'warning',
    advantages: ['精确控制', '灵活配置', '特殊需求'],
    suitable: '适合特殊权限需求'
  },
  template: {
    title: '权限模板',
    description: '使用预设的权限模板，快速分配',
    icon: 'ic:round-template',
    color: 'success',
    advantages: ['快速分配', '标准化', '批量应用'],
    suitable: '适合批量权限分配'
  }
};

// 分配方式
const assignMode = ref<'role' | 'permission' | 'template'>('role');

// 角色分配相关
const selectedRoles = ref<number[]>([]);
const availableRoles = ref<any[]>([]);

// 权限分配相关
const selectedPermissions = ref<number[]>([]);
const permissionTree = ref<any[]>([]);

// 权限模板相关
const selectedTemplate = ref<number | null>(null);
const permissionTemplates = ref<any[]>([]);

// 用户信息
const userInfo = ref<any>({});
const currentPermissions = ref<any[]>([]);
const currentRoles = ref<any[]>([]);

// 权限变更预览
const permissionChanges = ref<{
  addedRoles: number[];
  removedRoles: number[];
  addedPermissions: number[];
  removedPermissions: number[];
}>({
  addedRoles: [],
  removedRoles: [],
  addedPermissions: [],
  removedPermissions: []
});

// 向导状态
const wizardConfig = ref({
  showAdvanced: false,
  autoNext: true,
  confirmChanges: true
});

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
});

const canNext = computed(() => {
  switch (currentStep.value) {
    case 0:
      return true; // 选择分配方式总是可以继续
    case 1:
      if (assignMode.value === 'role') {
        return selectedRoles.value.length > 0;
      } else if (assignMode.value === 'permission') {
        return selectedPermissions.value.length > 0;
      } else if (assignMode.value === 'template') {
        return selectedTemplate.value !== null;
      }
      return false;
    case 2:
      return true; // 预览步骤总是可以继续
    default:
      return false;
  }
});

const isLastStep = computed(() => currentStep.value === steps.length - 1);

// 当前分配方式的配置
const currentModeConfig = computed(() => {
  return assignModeConfig[assignMode.value];
});

// 选择的角色信息
const selectedRoleInfo = computed(() => {
  return availableRoles.value.filter(role => selectedRoles.value.includes(role.role_id));
});

// 选择的权限信息
const selectedPermissionInfo = computed(() => {
  const flatPermissions = flattenPermissionTree(permissionTree.value);
  return flatPermissions.filter(permission => selectedPermissions.value.includes(permission.permission_id));
});

// 选择的模板信息
const selectedTemplateInfo = computed(() => {
  return permissionTemplates.value.find(template => template.id === selectedTemplate.value);
});

// 权限变更统计
const changeStats = computed(() => {
  const stats = {
    totalChanges: 0,
    addedRoles: selectedRoles.value.length,
    addedPermissions: selectedPermissions.value.length,
    appliedTemplate: selectedTemplate.value ? 1 : 0
  };

  stats.totalChanges = stats.addedRoles + stats.addedPermissions + stats.appliedTemplate;
  return stats;
});

// 工具函数
function flattenPermissionTree(tree: any[]): any[] {
  const result: any[] = [];

  function traverse(nodes: any[]) {
    for (const node of nodes) {
      if (node.permission_id) {
        result.push(node);
      }
      if (node.children && node.children.length > 0) {
        traverse(node.children);
      }
    }
  }

  traverse(tree);
  return result;
}

// 计算权限变更
function calculatePermissionChanges() {
  const changes: {
    addedRoles: number[];
    removedRoles: number[];
    addedPermissions: number[];
    removedPermissions: number[];
  } = {
    addedRoles: [],
    removedRoles: [],
    addedPermissions: [],
    removedPermissions: []
  };

  // 计算角色变更
  if (assignMode.value === 'role') {
    const currentRoleIds = currentRoles.value.map(role => role.role_id);
    changes.addedRoles = selectedRoles.value.filter(roleId => !currentRoleIds.includes(roleId));
  }

  // 计算权限变更
  if (assignMode.value === 'permission') {
    const currentPermissionIds = currentPermissions.value.map(perm => perm.permission_id);
    changes.addedPermissions = selectedPermissions.value.filter(permId => !currentPermissionIds.includes(permId));
  }

  permissionChanges.value = changes;
}

// 获取用户信息
async function fetchUserInfo() {
  if (!props.userId) return;

  try {
    const response = await request({
      url: `/api/user/${props.userId}`,
      method: 'get'
    });

    if (response) {
      userInfo.value = response;
    }
  } catch (error) {
    console.error('获取用户信息失败:', error);
  }
}

// 获取用户当前权限
async function fetchCurrentPermissions() {
  if (!props.userId) return;

  try {
    const response = await request({
      url: `/api/user/permissions/${props.userId}`,
      method: 'get'
    });

    if (Array.isArray(response)) {
      currentPermissions.value = response;
    }
  } catch (error) {
    console.error('获取用户权限失败:', error);
  }
}

// 获取可用角色
async function fetchAvailableRoles() {
  try {
    const response = await request({
      url: '/api/role/list',
      method: 'get',
      params: { limit: 100 }
    });

    if (response?.list) {
      availableRoles.value = response.list;
    }
  } catch (error) {
    console.error('获取角色列表失败:', error);
  }
}

// 获取权限树
async function fetchPermissionTree() {
  try {
    const response = await request({
      url: '/api/permission/tree',
      method: 'get'
    });

    if (Array.isArray(response)) {
      permissionTree.value = response;
    }
  } catch (error) {
    console.error('获取权限树失败:', error);
  }
}

// 获取权限模板
async function fetchPermissionTemplates() {
  try {
    const response = await request({
      url: '/api/permission/templates',
      method: 'get'
    });

    if (Array.isArray(response)) {
      permissionTemplates.value = response;
    }
  } catch (error) {
    console.error('获取权限模板失败:', error);
  }
}

// 下一步
function nextStep() {
  if (canNext.value && currentStep.value < steps.length - 1) {
    currentStep.value++;
  }
}

// 上一步
function prevStep() {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
}

// 完成分配
async function finishAssign() {
  saving.value = true;
  try {
    if (assignMode.value === 'role') {
      // 角色分配
      for (const roleId of selectedRoles.value) {
        await request({
          url: '/api/user-role/assign',
          method: 'post',
          data: {
            user_id: props.userId,
            role_id: roleId,
            grant_type: 'direct'
          }
        });
      }
      ElMessage.success(`成功分配 ${selectedRoles.value.length} 个角色`);
    } else if (assignMode.value === 'permission') {
      // 直接权限分配
      for (const permissionId of selectedPermissions.value) {
        await request({
          url: '/api/user-permission/assign',
          method: 'post',
          data: {
            user_id: props.userId,
            permission_id: permissionId,
            grant_type: 'grant'
          }
        });
      }
      ElMessage.success(`成功分配 ${selectedPermissions.value.length} 个权限`);
    } else if (assignMode.value === 'template') {
      // 权限模板分配
      await request({
        url: '/api/permission/apply-template',
        method: 'post',
        data: {
          user_id: props.userId,
          template_id: selectedTemplate.value
        }
      });
      ElMessage.success('权限模板应用成功');
    }

    currentStep.value = steps.length - 1; // 跳转到完成步骤
    emit('success');
  } catch (error) {
    console.error('权限分配失败:', error);
    ElMessage.error('权限分配失败');
  } finally {
    saving.value = false;
  }
}

// 重置向导
function resetWizard() {
  currentStep.value = 0;
  assignMode.value = 'role';
  selectedRoles.value = [];
  selectedPermissions.value = [];
  selectedTemplate.value = null;
}

// 关闭对话框
function closeDialog() {
  resetWizard();
  emit('update:visible', false);
}

// 查看用户权限
function handleViewUserPermissions() {
  closeDialog();
  // 这里可以触发查看用户权限的事件
  if (props.userId) {
    emit('view-permissions', props.userId);
  }
}

// 继续分配权限
function handleAssignMore() {
  resetWizard();
  // 重新开始向导流程
}

// 监听对话框显示状态
watch(
  () => props.visible,
  async visible => {
    if (visible && props.userId) {
      loading.value = true;
      try {
        await Promise.all([
          fetchUserInfo(),
          fetchCurrentPermissions(),
          fetchAvailableRoles(),
          fetchPermissionTree(),
          fetchPermissionTemplates()
        ]);
      } finally {
        loading.value = false;
      }
    }
  }
);

// 监听模式变化
watch(
  () => props.mode,
  mode => {
    if (mode) {
      assignMode.value = mode;
    }
  }
);

// 工具函数
function getPermissionTypeStats(permissions: any[]) {
  const stats: Record<string, number> = {};
  permissions.forEach(permission => {
    const type = permission.permission_type || 'other';
    stats[type] = (stats[type] || 0) + 1;
  });
  return stats;
}

function getPermissionTypeText(type: string): string {
  const typeMap: Record<string, string> = {
    menu: '菜单',
    button: '按钮',
    api: 'API',
    data: '数据',
    other: '其他'
  };
  return typeMap[type] || type;
}

function formatDate(dateString: string): string {
  if (!dateString) return '-';
  return new Date(dateString).toLocaleDateString('zh-CN');
}

function getPermissionTagType(type: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    menu: 'primary',
    button: 'success',
    api: 'warning',
    data: 'danger',
    other: 'info'
  };
  return typeMap[type] || 'info';
}
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    title="权限分配向导"
    width="800px"
    :close-on-click-modal="false"
    destroy-on-close
    @close="closeDialog"
  >
    <div v-loading="loading">
      <!-- 步骤指示器 -->
      <ElSteps :active="currentStep" align-center class="mb-24px">
        <ElStep v-for="(step, index) in steps" :key="index" :title="step.title" :description="step.description" />
      </ElSteps>

      <!-- 步骤内容 -->
      <div class="min-h-300px">
        <!-- 步骤1: 选择分配方式 -->
        <div v-if="currentStep === 0" class="space-y-24px">
          <!-- 用户信息展示 -->
          <div class="rounded-lg from-blue-50 to-purple-50 bg-gradient-to-r p-16px text-center">
            <div class="mb-8px flex items-center justify-center gap-12px">
              <ElAvatar :size="40" :src="userInfo.avatar">
                {{ userInfo.username?.charAt(0)?.toUpperCase() }}
              </ElAvatar>
              <div>
                <h3 class="text-lg font-medium">为用户 "{{ userInfo.username }}" 分配权限</h3>
                <p class="text-sm text-gray-600">{{ userInfo.email || '未设置邮箱' }}</p>
              </div>
            </div>
            <div class="flex items-center justify-center gap-16px text-sm text-gray-600">
              <div class="flex items-center gap-4px">
                <icon-ic-round-group class="text-blue-500" />
                <span>当前角色: {{ currentRoles.length }} 个</span>
              </div>
              <div class="flex items-center gap-4px">
                <icon-ic-round-security class="text-green-500" />
                <span>当前权限: {{ currentPermissions.length }} 个</span>
              </div>
            </div>
          </div>

          <!-- 分配方式选择 -->
          <div>
            <h4 class="mb-16px text-center text-lg font-medium">请选择权限分配方式</h4>
            <div class="grid grid-cols-1 gap-16px md:grid-cols-3">
              <ElCard
                v-for="(config, mode) in assignModeConfig"
                :key="mode"
                class="assign-mode-card cursor-pointer transition-all duration-200"
                :class="{
                  'selected border-primary shadow-lg': assignMode === mode,
                  'hover:shadow-md hover:border-gray-300': assignMode !== mode
                }"
                @click="assignMode = mode"
              >
                <div class="text-center space-y-12px">
                  <!-- 图标和标题 -->
                  <div>
                    <component :is="`icon-${config.icon}`" :class="`text-48px text-${config.color} mb-8px`" />
                    <h4 class="text-lg font-medium">{{ config.title }}</h4>
                    <p class="text-sm text-gray-600">{{ config.description }}</p>
                  </div>

                  <!-- 优势标签 -->
                  <div class="flex flex-wrap justify-center gap-4px">
                    <ElTag
                      v-for="advantage in config.advantages"
                      :key="advantage"
                      size="small"
                      :type="config.color as 'primary' | 'success' | 'warning' | 'danger' | 'info'"
                      effect="plain"
                    >
                      {{ advantage }}
                    </ElTag>
                  </div>

                  <!-- 适用场景 -->
                  <div class="rounded bg-gray-50 p-8px text-xs text-gray-500">
                    {{ config.suitable }}
                  </div>

                  <!-- 选中指示器 -->
                  <div v-if="assignMode === mode" class="flex items-center justify-center">
                    <ElTag type="primary" size="small">
                      <icon-ic-round-check class="mr-4px" />
                      已选择
                    </ElTag>
                  </div>
                </div>
              </ElCard>
            </div>
          </div>

          <!-- 选择说明 -->
          <div v-if="assignMode" class="rounded-lg bg-blue-50 p-16px">
            <div class="flex items-start gap-12px">
              <icon-ic-round-info class="mt-2px text-20px text-blue-500" />
              <div>
                <h5 class="mb-4px text-blue-700 font-medium">{{ currentModeConfig.title }} 说明</h5>
                <p class="mb-8px text-sm text-blue-600">{{ currentModeConfig.description }}</p>
                <div class="text-xs text-blue-500">
                  <strong>适用场景：</strong>
                  {{ currentModeConfig.suitable }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤2: 配置权限 -->
        <div v-else-if="currentStep === 1" class="space-y-20px">
          <!-- 步骤标题和说明 -->
          <div class="text-center">
            <h3 class="mb-8px text-lg font-medium">配置 {{ currentModeConfig.title }}</h3>
            <p class="text-gray-600">{{ currentModeConfig.description }}</p>
          </div>

          <!-- 角色分配 -->
          <div v-if="assignMode === 'role'" class="space-y-16px">
            <div class="flex items-center justify-between">
              <h4 class="text-md font-medium">选择要分配的角色</h4>
              <ElTag v-if="selectedRoles.length > 0" type="primary">已选择 {{ selectedRoles.length }} 个角色</ElTag>
            </div>

            <RoleSelector v-model="selectedRoles" :multiple="true" placeholder="请选择要分配的角色" />

            <!-- 选中角色预览 -->
            <div v-if="selectedRoleInfo.length > 0" class="rounded-lg bg-blue-50 p-16px">
              <h5 class="mb-12px text-blue-700 font-medium">已选择的角色</h5>
              <div class="grid grid-cols-1 gap-8px md:grid-cols-2">
                <div
                  v-for="role in selectedRoleInfo"
                  :key="role.role_id"
                  class="flex items-center gap-8px border rounded bg-white p-8px"
                >
                  <ElTag :type="role.is_system ? 'danger' : 'primary'" size="small">
                    {{ role.role_name }}
                  </ElTag>
                  <span class="flex-1 text-sm text-gray-600">{{ role.role_description }}</span>
                  <ElButton
                    size="small"
                    type="danger"
                    link
                    @click="selectedRoles = selectedRoles.filter(id => id !== role.role_id)"
                  >
                    移除
                  </ElButton>
                </div>
              </div>
            </div>
          </div>

          <!-- 直接权限分配 -->
          <div v-else-if="assignMode === 'permission'" class="space-y-16px">
            <div class="flex items-center justify-between">
              <h4 class="text-md font-medium">选择要分配的权限</h4>
              <ElTag v-if="selectedPermissions.length > 0" type="warning">
                已选择 {{ selectedPermissions.length }} 个权限
              </ElTag>
            </div>

            <PermissionTree v-model="selectedPermissions" :data="permissionTree" show-checkbox check-strictly />

            <!-- 选中权限统计 -->
            <div v-if="selectedPermissionInfo.length > 0" class="rounded-lg bg-orange-50 p-16px">
              <h5 class="mb-12px text-orange-700 font-medium">权限分布统计</h5>
              <div class="grid grid-cols-2 gap-12px md:grid-cols-4">
                <div
                  v-for="(count, type) in getPermissionTypeStats(selectedPermissionInfo)"
                  :key="type"
                  class="rounded bg-white p-8px text-center"
                >
                  <div class="text-lg text-orange-600 font-bold">{{ count }}</div>
                  <div class="text-xs text-gray-600">{{ getPermissionTypeText(type) }}</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 权限模板分配 -->
          <div v-else-if="assignMode === 'template'" class="space-y-16px">
            <div class="flex items-center justify-between">
              <h4 class="text-md font-medium">选择权限模板</h4>
              <ElTag v-if="selectedTemplate" type="success">已选择模板</ElTag>
            </div>

            <div class="grid grid-cols-1 gap-16px md:grid-cols-2">
              <ElCard
                v-for="template in permissionTemplates"
                :key="template.id"
                class="template-card cursor-pointer transition-all duration-200"
                :class="{
                  'selected border-success shadow-lg': selectedTemplate === template.id,
                  'hover:shadow-md hover:border-gray-300': selectedTemplate !== template.id
                }"
                @click="selectedTemplate = template.id"
              >
                <div class="space-y-12px">
                  <div class="flex items-start justify-between">
                    <div class="flex-1">
                      <h5 class="mb-4px text-lg font-medium">{{ template.name }}</h5>
                      <p class="mb-8px text-sm text-gray-600">{{ template.description }}</p>
                    </div>
                    <div v-if="selectedTemplate === template.id" class="ml-8px">
                      <ElTag type="success" size="small">
                        <icon-ic-round-check class="mr-4px" />
                        已选择
                      </ElTag>
                    </div>
                  </div>

                  <div class="flex items-center gap-8px">
                    <ElTag size="small" type="info">{{ template.permission_count }} 个权限</ElTag>
                    <ElTag v-if="template.category" size="small">
                      {{ template.category }}
                    </ElTag>
                    <ElTag v-if="template.is_system" size="small" type="warning">系统模板</ElTag>
                  </div>

                  <div class="text-xs text-gray-500">创建时间: {{ formatDate(template.create_time) }}</div>
                </div>
              </ElCard>
            </div>

            <!-- 选中模板详情 -->
            <div v-if="selectedTemplateInfo" class="rounded-lg bg-green-50 p-16px">
              <h5 class="mb-12px text-green-700 font-medium">模板详情</h5>
              <div class="grid grid-cols-1 gap-16px md:grid-cols-2">
                <div>
                  <div class="text-sm space-y-4px">
                    <div>
                      <strong>模板名称:</strong>
                      {{ selectedTemplateInfo.name }}
                    </div>
                    <div>
                      <strong>权限数量:</strong>
                      {{ selectedTemplateInfo.permission_count }} 个
                    </div>
                    <div>
                      <strong>模板分类:</strong>
                      {{ selectedTemplateInfo.category || '未分类' }}
                    </div>
                    <div>
                      <strong>创建时间:</strong>
                      {{ formatDate(selectedTemplateInfo.create_time) }}
                    </div>
                  </div>
                </div>
                <div>
                  <div class="text-sm">
                    <strong>模板描述:</strong>
                    <p class="mt-4px text-gray-600">{{ selectedTemplateInfo.description }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤3: 预览确认 -->
        <div v-else-if="currentStep === 2" class="space-y-20px">
          <!-- 标题和说明 -->
          <div class="text-center">
            <h3 class="mb-8px text-lg font-medium">权限变更预览</h3>
            <p class="text-gray-600">请仔细确认以下权限变更，确认无误后点击"完成分配"按钮</p>
          </div>

          <!-- 变更统计 -->
          <div class="rounded-lg from-blue-50 to-purple-50 bg-gradient-to-r p-16px">
            <h4 class="mb-12px text-gray-700 font-medium">本次变更统计</h4>
            <div class="grid grid-cols-2 gap-12px md:grid-cols-4">
              <div class="rounded bg-white p-12px text-center shadow-sm">
                <div class="text-2xl text-blue-600 font-bold">{{ changeStats.totalChanges }}</div>
                <div class="text-sm text-gray-600">总变更数</div>
              </div>
              <div v-if="assignMode === 'role'" class="rounded bg-white p-12px text-center shadow-sm">
                <div class="text-2xl text-green-600 font-bold">{{ changeStats.addedRoles }}</div>
                <div class="text-sm text-gray-600">新增角色</div>
              </div>
              <div v-if="assignMode === 'permission'" class="rounded bg-white p-12px text-center shadow-sm">
                <div class="text-2xl text-orange-600 font-bold">{{ changeStats.addedPermissions }}</div>
                <div class="text-sm text-gray-600">新增权限</div>
              </div>
              <div v-if="assignMode === 'template'" class="rounded bg-white p-12px text-center shadow-sm">
                <div class="text-2xl text-purple-600 font-bold">{{ changeStats.appliedTemplate }}</div>
                <div class="text-sm text-gray-600">应用模板</div>
              </div>
            </div>
          </div>

          <!-- 具体变更内容 -->
          <div class="space-y-16px">
            <!-- 角色变更 -->
            <div v-if="assignMode === 'role' && selectedRoleInfo.length > 0">
              <h4 class="mb-12px flex items-center gap-8px font-medium">
                <icon-ic-round-group class="text-green-500" />
                将要分配的角色 ({{ selectedRoleInfo.length }} 个)
              </h4>
              <div class="grid grid-cols-1 gap-8px md:grid-cols-2">
                <div
                  v-for="role in selectedRoleInfo"
                  :key="role.role_id"
                  class="flex items-center gap-12px border border-green-200 rounded bg-green-50 p-12px"
                >
                  <icon-ic-round-add-circle class="text-green-500" />
                  <div class="flex-1">
                    <div class="font-medium">{{ role.role_name }}</div>
                    <div class="text-sm text-gray-600">{{ role.role_description }}</div>
                  </div>
                  <ElTag :type="role.is_system ? 'danger' : 'success'" size="small">
                    {{ role.is_system ? '系统角色' : '自定义角色' }}
                  </ElTag>
                </div>
              </div>
            </div>

            <!-- 权限变更 -->
            <div v-if="assignMode === 'permission' && selectedPermissionInfo.length > 0">
              <h4 class="mb-12px flex items-center gap-8px font-medium">
                <icon-ic-round-security class="text-orange-500" />
                将要分配的权限 ({{ selectedPermissionInfo.length }} 个)
              </h4>
              <div class="max-h-300px overflow-y-auto">
                <div class="grid grid-cols-1 gap-4px">
                  <div
                    v-for="permission in selectedPermissionInfo"
                    :key="permission.permission_id"
                    class="flex items-center gap-12px border border-orange-200 rounded bg-orange-50 p-8px"
                  >
                    <icon-ic-round-add-circle class="text-orange-500" />
                    <ElTag size="small" :type="getPermissionTagType(permission.permission_type)">
                      {{ getPermissionTypeText(permission.permission_type) }}
                    </ElTag>
                    <div class="flex-1">
                      <div class="text-sm font-medium">{{ permission.permission_name }}</div>
                      <div class="text-xs text-gray-500">{{ permission.permission_code }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 模板变更 -->
            <div v-if="assignMode === 'template' && selectedTemplateInfo">
              <h4 class="mb-12px flex items-center gap-8px font-medium">
                <icon-ic-round-settings class="text-purple-500" />
                将要应用的权限模板
              </h4>
              <div class="border border-purple-200 rounded bg-purple-50 p-16px">
                <div class="flex items-start gap-16px">
                  <icon-ic-round-add-circle class="mt-4px text-24px text-purple-500" />
                  <div class="flex-1">
                    <h5 class="mb-4px text-lg font-medium">{{ selectedTemplateInfo.name }}</h5>
                    <p class="mb-8px text-gray-600">{{ selectedTemplateInfo.description }}</p>
                    <div class="flex items-center gap-8px">
                      <ElTag type="info" size="small">{{ selectedTemplateInfo.permission_count }} 个权限</ElTag>
                      <ElTag v-if="selectedTemplateInfo.category" size="small">
                        {{ selectedTemplateInfo.category }}
                      </ElTag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 当前权限预览 -->
          <div>
            <h4 class="mb-12px flex items-center gap-8px font-medium">
              <icon-ic-round-visibility class="text-blue-500" />
              用户当前权限概览
            </h4>
            <UserPermissionPreview
              v-if="props.userId"
              :user-id="props.userId"
              :show-header="false"
              :show-stats="true"
              group-by="source"
            />
          </div>

          <!-- 确认提示 -->
          <div class="border border-yellow-200 rounded-lg bg-yellow-50 p-16px">
            <div class="flex items-start gap-12px">
              <icon-ic-round-warning class="mt-2px text-20px text-yellow-500" />
              <div>
                <h5 class="mb-4px text-yellow-700 font-medium">重要提示</h5>
                <ul class="text-sm text-yellow-600 space-y-2px">
                  <li>• 权限分配后将立即生效，用户可以立即使用新权限</li>
                  <li>• 角色权限会自动继承，直接权限优先级更高</li>
                  <li>• 建议定期审查用户权限，确保权限分配合理</li>
                  <li>• 所有权限变更都会记录在系统日志中</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        <!-- 步骤4: 完成 -->
        <div v-else-if="currentStep === 3" class="text-center space-y-24px">
          <!-- 成功图标和标题 -->
          <div class="space-y-16px">
            <div class="relative">
              <icon-ic-round-check-circle class="mx-auto text-80px text-success" />
              <div class="absolute -right-4px -top-4px">
                <div class="h-6 w-6 flex items-center justify-center rounded-full bg-success">
                  <icon-ic-round-check class="text-14px text-white" />
                </div>
              </div>
            </div>
            <div>
              <h3 class="mb-8px text-xl text-success font-bold">权限分配完成！</h3>
              <p class="text-gray-600">用户 "{{ userInfo.username }}" 的权限已成功更新</p>
            </div>
          </div>

          <!-- 分配结果统计 -->
          <div class="rounded-lg bg-green-50 p-20px">
            <h4 class="mb-16px text-green-700 font-medium">分配结果</h4>
            <div class="grid grid-cols-1 gap-16px md:grid-cols-3">
              <div v-if="assignMode === 'role'" class="text-center">
                <div class="text-2xl text-green-600 font-bold">{{ selectedRoles.length }}</div>
                <div class="text-sm text-gray-600">个角色已分配</div>
              </div>
              <div v-if="assignMode === 'permission'" class="text-center">
                <div class="text-2xl text-green-600 font-bold">{{ selectedPermissions.length }}</div>
                <div class="text-sm text-gray-600">个权限已分配</div>
              </div>
              <div v-if="assignMode === 'template'" class="text-center">
                <div class="text-2xl text-green-600 font-bold">1</div>
                <div class="text-sm text-gray-600">个模板已应用</div>
              </div>
            </div>
          </div>

          <!-- 后续操作建议 -->
          <div class="rounded-lg bg-blue-50 p-16px">
            <h5 class="mb-8px text-blue-700 font-medium">建议后续操作</h5>
            <div class="text-sm text-blue-600 space-y-4px">
              <div>• 通知用户权限已更新，可以使用新功能</div>
              <div>• 定期检查用户权限使用情况</div>
              <div>• 如需调整权限，可以重新打开权限分配向导</div>
            </div>
          </div>

          <!-- 快速操作按钮 -->
          <div class="flex justify-center gap-12px">
            <ElButton type="primary" @click="handleViewUserPermissions">
              <template #icon>
                <icon-ic-round-visibility />
              </template>
              查看用户权限
            </ElButton>
            <ElButton @click="handleAssignMore">
              <template #icon>
                <icon-ic-round-add />
              </template>
              继续分配权限
            </ElButton>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-between">
        <ElButton v-if="currentStep > 0 && currentStep < 3" @click="prevStep">上一步</ElButton>
        <div v-else></div>

        <div class="flex gap-12px">
          <ElButton @click="closeDialog">
            {{ currentStep === 3 ? '关闭' : '取消' }}
          </ElButton>
          <ElButton v-if="currentStep < 2" type="primary" :disabled="!canNext" @click="nextStep">下一步</ElButton>
          <ElButton v-else-if="currentStep === 2" type="primary" :loading="saving" @click="finishAssign">
            完成分配
          </ElButton>
        </div>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.assign-mode-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.assign-mode-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.assign-mode-card.selected {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}

.template-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.template-card.selected {
  border-color: var(--el-color-success);
  background-color: var(--el-color-success-light-9);
}

.min-h-300px {
  min-height: 300px;
}
</style>
