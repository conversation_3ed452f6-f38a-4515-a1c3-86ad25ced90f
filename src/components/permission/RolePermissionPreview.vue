<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { request } from '@/service/request';

interface Props {
  roleId?: number;
  roleName?: string;
  visible: boolean;
  compareRoleId?: number; // 用于权限对比
  showAnalysis?: boolean; // 是否显示权限分析
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
}

const props = withDefaults(defineProps<Props>(), {
  roleId: 0,
  roleName: '',
  visible: false,
  compareRoleId: 0,
  showAnalysis: true
});

const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
const roleInfo = ref<any>({});
const rolePermissions = ref<any[]>([]);
const compareRoleInfo = ref<any>({});
const compareRolePermissions = ref<any[]>([]);
const activeTab = ref('permissions');

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
});

// 按分组整理权限
const groupedPermissions = computed(() => {
  const groups: Record<string, any[]> = {};

  rolePermissions.value.forEach(permission => {
    const groupName = getGroupDisplayName(permission.permission_group);
    if (!groups[groupName]) {
      groups[groupName] = [];
    }
    groups[groupName].push(permission);
  });

  return Object.entries(groups).map(([name, permissions]) => ({
    name,
    permissions: permissions.sort((a, b) => a.sort_order - b.sort_order)
  }));
});

// 权限统计分析
const permissionAnalysis = computed(() => {
  const total = rolePermissions.value.length;
  const byType = rolePermissions.value.reduce(
    (acc, perm) => {
      acc[perm.permission_type] = (acc[perm.permission_type] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );

  const byGroup = rolePermissions.value.reduce(
    (acc, perm) => {
      const group = getGroupDisplayName(perm.permission_group);
      acc[group] = (acc[group] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );

  return {
    total,
    byType,
    byGroup,
    coverage: total > 0 ? Math.round((total / 100) * 100) : 0 // 假设总权限数为100
  };
});

// 权限对比分析
const permissionComparison = computed(() => {
  if (!props.compareRoleId || compareRolePermissions.value.length === 0) {
    return null;
  }

  const currentPermIds = new Set(rolePermissions.value.map(p => p.permission_id));
  const comparePermIds = new Set(compareRolePermissions.value.map(p => p.permission_id));

  const onlyInCurrent = rolePermissions.value.filter(p => !comparePermIds.has(p.permission_id));
  const onlyInCompare = compareRolePermissions.value.filter(p => !currentPermIds.has(p.permission_id));
  const common = rolePermissions.value.filter(p => comparePermIds.has(p.permission_id));

  return {
    onlyInCurrent,
    onlyInCompare,
    common,
    similarity: Math.round((common.length / Math.max(currentPermIds.size, comparePermIds.size)) * 100)
  };
});

// 权限冲突检测
const permissionConflicts = computed(() => {
  const conflicts: any[] = [];

  // 检测同一资源的冲突权限（允许和拒绝）
  const resourceGroups = rolePermissions.value.reduce(
    (acc, perm) => {
      const key = `${perm.permission_group}_${perm.resource_type}`;
      if (!acc[key]) acc[key] = [];
      acc[key].push(perm);
      return acc;
    },
    {} as Record<string, any[]>
  );

  Object.entries(resourceGroups).forEach(([resource, permissions]) => {
    const grants = (permissions as any[]).filter((p: any) => p.grant_type === 'grant');
    const denies = (permissions as any[]).filter((p: any) => p.grant_type === 'deny');

    if (grants.length > 0 && denies.length > 0) {
      conflicts.push({
        type: 'grant_deny_conflict',
        resource,
        message: `${resource} 同时存在允许和拒绝权限`,
        permissions
      });
    }
  });

  return conflicts;
});

// 获取角色权限详情
async function fetchRolePermissions() {
  if (!props.roleId) return;

  loading.value = true;
  try {
    // 获取角色基本信息
    const roleResponse = await request({
      url: `/api/role/${props.roleId}`,
      method: 'get'
    });

    if (roleResponse) {
      roleInfo.value = roleResponse;
    }

    // 获取角色权限列表
    const permissionsResponse = await request({
      url: `/api/role-permission/list/${props.roleId}`,
      method: 'get'
    });

    if (Array.isArray(permissionsResponse)) {
      rolePermissions.value = permissionsResponse;
    }
  } catch (error) {
    console.error('获取角色权限失败:', error);
  } finally {
    loading.value = false;
  }
}

// 获取对比角色权限
async function fetchCompareRolePermissions() {
  if (!props.compareRoleId) return;

  try {
    // 获取对比角色基本信息
    const roleResponse = await request({
      url: `/api/role/${props.compareRoleId}`,
      method: 'get'
    });

    if (roleResponse) {
      compareRoleInfo.value = roleResponse;
    }

    // 获取对比角色权限列表
    const permissionsResponse = await request({
      url: `/api/role-permission/list/${props.compareRoleId}`,
      method: 'get'
    });

    if (Array.isArray(permissionsResponse)) {
      compareRolePermissions.value = permissionsResponse;
    }
  } catch (error) {
    console.error('获取对比角色权限失败:', error);
  }
}

// 获取权限类型标签
function getPermissionType(type: string): 'primary' | 'success' | 'info' | 'warning' | 'danger' {
  const typeMap: Record<string, 'primary' | 'success' | 'info' | 'warning' | 'danger'> = {
    menu: 'primary',
    button: 'success',
    api: 'warning',
    data: 'info'
  };
  return typeMap[type] || 'info';
}

function getPermissionTypeText(type: string) {
  const typeMap: Record<string, string> = {
    menu: '菜单',
    button: '按钮',
    api: 'API',
    data: '数据'
  };
  return typeMap[type] || type;
}

// 获取权限分组显示名称
function getGroupDisplayName(group: string) {
  const groupMap: Record<string, string> = {
    user: '用户管理',
    role: '角色管理',
    permission: '权限管理',
    system: '系统管理',
    order: '订单管理',
    course: '课程管理',
    supplier: '供应商管理',
    finance: '财务管理',
    report: '报表统计',
    config: '配置管理'
  };
  return groupMap[group] || group;
}

// 获取数据范围显示
function getDataScopeText(scope: string) {
  const scopeMap: Record<string, string> = {
    all: '全部数据',
    dept: '部门数据',
    self: '个人数据',
    custom: '自定义数据'
  };
  return scopeMap[scope] || scope;
}

// 监听roleId变化
watch(
  () => props.roleId,
  newRoleId => {
    if (newRoleId && props.visible) {
      fetchRolePermissions();
    }
  }
);

// 监听compareRoleId变化
watch(
  () => props.compareRoleId,
  newCompareRoleId => {
    if (newCompareRoleId && props.visible) {
      fetchCompareRolePermissions();
    }
  }
);

// 监听visible变化
watch(
  () => props.visible,
  newVisible => {
    if (newVisible) {
      if (props.roleId) {
        fetchRolePermissions();
      }
      if (props.compareRoleId) {
        fetchCompareRolePermissions();
      }
    }
  }
);
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="`角色权限预览 - ${props.roleName || roleInfo.role_name || '未知角色'}`"
    width="90%"
    :style="{ maxWidth: '1000px' }"
    top="5vh"
    :close-on-click-modal="false"
    class="role-permission-preview-dialog"
  >
    <div v-loading="loading" class="space-y-16px">
      <!-- 标签页 -->
      <ElTabs v-model="activeTab">
        <!-- 权限列表 -->
        <ElTabPane label="权限列表" name="permissions">
          <!-- 角色基本信息 -->
          <ElCard v-if="roleInfo.role_id" class="mb-16px">
            <template #header>
              <span class="text-14px font-medium">角色信息</span>
            </template>
            <ElDescriptions :column="2" border>
              <ElDescriptionsItem label="角色名称">
                {{ roleInfo.role_name }}
              </ElDescriptionsItem>
              <ElDescriptionsItem label="角色代码">
                {{ roleInfo.role_code }}
              </ElDescriptionsItem>
              <ElDescriptionsItem label="角色级别">
                {{ roleInfo.role_level }}
              </ElDescriptionsItem>
              <ElDescriptionsItem label="角色状态">
                <ElTag :type="roleInfo.status === 1 ? 'success' : 'danger'">
                  {{ roleInfo.status === 1 ? '启用' : '禁用' }}
                </ElTag>
              </ElDescriptionsItem>
              <ElDescriptionsItem label="角色描述" :span="2">
                {{ roleInfo.role_description || '无描述' }}
              </ElDescriptionsItem>
            </ElDescriptions>
          </ElCard>

          <!-- 权限列表 -->
          <ElCard>
            <template #header>
              <div class="flex items-center justify-between">
                <span class="text-14px font-medium">权限列表</span>
                <ElTag type="info">共 {{ rolePermissions.length }} 个权限</ElTag>
              </div>
            </template>

            <div v-if="groupedPermissions.length === 0" class="py-32px text-center text-gray-500">该角色暂无权限</div>

            <div v-else class="space-y-16px">
              <div v-for="group in groupedPermissions" :key="group.name" class="border border-gray-200 rounded-8px">
                <div class="border-b border-gray-200 bg-gray-50 px-16px py-12px">
                  <div class="flex items-center justify-between">
                    <span class="text-14px text-gray-700 font-medium">
                      {{ group.name }}
                    </span>
                    <ElTag size="small" type="info">{{ group.permissions.length }} 个权限</ElTag>
                  </div>
                </div>

                <div class="p-16px">
                  <div class="grid grid-cols-1 gap-12px">
                    <div
                      v-for="permission in group.permissions"
                      :key="permission.permission_id"
                      class="flex items-center justify-between rounded-6px bg-gray-50 p-12px"
                    >
                      <div class="flex items-center gap-12px">
                        <ElTag size="small" :type="getPermissionType(permission.permission_type)">
                          {{ getPermissionTypeText(permission.permission_type) }}
                        </ElTag>
                        <div>
                          <div class="text-14px font-medium">
                            {{ permission.permission_name }}
                          </div>
                          <div class="text-12px text-gray-500">
                            {{ permission.permission_code }}
                          </div>
                        </div>
                      </div>

                      <div class="flex items-center gap-8px">
                        <ElTag
                          v-if="permission.data_scope && permission.data_scope !== 'all'"
                          size="small"
                          type="warning"
                        >
                          {{ getDataScopeText(permission.data_scope) }}
                        </ElTag>
                        <ElTag size="small" :type="permission.grant_type === 'grant' ? 'success' : 'danger'">
                          {{ permission.grant_type === 'grant' ? '允许' : '拒绝' }}
                        </ElTag>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ElCard>
        </ElTabPane>

        <!-- 权限分析 -->
        <ElTabPane v-if="showAnalysis" label="权限分析" name="analysis">
          <div class="space-y-16px">
            <!-- 统计概览 -->
            <ElCard>
              <template #header>
                <span class="text-14px font-medium">权限统计</span>
              </template>
              <div class="grid grid-cols-2 gap-16px md:grid-cols-4">
                <div class="text-center">
                  <div class="text-2xl text-blue-600 font-bold">{{ permissionAnalysis.total }}</div>
                  <div class="text-12px text-gray-500">总权限数</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl text-green-600 font-bold">{{ permissionAnalysis.byType.menu || 0 }}</div>
                  <div class="text-12px text-gray-500">菜单权限</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl text-orange-600 font-bold">{{ permissionAnalysis.byType.api || 0 }}</div>
                  <div class="text-12px text-gray-500">API权限</div>
                </div>
                <div class="text-center">
                  <div class="text-2xl text-purple-600 font-bold">{{ permissionAnalysis.byType.button || 0 }}</div>
                  <div class="text-12px text-gray-500">按钮权限</div>
                </div>
              </div>
            </ElCard>

            <!-- 权限分布 -->
            <ElCard>
              <template #header>
                <span class="text-14px font-medium">权限分布</span>
              </template>
              <div class="space-y-8px">
                <div
                  v-for="(count, group) in permissionAnalysis.byGroup"
                  :key="group"
                  class="flex items-center justify-between"
                >
                  <span class="text-14px">{{ group }}</span>
                  <div class="flex items-center gap-8px">
                    <div class="h-6px w-100px rounded-full bg-gray-200">
                      <div
                        class="h-6px rounded-full bg-blue-500"
                        :style="{ width: `${(count / permissionAnalysis.total) * 100}%` }"
                      ></div>
                    </div>
                    <span class="w-30px text-right text-12px text-gray-500">{{ count }}</span>
                  </div>
                </div>
              </div>
            </ElCard>

            <!-- 权限冲突检测 -->
            <ElCard v-if="permissionConflicts.length > 0">
              <template #header>
                <span class="text-14px text-red-600 font-medium">权限冲突检测</span>
              </template>
              <div class="space-y-12px">
                <ElAlert
                  v-for="(conflict, index) in permissionConflicts"
                  :key="index"
                  :title="conflict.message"
                  type="warning"
                  show-icon
                >
                  <div class="mt-8px">
                    <div class="mb-4px text-12px text-gray-600">冲突权限：</div>
                    <div class="flex flex-wrap gap-4px">
                      <ElTag
                        v-for="perm in conflict.permissions"
                        :key="perm.permission_id"
                        size="small"
                        :type="perm.grant_type === 'grant' ? 'success' : 'danger'"
                      >
                        {{ perm.permission_name }}
                      </ElTag>
                    </div>
                  </div>
                </ElAlert>
              </div>
            </ElCard>
          </div>
        </ElTabPane>

        <!-- 权限对比 -->
        <ElTabPane v-if="compareRoleId && permissionComparison" label="权限对比" name="comparison">
          <div class="space-y-16px">
            <!-- 对比概览 -->
            <ElCard>
              <template #header>
                <span class="text-14px font-medium">对比概览</span>
              </template>
              <div class="grid grid-cols-2 gap-16px">
                <div>
                  <h4 class="mb-8px text-14px font-medium">{{ roleInfo.role_name }}</h4>
                  <div class="text-12px text-gray-500">{{ rolePermissions.length }} 个权限</div>
                </div>
                <div>
                  <h4 class="mb-8px text-14px font-medium">{{ compareRoleInfo.role_name }}</h4>
                  <div class="text-12px text-gray-500">{{ compareRolePermissions.length }} 个权限</div>
                </div>
              </div>
              <div class="mt-16px text-center">
                <div class="text-2xl text-blue-600 font-bold">{{ permissionComparison.similarity }}%</div>
                <div class="text-12px text-gray-500">权限相似度</div>
              </div>
            </ElCard>

            <!-- 权限差异 -->
            <div class="grid grid-cols-1 gap-16px md:grid-cols-3">
              <!-- 共同权限 -->
              <ElCard>
                <template #header>
                  <span class="text-14px text-green-600 font-medium">
                    共同权限 ({{ permissionComparison.common.length }})
                  </span>
                </template>
                <div class="max-h-200px overflow-y-auto space-y-4px">
                  <ElTag
                    v-for="perm in permissionComparison.common"
                    :key="perm.permission_id"
                    size="small"
                    type="success"
                    class="mb-4px"
                  >
                    {{ perm.permission_name }}
                  </ElTag>
                </div>
              </ElCard>

              <!-- 仅当前角色有 -->
              <ElCard>
                <template #header>
                  <span class="text-14px text-blue-600 font-medium">
                    仅{{ roleInfo.role_name }}有 ({{ permissionComparison.onlyInCurrent.length }})
                  </span>
                </template>
                <div class="max-h-200px overflow-y-auto space-y-4px">
                  <ElTag
                    v-for="perm in permissionComparison.onlyInCurrent"
                    :key="perm.permission_id"
                    size="small"
                    type="primary"
                    class="mb-4px"
                  >
                    {{ perm.permission_name }}
                  </ElTag>
                </div>
              </ElCard>

              <!-- 仅对比角色有 -->
              <ElCard>
                <template #header>
                  <span class="text-14px text-orange-600 font-medium">
                    仅{{ compareRoleInfo.role_name }}有 ({{ permissionComparison.onlyInCompare.length }})
                  </span>
                </template>
                <div class="max-h-200px overflow-y-auto space-y-4px">
                  <ElTag
                    v-for="perm in permissionComparison.onlyInCompare"
                    :key="perm.permission_id"
                    size="small"
                    type="warning"
                    class="mb-4px"
                  >
                    {{ perm.permission_name }}
                  </ElTag>
                </div>
              </ElCard>
            </div>
          </div>
        </ElTabPane>
      </ElTabs>
    </div>

    <template #footer>
      <div class="flex justify-end">
        <ElButton @click="dialogVisible = false">关闭</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
/* 角色权限预览弹窗样式 */
:deep(.role-permission-preview-dialog) {
  .el-dialog {
    margin: 5vh auto;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
  }

  .el-dialog__body {
    flex: 1;
    overflow: hidden;
    padding: 20px;
  }

  .el-dialog__footer {
    flex-shrink: 0;
    padding: 15px 20px;
    border-top: 1px solid #e4e7ed;
  }
}

/* 权限列表滚动区域 */
.max-h-200px {
  max-height: 40vh;
}

/* 移动端适配 */
@media (max-width: 768px) {
  :deep(.role-permission-preview-dialog) {
    .el-dialog {
      margin: 2vh auto;
      max-height: 96vh;
      width: 95% !important;
    }
  }

  .grid-cols-1 {
    grid-template-columns: 1fr;
  }

  .max-h-200px {
    max-height: 30vh;
  }
}
</style>
