<script setup lang="ts">
import { computed, ref } from 'vue';

interface Props {
  conflict: any;
  userInfo?: any;
  roleInfo?: any[];
}

interface Emits {
  (e: 'apply-suggestion', suggestion: any): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const selectedSuggestion = ref<string>('');

// 解决建议配置
const suggestionTemplates = {
  grant_deny_conflict: {
    title: '授权冲突解决建议',
    description: '检测到同一权限存在授予和拒绝的冲突',
    suggestions: [
      {
        id: 'use_grant',
        title: '使用授予权限',
        description: '保留授予权限，移除拒绝权限',
        icon: 'ic:round-check-circle',
        color: 'success',
        priority: 1,
        safety: 'medium',
        impact: '用户将获得该权限的完整访问权'
      },
      {
        id: 'use_deny',
        title: '使用拒绝权限',
        description: '保留拒绝权限，移除授予权限',
        icon: 'ic:round-block',
        color: 'danger',
        priority: 2,
        safety: 'high',
        impact: '用户将被完全拒绝访问该权限'
      },
      {
        id: 'conditional_grant',
        title: '条件性授权',
        description: '根据数据范围设置条件性权限',
        icon: 'ic:round-rule',
        color: 'warning',
        priority: 3,
        safety: 'high',
        impact: '用户将在特定条件下获得权限'
      }
    ]
  },
  role_scope_conflict: {
    title: '数据范围冲突解决建议',
    description: '检测到不同角色对同一权限设置了不同的数据范围',
    suggestions: [
      {
        id: 'use_widest_scope',
        title: '使用最宽数据范围',
        description: '选择最宽松的数据访问范围',
        icon: 'ic:round-expand',
        color: 'primary',
        priority: 1,
        safety: 'low',
        impact: '用户将获得最大的数据访问权限'
      },
      {
        id: 'use_narrowest_scope',
        title: '使用最窄数据范围',
        description: '选择最严格的数据访问范围',
        icon: 'ic:round-compress',
        color: 'warning',
        priority: 2,
        safety: 'high',
        impact: '用户将获得最小的数据访问权限'
      },
      {
        id: 'merge_scopes',
        title: '合并数据范围',
        description: '智能合并多个数据范围',
        icon: 'ic:round-merge',
        color: 'info',
        priority: 3,
        safety: 'medium',
        impact: '用户将获得合并后的数据访问权限'
      }
    ]
  },
  multiple_role_conflict: {
    title: '多角色冲突解决建议',
    description: '检测到多个角色对同一权限有不同配置',
    suggestions: [
      {
        id: 'use_highest_priority_role',
        title: '使用最高优先级角色',
        description: '根据角色优先级选择权限配置',
        icon: 'ic:round-priority-high',
        color: 'primary',
        priority: 1,
        safety: 'medium',
        impact: '使用优先级最高角色的权限配置'
      },
      {
        id: 'create_direct_permission',
        title: '创建直接权限',
        description: '为用户创建直接权限覆盖角色权限',
        icon: 'ic:round-person-add',
        color: 'success',
        priority: 2,
        safety: 'high',
        impact: '用户将拥有独立的权限配置'
      },
      {
        id: 'remove_conflicting_roles',
        title: '移除冲突角色',
        description: '移除导致冲突的角色分配',
        icon: 'ic:round-person-remove',
        color: 'danger',
        priority: 3,
        safety: 'high',
        impact: '用户将失去部分角色权限'
      }
    ]
  }
};

// 计算属性
const currentSuggestions = computed(() => {
  const conflictType = props.conflict?.conflict_type;
  return (
    suggestionTemplates[conflictType as keyof typeof suggestionTemplates] || {
      title: '通用解决建议',
      description: '为此权限冲突提供解决方案',
      suggestions: []
    }
  );
});

const selectedSuggestionInfo = computed(() => {
  return currentSuggestions.value.suggestions.find(s => s.id === selectedSuggestion.value);
});

// 获取安全等级颜色
function getSafetyColor(safety: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' {
  const colorMap: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    high: 'success',
    medium: 'warning',
    low: 'danger'
  };
  return colorMap[safety] || 'info';
}

// 获取安全等级文本
function getSafetyText(safety: string): string {
  const textMap: Record<string, string> = {
    high: '高安全',
    medium: '中等安全',
    low: '低安全'
  };
  return textMap[safety] || safety;
}

// 应用建议
function applySuggestion(suggestion: any) {
  selectedSuggestion.value = suggestion.id;
  emit('apply-suggestion', {
    conflict: props.conflict,
    suggestion,
    resolution: mapSuggestionToResolution(suggestion.id)
  });
}

// 将建议映射到解决方案
function mapSuggestionToResolution(suggestionId: string): string {
  const mappingMap: Record<string, string> = {
    use_grant: 'use_direct',
    use_deny: 'remove_conflict',
    conditional_grant: 'merge',
    use_widest_scope: 'merge',
    use_narrowest_scope: 'use_role',
    merge_scopes: 'merge',
    use_highest_priority_role: 'use_role',
    create_direct_permission: 'use_direct',
    remove_conflicting_roles: 'remove_conflict'
  };
  return mappingMap[suggestionId] || 'use_direct';
}

// 获取建议的详细分析
function getSuggestionAnalysis(suggestion: any) {
  const analysis = {
    pros: [] as string[],
    cons: [] as string[],
    requirements: [] as string[]
  };

  switch (suggestion.id) {
    case 'use_grant':
      analysis.pros = ['用户获得完整权限', '操作简单直接', '符合授权原则'];
      analysis.cons = ['可能存在安全风险', '需要确认权限必要性'];
      analysis.requirements = ['确认用户确实需要此权限', '评估安全影响'];
      break;
    case 'use_deny':
      analysis.pros = ['安全性最高', '避免权限滥用', '符合最小权限原则'];
      analysis.cons = ['用户可能无法正常工作', '需要重新评估权限需求'];
      analysis.requirements = ['确认用户不需要此权限', '提供替代方案'];
      break;
    case 'use_widest_scope':
      analysis.pros = ['用户获得最大灵活性', '减少权限不足问题'];
      analysis.cons = ['可能存在数据泄露风险', '违反最小权限原则'];
      analysis.requirements = ['评估数据安全影响', '确认业务必要性'];
      break;
    case 'use_narrowest_scope':
      analysis.pros = ['数据安全性最高', '符合最小权限原则'];
      analysis.cons = ['可能影响用户工作效率', '需要额外权限申请'];
      analysis.requirements = ['确认最小权限足够', '建立权限申请流程'];
      break;
    default:
      analysis.pros = ['平衡安全性和可用性'];
      analysis.cons = ['需要仔细评估'];
      analysis.requirements = ['详细分析具体情况'];
  }

  return analysis;
}
</script>

<template>
  <div class="conflict-suggestions">
    <!-- 建议标题 -->
    <div class="mb-16px">
      <h4 class="mb-4px text-lg text-gray-800 font-medium">{{ currentSuggestions.title }}</h4>
      <p class="text-sm text-gray-600">{{ currentSuggestions.description }}</p>
    </div>

    <!-- 建议列表 -->
    <div class="space-y-12px">
      <div
        v-for="suggestion in currentSuggestions.suggestions"
        :key="suggestion.id"
        class="suggestion-card cursor-pointer border rounded-8px p-16px transition-all duration-200"
        :class="{
          'border-primary bg-primary-light-9': selectedSuggestion === suggestion.id,
          'border-gray-200 hover:border-gray-300': selectedSuggestion !== suggestion.id
        }"
        @click="selectedSuggestion = suggestion.id"
      >
        <div class="flex items-start gap-12px">
          <!-- 建议图标 -->
          <div class="flex-shrink-0">
            <div
              class="h-40px w-40px flex items-center justify-center rounded-full"
              :class="`bg-${suggestion.color}-light-8`"
            >
              <component :is="`icon-${suggestion.icon}`" :class="`text-${suggestion.color} text-20px`" />
            </div>
          </div>

          <!-- 建议内容 -->
          <div class="flex-1">
            <div class="mb-8px flex items-center justify-between">
              <h5 class="text-gray-800 font-medium">{{ suggestion.title }}</h5>
              <div class="flex items-center gap-8px">
                <ElTag size="small" :type="getSafetyColor(suggestion.safety)">
                  {{ getSafetyText(suggestion.safety) }}
                </ElTag>
                <ElTag size="small" type="info">优先级 {{ suggestion.priority }}</ElTag>
              </div>
            </div>

            <p class="mb-8px text-sm text-gray-600">{{ suggestion.description }}</p>
            <p class="text-xs text-gray-500">{{ suggestion.impact }}</p>
          </div>

          <!-- 选择指示器 -->
          <div v-if="selectedSuggestion === suggestion.id" class="flex-shrink-0">
            <icon-ic-round-check-circle class="text-20px text-primary" />
          </div>
        </div>
      </div>
    </div>

    <!-- 选中建议的详细分析 -->
    <div v-if="selectedSuggestionInfo" class="mt-16px">
      <ElCard>
        <template #header>
          <div class="flex items-center gap-8px">
            <icon-ic-round-bar-chart class="text-blue-500" />
            <span class="font-medium">建议分析</span>
          </div>
        </template>

        <div class="space-y-12px">
          <!-- 优势 -->
          <div>
            <h6 class="mb-4px text-sm text-green-700 font-medium">优势</h6>
            <ul class="text-sm text-gray-600 space-y-2px">
              <li
                v-for="pro in getSuggestionAnalysis(selectedSuggestionInfo).pros"
                :key="pro"
                class="flex items-center gap-4px"
              >
                <icon-ic-round-check class="text-12px text-green-500" />
                {{ pro }}
              </li>
            </ul>
          </div>

          <!-- 风险 -->
          <div>
            <h6 class="mb-4px text-sm text-red-700 font-medium">风险</h6>
            <ul class="text-sm text-gray-600 space-y-2px">
              <li
                v-for="con in getSuggestionAnalysis(selectedSuggestionInfo).cons"
                :key="con"
                class="flex items-center gap-4px"
              >
                <icon-ic-round-warning class="text-12px text-red-500" />
                {{ con }}
              </li>
            </ul>
          </div>

          <!-- 要求 -->
          <div>
            <h6 class="mb-4px text-sm text-blue-700 font-medium">实施要求</h6>
            <ul class="text-sm text-gray-600 space-y-2px">
              <li
                v-for="req in getSuggestionAnalysis(selectedSuggestionInfo).requirements"
                :key="req"
                class="flex items-center gap-4px"
              >
                <icon-ic-round-info class="text-12px text-blue-500" />
                {{ req }}
              </li>
            </ul>
          </div>
        </div>
      </ElCard>
    </div>

    <!-- 应用按钮 -->
    <div v-if="selectedSuggestionInfo" class="mt-16px flex justify-end">
      <ElButton type="primary" @click="applySuggestion(selectedSuggestionInfo)">
        <template #icon>
          <icon-ic-round-check />
        </template>
        应用此建议
      </ElButton>
    </div>
  </div>
</template>

<style scoped>
.suggestion-card {
  transition: all 0.2s ease;
}

.suggestion-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.suggestion-card.selected {
  border-color: var(--el-color-primary);
  background-color: var(--el-color-primary-light-9);
}
</style>
