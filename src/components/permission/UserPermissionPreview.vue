<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { request } from '@/service/request';

defineOptions({
  name: 'UserPermissionPreview'
});

interface Props {
  userId: number;
  showHeader?: boolean;
  showStats?: boolean;
  groupBy?: 'module' | 'type' | 'source';
}

interface Permission {
  permission_id: number;
  permission_code: string;
  permission_name: string;
  permission_description: string;
  permission_type: 'menu' | 'button' | 'api' | 'data';
  permission_group: string;
  source: 'role' | 'direct';
  grant_type: 'grant' | 'deny';
  data_scope?: string;
}

const props = withDefaults(defineProps<Props>(), {
  showHeader: true,
  showStats: true,
  groupBy: 'module'
});

// 响应式数据
const loading = ref(false);
const permissions = ref<Permission[]>([]);
const searchKeyword = ref('');
const selectedType = ref<string>('');
const selectedSource = ref<string>('');

// 计算属性
const filteredPermissions = computed(() => {
  let filtered = permissions.value;

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(
      p =>
        p.permission_name.toLowerCase().includes(keyword) ||
        p.permission_code.toLowerCase().includes(keyword) ||
        p.permission_description.toLowerCase().includes(keyword)
    );
  }

  // 权限类型筛选
  if (selectedType.value) {
    filtered = filtered.filter(p => p.permission_type === selectedType.value);
  }

  // 权限来源筛选
  if (selectedSource.value) {
    filtered = filtered.filter(p => p.source === selectedSource.value);
  }

  return filtered;
});

const groupedPermissions = computed(() => {
  const grouped: Record<string, Permission[]> = {};

  filteredPermissions.value.forEach(permission => {
    let key = '';
    switch (props.groupBy) {
      case 'module':
        key = permission.permission_group || '其他';
        break;
      case 'type':
        key = getPermissionTypeText(permission.permission_type);
        break;
      case 'source':
        key = permission.source === 'role' ? '角色权限' : '直接权限';
        break;
      default:
        key = '全部权限';
    }

    if (!grouped[key]) {
      grouped[key] = [];
    }
    grouped[key].push(permission);
  });

  return grouped;
});

const permissionStats = computed(() => {
  const total = permissions.value.length;
  const byType = permissions.value.reduce(
    (acc, p) => {
      acc[p.permission_type] = (acc[p.permission_type] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );

  const bySource = permissions.value.reduce(
    (acc, p) => {
      acc[p.source] = (acc[p.source] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );

  return {
    total,
    byType,
    bySource
  };
});

// 获取用户权限
async function fetchUserPermissions() {
  if (!props.userId) return;

  loading.value = true;
  try {
    const response = await request({
      url: `/api/user/permissions/${props.userId}`,
      method: 'get'
    });

    if (Array.isArray(response)) {
      permissions.value = response;
    }
  } catch (error) {
    console.error('获取用户权限失败:', error);
    ElMessage.error('获取用户权限失败');
  } finally {
    loading.value = false;
  }
}

// 获取权限类型文本
function getPermissionTypeText(type: string) {
  const typeMap: Record<string, string> = {
    menu: '菜单权限',
    button: '按钮权限',
    api: 'API权限',
    data: '数据权限'
  };
  return typeMap[type] || type;
}

// 获取权限类型图标
function getPermissionTypeIcon(type: string) {
  const iconMap: Record<string, string> = {
    menu: 'ic-round-menu',
    button: 'ic-round-smart-button',
    api: 'ic-round-api',
    data: 'ic-round-database'
  };
  return iconMap[type] || 'ic-round-security';
}

// 获取权限类型颜色
function getPermissionTypeColor(type: string): 'primary' | 'success' | 'info' | 'warning' | 'danger' {
  const colorMap: Record<string, 'primary' | 'success' | 'info' | 'warning' | 'danger'> = {
    menu: 'primary',
    button: 'success',
    api: 'warning',
    data: 'danger'
  };
  return colorMap[type] || '';
}

// 获取权限来源颜色
function getSourceColor(source: string) {
  return source === 'role' ? 'primary' : 'success';
}

// 清空筛选
function clearFilters() {
  searchKeyword.value = '';
  selectedType.value = '';
  selectedSource.value = '';
}

// 初始化
onMounted(() => {
  if (props.userId) {
    fetchUserPermissions();
  }
});

// 监听userId变化
watch(
  () => props.userId,
  newUserId => {
    if (newUserId) {
      fetchUserPermissions();
    }
  }
);
</script>

<template>
  <div v-loading="loading" class="user-permission-preview">
    <!-- 头部信息 -->
    <div v-if="showHeader" class="mb-16px">
      <div class="mb-12px flex items-center justify-between">
        <h4 class="text-lg font-medium">权限预览</h4>
        <ElButton size="small" @click="fetchUserPermissions">刷新</ElButton>
      </div>

      <!-- 统计信息 -->
      <div v-if="showStats" class="grid grid-cols-2 mb-16px gap-12px md:grid-cols-4">
        <ElCard class="stat-card">
          <div class="text-center">
            <div class="text-2xl text-primary font-bold">{{ permissionStats.total }}</div>
            <div class="text-sm text-gray-500">总权限数</div>
          </div>
        </ElCard>
        <ElCard class="stat-card">
          <div class="text-center">
            <div class="text-2xl text-success font-bold">{{ permissionStats.bySource.role || 0 }}</div>
            <div class="text-sm text-gray-500">角色权限</div>
          </div>
        </ElCard>
        <ElCard class="stat-card">
          <div class="text-center">
            <div class="text-2xl text-warning font-bold">{{ permissionStats.bySource.direct || 0 }}</div>
            <div class="text-sm text-gray-500">直接权限</div>
          </div>
        </ElCard>
        <ElCard class="stat-card">
          <div class="text-center">
            <div class="text-2xl text-info font-bold">{{ permissionStats.byType.menu || 0 }}</div>
            <div class="text-sm text-gray-500">菜单权限</div>
          </div>
        </ElCard>
      </div>

      <!-- 筛选工具栏 -->
      <div class="flex flex-wrap items-center gap-12px">
        <ElInput v-model="searchKeyword" placeholder="搜索权限..." style="width: 200px" clearable />

        <ElSelect v-model="selectedType" placeholder="权限类型" style="width: 120px" clearable>
          <ElOption label="菜单权限" value="menu" />
          <ElOption label="按钮权限" value="button" />
          <ElOption label="API权限" value="api" />
          <ElOption label="数据权限" value="data" />
        </ElSelect>

        <ElSelect v-model="selectedSource" placeholder="权限来源" style="width: 120px" clearable>
          <ElOption label="角色权限" value="role" />
          <ElOption label="直接权限" value="direct" />
        </ElSelect>

        <ElButton v-if="searchKeyword || selectedType || selectedSource" size="small" @click="clearFilters">
          清空筛选
        </ElButton>
      </div>
    </div>

    <!-- 权限列表 -->
    <div v-if="Object.keys(groupedPermissions).length > 0" class="space-y-16px">
      <div v-for="(groupPermissions, groupName) in groupedPermissions" :key="groupName">
        <div class="mb-12px flex items-center gap-8px">
          <h5 class="text-md font-medium">{{ groupName }}</h5>
          <ElTag size="small">{{ groupPermissions.length }}</ElTag>
        </div>

        <div class="grid grid-cols-1 gap-12px lg:grid-cols-3 md:grid-cols-2">
          <ElCard
            v-for="permission in groupPermissions"
            :key="permission.permission_id"
            class="permission-card"
            shadow="hover"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1">
                <div class="mb-4px flex items-center gap-8px">
                  <span class="font-medium">{{ permission.permission_name }}</span>
                </div>
                <div class="mb-4px text-sm text-gray-500">{{ permission.permission_code }}</div>
                <div class="text-xs text-gray-400">{{ permission.permission_description }}</div>

                <!-- 权限详情 -->
                <div class="mt-8px flex items-center gap-8px">
                  <ElTag size="small" :type="getPermissionTypeColor(permission.permission_type)">
                    {{ getPermissionTypeText(permission.permission_type) }}
                  </ElTag>
                  <ElTag size="small" :type="getSourceColor(permission.source)">
                    {{ permission.source === 'role' ? '角色' : '直接' }}
                  </ElTag>
                  <ElTag v-if="permission.data_scope" size="small" type="info">
                    {{ permission.data_scope }}
                  </ElTag>
                </div>
              </div>
            </div>
          </ElCard>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="!loading" class="py-32px text-center text-gray-500">
      <div class="mb-8px text-48px">🔒</div>
      <div>{{ searchKeyword || selectedType || selectedSource ? '没有找到匹配的权限' : '该用户暂无任何权限' }}</div>
    </div>
  </div>
</template>

<style scoped>
.stat-card {
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.permission-card {
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.permission-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-left-color: var(--el-color-primary);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .grid-cols-2.md\:grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }

  .grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3 {
    grid-template-columns: 1fr;
  }
}
</style>
