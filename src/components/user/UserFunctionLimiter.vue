<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { request } from '@/service/request';

interface Props {
  visible: boolean;
  userId: number;
  userInfo?: any;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  userId: 0,
  userInfo: undefined
});

const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
const saving = ref(false);
const userLimitations = ref<any>({});

// 功能限制配置
const functionLimitations = ref([
  {
    key: 'disable_subordinate_creation',
    name: '禁止开通下级',
    description: '禁止用户开通下级代理账户',
    category: 'user_management',
    icon: 'ic:round-person-add-disabled',
    color: 'danger',
    defaultValue: false
  },
  {
    key: 'disable_online_recharge',
    name: '禁止在线充值',
    description: '禁止用户使用在线充值功能',
    category: 'finance',
    icon: 'ic:round-payment-disabled',
    color: 'warning',
    defaultValue: false
  },
  {
    key: 'disable_withdraw',
    name: '禁止提现',
    description: '禁止用户申请提现',
    category: 'finance',
    icon: 'ic:round-money-off',
    color: 'warning',
    defaultValue: false
  },
  {
    key: 'disable_rate_modification',
    name: '禁止修改费率',
    description: '禁止用户修改自己的费率设置',
    category: 'user_management',
    icon: 'ic:round-percent-disabled',
    color: 'info',
    defaultValue: false
  },
  {
    key: 'disable_data_export',
    name: '禁止数据导出',
    description: '禁止用户导出系统数据',
    category: 'data_access',
    icon: 'ic:round-download-disabled',
    color: 'info',
    defaultValue: false
  },
  {
    key: 'disable_statistics_view',
    name: '禁止查看统计',
    description: '禁止用户查看详细统计数据',
    category: 'data_access',
    icon: 'ic:round-analytics-disabled',
    color: 'info',
    defaultValue: false
  },
  {
    key: 'limit_login_devices',
    name: '限制登录设备',
    description: '限制用户同时登录的设备数量',
    category: 'security',
    icon: 'ic:round-devices',
    color: 'warning',
    defaultValue: false,
    hasValue: true,
    valueType: 'number',
    valueLabel: '最大设备数',
    valueMin: 1,
    valueMax: 10
  },
  {
    key: 'limit_daily_operations',
    name: '限制日操作次数',
    description: '限制用户每日的操作次数',
    category: 'security',
    icon: 'ic:round-timer',
    color: 'warning',
    defaultValue: false,
    hasValue: true,
    valueType: 'number',
    valueLabel: '每日最大操作次数',
    valueMin: 1,
    valueMax: 10000
  },
  {
    key: 'force_password_change',
    name: '强制修改密码',
    description: '强制用户在下次登录时修改密码',
    category: 'security',
    icon: 'ic:round-lock-reset',
    color: 'danger',
    defaultValue: false
  },
  {
    key: 'enable_ip_whitelist',
    name: '启用IP白名单',
    description: '只允许白名单IP地址登录',
    category: 'security',
    icon: 'ic:round-security',
    color: 'danger',
    defaultValue: false,
    hasValue: true,
    valueType: 'text',
    valueLabel: 'IP白名单（逗号分隔）',
    placeholder: '例如：***********,***********'
  }
]);

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
});

const limitationsByCategory = computed(() => {
  const categories: Record<string, any[]> = {};
  functionLimitations.value.forEach(limitation => {
    if (!categories[limitation.category]) {
      categories[limitation.category] = [];
    }
    categories[limitation.category].push(limitation);
  });
  return categories;
});

const categoryNames = computed(() => ({
  user_management: '用户管理',
  finance: '财务功能',
  data_access: '数据访问',
  security: '安全控制'
}));

const categoryIcons = computed(() => ({
  user_management: 'ic:round-people',
  finance: 'ic:round-account-balance',
  data_access: 'ic:round-analytics',
  security: 'ic:round-security'
}));

// 获取用户功能限制
async function fetchUserLimitations() {
  if (!props.userId) return;

  loading.value = true;
  try {
    const response = await request({
      url: `/api/user/${props.userId}/limitations`,
      method: 'get'
    });

    if (response) {
      userLimitations.value = response.limitations || {};
    }
  } catch (error) {
    console.error('获取用户功能限制失败:', error);
    ElMessage.error('获取用户功能限制失败');
  } finally {
    loading.value = false;
  }
}

// 保存功能限制设置
async function saveLimitations() {
  if (!props.userId) return;

  try {
    await ElMessageBox.confirm('确定要保存功能限制设置吗？', '确认保存', {
      type: 'warning',
      confirmButtonText: '确认保存',
      cancelButtonText: '取消'
    });

    saving.value = true;

    await request({
      url: `/api/user/${props.userId}/limitations`,
      method: 'post',
      data: {
        limitations: userLimitations.value
      }
    });

    ElMessage.success('功能限制设置保存成功');
    emit('success');
  } catch (error) {
    if (error !== 'cancel') {
      console.error('保存功能限制设置失败:', error);
      ElMessage.error('保存功能限制设置失败');
    }
  } finally {
    saving.value = false;
  }
}

// 切换功能限制
function toggleLimitation(limitationKey: string) {
  if (!userLimitations.value[limitationKey]) {
    userLimitations.value[limitationKey] = { enabled: false };
  }
  userLimitations.value[limitationKey].enabled = !userLimitations.value[limitationKey].enabled;
}

// 检查功能限制是否启用
function isLimitationEnabled(limitationKey: string): boolean {
  return userLimitations.value[limitationKey]?.enabled || false;
}

// 获取限制值
function getLimitationValue(limitationKey: string): any {
  return userLimitations.value[limitationKey]?.value || '';
}

// 设置限制值
function setLimitationValue(limitationKey: string, value: any) {
  if (!userLimitations.value[limitationKey]) {
    userLimitations.value[limitationKey] = { enabled: false };
  }
  userLimitations.value[limitationKey].value = value;
}

// 获取启用的限制数量
function getEnabledLimitationsCount(): number {
  return Object.values(userLimitations.value).filter((limitation: any) => limitation.enabled).length;
}

// 重置所有限制
async function resetAllLimitations() {
  try {
    await ElMessageBox.confirm('确定要重置所有功能限制吗？这将清除所有限制设置。', '重置确认', {
      type: 'warning',
      confirmButtonText: '确认重置',
      cancelButtonText: '取消'
    });

    userLimitations.value = {};
    ElMessage.success('已重置所有功能限制');
  } catch (error) {
    // 用户取消
  }
}

// 关闭对话框
function closeDialog() {
  emit('update:visible', false);
}

// 监听对话框显示状态
watch(
  () => props.visible,
  visible => {
    if (visible && props.userId) {
      fetchUserLimitations();
    }
  }
);

// 组件挂载
onMounted(() => {
  if (props.visible && props.userId) {
    fetchUserLimitations();
  }
});
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    title="用户功能限制"
    width="800px"
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <div v-loading="loading" class="space-y-16px">
      <!-- 用户信息和限制概览 -->
      <ElCard>
        <template #header>
          <div class="flex items-center gap-8px">
            <icon-ic-round-person class="text-blue-500" />
            <span class="font-medium">用户信息</span>
          </div>
        </template>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-16px">
            <div>
              <div class="text-lg font-medium">{{ userInfo?.username || `用户ID: ${userId}` }}</div>
              <div class="text-sm text-gray-600">{{ userInfo?.email || '未设置邮箱' }}</div>
            </div>
            <ElTag :type="userInfo?.status === 1 ? 'success' : 'danger'">
              {{ userInfo?.status === 1 ? '正常' : '禁用' }}
            </ElTag>
          </div>
          <div class="text-right">
            <div class="text-sm text-gray-600">已启用限制</div>
            <div
              class="text-2xl font-bold"
              :class="getEnabledLimitationsCount() > 0 ? 'text-red-600' : 'text-green-600'"
            >
              {{ getEnabledLimitationsCount() }} / {{ functionLimitations.length }}
            </div>
          </div>
        </div>
      </ElCard>

      <!-- 功能限制设置 -->
      <div class="space-y-16px">
        <div v-for="(limitations, category) in limitationsByCategory" :key="category" class="space-y-8px">
          <ElCard>
            <template #header>
              <div class="flex items-center justify-between">
                <div class="flex items-center gap-8px">
                  <component
                    :is="`icon-${categoryIcons[category as keyof typeof categoryIcons]}`"
                    class="text-purple-500"
                  />
                  <span class="font-medium">{{ categoryNames[category as keyof typeof categoryNames] }}</span>
                </div>
                <div class="text-sm text-gray-600">
                  {{ limitations.filter(l => isLimitationEnabled(l.key)).length }} / {{ limitations.length }} 项限制
                </div>
              </div>
            </template>

            <div class="space-y-12px">
              <div
                v-for="limitation in limitations"
                :key="limitation.key"
                class="border rounded-8px p-12px transition-all duration-200"
                :class="{
                  'border-red-300 bg-red-50': isLimitationEnabled(limitation.key),
                  'border-gray-200 hover:border-gray-300': !isLimitationEnabled(limitation.key)
                }"
              >
                <div class="flex items-start gap-12px">
                  <ElCheckbox
                    :model-value="isLimitationEnabled(limitation.key)"
                    @change="toggleLimitation(limitation.key)"
                  />
                  <component :is="`icon-${limitation.icon}`" :class="`text-${limitation.color} text-20px`" />
                  <div class="flex-1">
                    <div class="mb-4px flex items-center justify-between">
                      <div class="font-medium">{{ limitation.name }}</div>
                      <ElTag size="small" :type="limitation.color">
                        {{
                          limitation.category === 'security'
                            ? '安全'
                            : limitation.category === 'finance'
                              ? '财务'
                              : '功能'
                        }}
                      </ElTag>
                    </div>
                    <div class="mb-8px text-sm text-gray-600">{{ limitation.description }}</div>

                    <!-- 限制值设置 -->
                    <div v-if="limitation.hasValue && isLimitationEnabled(limitation.key)" class="mt-8px">
                      <div class="mb-4px text-sm text-gray-700 font-medium">{{ limitation.valueLabel }}</div>
                      <ElInputNumber
                        v-if="limitation.valueType === 'number'"
                        :model-value="getLimitationValue(limitation.key)"
                        :min="limitation.valueMin"
                        :max="limitation.valueMax"
                        :step="1"
                        class="w-200px"
                        @update:model-value="setLimitationValue(limitation.key, $event)"
                      />
                      <ElInput
                        v-else-if="limitation.valueType === 'text'"
                        :model-value="getLimitationValue(limitation.key)"
                        :placeholder="limitation.placeholder"
                        class="w-300px"
                        @update:model-value="setLimitationValue(limitation.key, $event)"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </ElCard>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-between">
        <div class="flex items-center gap-8px">
          <ElButton @click="resetAllLimitations">
            <template #icon>
              <icon-ic-round-refresh />
            </template>
            重置所有限制
          </ElButton>
          <div class="text-sm text-gray-600">已启用 {{ getEnabledLimitationsCount() }} 项功能限制</div>
        </div>
        <div class="flex gap-8px">
          <ElButton @click="closeDialog">取消</ElButton>
          <ElButton type="primary" :loading="saving" @click="saveLimitations">
            <template #icon>
              <icon-ic-round-check />
            </template>
            保存设置
          </ElButton>
        </div>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.limitation-card {
  transition: all 0.2s ease;
}

.limitation-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.limitation-card.enabled {
  border-color: var(--el-color-danger);
  background-color: var(--el-color-danger-light-9);
}
</style>
