<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { request } from '@/service/request';
// RoleSelector组件已删除 - 改为单角色模式
import PermissionTree from '@/components/permission/PermissionTree.vue';

defineOptions({
  name: 'BatchUserPermissionManager'
});

interface Props {
  visible: boolean;
  selectedUserIds?: number[];
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  selectedUserIds: () => []
});

const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
const saving = ref(false);
const activeTab = ref('roles');

// 用户信息
const selectedUsers = ref<any[]>([]);
const userStats = ref({
  total: 0,
  withRoles: 0,
  withoutRoles: 0,
  avgPermissions: 0
});

// 批量角色操作
const batchRoleOperation = ref<'assign' | 'remove' | 'replace'>('assign');
const selectedRoles = ref<number[]>([]);
const availableRoles = ref<any[]>([]);

// 批量权限操作
const batchPermissionOperation = ref<'grant' | 'revoke' | 'replace'>('grant');
const selectedPermissions = ref<number[]>([]);
const permissionTree = ref<any[]>([]);

// 操作预览
const operationPreview = ref<any[]>([]);
const previewVisible = ref(false);

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
});

const hasSelectedUsers = computed(() => props.selectedUserIds.length > 0);

const canExecute = computed(() => {
  if (activeTab.value === 'roles') {
    return selectedRoles.value.length > 0;
  } else if (activeTab.value === 'permissions') {
    return selectedPermissions.value.length > 0;
  }
  return false;
});

// 获取选中用户信息
async function fetchSelectedUsers() {
  if (props.selectedUserIds.length === 0) return;

  loading.value = true;
  try {
    const userPromises = props.selectedUserIds.map(userId =>
      request({
        url: `/api/user/${userId}`,
        method: 'get'
      })
    );

    const users = await Promise.all(userPromises);
    selectedUsers.value = users.filter(user => user);

    // 计算用户统计
    calculateUserStats();
  } catch (error) {
    console.error('获取用户信息失败:', error);
    ElMessage.error('获取用户信息失败');
  } finally {
    loading.value = false;
  }
}

// 计算用户统计
function calculateUserStats() {
  const total = selectedUsers.value.length;
  let withRoles = 0;
  let totalPermissions = 0;

  selectedUsers.value.forEach(user => {
    if (user.roles && user.roles.length > 0) {
      withRoles++;
    }
    if (user.permissions) {
      totalPermissions += user.permissions.length;
    }
  });

  userStats.value = {
    total,
    withRoles,
    withoutRoles: total - withRoles,
    avgPermissions: total > 0 ? Math.round(totalPermissions / total) : 0
  };
}

// 获取可用角色
async function fetchAvailableRoles() {
  try {
    const response = await request({
      url: '/api/role/list',
      method: 'get',
      params: { limit: 100 }
    });

    if (response?.list) {
      availableRoles.value = response.list;
    }
  } catch (error) {
    console.error('获取角色列表失败:', error);
  }
}

// 获取权限树
async function fetchPermissionTree() {
  try {
    const response = await request({
      url: '/api/permission/tree',
      method: 'get'
    });

    if (Array.isArray(response)) {
      permissionTree.value = response;
    }
  } catch (error) {
    console.error('获取权限树失败:', error);
  }
}

// 生成操作预览
async function generatePreview() {
  loading.value = true;
  try {
    const preview: Array<{ type: string; action: string; target: any }> = [];

    for (const user of selectedUsers.value) {
      const userPreview: any = {
        userId: user.userId,
        username: user.username,
        currentRoles: user.roles || [],
        currentPermissions: user.permissions || [],
        changes: []
      };

      if (activeTab.value === 'roles') {
        // 角色操作预览
        switch (batchRoleOperation.value) {
          case 'assign':
            userPreview.changes = selectedRoles.value.map(roleId => ({
              type: 'role_assign',
              action: '分配角色',
              target: availableRoles.value.find(r => r.role_id === roleId)?.role_name
            }));
            break;
          case 'remove':
            userPreview.changes = selectedRoles.value.map(roleId => ({
              type: 'role_remove',
              action: '移除角色',
              target: availableRoles.value.find(r => r.role_id === roleId)?.role_name
            }));
            break;
          case 'replace':
            userPreview.changes = [
              {
                type: 'role_replace',
                action: '替换所有角色',
                target: `${selectedRoles.value.length} 个角色`
              }
            ];
            break;
        }
      } else if (activeTab.value === 'permissions') {
        // 权限操作预览
        switch (batchPermissionOperation.value) {
          case 'grant':
            userPreview.changes = [
              {
                type: 'permission_grant',
                action: '授予权限',
                target: `${selectedPermissions.value.length} 个权限`
              }
            ];
            break;
          case 'revoke':
            userPreview.changes = [
              {
                type: 'permission_revoke',
                action: '撤销权限',
                target: `${selectedPermissions.value.length} 个权限`
              }
            ];
            break;
          case 'replace':
            userPreview.changes = [
              {
                type: 'permission_replace',
                action: '替换所有权限',
                target: `${selectedPermissions.value.length} 个权限`
              }
            ];
            break;
        }
      }

      preview.push(userPreview);
    }

    operationPreview.value = preview;
    previewVisible.value = true;
  } catch (error) {
    console.error('生成预览失败:', error);
    ElMessage.error('生成预览失败');
  } finally {
    loading.value = false;
  }
}

// 执行批量操作
async function executeBatchOperation() {
  saving.value = true;
  try {
    let successCount = 0;
    let failCount = 0;
    const errors = [];

    for (const userId of props.selectedUserIds) {
      try {
        if (activeTab.value === 'roles') {
          // 批量角色操作
          switch (batchRoleOperation.value) {
            case 'assign':
              for (const roleId of selectedRoles.value) {
                await request({
                  url: '/api/user-role/assign',
                  method: 'post',
                  data: {
                    user_id: userId,
                    role_id: roleId,
                    grant_type: 'direct'
                  }
                });
              }
              break;
            case 'remove':
              for (const roleId of selectedRoles.value) {
                await request({
                  url: '/api/user-role/revoke',
                  method: 'post',
                  data: {
                    user_id: userId,
                    role_id: roleId
                  }
                });
              }
              break;
            case 'replace':
              // 先清除所有角色，再分配新角色
              await request({
                url: '/api/user-role/clear',
                method: 'post',
                data: { user_id: userId }
              });
              for (const roleId of selectedRoles.value) {
                await request({
                  url: '/api/user-role/assign',
                  method: 'post',
                  data: {
                    user_id: userId,
                    role_id: roleId,
                    grant_type: 'direct'
                  }
                });
              }
              break;
          }
        } else if (activeTab.value === 'permissions') {
          // 批量权限操作
          switch (batchPermissionOperation.value) {
            case 'grant':
              for (const permissionId of selectedPermissions.value) {
                await request({
                  url: '/api/user-permission/assign',
                  method: 'post',
                  data: {
                    user_id: userId,
                    permission_id: permissionId,
                    grant_type: 'grant'
                  }
                });
              }
              break;
            case 'revoke':
              for (const permissionId of selectedPermissions.value) {
                await request({
                  url: '/api/user-permission/revoke',
                  method: 'post',
                  data: {
                    user_id: userId,
                    permission_id: permissionId
                  }
                });
              }
              break;
            case 'replace':
              // 先清除所有直接权限，再分配新权限
              await request({
                url: '/api/user-permission/clear',
                method: 'post',
                data: { user_id: userId }
              });
              for (const permissionId of selectedPermissions.value) {
                await request({
                  url: '/api/user-permission/assign',
                  method: 'post',
                  data: {
                    user_id: userId,
                    permission_id: permissionId,
                    grant_type: 'grant'
                  }
                });
              }
              break;
          }
        }
        successCount++;
      } catch (error) {
        failCount++;
        errors.push(`用户 ${userId}: ${error}`);
      }
    }

    // 显示操作结果
    if (successCount > 0) {
      ElMessage.success(`批量操作完成：成功 ${successCount} 个，失败 ${failCount} 个`);
    }
    if (failCount > 0) {
      console.error('批量操作错误:', errors);
    }

    previewVisible.value = false;
    emit('success');
  } catch (error) {
    console.error('批量操作失败:', error);
    ElMessage.error('批量操作失败');
  } finally {
    saving.value = false;
  }
}

// 关闭对话框
function closeDialog() {
  emit('update:visible', false);
}

// 初始化数据
async function initData() {
  loading.value = true;
  try {
    await Promise.all([fetchSelectedUsers(), fetchAvailableRoles(), fetchPermissionTree()]);
  } finally {
    loading.value = false;
  }
}

// 监听对话框显示状态
watch(
  () => props.visible,
  visible => {
    if (visible && hasSelectedUsers.value) {
      initData();
    }
  }
);

// 监听选中用户变化
watch(
  () => props.selectedUserIds,
  () => {
    if (props.visible && hasSelectedUsers.value) {
      fetchSelectedUsers();
    }
  }
);
</script>

<script lang="ts">
// 获取变更类型的标签类型
type ChangeType =
  | 'role_assign'
  | 'role_remove'
  | 'role_replace'
  | 'permission_grant'
  | 'permission_revoke'
  | 'permission_replace';
function getChangeType(type: ChangeType): 'primary' | 'info' | 'success' | 'warning' | 'danger' {
  const typeMap: Record<ChangeType, 'primary' | 'info' | 'success' | 'warning' | 'danger'> = {
    role_assign: 'success',
    role_remove: 'danger',
    role_replace: 'warning',
    permission_grant: 'success',
    permission_revoke: 'danger',
    permission_replace: 'warning'
  };
  return typeMap[type];
}
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    title="批量用户权限管理"
    width="900px"
    :close-on-click-modal="false"
    destroy-on-close
    @close="closeDialog"
  >
    <div v-loading="loading">
      <!-- 用户统计概览 -->
      <ElCard class="mb-16px">
        <template #header>
          <span class="text-lg font-medium">选中用户概览</span>
        </template>
        <div class="grid grid-cols-2 gap-16px md:grid-cols-4">
          <div class="text-center">
            <div class="text-2xl text-primary font-bold">{{ userStats.total }}</div>
            <div class="text-sm text-gray-500">选中用户</div>
          </div>
          <div class="text-center">
            <div class="text-2xl text-success font-bold">{{ userStats.withRoles }}</div>
            <div class="text-sm text-gray-500">已分配角色</div>
          </div>
          <div class="text-center">
            <div class="text-2xl text-warning font-bold">{{ userStats.withoutRoles }}</div>
            <div class="text-sm text-gray-500">未分配角色</div>
          </div>
          <div class="text-center">
            <div class="text-2xl text-info font-bold">{{ userStats.avgPermissions }}</div>
            <div class="text-sm text-gray-500">平均权限数</div>
          </div>
        </div>
      </ElCard>

      <!-- 批量操作选项 -->
      <ElTabs v-model="activeTab">
        <!-- 批量角色操作 -->
        <ElTabPane label="批量角色操作" name="roles">
          <div class="space-y-16px">
            <!-- 操作类型选择 -->
            <div>
              <h4 class="text-md mb-8px font-medium">操作类型</h4>
              <ElRadioGroup v-model="batchRoleOperation">
                <ElRadio value="assign">分配角色</ElRadio>
                <ElRadio value="remove">移除角色</ElRadio>
                <ElRadio value="replace">替换角色</ElRadio>
              </ElRadioGroup>
              <div class="mt-4px text-sm text-gray-600">
                <span v-if="batchRoleOperation === 'assign'">为选中用户添加指定角色</span>
                <span v-else-if="batchRoleOperation === 'remove'">从选中用户中移除指定角色</span>
                <span v-else-if="batchRoleOperation === 'replace'">清除选中用户的所有角色，然后分配指定角色</span>
              </div>
            </div>

            <!-- 角色选择 -->
            <div>
              <h4 class="text-md mb-8px font-medium">选择角色</h4>
              <RoleSelector v-model="selectedRoles" :multiple="true" placeholder="请选择要操作的角色" />
            </div>
          </div>
        </ElTabPane>

        <!-- 批量权限操作 -->
        <ElTabPane label="批量权限操作" name="permissions">
          <div class="space-y-16px">
            <!-- 操作类型选择 -->
            <div>
              <h4 class="text-md mb-8px font-medium">操作类型</h4>
              <ElRadioGroup v-model="batchPermissionOperation">
                <ElRadio value="grant">授予权限</ElRadio>
                <ElRadio value="revoke">撤销权限</ElRadio>
                <ElRadio value="replace">替换权限</ElRadio>
              </ElRadioGroup>
              <div class="mt-4px text-sm text-gray-600">
                <span v-if="batchPermissionOperation === 'grant'">为选中用户添加指定权限</span>
                <span v-else-if="batchPermissionOperation === 'revoke'">从选中用户中撤销指定权限</span>
                <span v-else-if="batchPermissionOperation === 'replace'">
                  清除选中用户的所有直接权限，然后分配指定权限
                </span>
              </div>
            </div>

            <!-- 权限选择 -->
            <div>
              <h4 class="text-md mb-8px font-medium">选择权限</h4>
              <PermissionTree
                v-model="selectedPermissions"
                :data="permissionTree"
                show-checkbox
                check-strictly
                :default-expand-all="false"
              />
            </div>
          </div>
        </ElTabPane>
      </ElTabs>
    </div>

    <template #footer>
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-600">将对 {{ props.selectedUserIds.length }} 个用户执行批量操作</div>
        <div class="flex gap-12px">
          <ElButton @click="closeDialog">取消</ElButton>
          <ElButton
            type="primary"
            :disabled="!canExecute"
            @click="generatePreview"
          >
            预览操作
          </ElButton>
        </div>
      </div>
    </template>

    <!-- 操作预览对话框 -->
    <ElDialog v-model="previewVisible" title="批量操作预览" width="700px" :close-on-click-modal="false">
      <div class="space-y-16px">
        <div class="text-sm text-gray-600">以下是即将执行的批量操作，请确认后点击"执行操作"按钮</div>

        <ElTable :data="operationPreview" style="width: 100%" max-height="400px">
          <ElTableColumn prop="username" label="用户名" width="120" />
          <ElTableColumn label="操作内容">
            <template #default="{ row }">
              <div class="space-y-4px">
                <div v-for="change in row.changes" :key="change.type" class="text-sm">
                  <ElTag size="small" :type="getChangeType(change.type)">
                    {{ change.action }}
                  </ElTag>
                  <span class="ml-8px">{{ change.target }}</span>
                </div>
              </div>
            </template>
          </ElTableColumn>
        </ElTable>
      </div>

      <template #footer>
        <div class="flex justify-end gap-12px">
          <ElButton @click="previewVisible = false">取消</ElButton>
          <ElButton
            type="primary"
            :loading="saving"
            @click="executeBatchOperation"
          >
            执行操作
          </ElButton>
        </div>
      </template>
    </ElDialog>
  </ElDialog>
</template>

<style scoped>
/* 统计卡片样式 */
.grid-cols-2.md\:grid-cols-4 > div {
  padding: 12px;
  border-radius: 8px;
  background-color: var(--el-color-info-light-9);
  transition: all 0.3s ease;
}

.grid-cols-2.md\:grid-cols-4 > div:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .grid-cols-2.md\:grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
