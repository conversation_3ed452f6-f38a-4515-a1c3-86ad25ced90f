<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { request } from '@/service/request';

defineOptions({
  name: 'AdvancedUserFilter'
});

interface Props {
  visible: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'apply-filter', users: any[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
const searching = ref(false);

// 筛选条件
const filterConditions = ref({
  // 基础条件
  keyword: '',
  status: undefined as number | undefined,
  userRole: '',

  // 时间条件
  createTimeRange: [] as string[],
  lastLoginRange: [] as string[],

  // 角色条件
  hasRoles: undefined as boolean | undefined,
  roleIds: [] as number[],
  roleOperator: 'any' as 'any' | 'all' | 'none',

  // 权限条件
  hasPermissions: undefined as boolean | undefined,
  permissionIds: [] as number[],
  permissionOperator: 'any' as 'any' | 'all' | 'none',

  // 业务条件
  balanceRange: [0, 0] as [number, number],
  rateRange: [0, 1] as [number, number],
  orderCountRange: [0, 0] as [number, number],

  // 层级条件
  hasInviter: undefined as boolean | undefined,
  inviterIds: [] as number[],
  hasInvitees: undefined as boolean | undefined,
  inviteeCountRange: [0, 0] as [number, number]
});

// 筛选结果
const filteredUsers = ref<any[]>([]);
const filterStats = ref({
  total: 0,
  matched: 0,
  percentage: 0
});

// 保存的筛选条件
const savedFilters = ref<any[]>([]);
const currentFilterName = ref('');

// 可用选项
const availableRoles = ref<any[]>([]);
const availablePermissions = ref<any[]>([]);
const availableUsers = ref<any[]>([]);

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
});

const hasConditions = computed(() => {
  return Object.values(filterConditions.value).some(value => {
    if (Array.isArray(value)) {
      return value.length > 0;
    }
    return value !== undefined && value !== '' && value !== null;
  });
});

const canSearch = computed(() => hasConditions.value);

// 获取可用角色
async function fetchAvailableRoles() {
  try {
    const response = await request({
      url: '/api/role/list',
      method: 'get',
      params: { limit: 100 }
    });

    if (response?.list) {
      availableRoles.value = response.list;
    }
  } catch (error) {
    console.error('获取角色列表失败:', error);
  }
}

// 获取可用权限
async function fetchAvailablePermissions() {
  try {
    const response = await request({
      url: '/api/permission/list',
      method: 'get',
      params: { limit: 1000 }
    });

    if (response?.list) {
      availablePermissions.value = response.list;
    }
  } catch (error) {
    console.error('获取权限列表失败:', error);
  }
}

// 获取可用用户（用于邀请人选择）
async function fetchAvailableUsers() {
  try {
    const response = await request({
      url: '/api/user/list',
      method: 'get',
      params: { limit: 1000 }
    });

    if (response?.list) {
      availableUsers.value = response.list;
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
  }
}

// 执行筛选
async function executeFilter() {
  searching.value = true;
  try {
    const response = await request({
      url: '/api/user/advanced-filter',
      method: 'post',
      data: filterConditions.value
    });

    if (response?.users) {
      filteredUsers.value = response.users;
      filterStats.value = {
        total: response.total || 0,
        matched: response.users.length,
        percentage: response.total > 0 ? Math.round((response.users.length / response.total) * 100) : 0
      };
    }
  } catch (error) {
    console.error('筛选用户失败:', error);
    ElMessage.error('筛选用户失败');
  } finally {
    searching.value = false;
  }
}

// 应用筛选结果
function applyFilter() {
  emit('apply-filter', filteredUsers.value);
  emit('update:visible', false);
}

// 重置筛选条件
function resetFilter() {
  filterConditions.value = {
    keyword: '',
    status: undefined,
    userRole: '',
    createTimeRange: [],
    lastLoginRange: [],
    hasRoles: undefined,
    roleIds: [],
    roleOperator: 'any',
    hasPermissions: undefined,
    permissionIds: [],
    permissionOperator: 'any',
    balanceRange: [0, 0],
    rateRange: [0, 1],
    orderCountRange: [0, 0],
    hasInviter: undefined,
    inviterIds: [],
    hasInvitees: undefined,
    inviteeCountRange: [0, 0]
  };
  filteredUsers.value = [];
  filterStats.value = { total: 0, matched: 0, percentage: 0 };
}

// 保存筛选条件
async function saveFilter() {
  if (!currentFilterName.value) {
    ElMessage.warning('请输入筛选条件名称');
    return;
  }

  try {
    const filterData = {
      name: currentFilterName.value,
      conditions: filterConditions.value,
      create_time: new Date().toISOString()
    };

    await request({
      url: '/api/user/save-filter',
      method: 'post',
      data: filterData
    });

    ElMessage.success('筛选条件保存成功');
    currentFilterName.value = '';
    await fetchSavedFilters();
  } catch (error) {
    console.error('保存筛选条件失败:', error);
    ElMessage.error('保存筛选条件失败');
  }
}

// 获取保存的筛选条件
async function fetchSavedFilters() {
  try {
    const response = await request({
      url: '/api/user/saved-filters',
      method: 'get'
    });

    if (Array.isArray(response)) {
      savedFilters.value = response;
    }
  } catch (error) {
    console.error('获取保存的筛选条件失败:', error);
  }
}

// 加载保存的筛选条件
function loadSavedFilter(filter: any) {
  filterConditions.value = { ...filter.conditions };
  ElMessage.success(`已加载筛选条件：${filter.name}`);
}

// 删除保存的筛选条件
async function deleteSavedFilter(filterId: number) {
  try {
    await request({
      url: `/api/user/delete-filter/${filterId}`,
      method: 'delete'
    });

    ElMessage.success('筛选条件删除成功');
    await fetchSavedFilters();
  } catch (error) {
    console.error('删除筛选条件失败:', error);
    ElMessage.error('删除筛选条件失败');
  }
}

// 关闭对话框
function closeDialog() {
  emit('update:visible', false);
}

// 初始化数据
async function initData() {
  loading.value = true;
  try {
    await Promise.all([fetchAvailableRoles(), fetchAvailablePermissions(), fetchAvailableUsers(), fetchSavedFilters()]);
  } finally {
    loading.value = false;
  }
}

// 监听对话框显示状态
watch(
  () => props.visible,
  visible => {
    if (visible) {
      initData();
    }
  }
);
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    title="高级用户筛选"
    width="1000px"
    :close-on-click-modal="false"
    destroy-on-close
    @close="closeDialog"
  >
    <div v-loading="loading" class="space-y-16px">
      <!-- 筛选条件 -->
      <ElCard>
        <template #header>
          <div class="flex items-center justify-between">
            <span class="text-lg font-medium">筛选条件</span>
            <div class="flex gap-8px">
              <ElButton size="small" @click="resetFilter">重置</ElButton>
              <ElButton size="small" type="primary" :disabled="!canSearch" :loading="searching" @click="executeFilter">
                执行筛选
              </ElButton>
            </div>
          </div>
        </template>

        <ElTabs>
          <!-- 基础条件 -->
          <ElTabPane label="基础条件" name="basic">
            <div class="grid grid-cols-1 gap-16px md:grid-cols-2">
              <ElFormItem label="关键词">
                <ElInput v-model="filterConditions.keyword" placeholder="用户名、邮箱、手机号" clearable />
              </ElFormItem>

              <ElFormItem label="用户状态">
                <ElSelect v-model="filterConditions.status" placeholder="请选择状态" clearable>
                  <ElOption label="正常" :value="1" />
                  <ElOption label="禁用" :value="0" />
                </ElSelect>
              </ElFormItem>

              <ElFormItem label="注册时间">
                <ElDatePicker
                  v-model="filterConditions.createTimeRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  style="width: 100%"
                />
              </ElFormItem>

              <ElFormItem label="最后登录">
                <ElDatePicker
                  v-model="filterConditions.lastLoginRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  style="width: 100%"
                />
              </ElFormItem>
            </div>
          </ElTabPane>

          <!-- 角色条件 -->
          <ElTabPane label="角色条件" name="roles">
            <div class="space-y-16px">
              <ElFormItem label="角色状态">
                <ElRadioGroup v-model="filterConditions.hasRoles">
                  <ElRadio :value="undefined">不限</ElRadio>
                  <ElRadio :value="true">已分配角色</ElRadio>
                  <ElRadio :value="false">未分配角色</ElRadio>
                </ElRadioGroup>
              </ElFormItem>

              <ElFormItem label="角色匹配">
                <ElRadioGroup v-model="filterConditions.roleOperator">
                  <ElRadio value="any">包含任一角色</ElRadio>
                  <ElRadio value="all">包含所有角色</ElRadio>
                  <ElRadio value="none">不包含任何角色</ElRadio>
                </ElRadioGroup>
              </ElFormItem>

              <ElFormItem label="指定角色">
                <ElSelect v-model="filterConditions.roleIds" multiple placeholder="请选择角色" style="width: 100%">
                  <ElOption
                    v-for="role in availableRoles"
                    :key="role.role_id"
                    :label="role.role_name"
                    :value="role.role_id"
                  />
                </ElSelect>
              </ElFormItem>
            </div>
          </ElTabPane>

          <!-- 权限条件 -->
          <ElTabPane label="权限条件" name="permissions">
            <div class="space-y-16px">
              <ElFormItem label="权限状态">
                <ElRadioGroup v-model="filterConditions.hasPermissions">
                  <ElRadio :value="undefined">不限</ElRadio>
                  <ElRadio :value="true">有直接权限</ElRadio>
                  <ElRadio :value="false">无直接权限</ElRadio>
                </ElRadioGroup>
              </ElFormItem>

              <ElFormItem label="权限匹配">
                <ElRadioGroup v-model="filterConditions.permissionOperator">
                  <ElRadio value="any">包含任一权限</ElRadio>
                  <ElRadio value="all">包含所有权限</ElRadio>
                  <ElRadio value="none">不包含任何权限</ElRadio>
                </ElRadioGroup>
              </ElFormItem>

              <ElFormItem label="指定权限">
                <ElSelect
                  v-model="filterConditions.permissionIds"
                  multiple
                  placeholder="请选择权限"
                  style="width: 100%"
                  filterable
                >
                  <ElOption
                    v-for="permission in availablePermissions"
                    :key="permission.permission_id"
                    :label="permission.permission_name"
                    :value="permission.permission_id"
                  />
                </ElSelect>
              </ElFormItem>
            </div>
          </ElTabPane>

          <!-- 业务条件 -->
          <ElTabPane label="业务条件" name="business">
            <div class="grid grid-cols-1 gap-16px md:grid-cols-2">
              <ElFormItem label="余额范围">
                <ElSlider v-model="filterConditions.balanceRange" range :min="0" :max="10000" :step="100" show-input />
              </ElFormItem>

              <ElFormItem label="费率范围">
                <ElSlider v-model="filterConditions.rateRange" range :min="0" :max="1" :step="0.01" show-input />
              </ElFormItem>

              <ElFormItem label="订单数量">
                <ElSlider v-model="filterConditions.orderCountRange" range :min="0" :max="1000" :step="10" show-input />
              </ElFormItem>

              <ElFormItem label="邀请人数">
                <ElSlider v-model="filterConditions.inviteeCountRange" range :min="0" :max="100" :step="1" show-input />
              </ElFormItem>
            </div>
          </ElTabPane>
        </ElTabs>
      </ElCard>

      <!-- 筛选结果 -->
      <ElCard v-if="filterStats.matched > 0">
        <template #header>
          <div class="flex items-center justify-between">
            <span class="text-lg font-medium">筛选结果</span>
            <div class="flex gap-8px">
              <ElTag type="primary">{{ filterStats.matched }} / {{ filterStats.total }} 用户</ElTag>
              <ElTag type="success">{{ filterStats.percentage }}% 匹配</ElTag>
            </div>
          </div>
        </template>

        <ElTable :data="filteredUsers.slice(0, 10)" style="width: 100%" max-height="300px">
          <ElTableColumn prop="username" label="用户名" width="120" />
          <ElTableColumn prop="email" label="邮箱" width="180" />
          <ElTableColumn prop="status" label="状态" width="80">
            <template #default="{ row }">
              <ElTag :type="row.status === 1 ? 'success' : 'danger'">
                {{ row.status === 1 ? '正常' : '禁用' }}
              </ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="role_count" label="角色数" width="80" />
          <ElTableColumn prop="permission_count" label="权限数" width="80" />
          <ElTableColumn prop="create_time" label="注册时间" width="160" />
        </ElTable>

        <div v-if="filteredUsers.length > 10" class="mt-12px text-center text-gray-500">
          还有 {{ filteredUsers.length - 10 }} 个用户未显示...
        </div>
      </ElCard>

      <!-- 保存的筛选条件 -->
      <ElCard>
        <template #header>
          <span class="text-lg font-medium">保存的筛选条件</span>
        </template>

        <div class="space-y-12px">
          <!-- 保存当前条件 -->
          <div class="flex gap-8px">
            <ElInput v-model="currentFilterName" placeholder="输入筛选条件名称" style="width: 200px" />
            <ElButton type="primary" :disabled="!hasConditions || !currentFilterName" @click="saveFilter">
              保存当前条件
            </ElButton>
          </div>

          <!-- 已保存的条件列表 -->
          <div v-if="savedFilters.length > 0" class="space-y-8px">
            <div
              v-for="filter in savedFilters"
              :key="filter.id"
              class="flex items-center justify-between border border-gray-200 rounded-8px p-12px"
            >
              <div>
                <div class="font-medium">{{ filter.name }}</div>
                <div class="text-sm text-gray-500">{{ filter.create_time }}</div>
              </div>
              <div class="flex gap-8px">
                <ElButton size="small" @click="loadSavedFilter(filter)">加载</ElButton>
                <ElButton size="small" type="danger" @click="deleteSavedFilter(filter.id)">删除</ElButton>
              </div>
            </div>
          </div>
          <div v-else class="py-16px text-center text-gray-500">暂无保存的筛选条件</div>
        </div>
      </ElCard>
    </div>

    <template #footer>
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-600">
          <span v-if="filterStats.matched > 0">已筛选出 {{ filterStats.matched }} 个用户</span>
          <span v-else-if="hasConditions">请点击"执行筛选"查看结果</span>
          <span v-else>请设置筛选条件</span>
        </div>
        <div class="flex gap-12px">
          <ElButton @click="closeDialog">取消</ElButton>
          <ElButton type="primary" :disabled="filterStats.matched === 0" @click="applyFilter">应用筛选结果</ElButton>
        </div>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
/* 表单项样式 */
.el-form-item {
  margin-bottom: 16px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .grid-cols-1.md\:grid-cols-2 {
    grid-template-columns: 1fr;
  }
}
</style>
