<template>
  <ElDrawer v-model="visibleProxy" :append-to-body="true" size="100%">
    <template #header>
      <div class="flex items-center justify-between pr-8px">
        <div class="font-medium">用户权限预览</div>
        <ElButton size="small" @click="refresh">刷新</ElButton>
      </div>
    </template>

    <div class="p-8px">
      <UserPermissionPreview v-if="userId" :user-id="userId" :show-header="false" />
    </div>
  </ElDrawer>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import UserPermissionPreview from '@/components/permission/UserPermissionPreview.vue';

interface Props {
  modelValue: boolean;
  userId?: number | null;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  userId: null
});

const emit = defineEmits<{
  (e: 'update:modelValue', v: boolean): void;
}>();

const visibleProxy = computed({
  get: () => props.modelValue,
  set: v => emit('update:modelValue', v)
});

function refresh() {
  // 让子组件自行处理刷新，这里不做强制逻辑
}
</script>

