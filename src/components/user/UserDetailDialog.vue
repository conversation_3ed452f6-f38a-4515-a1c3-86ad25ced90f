<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { request } from '@/service/request';
// UserPermissionManager组件已删除 - 改为单角色模式
import PermissionAssignWizard from '@/components/permission/PermissionAssignWizard.vue';
import PermissionInheritanceViewer from '@/components/permission/PermissionInheritanceViewer.vue';

defineOptions({
  name: 'UserDetailDialog'
});

interface Props {
  visible: boolean;
  userId?: number;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'edit-user', userId: number): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  userId: 0
});

const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
const userInfo = ref<any>({});
// 多角色数据已删除 - 改为单角色模式
const userPermissions = ref<string[]>([]);
const operationLogs = ref<any[]>([]);
const userStats = ref<any>({});
const inviteeList = ref<any[]>([]);
const orderHistory = ref<any[]>([]);
const activeTab = ref('basic');
const wizardVisible = ref(false);
const inheritanceViewerVisible = ref(false);
const statsLoading = ref(false);
const logsLoading = ref(false);

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
});

// 获取用户详细信息
async function fetchUserDetail() {
  if (!props.userId) return;

  loading.value = true;
  try {
    // 获取用户基本信息
    const userResponse = await request({
      url: `/api/user/${props.userId}`,
      method: 'get'
    });

    if (userResponse) {
      // 处理数据格式，确保字段匹配
      userInfo.value = {
        ...userResponse,
        userRate: Number.parseFloat(userResponse.rate || '1.0'),
        balance: Number.parseFloat(userResponse.balance || '0'),
        totalRecharge: Number.parseFloat(userResponse.totalRecharge || '0'),
        totalConsumption: Number.parseFloat(userResponse.totalConsumption || '0'),
        email: userResponse.email || '',
        phone: userResponse.phone || ''
      };
    }

    // 多角色获取逻辑已删除 - 改为单角色模式
  } catch (error) {
    console.error('获取用户详情失败:', error);
    ElMessage.error('获取用户详情失败');
  } finally {
    loading.value = false;
  }
}

// 获取操作日志
async function fetchOperationLogs() {
  logsLoading.value = true;
  try {
    const response = await request({
      url: `/api/log/user/${props.userId}`,
      method: 'get',
      params: { limit: 50 }
    });

    if (Array.isArray(response)) {
      operationLogs.value = response;
    }
  } catch (error) {
    console.error('获取操作日志失败:', error);
  } finally {
    logsLoading.value = false;
  }
}

// 获取用户统计信息
async function fetchUserStats() {
  statsLoading.value = true;
  try {
    const response = await request({
      url: `/api/user/stats/${props.userId}`,
      method: 'get'
    });

    if (response) {
      userStats.value = response;
    }
  } catch (error) {
    console.error('获取用户统计失败:', error);
    // 设置默认统计数据
    userStats.value = {
      totalOrders: 0,
      completedOrders: 0,
      totalAmount: 0,
      totalRecharge: 0,
      inviteeCount: 0,
      lastLoginTime: null,
      registerDays: 0
    };
  } finally {
    statsLoading.value = false;
  }
}

// 获取下级用户列表
async function fetchInviteeList() {
  try {
    const response = await request({
      url: `/api/user/invitees/${props.userId}`,
      method: 'get',
      params: { limit: 10 }
    });

    if (Array.isArray(response)) {
      inviteeList.value = response;
    }
  } catch (error) {
    console.error('获取下级用户失败:', error);
  }
}

// 获取订单历史
async function fetchOrderHistory() {
  try {
    const response = await request({
      url: `/api/order/user/${props.userId}`,
      method: 'get',
      params: { limit: 10 }
    });

    if (Array.isArray(response)) {
      orderHistory.value = response;
    }
  } catch (error) {
    console.error('获取订单历史失败:', error);
  }
}

// 关闭弹窗
function closeDialog() {
  dialogVisible.value = false;
}

// 编辑用户
function editUser() {
  if (props.userId) {
    emit('edit-user', props.userId);
    closeDialog();
  }
}

// 打开权限分配向导
function openPermissionWizard() {
  wizardVisible.value = true;
}

// 权限分配成功回调
function onPermissionAssignSuccess() {
  fetchUserDetail(); // 刷新用户详情
}

// 获取角色类型
function getRoleType(roleCode: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    super_admin: 'danger',
    admin: 'warning',
    manager: 'success',
    agent: 'info',
    vip: 'primary',
    user: 'info'
  };
  return typeMap[roleCode] || 'info';
}

// 获取用户状态
function getUserStatus(status: number): 'success' | 'danger' {
  return status === 1 ? 'success' : 'danger';
}

function getUserStatusText(status: number) {
  return status === 1 ? '正常' : '禁用';
}

// 打开权限继承查看器
function openInheritanceViewer() {
  inheritanceViewerVisible.value = true;
}

// 权限冲突解决回调
function onConflictResolved() {
  // 刷新用户权限数据
  fetchUserDetail();
}

// 格式化时间
function formatTime(time: string) {
  return new Date(time).toLocaleString('zh-CN');
}

// 格式化货币
function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount || 0);
}

// 获取相对时间
function getRelativeTime(dateTime: string | Date): string {
  if (!dateTime) return '';
  const now = new Date();
  const date = new Date(dateTime);
  const diff = now.getTime() - date.getTime();

  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (days > 0) return `${days}天前`;
  if (hours > 0) return `${hours}小时前`;
  if (minutes > 0) return `${minutes}分钟前`;
  return '刚刚';
}

// 获取注册天数
function getRegisterDays(createTime: string): number {
  if (!createTime) return 0;
  const now = new Date();
  const create = new Date(createTime);
  const diff = now.getTime() - create.getTime();
  return Math.floor(diff / (1000 * 60 * 60 * 24));
}

// 获取用户等级
function getUserLevel(userInfo: any): {
  level: string;
  color: 'primary' | 'success' | 'warning' | 'danger' | 'info';
  description: string;
} {
  const rate = userInfo.userRate || userInfo.price_rate || 1.0;

  if (rate <= 0.2) {
    return { level: 'VIP', color: 'danger', description: '最高等级用户，享受最低价格' };
  }
  if (rate <= 0.5) {
    return { level: '代理商', color: 'warning', description: '代理商等级，享受代理价格' };
  }
  if (rate <= 0.8) {
    return { level: '会员', color: 'success', description: '会员等级，享受会员价格' };
  }
  return { level: '普通用户', color: 'info', description: '标准用户，按原价购买' };
}

// 获取在线状态
function getOnlineStatus(lastLoginTime: string | Date): { status: string; color: string; text: string } {
  if (!lastLoginTime) {
    return { status: 'offline', color: 'gray', text: '从未登录' };
  }

  const now = new Date();
  const lastLogin = new Date(lastLoginTime);
  const diff = now.getTime() - lastLogin.getTime();
  const minutes = Math.floor(diff / (1000 * 60));

  if (minutes <= 5) {
    return { status: 'online', color: 'green', text: '在线' };
  }
  if (minutes <= 30) {
    return { status: 'away', color: 'yellow', text: '最近活跃' };
  }
  return { status: 'offline', color: 'gray', text: `最后登录: ${getRelativeTime(lastLoginTime)}` };
}

// 复制到剪贴板
async function copyToClipboard(text: string) {
  try {
    await navigator.clipboard.writeText(text);
    ElMessage.success('已复制到剪贴板');
  } catch (error) {
    console.error('复制失败:', error);
    ElMessage.error('复制失败');
  }
}

// 获取订单状态类型
function getOrderStatusType(status: number): 'primary' | 'success' | 'warning' | 'danger' | 'info' {
  const typeMap: Record<number, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    0: 'info', // 待处理
    1: 'warning', // 处理中
    2: 'success', // 已完成
    3: 'danger', // 已取消
    4: 'danger' // 失败
  };
  return typeMap[status] || 'info';
}

// 获取订单状态文本
function getOrderStatusText(status: number): string {
  const textMap: Record<number, string> = {
    0: '待处理',
    1: '处理中',
    2: '已完成',
    3: '已取消',
    4: '失败'
  };
  return textMap[status] || '未知';
}

// 获取日志类型
function getLogType(type: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    login: 'success',
    logout: 'info',
    order: 'primary',
    payment: 'warning',
    error: 'danger'
  };
  return typeMap[type] || 'info';
}

// 获取设备信息
function getDeviceInfo(userAgent: string): string {
  if (!userAgent) return '未知设备';

  if (userAgent.includes('Mobile')) return '移动设备';
  if (userAgent.includes('Chrome')) return 'Chrome浏览器';
  if (userAgent.includes('Firefox')) return 'Firefox浏览器';
  if (userAgent.includes('Safari')) return 'Safari浏览器';
  if (userAgent.includes('Edge')) return 'Edge浏览器';

  return '桌面设备';
}

// 监听userId变化
watch(
  () => props.userId,
  newUserId => {
    if (newUserId && props.visible) {
      fetchUserDetail();
      fetchUserStats();
      fetchInviteeList();
      fetchOrderHistory();
    }
  }
);

// 监听visible变化
watch(
  () => props.visible,
  newVisible => {
    if (newVisible && props.userId) {
      fetchUserDetail();
      fetchUserStats();
      fetchInviteeList();
      fetchOrderHistory();
    }
  }
);

// 监听activeTab变化，按需加载数据
watch(activeTab, newTab => {
  if (!props.userId || !props.visible) return;

  switch (newTab) {
    case 'logs':
      if (operationLogs.value.length === 0) {
        fetchOperationLogs();
      }
      break;
    case 'invitees':
      if (inviteeList.value.length === 0) {
        fetchInviteeList();
      }
      break;
    case 'orders':
      if (orderHistory.value.length === 0) {
        fetchOrderHistory();
      }
      break;
  }
});
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    :title="`用户详情 - ${userInfo.username || '未知用户'}`"
    width="900px"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <template #header>
      <div class="flex items-center justify-between">
        <span class="text-lg font-medium">用户详情</span>
        <div class="flex gap-8px">
          <ElButton @click="openPermissionWizard">权限分配</ElButton>
          <ElButton type="primary" @click="editUser">编辑用户</ElButton>
        </div>
      </div>
    </template>

    <!-- 用户信息内容 -->
    <div v-loading="loading" class="space-y-16px">
      <ElTabs v-model="activeTab">
        <!-- 基本信息 -->
        <ElTabPane label="基本信息" name="basic">
          <!-- 用户头像和基本信息 -->
          <div class="mb-24px">
            <ElCard shadow="never" class="border-0 from-blue-50 to-purple-50 bg-gradient-to-r">
              <div class="flex items-center gap-24px">
                <div class="relative">
                  <ElAvatar :size="100" :src="userInfo.avatar">
                    {{ userInfo.username?.charAt(0)?.toUpperCase() }}
                  </ElAvatar>
                  <!-- 在线状态指示器 -->
                  <div
                    class="absolute bottom-2 right-2 h-6 w-6 border-2 border-white rounded-full"
                    :class="`bg-${getOnlineStatus(userInfo.lastLoginTime).color}-500`"
                    :title="getOnlineStatus(userInfo.lastLoginTime).text"
                  ></div>
                </div>

                <div class="flex-1">
                  <div class="mb-8px flex items-center gap-12px">
                    <h3 class="text-2xl text-gray-800 font-bold">{{ userInfo.username }}</h3>
                    <ElTag :type="getUserStatus(userInfo.status)" size="large">
                      {{ getUserStatusText(userInfo.status) }}
                    </ElTag>
                  </div>

                  <div class="mb-12px flex items-center gap-12px">
                    <ElTag :type="getUserLevel(userInfo).color" size="large" effect="dark">
                      {{ getUserLevel(userInfo).level }}
                    </ElTag>
                    <span class="text-sm text-gray-600">{{ getUserLevel(userInfo).description }}</span>
                  </div>

                  <div class="flex items-center gap-16px text-sm text-gray-600">
                    <div class="flex items-center gap-4px">
                      <span>👤 ID: {{ userInfo.userId }}</span>
                    </div>
                    <div class="flex items-center gap-4px">
                      <span>📅 注册 {{ getRegisterDays(userInfo.createTime) }} 天</span>
                    </div>
                    <div class="flex items-center gap-4px">
                      <span>🕒 {{ getOnlineStatus(userInfo.lastLoginTime).text }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </ElCard>
          </div>

          <!-- 统计卡片 -->
          <div class="grid grid-cols-2 mb-24px gap-16px md:grid-cols-4">
            <ElCard shadow="hover" class="text-center">
              <div class="flex flex-col items-center">
                <div class="mb-8px text-3xl">💰</div>
                <div class="text-2xl text-green-600 font-bold">¥{{ formatCurrency(userInfo.balance || 0) }}</div>
                <div class="text-sm text-gray-500">账户余额</div>
              </div>
            </ElCard>

            <ElCard shadow="hover" class="text-center">
              <div class="flex flex-col items-center">
                <div class="mb-8px text-3xl">📊</div>
                <div class="text-2xl text-blue-600 font-bold">
                  {{ ((userInfo.userRate || userInfo.rate || 1.0) * 100).toFixed(1) }}%
                </div>
                <div class="text-sm text-gray-500">费率</div>
              </div>
            </ElCard>

            <ElCard v-loading="statsLoading" shadow="hover" class="text-center">
              <div class="flex flex-col items-center">
                <div class="mb-8px text-3xl">📦</div>
                <div class="text-2xl text-purple-600 font-bold">
                  {{ userInfo.orderCount || 0 }}
                </div>
                <div class="text-sm text-gray-500">总订单</div>
              </div>
            </ElCard>

            <ElCard v-loading="statsLoading" shadow="hover" class="text-center">
              <div class="flex flex-col items-center">
                <div class="mb-8px text-3xl">👥</div>
                <div class="text-2xl text-orange-600 font-bold">
                  {{ userInfo.inviteeCount || 0 }}
                </div>
                <div class="text-sm text-gray-500">下级用户</div>
              </div>
            </ElCard>
          </div>

          <!-- 详细信息 -->
          <div class="grid grid-cols-1 gap-24px lg:grid-cols-2">
            <!-- 基本信息 -->
            <ElCard shadow="never" header="基本信息">
              <ElDescriptions :column="1" border>
                <ElDescriptionsItem label="用户名">
                  <div class="flex items-center gap-8px">
                    <span>{{ userInfo.username }}</span>
                    <ElButton size="small" type="primary" link @click="copyToClipboard(userInfo.username)">📋</ElButton>
                  </div>
                </ElDescriptionsItem>
                <ElDescriptionsItem label="邮箱">
                  <div v-if="userInfo.email" class="flex items-center gap-8px">
                    <span>{{ userInfo.email }}</span>
                    <ElButton size="small" type="primary" link @click="copyToClipboard(userInfo.email)">📋</ElButton>
                  </div>
                  <span v-else class="text-gray-400">未设置</span>
                </ElDescriptionsItem>
                <ElDescriptionsItem label="手机号">
                  <span>{{ userInfo.phone || '未设置' }}</span>
                </ElDescriptionsItem>
                <ElDescriptionsItem label="注册时间">
                  <div v-if="userInfo.createTime">
                    <div>{{ formatTime(userInfo.createTime) }}</div>
                    <div class="text-xs text-gray-500">{{ getRelativeTime(userInfo.createTime) }}</div>
                  </div>
                  <span v-else>-</span>
                </ElDescriptionsItem>
                <ElDescriptionsItem label="最后登录">
                  <div v-if="userInfo.lastLoginTime">
                    <div>{{ formatTime(userInfo.lastLoginTime) }}</div>
                    <div class="text-xs text-gray-500">{{ getRelativeTime(userInfo.lastLoginTime) }}</div>
                  </div>
                  <span v-else class="text-gray-400">从未登录</span>
                </ElDescriptionsItem>
              </ElDescriptions>
            </ElCard>

            <!-- 账户信息 -->
            <ElCard shadow="never" header="账户信息">
              <ElDescriptions :column="1" border>
                <ElDescriptionsItem label="邀请码">
                  <div v-if="userInfo.inviteCode" class="flex items-center gap-8px">
                    <ElTag type="success">{{ userInfo.inviteCode }}</ElTag>
                    <ElButton size="small" type="primary" link @click="copyToClipboard(userInfo.inviteCode)">
                      📋
                    </ElButton>
                  </div>
                  <span v-else class="text-gray-400">未设置</span>
                </ElDescriptionsItem>
                <ElDescriptionsItem label="邀请人">
                  <span>{{ userInfo.inviterName || '无' }}</span>
                </ElDescriptionsItem>
                <ElDescriptionsItem v-loading="statsLoading" label="总充值金额">
                  <span class="text-green-600 font-medium">¥{{ formatCurrency(userStats.totalRecharge || 0) }}</span>
                </ElDescriptionsItem>
                <ElDescriptionsItem v-loading="statsLoading" label="总消费金额">
                  <span class="text-blue-600 font-medium">¥{{ formatCurrency(userStats.totalAmount || 0) }}</span>
                </ElDescriptionsItem>
                <ElDescriptionsItem v-loading="statsLoading" label="完成订单">
                  <div class="flex items-center gap-8px">
                    <span>{{ userStats.completedOrders || 0 }}</span>
                    <span class="text-gray-400">/</span>
                    <span>{{ userStats.totalOrders || 0 }}</span>
                    <ElTag
                      v-if="userStats.totalOrders > 0"
                      size="small"
                      :type="userStats.completedOrders / userStats.totalOrders > 0.8 ? 'success' : 'warning'"
                    >
                      {{ ((userStats.completedOrders / userStats.totalOrders) * 100).toFixed(1) }}%
                    </ElTag>
                  </div>
                </ElDescriptionsItem>
              </ElDescriptions>
            </ElCard>
          </div>
        </ElTabPane>

        <!-- 权限管理 -->
        <ElTabPane label="权限管理" name="permissions">
          <UserPermissionManager :user-id="props.userId" @refresh="fetchUserDetail" />
        </ElTabPane>

        <!-- 下级用户 -->
        <ElTabPane label="下级用户" name="invitees">
          <ElCard shadow="never">
            <template #header>
              <div class="flex items-center justify-between">
                <span>下级用户列表</span>
                <ElTag type="info">共 {{ userStats.inviteeCount || 0 }} 人</ElTag>
              </div>
            </template>

            <ElTable :data="inviteeList" style="width: 100%" max-height="400px">
              <ElTableColumn prop="username" label="用户名" width="120" />
              <ElTableColumn prop="email" label="邮箱" width="180" show-overflow-tooltip />
              <ElTableColumn prop="balance" label="余额" width="100">
                <template #default="{ row }">¥{{ formatCurrency(row.balance) }}</template>
              </ElTableColumn>
              <ElTableColumn prop="status" label="状态" width="80">
                <template #default="{ row }">
                  <ElTag :type="row.status === 1 ? 'success' : 'danger'">
                    {{ row.status === 1 ? '正常' : '禁用' }}
                  </ElTag>
                </template>
              </ElTableColumn>
              <ElTableColumn prop="createTime" label="注册时间" width="160">
                <template #default="{ row }">
                  {{ formatTime(row.createTime) }}
                </template>
              </ElTableColumn>
            </ElTable>

            <div v-if="inviteeList.length === 0" class="py-32px text-center text-gray-500">暂无下级用户</div>
          </ElCard>
        </ElTabPane>

        <!-- 订单历史 -->
        <ElTabPane label="订单历史" name="orders">
          <ElCard shadow="never">
            <template #header>
              <div class="flex items-center justify-between">
                <span>最近订单</span>
                <ElTag type="info">共 {{ userStats.totalOrders || 0 }} 单</ElTag>
              </div>
            </template>

            <ElTable :data="orderHistory" style="width: 100%" max-height="400px">
              <ElTableColumn prop="orderNo" label="订单号" width="160" />
              <ElTableColumn prop="courseName" label="课程名称" show-overflow-tooltip />
              <ElTableColumn prop="amount" label="金额" width="100">
                <template #default="{ row }">¥{{ formatCurrency(row.amount) }}</template>
              </ElTableColumn>
              <ElTableColumn prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <ElTag :type="getOrderStatusType(row.status)">
                    {{ getOrderStatusText(row.status) }}
                  </ElTag>
                </template>
              </ElTableColumn>
              <ElTableColumn prop="createTime" label="创建时间" width="160">
                <template #default="{ row }">
                  {{ formatTime(row.createTime) }}
                </template>
              </ElTableColumn>
            </ElTable>

            <div v-if="orderHistory.length === 0" class="py-32px text-center text-gray-500">暂无订单记录</div>
          </ElCard>
        </ElTabPane>

        <!-- 操作日志 -->
        <ElTabPane label="操作日志" name="logs">
          <ElCard v-loading="logsLoading" shadow="never">
            <template #header>
              <div class="flex items-center justify-between">
                <span>最近操作记录</span>
                <ElButton size="small" @click="fetchOperationLogs">刷新</ElButton>
              </div>
            </template>

            <ElTable :data="operationLogs" style="width: 100%" max-height="400px">
              <ElTableColumn prop="operation_type" label="操作类型" width="120">
                <template #default="{ row }">
                  <ElTag size="small" :type="getLogType(row.operation_type)">
                    {{ row.operation_type }}
                  </ElTag>
                </template>
              </ElTableColumn>
              <ElTableColumn prop="operation_desc" label="操作描述" show-overflow-tooltip />
              <ElTableColumn prop="ip_address" label="IP地址" width="120" />
              <ElTableColumn prop="user_agent" label="设备信息" width="150" show-overflow-tooltip>
                <template #default="{ row }">
                  <ElTooltip :content="row.user_agent" placement="top">
                    <span>{{ getDeviceInfo(row.user_agent) }}</span>
                  </ElTooltip>
                </template>
              </ElTableColumn>
              <ElTableColumn prop="create_time" label="操作时间" width="160">
                <template #default="{ row }">
                  <div>
                    <div>{{ formatTime(row.create_time) }}</div>
                    <div class="text-xs text-gray-500">{{ getRelativeTime(row.create_time) }}</div>
                  </div>
                </template>
              </ElTableColumn>
            </ElTable>

            <div v-if="operationLogs.length === 0" class="py-32px text-center text-gray-500">暂无操作记录</div>
          </ElCard>
        </ElTabPane>
      </ElTabs>
    </div>

    <template #footer>
      <div class="flex items-center justify-between">
        <div class="flex gap-8px">
          <ElButton @click="openInheritanceViewer">权限继承分析</ElButton>
        </div>
        <div class="flex gap-12px">
          <ElButton @click="closeDialog">关闭</ElButton>
          <ElButton type="primary" @click="editUser">编辑用户</ElButton>
        </div>
      </div>
    </template>
  </ElDialog>

  <!-- 权限分配向导 -->
  <PermissionAssignWizard
    v-model:visible="wizardVisible"
    :user-id="props.userId"
    @success="onPermissionAssignSuccess"
  />

  <!-- 权限继承查看器 -->
  <PermissionInheritanceViewer
    v-model:visible="inheritanceViewerVisible"
    :user-id="props.userId"
    @resolve-conflict="onConflictResolved"
  />
</template>

<style scoped>
/* 移动端适配 */
@media (max-width: 768px) {
  .grid-cols-1.md\:grid-cols-2 {
    grid-template-columns: 1fr;
  }
}
</style>
