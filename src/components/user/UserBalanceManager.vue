<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { request } from '@/service/request';

interface Props {
  visible: boolean;
  userId: number;
  userInfo?: any;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'success'): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  userId: 0,
  userInfo: undefined
});

const emit = defineEmits<Emits>();

// 响应式数据
const loading = ref(false);
const processing = ref(false);
const balanceRecords = ref<any[]>([]);
const currentBalance = ref<number>(0);
const operationType = ref<'recharge' | 'deduct'>('recharge');
const operationAmount = ref<number>(0);
const operationRemark = ref<string>('');

// 余额记录分页
const pagination = ref({
  current: 1,
  size: 10,
  total: 0
});

// 计算属性
const dialogVisible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
});

const operationTitle = computed(() => {
  return operationType.value === 'recharge' ? '充值' : '扣费';
});

const operationColor = computed(() => {
  return operationType.value === 'recharge' ? 'success' : 'warning';
});

const operationIcon = computed(() => {
  return operationType.value === 'recharge' ? 'ic:round-add' : 'ic:round-remove';
});

// 获取用户余额信息
async function fetchUserBalance() {
  if (!props.userId) return;

  loading.value = true;
  try {
    const response = await request({
      url: `/api/user/${props.userId}`,
      method: 'get'
    });

    if (response && response.data) {
      currentBalance.value = response.data.balance || 0;
    }
  } catch (error) {
    console.error('获取用户余额失败:', error);
    ElMessage.error('获取用户余额失败');
  } finally {
    loading.value = false;
  }
}

// 获取余额记录
async function fetchBalanceRecords() {
  if (!props.userId) return;

  loading.value = true;
  try {
    const response = await request({
      url: `/api/user/${props.userId}/balance-records`,
      method: 'get',
      params: {
        page: pagination.value.current,
        size: pagination.value.size
      }
    });

    if (response && response.data) {
      balanceRecords.value = response.data.records || [];
      pagination.value.total = response.data.total || 0;
      // 更新当前余额
      if (response.data.current_balance !== undefined) {
        currentBalance.value = response.data.current_balance;
      }
    }
  } catch (error) {
    console.error('获取余额记录失败:', error);
    ElMessage.error('获取余额记录失败');
  } finally {
    loading.value = false;
  }
}

// 执行余额操作
async function executeBalanceOperation() {
  if (!props.userId || operationAmount.value <= 0) {
    ElMessage.error('请输入有效的金额');
    return;
  }

  const operation = operationType.value;
  const operationText = operation === 'recharge' ? '充值' : '扣费';

  try {
    await ElMessageBox.confirm(`确定要为用户${operationText} ¥${operationAmount.value} 吗？`, `确认${operationText}`, {
      type: 'warning',
      confirmButtonText: `确认${operationText}`,
      cancelButtonText: '取消'
    });

    processing.value = true;

    await request({
      url: `/api/user/${operation}`,
      method: 'post',
      data: {
        user_id: props.userId,
        amount: operationAmount.value,
        remark: operationRemark.value || `管理员${operationText}`
      }
    });

    ElMessage.success(`${operationText}成功`);

    // 重置表单
    operationAmount.value = 0;
    operationRemark.value = '';

    // 刷新数据
    await fetchUserBalance();
    await fetchBalanceRecords();

    emit('success');
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${operationText}失败:`, error);
      ElMessage.error(`${operationText}失败`);
    }
  } finally {
    processing.value = false;
  }
}

// 格式化金额
function formatCurrency(amount: number): string {
  return amount.toFixed(2);
}

// 获取操作类型标签
function getOperationTypeTag(type: string) {
  const typeMap: Record<string, { label: string; type: 'success' | 'warning' | 'danger' | 'info' }> = {
    recharge: { label: '充值', type: 'success' },
    deduct: { label: '扣费', type: 'warning' },
    consume: { label: '消费', type: 'info' },
    refund: { label: '退款', type: 'success' },
    freeze: { label: '冻结', type: 'danger' },
    unfreeze: { label: '解冻', type: 'success' }
  };
  return typeMap[type] || { label: type, type: 'info' };
}

// 格式化时间
function formatDateTime(dateTime: string): string {
  return new Date(dateTime).toLocaleString();
}

// 分页变化
function handlePageChange(page: number) {
  pagination.value.current = page;
  fetchBalanceRecords();
}

// 分页大小变化
function handleSizeChange(size: number) {
  pagination.value.size = size;
  pagination.value.current = 1;
  fetchBalanceRecords();
}

// 关闭对话框
function closeDialog() {
  operationType.value = 'recharge';
  operationAmount.value = 0;
  operationRemark.value = '';
  emit('update:visible', false);
}

// 监听对话框显示状态
watch(
  () => props.visible,
  visible => {
    if (visible && props.userId) {
      fetchUserBalance();
      fetchBalanceRecords();
    }
  }
);

// 组件挂载
onMounted(() => {
  if (props.visible && props.userId) {
    fetchUserBalance();
    fetchBalanceRecords();
  }
});
</script>

<template>
  <ElDialog
    v-model="dialogVisible"
    title="用户资金管理"
    width="800px"
    :close-on-click-modal="false"
    @close="closeDialog"
  >
    <div v-loading="loading" class="space-y-16px">
      <!-- 用户信息和当前余额 -->
      <ElCard>
        <template #header>
          <div class="flex items-center gap-8px">
            <icon-ic-round-person class="text-blue-500" />
            <span class="font-medium">用户信息</span>
          </div>
        </template>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-16px">
            <div>
              <div class="text-lg font-medium">{{ userInfo?.username || `用户ID: ${userId}` }}</div>
              <div class="text-sm text-gray-600">{{ userInfo?.email || '未设置邮箱' }}</div>
            </div>
          </div>
          <div class="text-right">
            <div class="text-sm text-gray-600">当前余额</div>
            <div class="text-2xl font-bold" :class="currentBalance > 0 ? 'text-green-600' : 'text-gray-500'">
              ¥{{ formatCurrency(currentBalance) }}
            </div>
          </div>
        </div>
      </ElCard>

      <!-- 余额操作 -->
      <ElCard>
        <template #header>
          <div class="flex items-center gap-8px">
            <icon-ic-round-account-balance-wallet class="text-green-500" />
            <span class="font-medium">余额操作</span>
          </div>
        </template>

        <div class="space-y-16px">
          <!-- 操作类型选择 -->
          <div>
            <div class="mb-8px text-sm text-gray-700 font-medium">操作类型</div>
            <ElRadioGroup v-model="operationType">
              <ElRadio value="recharge">
                <div class="flex items-center gap-8px">
                  <icon-ic-round-add class="text-green-500" />
                  <span>充值</span>
                </div>
              </ElRadio>
              <ElRadio value="deduct">
                <div class="flex items-center gap-8px">
                  <icon-ic-round-remove class="text-orange-500" />
                  <span>扣费</span>
                </div>
              </ElRadio>
            </ElRadioGroup>
          </div>

          <!-- 操作表单 -->
          <div class="grid grid-cols-1 gap-16px md:grid-cols-2">
            <div>
              <div class="mb-8px text-sm text-gray-700 font-medium">{{ operationTitle }}金额</div>
              <ElInputNumber
                v-model="operationAmount"
                :min="0.01"
                :max="999999.99"
                :precision="2"
                :step="1"
                placeholder="请输入金额"
                class="w-full"
              >
                <template #prefix>¥</template>
              </ElInputNumber>
            </div>

            <div>
              <div class="mb-8px text-sm text-gray-700 font-medium">操作备注</div>
              <ElInput
                v-model="operationRemark"
                :placeholder="`请输入${operationTitle}备注（可选）`"
                maxlength="100"
                show-word-limit
              />
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex justify-end">
            <ElButton :type="operationColor" :loading="processing" @click="executeBalanceOperation">
              <template #icon>
                <component :is="`icon-${operationIcon}`" />
              </template>
              确认{{ operationTitle }}
            </ElButton>
          </div>
        </div>
      </ElCard>

      <!-- 余额记录 -->
      <ElCard>
        <template #header>
          <div class="flex items-center gap-8px">
            <icon-ic-round-history class="text-purple-500" />
            <span class="font-medium">余额记录</span>
          </div>
        </template>

        <ElTable :data="balanceRecords" stripe>
          <ElTableColumn prop="operation_type" label="操作类型" width="100">
            <template #default="{ row }">
              <ElTag :type="getOperationTypeTag(row.operation_type).type" size="small">
                {{ getOperationTypeTag(row.operation_type).label }}
              </ElTag>
            </template>
          </ElTableColumn>

          <ElTableColumn prop="amount" label="金额" width="120">
            <template #default="{ row }">
              <span :class="row.amount > 0 ? 'text-green-600' : 'text-red-600'">
                {{ row.amount > 0 ? '+' : '' }}¥{{ formatCurrency(Math.abs(row.amount)) }}
              </span>
            </template>
          </ElTableColumn>

          <ElTableColumn prop="balance_after" label="操作后余额" width="120">
            <template #default="{ row }">
              <span class="font-medium">¥{{ formatCurrency(row.balance_after) }}</span>
            </template>
          </ElTableColumn>

          <ElTableColumn prop="remark" label="备注" show-overflow-tooltip />

          <ElTableColumn prop="created_at" label="操作时间" width="160">
            <template #default="{ row }">
              {{ formatDateTime(row.created_at) }}
            </template>
          </ElTableColumn>
        </ElTable>

        <!-- 分页 -->
        <div class="mt-16px flex justify-center">
          <ElPagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @current-change="handlePageChange"
            @size-change="handleSizeChange"
          />
        </div>
      </ElCard>
    </div>

    <template #footer>
      <div class="flex justify-end">
        <ElButton @click="closeDialog">关闭</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.balance-card {
  transition: all 0.3s ease;
}

.balance-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
