import { createApp } from 'vue';
import './plugins/assets';
import {
  setupAppVersionNotification,
  setupDayjs,
  setupIconifyOffline,
  setupLoading,
  setupNProgress,
  setupUI
} from './plugins';
import { setupStore } from './store';
import { setupRouter } from './router';
import { setupI18n } from './locales';
import { detectBackendPort, updateServiceConfig } from './utils/port-detector';
import { setupPermissionDirective } from './directives/permission';
// import { createLicenseAdminModule } from './modules/license-admin'; // 已解耦到独立项目
import App from './App.vue';

async function setupApp() {
  setupLoading();

  setupNProgress();

  // 🔍 自动检测后端服务端口
  if (import.meta.env.DEV) {
    console.log('🚀 SoybeanAdmin 课程管理系统启动中...');
    console.log('🔍 正在检测后端服务...');

    try {
      const backendPort = await detectBackendPort();
      updateServiceConfig(backendPort);

      // 后端监控已禁用 - 减少不必要的资源消耗
      // startBackendMonitoring();

      console.log('✅ 前后端连接配置完成');
    } catch (error) {
      console.error('❌ 后端服务检测失败:', error);
      console.log('🔄 将使用默认配置，请确保后端服务在端口 3000 运行');
    }
  }

  setupIconifyOffline();

  setupDayjs();

  const app = createApp(App);

  // 处理浏览器扩展冲突错误
  window.addEventListener('unhandledrejection', event => {
    if (event.reason?.message?.includes('message channel closed')) {
      event.preventDefault(); // 阻止错误显示
    }
  });

  setupUI(app);

  setupStore(app);

  const router = await setupRouter(app);

  setupI18n(app);

  // 注册权限指令
  setupPermissionDirective(app);

  // 注册授权管理模块 - 已解耦到独立项目 (license-admin-system)
  // 核心授权验证功能保留在原项目中，管理功能已迁移到独立系统
  // const licenseAdminModule = createLicenseAdminModule({
  //   enableDebugMode: import.meta.env.DEV,
  //   autoRegisterRoutes: true
  // });
  // licenseAdminModule.install(app, router);

  setupAppVersionNotification();

  // 移除授权状态初始化，前端完全无授权限制
  app.mount('#app');
}

setupApp();
