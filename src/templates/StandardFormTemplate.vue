<script setup lang="ts">
import { onMounted, onUnmounted, reactive, ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage } from 'element-plus';

defineOptions({
  name: 'StandardFormTemplate'
});

// 移动端检测
const isMobile = ref(false);
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

// 表单相关
const formRef = ref<FormInstance>();
const loading = ref(false);
const formData = reactive({
  name: '',
  email: '',
  phone: '',
  status: 1,
  remark: ''
});

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ]
};

// 生命周期
onMounted(() => {
  checkMobile();
  window.addEventListener('resize', checkMobile);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile);
});

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    loading.value = true;

    // TODO: 提交表单数据
    console.log('表单数据:', formData);

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));

    ElMessage.success('保存成功');
  } catch (error: any) {
    console.error('保存失败:', error);
    const errorMessage = error.response?.data?.msg || error.message || '保存失败';
    ElMessage.error(errorMessage);
  } finally {
    loading.value = false;
  }
};

// 重置表单
const handleReset = () => {
  formRef.value?.resetFields();
};
</script>

<template>
  <!-- 标准表单页面布局 -->
  <div class="flex flex-col gap-16px p-16px">
    <!-- 页面头部 -->
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-medium">表单页面</span>
          <div class="flex items-center gap-12px">
            <ElButton @click="handleReset">重置</ElButton>
            <ElButton type="primary" :loading="loading" @click="handleSubmit">保存</ElButton>
          </div>
        </div>
      </template>

      <!-- 表单内容 -->
      <ElForm
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-width="isMobile ? '80px' : '100px'"
        :label-position="isMobile ? 'top' : 'right'"
      >
        <div class="grid grid-cols-1 gap-16px lg:grid-cols-2 md:gap-24px">
          <!-- 左侧表单 -->
          <div class="space-y-16px">
            <ElFormItem label="名称" prop="name">
              <ElInput v-model="formData.name" placeholder="请输入名称" clearable maxlength="50" show-word-limit />
            </ElFormItem>

            <ElFormItem label="邮箱" prop="email">
              <ElInput v-model="formData.email" placeholder="请输入邮箱" clearable type="email" />
            </ElFormItem>

            <ElFormItem label="手机号" prop="phone">
              <ElInput v-model="formData.phone" placeholder="请输入手机号" clearable maxlength="11" />
            </ElFormItem>
          </div>

          <!-- 右侧表单 -->
          <div class="space-y-16px">
            <ElFormItem label="状态">
              <ElRadioGroup v-model="formData.status">
                <ElRadio :value="1">启用</ElRadio>
                <ElRadio :value="0">禁用</ElRadio>
              </ElRadioGroup>
            </ElFormItem>

            <ElFormItem label="备注">
              <ElInput
                v-model="formData.remark"
                type="textarea"
                :rows="isMobile ? 3 : 4"
                placeholder="请输入备注信息"
                maxlength="200"
                show-word-limit
              />
            </ElFormItem>
          </div>
        </div>
      </ElForm>
    </ElCard>

    <!-- 操作说明卡片 -->
    <ElCard>
      <template #header>
        <span class="font-medium">操作说明</span>
      </template>
      <div class="text-sm text-gray-600 space-y-8px">
        <div>
          • 带有
          <span class="text-red-500">*</span>
          的字段为必填项
        </div>
        <div>• 邮箱格式需要正确，用于接收通知</div>
        <div>• 手机号用于身份验证和紧急联系</div>
        <div>• 保存前请仔细检查填写的信息</div>
      </div>
    </ElCard>

    <!-- 预览卡片 -->
    <ElCard>
      <template #header>
        <span class="font-medium">数据预览</span>
      </template>
      <div class="grid grid-cols-1 gap-12px md:grid-cols-2">
        <div class="text-sm">
          <span class="text-gray-500">名称：</span>
          <span>{{ formData.name || '未填写' }}</span>
        </div>
        <div class="text-sm">
          <span class="text-gray-500">邮箱：</span>
          <span>{{ formData.email || '未填写' }}</span>
        </div>
        <div class="text-sm">
          <span class="text-gray-500">手机号：</span>
          <span>{{ formData.phone || '未填写' }}</span>
        </div>
        <div class="text-sm">
          <span class="text-gray-500">状态：</span>
          <ElTag :type="formData.status === 1 ? 'success' : 'info'">
            {{ formData.status === 1 ? '启用' : '禁用' }}
          </ElTag>
        </div>
      </div>
      <div v-if="formData.remark" class="mt-12px text-sm">
        <span class="text-gray-500">备注：</span>
        <span>{{ formData.remark }}</span>
      </div>
    </ElCard>
  </div>
</template>

<style scoped>
/* 移动端适配 */
@media (max-width: 768px) {
  .flex.flex-col.gap-16px.p-16px {
    padding: 12px;
    gap: 12px;
  }

  /* 移动端表单标签位置调整 */
  :deep(.el-form--label-top .el-form-item__label) {
    padding-bottom: 4px;
  }
}
</style>
