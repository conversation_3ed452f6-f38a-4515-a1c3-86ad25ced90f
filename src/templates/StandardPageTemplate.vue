<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';

defineOptions({
  name: 'StandardPageTemplate'
});

// 移动端检测
const isMobile = ref(false);
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

// 页面状态
const loading = ref(false);

// 生命周期
onMounted(() => {
  checkMobile();
  window.addEventListener('resize', checkMobile);
  // 初始化页面数据
  initPageData();
});

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile);
});

// 初始化页面数据
const initPageData = async () => {
  loading.value = true;
  try {
    // TODO: 加载页面数据
  } catch (error) {
    console.error('页面数据加载失败:', error);
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <!-- 标准页面布局 -->
  <div class="flex flex-col gap-16px p-16px">
    <!-- 页面头部 -->
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-medium">页面标题</span>
          <div class="flex items-center gap-12px">
            <!-- 页面操作按钮 -->
            <ElButton type="primary" :loading="loading">主要操作</ElButton>
          </div>
        </div>
      </template>

      <!-- 搜索和筛选区域 -->
      <div class="flex flex-wrap items-center gap-16px pb-12px">
        <ElInput placeholder="搜索关键词" clearable class="w-240px sm:w-200px">
          <template #prefix>
            <ElIcon><Search /></ElIcon>
          </template>
        </ElInput>

        <ElSelect placeholder="筛选条件" clearable class="w-120px">
          <ElOption label="选项1" value="1" />
          <ElOption label="选项2" value="2" />
        </ElSelect>

        <ElButton type="primary">搜索</ElButton>
        <ElButton>重置</ElButton>
      </div>
    </ElCard>

    <!-- 主要内容区域 -->
    <ElCard>
      <!-- 数据表格 -->
      <div class="w-full overflow-x-auto">
        <ElTable
          v-loading="loading"
          :data="[]"
          class="min-w-800px w-full"
          max-height="70vh"
          stripe
          empty-text="暂无数据"
          :size="isMobile ? 'small' : 'default'"
        >
          <ElTableColumn prop="id" label="ID" width="80" />
          <ElTableColumn prop="name" label="名称" min-width="120" />
          <ElTableColumn prop="status" label="状态" width="100">
            <template #default="{ row }">
              <ElTag :type="row.status === 1 ? 'success' : 'info'">
                {{ row.status === 1 ? '启用' : '禁用' }}
              </ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="createTime" label="创建时间" width="160" />
          <ElTableColumn label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <ElButton type="primary" size="small" text>编辑</ElButton>
              <ElButton type="danger" size="small" text>删除</ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </div>

      <!-- 分页 -->
      <div class="mt-16px flex justify-center">
        <ElPagination
          :current-page="1"
          :page-size="20"
          :total="0"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="() => {}"
          @current-change="() => {}"
        />
      </div>
    </ElCard>

    <!-- 统计信息卡片 -->
    <div class="grid grid-cols-1 gap-16px lg:grid-cols-4 md:gap-24px">
      <ElCard class="text-center">
        <div class="text-2xl text-blue-600 font-bold">0</div>
        <div class="mt-4px text-sm text-gray-500">总数</div>
      </ElCard>
      <ElCard class="text-center">
        <div class="text-2xl text-green-600 font-bold">0</div>
        <div class="mt-4px text-sm text-gray-500">成功</div>
      </ElCard>
      <ElCard class="text-center">
        <div class="text-2xl text-orange-600 font-bold">0</div>
        <div class="mt-4px text-sm text-gray-500">处理中</div>
      </ElCard>
      <ElCard class="text-center">
        <div class="text-2xl text-red-600 font-bold">0</div>
        <div class="mt-4px text-sm text-gray-500">失败</div>
      </ElCard>
    </div>
  </div>

  <!-- 编辑对话框 -->
  <ElDialog v-model="false" title="编辑" width="500px" :close-on-click-modal="false">
    <ElForm label-width="80px">
      <ElFormItem label="名称" required>
        <ElInput placeholder="请输入名称" />
      </ElFormItem>
      <ElFormItem label="状态">
        <ElSelect placeholder="请选择状态" class="w-full">
          <ElOption label="启用" :value="1" />
          <ElOption label="禁用" :value="0" />
        </ElSelect>
      </ElFormItem>
    </ElForm>

    <template #footer>
      <div class="flex justify-end gap-8px">
        <ElButton>取消</ElButton>
        <ElButton type="primary" :loading="loading">确定</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
/* 移动端适配 */
@media (max-width: 768px) {
  .flex.flex-col.gap-16px.p-16px {
    padding: 12px;
    gap: 12px;
  }
}
</style>
