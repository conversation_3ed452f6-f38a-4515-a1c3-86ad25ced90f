/**
 * 标准化字段库 - 前端版本
 * 定义所有接口类型的标准字段、说明和验证规则
 */

export interface FieldDefinition {
  name: string;
  label: string;
  description: string;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object';
  required: boolean;
  defaultValue?: any;
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
    options?: string[];
  };
  example?: any;
  tips?: string[];
}

export interface InterfaceDefinition {
  type: string;
  name: string;
  description: string;
  category: 'basic' | 'advanced' | 'management' | 'batch';
  icon: string;
  color: string;
  fields: FieldDefinition[];
  responseFields?: FieldDefinition[];
  examples?: {
    platform29?: any;
    universal?: any;
  };
}

// 通用认证字段
const authFields: FieldDefinition[] = [
  {
    name: 'username',
    label: 'API用户名',
    description: '货源提供的API用户名或UID',
    type: 'string',
    required: true,
    example: 'api_user_123',
    tips: ['通常由货源商提供', '有些平台叫做UID或用户ID']
  },
  {
    name: 'password',
    label: 'API密码',
    description: '货源提供的API密码或密钥',
    type: 'string',
    required: true,
    example: 'api_key_456',
    tips: ['通常由货源商提供', '有些平台叫做KEY或API密钥']
  }
];

// 用户信息字段
const userFields: FieldDefinition[] = [
  {
    name: 'student_username',
    label: '学生用户名',
    description: '学生的登录用户名',
    type: 'string',
    required: true,
    example: 'student123',
    tips: ['学生在学习平台的用户名', '通常是手机号或学号']
  },
  {
    name: 'student_password',
    label: '学生密码',
    description: '学生的登录密码',
    type: 'string',
    required: true,
    example: 'password123',
    tips: ['学生在学习平台的密码']
  },
  {
    name: 'school',
    label: '学校名称',
    description: '学生所在的学校',
    type: 'string',
    required: true,
    example: '北京大学',
    tips: ['完整的学校名称', '某些平台需要精确匹配']
  }
];

// 课程相关字段
const courseFields: FieldDefinition[] = [
  {
    name: 'course_id',
    label: '课程ID',
    description: '课程的唯一标识符',
    type: 'string',
    required: false,
    example: 'course_12345',
    tips: ['平台内部的课程编号']
  },
  {
    name: 'course_name',
    label: '课程名称',
    description: '课程的完整名称',
    type: 'string',
    required: false,
    example: '高等数学',
    tips: ['完整的课程名称']
  },
  {
    name: 'platform',
    label: '学习平台',
    description: '课程所在的学习平台',
    type: 'string',
    required: false,
    example: 'network_course',
    validation: {
      options: ['network_course', 'mooc', 'zhihuishu', 'xuexitong', 'other']
    },
    tips: ['选择对应的学习平台类型']
  }
];

// 订单相关字段
const orderFields: FieldDefinition[] = [
  {
    name: 'upstream_order_id',
    label: '上游订单ID',
    description: '货源返回的订单ID',
    type: 'string',
    required: true,
    example: 'upstream_12345',
    tips: ['货源系统的订单编号', '用于后续查询和操作']
  },
  {
    name: 'new_password',
    label: '新密码',
    description: '要修改的新密码',
    type: 'string',
    required: true,
    example: 'new_password_123',
    tips: ['修改密码时使用']
  }
];

// 定义所有接口类型
export const INTERFACE_DEFINITIONS: InterfaceDefinition[] = [
  // 基础接口
  {
    type: 'query',
    name: '查课接口',
    description: '查询学生的课程信息和进度',
    category: 'basic',
    icon: 'Search',
    color: '#409EFF',
    fields: [...authFields, ...userFields, ...courseFields],
    examples: {
      platform29: {
        request: {
          uid: '${auth.username}',
          key: '${auth.password}',
          platform: '${data.platform}',
          user: '${data.student_username}',
          pass: '${data.student_password}',
          school: '${data.school}',
          kcid: '${data.course_id}'
        },
        response: {
          success_field: 'code',
          success_value: 0,
          message_field: 'msg',
          data_field: 'data'
        }
      }
    }
  },
  {
    type: 'order',
    name: '下单接口',
    description: '创建新的刷课订单',
    category: 'basic',
    icon: 'Plus',
    color: '#67C23A',
    fields: [...authFields, ...userFields, ...courseFields],
    examples: {
      platform29: {
        request: {
          uid: '${auth.username}',
          key: '${auth.password}',
          platform: '${data.platform}',
          user: '${data.student_username}',
          pass: '${data.student_password}',
          school: '${data.school}',
          kcname: '${data.course_name}',
          kcid: '${data.course_id}'
        },
        response: {
          success_field: 'code',
          success_value: 0,
          message_field: 'msg',
          upstream_order_id_field: 'id'
        }
      }
    }
  },
  {
    type: 'sync',
    name: '同步接口',
    description: '同步订单状态和进度',
    category: 'basic',
    icon: 'Refresh',
    color: '#E6A23C',
    fields: [...authFields, ...orderFields.filter(f => f.name === 'upstream_order_id')]
  },
  {
    type: 'refill',
    name: '补刷接口',
    description: '重新执行未完成的订单',
    category: 'basic',
    icon: 'RefreshRight',
    color: '#F56C6C',
    fields: [...authFields, ...orderFields.filter(f => f.name === 'upstream_order_id')]
  },
  {
    type: 'change_password',
    name: '改密接口',
    description: '修改学生账户密码',
    category: 'basic',
    icon: 'Lock',
    color: '#909399',
    fields: [...authFields, ...orderFields.filter(f => ['upstream_order_id', 'new_password'].includes(f.name))]
  },

  // 高级接口
  {
    type: 'get_courses',
    name: '获取课程列表',
    description: '获取平台所有可用课程',
    category: 'advanced',
    icon: 'Menu',
    color: '#409EFF',
    fields: [...authFields]
  },
  {
    type: 'get_balance',
    name: '余额查询',
    description: '查询货源账户余额',
    category: 'management',
    icon: 'Wallet',
    color: '#67C23A',
    fields: [...authFields]
  },
  {
    type: 'pause_order',
    name: '暂停订单',
    description: '暂停正在执行的订单',
    category: 'management',
    icon: 'VideoPause',
    color: '#E6A23C',
    fields: [...authFields, ...orderFields.filter(f => f.name === 'upstream_order_id')]
  },
  {
    type: 'resume_order',
    name: '恢复订单',
    description: '恢复已暂停的订单',
    category: 'management',
    icon: 'VideoPlay',
    color: '#67C23A',
    fields: [...authFields, ...orderFields.filter(f => f.name === 'upstream_order_id')]
  },
  {
    type: 'cancel_order',
    name: '取消订单',
    description: '取消未完成的订单',
    category: 'management',
    icon: 'Close',
    color: '#F56C6C',
    fields: [...authFields, ...orderFields.filter(f => f.name === 'upstream_order_id')]
  },
  {
    type: 'get_order_logs',
    name: '订单日志',
    description: '获取订单执行日志',
    category: 'management',
    icon: 'Document',
    color: '#909399',
    fields: [...authFields, ...orderFields.filter(f => f.name === 'upstream_order_id')]
  }
];

// 获取接口定义
export function getInterfaceDefinition(type: string): InterfaceDefinition | undefined {
  return INTERFACE_DEFINITIONS.find(def => def.type === type);
}

// 获取分类的接口
export function getInterfacesByCategory(category: string): InterfaceDefinition[] {
  return INTERFACE_DEFINITIONS.filter(def => def.category === category);
}

// 获取所有分类
export function getCategories(): Array<{ key: string; label: string; color: string }> {
  return [
    { key: 'basic', label: '基础接口', color: '#409EFF' },
    { key: 'advanced', label: '高级接口', color: '#67C23A' },
    { key: 'management', label: '管理接口', color: '#E6A23C' },
    { key: 'batch', label: '批量接口', color: '#F56C6C' }
  ];
}
