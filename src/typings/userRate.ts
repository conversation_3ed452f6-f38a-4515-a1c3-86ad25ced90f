/** 用户费率等级定义 */
export interface UserRateLevel {
  /** 费率值 */
  rate: number;
  /** 等级名称 */
  level: string;
  /** 角色标识 */
  role: string;
  /** 等级描述 */
  description: string;
}

/** 用户费率信息 */
export interface UserRateInfo {
  /** 用户ID */
  user_id: number;
  /** 用户名 */
  username: string;
  /** 用户费率 */
  price_rate: number;
  /** 数值角色 */
  role: number;
  /** 字符串角色 */
  user_role: string;
  /** 账户余额 */
  balance: number;
  /** 等级信息 */
  levelInfo: {
    /** 等级名称 */
    level: string;
    /** 等级描述 */
    description: string;
    /** 费率值 */
    rate: number;
  };
  /** 价格示例 */
  priceExample: {
    /** 基础价格 */
    basePrice: number;
    /** 用户价格 */
    userPrice: number;
    /** 折扣百分比 */
    discount: number;
  };
}

/** 费率变更日志 */
export interface UserRateLog {
  /** 日志ID */
  log_id: number;
  /** 用户ID */
  user_id: number;
  /** 用户名 */
  username: string;
  /** 原费率 */
  old_rate: number;
  /** 新费率 */
  new_rate: number;
  /** 原角色 */
  old_role: string;
  /** 新角色 */
  new_role: string;
  /** 操作人ID */
  operator_id: number;
  /** 操作人姓名 */
  operator_name: string;
  /** 变更原因 */
  reason: string;
  /** 创建时间 */
  create_time: string;
}

/** 更新费率请求 */
export interface UpdateUserRateRequest {
  /** 新费率 */
  price_rate: number;
  /** 变更原因 */
  reason?: string;
}

/** 批量更新费率请求 */
export interface BatchUpdateUserRateRequest {
  /** 用户ID列表 */
  user_ids: number[];
  /** 新费率 */
  price_rate: number;
  /** 变更原因 */
  reason?: string;
}

/** 费率管理相关的用户角色枚举 */
export enum UserRateRole {
  /** 超级管理员 */
  SUPER = 'super',
  /** 管理员 */
  ADMIN = 'admin',
  /** VIP用户 */
  VIP = 'vip',
  /** 代理商 */
  AGENT = 'agent',
  /** 会员 */
  MEMBER = 'member',
  /** 普通用户 */
  USER = 'user'
}

/** 费率等级常量 */
export const USER_RATE_LEVELS: UserRateLevel[] = [
  { rate: 0.2, level: 'VIP', role: 'vip', description: '最高等级，最低费率' },
  { rate: 0.5, level: '代理商', role: 'agent', description: '代理商等级' },
  { rate: 0.8, level: '会员', role: 'member', description: '会员等级' },
  { rate: 1.0, level: '普通用户', role: 'user', description: '标准费率' }
];

/** 根据费率获取用户角色 */
export function getUserRoleByRate(rate: number): UserRateRole {
  if (rate <= 0.2) return UserRateRole.VIP;
  if (rate <= 0.5) return UserRateRole.AGENT;
  if (rate <= 0.8) return UserRateRole.MEMBER;
  return UserRateRole.USER;
}

/** 根据费率获取等级信息 */
export function getRateLevelInfo(rate: number): UserRateLevel {
  for (const level of USER_RATE_LEVELS) {
    if (rate <= level.rate) {
      return level;
    }
  }
  return USER_RATE_LEVELS[USER_RATE_LEVELS.length - 1];
}

/** 计算用户实际价格 */
export function calculateUserPrice(basePrice: number, priceRate: number): number {
  return Math.round(basePrice * priceRate * 100) / 100;
}

/** 计算折扣百分比 */
export function calculateDiscount(priceRate: number): number {
  return Math.round((1 - priceRate) * 100);
}

/** 获取费率等级颜色 */
export function getRateLevelColor(rate: number): string {
  if (rate <= 0.2) return '#f56c6c'; // 红色 - VIP
  if (rate <= 0.5) return '#e6a23c'; // 橙色 - 代理商
  if (rate <= 0.8) return '#409eff'; // 蓝色 - 会员
  return '#909399'; // 灰色 - 普通用户
}

/** 获取费率等级标签类型 */
export function getRateLevelType(rate: number): 'danger' | 'warning' | 'primary' | 'info' {
  if (rate <= 0.2) return 'danger';
  if (rate <= 0.5) return 'warning';
  if (rate <= 0.8) return 'primary';
  return 'info';
}
