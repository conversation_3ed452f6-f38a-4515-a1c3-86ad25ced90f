/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AdvancedUserFilter: typeof import('./../components/user/AdvancedUserFilter.vue')['default']
    AppProvider: typeof import('./../components/common/app-provider.vue')['default']
    AvatarUpload: typeof import('./../components/custom/avatar-upload.vue')['default']
    BatchOperations: typeof import('./../components/common/BatchOperations.vue')['default']
    BatchUserPermissionManager: typeof import('./../components/user/BatchUserPermissionManager.vue')['default']
    BetterScroll: typeof import('./../components/custom/better-scroll.vue')['default']
    ButtonIcon: typeof import('./../components/custom/button-icon.vue')['default']
    ConflictResolutionSuggestions: typeof import('./../components/permission/ConflictResolutionSuggestions.vue')['default']
    CountTo: typeof import('./../components/custom/count-to.vue')['default']
    CustomIconSelect: typeof import('./../components/custom/custom-icon-select.vue')['default']
    DarkModeContainer: typeof import('./../components/common/dark-mode-container.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElCollapse: typeof import('element-plus/es')['ElCollapse']
    ElCollapseItem: typeof import('element-plus/es')['ElCollapseItem']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElOptionGroup: typeof import('element-plus/es')['ElOptionGroup']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopconfirm: typeof import('element-plus/es')['ElPopconfirm']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioButton: typeof import('element-plus/es')['ElRadioButton']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElResult: typeof import('element-plus/es')['ElResult']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSpace: typeof import('element-plus/es')['ElSpace']
    ElStatistic: typeof import('element-plus/es')['ElStatistic']
    ElStep: typeof import('element-plus/es')['ElStep']
    ElSteps: typeof import('element-plus/es')['ElSteps']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTimeline: typeof import('element-plus/es')['ElTimeline']
    ElTimelineItem: typeof import('element-plus/es')['ElTimelineItem']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElWatermark: typeof import('element-plus/es')['ElWatermark']
    ExceptionBase: typeof import('./../components/common/exception-base.vue')['default']
    FilterDrawer: typeof import('./../components/responsive/FilterDrawer.vue')['default']
    FullScreen: typeof import('./../components/common/full-screen.vue')['default']
    GithubLink: typeof import('./../components/custom/github-link.vue')['default']
    IconAntDesignEnterOutlined: typeof import('~icons/ant-design/enter-outlined')['default']
    IconAntDesignReloadOutlined: typeof import('~icons/ant-design/reload-outlined')['default']
    IconGridiconsFullscreen: typeof import('~icons/gridicons/fullscreen')['default']
    IconGridiconsFullscreenExit: typeof import('~icons/gridicons/fullscreen-exit')['default']
    IconIcRoundAccountBalanceWallet: typeof import('~icons/ic/round-account-balance-wallet')['default']
    IconIcRoundAccountTree: typeof import('~icons/ic/round-account-tree')['default']
    IconIcRoundAdd: typeof import('~icons/ic/round-add')['default']
    IconIcRoundAddCircle: typeof import('~icons/ic/round-add-circle')['default']
    IconIcRoundApi: typeof import('~icons/ic/round-api')['default']
    IconIcRoundCancel: typeof import('~icons/ic/round-cancel')['default']
    IconIcRoundCategory: typeof import('~icons/ic/round-category')['default']
    IconIcRoundCheck: typeof import('~icons/ic/round-check')['default']
    IconIcRoundCheckCircle: typeof import('~icons/ic/round-check-circle')['default']
    IconIcRoundClose: typeof import('~icons/ic/round-close')['default']
    IconIcRoundDownload: typeof import('~icons/ic/round-download')['default']
    IconIcRoundEdit: typeof import('~icons/ic/round-edit')['default']
    IconIcRoundError: typeof import('~icons/ic/round-error')['default']
    IconIcRoundFeedback: typeof import('~icons/ic/round-feedback')['default']
    IconIcRoundFolder: typeof import('~icons/ic/round-folder')['default']
    IconIcRoundGroup: typeof import('~icons/ic/round-group')['default']
    IconIcRoundHistory: typeof import('~icons/ic/round-history')['default']
    IconIcRoundHub: typeof import('~icons/ic/round-hub')['default']
    IconIcRoundInfo: typeof import('~icons/ic/round-info')['default']
    IconIcRoundKeyboardArrowDown: typeof import('~icons/ic/round-keyboard-arrow-down')['default']
    IconIcRoundKeyboardArrowUp: typeof import('~icons/ic/round-keyboard-arrow-up')['default']
    IconIcRoundList: typeof import('~icons/ic/round-list')['default']
    IconIcRoundLock: typeof import('~icons/ic/round-lock')['default']
    IconIcRoundMenu: typeof import('~icons/ic/round-menu')['default']
    IconIcRoundNavigation: typeof import('~icons/ic/round-navigation')['default']
    IconIcRoundPayment: typeof import('~icons/ic/round-payment')['default']
    IconIcRoundPerson: typeof import('~icons/ic/round-person')['default']
    IconIcRoundPlus: typeof import('~icons/ic/round-plus')['default']
    IconIcRoundRefresh: typeof import('~icons/ic/round-refresh')['default']
    IconIcRoundRemove: typeof import('~icons/ic/round-remove')['default']
    IconIcRoundSchool: typeof import('~icons/ic/round-school')['default']
    IconIcRoundSearch: typeof import('~icons/ic/round-search')['default']
    IconIcRoundSecurity: typeof import('~icons/ic/round-security')['default']
    IconIcRoundSettings: typeof import('~icons/ic/round-settings')['default']
    IconIcRoundSmartToy: typeof import('~icons/ic/round-smart-toy')['default']
    IconIcRoundSort: typeof import('~icons/ic/round-sort')['default']
    IconIcRoundStore: typeof import('~icons/ic/round-store')['default']
    IconIcRoundSync: typeof import('~icons/ic/round-sync')['default']
    IconIcRoundTune: typeof import('~icons/ic/round-tune')['default']
    IconIcRoundUpdate: typeof import('~icons/ic/round-update')['default']
    IconIcRoundVisibility: typeof import('~icons/ic/round-visibility')['default']
    IconIcRoundWarning: typeof import('~icons/ic/round-warning')['default']
    IconLocalBanner: typeof import('~icons/local/banner')['default']
    IconLocalLogo: typeof import('~icons/local/logo')['default']
    IconMdiArrowDownThin: typeof import('~icons/mdi/arrow-down-thin')['default']
    IconMdiArrowUpThin: typeof import('~icons/mdi/arrow-up-thin')['default']
    IconMdiChartLine: typeof import('~icons/mdi/chart-line')['default']
    IconMdiFilterVariant: typeof import('~icons/mdi/filter-variant')['default']
    IconMdiKeyboardEsc: typeof import('~icons/mdi/keyboard-esc')['default']
    IconMdiKeyboardReturn: typeof import('~icons/mdi/keyboard-return')['default']
    IconMdiTrendingDown: typeof import('~icons/mdi/trending-down')['default']
    IconMdiTrendingUp: typeof import('~icons/mdi/trending-up')['default']
    IconMdiWallet: typeof import('~icons/mdi/wallet')['default']
    IconUilSearch: typeof import('~icons/uil/search')['default']
    LangSwitch: typeof import('./../components/common/lang-switch.vue')['default']
    LookForward: typeof import('./../components/custom/look-forward.vue')['default']
    MenuToggler: typeof import('./../components/common/menu-toggler.vue')['default']
    OperateDrawer: typeof import('./../components/common/operate-drawer.vue')['default']
    PermissionAssignWizard: typeof import('./../components/permission/PermissionAssignWizard.vue')['default']
    PermissionConflictResolver: typeof import('./../components/permission/PermissionConflictResolver.vue')['default']
    PermissionInheritanceViewer: typeof import('./../components/permission/PermissionInheritanceViewer.vue')['default']
    PermissionSelector: typeof import('./../components/permission/PermissionSelector.vue')['default']
    PermissionTemplateApplicator: typeof import('./../components/permission/PermissionTemplateApplicator.vue')['default']
    PermissionTemplateManager: typeof import('./../components/permission/PermissionTemplateManager.vue')['default']
    PermissionTree: typeof import('./../components/permission/PermissionTree.vue')['default']
    PinToggler: typeof import('./../components/common/pin-toggler.vue')['default']
    ReloadButton: typeof import('./../components/common/reload-button.vue')['default']
    ResponsiveDialog: typeof import('./../components/responsive/ResponsiveDialog.vue')['default']
    // RolePermissionManager 已移除，使用角色管理抽屉统一权限分配
    RolePermissionPreview: typeof import('./../components/permission/RolePermissionPreview.vue')['default']
    RoleTemplateManager: typeof import('./../components/permission/RoleTemplateManager.vue')['default']
    RoutePermissionManager: typeof import('./../components/permission/RoutePermissionManager.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SimplePermissionManager: typeof import('./../components/permission/SimplePermissionManager.vue')['default']
    SmartTable: typeof import('./../components/responsive/SmartTable.vue')['default']
    SoybeanAvatar: typeof import('./../components/custom/soybean-avatar.vue')['default']
    SvgIcon: typeof import('./../components/custom/svg-icon.vue')['default']
    SystemLogo: typeof import('./../components/common/system-logo.vue')['default']
    TableColumnSetting: typeof import('./../components/advanced/table-column-setting.vue')['default']
    TableHeaderOperation: typeof import('./../components/advanced/table-header-operation.vue')['default']
    ThemeSchemaSwitch: typeof import('./../components/common/theme-schema-switch.vue')['default']
    UserBalanceManager: typeof import('./../components/user/UserBalanceManager.vue')['default']
    UserDetailDialog: typeof import('./../components/user/UserDetailDialog.vue')['default']
    UserFunctionLimiter: typeof import('./../components/user/UserFunctionLimiter.vue')['default']
    UserPermissionDrawer: typeof import('./../components/user/UserPermissionDrawer.vue')['default']
    UserPermissionPreview: typeof import('./../components/permission/UserPermissionPreview.vue')['default']
    WaveBg: typeof import('./../components/custom/wave-bg.vue')['default']
    WebSiteLink: typeof import('./../components/custom/web-site-link.vue')['default']
  }
  export interface GlobalDirectives {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
