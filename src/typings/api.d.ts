/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  namespace Common {
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      current: number;
      /** page size */
      size: number;
      /** total count */
      total: number;
    }

    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      records: T[];
    }

    /** common search params of table */
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>;

    /**
     * enable status
     *
     * - 1: enabled
     * - 0: disabled
     */
    type EnableStatus = 1 | 0;

    /** common record */
    type CommonRecord<T = any> = {
      /** record id */
      id: number;
      /** record creator */
      createBy: string;
      /** record create time */
      createTime: string; // 使用驼峰命名
      /** record updater */
      updateBy: string;
      /** record update time */
      updateTime: string; // 使用驼峰命名
      /** record status */
      status: EnableStatus | undefined;
    } & T;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    interface LoginToken {
      token: string;
      refreshToken: string;
    }

    interface UserInfo {
      userId: number;
      username: string;
      nickname: string;
      email: string;
      phone: string;
      avatar: string | null;
      role: number; // 数值角色 (1=管理员, 2=代理商, 3=普通用户)
      userRole: string; // 字符串角色 (admin/agent/user)
      status: number;
      createTime: string; // 创建时间
      updateTime: string; // 更新时间
      inviteCode: string;
      balance: number; // 账户余额
      userRate: number; // 用户费率
      totalRecharge?: number; // 总充值金额
      referrerId?: number; // 推荐人ID
      levelId?: number; // 用户等级ID
      sid?: number; // 上级用户ID
      roles: string[]; // 角色数组
      permissions: string[]; // 权限数组
    }

    /** 用户列表响应 */
    interface UserListResponse {
      list: UserInfo[];
      pagination: {
        current: number;
        size: number;
        total: number;
      };
    }

    /** 创建用户参数 */
    interface CreateUserParams {
      username: string;
      password: string;
      nickname: string;
      email: string;
      phone?: string;
      role: number;
      userRole: string;
      status: number;
      avatar?: string;
      balance?: number;
      userRate?: number;
      inviteCode?: string;
      sid?: number;
      referrerId?: number;
      levelId?: number;
    }

    /** 更新用户参数 */
    interface UpdateUserParams {
      userId: number;
      username?: string;
      password?: string;
      nickname?: string;
      email?: string;
      phone?: string;
      role?: number;
      userRole?: string;
      status?: number;
      avatar?: string;
      balance?: number;
      userRate?: number;
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }

  /**
   * namespace SystemManage
   *
   * backend api module: "systemManage"
   */
  namespace SystemManage {
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>;

    /** role */
    type Role = Common.CommonRecord<{
      /** role name */
      roleName: string;
      /** role code */
      roleCode: string;
      /** role description */
      roleDesc: string;
    }>;

    /** role search params */
    type RoleSearchParams = CommonType.RecordNullable<
      Pick<Api.SystemManage.Role, 'roleName' | 'roleCode' | 'status'> & CommonSearchParams
    >;

    /** role list */
    type RoleList = Common.PaginatingQueryRecord<Role>;

    /** all role */
    type AllRole = Pick<Role, 'id' | 'roleName' | 'roleCode'>;

    /**
     * user gender
     *
     * - "1": "male"
     * - "2": "female"
     */
    type UserGender = '1' | '2';

    /** user */
    type User = Common.CommonRecord<{
      /** user id */
      userId: number;
      /** user name (QQ号) */
      username: string;
      /** user nick name */
      nickname: string;
      /** user email */
      email: string;
      /** user phone */
      phone: string;
      /** user avatar */
      avatar: string | null;
      /** user role (数值) */
      role: number;
      /** user role string */
      userRole: string;
      /** user balance */
      balance: number;
      /** user price rate */
      userRate: number;
      /** invite code */
      inviteCode: string;
      /** total recharge amount */
      totalRecharge?: number;
      /** referrer id */
      referrerId?: number;
      /** level id */
      levelId?: number;
      /** superior user id */
      sid?: number;
      /** user roles array */
      roles: string[];
      /** user permissions array */
      permissions: string[];
    }>;

    /** user search params */
    type UserSearchParams = CommonType.RecordNullable<
      Pick<Api.SystemManage.User, 'username' | 'nickname' | 'phone' | 'email' | 'userRole' | 'status'> &
        CommonSearchParams & {
          keyword?: string;
        }
    >;

    /** user list */
    type UserList = Common.PaginatingQueryRecord<User>;

    /**
     * menu type
     *
     * - "1": directory
     * - "2": menu
     */
    type MenuType = '1' | '2';

    type MenuButton = {
      /**
       * button code
       *
       * it can be used to control the button permission
       */
      code: string;
      /** button description */
      desc: string;
    };

    /**
     * icon type
     *
     * - "1": iconify icon
     * - "2": local icon
     */
    type IconType = '1' | '2';

    type MenuPropsOfRoute = Pick<
      import('vue-router').RouteMeta,
      | 'i18nKey'
      | 'keepAlive'
      | 'constant'
      | 'order'
      | 'href'
      | 'hideInMenu'
      | 'activeMenu'
      | 'multiTab'
      | 'fixedIndexInTab'
      | 'query'
    >;

    type Menu = Common.CommonRecord<{
      /** parent menu id */
      parentId: number;
      /** menu type */
      menuType: MenuType;
      /** menu name */
      menuName: string;
      /** route name */
      routeName: string;
      /** route path */
      routePath: string;
      /** component */
      component?: string;
      /** iconify icon name or local icon name */
      icon: string;
      /** icon type */
      iconType: IconType;
      /** buttons */
      buttons?: MenuButton[] | null;
      /** children menu */
      children?: Menu[] | null;
    }> &
      MenuPropsOfRoute;

    /** menu list */
    type MenuList = Common.PaginatingQueryRecord<Menu>;

    type MenuTree = {
      id: number;
      label: string;
      pId: number;
      children?: MenuTree[];
    };
  }

  namespace License {
    /** 授权验证结果 */
    interface ValidateResult {
      status: string;
      licenseKey?: string;
      domain?: string;
      expiryDate?: string;
      plan?: {
        name: string;
        maxProducts: number;
        maxUsers: number;
        features: string[];
      };
      validatedAt?: string;
      needsRevalidation?: boolean;
      inGracePeriod?: boolean;
      gracePeriodEnd?: string;
    }

    /** 授权状态结果 */
    interface StatusResult {
      isAuthorized: boolean;
      status: string;
      message: string;
      // 详细授权信息
      licenseKey: string; // 脱敏显示
      domain: string;
      expiryDate: string | null;
      validatedAt: string | null;
      boundAt: string | null;
      plan: string | null;
      planDescription: string | null;
      startDate: string | null;
      daysLeft: number | null;
      maxProducts: number | null;
      maxUsers: number | null;
      // JSON解析后的字段
      planFeatures: any;
      bindingInfo: any;
      usageStats: any;
      // 宽限期信息
      gracePeriodStart: string | null;
      gracePeriodEnd: string | null;
      inGracePeriod: boolean;
      // 验证信息
      lastExternalValidation: string | null;
      validationCount: number;
    }

    /** 重置授权结果 */
    interface ResetResult {
      success: boolean;
      message?: string;
    }

    /** 授权详细信息结果（管理员专用） */
    interface DetailsResult {
      // 基础信息
      status: string;
      licenseKey?: string;
      domain?: string;
      expiryDate?: string;
      plan?: string;
      validatedAt?: string;
      needsRevalidation?: boolean;
      inGracePeriod?: boolean;
      gracePeriodEnd?: string;
      securityStatus?: string;
      lastExternalValidation?: string;
      validationCount?: number;
      createdAt?: string;
      updatedAt?: string;

      // 扩展信息 - 来自外部授权系统
      startDate?: string;
      daysLeft?: number;
      planDescription?: string;
      maxProducts?: number;
      maxUsers?: number;

      // 结构化数据
      planFeatures?: string[];
      bindingInfo?: {
        id: string;
        domain: string;
        ip: string;
        bindTime: string;
        status: boolean;
        createdAt: string;
        isCurrentDomain: boolean;
        isCurrentIp: boolean;
      };
      usageStats?: {
        totalValidations: number;
        todayValidations: number;
        weekValidations: number;
        lastValidation: string | null;
        firstUsed: string;
      };

      // 完整的外部系统响应数据
      externalData?: any;
    }

    /** 授权绑定结果 */
    interface BindResult {
      success: boolean;
      message: string;
      data: {
        licenseKey: string;
        domain: string;
        boundAt: string;
      };
      status: number;
    }

    /** 授权系统登录结果 */
    interface LoginResult {
      success: boolean;
      message: string;
      data: {
        token: string;
        refreshToken: string;
      };
      status: number;
    }

    /** 授权密钥列表结果 */
    interface KeysResult {
      success: boolean;
      message: string;
      data: {
        keys: Array<{
          id: string;
          licenseKey: string;
          status: string;
          domain: string | null;
          createdAt: string;
          expiryDate: string;
          note: string | null;
        }>;
        total: number;
        page: number;
        pageSize: number;
      };
      status: number;
    }

    /** 系统信息结果 */
    interface SystemInfoResult {
      success: boolean;
      message: string;
      data: {
        version: string;
        apiVersion: string;
        activeKeys: number;
        totalKeys: number;
      };
      status: number;
    }

    /** 授权计划结果 */
    interface PlansResult {
      success: boolean;
      message: string;
      data: Array<{
        id: string;
        name: string;
        description: string;
        price: number;
        duration: number;
        features: string[];
        maxDomains: number;
        isActive: boolean;
        createdAt: string;
        updatedAt: string;
      }>;
      status: number;
    }
  }

  /** 系统配置模块 */
  namespace Config {
    /** 系统配置 */
    interface SystemConfig {
      // 基础配置
      systemName: string;
      systemDescription: string;
      systemVersion: string;
      maintenanceMode: boolean;

      // 数据库配置（只读）
      dbHost: string;
      dbPort: number;
      dbName: string;
      dbPoolSize: number;

      // 安全配置
      jwtSecret?: string;
      tokenExpiry: number;
      passwordMinLength: number;
      enableTwoFactor: boolean;

      // 邮件配置
      smtpHost: string;
      smtpPort: number;
      emailFrom: string;
      emailPassword?: string;
      emailSSL: boolean;

      // 日志配置
      logLevel: string;
      logRetentionDays: number;
      enableFileLog: boolean;

      // 系统限制
      maxUploadSize: number;
      sessionTimeout: number;
      maxLoginAttempts: number;

      // 功能开关
      enableRegistration: boolean;
      enableGuestAccess: boolean;
      enableApiDocs: boolean;

      // 备份配置
      autoBackup: boolean;
      backupInterval: number;
      maxBackupFiles: number;

      // 监控配置
      enableMonitoring: boolean;
      monitoringInterval: number;
      alertThreshold: number;
    }

    /** 邮件测试配置 */
    interface EmailTestConfig {
      smtpHost: string;
      smtpPort: number;
      emailFrom: string;
      emailPassword: string;
      emailSSL: boolean;
      testEmail: string;
    }

    /** 系统状态 */
    interface SystemStatus {
      systemName: string;
      systemVersion: string;
      maintenanceMode: boolean;
      uptime: number;
      nodeVersion: string;
      platform: string;
      arch: string;
      memoryUsage: {
        rss: number;
        heapTotal: number;
        heapUsed: number;
        external: number;
        arrayBuffers: number;
      };
      cpuUsage: {
        user: number;
        system: number;
      };
      timestamp: string;
    }
  }

  /** 数据库管理模块 */
  namespace Database {
    /** 数据库信息 */
    interface DatabaseInfo {
      connection: {
        status: string;
        host: string;
        port: number;
        database: string;
        charset: string;
        version: string;
        uptime: number;
      };
      size: {
        totalSize: number;
        dataSize: number;
        indexSize: number;
        tableCount: number;
      };
      performance: {
        queries: number;
        slowQueries: number;
        connections: number;
        maxConnections: number;
      };
    }

    /** 表搜索参数 */
    interface TableSearchParams {
      page?: number;
      pageSize?: number;
      keyword?: string;
      tableType?: string;
    }

    /** 表信息 */
    interface TableInfo {
      tableName: string;
      tableType: string;
      tableRows: number | null;
      dataLength: number | null;
      indexLength: number | null;
      tableComment: string;
      createTime: string;
    }

    /** 表列表 */
    interface TableList {
      list: TableInfo[];
      pagination: {
        page: number;
        pageSize: number;
        total: number;
      };
    }

    /** 表结构 */
    interface TableStructure {
      Field: string;
      Type: string;
      Null: string;
      Key: string;
      Default: string | null;
      Extra: string;
      Comment: string;
    }

    /** SQL执行参数 */
    interface SQLExecuteParams {
      sql: string;
      limit?: number;
    }

    /** SQL执行结果 */
    interface SQLExecuteResult {
      columns: string[];
      rows: any[];
      affectedRows: number;
      executionTime: number;
    }
  }
}
