<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  type Platform29Product,
  type SyncLog,
  advancedSync29PlatformProducts,
  deactivateInvalidProducts,
  detectInvalidProducts,
  fetch29PlatformProducts,
  fetchSyncLogs,
  fetchExistingProducts as getExistingProductsAPI,
  sync29PlatformProducts,
  updateExistingProducts
} from '@/service/api/platform29';
import { fetchProviderList } from '@/service/api/provider';
import { createCategory, fetchProductCategories } from '@/service/api/product';

defineOptions({
  name: 'Platform29Docking'
});

// 响应式数据
const loading = ref(false);
const refreshing = ref(false);
const syncing = ref(false);
const logLoading = ref(false);
const isFiltering = ref(false);
const updatingExisting = ref(false);
const detectingInvalid = ref(false);
const deactivatingInvalid = ref(false);
const products = ref<Platform29Product[]>([]);
const selectedProducts = ref<Platform29Product[]>([]);
const syncLogs = ref<SyncLog[]>([]);
const providers = ref<any[]>([]);
const selectedProviderId = ref<number>(3); // 默认选择29平台服务商
const existingProducts = ref<any[]>([]); // 已对接的商品
const productTableRef = ref(); // 表格引用

// 筛选相关
const selectedCategory = ref('');
const searchKeyword = ref('');
const statusFilter = ref('');
const priceRangeFilter = ref('');
const categories = ref<any[]>([]);

// 批量操作相关
const priceMultiplier = ref(1.0);

// 移动端检测
const isMobile = ref(false);
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

// 高级同步相关
const showAdvancedSyncDialog = ref(false);
const localCategories = ref<any[]>([]);
const advancedSyncForm = reactive({
  categoryMode: 'auto',
  categoryId: undefined as number | undefined,
  newCategoryName: '',
  priceMultiplier: 1.0,
  options: ['enableProducts'],
  sortOrder: 10
});

// 失效商品检测相关
const showInvalidProductsDialog = ref(false);
const invalidProducts = ref<any[]>([]);
const selectedInvalidProducts = ref<any[]>([]);

// 防抖定时器
let searchTimer: NodeJS.Timeout | null = null;

// 缓存相关
const CACHE_DURATION = 5 * 60 * 1000; // 5分钟缓存
const cache = reactive({
  products: null as any,
  providers: null as any,
  existingProducts: null as any,
  timestamp: 0
});

// 商品分页
const pagination = reactive({
  page: 1,
  pageSize: 50,
  total: 0
});

// 日志分页
const logPagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

// 计算属性
const filteredProducts = computed(() => {
  let filtered = products.value;

  // 分类筛选
  if (selectedCategory.value) {
    filtered = filtered.filter(product => {
      if (product.category_name) {
        return product.category_name === selectedCategory.value;
      }
      return product.fenlei === selectedCategory.value;
    });
  }

  // 状态筛选
  if (statusFilter.value) {
    if (statusFilter.value === 'existing') {
      filtered = filtered.filter(product => product.isExisting);
    } else if (statusFilter.value === 'new') {
      filtered = filtered.filter(product => !product.isExisting);
    }
  }

  // 价格范围筛选
  if (priceRangeFilter.value) {
    filtered = filtered.filter(product => {
      const price = product.adjustedPrice || product.price || 0;
      switch (priceRangeFilter.value) {
        case '0-10':
          return price >= 0 && price <= 10;
        case '10-50':
          return price > 10 && price <= 50;
        case '50-100':
          return price > 50 && price <= 100;
        case '100+':
          return price > 100;
        default:
          return true;
      }
    });
  }

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(
      product =>
        product.name.toLowerCase().includes(keyword) ||
        product.content?.toLowerCase().includes(keyword) ||
        product.cid?.toString().includes(keyword)
    );
  }

  return filtered;
});

// 分页后的商品列表
const paginatedProducts = computed(() => {
  const start = (pagination.page - 1) * pagination.pageSize;
  const end = start + pagination.pageSize;
  return filteredProducts.value.slice(start, end);
});

// 已对接商品数量
const existingProductsCount = computed(() => {
  return products.value.filter(product => product.isExisting).length;
});

// 方法

// 缓存检查函数
const isCacheValid = () => {
  return Date.now() - cache.timestamp < CACHE_DURATION;
};

// 货源选择变化处理
const handleProviderChange = () => {
  // 清除商品数据，重新获取
  products.value = [];
  selectedProducts.value = [];
  pagination.page = 1;

  // 清除筛选条件
  selectedCategory.value = '';
  searchKeyword.value = '';
  statusFilter.value = '';
};

// 选择当前页所有商品
const handleSelectCurrentPage = () => {
  if (productTableRef.value) {
    productTableRef.value.clearSelection();
    paginatedProducts.value.forEach(product => {
      productTableRef.value.toggleRowSelection(product, true);
    });
  }
  ElMessage.success(`已选择当前页 ${paginatedProducts.value.length} 个商品`);
};

// 清除所有选择
const handleClearSelection = () => {
  selectedProducts.value = [];
  if (productTableRef.value) {
    productTableRef.value.clearSelection();
  }
  ElMessage.success('已清除所有选择');
};

// 防抖搜索输入处理
const handleSearchInput = (value: string) => {
  if (searchTimer) {
    clearTimeout(searchTimer);
  }

  isFiltering.value = true;
  searchTimer = setTimeout(() => {
    isFiltering.value = false;
    // 搜索时重置到第一页
    pagination.page = 1;
  }, 300);
};

// 分类筛选处理
const handleCategoryChange = () => {
  pagination.page = 1;
};

// 状态筛选处理
const handleStatusChange = () => {
  pagination.page = 1;
};

// 价格范围筛选处理
const handlePriceRangeChange = () => {
  pagination.page = 1;
};

// 清除筛选条件
const handleClearCategory = () => {
  selectedCategory.value = '';
  pagination.page = 1;
};

const handleClearSearch = () => {
  searchKeyword.value = '';
  pagination.page = 1;
};

const handleClearStatus = () => {
  statusFilter.value = '';
  pagination.page = 1;
};

const handleClearAllFilters = () => {
  selectedCategory.value = '';
  searchKeyword.value = '';
  statusFilter.value = '';
  priceRangeFilter.value = '';
  pagination.page = 1;
};

// 刷新处理
const handleRefresh = async () => {
  refreshing.value = true;
  try {
    // 强制刷新，不使用缓存
    await handleGetProducts(true);
  } finally {
    refreshing.value = false;
  }
};

// 一键更新已对接商品
const handleUpdateExisting = async () => {
  if (!selectedProviderId.value) {
    ElMessage.warning('请先选择货源');
    return;
  }

  try {
    await ElMessageBox.confirm('确定要更新该货源下所有已对接的商品吗？这将同步最新的价格和状态信息。', '确认更新', {
      type: 'warning'
    });

    updatingExisting.value = true;

    const response = await updateExistingProducts({
      providerId: selectedProviderId.value,
      updateAll: true
    });

    if (response.failedCount === 0) {
      ElMessage.success(`更新成功！共更新 ${response.successCount} 个商品`);
    } else {
      ElMessage.warning(`更新完成！成功 ${response.successCount} 个，失败 ${response.failedCount} 个`);
    }

    // 刷新商品列表
    await handleGetProducts(true);
  } catch (error: any) {
    if (error !== 'cancel') {
      // 优先显示后端返回的错误信息
      const errorMessage = error.response?.data?.msg || error.message || '更新失败';
      ElMessage.error(errorMessage);
    }
  } finally {
    updatingExisting.value = false;
  }
};

// 检测失效商品
const handleDetectInvalid = async () => {
  if (!selectedProviderId.value) {
    ElMessage.warning('请先选择货源');
    return;
  }

  try {
    detectingInvalid.value = true;

    const response = await detectInvalidProducts(selectedProviderId.value);
    invalidProducts.value = response.invalidProducts || [];

    showInvalidProductsDialog.value = true;

    if (invalidProducts.value.length === 0) {
      ElMessage.success('未发现失效商品，所有商品状态正常');
    } else {
      ElMessage.warning(`发现 ${invalidProducts.value.length} 个失效商品`);
    }
  } catch (error: any) {
    ElMessage.error(error.message || '检测失败');
  } finally {
    detectingInvalid.value = false;
  }
};

const handleGetProducts = async (forceRefresh = false) => {
  try {
    loading.value = true;

    // 检查缓存
    if (!forceRefresh && isCacheValid() && cache.products) {
      products.value = cache.products;
      generateCategories();
      ElMessage.success(`从缓存加载！共 ${products.value.length} 个商品`);
      return;
    }

    // 重置分页和选择
    pagination.page = 1;
    selectedProducts.value = [];

    const response = await fetch29PlatformProducts();
    const rawProducts = response.products || [];

    // 获取已对接的商品
    await fetchExistingProducts();

    // 创建已对接商品的快速查找Map，提升性能
    const existingProductsMap = new Map();
    existingProducts.value.forEach(existing => {
      const key = `${existing.getnoun}_${existing.queryplat}`;
      existingProductsMap.set(key, true);
    });

    // 处理商品数据，添加已对接状态和价格信息
    products.value = rawProducts.map(product => ({
      ...product,
      originalPrice: product.price,
      adjustedPrice: product.price,
      isExisting: existingProductsMap.has(`${product.cid}_${selectedProviderId.value}`)
    }));

    // 缓存数据
    cache.products = products.value;
    cache.timestamp = Date.now();

    // 生成分类列表
    generateCategories();

    ElMessage.success(`获取成功！共 ${products.value.length} 个商品`);

    // 刷新日志
    await fetchLogs();
  } catch (error: any) {
    ElMessage.error(error.message || '获取29平台商品失败');
  } finally {
    loading.value = false;
  }
};

// 获取已对接的商品
const fetchExistingProducts = async () => {
  try {
    const response = await getExistingProductsAPI(selectedProviderId.value);
    existingProducts.value = response.products || [];
  } catch (error) {
    console.error('获取已对接商品失败:', error);
    existingProducts.value = [];
  }
};

// 生成分类列表
const generateCategories = () => {
  const categoryMap = new Map();

  products.value.forEach(product => {
    if (product.category_name) {
      // 如果有分类名称，直接使用
      const key = product.category_name;
      if (categoryMap.has(key)) {
        categoryMap.get(key).count++;
      } else {
        categoryMap.set(key, {
          key,
          name: product.category_name,
          count: 1,
          type: 'category_name'
        });
      }
    } else {
      // 如果没有分类名称，根据fenlei分组并提取共同词语
      const key = product.fenlei;
      if (categoryMap.has(key)) {
        categoryMap.get(key).count++;
        categoryMap.get(key).products.push(product);
      } else {
        categoryMap.set(key, {
          key,
          name: `分类${key}`,
          count: 1,
          type: 'fenlei',
          products: [product]
        });
      }
    }
  });

  // 对于fenlei类型的分类，尝试提取共同词语
  categoryMap.forEach((category, key) => {
    if (category.type === 'fenlei' && category.products) {
      const commonWords = extractCommonWords(category.products.map((p: Platform29Product) => p.name));
      if (commonWords) {
        category.name = commonWords;
      }
    }
  });

  categories.value = Array.from(categoryMap.values()).sort((a, b) => b.count - a.count);
};

// 提取商品名称中的共同词语
const extractCommonWords = (names: string[]): string => {
  if (names.length === 0) return '';
  if (names.length === 1) return names[0];

  // 简单的共同词语提取算法
  const words = names[0].split(/[\s\-\(\)（）]/);
  let commonWords = '';

  for (const word of words) {
    if (word.length >= 2 && names.every(name => name.includes(word))) {
      commonWords += word;
      if (commonWords.length >= 4) break; // 限制长度
    }
  }

  return commonWords || `分类${names.length}个商品`;
};

// 高级同步商品
const handleAdvancedSync = async () => {
  if (selectedProducts.value.length === 0) {
    ElMessage.warning('请先选择要同步的商品');
    return;
  }

  if (!selectedProviderId.value) {
    ElMessage.warning('请先选择对接货源');
    return;
  }

  // 验证表单
  if (advancedSyncForm.categoryMode === 'existing' && !advancedSyncForm.categoryId) {
    ElMessage.warning('请选择分类');
    return;
  }

  if (advancedSyncForm.categoryMode === 'new' && !advancedSyncForm.newCategoryName.trim()) {
    ElMessage.warning('请输入新分类名称');
    return;
  }

  try {
    syncing.value = true;

    // 使用调整后的价格进行同步
    const productsToSync = selectedProducts.value.map(product => ({
      ...product,
      price: (product.adjustedPrice || product.price) * advancedSyncForm.priceMultiplier
    }));

    const syncData = {
      selectedProducts: productsToSync,
      providerId: selectedProviderId.value,
      categoryId: advancedSyncForm.categoryId,
      createNewCategory: advancedSyncForm.categoryMode === 'new',
      newCategoryName: advancedSyncForm.newCategoryName
    };

    const response = await advancedSync29PlatformProducts(syncData);

    if (response.failedCount === 0) {
      ElMessage.success(`高级同步成功！共同步 ${response.successCount} 个商品`);
    } else {
      ElMessage.warning(`同步完成！成功 ${response.successCount} 个，失败 ${response.failedCount} 个`);
    }

    // 清除选择
    selectedProducts.value = [];
    if (productTableRef.value) {
      productTableRef.value.clearSelection();
    }

    // 更新本地商品的已对接状态
    const syncedCids = new Set(productsToSync.map(p => p.cid));
    products.value.forEach(product => {
      if (syncedCids.has(product.cid)) {
        product.isExisting = true;
      }
    });

    // 清除缓存
    cache.products = null;
    cache.timestamp = 0;

    // 关闭对话框
    showAdvancedSyncDialog.value = false;

    // 刷新分类列表
    await fetchLocalCategories();

    // 刷新日志
    await fetchLogs();
  } catch (error: any) {
    ElMessage.error(error.message || '高级同步失败');
  } finally {
    syncing.value = false;
  }
};

// 重置高级同步表单
const resetAdvancedSyncForm = () => {
  Object.assign(advancedSyncForm, {
    categoryMode: 'auto',
    categoryId: null,
    newCategoryName: '',
    priceMultiplier: 1.0,
    options: ['enableProducts'],
    sortOrder: 10
  });
};

// 一键下架失效商品
const handleDeactivateInvalid = async () => {
  if (invalidProducts.value.length === 0) {
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要下架 ${invalidProducts.value.length} 个失效商品吗？下架后用户将无法购买这些商品。`,
      '确认下架',
      { type: 'warning' }
    );

    deactivatingInvalid.value = true;

    const response = await deactivateInvalidProducts({
      providerId: selectedProviderId.value,
      productIds: invalidProducts.value.map(p => p.cid)
    });

    if (response.failedCount === 0) {
      ElMessage.success(`下架成功！共下架 ${response.successCount} 个商品`);
    } else {
      ElMessage.warning(`下架完成！成功 ${response.successCount} 个，失败 ${response.failedCount} 个`);
    }

    // 关闭对话框
    showInvalidProductsDialog.value = false;
    invalidProducts.value = [];

    // 刷新商品列表
    await handleGetProducts(true);
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '下架失败');
    }
  } finally {
    deactivatingInvalid.value = false;
  }
};

const handleSyncProducts = async () => {
  if (selectedProducts.value.length === 0) {
    ElMessage.warning('请先选择要同步的商品');
    return;
  }

  if (!selectedProviderId.value) {
    ElMessage.warning('请先选择对接货源');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要同步选中的 ${selectedProducts.value.length} 个商品到货源 ${selectedProviderId.value} 吗？`,
      '确认同步',
      { type: 'warning' }
    );

    syncing.value = true;

    // 使用调整后的价格进行同步
    const productsToSync = selectedProducts.value.map(product => ({
      ...product,
      price: product.adjustedPrice || product.price
    }));

    const response = await sync29PlatformProducts({
      selectedProducts: productsToSync,
      providerId: selectedProviderId.value
    });

    if (response.failedCount === 0) {
      ElMessage.success(`同步成功！共同步 ${response.successCount} 个商品`);
    } else {
      ElMessage.warning(`同步完成！成功 ${response.successCount} 个，失败 ${response.failedCount} 个`);
    }

    // 清除选择
    selectedProducts.value = [];
    if (productTableRef.value) {
      productTableRef.value.clearSelection();
    }

    // 更新本地商品的已对接状态，避免重新获取
    const syncedCids = new Set(productsToSync.map(p => p.cid));
    products.value.forEach(product => {
      if (syncedCids.has(product.cid)) {
        product.isExisting = true;
      }
    });

    // 清除缓存，下次获取时会重新加载
    cache.products = null;
    cache.timestamp = 0;

    // 刷新日志
    await fetchLogs();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '同步商品失败');
    }
  } finally {
    syncing.value = false;
  }
};

const handleSelectionChange = (selection: Platform29Product[]) => {
  selectedProducts.value = selection;
};

// 分页相关方法
const handlePageSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.page = 1;
  // 清除选择以避免跨页选择问题
  selectedProducts.value = [];
  if (productTableRef.value) {
    productTableRef.value.clearSelection();
  }
};

const handleCurrentPageChange = (page: number) => {
  pagination.page = page;
  // 清除选择以避免跨页选择问题
  selectedProducts.value = [];
  if (productTableRef.value) {
    productTableRef.value.clearSelection();
  }
};

const getCategoryName = (categoryKey: string) => {
  const category = categories.value.find(c => c.key === categoryKey);
  return category ? category.name : categoryKey;
};

// 批量操作方法
const handleApplyPriceMultiplier = () => {
  if (selectedProducts.value.length === 0) {
    ElMessage.warning('请先选择商品');
    return;
  }

  selectedProducts.value.forEach(product => {
    product.adjustedPrice = Number(((product.originalPrice || product.price) * priceMultiplier.value).toFixed(2));
  });

  // 更新products数组中对应的商品
  products.value.forEach(product => {
    const selected = selectedProducts.value.find(s => s.cid === product.cid);
    if (selected) {
      product.adjustedPrice = selected.adjustedPrice;
    }
  });

  ElMessage.success(`已应用 ${priceMultiplier.value} 倍价格调整`);
};

const fetchLogs = async () => {
  try {
    logLoading.value = true;
    const response = await fetchSyncLogs({
      page: logPagination.page,
      pageSize: logPagination.pageSize
    });

    syncLogs.value = response.list || [];
    logPagination.total = response.total || 0;
  } catch (error) {
    console.error('获取同步日志失败:', error);
  } finally {
    logLoading.value = false;
  }
};

const handleLogSizeChange = (size: number) => {
  logPagination.pageSize = size;
  logPagination.page = 1;
  fetchLogs();
};

const handleLogCurrentChange = (page: number) => {
  logPagination.page = page;
  fetchLogs();
};

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString();
};

// 获取货源列表
const fetchProviders = async () => {
  try {
    // 检查缓存
    if (isCacheValid() && cache.providers) {
      providers.value = cache.providers;
      return;
    }

    const response = await fetchProviderList({});
    providers.value = response.list || [];

    // 缓存数据
    cache.providers = providers.value;
    cache.timestamp = Date.now();
  } catch (error) {
    console.error('获取货源列表失败:', error);
    ElMessage.error('获取货源列表失败');
  }
};

// 获取本地分类列表
const fetchLocalCategories = async () => {
  try {
    const response = await fetchProductCategories();
    localCategories.value = response || [];
  } catch (error) {
    console.error('获取本地分类失败:', error);
  }
};

// 初始化
onMounted(async () => {
  await fetchProviders();
  await fetchLocalCategories();
  await fetchLogs();
  checkMobile();
  window.addEventListener('resize', checkMobile);
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <!-- 货源选择和操作区域 -->
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-medium">29平台一键对接</span>
          <div v-if="products.length > 0" class="flex gap-12px text-sm text-gray-600">
            <span>总商品: {{ products.length }}</span>
            <span>已对接: {{ existingProductsCount }}</span>
            <span>未对接: {{ products.length - existingProductsCount }}</span>
          </div>
        </div>
      </template>

      <div class="space-y-16px">
        <!-- 货源选择 -->
        <div class="flex items-center gap-16px border border-blue-200 rounded-lg bg-blue-50 p-16px">
          <div class="flex items-center gap-8px">
            <icon-ic-round-store class="text-lg text-blue-600" />
            <span class="text-blue-800 font-medium">选择货源：</span>
          </div>
          <ElSelect
            v-model="selectedProviderId"
            placeholder="请选择货源"
            style="width: 200px"
            size="default"
            @change="handleProviderChange"
          >
            <ElOption
              v-for="provider in providers"
              :key="provider.providerId"
              :label="provider.name"
              :value="provider.providerId"
            />
          </ElSelect>
          <span class="text-sm text-blue-600">不同货源的商品将分别管理，避免重复对接</span>
        </div>

        <!-- 操作按钮区域 -->
        <div class="flex flex-col gap-12px md:flex-row md:items-center md:justify-between">
          <div class="flex flex-wrap gap-8px">
            <ElButton type="primary" :loading="loading" size="default" @click="() => handleGetProducts()">
              <template #icon>
                <icon-ic-round-download class="text-icon" />
              </template>
              获取29平台商品
            </ElButton>
            <ElButton :loading="refreshing" size="default" @click="handleRefresh">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              刷新数据
            </ElButton>
            <ElButton type="warning" :loading="updatingExisting" size="default" @click="handleUpdateExisting">
              <template #icon>
                <icon-ic-round-update class="text-icon" />
              </template>
              一键更新已对接
            </ElButton>
            <ElButton type="danger" :loading="detectingInvalid" size="default" @click="handleDetectInvalid">
              <template #icon>
                <icon-ic-round-warning class="text-icon" />
              </template>
              检测失效商品
            </ElButton>
          </div>

          <div v-if="selectedProducts.length > 0" class="flex flex-wrap gap-8px">
            <ElButton type="success" :loading="syncing" size="default" @click="showAdvancedSyncDialog = true">
              <template #icon>
                <icon-ic-round-sync class="text-icon" />
              </template>
              高级同步 ({{ selectedProducts.length }})
            </ElButton>
          </div>
        </div>
      </div>
    </ElCard>

    <!-- 商品列表区域 -->
    <ElCard v-if="products.length > 0 || loading">
      <template #header>
        <div class="flex flex-col gap-12px lg:flex-row lg:items-center lg:justify-between">
          <span class="font-medium">商品列表</span>

          <!-- 筛选工具栏 -->
          <div class="flex flex-wrap items-center gap-8px">
            <ElSelect
              v-model="selectedCategory"
              placeholder="选择分类"
              clearable
              filterable
              class="w-180px"
              size="small"
              @change="handleCategoryChange"
            >
              <ElOption label="全部分类" value="" />
              <ElOption
                v-for="category in categories"
                :key="category.key"
                :label="`${category.name} (${category.count})`"
                :value="category.key"
              />
            </ElSelect>

            <ElSelect
              v-model="statusFilter"
              placeholder="全部状态"
              clearable
              class="w-120px"
              size="small"
              @change="handleStatusChange"
            >
              <ElOption label="全部状态" value="" />
              <ElOption label="已对接" value="existing" />
              <ElOption label="未对接" value="new" />
            </ElSelect>

            <ElSelect
              v-model="priceRangeFilter"
              placeholder="价格范围"
              clearable
              class="w-120px"
              size="small"
              @change="handlePriceRangeChange"
            >
              <ElOption label="全部价格" value="" />
              <ElOption label="0-10元" value="0-10" />
              <ElOption label="10-50元" value="10-50" />
              <ElOption label="50-100元" value="50-100" />
              <ElOption label="100元以上" value="100+" />
            </ElSelect>

            <ElInput
              v-model="searchKeyword"
              placeholder="搜索商品名称/说明"
              clearable
              class="w-200px"
              size="small"
              @input="handleSearchInput"
            >
              <template #prefix>
                <icon-ic-round-search class="text-icon" />
              </template>
            </ElInput>
          </div>
        </div>
      </template>

      <!-- 分页和批量操作工具栏 -->
      <div v-if="products.length > 0" class="mb-16px">
        <!-- 分页组件 -->
        <div class="mb-12px flex items-center justify-between">
          <div class="text-sm text-gray-500">
            共 {{ products.length }} 个商品，筛选后 {{ filteredProducts.length }} 个，已选择
            {{ selectedProducts.length }} 个
            <span v-if="isFiltering" class="ml-8px text-primary">
              <icon-ic-round-search class="animate-spin text-xs" />
              筛选中...
            </span>
          </div>
          <ElPagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :total="filteredProducts.length"
            :page-sizes="[20, 50, 100, 200, 500]"
            layout="sizes, prev, pager, next, jumper"
            small
            @size-change="handlePageSizeChange"
            @current-change="handleCurrentPageChange"
          />
        </div>

        <!-- 批量操作工具栏 -->
        <div class="rounded-lg bg-gray-50 p-12px">
          <div class="flex flex-col gap-12px lg:flex-row lg:items-center lg:justify-between">
            <div class="flex flex-wrap items-center gap-12px">
              <span class="text-sm text-gray-600">批量操作：</span>
              <ElButton size="small" @click="handleSelectCurrentPage">选择当前页</ElButton>
              <ElButton size="small" @click="handleClearSelection">清除选择</ElButton>
              <div class="flex items-center gap-4px">
                <span class="text-xs text-gray-500">价格倍数：</span>
                <ElInputNumber
                  v-model="priceMultiplier"
                  :min="0.1"
                  :max="10"
                  :step="0.1"
                  :precision="1"
                  size="small"
                  class="w-80px"
                />
                <ElButton
                  size="small"
                  type="primary"
                  :disabled="selectedProducts.length === 0"
                  @click="handleApplyPriceMultiplier"
                >
                  应用
                </ElButton>
              </div>
            </div>

            <div class="text-sm text-gray-500">
              第 {{ (pagination.page - 1) * pagination.pageSize + 1 }} -
              {{ Math.min(pagination.page * pagination.pageSize, filteredProducts.length) }} 条
            </div>
          </div>
        </div>
      </div>

      <!-- 筛选标签 -->
      <div v-if="paginatedProducts.length > 0 && (selectedCategory || searchKeyword || statusFilter)" class="mb-16px">
        <div class="flex gap-8px">
          <ElTag v-if="selectedCategory" type="info" closable @close="handleClearCategory">
            {{ getCategoryName(selectedCategory) }}
          </ElTag>
          <ElTag v-if="searchKeyword" type="warning" closable @close="handleClearSearch">
            搜索: {{ searchKeyword }}
          </ElTag>
          <ElTag v-if="statusFilter" type="success" closable @close="handleClearStatus">
            状态: {{ statusFilter === 'existing' ? '已对接' : '未对接' }}
          </ElTag>
        </div>
      </div>

      <!-- 商品表格 -->
      <div v-if="paginatedProducts.length > 0" class="overflow-x-auto">
        <ElTable
          ref="productTableRef"
          v-loading="loading"
          :data="paginatedProducts"
          class="min-w-1000px w-full"
          row-key="cid"
          max-height="70vh"
          stripe
          border
          :size="isMobile ? 'small' : 'default'"
          @selection-change="handleSelectionChange"
        >
          <ElTableColumn type="selection" width="55" />
          <ElTableColumn prop="cid" label="CID" width="80" />
          <ElTableColumn label="对接状态" width="100">
            <template #default="{ row }">
              <ElTag v-if="row.isExisting" type="success" size="small">已对接</ElTag>
              <ElTag v-else type="info" size="small">未对接</ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="name" label="商品名称" min-width="200" show-overflow-tooltip />
          <ElTableColumn prop="content" label="说明" min-width="200" show-overflow-tooltip />
          <ElTableColumn label="分类" width="150">
            <template #default="{ row }">
              <div class="text-xs">
                <div v-if="row.category_name" class="text-primary">{{ row.category_name }}</div>
                <div class="text-gray-500">ID: {{ row.fenlei }}</div>
              </div>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="price" label="原价格" width="100">
            <template #default="{ row }">
              <span class="text-gray-500">¥{{ row.originalPrice || 0 }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="adjustedPrice" label="调整后价格" width="120">
            <template #default="{ row }">
              <span class="text-primary font-medium">¥{{ row.adjustedPrice || row.price || 0 }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="status" label="状态" width="80">
            <template #default="{ row }">
              <ElTag :type="row.status === '1' ? 'success' : 'danger'" size="small">
                {{ row.status === '1' ? '启用' : '禁用' }}
              </ElTag>
            </template>
          </ElTableColumn>
        </ElTable>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!loading && products.length === 0" class="py-32px text-center">
        <ElEmpty description="暂无商品数据，请点击获取29平台商品按钮获取最新商品信息" :image-size="120">
          <ElButton type="primary" @click="() => handleGetProducts()">获取29平台商品</ElButton>
        </ElEmpty>
      </div>

      <!-- 筛选无结果 -->
      <div v-else-if="!loading && products.length > 0 && filteredProducts.length === 0" class="py-32px text-center">
        <ElEmpty description="没有找到符合条件的商品" :image-size="100">
          <ElButton @click="handleClearAllFilters">清除所有筛选条件</ElButton>
        </ElEmpty>
      </div>

      <!-- 加载状态 -->
      <div v-else-if="loading" class="py-32px text-center">
        <ElSkeleton :rows="8" animated />
        <div class="mt-16px text-sm text-gray-500">正在获取商品数据，请稍候...</div>
      </div>
    </ElCard>

    <!-- 高级同步对话框 -->
    <ElDialog v-model="showAdvancedSyncDialog" title="高级同步设置" width="600px" @close="resetAdvancedSyncForm">
      <ElForm :model="advancedSyncForm" label-width="120px">
        <ElFormItem label="同步商品数量">
          <ElTag type="info">{{ selectedProducts.length }} 个商品</ElTag>
        </ElFormItem>

        <ElFormItem label="分类设置" required>
          <ElRadioGroup v-model="advancedSyncForm.categoryMode">
            <ElRadio value="existing">分配到现有分类</ElRadio>
            <ElRadio value="new">创建新分类</ElRadio>
            <ElRadio value="auto">智能分类（根据商品名称自动识别）</ElRadio>
          </ElRadioGroup>
        </ElFormItem>

        <ElFormItem v-if="advancedSyncForm.categoryMode === 'existing'" label="选择分类">
          <ElSelect v-model="advancedSyncForm.categoryId" placeholder="请选择分类" filterable style="width: 100%">
            <ElOption
              v-for="category in localCategories"
              :key="category.category_id"
              :label="`${category.name} (${category.product_count}个商品)`"
              :value="category.category_id"
            />
          </ElSelect>
        </ElFormItem>

        <ElFormItem v-if="advancedSyncForm.categoryMode === 'new'" label="新分类名称">
          <ElInput
            v-model="advancedSyncForm.newCategoryName"
            placeholder="请输入新分类名称"
            maxlength="50"
            show-word-limit
          />
        </ElFormItem>

        <ElFormItem label="价格调整">
          <div class="flex items-center gap-8px">
            <ElInputNumber
              v-model="advancedSyncForm.priceMultiplier"
              :min="0.1"
              :max="10"
              :step="0.1"
              :precision="1"
              style="width: 120px"
            />
            <span class="text-sm text-gray-500">倍数（当前价格 × 倍数）</span>
          </div>
        </ElFormItem>

        <ElFormItem label="同步选项">
          <ElCheckboxGroup v-model="advancedSyncForm.options">
            <ElCheckbox value="updateExisting">更新已存在的商品</ElCheckbox>
            <ElCheckbox value="enableProducts">启用同步的商品</ElCheckbox>
            <ElCheckbox value="setSort">设置排序权重</ElCheckbox>
          </ElCheckboxGroup>
        </ElFormItem>

        <ElFormItem v-if="advancedSyncForm.options.includes('setSort')" label="排序权重">
          <ElInputNumber v-model="advancedSyncForm.sortOrder" :min="1" :max="999" style="width: 120px" />
        </ElFormItem>
      </ElForm>

      <template #footer>
        <div class="flex justify-end gap-8px">
          <ElButton @click="showAdvancedSyncDialog = false">取消</ElButton>
          <ElButton type="primary" :loading="syncing" @click="handleAdvancedSync">开始同步</ElButton>
        </div>
      </template>
    </ElDialog>

    <!-- 失效商品检测对话框 -->
    <ElDialog v-model="showInvalidProductsDialog" title="失效商品检测结果" width="800px">
      <div v-if="invalidProducts.length > 0">
        <ElAlert
          title="检测到失效商品"
          :description="`发现 ${invalidProducts.length} 个失效商品，这些商品在上游已下架或状态异常`"
          type="warning"
          show-icon
          class="mb-16px"
        />

        <ElTable :data="invalidProducts" max-height="400px">
          <ElTableColumn type="selection" width="55" />
          <ElTableColumn prop="name" label="商品名称" min-width="200" />
          <ElTableColumn prop="cid" label="CID" width="80" />
          <ElTableColumn prop="price" label="价格" width="80">
            <template #default="{ row }">¥{{ row.price }}</template>
          </ElTableColumn>
          <ElTableColumn prop="status" label="状态" width="100">
            <template #default="{ row }">
              <ElTag :type="row.status === 1 ? 'success' : 'danger'" size="small">
                {{ row.status === 1 ? '启用' : '禁用' }}
              </ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="reason" label="失效原因" min-width="150" />
        </ElTable>
      </div>

      <div v-else class="py-32px text-center">
        <ElEmpty description="未发现失效商品，所有商品状态正常" :image-size="100" />
      </div>

      <template #footer>
        <div class="flex justify-between">
          <div>
            <ElButton @click="showInvalidProductsDialog = false">关闭</ElButton>
          </div>
          <div v-if="invalidProducts.length > 0">
            <ElButton type="danger" :loading="deactivatingInvalid" @click="handleDeactivateInvalid">
              一键下架失效商品
            </ElButton>
          </div>
        </div>
      </template>
    </ElDialog>

    <!-- 同步日志 -->
    <ElCard>
      <template #header>
        <span class="font-medium">同步日志</span>
      </template>

      <ElTable v-loading="logLoading" :data="syncLogs" class="w-full">
        <ElTableColumn prop="sync_type" label="同步类型" width="120">
          <template #default="{ row }">
            <ElTag size="small" type="info">
              {{ row.sync_type === 'getclass' ? '获取商品' : '同步商品' }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="sync_status" label="状态" width="100">
          <template #default="{ row }">
            <ElTag
              size="small"
              :type="row.sync_status === 'success' ? 'success' : row.sync_status === 'failed' ? 'danger' : 'warning'"
            >
              {{ row.sync_status === 'success' ? '成功' : row.sync_status === 'failed' ? '失败' : '部分成功' }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="sync_count" label="数量" width="80" />
        <ElTableColumn prop="error_message" label="错误信息" min-width="200" show-overflow-tooltip />
        <ElTableColumn prop="create_time" label="同步时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.create_time) }}
          </template>
        </ElTableColumn>
      </ElTable>

      <!-- 分页 -->
      <div class="mt-16px flex justify-center">
        <ElPagination
          v-model:current-page="logPagination.page"
          v-model:page-size="logPagination.pageSize"
          :total="logPagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleLogSizeChange"
          @current-change="handleLogCurrentChange"
        />
      </div>
    </ElCard>
  </div>
</template>

<style scoped>
/* 表格优化 */
:deep(.el-table) {
  --el-table-border-color: var(--el-border-color-lighter);
}

:deep(.el-table__header-wrapper) {
  background-color: var(--el-fill-color-lighter);
}

:deep(.el-table__row:hover) {
  background-color: var(--el-fill-color-light);
}

/* 分页优化 */
:deep(.el-pagination) {
  --el-pagination-font-size: 13px;
}

/* 加载动画 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 标签间距优化 */
.el-tag + .el-tag {
  margin-left: 8px;
}

/* 卡片阴影优化 */
:deep(.el-card) {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--el-border-color-lighter);
}

:deep(.el-card:hover) {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-card {
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 按钮组间距 */
.el-button + .el-button {
  margin-left: 8px;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .flex-wrap {
    flex-direction: column;
    align-items: stretch;
  }

  .w-200px {
    width: 100% !important;
  }
}
</style>
