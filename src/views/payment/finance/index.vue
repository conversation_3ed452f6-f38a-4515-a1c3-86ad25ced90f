<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { fetchFinanceList } from '@/service/api/finance';
import { useAuthStore } from '@/store/modules/auth';
import { formatCurrency, formatDateTime as formatDate, safeToFixed } from '@/utils/format';

defineOptions({
  name: 'PaymentFinance'
});

const authStore = useAuthStore();

// 响应式数据
const loading = ref(false);
const financeList = ref<any[]>([]);
const currentBalance = ref(Number(authStore.userInfo?.balance) || 0);

// 筛选条件
const filters = reactive({
  type: '',
  dateRange: [] as string[]
});

// 分页信息
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
});

// 统计数据
const statistics = reactive({
  totalIncome: 0,
  totalExpense: 0,
  monthlyFlow: 0
});

// 获取财务记录列表
async function fetchFinanceData() {
  try {
    loading.value = true;

    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      type: filters.type || undefined
    };

  const result = await fetchFinanceList(params);
  financeList.value = result.list;
  pagination.total = result.pagination.total;

    // 计算统计数据
    calculateStatistics();
  } catch (error: any) {
    // 打印后端返回的详细信息，便于定位问题
    console.error('获取财务记录失败:', error);
    if (error.response && error.response.data) {
      console.error('后端响应:', error.response.data);
      const backendMsg = error.response.data.msg || error.response.data.message;
      window.$message?.error(backendMsg || '获取财务记录失败');
    } else if (error.message) {
      window.$message?.error(error.message);
    } else {
      window.$message?.error('获取财务记录失败');
    }
  } finally {
    loading.value = false;
  }
}

// 计算统计数据
function calculateStatistics() {
  let totalIncome = 0;
  let totalExpense = 0;
  let monthlyFlow = 0;

  const currentMonth = new Date().getMonth();
  const currentYear = new Date().getFullYear();

  financeList.value.forEach(record => {
    const amount = Number(record.amount);
    const recordDate = new Date(record.create_time);

    if (amount > 0) {
      totalIncome += amount;
    } else {
      totalExpense += Math.abs(amount);
    }

    // 计算本月流水
    if (recordDate.getMonth() === currentMonth && recordDate.getFullYear() === currentYear) {
      monthlyFlow += Math.abs(amount);
    }
  });

  statistics.totalIncome = totalIncome;
  statistics.totalExpense = totalExpense;
  statistics.monthlyFlow = monthlyFlow;
}

// 获取类型标签类型
function getTypeTagType(type: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    recharge: 'success',
    consume: 'warning',
    refund: 'info',
    commission: 'primary'
  };
  return typeMap[type] || 'info';
}

// 获取类型文本
function getTypeText(type: string) {
  const textMap: Record<string, string> = {
    recharge: '充值',
    consume: '消费',
    refund: '退款',
    commission: '佣金'
  };
  return textMap[type] || type;
}

// 处理搜索
function handleSearch() {
  pagination.page = 1;
  fetchFinanceData();
}

// 处理重置
function handleReset() {
  filters.type = '';
  pagination.page = 1;
  fetchFinanceData();
}

// 处理页面大小变化
function handleSizeChange() {
  pagination.page = 1;
  fetchFinanceData();
}

// 处理当前页变化
function handleCurrentChange() {
  fetchFinanceData();
}

// 生命周期
onMounted(() => {
  fetchFinanceData();
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <ElCard>
      <template #header>
        <span class="text-lg font-medium">财务记录</span>
      </template>
      <!-- 筛选条件 -->
      <ElForm :model="filters" label-width="100px" class="mb-16px">
        <div class="grid grid-cols-1 gap-16px md:grid-cols-4">
          <ElFormItem label="记录类型">
            <ElSelect v-model="filters.type" placeholder="全部类型" clearable class="w-full">
              <ElOption label="充值" value="recharge" />
              <ElOption label="消费" value="consume" />
              <ElOption label="退款" value="refund" />
              <ElOption label="佣金" value="commission" />
            </ElSelect>
          </ElFormItem>

          <ElFormItem label="时间范围">
            <ElDatePicker
              v-model="filters.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="w-full"
            />
          </ElFormItem>

          <ElFormItem>
            <div class="flex gap-8px">
              <ElButton type="primary" @click="handleSearch">查询</ElButton>
              <ElButton @click="handleReset">重置</ElButton>
            </div>
          </ElFormItem>
        </div>
      </ElForm>
    </ElCard>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 gap-16px md:grid-cols-4">
      <ElCard class="stat-card">
        <div class="flex items-center justify-between">
          <div>
            <div class="mb-4px text-sm text-gray-600">总收入</div>
            <div class="text-xl text-green-600 font-bold">¥{{ safeToFixed(statistics.totalIncome) }}</div>
          </div>
          <div class="text-4xl text-green-600 opacity-20">
            <icon-mdi-trending-up />
          </div>
        </div>
      </ElCard>

      <ElCard class="stat-card">
        <div class="flex items-center justify-between">
          <div>
            <div class="mb-4px text-sm text-gray-600">总支出</div>
            <div class="text-xl text-red-600 font-bold">¥{{ safeToFixed(statistics.totalExpense) }}</div>
          </div>
          <div class="text-4xl text-red-600 opacity-20">
            <icon-mdi-trending-down />
          </div>
        </div>
      </ElCard>

      <ElCard class="stat-card">
        <div class="flex items-center justify-between">
          <div>
            <div class="mb-4px text-sm text-gray-600">当前余额</div>
            <div class="text-xl text-blue-600 font-bold">¥{{ safeToFixed(currentBalance) }}</div>
          </div>
          <div class="text-4xl text-blue-600 opacity-20">
            <icon-mdi-wallet />
          </div>
        </div>
      </ElCard>

      <ElCard class="stat-card">
        <div class="flex items-center justify-between">
          <div>
            <div class="mb-4px text-sm text-gray-600">本月流水</div>
            <div class="text-xl text-purple-600 font-bold">¥{{ safeToFixed(statistics.monthlyFlow) }}</div>
          </div>
          <div class="text-4xl text-purple-600 opacity-20">
            <icon-mdi-chart-line />
          </div>
        </div>
      </ElCard>
    </div>

    <!-- 财务记录表格 -->
    <ElCard>
      <template #header>
        <span class="font-medium">财务记录</span>
      </template>

      <!-- 财务记录表格 -->
      <ElTable v-loading="loading" :data="financeList" stripe class="w-full">
        <ElTableColumn prop="finance_id" label="记录ID" width="80" />

        <ElTableColumn prop="type" label="类型" width="80">
          <template #default="{ row }">
            <ElTag :type="getTypeTagType(row.type)" size="small">
              {{ getTypeText(row.type) }}
            </ElTag>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="amount" label="金额" width="120">
          <template #default="{ row }">
            <span :class="row.amount > 0 ? 'text-green-600' : 'text-red-600'" class="font-medium">
              {{ row.amount > 0 ? '+' : '' }}¥{{ safeToFixed(row.amount) }}
            </span>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="balance_before" label="变动前余额" width="120">
          <template #default="{ row }">¥{{ safeToFixed(row.balance_before) }}</template>
        </ElTableColumn>

        <ElTableColumn prop="balance_after" label="变动后余额" width="120">
          <template #default="{ row }">¥{{ safeToFixed(row.balance_after) }}</template>
        </ElTableColumn>

        <ElTableColumn prop="description" label="描述" min-width="150" show-overflow-tooltip />

        <ElTableColumn prop="operator_name" label="操作员" width="100">
          <template #default="{ row }">
            {{ row.operator_name || '系统' }}
          </template>
        </ElTableColumn>

        <ElTableColumn prop="create_time" label="时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.create_time) }}
          </template>
        </ElTableColumn>
      </ElTable>

      <!-- 分页 -->
      <div class="mt-16px flex justify-center md:justify-end">
        <ElPagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </ElCard>
  </div>
</template>
