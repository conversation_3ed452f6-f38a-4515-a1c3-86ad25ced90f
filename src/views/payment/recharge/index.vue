<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
// 移除未使用的图标导入
import { createRechargeOrder, fetchPaymentList, fetchRechargeConfig } from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
import { formatDateTime, safeToFixed } from '@/utils/format';

defineOptions({
  name: 'PaymentRecharge'
});

const authStore = useAuthStore();

// 响应式数据
const formRef = ref<FormInstance | null>(null);
const submitting = ref(false);
const showAllRecords = ref(false);
const userBalance = ref(Number(authStore.userInfo?.balance) || 0);
const paymentMethods = ref<any[]>([]);
const rechargeRecords = ref<any[]>([]);

// 快捷金额选项
const quickAmounts = [10, 50, 100, 200, 500, 1000];

// 充值表单
const rechargeForm = reactive({
  amount: null as number | null,
  paymentMethod: ''
});

// 表单验证规则
const formRules: FormRules = {
  amount: [
    { required: true, message: '请输入充值金额', trigger: 'blur' },
    { type: 'number', min: 1, max: 10000, message: '充值金额必须在1-10000之间', trigger: 'blur' }
  ],
  paymentMethod: [{ required: true, message: '请选择支付方式', trigger: 'change' }]
};

// 获取支付方式配置
async function fetchPaymentMethods() {
  try {
    const result = await fetchRechargeConfig();
    const methods = result.map((item: any) => ({
      value: item.payment_method,
      label: item.method_name,
      status: item.status,
      icon: item.payment_method === 'alipay' ? 'credit-card' : 'money'
    }));

    paymentMethods.value = methods;

    // 默认选择第一个可用的支付方式
    const availableMethod = methods.find((m: any) => m.status === 1);
    if (availableMethod) {
      rechargeForm.paymentMethod = availableMethod.value;
    }
  } catch (error) {
    console.error('获取支付方式失败:', error);
    window.$message?.error('获取支付方式失败');
  }
}

// 获取充值记录
async function fetchRechargeRecords() {
  try {
    const result = await fetchPaymentList({
      page: 1,
      pageSize: 10,
      payment_type: 'recharge'
    });

    rechargeRecords.value = result.list || [];
  } catch (error) {
    console.error('获取充值记录失败:', error);
  }
}

// 提交充值
async function handleSubmit() {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    submitting.value = true;

    const result = await createRechargeOrder({
      amount: rechargeForm.amount!,
      payment_method: rechargeForm.paymentMethod
    });

    window.$message?.success('充值订单创建成功');

    // 这里应该跳转到支付页面或显示支付二维码
    console.log('支付信息:', result);

    // 刷新记录
    await fetchRechargeRecords();
  } catch (error: any) {
    console.error('创建充值订单失败:', error);
    window.$message?.error(error.message || '创建充值订单失败');
  } finally {
    submitting.value = false;
  }
}

// 获取支付方式文本
function getPaymentMethodText(method: string) {
  const methodMap: Record<string, string> = {
    alipay: '支付宝',
    wechat: '微信支付',
    balance: '余额支付'
  };
  return methodMap[method] || method;
}

// 获取状态类型
function getStatusType(status: number): 'primary' | 'success' | 'warning' | 'danger' | 'info' {
  const typeMap: Record<number, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    0: 'warning',
    1: 'success',
    2: 'danger',
    3: 'info'
  };
  return typeMap[status] || 'info';
}

// 获取状态文本
function getStatusText(status: number) {
  const textMap: Record<number, string> = {
    0: '待支付',
    1: '已支付',
    2: '支付失败',
    3: '已退款'
  };
  return textMap[status] || '未知';
}

// 生命周期
onMounted(() => {
  fetchPaymentMethods();
  fetchRechargeRecords();
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <ElCard>
      <template #header>
        <span class="text-lg font-medium">账户充值</span>
      </template>

      <div class="mx-auto max-w-600px">
        <!-- 当前余额显示 -->
        <ElCard class="mb-24px">
          <div class="flex items-center justify-between">
            <div>
              <div class="mb-4px text-sm text-gray-600">当前余额</div>
              <div class="text-2xl text-primary font-bold">¥{{ safeToFixed(userBalance) }}</div>
            </div>
            <div class="text-4xl text-primary opacity-20">
              <icon-mdi-wallet />
            </div>
          </div>
        </ElCard>

        <!-- 充值表单 -->
        <ElForm ref="formRef" :model="rechargeForm" :rules="formRules" label-width="100px" class="mt-24px">
          <!-- 充值金额 -->
          <ElFormItem label="充值金额" prop="amount">
            <ElInputNumber
              v-model="rechargeForm.amount"
              :min="1"
              :max="10000"
              :precision="2"
              placeholder="请输入充值金额"
              class="w-full"
            >
              <template #prefix>¥</template>
            </ElInputNumber>
            <div class="mt-8px">
              <ElButton
                v-for="amount in quickAmounts"
                :key="amount"
                size="small"
                class="mr-8px"
                @click="rechargeForm.amount = amount"
              >
                ¥{{ amount }}
              </ElButton>
            </div>
          </ElFormItem>

          <!-- 支付方式 -->
          <ElFormItem label="支付方式" prop="paymentMethod">
            <ElRadioGroup v-model="rechargeForm.paymentMethod" class="w-full">
              <div class="space-y-12px">
                <ElRadio
                  v-for="method in paymentMethods"
                  :key="method.value"
                  :value="method.value"
                  :disabled="method.status !== 1"
                  class="w-full"
                >
                  <div class="w-full flex items-center justify-between">
                    <div class="flex items-center">
                      <ElIcon size="20" class="mr-8px">
                        <component :is="method.icon" />
                      </ElIcon>
                      <span>{{ method.label }}</span>
                    </div>
                    <ElTag v-if="method.status !== 1" type="info" size="small">暂不可用</ElTag>
                  </div>
                </ElRadio>
              </div>
            </ElRadioGroup>
          </ElFormItem>

          <!-- 提交按钮 -->
          <ElFormItem>
            <ElButton type="primary" size="large" :loading="submitting" class="w-full" @click="handleSubmit">
              {{ submitting ? '处理中...' : `立即充值 ¥${rechargeForm.amount || 0}` }}
            </ElButton>
          </ElFormItem>
        </ElForm>

        <!-- 充值记录 -->
        <div class="mt-32px">
          <div class="mb-16px flex items-center justify-between">
            <h3 class="text-lg font-medium">最近充值记录</h3>
            <ElButton text @click="showAllRecords = !showAllRecords">
              {{ showAllRecords ? '收起' : '查看更多' }}
            </ElButton>
          </div>

          <ElTable :data="rechargeRecords" :max-height="showAllRecords ? 400 : 200" stripe>
            <ElTableColumn prop="payment_no" label="订单号" width="180" />
            <ElTableColumn prop="amount" label="金额" width="100">
              <template #default="{ row }">
                <span class="text-green-600 font-medium">+¥{{ row.amount }}</span>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="payment_method" label="支付方式" width="100">
              <template #default="{ row }">
                <ElTag size="small">{{ getPaymentMethodText(row.payment_method) }}</ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="status" label="状态" width="80">
              <template #default="{ row }">
                <ElTag :type="getStatusType(row.status)" size="small">
                  {{ getStatusText(row.status) }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="create_time" label="时间" width="160">
              <template #default="{ row }">
                {{ formatDateTime(row.create_time) }}
              </template>
            </ElTableColumn>
          </ElTable>
        </div>
      </div>
    </ElCard>
  </div>
</template>

<style scoped>
.el-radio {
  @apply border border-gray-200 rounded-lg p-12px mb-8px;
}

.el-radio.is-checked {
  @apply border-primary bg-primary-50;
}
</style>
