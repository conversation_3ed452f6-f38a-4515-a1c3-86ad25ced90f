<script setup lang="ts">
import { computed, h, onMounted, reactive, ref, watch } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus';
import {
  fetchCreateSystemUser,
  fetchDeleteSystemUser,
  fetchGetUserList,
  fetchGetUserStats,
  fetchUpdateSystemUser
} from '@/service/api/system-manage';
import { request } from '@/service/request';
import { useIsMobile } from '@/composables/useIsMobile';
// RoleSelector组件已删除 - 改为单角色模式
import { useTableOperate } from '@/hooks/common/table-pagination';
import BatchOperations from '@/components/common/BatchOperations.vue';
import UserDetailDialog from '@/components/user/UserDetailDialog.vue';
import AdvancedUserFilter from '@/components/user/AdvancedUserFilter.vue';
import UserBalanceManager from '@/components/user/UserBalanceManager.vue';
import SimplePermissionManager from '@/components/permission/SimplePermissionManager.vue';

import ResponsiveDialog from '@/components/responsive/ResponsiveDialog.vue';
import FilterDrawer from '@/components/responsive/FilterDrawer.vue';

import UserPermissionDrawer from '@/components/user/UserPermissionDrawer.vue';
import UserFunctionLimiter from '@/components/user/UserFunctionLimiter.vue';

const emit = defineEmits<{

}>();

const props = defineProps<{
  presetRoleCode?: string;
}>();
const { isMobile } = useIsMobile();

defineOptions({
  name: 'UserManagement'
});

// 使用表格操作Hook
const { pagination, loading, tableData, handlePageChange, handleSizeChange, fetchTableData, refreshTable } =
  useTableOperate({
    fetchData: async params => {
      const response = await fetchGetUserList({
        ...params,
        ...searchParams
      });

      const users = (response as any).list || (response as any).data || [];
      // 性能优化：避免逐行 N+1 获取角色信息，改为直接使用服务端返回的 user_role 等字段。
      return {
        data: users,
        total: (response as any).pagination?.total || (response as any).total || 0
      };
    }
  });

// 响应式数据
const modalVisible = ref(false);
const batchRateModalVisible = ref(false);
// 角色分配模态框已删除 - 改为单角色模式
const userPermissionModalVisible = ref(false);
const isEdit = ref(false);
const saving = ref(false);
const batchRateSaving = ref(false);
// 角色分配保存状态已删除 - 改为单角色模式
const selectedUsers = ref<any[]>([]);

// 高级搜索相关
const showAdvancedSearch = ref(false);
const advancedSearchCollapse = ref(['advanced']);
const createTimeRange = ref<string[]>([]);
const lastLoginRange = ref<string[]>([]);
const searchTemplates = ref<any[]>([]);
const searchTemplateModalVisible = ref(false);
const searchTemplateForm = reactive({
  name: '',
  description: ''
});

// 计算属性
const isFormValid = computed(() => {
  // 基本字段检查
  if (!userForm.username.trim()) return false;
  if (!userForm.email.trim()) return false;
  if (!isEdit.value && !userForm.password.trim()) return false;

  // 验证状态检查
  if (usernameValidation.status === 'error') return false;
  if (emailValidation.status === 'error') return false;

  // 费率范围检查
  if (userForm.userRate < 0.1 || userForm.userRate > 1) return false;

  return true;
});
// 可用角色列表已删除 - 改为单角色模式
const userPermissions = ref<string[]>([]);
const currentUserId = ref<number | null>(null);
const userDetailVisible = ref(false);
const currentDetailUserId = ref<number>(0);
const advancedFilterVisible = ref(false);
const filteredUserIds = ref<number[]>([]);

// 当前用户信息和权限
const currentUser = ref<any>(null);
const currentUserRoles = ref<string[]>([]);

// 用户状态管理
const statusChanging = ref(false);
const balanceManaging = ref(false);
const balanceDialogVisible = ref(false);
const currentBalanceUserId = ref<number>(0);
const balanceOperation = ref<'recharge' | 'deduct'>('recharge');
const balanceAmount = ref<number>(0);
const balanceRemark = ref<string>('');

// 用户资金管理
// 用户权限预览抽屉
const permissionDrawerVisible = ref(false);
const permissionDrawerUserId = ref<number | null>(null);

const balanceManagerVisible = ref(false);
const currentBalanceUserInfo = ref<any>(null);

// 简单权限管理
const simplePermissionManagerVisible = ref(false);
const currentPermissionUserId = ref<number>(0);
const currentPermissionUserInfo = ref<any>(null);
// 筛选抽屉
const filterDrawerVisible = ref(false);


// 用户功能限制
const functionLimiterVisible = ref(false);
const currentLimiterUserId = ref<number>(0);
const currentLimiterUserInfo = ref<any>(null);

// 批量操作配置
const batchOperations = ref([
  {
    key: 'advanced-filter',
    label: '高级筛选',
    icon: 'ic:round-filter-list',
    type: 'primary' as const
  },
  {
    key: 'batch-recharge',
    label: '批量充值',
    icon: 'ic:round-account-balance-wallet',
    type: 'success' as const,
    confirmMessage: '确定要为选中的 {count} 个用户进行批量充值吗？'
  },
  {
    key: 'batch-deduct',
    label: '批量扣费',
    icon: 'ic:round-money-off',
    type: 'warning' as const,
    confirmMessage: '确定要为选中的 {count} 个用户进行批量扣费吗？'
  },
  {
    key: 'enable-users',
    label: '启用用户',
    icon: 'ic:round-check-circle',
    type: 'success' as const,
    confirmMessage: '确定要启用选中的 {count} 个用户吗？'
  },
  {
    key: 'disable-users',
    label: '批量禁用',
    icon: 'ic:round-block',
    type: 'warning' as const,
    confirmMessage: '确定要禁用选中的 {count} 个用户吗？'
  },
  {
    key: 'ban-users',
    label: '批量封禁',
    icon: 'ic:round-gavel',
    type: 'danger' as const,
    confirmMessage: '确定要封禁选中的 {count} 个用户吗？'
  },
  {
    key: 'delete-users',
    label: '删除用户',
    icon: 'ic:round-delete',
    type: 'danger' as const,
    confirmMessage: '确定要删除选中的 {count} 个用户吗？此操作不可恢复！'
  }
]);

// 表单引用
const formRef = ref<FormInstance | null>(null);
const batchRateFormRef = ref<FormInstance | null>(null);

// 搜索参数
const searchParams = reactive({
  keyword: '',
  userRole: undefined as string | undefined,
  status: undefined as number | undefined,
  sortField: undefined as string | undefined,
  sortOrder: undefined as string | undefined,
  // 新增高级搜索参数
  email: '',
  balanceMin: undefined as number | undefined,
  balanceMax: undefined as number | undefined,
  rateMin: undefined as number | undefined,
  rateMax: undefined as number | undefined,
  createTimeStart: '',
  createTimeEnd: '',
  lastLoginStart: '',
  lastLoginEnd: '',
  hasEmail: undefined as boolean | undefined,
  hasInvitees: undefined as boolean | undefined
});

// 用户表单
const userForm = reactive({
  userId: null as number | null,
  username: '',
  email: '',
  password: '',
  userRate: 0.8,
  userRole: 'user',
  balance: 0,
  status: 1
});

// 表单验证状态
const usernameValidation = reactive({
  status: '', // 'success' | 'error' | ''
  message: ''
});

const emailValidation = reactive({
  status: '',
  message: ''
});

// 监听 IAM 预设角色并触发刷新
watch(
  () => props.presetRoleCode,
  val => {
    searchParams.userRole = val || undefined;
    fetchTableData();
  },
  { immediate: true }
);

// 性能优化：搜索条件变动 300ms 防抖触发表格刷新
let __searchTimer: any = null;
watch(
  () => ({ ...searchParams }),
  () => {
    if (__searchTimer) clearTimeout(__searchTimer);
    __searchTimer = setTimeout(() => {
      fetchTableData();
    }, 300);
  },
  { deep: true }
);

const passwordStrength = reactive({
  level: 0, // 0-4
  text: '',
  tips: ''
});

// 批量调费率表单
const batchRateForm = reactive({
  newRate: 0.8
});

// 角色分配表单已删除 - 改为单角色模式

// 选项数据
const roleOptions = [
  { label: 'VIP用户', value: 'vip' },
  { label: '代理商', value: 'agent' },
  { label: '会员', value: 'member' },
  { label: '普通用户', value: 'user' }
];

const statusOptions = [
  { label: '启用', value: 1 },
  { label: '禁用', value: 0 }
];

// 动态表单验证规则
const formRules = computed<FormRules>(() => ({
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { pattern: /^\d{5,11}$/, message: '用户名必须是5-11位QQ号', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  password: isEdit.value
    ? [{ min: 6, message: '密码长度不能少于6位', trigger: 'blur' }]
    : [
        { required: true, message: '请输入密码', trigger: 'blur' },
        { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
      ],
  userRate: [{ required: true, message: '请输入费率', trigger: 'blur' }],
  userRole: [{ required: true, message: '请选择角色', trigger: 'change' }]
}));

const batchRateRules: FormRules = {
  newRate: [{ required: true, message: '请输入新费率', trigger: 'blur' }]
};

// 工具函数
function getRoleType(role: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' {
  const typeMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    vip: 'danger',
    agent: 'warning',
    member: 'success',
    user: 'info'
  };
  return typeMap[role] || 'info';
}

function getRoleText(role: string): string {
  const textMap: Record<string, string> = {
    vip: 'VIP用户',
    agent: '代理商',
    member: '会员',
    user: '普通用户'
  };
  return textMap[role] || '普通用户';
}

// 新增工具函数
function getUserRateType(rate: number): 'success' | 'warning' | 'info' | 'primary' | 'danger' {
  if (rate <= 0.2) return 'danger'; // VIP
  if (rate <= 0.5) return 'warning'; // 代理商
  if (rate <= 0.8) return 'success'; // 会员
  return 'info'; // 普通用户
}

function getRoleTagType(roleCode: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' {
  const typeMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    super_admin: 'danger',
    admin: 'warning',
    manager: 'primary',
    user: 'info'
  };
  return typeMap[roleCode] || 'primary';
}

function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount || 0);
}

function formatDateTime(dateTime: string | Date): string {
  if (!dateTime) return '-';
  const date = new Date(dateTime);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
}

function getRelativeTime(dateTime: string | Date): string {
  if (!dateTime) return '';
  const now = new Date();
  const date = new Date(dateTime);
  const diff = now.getTime() - date.getTime();

  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (days > 0) return `${days}天前`;
  if (hours > 0) return `${hours}小时前`;
  if (minutes > 0) return `${minutes}分钟前`;
  return '刚刚';
}

function getOnlineStatus(lastLoginTime: string | Date): string {
  if (!lastLoginTime) return 'bg-gray-400';

  const now = new Date();
  const lastLogin = new Date(lastLoginTime);
  const diff = now.getTime() - lastLogin.getTime();
  const minutes = Math.floor(diff / (1000 * 60));

  if (minutes <= 5) return 'bg-green-500'; // 在线
  if (minutes <= 30) return 'bg-yellow-500'; // 最近活跃
  return 'bg-gray-400'; // 离线
}

function getOnlineStatusText(lastLoginTime: string | Date): string {
  if (!lastLoginTime) return '从未登录';

  const now = new Date();
  const lastLogin = new Date(lastLoginTime);
  const diff = now.getTime() - lastLogin.getTime();
  const minutes = Math.floor(diff / (1000 * 60));

  if (minutes <= 5) return '在线';
  if (minutes <= 30) return '最近活跃';
  return `最后登录: ${getRelativeTime(lastLoginTime)}`;
}

// 表单验证函数
function validateUsername() {
  const username = userForm.username.trim();

  if (!username) {
    usernameValidation.status = '';
    usernameValidation.message = '';
    return;
  }

  // QQ号验证：5-11位数字
  const qqRegex = /^\d{5,11}$/;
  if (!qqRegex.test(username)) {
    usernameValidation.status = 'error';
    usernameValidation.message = '请输入5-11位数字的QQ号';
    return;
  }

  usernameValidation.status = 'success';
  usernameValidation.message = 'QQ号格式正确';
}

function validateEmail() {
  const email = userForm.email.trim();

  if (!email) {
    emailValidation.status = '';
    emailValidation.message = '';
    return;
  }

  // 邮箱验证
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    emailValidation.status = 'error';
    emailValidation.message = '请输入有效的邮箱地址';
    return;
  }

  emailValidation.status = 'success';
  emailValidation.message = '邮箱格式正确';
}

function validatePassword() {
  const password = userForm.password;

  if (!password || isEdit.value) {
    passwordStrength.level = 0;
    passwordStrength.text = '';
    passwordStrength.tips = '';
    return;
  }

  let score = 0;
  const tips = [];

  // 长度检查
  if (password.length >= 8) {
    score += 1;
  } else {
    tips.push('至少8位字符');
  }

  // 包含数字
  if (/\d/.test(password)) {
    score += 1;
  } else {
    tips.push('包含数字');
  }

  // 包含小写字母
  if (/[a-z]/.test(password)) {
    score += 1;
  } else {
    tips.push('包含小写字母');
  }

  // 包含大写字母或特殊字符
  if (/[A-Z]/.test(password) || /[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    score += 1;
  } else {
    tips.push('包含大写字母或特殊字符');
  }

  passwordStrength.level = score;

  const strengthTexts = ['很弱', '弱', '中等', '强', '很强'];
  passwordStrength.text = strengthTexts[score] || '很弱';

  if (tips.length > 0) {
    passwordStrength.tips = `建议: ${tips.join('、')}`;
  } else {
    passwordStrength.tips = '密码强度良好';
  }
}

// 工具函数
function formatRateTooltip(value: number): string {
  return `${(value * 100).toFixed(1)}%`;
}

function getRateDescription(rate: number): string {
  if (rate <= 0.2) return 'VIP用户，享受最低价格';
  if (rate <= 0.5) return '代理商，享受代理价格';
  if (rate <= 0.8) return '会员，享受会员价格';
  return '普通用户，按原价购买';
}

function getRoleDescription(role: string): string {
  const descriptions: Record<string, string> = {
    vip: '最高等级用户，享受所有特权',
    agent: '代理商用户，可管理下级用户',
    member: '会员用户，享受会员特权',
    user: '普通用户，基础功能权限'
  };
  return descriptions[role] || '基础功能权限';
}

function getPasswordStrengthColor(level: number): string {
  const colors = ['bg-red-400', 'bg-orange-400', 'bg-yellow-400', 'bg-blue-400', 'bg-green-400'];
  return colors[level - 1] || 'bg-gray-200';
}

function getPasswordStrengthTextColor(level: number): string {
  const colors = ['text-red-500', 'text-orange-500', 'text-yellow-500', 'text-blue-500', 'text-green-500'];
  return colors[level - 1] || 'text-gray-500';
}

function onRoleChange(role: string) {
  // 根据角色自动调整费率
  const rateMap: Record<string, number> = {
    vip: 0.15,
    agent: 0.35,
    member: 0.65,
    user: 0.85
  };

  if (rateMap[role]) {
    userForm.userRate = rateMap[role];
  }
}

// 重置验证状态
function resetValidationStatus() {
  usernameValidation.status = '';
  usernameValidation.message = '';
  emailValidation.status = '';
  emailValidation.message = '';
  passwordStrength.level = 0;
  passwordStrength.text = '';
  passwordStrength.tips = '';
}

// 处理函数
function handleSearch() {
  fetchTableData();
}

function handleReset() {
  Object.assign(searchParams, {
    keyword: '',
    userRole: undefined,
    status: undefined,
    sortField: undefined,
    sortOrder: undefined,
    // 重置高级搜索条件
    email: '',
    balanceMin: undefined,
    balanceMax: undefined,
    rateMin: undefined,
    rateMax: undefined,
    createTimeStart: '',
    createTimeEnd: '',
    lastLoginStart: '',
    lastLoginEnd: '',
    hasEmail: undefined,
    hasInvitees: undefined
  });

  // 重置时间选择器
  createTimeRange.value = [];
  lastLoginRange.value = [];

  fetchTableData();
}

// 新增快速筛选函数
function handleQuickFilter(type: string) {
  switch (type) {
    case 'all':
      searchParams.status = undefined;
      break;
    case 'active':
      searchParams.status = 1;
      break;
    case 'inactive':
      searchParams.status = 0;
      break;
  }
  fetchTableData();
}

function handleRoleFilter(role: string) {
  searchParams.userRole = role || undefined;
  fetchTableData();
}

// 权限检查函数
function canEditPassword(targetUserId: number): boolean {
  // 如果没有当前用户信息，不允许修改
  if (!currentUser.value) return false;

  // 用户可以修改自己的密码
  if (currentUser.value.userId === targetUserId) return true;

  // 超级管理员可以修改任何人的密码
  if (currentUserRoles.value.includes('super_admin')) return true;

  return false;
}

function isSuperAdmin(): boolean {
  return currentUserRoles.value.includes('super_admin');
}

// 获取当前用户信息
async function fetchCurrentUser() {
  try {
    const response = await request({
      url: '/api/auth/getUserInfo',
      method: 'get'
    });

    currentUser.value = response;

    // 获取当前用户角色
    if (response?.userId) {
      // 使用统一 claims（RBAC），不再依赖 user_role 旧字段
      const claims = await request<{ roles: string[] }>({ url: '/api/auth/claims', method: 'get' });
      currentUserRoles.value = claims?.roles || [];
    }
  } catch (error) {
    console.error('获取当前用户信息失败:', error);
  }
}

// 高级搜索相关函数
function toggleAdvancedSearch() {
  showAdvancedSearch.value = !showAdvancedSearch.value;
  if (showAdvancedSearch.value) {
    advancedSearchCollapse.value = ['advanced'];
  }
}

function handleCreateTimeChange(value: string[] | null) {
  if (value && value.length === 2) {
    searchParams.createTimeStart = value[0];
    searchParams.createTimeEnd = value[1];
  } else {
    searchParams.createTimeStart = '';
    searchParams.createTimeEnd = '';
  }
}

function handleLastLoginChange(value: string[] | null) {
  if (value && value.length === 2) {
    searchParams.lastLoginStart = value[0];
    searchParams.lastLoginEnd = value[1];
  } else {
    searchParams.lastLoginStart = '';
    searchParams.lastLoginEnd = '';
  }
}

function handleAdvancedSearch() {
  // 验证搜索条件
  if (searchParams.balanceMin !== undefined && searchParams.balanceMax !== undefined) {
    if (searchParams.balanceMin > searchParams.balanceMax) {
      ElMessage.warning('余额最小值不能大于最大值');
      return;
    }
  }

  if (searchParams.rateMin !== undefined && searchParams.rateMax !== undefined) {
    if (searchParams.rateMin > searchParams.rateMax) {
      ElMessage.warning('费率最小值不能大于最大值');
      return;
    }
  }

  // 执行搜索
  fetchTableData();
  ElMessage.success('高级搜索执行完成');
}

function handleResetAdvanced() {
  // 重置高级搜索条件
  searchParams.email = '';
  searchParams.balanceMin = undefined;
  searchParams.balanceMax = undefined;
  searchParams.rateMin = undefined;
  searchParams.rateMax = undefined;
  searchParams.createTimeStart = '';
  searchParams.createTimeEnd = '';
  searchParams.lastLoginStart = '';
  searchParams.lastLoginEnd = '';
  searchParams.hasEmail = undefined;
  searchParams.hasInvitees = undefined;

  // 重置时间选择器
  createTimeRange.value = [];
  lastLoginRange.value = [];

  ElMessage.success('高级搜索条件已清空');
}

// 搜索模板管理
function handleSaveSearchTemplate() {
  // 检查是否有搜索条件
  const hasConditions = Object.entries(searchParams).some(([key, value]) => {
    if (key === 'sortField' || key === 'sortOrder') return false;
    return value !== undefined && value !== '' && value !== null;
  });

  if (!hasConditions) {
    ElMessage.warning('请先设置搜索条件');
    return;
  }

  searchTemplateForm.name = '';
  searchTemplateForm.description = '';
  searchTemplateModalVisible.value = true;
}

async function saveSearchTemplate() {
  if (!searchTemplateForm.name.trim()) {
    ElMessage.warning('请输入模板名称');
    return;
  }

  const template = {
    id: Date.now(),
    name: searchTemplateForm.name,
    description: searchTemplateForm.description,
    conditions: { ...searchParams },
    createTime: new Date().toISOString()
  };

  try {
    // 保存到本地存储
    const templates = JSON.parse(localStorage.getItem('userSearchTemplates') || '[]');
    templates.push(template);
    localStorage.setItem('userSearchTemplates', JSON.stringify(templates));

    searchTemplates.value = templates;
    searchTemplateModalVisible.value = false;

    ElMessage.success('搜索模板保存成功');
  } catch (error) {
    ElMessage.error('保存搜索模板失败');
  }
}

function handleLoadSearchTemplate() {
  loadSearchTemplates();
  // 这里可以打开一个选择模板的对话框
  ElMessage.info('搜索模板加载功能');
}

function loadSearchTemplates() {
  try {
    const templates = JSON.parse(localStorage.getItem('userSearchTemplates') || '[]');
    searchTemplates.value = templates;
  } catch (error) {
    console.error('加载搜索模板失败:', error);
    searchTemplates.value = [];
  }
}

function applySearchTemplate(template: any) {
  Object.assign(searchParams, template.conditions);

  // 更新时间选择器
  if (searchParams.createTimeStart && searchParams.createTimeEnd) {
    createTimeRange.value = [searchParams.createTimeStart, searchParams.createTimeEnd];
  }
  if (searchParams.lastLoginStart && searchParams.lastLoginEnd) {
    lastLoginRange.value = [searchParams.lastLoginStart, searchParams.lastLoginEnd];
  }

  fetchTableData();
  ElMessage.success(`搜索模板"${template.name}"已应用`);
}

// 新增排序处理函数
function handleSortChange({ prop, order }: { prop: string; order: string | null }) {
  if (order) {
    searchParams.sortField = prop;
    searchParams.sortOrder = order === 'ascending' ? 'asc' : 'desc';
  } else {
    searchParams.sortField = undefined;
    searchParams.sortOrder = undefined;
  }
  fetchTableData();
}

// 新增行操作处理函数
async function handleRowAction(command: string, row: any) {
  switch (command) {
    case 'toggle-status':
      await handleToggleUserStatus(row);
      break;
    case 'reset-password':
      await handleResetPassword(row);
      break;
    case 'view-permissions':
      permissionDrawerUserId.value = row.userId;
      permissionDrawerVisible.value = true;
      break;
    case 'view-logs':
      handleViewUserLogs(row);
      break;
    case 'delete':
      await handleDelete(row.userId);
      break;
    case 'recharge':
      openBalanceDialog(row.userId, 'recharge');
      break;
    case 'deduct':
      openBalanceDialog(row.userId, 'deduct');
      break;
    case 'ban-user':
      await handleBanUser(row);
      break;
    case 'balance-manage':
      openBalanceManager(row);
      break;
    case 'function-limit':
      openFunctionLimiter(row);
      break;
    default:
      ElMessage.warning('未知操作');
  }
}

// 切换用户状态
async function handleToggleUserStatus(row: any) {
  const newStatus = row.status === 1 ? 0 : 1;
  const action = newStatus === 1 ? '启用' : '禁用';
  const actionColor = newStatus === 1 ? 'success' : 'warning';

  try {
    // 显示详细的状态变更确认对话框
    const confirmResult = await ElMessageBox.confirm(
      h('div', { class: 'space-y-12px' }, [
        h('div', { class: 'flex items-center gap-8px' }, [
          h('span', { class: 'font-medium' }, `👤 用户: ${row.username}`)
        ]),
        h('div', { class: 'flex items-center gap-8px' }, [
          h('span', { class: 'text-sm text-gray-600' }, `📧 ${row.email || '未设置邮箱'}`)
        ]),
        h('div', { class: 'mt-12px p-12px bg-gray-50 rounded' }, [
          h('div', { class: 'text-sm' }, [
            h('div', { class: 'mb-4px' }, `当前状态: `),
            h(
              'span',
              {
                class: `inline-flex items-center gap-4px px-8px py-2px rounded text-xs ${
                  row.status === 1 ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                }`
              },
              [row.status === 1 ? '✅ 启用' : '❌ 禁用']
            )
          ]),
          h('div', { class: 'text-sm mt-8px' }, [
            h('div', { class: 'mb-4px' }, `变更为: `),
            h(
              'span',
              {
                class: `inline-flex items-center gap-4px px-8px py-2px rounded text-xs ${
                  newStatus === 1 ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                }`
              },
              [newStatus === 1 ? `✅ ${action}` : `❌ ${action}`]
            )
          ])
        ]),
        h(
          'div',
          { class: 'text-xs text-gray-500 mt-8px' },
          newStatus === 1 ? '启用后用户可以正常登录和使用系统功能' : '禁用后用户将无法登录系统，但数据会保留'
        )
      ]),
      `确认${action}用户`,
      {
        type: actionColor,
        confirmButtonText: `确认${action}`,
        cancelButtonText: '取消',
        confirmButtonClass: newStatus === 1 ? 'el-button--success' : 'el-button--warning'
      }
    );

    // 执行状态变更
    const loading = ElLoading.service({
      lock: true,
      text: `正在${action}用户...`,
      background: 'rgba(0, 0, 0, 0.7)'
    });

    try {
      await fetchUpdateSystemUser({
        userId: row.userId,
        status: newStatus
      });

      // 记录状态变更日志
      await recordUserStatusLog({
        userId: row.userId,
        username: row.username,
        oldStatus: row.status,
        newStatus,
        action,
        operator: '当前管理员', // TODO: 获取当前登录用户
        reason: `管理员手动${action}用户`
      });

      ElMessage.success({
        message: `${action}成功`,
        type: 'success',
        duration: 3000
      });

      refreshTable();
    } finally {
      loading.close();
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error(`${action}失败:`, error);
      ElMessage.error({
        message: error.response?.data?.msg || `${action}失败`,
        duration: 5000
      });
    }
  }
}

// 重置密码
async function handleResetPassword(row: any) {
  try {
    // 检查权限
    if (!canEditPassword(row.userId)) {
      ElMessage.warning('您没有权限重置此用户的密码');
      return;
    }

    await ElMessageBox.confirm(`确定要重置用户 "${row.username}" 的密码吗？新密码将发送到用户邮箱。`, '确认重置密码', {
      type: 'warning'
    });

    // TODO: 实现重置密码API
    ElMessage.warning('重置密码功能暂未实现');
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('重置密码失败:', error);
      ElMessage.error('重置密码失败');
    }
  }
}

// 查看用户日志
function handleViewUserLogs(row: any) {
  // TODO: 实现用户日志查看功能
  ElMessage.info(`查看用户 ${row.username} 的操作日志`);
}

// 打开余额管理对话框
function openBalanceDialog(userId: number, operation: 'recharge' | 'deduct') {
  currentBalanceUserId.value = userId;
  balanceOperation.value = operation;
  balanceAmount.value = 0;
  balanceRemark.value = '';
  balanceDialogVisible.value = true;
}

// 处理余额操作
async function handleBalanceOperation() {
  if (!currentBalanceUserId.value || balanceAmount.value <= 0) {
    ElMessage.error('请输入有效的金额');
    return;
  }

  const operation = balanceOperation.value;
  const operationText = operation === 'recharge' ? '充值' : '扣费';

  try {
    await ElMessageBox.confirm(`确定要为用户${operationText} ¥${balanceAmount.value} 吗？`, `确认${operationText}`, {
      type: 'warning',
      confirmButtonText: `确认${operationText}`,
      cancelButtonText: '取消'
    });

    balanceManaging.value = true;

    await request({
      url: `/api/user/${operation}`,
      method: 'post',
      data: {
        user_id: currentBalanceUserId.value,
        amount: balanceAmount.value,
        remark: balanceRemark.value || `管理员${operationText}`
      }
    });

    ElMessage.success(`${operationText}成功`);
    balanceDialogVisible.value = false;
    await fetchTableData();
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${operationText}失败:`, error);
      ElMessage.error(`${operationText}失败`);
    }
  } finally {
    balanceManaging.value = false;
  }
}

// 封禁用户
async function handleBanUser(row: any) {
  try {
    const { value: reason } = await ElMessageBox.prompt(
      `确定要封禁用户 ${row.username} 吗？请输入封禁原因：`,
      '封禁用户',
      {
        confirmButtonText: '确认封禁',
        cancelButtonText: '取消',
        inputPlaceholder: '请输入封禁原因',
        inputValidator: value => {
          if (!value || value.trim().length === 0) {
            return '请输入封禁原因';
          }
          return true;
        }
      }
    );

    statusChanging.value = true;

    await request({
      url: '/api/user/ban',
      method: 'post',
      data: {
        user_id: row.userId,
        reason: reason.trim()
      }
    });

    ElMessage.success(`用户 ${row.username} 已被封禁`);
    await fetchTableData();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('封禁用户失败:', error);
      ElMessage.error('封禁用户失败');
    }
  } finally {
    statusChanging.value = false;
  }
}

// 打开用户资金管理器
function openBalanceManager(row: any) {
  currentBalanceUserId.value = row.userId;
  currentBalanceUserInfo.value = row;
  balanceManagerVisible.value = true;
}

// 处理资金管理成功
function handleBalanceManagerSuccess() {
  ElMessage.success('资金操作成功');
  fetchTableData();
}

// 打开简单权限管理器
function openSimplePermissionManager(userId: number, userInfo: any) {
  currentPermissionUserId.value = userId;
  currentPermissionUserInfo.value = userInfo;
  simplePermissionManagerVisible.value = true;
}

// 处理权限管理成功
function handlePermissionManagerSuccess() {
  ElMessage.success('权限设置保存成功');
  fetchTableData();
}

// 打开用户功能限制器
function openFunctionLimiter(row: any) {
  currentLimiterUserId.value = row.userId;
  currentLimiterUserInfo.value = row;
  functionLimiterVisible.value = true;
}

// 处理功能限制成功
function handleFunctionLimiterSuccess() {
  ElMessage.success('功能限制设置保存成功');
  fetchTableData();
}

// 批量状态管理
async function handleBatchStatusChange(targetStatus: number) {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请先选择要操作的用户');
    return;
  }

  const action = targetStatus === 1 ? '启用' : '禁用';
  const actionColor = targetStatus === 1 ? 'success' : 'warning';

  // 统计当前状态
  const currentActiveCount = selectedUsers.value.filter(user => user.status === 1).length;
  const currentInactiveCount = selectedUsers.value.filter(user => user.status === 0).length;
  const willChangeCount = selectedUsers.value.filter(user => user.status !== targetStatus).length;

  if (willChangeCount === 0) {
    ElMessage.info(`所选用户已经是${action}状态`);
    return;
  }

  try {
    // 显示批量状态变更确认对话框
    await ElMessageBox.confirm(
      h('div', { class: 'space-y-16px' }, [
        h('div', { class: 'text-center' }, [
          h('div', { class: 'text-lg font-medium mb-8px' }, `批量${action}用户`),
          h('div', { class: 'text-sm text-gray-600' }, `将对 ${willChangeCount} 个用户执行${action}操作`)
        ]),

        h('div', { class: 'bg-gray-50 p-12px rounded' }, [
          h('div', { class: 'text-sm font-medium mb-8px' }, '操作统计:'),
          h('div', { class: 'grid grid-cols-2 gap-8px text-xs' }, [
            h('div', { class: 'flex justify-between' }, [
              h('span', '总选择:'),
              h('span', { class: 'font-medium' }, selectedUsers.value.length)
            ]),
            h('div', { class: 'flex justify-between' }, [
              h('span', '当前启用:'),
              h('span', { class: 'font-medium text-green-600' }, currentActiveCount)
            ]),
            h('div', { class: 'flex justify-between' }, [
              h('span', '当前禁用:'),
              h('span', { class: 'font-medium text-red-600' }, currentInactiveCount)
            ]),
            h('div', { class: 'flex justify-between' }, [
              h('span', `将${action}:`),
              h(
                'span',
                { class: `font-medium ${targetStatus === 1 ? 'text-green-600' : 'text-red-600'}` },
                willChangeCount
              )
            ])
          ])
        ]),

        h('div', { class: 'bg-blue-50 p-12px rounded' }, [
          h('div', { class: 'text-sm font-medium mb-4px text-blue-700' }, '影响说明:'),
          h(
            'div',
            { class: 'text-xs text-blue-600' },
            targetStatus === 1 ? '启用后用户可以正常登录和使用系统功能' : '禁用后用户将无法登录系统，但数据会保留'
          )
        ]),

        h('div', { class: 'max-h-120px overflow-y-auto' }, [
          h('div', { class: 'text-sm font-medium mb-8px' }, '受影响用户:'),
          h(
            'div',
            { class: 'space-y-4px' },
            selectedUsers.value
              .filter(user => user.status !== targetStatus)
              .map(user =>
                h('div', { class: 'flex items-center justify-between text-xs p-4px bg-white rounded' }, [
                  h('span', user.username),
                  h('span', { class: 'text-gray-500' }, user.email || '无邮箱')
                ])
              )
          )
        ])
      ]),
      `确认批量${action}`,
      {
        type: actionColor,
        confirmButtonText: `确认${action} ${willChangeCount} 个用户`,
        cancelButtonText: '取消',
        confirmButtonClass: targetStatus === 1 ? 'el-button--success' : 'el-button--warning'
      }
    );

    // 执行批量状态变更
    const loading = ElLoading.service({
      lock: true,
      text: `正在批量${action}用户...`,
      background: 'rgba(0, 0, 0, 0.7)'
    });

    try {
      const userIds = selectedUsers.value.filter(user => user.status !== targetStatus).map(user => user.userId);

      // 批量更新用户状态
      await request({
        url: '/api/user/batch-status',
        method: 'post',
        data: {
          userIds,
          status: targetStatus,
          remark: `管理员批量${action}用户`
        }
      });

      // 记录批量状态变更日志
      await recordBatchStatusLog({
        userIds,
        usernames: selectedUsers.value.filter(user => user.status !== targetStatus).map(user => user.username),
        targetStatus,
        action,
        operator: '当前管理员', // TODO: 获取当前登录用户
        reason: `管理员批量${action}用户`,
        affectedCount: willChangeCount
      });

      ElMessage.success({
        message: `批量${action}成功，共处理 ${willChangeCount} 个用户`,
        type: 'success',
        duration: 3000
      });

      // 清空选择并刷新表格
      selectedUsers.value = [];
      refreshTable();
    } finally {
      loading.close();
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error(`批量${action}失败:`, error);
      ElMessage.error({
        message: error.response?.data?.msg || `批量${action}失败`,
        duration: 5000
      });
    }
  }
}

// 状态变更日志记录
async function recordUserStatusLog(logData: any) {
  try {
    await request({
      url: '/api/log/user-status',
      method: 'post',
      data: {
        ...logData,
        timestamp: new Date().toISOString(),
        ip: await getUserIP()
      }
    });
  } catch (error) {
    console.error('记录用户状态日志失败:', error);
  }
}

// 批量状态变更日志记录
async function recordBatchStatusLog(logData: any) {
  try {
    await request({
      url: '/api/log/batch-user-status',
      method: 'post',
      data: {
        ...logData,
        timestamp: new Date().toISOString(),
        ip: await getUserIP()
      }
    });
  } catch (error) {
    console.error('记录批量状态日志失败:', error);
  }
}

// 获取用户IP（简单实现）
async function getUserIP(): Promise<string> {
  try {
    // 这里可以调用获取IP的API
    return 'unknown';
  } catch {
    return 'unknown';
  }
}

function handleSelectionChange(selection: any[]) {
  selectedUsers.value = selection;
}

function handleCreate() {
  isEdit.value = false;
  Object.assign(userForm, {
    userId: null,
    username: '',
    email: '',
    password: '',
    userRate: 0.8,
    userRole: 'user',
    balance: 0,
    status: 1
  });

  // 重置验证状态
  resetValidationStatus();

  modalVisible.value = true;
}

function handleEdit(row: any) {
  isEdit.value = true;

  // 调试信息：打印原始数据
  console.log('编辑用户原始数据:', row);

  Object.assign(userForm, {
    userId: row.userId,
    username: row.username,
    email: row.email || '',
    password: '',
    userRate: Number.parseFloat(row.userRate || row.user_rate || '0.8'),
    userRole: row.userRole || row.user_role || 'user',
    balance: Number.parseFloat(row.balance || '0'),
    status: row.status || 1
  });

  // 调试信息：打印表单数据
  console.log('表单数据:', userForm);

  // 重置验证状态
  resetValidationStatus();

  modalVisible.value = true;
}

async function handleDelete(userId: number) {
  try {
    await ElMessageBox.confirm('确定要删除这个用户吗？', '确认删除', {
      type: 'warning'
    });

    await fetchDeleteSystemUser(userId);
    ElMessage.success('删除成功');
    refreshTable();
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
      // 优先显示后端返回的错误信息
      const errorMessage = error.response?.data?.msg || error.message || '删除失败';
      ElMessage.error(errorMessage);
    }
  }
}

function handleBatchRate() {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请先选择要调整的用户');
    return;
  }
  batchRateForm.newRate = 0.8;
  batchRateModalVisible.value = true;
}

// 保存用户
async function handleSave() {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    saving.value = true;

    if (isEdit.value) {
      // 更新用户
      const updateData: any = {
        email: userForm.email,
        userRate: userForm.userRate,
        userRole: userForm.userRole,
        balance: userForm.balance,
        status: userForm.status
      };

      // 只有有权限的用户才能修改密码
      if (userForm.password && canEditPassword(userForm.userId!)) {
        updateData.password = userForm.password;
      }

      await fetchUpdateSystemUser({
        userId: userForm.userId!,
        ...updateData
      });
      ElMessage.success('用户更新成功');
    } else {
      // 创建用户
      await fetchCreateSystemUser({
        username: userForm.username,
        nickname: userForm.username, // 使用用户名作为昵称
        email: userForm.email,
        password: userForm.password,
        role: Number.parseInt(userForm.userRole), // 转换为数字
        userRole: userForm.userRole, // 保留原始字符串
        balance: userForm.balance,
        status: userForm.status
      });
      ElMessage.success('用户创建成功');
    }

    modalVisible.value = false;
    refreshTable();
  } catch (error: any) {
    console.error('保存失败:', error);
    // 优先显示后端返回的错误信息
    const errorMessage = error.response?.data?.msg || error.message || '保存失败';
    ElMessage.error(errorMessage);
  } finally {
    saving.value = false;
  }
}

// 批量调费率
async function handleBatchRateSave() {
  if (!batchRateFormRef.value) return;

  try {
    await batchRateFormRef.value.validate();
    batchRateSaving.value = true;

    const userIds = selectedUsers.value.map((user: any) => user.userId);
    // 批量更新用户费率 - 暂时注释掉，需要实现对应的API
    // await fetchBatchUpdateUserRate({
    //   userIds,
    //   userRate: batchRateForm.newRate
    // });

    ElMessage.warning('批量更新费率功能暂未实现');
    batchRateModalVisible.value = false;
  } catch (error: any) {
    console.error('批量调费率失败:', error);
    ElMessage.error(error.message || '批量调费率失败');
  } finally {
    batchRateSaving.value = false;
  }
}

// 多角色管理功能已删除 - 改为单角色模式

// 获取用户简化角色信息（只返回主要角色）
async function fetchUserSimpleRole(userId: number) {
  try {
    const response = await request({
      url: `/api/user-role-optimization/user/${userId}/role`,
      method: 'get'
    });

    // 返回角色信息，如果有的话
    return response
      ? {
          role_id: response.role_id,
          role_name: response.role_name,
          role_code: response.user_role,
          role_description: response.role_description
        }
      : null;
  } catch (error) {
    console.error('获取用户简化角色失败:', error);
    return null;
  }
}

// 获取用户权限信息
async function fetchUserPermissions(userId: number) {
  try {
    const response = await request({
      url: `/api/user/permissions/${userId}`,
      method: 'get'
    });

    if (Array.isArray(response)) {
      // 提取权限名称
      userPermissions.value = response.map(permission => permission.permission_name);
    }
  } catch (error) {
    console.error('获取用户权限失败:', error);
    userPermissions.value = [];
  }
}

// 批量角色分配函数已删除 - 改为单角色模式

// 设置用户主要角色（简化版）
async function setUserPrimaryRole(userId: number, roleId: number) {
  try {
    const response = await request({
      url: '/api/user-role-optimization/set-primary-role',
      method: 'post',
      data: { userId, roleId }
    });

    return response;
  } catch (error) {
    console.error('设置用户角色失败:', error);
    throw error;
  }
}

// 角色分配功能已删除 - 改为单角色模式

function handleViewDetail(userId: number) {
  currentDetailUserId.value = userId;
  userDetailVisible.value = true;
}

async function handleViewUserPermissions(userId: number) {
  // 获取用户权限数据
  await fetchUserPermissions(userId);
  // 打开权限查看弹窗
  userPermissionModalVisible.value = true;
}

// 多角色分配功能已删除 - 改为单角色模式

// 批量操作处理
function handleBatchOperation(operation: string, items: any[]) {
  switch (operation) {
    case 'advanced-filter':
      handleAdvancedFilter();
      break;
    case 'batch-recharge':
      handleBatchRecharge();
      break;
    case 'batch-deduct':
      handleBatchDeduct();
      break;
    case 'enable-users':
      handleBatchStatusChange(1);
      break;
    case 'disable-users':
      handleBatchStatusChange(0);
      break;
    case 'ban-users':
      handleBatchBan();
      break;
    case 'delete-users':
      handleBatchDelete(items);
      break;
    default:
      ElMessage.warning('未知操作');
  }
}

// 打开高级筛选
function handleAdvancedFilter() {
  advancedFilterVisible.value = true;
}

// 批量充值
async function handleBatchRecharge() {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请选择要充值的用户');
    return;
  }

  try {
    const { value: amount } = await ElMessageBox.prompt(
      `为选中的 ${selectedUsers.value.length} 个用户充值`,
      '批量充值',
      {
        confirmButtonText: '确认充值',
        cancelButtonText: '取消',
        inputPattern: /^\d+(\.\d{1,2})?$/,
        inputErrorMessage: '请输入有效的金额'
      }
    );

    if (!amount || Number.parseFloat(amount) <= 0) {
      ElMessage.error('充值金额必须大于0');
      return;
    }

    balanceManaging.value = true;

    await request({
      url: '/api/user/batch-recharge',
      method: 'post',
      data: {
        user_ids: selectedUsers.value.map(u => u.userId),
        amount: Number.parseFloat(amount),
        remark: '批量充值'
      }
    });

    ElMessage.success(`成功为 ${selectedUsers.value.length} 个用户充值 ¥${amount}`);
    selectedUsers.value = [];
    await fetchTableData();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量充值失败:', error);
      ElMessage.error('批量充值失败');
    }
  } finally {
    balanceManaging.value = false;
  }
}

// 批量扣费
async function handleBatchDeduct() {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请选择要扣费的用户');
    return;
  }

  try {
    const { value: amount } = await ElMessageBox.prompt(
      `为选中的 ${selectedUsers.value.length} 个用户扣费`,
      '批量扣费',
      {
        confirmButtonText: '确认扣费',
        cancelButtonText: '取消',
        inputPattern: /^\d+(\.\d{1,2})?$/,
        inputErrorMessage: '请输入有效的金额'
      }
    );

    if (!amount || Number.parseFloat(amount) <= 0) {
      ElMessage.error('扣费金额必须大于0');
      return;
    }

    balanceManaging.value = true;

    await request({
      url: '/api/user/batch-deduct',
      method: 'post',
      data: {
        user_ids: selectedUsers.value.map(u => u.userId),
        amount: Number.parseFloat(amount),
        remark: '批量扣费'
      }
    });

    ElMessage.success(`成功为 ${selectedUsers.value.length} 个用户扣费 ¥${amount}`);
    selectedUsers.value = [];
    await fetchTableData();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量扣费失败:', error);
      ElMessage.error('批量扣费失败');
    }
  } finally {
    balanceManaging.value = false;
  }
}

// 批量封禁
async function handleBatchBan() {
  if (selectedUsers.value.length === 0) {
    ElMessage.warning('请选择要封禁的用户');
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要封禁选中的 ${selectedUsers.value.length} 个用户吗？封禁后用户将无法登录系统。`,
      '批量封禁确认',
      {
        type: 'warning',
        confirmButtonText: '确认封禁',
        cancelButtonText: '取消'
      }
    );

    statusChanging.value = true;

    await request({
      url: '/api/user/batch-ban',
      method: 'post',
      data: {
        user_ids: selectedUsers.value.map(u => u.userId),
        reason: '管理员批量封禁'
      }
    });

    ElMessage.success(`成功封禁 ${selectedUsers.value.length} 个用户`);
    selectedUsers.value = [];
    await fetchTableData();
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量封禁失败:', error);
      ElMessage.error('批量封禁失败');
    }
  } finally {
    statusChanging.value = false;
  }
}

// 应用筛选结果
function applyFilterResult(users: any[]) {
  filteredUserIds.value = users.map(user => user.userId);
  // 更新选中用户为筛选结果
  selectedUsers.value = users;
  ElMessage.success(`已筛选出 ${users.length} 个用户并自动选中`);
}

// 权限模板应用回调
function onTemplateApplied(template: any) {
  ElMessage.success(`权限模板"${template.template_name}"已应用`);
  // 这里可以根据需要进行后续处理
}

// 批量删除
async function handleBatchDelete(users: any[]) {
  try {
    const userIds = users.map(user => user.userId);
    // 这里应该调用批量删除的API
    // const response = await request({
    //   url: '/api/user/batch-delete',
    //   method: 'post',
    //   data: { user_ids: userIds }
    // });

    ElMessage.success('删除用户成功');
    fetchTableData(); // 刷新表格
    selectedUsers.value = []; // 清空选择
  } catch (error) {
    ElMessage.error('删除用户失败');
  }
}

// 生命周期
onMounted(() => {
  fetchTableData();
  // fetchRoles() 已删除 - 改为单角色模式
  loadSearchTemplates();
  fetchCurrentUser(); // 获取当前用户信息
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-medium">用户管理</span>
          <div class="flex gap-8px">
            <ElButton type="info" @click="handleBatchRate">批量调费率</ElButton>
            <!-- 角色分配按钮已删除 - 改为单角色模式 -->
            <ElButton type="primary" @click="handleCreate">新增用户</ElButton>
          </div>
        </div>
      </template>

      <!-- 搜索栏（移动端抽屉化） -->
      <div class="pb-16px space-y-12px">
        <div class="flex flex-wrap items-center gap-16px">
          <ElInput
            v-model="searchParams.keyword"
            placeholder="搜索用户名、邮箱、ID"
            clearable
            class="w-280px sm:w-240px"
            @keyup.enter="handleSearch"
          />

          <ElSelect v-model="searchParams.userRole" placeholder="用户角色" clearable class="w-140px hidden md:block">
            <ElOption v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item.value" />
          </ElSelect>

          <ElSelect v-model="searchParams.status" placeholder="状态" clearable class="w-120px hidden md:block">
            <ElOption v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </ElSelect>

          <ElButton type="primary" class="hidden md:inline-flex" @click="handleSearch">搜索</ElButton>
          <ElButton class="hidden md:inline-flex" @click="handleReset">重置</ElButton>

          <!-- 小屏用筛选抽屉 -->
          <ElButton class="md:hidden" @click="filterDrawerVisible = true">筛选</ElButton>
        </div>

        <!-- 高级搜索区域：PC显示；移动端改为抽屉 -->
        <ElCollapse v-show="showAdvancedSearch" v-model="advancedSearchCollapse">
          <ElCollapseItem name="advanced" title="高级搜索选项">
            <div class="grid grid-cols-1 gap-16px lg:grid-cols-3 md:grid-cols-2">
              <!-- 邮箱搜索 -->
              <ElFormItem label="邮箱">
                <ElInput v-model="searchParams.email" placeholder="搜索邮箱地址" clearable></ElInput>
              </ElFormItem>

              <!-- 余额范围 -->
              <ElFormItem label="余额范围">
                <div class="flex items-center gap-8px">
                  <ElInputNumber
                    v-model="searchParams.balanceMin"
                    placeholder="最小值"
                    :min="0"
                    :precision="2"
                    class="flex-1"
                  />
                  <span class="text-gray-400">-</span>
                  <ElInputNumber
                    v-model="searchParams.balanceMax"
                    placeholder="最大值"
                    :min="0"
                    :precision="2"
                    class="flex-1"
                  />
                </div>
              </ElFormItem>

              <!-- 费率范围 -->
              <ElFormItem label="费率范围">
                <div class="flex items-center gap-8px">
                  <ElInputNumber
                    v-model="searchParams.rateMin"
                    placeholder="最小值"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    :precision="2"
                    class="flex-1"
                  />
                  <span class="text-gray-400">-</span>
                  <ElInputNumber
                    v-model="searchParams.rateMax"
                    placeholder="最大值"
                    :min="0"
                    :max="1"
                    :step="0.01"
                    :precision="2"
                    class="flex-1"
                  />
                </div>
              </ElFormItem>

              <!-- 注册时间范围 -->
              <ElFormItem label="注册时间">
                <ElDatePicker
                  v-model="createTimeRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  @change="handleCreateTimeChange"
                />
              </ElFormItem>

              <!-- 最后登录时间范围 -->
              <ElFormItem label="最后登录">
                <ElDatePicker
                  v-model="lastLoginRange"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  @change="handleLastLoginChange"
                />
              </ElFormItem>

              <!-- 特殊条件 -->
              <ElFormItem label="特殊条件">
                <div class="space-y-8px">
                  <ElCheckbox v-model="searchParams.hasEmail">已设置邮箱</ElCheckbox>
                  <ElCheckbox v-model="searchParams.hasInvitees">有下级用户</ElCheckbox>
                </div>
              </ElFormItem>
            </div>

            <!-- 高级搜索操作按钮 -->
            <div class="mt-16px flex items-center gap-12px">
              <ElButton type="primary" @click="handleAdvancedSearch">执行高级搜索</ElButton>
              <ElButton @click="handleResetAdvanced">清空高级条件</ElButton>
              <ElButton type="success" @click="handleSaveSearchTemplate">保存搜索模板</ElButton>
              <ElButton type="info" @click="handleLoadSearchTemplate">加载搜索模板</ElButton>
            </div>
          </ElCollapseItem>
        </ElCollapse>


        <!-- 小屏筛选抽屉内容 -->
        <FilterDrawer v-model="filterDrawerVisible" @apply="handleSearch" @reset="handleReset">
          <div class="grid grid-cols-1 gap-12px">
            <ElSelect v-model="searchParams.userRole" placeholder="用户角色" clearable class="w-full">
              <ElOption v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
            <ElSelect v-model="searchParams.status" placeholder="状态" clearable class="w-full">
              <ElOption v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
            <ElDivider>高级搜索</ElDivider>
            <ElInput v-model="searchParams.email" placeholder="邮箱" clearable />
            <div class="grid grid-cols-2 gap-8px">
              <ElInputNumber v-model="searchParams.balanceMin" :min="0" :precision="2" placeholder="余额最小" />
              <ElInputNumber v-model="searchParams.balanceMax" :min="0" :precision="2" placeholder="余额最大" />
            </div>
            <div class="grid grid-cols-2 gap-8px">
              <ElInputNumber v-model="searchParams.rateMin" :min="0" :max="1" :step="0.01" :precision="2" placeholder="费率最小" />
              <ElInputNumber v-model="searchParams.rateMax" :min="0" :max="1" :step="0.01" :precision="2" placeholder="费率最大" />
            </div>
            <ElDatePicker
              v-model="createTimeRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="注册开始"
              end-placeholder="注册结束"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="handleCreateTimeChange"
            />
            <ElDatePicker
              v-model="lastLoginRange"
              type="datetimerange"
              range-separator="至"
              start-placeholder="登录开始"
              end-placeholder="登录结束"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              @change="handleLastLoginChange"
            />
            <div class="space-y-8px">
              <ElCheckbox v-model="searchParams.hasEmail">已设置邮箱</ElCheckbox>
              <ElCheckbox v-model="searchParams.hasInvitees">有下级用户</ElCheckbox>
            </div>
          </div>
        </FilterDrawer>

        <!-- 第二行：快速筛选和统计信息 -->
        <div class="flex flex-wrap items-center justify-between gap-16px">
          <div class="flex items-center gap-12px">
            <span class="text-sm text-gray-600">快速筛选:</span>
            <ElButtonGroup size="small">
              <ElButton :type="!searchParams.status ? 'primary' : ''" @click="handleQuickFilter('all')">全部</ElButton>
              <ElButton :type="searchParams.status === 1 ? 'success' : ''" @click="handleQuickFilter('active')">
                启用
              </ElButton>
              <ElButton :type="searchParams.status === 0 ? 'danger' : ''" @click="handleQuickFilter('inactive')">
                禁用
              </ElButton>
            </ElButtonGroup>

            <ElDivider direction="vertical" />

            <ElButtonGroup size="small">
              <ElButton :type="!searchParams.userRole ? 'primary' : ''" @click="handleRoleFilter('')">
                全部角色
              </ElButton>
              <ElButton :type="searchParams.userRole === 'vip' ? 'danger' : ''" @click="handleRoleFilter('vip')">
                VIP
              </ElButton>
              <ElButton :type="searchParams.userRole === 'agent' ? 'warning' : ''" @click="handleRoleFilter('agent')">
                代理商
              </ElButton>
            </ElButtonGroup>
          </div>

          <!-- 统计信息 -->
          <div class="flex items-center gap-16px text-sm">
            <div class="flex items-center gap-4px">
              <span class="text-gray-600">总用户:</span>
              <span class="text-blue-600 font-medium">{{ pagination.total }}</span>
            </div>
            <div class="flex items-center gap-4px">
              <span class="text-gray-600">已选择:</span>
              <span class="text-green-600 font-medium">{{ selectedUsers.length }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 数据表格（仅列表） -->
      <ElTable
        v-loading="loading"
        :data="tableData"
        style="width: 100%"
        max-height="70vh"
        stripe
        border
        :default-sort="{ prop: 'createTime', order: 'descending' }"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
    <ElTableColumn type="selection" width="55" />

        <!-- 用户基本信息 -->
        <ElTableColumn prop="username" label="用户名" width="120" sortable="custom">
          <template #default="{ row }">
            <div class="flex items-center gap-8px">
              <ElAvatar :size="32" :src="row.avatar">
                {{ row.username?.charAt(0)?.toUpperCase() }}
              </ElAvatar>
              <div>
                <div class="font-medium">{{ row.username }}</div>
                <div class="text-xs text-gray-500">ID: {{ row.userId }}</div>
              </div>
            </div>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="email" label="邮箱" width="200" show-overflow-tooltip sortable="custom">
          <template #default="{ row }">
            <div v-if="row.email" class="flex items-center gap-4px">
              <span>{{ row.email }}</span>
            </div>
            <span v-else class="text-gray-400">未设置</span>
          </template>
        </ElTableColumn>

        <!-- 费率信息 -->
        <ElTableColumn prop="userRate" label="费率" width="100" sortable="custom">
          <template #default="{ row }">
            <div class="flex items-center gap-4px">
              <ElTag :type="getUserRateType(row.userRate)" size="small" effect="dark">
                {{ (row.userRate * 100).toFixed(1) }}%
              </ElTag>
              <ElTooltip content="费率越低，享受的折扣越大" placement="top">💡</ElTooltip>
            </div>
          </template>
        </ElTableColumn>

        <!-- 角色信息 -->
        <ElTableColumn label="角色信息" width="120">
          <template #default="{ row }">
            <div class="flex items-center gap-4px">
              <ElTag
                v-if="row.roles && row.roles.length > 0"
                size="small"
                :type="getRoleTagType(row.roles[0].role_code)"
              >
                {{ row.roles[0].role_name }}
              </ElTag>
              <ElTag v-else size="small" type="info">
                {{ getRoleText(row.userRole || 'user') }}
              </ElTag>
            </div>
          </template>
        </ElTableColumn>

        <!-- 余额信息 -->
        <ElTableColumn prop="balance" label="余额" width="120" sortable="custom">
          <template #default="{ row }">
            <span class="font-medium" :class="row.balance > 0 ? 'text-green-600' : 'text-gray-500'">
              ¥{{ formatCurrency(row.balance) }}
            </span>
          </template>
        </ElTableColumn>

        <!-- 状态信息 -->
        <ElTableColumn prop="status" label="状态" width="100" sortable="custom">
          <template #default="{ row }">
            <div class="flex items-center gap-8px">
              <ElTag :type="row.status === 1 ? 'success' : 'danger'" effect="dark">
                {{ row.status === 1 ? '启用' : '禁用' }}
              </ElTag>
              <!-- 在线状态指示器 -->
              <div
                class="h-2 w-2 rounded-full"
                :class="getOnlineStatus(row.lastLoginTime)"
                :title="getOnlineStatusText(row.lastLoginTime)"
              ></div>
            </div>
          </template>
        </ElTableColumn>

        <!-- 时间信息 -->
        <ElTableColumn prop="createTime" label="创建时间" width="160" sortable="custom">
          <template #default="{ row }">
            <div class="text-sm">
              <div>{{ formatDateTime(row.createTime) }}</div>
              <div class="text-xs text-gray-500">
                {{ getRelativeTime(row.createTime) }}
              </div>
            </div>
          </template>
        </ElTableColumn>

        <!-- 操作列 -->
        <ElTableColumn label="操作" width="280" fixed="right">
          <template #default="{ row }">
            <div class="flex flex-wrap gap-4px">
              <ElButton size="small" type="success" @click="handleViewDetail(row.userId)">详情</ElButton>
              <ElButton size="small" type="primary" @click="handleEdit(row)">编辑</ElButton>

              <ElDropdown @command="command => handleRowAction(command, row)">
                <ElButton size="small" type="warning">更多</ElButton>
                <template #dropdown>
                  <ElDropdownMenu>
                    <ElDropdownItem command="toggle-status">
                      {{ row.status === 1 ? '禁用' : '启用' }}
                    </ElDropdownItem>
                    <ElDropdownItem command="view-permissions">查看权限</ElDropdownItem>
                    <ElDropdownItem v-if="canEditPassword(row.userId)" command="reset-password">
                      重置密码
                    </ElDropdownItem>
                    <ElDropdownItem command="recharge">充值</ElDropdownItem>
                    <ElDropdownItem command="deduct">扣费</ElDropdownItem>
                    <ElDropdownItem command="ban-user">封禁用户</ElDropdownItem>
                    <ElDropdownItem command="function-limit">功能限制</ElDropdownItem>
                    <ElDropdownItem command="balance-manage">资金管理</ElDropdownItem>



                    <ElDropdownItem command="view-logs">查看日志</ElDropdownItem>
                    <ElDropdownItem command="delete" divided>删除用户</ElDropdownItem>
                  </ElDropdownMenu>
                </template>
              </ElDropdown>
            </div>
          </template>
        </ElTableColumn>
      </ElTable>

      <!-- 分页 -->
      <div class="mt-16px flex justify-center md:justify-end">
        <ElPagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange" />
      </div>

      <!-- 权限预览抽屉：移动端优先 -->
      <UserPermissionDrawer v-model="permissionDrawerVisible" :user-id="permissionDrawerUserId || 0" />
    </ElCard>

    <!-- 用户表单模态框 -->
    <ElDialog
      v-model="modalVisible"
      :title="isEdit ? '编辑用户' : '新增用户'"
      width="700px"
      :close-on-click-modal="false"
      destroy-on-close
    >
      <div class="grid grid-cols-1 gap-24px lg:grid-cols-3">
        <!-- 表单区域 -->
        <div class="lg:col-span-2">
          <ElForm ref="formRef" :model="userForm" :rules="formRules" label-width="100px">
            <!-- 基本信息 -->
            <div class="mb-16px">
              <h4 class="text-md mb-12px text-gray-700 font-medium">基本信息</h4>

              <ElFormItem label="用户名" prop="username">
                <ElInput
                  v-model="userForm.username"
                  placeholder="请输入5-11位QQ号"
                  :disabled="isEdit"
                  @input="validateUsername"
                >
                  <template #suffix>
                    <span v-if="usernameValidation.status === 'success'" class="text-green-500">✓</span>
                    <span v-else-if="usernameValidation.status === 'error'" class="text-red-500">✗</span>
                  </template>
                </ElInput>
                <div
                  v-if="usernameValidation.message"
                  class="mt-4px text-xs"
                  :class="usernameValidation.status === 'error' ? 'text-red-500' : 'text-green-500'"
                >
                  {{ usernameValidation.message }}
                </div>
              </ElFormItem>

              <ElFormItem label="邮箱" prop="email">
                <ElInput v-model="userForm.email" placeholder="请输入邮箱地址" @input="validateEmail">
                  <template #suffix>
                    <span v-if="emailValidation.status === 'success'" class="text-green-500">✓</span>
                    <span v-else-if="emailValidation.status === 'error'" class="text-red-500">✗</span>
                  </template>
                </ElInput>
                <div
                  v-if="emailValidation.message"
                  class="mt-4px text-xs"
                  :class="emailValidation.status === 'error' ? 'text-red-500' : 'text-green-500'"
                >
                  {{ emailValidation.message }}
                </div>
              </ElFormItem>

              <ElFormItem label="密码" prop="password">
                <ElInput
                  v-model="userForm.password"
                  type="password"
                  :placeholder="isEdit ? '留空则不修改密码' : '请输入密码'"
                  :disabled="isEdit && !canEditPassword(userForm.userId!)"
                  show-password
                  @input="validatePassword"
                />
                <!-- 权限提示 -->
                <div v-if="isEdit && !canEditPassword(userForm.userId!)" class="mt-4px text-xs text-gray-500">
                  ℹ️ 只有用户本人和超级管理员可以修改密码
                </div>
                <!-- 密码强度指示器 -->
                <div v-if="userForm.password && !isEdit" class="mt-8px">
                  <div class="mb-4px flex items-center gap-8px">
                    <span class="text-xs text-gray-600">密码强度:</span>
                    <div class="flex gap-2px">
                      <div
                        v-for="i in 4"
                        :key="i"
                        class="h-2px w-16px rounded"
                        :class="
                          i <= passwordStrength.level ? getPasswordStrengthColor(passwordStrength.level) : 'bg-gray-200'
                        "
                      ></div>
                    </div>
                    <span class="text-xs" :class="getPasswordStrengthTextColor(passwordStrength.level)">
                      {{ passwordStrength.text }}
                    </span>
                  </div>
                  <div class="text-xs text-gray-500">
                    {{ passwordStrength.tips }}
                  </div>
                </div>
              </ElFormItem>
            </div>

            <!-- 权限设置 -->
            <div class="mb-16px">
              <h4 class="text-md mb-12px text-gray-700 font-medium">权限设置</h4>

              <ElFormItem label="费率" prop="userRate">
                <div class="w-full">
                  <ElSlider
                    v-model="userForm.userRate"
                    :min="0.1"
                    :max="1"
                    :step="0.01"
                    :format-tooltip="formatRateTooltip"
                    show-input
                    :show-input-controls="false"
                    class="mb-8px"
                  />
                  <div class="flex justify-between text-xs text-gray-500">
                    <span>VIP (0.1-0.2)</span>
                    <span>代理商 (0.2-0.5)</span>
                    <span>会员 (0.5-0.8)</span>
                    <span>普通用户 (0.8-1.0)</span>
                  </div>
                  <div class="mt-8px rounded bg-blue-50 p-8px text-xs text-blue-600">
                    当前费率: {{ (userForm.userRate * 100).toFixed(1) }}% - {{ getRateDescription(userForm.userRate) }}
                  </div>
                </div>
              </ElFormItem>

              <ElFormItem label="角色" prop="userRole">
                <ElSelect v-model="userForm.userRole" style="width: 100%" @change="onRoleChange">
                  <ElOption v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item.value">
                    <div class="flex items-center justify-between">
                      <span>{{ item.label }}</span>
                      <ElTag size="small" :type="getRoleTagType(item.value)">
                        {{ getRoleDescription(item.value) }}
                      </ElTag>
                    </div>
                  </ElOption>
                </ElSelect>
              </ElFormItem>
            </div>

            <!-- 账户设置 -->
            <div class="mb-16px">
              <h4 class="text-md mb-12px text-gray-700 font-medium">账户设置</h4>

              <ElFormItem label="初始余额">
                <ElInputNumber
                  v-model="userForm.balance"
                  :min="0"
                  :precision="2"
                  style="width: 100%"
                  controls-position="right"
                />
                <div class="mt-4px text-xs text-gray-500">设置用户的初始账户余额</div>
              </ElFormItem>

              <ElFormItem label="状态">
                <ElRadioGroup v-model="userForm.status">
                  <ElRadio :label="1">
                    <div class="flex items-center gap-4px">
                      <span>启用</span>
                    </div>
                  </ElRadio>
                  <ElRadio :label="0">
                    <div class="flex items-center gap-4px">
                      <span>禁用</span>
                    </div>
                  </ElRadio>
                </ElRadioGroup>
                <div class="mt-4px text-xs text-gray-500">禁用后用户将无法登录系统</div>
              </ElFormItem>
            </div>
          </ElForm>
        </div>

        <!-- 预览区域 -->
        <div class="lg:col-span-1">
          <div class="sticky top-0">
            <h4 class="text-md mb-12px text-gray-700 font-medium">用户预览</h4>
            <ElCard shadow="never" class="border border-gray-200">
              <div class="text-center space-y-12px">
                <ElAvatar :size="60">
                  {{ userForm.username?.charAt(0)?.toUpperCase() || 'U' }}
                </ElAvatar>

                <div>
                  <div class="text-lg font-medium">
                    {{ userForm.username || '用户名' }}
                  </div>
                  <div class="text-sm text-gray-500">
                    {{ userForm.email || '邮箱未设置' }}
                  </div>
                </div>

                <div class="space-y-8px">
                  <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">角色:</span>
                    <ElTag :type="getRoleTagType(userForm.userRole)" size="small">
                      {{ getRoleText(userForm.userRole) }}
                    </ElTag>
                  </div>

                  <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">费率:</span>
                    <ElTag :type="getUserRateType(userForm.userRate)" size="small">
                      {{ (userForm.userRate * 100).toFixed(1) }}%
                    </ElTag>
                  </div>

                  <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">余额:</span>
                    <span class="text-green-600 font-medium">¥{{ formatCurrency(userForm.balance) }}</span>
                  </div>

                  <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-600">状态:</span>
                    <ElTag :type="userForm.status === 1 ? 'success' : 'danger'" size="small">
                      {{ userForm.status === 1 ? '启用' : '禁用' }}
                    </ElTag>
                  </div>
                </div>

                <!-- 权限说明 -->
                <div class="rounded bg-gray-50 p-8px text-xs text-gray-500">
                  {{ getRoleDescription(userForm.userRole) }}
                </div>
              </div>
            </ElCard>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-500">ℹ️ {{ isEdit ? '修改后将立即生效' : '创建后用户可立即使用' }}</div>
          <div class="flex gap-8px">
            <ElButton @click="modalVisible = false">取消</ElButton>
            <ElButton type="primary" :loading="saving" :disabled="!isFormValid" @click="handleSave">
              {{ isEdit ? '更新用户' : '创建用户' }}
            </ElButton>
          </div>
        </div>
      </template>
    </ElDialog>

    <!-- 批量调费率模态框 -->
    <ElDialog v-model="batchRateModalVisible" title="批量调费率" width="400px" :close-on-click-modal="false">
      <ElForm ref="batchRateFormRef" :model="batchRateForm" :rules="batchRateRules" label-width="80px">
        <ElFormItem label="新费率" prop="newRate">
          <ElInputNumber
            v-model="batchRateForm.newRate"
            :min="0"
            :max="1"
            :step="0.01"
            :precision="2"
            style="width: 100%"
          />
        </ElFormItem>

        <div class="mb-4 text-sm text-gray-500">将为 {{ selectedUsers.length }} 个用户调整费率</div>
      </ElForm>

      <template #footer>
        <div class="flex justify-end gap-8px">
          <ElButton @click="batchRateModalVisible = false">取消</ElButton>
          <ElButton type="primary" :loading="batchRateSaving" @click="handleBatchRateSave">确认调整</ElButton>
        </div>
      </template>
    </ElDialog>

    <!-- 角色分配模态框已删除 - 改为单角色模式 -->

    <!-- 用户权限查看模态框 -->
    <ElDialog v-model="userPermissionModalVisible" title="用户权限详情" width="500px">
      <div class="space-y-16px">
        <div>
          <div class="mb-8px text-14px font-medium">用户权限列表</div>
          <div class="flex flex-wrap gap-8px">
            <ElTag v-for="permission in userPermissions" :key="permission" type="primary">
              {{ permission }}
            </ElTag>
            <ElTag v-if="userPermissions.length === 0" type="info">暂无权限</ElTag>
          </div>
        </div>

        <div>
          <div class="mb-8px text-14px font-medium">权限说明</div>
          <div class="text-12px text-gray-600 leading-relaxed">
            用户的实际权限由其分配的角色决定。每个用户只能有一个角色， 权限由角色统一管理。
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end">
          <ElButton @click="userPermissionModalVisible = false">关闭</ElButton>
        </div>
      </template>
    </ElDialog>

    <!-- 批量操作组件 -->
    <BatchOperations
      :selected-items="selectedUsers"
      :operations="batchOperations"
      :loading="batchRateSaving"
      @operation="handleBatchOperation"
      @clear-selection="selectedUsers = []"
    />

    <!-- 用户详情弹窗 -->
    <UserDetailDialog v-model:visible="userDetailVisible" :user-id="currentDetailUserId" @edit-user="handleEdit" />

    <!-- 高级用户筛选 -->
    <AdvancedUserFilter v-model:visible="advancedFilterVisible" @apply-filter="applyFilterResult" />

    <!-- 搜索模板保存对话框 -->
    <ElDialog v-model="searchTemplateModalVisible" title="保存搜索模板" width="500px" :close-on-click-modal="false">
      <ElForm :model="searchTemplateForm" label-width="80px">
        <ElFormItem label="模板名称" required>
          <ElInput v-model="searchTemplateForm.name" placeholder="请输入模板名称" maxlength="50" show-word-limit />
        </ElFormItem>
        <ElFormItem label="模板描述">
          <ElInput
            v-model="searchTemplateForm.description"
            type="textarea"
            placeholder="请输入模板描述（可选）"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
        </ElFormItem>

        <!-- 当前搜索条件预览 -->
        <ElFormItem label="搜索条件">
          <div class="rounded bg-gray-50 p-12px text-sm">
            <div class="space-y-4px">
              <div v-if="searchParams.keyword">
                <span class="text-gray-600">关键词:</span>
                <span class="ml-4px">{{ searchParams.keyword }}</span>
              </div>
              <div v-if="searchParams.userRole">
                <span class="text-gray-600">角色:</span>
                <span class="ml-4px">{{ getRoleText(searchParams.userRole) }}</span>
              </div>
              <div v-if="searchParams.status !== undefined">
                <span class="text-gray-600">状态:</span>
                <span class="ml-4px">{{ searchParams.status === 1 ? '启用' : '禁用' }}</span>
              </div>
              <div v-if="searchParams.email">
                <span class="text-gray-600">邮箱:</span>
                <span class="ml-4px">{{ searchParams.email }}</span>
              </div>
              <div v-if="searchParams.balanceMin !== undefined || searchParams.balanceMax !== undefined">
                <span class="text-gray-600">余额范围:</span>
                <span class="ml-4px">{{ searchParams.balanceMin || 0 }} - {{ searchParams.balanceMax || '∞' }}</span>
              </div>
              <div v-if="searchParams.rateMin !== undefined || searchParams.rateMax !== undefined">
                <span class="text-gray-600">费率范围:</span>
                <span class="ml-4px">
                  {{ (searchParams.rateMin || 0) * 100 }}% - {{ (searchParams.rateMax || 1) * 100 }}%
                </span>
              </div>
              <div v-if="searchParams.createTimeStart && searchParams.createTimeEnd">
                <span class="text-gray-600">注册时间:</span>
                <span class="ml-4px">{{ searchParams.createTimeStart }} 至 {{ searchParams.createTimeEnd }}</span>
              </div>
              <div v-if="searchParams.hasEmail !== undefined">
                <span class="text-gray-600">邮箱设置:</span>
                <span class="ml-4px">{{ searchParams.hasEmail ? '已设置' : '未设置' }}</span>
              </div>
              <div v-if="searchParams.hasInvitees !== undefined">
                <span class="text-gray-600">下级用户:</span>
                <span class="ml-4px">{{ searchParams.hasInvitees ? '有下级' : '无下级' }}</span>
              </div>
            </div>
          </div>
        </ElFormItem>
      </ElForm>

      <template #footer>
        <div class="flex justify-end gap-8px">
          <ElButton @click="searchTemplateModalVisible = false">取消</ElButton>
          <ElButton type="primary" @click="saveSearchTemplate">保存模板</ElButton>
        </div>
      </template>
    </ElDialog>

    <!-- 余额管理对话框 -->
    <ElDialog
      v-model="balanceDialogVisible"
      :title="balanceOperation === 'recharge' ? '用户充值' : '用户扣费'"
      width="400px"
      :close-on-click-modal="false"
    >
      <ElForm label-width="80px">
        <ElFormItem label="操作类型">
          <ElTag :type="balanceOperation === 'recharge' ? 'success' : 'warning'" size="large">
            {{ balanceOperation === 'recharge' ? '充值' : '扣费' }}
          </ElTag>
        </ElFormItem>

        <ElFormItem label="金额" required>
          <ElInputNumber
            v-model="balanceAmount"
            :min="0.01"
            :max="999999.99"
            :precision="2"
            :step="1"
            placeholder="请输入金额"
            class="w-full"
          >
            <template #prefix>¥</template>
          </ElInputNumber>
        </ElFormItem>

        <ElFormItem label="备注">
          <ElInput
            v-model="balanceRemark"
            type="textarea"
            :rows="3"
            :placeholder="`请输入${balanceOperation === 'recharge' ? '充值' : '扣费'}备注（可选）`"
            maxlength="200"
            show-word-limit
          />
        </ElFormItem>
      </ElForm>

      <template #footer>
        <div class="flex justify-end gap-8px">
          <ElButton @click="balanceDialogVisible = false">取消</ElButton>
          <ElButton
            :type="balanceOperation === 'recharge' ? 'success' : 'warning'"
            :loading="balanceManaging"
            @click="handleBalanceOperation"
          >
            确认{{ balanceOperation === 'recharge' ? '充值' : '扣费' }}
          </ElButton>
        </div>
      </template>
    </ElDialog>

    <!-- 用户资金管理 -->
    <UserBalanceManager
      v-model:visible="balanceManagerVisible"
      :user-id="currentBalanceUserId"
      :user-info="currentBalanceUserInfo"
      @success="handleBalanceManagerSuccess"
    />

    <!-- 简单权限管理 -->
    <SimplePermissionManager
      v-model:visible="simplePermissionManagerVisible"
      :user-id="currentPermissionUserId"
      :user-info="currentPermissionUserInfo"
      @success="handlePermissionManagerSuccess"
    />

    <!-- 用户功能限制 -->
    <UserFunctionLimiter
      v-model:visible="functionLimiterVisible"
      :user-id="currentLimiterUserId"
      :user-info="currentLimiterUserInfo"
      @success="handleFunctionLimiterSuccess"
    />
  </div>
</template>

<style scoped>
/* 响应式表格滚动 */
.el-table {
  @apply w-full;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .el-table {
    font-size: 12px;
  }
}
</style>
