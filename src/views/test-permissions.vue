<script setup lang="ts">
import { ref } from 'vue';
import SimplePermissionManager from '@/components/permission/SimplePermissionManager.vue';
import UserFunctionLimiter from '@/components/user/UserFunctionLimiter.vue';

// 测试数据
const permissionManagerVisible = ref(false);
const functionLimiterVisible = ref(false);
const testUserId = ref(1);
const testUserInfo = ref({
  userId: 1,
  username: 'admin',
  email: '<EMAIL>',
  status: 1
});

function openPermissionManager() {
  permissionManagerVisible.value = true;
}

function openFunctionLimiter() {
  functionLimiterVisible.value = true;
}

function handleSuccess() {
  console.log('操作成功');
}
</script>

<template>
  <div class="p-24px">
    <div class="space-y-16px">
      <ElCard>
        <template #header>
          <div class="flex items-center gap-8px">
            <icon-ic-round-settings class="text-blue-500" />
            <span class="font-medium">权限管理测试</span>
          </div>
        </template>

        <div class="space-y-16px">
          <div>
            <h3 class="mb-8px text-lg font-medium">测试用户信息</h3>
            <div class="rounded-8px bg-gray-50 p-12px">
              <div>用户ID: {{ testUserInfo.userId }}</div>
              <div>用户名: {{ testUserInfo.username }}</div>
              <div>邮箱: {{ testUserInfo.email }}</div>
              <div>状态: {{ testUserInfo.status === 1 ? '正常' : '禁用' }}</div>
            </div>
          </div>

          <div class="flex gap-16px">
            <ElButton type="primary" @click="openPermissionManager">
              <template #icon>
                <icon-ic-round-security />
              </template>
              测试简单权限管理
            </ElButton>

            <ElButton type="warning" @click="openFunctionLimiter">
              <template #icon>
                <icon-ic-round-block />
              </template>
              测试功能限制管理
            </ElButton>
          </div>
        </div>
      </ElCard>
    </div>

    <!-- 简单权限管理 -->
    <SimplePermissionManager
      v-model:visible="permissionManagerVisible"
      :user-id="testUserId"
      :user-info="testUserInfo"
      @success="handleSuccess"
    />

    <!-- 用户功能限制 -->
    <UserFunctionLimiter
      v-model:visible="functionLimiterVisible"
      :user-id="testUserId"
      :user-info="testUserInfo"
      @success="handleSuccess"
    />
  </div>
</template>

<style scoped>
/* 测试页面样式 */
</style>
