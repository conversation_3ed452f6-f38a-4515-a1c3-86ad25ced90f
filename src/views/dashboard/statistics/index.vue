<script setup lang="ts">
import { nextTick, onMounted, onUnmounted, reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { Refresh } from '@element-plus/icons-vue';
import * as echarts from 'echarts';
import {
  STATS_PERIOD_TEXT,
  StatsPeriod,
  fetchOrderAnalytics,
  fetchRealTimeStats,
  fetchRevenueAnalytics,
  fetchSystemOverview,
  fetchUserAnalytics,
  formatters
} from '@/service/api/statistics';

defineOptions({
  name: 'StatisticsAnalysis'
});

// 响应式数据
const loading = ref(false);
const activeTab = ref('users');
const selectedPeriod = ref<StatsPeriod>(StatsPeriod.DAY_7);

// 统计数据类型定义
interface OverviewData {
  users: { totalUsers: number; activeUsers: number; todayUsers: number; totalBalance: number };
  orders: {
    totalOrders: number;
    completedOrders: number;
    todayOrders: number;
    totalRevenue: number;
    todayRevenue: number;
  };
  courses: { totalCourses: number; activeCourses: number };
  providers: { totalProviders: number; activeProviders: number };
}

const overview = ref<OverviewData>({
  users: { totalUsers: 0, activeUsers: 0, todayUsers: 0, totalBalance: 0 },
  orders: { totalOrders: 0, completedOrders: 0, todayOrders: 0, totalRevenue: 0, todayRevenue: 0 },
  courses: { totalCourses: 0, activeCourses: 0 },
  providers: { totalProviders: 0, activeProviders: 0 }
});

const realTimeStats = ref({
  today: { todayUsers: 0, todayOrders: 0, todayRevenue: 0, todayCompletedOrders: 0 },
  activeUsers: 0,
  systemStatus: { processingOrders: 0, failedOrders: 0, activeUsers: 0, activeCourses: 0 }
});

const userAnalytics = ref({
  registrationTrend: [],
  roleDistribution: [],
  balanceDistribution: [],
  activeUsersTrend: []
});

const orderAnalytics = ref({
  orderTrend: [],
  statusDistribution: [],
  popularCourses: [],
  userOrderDistribution: []
});

const revenueAnalytics = ref({
  revenueTrend: [],
  revenueByType: [],
  userContribution: [],
  monthlyComparison: [],
  summary: { totalRevenue: 0, totalCost: 0, totalProfit: 0, avgOrderValue: 0, totalOrders: 0 }
});

// 图表引用
const userTrendChart = ref<HTMLElement>();
const userRoleChart = ref<HTMLElement>();
const userBalanceChart = ref<HTMLElement>();
const activeUsersChart = ref<HTMLElement>();
const orderTrendChart = ref<HTMLElement>();
const orderStatusChart = ref<HTMLElement>();
const popularCoursesChart = ref<HTMLElement>();
const userOrderChart = ref<HTMLElement>();
const revenueTrendChart = ref<HTMLElement>();
const revenueSourceChart = ref<HTMLElement>();
const userContributionChart = ref<HTMLElement>();

// 图表实例
const charts = reactive<Record<string, echarts.ECharts>>({});

// 加载系统概览
async function loadOverview() {
  try {
    const response = await fetchSystemOverview();
    overview.value = response;
  } catch (error) {
    console.error('获取系统概览失败:', error);
    // 不显示错误消息，使用默认值
    overview.value = {
      users: { totalUsers: 0, activeUsers: 0, todayUsers: 0, totalBalance: 0 },
      orders: { totalOrders: 0, completedOrders: 0, todayOrders: 0, totalRevenue: 0, todayRevenue: 0 },
      courses: { totalCourses: 0, activeCourses: 0 },
      providers: { totalProviders: 0, activeProviders: 0 }
    };
  }
}

// 加载实时统计
async function loadRealTimeStats() {
  try {
    const response = await fetchRealTimeStats();
    realTimeStats.value = response;
  } catch (error) {
    console.error('获取实时统计失败:', error);
    // 不显示错误消息，使用默认值
    realTimeStats.value = {
      today: { todayUsers: 0, todayOrders: 0, todayRevenue: 0, todayCompletedOrders: 0 },
      activeUsers: 0,
      systemStatus: { processingOrders: 0, failedOrders: 0, activeUsers: 0, activeCourses: 0 }
    };
  }
}

// 加载用户分析数据
async function loadUserAnalytics() {
  try {
    const response = await fetchUserAnalytics({ period: selectedPeriod.value });
    userAnalytics.value = response;
    await nextTick();
    renderUserCharts();
  } catch (error) {
    console.error('获取用户分析数据失败:', error);
    // 不显示错误消息，使用默认值
    userAnalytics.value = {
      registrationTrend: [],
      roleDistribution: [],
      balanceDistribution: [],
      activeUsersTrend: []
    };
    await nextTick();
    renderUserCharts();
  }
}

// 加载订单分析数据
async function loadOrderAnalytics() {
  try {
    const response = await fetchOrderAnalytics({ period: selectedPeriod.value });
    orderAnalytics.value = response;
    await nextTick();
    renderOrderCharts();
  } catch (error) {
    console.error('获取订单分析数据失败:', error);
    // 不显示错误消息，使用默认值
    orderAnalytics.value = {
      orderTrend: [],
      statusDistribution: [],
      popularCourses: [],
      userOrderDistribution: []
    };
    await nextTick();
    renderOrderCharts();
  }
}

// 加载收入分析数据
async function loadRevenueAnalytics() {
  try {
    const response = await fetchRevenueAnalytics({ period: selectedPeriod.value });
    revenueAnalytics.value = response;
    await nextTick();
    renderRevenueCharts();
  } catch (error) {
    console.error('获取收入分析数据失败:', error);
    // 不显示错误消息，使用默认值
    revenueAnalytics.value = {
      revenueTrend: [],
      revenueByType: [],
      userContribution: [],
      monthlyComparison: [],
      summary: {
        totalRevenue: 0,
        totalCost: 0,
        totalProfit: 0,
        avgOrderValue: 0,
        totalOrders: 0
      }
    };
    await nextTick();
    renderRevenueCharts();
  }
}

// 渲染用户图表
function renderUserCharts() {
  // 用户注册趋势图表
  if (userTrendChart.value) {
    const chart = echarts.init(userTrendChart.value);
    charts.userTrend = chart;

    chart.setOption({
      title: { text: '' },
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: (userAnalytics.value.registrationTrend || []).map((item: any) => item.date)
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '新增用户',
          type: 'line',
          data: (userAnalytics.value.registrationTrend || []).map((item: any) => item.count),
          smooth: true,
          areaStyle: {}
        }
      ]
    });
  }

  // 用户角色分布饼图
  if (userRoleChart.value) {
    const chart = echarts.init(userRoleChart.value);
    charts.userRole = chart;

    chart.setOption({
      title: { text: '' },
      tooltip: { trigger: 'item' },
      series: [
        {
          name: '用户角色',
          type: 'pie',
          radius: '50%',
          data: (userAnalytics.value.roleDistribution || []).map((item: any) => ({
            value: item.count,
            name: item.user_role
          }))
        }
      ]
    });
  }

  // 用户余额分布柱状图
  if (userBalanceChart.value) {
    const chart = echarts.init(userBalanceChart.value);
    charts.userBalance = chart;

    chart.setOption({
      title: { text: '' },
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: (userAnalytics.value.balanceDistribution || []).map((item: any) => item.balanceRange)
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '用户数',
          type: 'bar',
          data: (userAnalytics.value.balanceDistribution || []).map((item: any) => item.count)
        }
      ]
    });
  }

  // 活跃用户趋势图表
  if (activeUsersChart.value) {
    const chart = echarts.init(activeUsersChart.value);
    charts.activeUsers = chart;

    chart.setOption({
      title: { text: '' },
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: (userAnalytics.value.activeUsersTrend || []).map((item: any) => item.date)
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '活跃用户',
          type: 'line',
          data: (userAnalytics.value.activeUsersTrend || []).map((item: any) => item.activeUsers),
          smooth: true
        }
      ]
    });
  }
}

// 渲染订单图表
function renderOrderCharts() {
  // 订单趋势图表
  if (orderTrendChart.value) {
    const chart = echarts.init(orderTrendChart.value);
    charts.orderTrend = chart;

    chart.setOption({
      title: { text: '' },
      tooltip: { trigger: 'axis' },
      legend: { data: ['总订单', '已完成', '已取消'] },
      xAxis: {
        type: 'category',
        data: orderAnalytics.value.orderTrend.map((item: any) => item.date)
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '总订单',
          type: 'line',
          data: orderAnalytics.value.orderTrend.map((item: any) => item.totalOrders)
        },
        {
          name: '已完成',
          type: 'line',
          data: orderAnalytics.value.orderTrend.map((item: any) => item.completedOrders)
        },
        {
          name: '已取消',
          type: 'line',
          data: orderAnalytics.value.orderTrend.map((item: any) => item.cancelledOrders)
        }
      ]
    });
  }

  // 订单状态分布饼图
  if (orderStatusChart.value) {
    const chart = echarts.init(orderStatusChart.value);
    charts.orderStatus = chart;

    chart.setOption({
      title: { text: '' },
      tooltip: { trigger: 'item' },
      series: [
        {
          name: '订单状态',
          type: 'pie',
          radius: '50%',
          data: orderAnalytics.value.statusDistribution.map((item: any) => ({
            value: item.count,
            name: item.statusText
          }))
        }
      ]
    });
  }

  // 热门课程柱状图
  if (popularCoursesChart.value) {
    const chart = echarts.init(popularCoursesChart.value);
    charts.popularCourses = chart;

    chart.setOption({
      title: { text: '' },
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: orderAnalytics.value.popularCourses.map((item: any) => item.course_name).slice(0, 10),
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '订单数',
          type: 'bar',
          data: orderAnalytics.value.popularCourses.map((item: any) => item.orderCount).slice(0, 10)
        }
      ]
    });
  }

  // 用户订单分布饼图
  if (userOrderChart.value) {
    const chart = echarts.init(userOrderChart.value);
    charts.userOrder = chart;

    chart.setOption({
      title: { text: '' },
      tooltip: { trigger: 'item' },
      series: [
        {
          name: '用户订单分布',
          type: 'pie',
          radius: '50%',
          data: orderAnalytics.value.userOrderDistribution.map((item: any) => ({
            value: item.userCount,
            name: item.orderRange
          }))
        }
      ]
    });
  }
}

// 渲染收入图表
function renderRevenueCharts() {
  // 收入趋势图表
  if (revenueTrendChart.value) {
    const chart = echarts.init(revenueTrendChart.value);
    charts.revenueTrend = chart;

    chart.setOption({
      title: { text: '' },
      tooltip: { trigger: 'axis' },
      legend: { data: ['收入', '成本', '利润'] },
      xAxis: {
        type: 'category',
        data: revenueAnalytics.value.revenueTrend.map((item: any) => item.date)
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '收入',
          type: 'line',
          data: revenueAnalytics.value.revenueTrend.map((item: any) => item.revenue),
          smooth: true
        },
        {
          name: '成本',
          type: 'line',
          data: revenueAnalytics.value.revenueTrend.map((item: any) => item.cost),
          smooth: true
        },
        {
          name: '利润',
          type: 'line',
          data: revenueAnalytics.value.revenueTrend.map((item: any) => item.profit),
          smooth: true
        }
      ]
    });
  }

  // 收入来源饼图
  if (revenueSourceChart.value) {
    const chart = echarts.init(revenueSourceChart.value);
    charts.revenueSource = chart;

    chart.setOption({
      title: { text: '' },
      tooltip: { trigger: 'item' },
      series: [
        {
          name: '收入来源',
          type: 'pie',
          radius: '50%',
          data: revenueAnalytics.value.revenueByType.map((item: any) => ({
            value: item.revenue,
            name: item.platform_type
          }))
        }
      ]
    });
  }

  // 用户贡献柱状图
  if (userContributionChart.value) {
    const chart = echarts.init(userContributionChart.value);
    charts.userContribution = chart;

    chart.setOption({
      title: { text: '' },
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: revenueAnalytics.value.userContribution.map((item: any) => item.username).slice(0, 20),
        axisLabel: {
          rotate: 45
        }
      },
      yAxis: { type: 'value' },
      series: [
        {
          name: '消费金额',
          type: 'bar',
          data: revenueAnalytics.value.userContribution.map((item: any) => item.totalSpent).slice(0, 20)
        }
      ]
    });
  }
}

// 标签切换处理
function handleTabChange(tabName: string | number) {
  activeTab.value = String(tabName);
  nextTick(() => {
    switch (tabName) {
      case 'users':
        loadUserAnalytics();
        break;
      case 'orders':
        loadOrderAnalytics();
        break;
      case 'revenue':
        loadRevenueAnalytics();
        break;
    }
  });
}

// 时间周期切换处理
function handlePeriodChange() {
  switch (activeTab.value) {
    case 'users':
      loadUserAnalytics();
      break;
    case 'orders':
      loadOrderAnalytics();
      break;
    case 'revenue':
      loadRevenueAnalytics();
      break;
  }
}

// 刷新数据
function refreshData() {
  loading.value = true;
  Promise.all([
    loadOverview(),
    loadRealTimeStats(),
    activeTab.value === 'users' ? loadUserAnalytics() : Promise.resolve(),
    activeTab.value === 'orders' ? loadOrderAnalytics() : Promise.resolve(),
    activeTab.value === 'revenue' ? loadRevenueAnalytics() : Promise.resolve()
  ]).finally(() => {
    loading.value = false;
  });
}

// 窗口大小变化时重新调整图表
function handleResize() {
  Object.values(charts).forEach(chart => {
    chart.resize();
  });
}

// 生命周期
onMounted(() => {
  loadOverview();
  loadRealTimeStats();
  loadUserAnalytics();

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
});

// 组件卸载时清理
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  Object.values(charts).forEach(chart => {
    chart.dispose();
  });
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <!-- 实时统计卡片 -->
    <div class="grid grid-cols-1 gap-16px lg:grid-cols-4 md:grid-cols-2">
      <ElCard class="stat-card">
        <div class="flex items-center justify-between">
          <div>
            <div class="mb-4px text-12px text-gray-500">今日新增用户</div>
            <div class="text-24px text-blue-600 font-bold">{{ realTimeStats.today?.todayUsers || 0 }}</div>
          </div>
          <div class="stat-icon bg-blue-100">
            <SvgIcon icon="carbon:user-multiple" class="text-24px text-blue-600" />
          </div>
        </div>
      </ElCard>

      <ElCard class="stat-card">
        <div class="flex items-center justify-between">
          <div>
            <div class="mb-4px text-12px text-gray-500">今日订单数</div>
            <div class="text-24px text-green-600 font-bold">{{ realTimeStats.today?.todayOrders || 0 }}</div>
          </div>
          <div class="stat-icon bg-green-100">
            <SvgIcon icon="carbon:shopping-cart" class="text-24px text-green-600" />
          </div>
        </div>
      </ElCard>

      <ElCard class="stat-card">
        <div class="flex items-center justify-between">
          <div>
            <div class="mb-4px text-12px text-gray-500">今日收入</div>
            <div class="text-24px text-orange-600 font-bold">
              ¥{{ formatters.number(realTimeStats.today?.todayRevenue || 0) }}
            </div>
          </div>
          <div class="stat-icon bg-orange-100">
            <SvgIcon icon="carbon:currency-dollar" class="text-24px text-orange-600" />
          </div>
        </div>
      </ElCard>

      <ElCard class="stat-card">
        <div class="flex items-center justify-between">
          <div>
            <div class="mb-4px text-12px text-gray-500">活跃用户</div>
            <div class="text-24px text-purple-600 font-bold">{{ realTimeStats.activeUsers || 0 }}</div>
          </div>
          <div class="stat-icon bg-purple-100">
            <SvgIcon icon="carbon:user-activity" class="text-24px text-purple-600" />
          </div>
        </div>
      </ElCard>
    </div>

    <!-- 系统概览 -->
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-12px">
            <SvgIcon icon="carbon:dashboard" class="text-24px text-primary" />
            <span class="text-18px font-semibold">系统概览</span>
          </div>
          <ElButton type="primary" :icon="Refresh" :loading="loading" @click="refreshData">刷新</ElButton>
        </div>
      </template>

      <div class="grid grid-cols-1 gap-24px lg:grid-cols-4 md:grid-cols-2">
        <!-- 用户统计 -->
        <div class="overview-section">
          <div class="section-title">用户统计</div>
          <div class="stat-item">
            <span class="label">总用户数</span>
            <span class="value">{{ formatters.number(overview.users?.totalUsers || 0) }}</span>
          </div>
          <div class="stat-item">
            <span class="label">活跃用户</span>
            <span class="value text-green-600">{{ formatters.number(overview.users?.activeUsers || 0) }}</span>
          </div>
          <div class="stat-item">
            <span class="label">用户余额</span>
            <span class="value">¥{{ formatters.number(overview.users?.totalBalance || 0) }}</span>
          </div>
        </div>

        <!-- 订单统计 -->
        <div class="overview-section">
          <div class="section-title">订单统计</div>
          <div class="stat-item">
            <span class="label">总订单数</span>
            <span class="value">{{ formatters.number(overview.orders?.totalOrders || 0) }}</span>
          </div>
          <div class="stat-item">
            <span class="label">已完成</span>
            <span class="value text-green-600">{{ formatters.number(overview.orders?.completedOrders || 0) }}</span>
          </div>
          <div class="stat-item">
            <span class="label">总收入</span>
            <span class="value">¥{{ formatters.number(overview.orders?.totalRevenue || 0) }}</span>
          </div>
        </div>

        <!-- 课程统计 -->
        <div class="overview-section">
          <div class="section-title">课程统计</div>
          <div class="stat-item">
            <span class="label">总课程数</span>
            <span class="value">{{ formatters.number(overview.courses?.totalCourses || 0) }}</span>
          </div>
          <div class="stat-item">
            <span class="label">上架课程</span>
            <span class="value text-green-600">{{ formatters.number(overview.courses?.activeCourses || 0) }}</span>
          </div>
        </div>

        <!-- 供应商统计 -->
        <div class="overview-section">
          <div class="section-title">供应商统计</div>
          <div class="stat-item">
            <span class="label">总供应商</span>
            <span class="value">{{ formatters.number(overview.providers?.totalProviders || 0) }}</span>
          </div>
          <div class="stat-item">
            <span class="label">活跃供应商</span>
            <span class="value text-green-600">{{ formatters.number(overview.providers?.activeProviders || 0) }}</span>
          </div>
        </div>
      </div>
    </ElCard>

    <!-- 数据分析选项卡 -->
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-12px">
            <SvgIcon icon="carbon:analytics" class="text-24px text-primary" />
            <span class="text-18px font-semibold">数据分析</span>
          </div>
          <div class="flex items-center gap-8px">
            <ElSelect v-model="selectedPeriod" style="width: 120px" @change="handlePeriodChange">
              <ElOption v-for="(text, value) in STATS_PERIOD_TEXT" :key="value" :label="text" :value="value" />
            </ElSelect>
          </div>
        </div>
      </template>

      <ElTabs v-model="activeTab" @tab-change="handleTabChange">
        <ElTabPane label="用户分析" name="users">
          <div class="grid grid-cols-1 gap-16px lg:grid-cols-2">
            <!-- 用户注册趋势 -->
            <div class="chart-container">
              <div class="chart-title">用户注册趋势</div>
              <div ref="userTrendChart" class="chart"></div>
            </div>

            <!-- 用户角色分布 -->
            <div class="chart-container">
              <div class="chart-title">用户角色分布</div>
              <div ref="userRoleChart" class="chart"></div>
            </div>

            <!-- 用户余额分布 -->
            <div class="chart-container">
              <div class="chart-title">用户余额分布</div>
              <div ref="userBalanceChart" class="chart"></div>
            </div>

            <!-- 活跃用户趋势 -->
            <div class="chart-container">
              <div class="chart-title">活跃用户趋势</div>
              <div ref="activeUsersChart" class="chart"></div>
            </div>
          </div>
        </ElTabPane>

        <ElTabPane label="订单分析" name="orders">
          <div class="grid grid-cols-1 gap-16px lg:grid-cols-2">
            <!-- 订单趋势 -->
            <div class="chart-container">
              <div class="chart-title">订单趋势</div>
              <div ref="orderTrendChart" class="chart"></div>
            </div>

            <!-- 订单状态分布 -->
            <div class="chart-container">
              <div class="chart-title">订单状态分布</div>
              <div ref="orderStatusChart" class="chart"></div>
            </div>

            <!-- 热门课程 -->
            <div class="chart-container">
              <div class="chart-title">热门课程TOP10</div>
              <div ref="popularCoursesChart" class="chart"></div>
            </div>

            <!-- 用户订单分布 -->
            <div class="chart-container">
              <div class="chart-title">用户订单分布</div>
              <div ref="userOrderChart" class="chart"></div>
            </div>
          </div>
        </ElTabPane>

        <ElTabPane label="收入分析" name="revenue">
          <div class="grid grid-cols-1 gap-16px lg:grid-cols-2">
            <!-- 收入趋势 -->
            <div class="chart-container lg:col-span-2">
              <div class="chart-title">收入趋势</div>
              <div ref="revenueTrendChart" class="chart"></div>
            </div>

            <!-- 收入来源 -->
            <div class="chart-container">
              <div class="chart-title">收入来源分析</div>
              <div ref="revenueSourceChart" class="chart"></div>
            </div>

            <!-- 用户贡献 -->
            <div class="chart-container">
              <div class="chart-title">用户贡献TOP20</div>
              <div ref="userContributionChart" class="chart"></div>
            </div>
          </div>
        </ElTabPane>
      </ElTabs>
    </ElCard>
  </div>
</template>

<style scoped>
.stat-card {
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.overview-section {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.stat-item .label {
  font-size: 12px;
  color: #909399;
}

.stat-item .value {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.chart-container {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: white;
}

.chart-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
  text-align: center;
}

.chart {
  height: 300px;
  width: 100%;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.gap-16px {
  gap: 16px;
}
</style>
