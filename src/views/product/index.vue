<script setup lang="ts">
import { computed, onMounted, onUnmounted, reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  autoSetSort,
  batchDeleteProducts,
  batchUpdateProducts,
  createProduct,
  deleteProduct,
  fetchProductCategories,
  fetchProductList,
  updateProduct
} from '@/service/api/product';

defineOptions({
  name: 'ProductManage'
});

// 响应式数据
const loading = ref(false);
const submitting = ref(false);
const batchProcessing = ref(false);
const tableData = ref<any[]>([]);
const categories = ref<any[]>([]);
const providers = ref<any[]>([]);
const selectedProducts = ref<any[]>([]);

// 移动端检测
const isMobile = ref(false);
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

// 批量操作相关
const showBatchDialog = ref(false);
const batchForm = reactive({
  operationType: 'price',
  // 价格相关
  priceMode: 'set',
  fixedPrice: 0,
  priceMultiplier: 1.0,
  priceAdjustment: 0,
  // 分类相关
  categoryId: undefined as number | undefined,
  // 排序相关
  sortOrder: 10,
  // 状态相关
  status: 1,
  // 前缀相关
  prefixMode: 'add',
  prefixText: '',
  oldPrefix: '',
  // 删除确认
  confirmDelete: false
});

// 一键排序相关
const showSortDialog = ref(false);
const sortProcessing = ref(false);
const sortForm = reactive({
  scope: 'all',
  categoryId: undefined as number | undefined,
  sortType: 'by_cid_asc'
});

// 表单折叠面板
const activeCollapse = ref(['advanced']);

// 搜索表单
const searchFormRef = ref();
const searchForm = reactive({
  keyword: '',
  categoryId: 0,
  platformType: '',
  status: undefined as number | undefined
});

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
});

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref<'create' | 'edit' | 'view'>('create');
const dialogTitle = computed(() => {
  const titles = {
    create: '新增商品',
    edit: '编辑商品',
    view: '查看商品'
  };
  return titles[dialogMode.value];
});

// 表单数据
const formRef = ref();
const formData = reactive({
  cid: 0,
  name: '',
  fenlei: 0,
  queryplat: 0,
  price: 0,
  content: '',
  getnoun: '',
  noun: '',
  docking: '',
  status: 1,
  sort: 10,
  wck: 0,
  kcid: 1,
  api: 1,
  nocheck: 0
});

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
  fenlei: [{ required: true, message: '请选择商品分类', trigger: 'change' }],
  queryplat: [{ required: true, message: '请选择服务商', trigger: 'change' }],
  price: [{ required: true, message: '请输入销售价格', trigger: 'blur' }]
};

// 方法
const fetchTableData = async () => {
  try {
    loading.value = true;
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      keyword: searchForm.keyword || undefined,
      categoryId: searchForm.categoryId || undefined,
      platformType: searchForm.platformType || undefined,
      status: searchForm.status
    };

    const response = await fetchProductList(params);
    tableData.value = response.list || [];
    pagination.total = response.total || 0;
  } catch (error) {
    ElMessage.error('获取商品列表失败');
  } finally {
    loading.value = false;
  }
};

const loadCategories = async () => {
  try {
    const response = await fetchProductCategories();
    categories.value = response || [];
  } catch (error) {
    ElMessage.error('获取分类列表失败');
  }
};

const loadProviders = async () => {
  try {
    // 临时使用固定的服务商数据，后续可以从API获取
    providers.value = [{ provider_id: 1, name: '29平台' }];
  } catch (error) {
    ElMessage.error('获取服务商列表失败');
  }
};

const handleSearch = () => {
  pagination.page = 1;
  fetchTableData();
};

const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    categoryId: 0,
    platformType: '',
    status: undefined
  });
  pagination.page = 1;
  fetchTableData();
};

const handleRefresh = () => {
  fetchTableData();
};

const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.page = 1;
  fetchTableData();
};

const handleCurrentChange = (page: number) => {
  pagination.page = page;
  fetchTableData();
};

// 选择变化处理
const handleSelectionChange = (selection: any[]) => {
  selectedProducts.value = selection;
};

// 排序变化处理（实时更新）
const handleSortChange = (row: any) => {
  // 防抖处理，避免频繁更新
  if (row.sortTimer) {
    clearTimeout(row.sortTimer);
  }
  row.sortTimer = setTimeout(() => {
    updateProductSort(row.cid, row.sort);
  }, 500);
};

// 排序失焦处理
const handleSortBlur = (row: any) => {
  if (row.sortTimer) {
    clearTimeout(row.sortTimer);
    updateProductSort(row.cid, row.sort);
  }
};

// 快速排序按钮
const handleQuickSort = (row: any) => {
  updateProductSort(row.cid, row.sort);
};

// 更新商品排序
const updateProductSort = async (productId: number, sortOrder: number) => {
  try {
    await updateProduct(productId, { sortOrder });
    ElMessage.success('排序更新成功');
  } catch (error) {
    ElMessage.error('排序更新失败');
  }
};

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString();
};

const resetFormData = () => {
  Object.assign(formData, {
    cid: 0,
    name: '',
    fenlei: 0,
    queryplat: 0,
    price: 0,
    content: '',
    getnoun: '',
    noun: '',
    docking: '',
    status: 1,
    sort: 10,
    wck: 0,
    kcid: 1,
    api: 1,
    nocheck: 0
  });
};

// 重置批量操作表单
const resetBatchForm = () => {
  Object.assign(batchForm, {
    operationType: 'price',
    priceMode: 'set',
    fixedPrice: 0,
    priceMultiplier: 1.0,
    priceAdjustment: 0,
    categoryId: null,
    sortOrder: 10,
    status: 1,
    prefixMode: 'add',
    prefixText: '',
    oldPrefix: '',
    confirmDelete: false
  });
};

const handleCreate = () => {
  dialogMode.value = 'create';
  resetFormData();
  dialogVisible.value = true;
};

// 批量操作处理
const handleBatchOperation = async () => {
  if (selectedProducts.value.length === 0) {
    ElMessage.warning('请先选择要操作的商品');
    return;
  }

  // 验证表单
  if (!validateBatchForm()) {
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要对 ${selectedProducts.value.length} 个商品执行${getBatchOperationName()}操作吗？`,
      '确认操作',
      { type: 'warning' }
    );

    batchProcessing.value = true;

    switch (batchForm.operationType) {
      case 'price':
        await handleBatchPrice();
        break;
      case 'category':
        await handleBatchCategory();
        break;
      case 'sort':
        await handleBatchSort();
        break;
      case 'status':
        await handleBatchStatus();
        break;
      case 'prefix':
        await handleBatchPrefix();
        break;
      case 'delete':
        await handleBatchDelete();
        break;
    }

    ElMessage.success('批量操作完成');
    showBatchDialog.value = false;
    selectedProducts.value = [];
    await fetchTableData();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '批量操作失败');
    }
  } finally {
    batchProcessing.value = false;
  }
};

const handleView = (row: any) => {
  dialogMode.value = 'view';
  Object.assign(formData, row);
  dialogVisible.value = true;
};

const handleEdit = (row: any) => {
  dialogMode.value = 'edit';

  // 数据映射，兼容新旧字段名
  Object.assign(formData, {
    cid: row.cid || row.product_id || 0,
    name: row.name || row.product_name || '',
    fenlei: row.fenlei || row.category_id || 0,
    queryplat: row.queryplat || row.provider_id || 0,
    price: row.price || 0,
    content: row.content || row.description || '',
    getnoun: row.getnoun || '',
    noun: row.noun || '',
    docking: row.docking || '',
    status: row.status || 1,
    sort: row.sort || 10,
    wck: row.wck || 0,
    kcid: row.kcid || 1,
    api: row.api || 1,
    nocheck: row.nocheck || 0
  });

  dialogVisible.value = true;
};

const handleToggleStatus = async (row: any) => {
  try {
    const action = row.status === 1 ? '禁用' : '启用';
    await ElMessageBox.confirm(`确定要${action}该商品吗？`, '确认操作', {
      type: 'warning'
    });

    await updateProduct(row.product_id, {
      status: row.status === 1 ? 0 : 1
    });

    ElMessage.success(`${action}成功`);
    fetchTableData();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败');
    }
  }
};

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除该商品吗？删除后不可恢复！', '确认删除', {
      type: 'error'
    });

    await deleteProduct(row.product_id);
    ElMessage.success('删除成功');
    fetchTableData();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};

const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    submitting.value = true;

    if (dialogMode.value === 'create') {
      await createProduct({
        productName: formData.name,
        categoryId: formData.fenlei,
        platformType: 'network_course',
        providerId: formData.queryplat,
        price: formData.price,
        costPrice: formData.price,
        serviceType: 'course',
        description: formData.content,
        status: formData.status,
        sortOrder: formData.sort
      });
      ElMessage.success('创建成功');
    } else {
      await updateProduct(formData.cid, {
        productName: formData.name,
        categoryId: formData.fenlei,
        platformType: 'network_course',
        providerId: formData.queryplat,
        price: formData.price,
        costPrice: formData.price,
        serviceType: 'course',
        description: formData.content,
        status: formData.status,
        sortOrder: formData.sort
      });
      ElMessage.success('更新成功');
    }

    dialogVisible.value = false;
    fetchTableData();
  } catch (error) {
    ElMessage.error('操作失败');
  } finally {
    submitting.value = false;
  }
};

// 验证批量操作表单
const validateBatchForm = () => {
  switch (batchForm.operationType) {
    case 'price':
      if (batchForm.priceMode === 'set' && (!batchForm.fixedPrice || batchForm.fixedPrice <= 0)) {
        ElMessage.warning('请输入有效的固定价格');
        return false;
      }
      if (batchForm.priceMode === 'multiply' && (!batchForm.priceMultiplier || batchForm.priceMultiplier <= 0)) {
        ElMessage.warning('请输入有效的价格倍数');
        return false;
      }
      break;
    case 'category':
      if (!batchForm.categoryId) {
        ElMessage.warning('请选择目标分类');
        return false;
      }
      break;
    case 'prefix':
      if (batchForm.prefixMode !== 'remove' && !batchForm.prefixText.trim()) {
        ElMessage.warning('请输入前缀内容');
        return false;
      }
      if (batchForm.prefixMode === 'replace' && !batchForm.oldPrefix.trim()) {
        ElMessage.warning('请输入要替换的旧前缀');
        return false;
      }
      break;
    case 'delete':
      if (!batchForm.confirmDelete) {
        ElMessage.warning('请确认删除操作');
        return false;
      }
      break;
  }
  return true;
};

// 获取批量操作名称
const getBatchOperationName = () => {
  const names: Record<string, string> = {
    price: '价格调整',
    category: '分类设置',
    sort: '排序设置',
    status: '状态设置',
    prefix: '前缀修改',
    delete: '删除'
  };
  return names[batchForm.operationType] || '未知操作';
};

// 批量价格操作
const handleBatchPrice = async () => {
  const productIds = selectedProducts.value.map(p => p.cid);
  const updateData: any = {};

  if (batchForm.priceMode === 'set') {
    updateData.price = batchForm.fixedPrice;
    await batchUpdateProducts({ productIds, updateData });
  } else {
    // 对于倍数和增减操作，需要逐个处理
    for (const product of selectedProducts.value) {
      let newPrice = product.price;

      switch (batchForm.priceMode) {
        case 'multiply':
          newPrice = product.price * batchForm.priceMultiplier;
          break;
        case 'add':
          newPrice = product.price + batchForm.priceAdjustment;
          break;
      }

      // 确保价格不为负数
      newPrice = Math.max(0.01, newPrice);

      await updateProduct(product.cid, { price: newPrice });
    }
  }
};

// 批量分类操作
const handleBatchCategory = async () => {
  const productIds = selectedProducts.value.map(p => p.cid);
  const updateData = { fenlei: batchForm.categoryId };
  await batchUpdateProducts({ productIds, updateData });
};

// 批量排序操作
const handleBatchSort = async () => {
  const productIds = selectedProducts.value.map(p => p.cid);
  const updateData = { sort: batchForm.sortOrder };
  await batchUpdateProducts({ productIds, updateData });
};

// 批量状态操作
const handleBatchStatus = async () => {
  const productIds = selectedProducts.value.map(p => p.cid);
  const updateData = { status: batchForm.status };
  await batchUpdateProducts({ productIds, updateData });
};

// 批量前缀操作
const handleBatchPrefix = async () => {
  for (const product of selectedProducts.value) {
    let newName = product.name;

    switch (batchForm.prefixMode) {
      case 'add':
        newName = batchForm.prefixText + newName;
        break;
      case 'replace':
        if (newName.startsWith(batchForm.oldPrefix)) {
          newName = batchForm.prefixText + newName.substring(batchForm.oldPrefix.length);
        }
        break;
      case 'remove':
        // 移除常见前缀
        const commonPrefixes = ['【', '[', '(', '（'];
        for (const prefix of commonPrefixes) {
          if (newName.startsWith(prefix)) {
            const endChar = prefix === '【' ? '】' : prefix === '[' ? ']' : prefix === '(' ? ')' : '）';
            const endIndex = newName.indexOf(endChar);
            if (endIndex > 0) {
              newName = newName.substring(endIndex + 1).trim();
              break;
            }
          }
        }
        break;
    }

    await updateProduct(product.cid, { productName: newName });
  }
};

// 批量删除操作
const handleBatchDelete = async () => {
  const productIds = selectedProducts.value.map(p => p.cid);
  await batchDeleteProducts(productIds);
};

// 重置排序表单
const resetSortForm = () => {
  Object.assign(sortForm, {
    scope: 'all',
    categoryId: null,
    sortType: 'by_cid_asc'
  });
};

// 获取排序说明
const getSortDescription = () => {
  const descriptions: Record<string, string> = {
    by_cid_asc: '将商品按ID从小到大重新排序，ID越小排序值越小',
    by_cid_desc: '将商品按ID从大到小重新排序，ID越大排序值越小',
    by_name_asc: '将商品按名称字母顺序A-Z重新排序',
    by_price_asc: '将商品按价格从低到高重新排序，价格越低排序值越小',
    by_price_desc: '将商品按价格从高到低重新排序，价格越高排序值越小',
    reset_default: '将所有商品的排序值重置为默认值10'
  };
  return descriptions[sortForm.sortType] || '请选择排序方式';
};

// 一键排序处理
const handleAutoSort = async () => {
  try {
    // 验证表单
    if (sortForm.scope === 'category' && !sortForm.categoryId) {
      ElMessage.warning('请选择分类');
      return;
    }

    await ElMessageBox.confirm(
      `确定要执行${getSortDescription()}吗？此操作将影响${sortForm.scope === 'all' ? '所有' : '指定分类的'}商品排序。`,
      '确认排序',
      { type: 'warning' }
    );

    sortProcessing.value = true;

    const data: any = {
      sortType: sortForm.sortType
    };

    if (sortForm.scope === 'category') {
      data.categoryId = sortForm.categoryId;
    }

    const response = await autoSetSort(data);

    ElMessage.success(response.msg || '排序设置成功');
    showSortDialog.value = false;

    // 刷新商品列表
    await fetchTableData();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '排序设置失败');
    }
  } finally {
    sortProcessing.value = false;
  }
};

// 初始化
onMounted(async () => {
  checkMobile();
  window.addEventListener('resize', checkMobile);
  await loadCategories();
  await loadProviders();
  await fetchTableData();
});

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile);
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <!-- 页面标题 -->
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-16px">
            <span class="text-lg font-medium">商品管理</span>
            <div v-if="tableData.length > 0" class="flex gap-12px text-sm text-gray-600">
              <span>总商品: {{ pagination.total }}</span>
              <span>已选择: {{ selectedProducts.length }}</span>
            </div>
          </div>
          <div class="flex gap-8px">
            <ElButton type="primary" @click="handleCreate">
              <template #icon>
                <icon-ic-round-add class="text-icon" />
              </template>
              新增商品
            </ElButton>
            <ElButton v-if="selectedProducts.length > 0" type="warning" @click="showBatchDialog = true">
              <template #icon>
                <icon-ic-round-edit class="text-icon" />
              </template>
              批量操作 ({{ selectedProducts.length }})
            </ElButton>
            <ElButton type="success" @click="showSortDialog = true">
              <template #icon>
                <icon-ic-round-sort class="text-icon" />
              </template>
              一键排序
            </ElButton>
            <ElButton @click="handleRefresh">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              刷新
            </ElButton>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <ElForm ref="searchFormRef" :model="searchForm" label-width="100px" class="mb-16px">
        <div class="grid grid-cols-1 gap-16px lg:grid-cols-4 md:grid-cols-2">
          <ElFormItem label="商品名称">
            <ElInput v-model="searchForm.keyword" placeholder="请输入商品名称" clearable />
          </ElFormItem>

          <ElFormItem label="商品分类">
            <ElSelect v-model="searchForm.categoryId" placeholder="选择商品分类" clearable class="w-full">
              <ElOption label="全部分类" :value="0" />
              <ElOption
                v-for="category in categories"
                :key="category.category_id"
                :label="category.name"
                :value="category.category_id"
              />
            </ElSelect>
          </ElFormItem>

          <ElFormItem label="平台类型">
            <ElInput v-model="searchForm.platformType" placeholder="请输入平台类型" clearable />
          </ElFormItem>

          <ElFormItem label="商品状态">
            <ElSelect v-model="searchForm.status" placeholder="选择商品状态" clearable class="w-full">
              <ElOption label="全部状态" value="" />
              <ElOption label="启用" :value="1" />
              <ElOption label="禁用" :value="0" />
            </ElSelect>
          </ElFormItem>
        </div>

        <div class="flex justify-end gap-8px">
          <ElButton @click="handleReset">重置</ElButton>
          <ElButton type="primary" @click="handleSearch">搜索</ElButton>
        </div>
      </ElForm>
    </ElCard>

    <!-- 商品列表 -->
    <ElCard>
      <template #header>
        <span class="font-medium">商品列表</span>
      </template>

      <div class="w-full overflow-x-auto">
        <ElTable
          v-loading="loading"
          :data="tableData"
          class="min-w-1000px w-full"
          max-height="70vh"
          stripe
          empty-text="暂无商品数据"
          :size="isMobile ? 'small' : 'default'"
          @sort-change="handleSortChange"
          @selection-change="handleSelectionChange"
        >
          <ElTableColumn type="selection" width="55" />
          <ElTableColumn prop="cid" label="ID" width="80">
            <template #default="{ row }">
              {{ row.cid || row.product_id }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="name" label="商品名称" min-width="200">
            <template #default="{ row }">
              <div class="flex flex-col">
                <span class="font-medium">{{ row.name || row.product_name }}</span>
                <span v-if="row.content || row.description" class="truncate text-xs text-gray-500">
                  {{ row.content || row.description }}
                </span>
              </div>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="fenlei" label="分类" width="120">
            <template #default="{ row }">
              <ElTag v-if="row.category_name" size="small" type="info">
                {{ row.category_name }}
              </ElTag>
              <ElTag v-else size="small" type="warning">ID: {{ row.fenlei || row.category_id }}</ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="queryplat" label="服务商" width="120">
            <template #default="{ row }">
              <ElTag v-if="row.provider_name" size="small" type="success">
                {{ row.provider_name }}
              </ElTag>
              <ElTag v-else size="small" type="warning">ID: {{ row.queryplat || row.provider_id }}</ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="getnoun" label="源台CID" width="120">
            <template #default="{ row }">
              <ElTag v-if="row.getnoun || row.noun" size="small" type="success">
                {{ row.getnoun || row.noun }}
              </ElTag>
              <span v-else class="text-gray-400">-</span>
            </template>
          </ElTableColumn>
          <ElTableColumn label="29平台同步" width="100">
            <template #default="{ row }">
              <ElTag :type="row.queryplat === '3' || row.provider_id === '3' ? 'success' : 'info'" size="small">
                {{ row.queryplat === '3' || row.provider_id === '3' ? '已同步' : '未同步' }}
              </ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="price" label="价格" width="100" sortable="custom">
            <template #default="{ row }">
              <span class="text-primary font-medium">¥{{ row.price }}</span>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="status" label="状态" width="80">
            <template #default="{ row }">
              <ElTag :type="row.status === 1 ? 'success' : 'danger'" size="small">
                {{ row.status === 1 ? '启用' : '禁用' }}
              </ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="sort" label="排序" width="100" sortable="custom">
            <template #default="{ row }">
              <div class="flex items-center gap-4px">
                <ElInputNumber
                  v-model="row.sort"
                  :min="1"
                  :max="999"
                  size="small"
                  style="width: 65px"
                  :controls="false"
                  @change="handleSortChange(row)"
                  @blur="handleSortBlur(row)"
                />
                <ElButton size="small" type="primary" text title="快速设置排序" @click="handleQuickSort(row)">
                  <icon-ic-round-check class="text-12px" />
                </ElButton>
              </div>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="addtime" label="创建时间" width="180" sortable="custom">
            <template #default="{ row }">
              {{ formatDateTime(row.addtime || row.create_time) }}
            </template>
          </ElTableColumn>
          <ElTableColumn label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <div class="flex flex-wrap gap-4px">
                <ElButton size="small" type="primary" @click="handleEdit(row)">编辑</ElButton>
                <ElButton
                  size="small"
                  :type="row.status === 1 ? 'warning' : 'success'"
                  @click="handleToggleStatus(row)"
                >
                  {{ row.status === 1 ? '禁用' : '启用' }}
                </ElButton>
                <ElButton size="small" type="danger" @click="handleDelete(row)">删除</ElButton>
              </div>
            </template>
          </ElTableColumn>
        </ElTable>
      </div>

      <!-- 分页 -->
      <div class="mt-16px flex justify-center">
        <ElPagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </ElCard>

    <!-- 批量操作对话框 -->
    <ElDialog v-model="showBatchDialog" title="批量操作" width="600px" @close="resetBatchForm">
      <ElForm :model="batchForm" label-width="120px">
        <ElFormItem label="操作类型">
          <ElRadioGroup v-model="batchForm.operationType">
            <ElRadio value="price">批量设置价格</ElRadio>
            <ElRadio value="category">批量设置分类</ElRadio>
            <ElRadio value="sort">批量设置排序</ElRadio>
            <ElRadio value="status">批量上下架</ElRadio>
            <ElRadio value="prefix">修改商品名称前缀</ElRadio>
            <ElRadio value="delete">批量删除</ElRadio>
          </ElRadioGroup>
        </ElFormItem>

        <!-- 价格设置 -->
        <ElFormItem v-if="batchForm.operationType === 'price'" label="价格操作">
          <div class="space-y-12px">
            <ElRadioGroup v-model="batchForm.priceMode">
              <ElRadio value="set">设置固定价格</ElRadio>
              <ElRadio value="multiply">价格倍数调整</ElRadio>
              <ElRadio value="add">价格增减调整</ElRadio>
            </ElRadioGroup>

            <div v-if="batchForm.priceMode === 'set'">
              <ElInputNumber
                v-model="batchForm.fixedPrice"
                :min="0.01"
                :precision="2"
                placeholder="输入固定价格"
                style="width: 200px"
              />
              <span class="ml-8px text-sm text-gray-500">元</span>
            </div>

            <div v-if="batchForm.priceMode === 'multiply'">
              <ElInputNumber
                v-model="batchForm.priceMultiplier"
                :min="0.1"
                :max="10"
                :step="0.1"
                :precision="1"
                placeholder="输入倍数"
                style="width: 200px"
              />
              <span class="ml-8px text-sm text-gray-500">倍（当前价格 × 倍数）</span>
            </div>

            <div v-if="batchForm.priceMode === 'add'">
              <ElInputNumber
                v-model="batchForm.priceAdjustment"
                :precision="2"
                placeholder="输入调整金额"
                style="width: 200px"
              />
              <span class="ml-8px text-sm text-gray-500">元（正数增加，负数减少）</span>
            </div>
          </div>
        </ElFormItem>

        <!-- 分类设置 -->
        <ElFormItem v-if="batchForm.operationType === 'category'" label="目标分类">
          <ElSelect v-model="batchForm.categoryId" placeholder="请选择分类" filterable style="width: 300px">
            <ElOption
              v-for="category in categories"
              :key="category.category_id"
              :label="category.name"
              :value="category.category_id"
            />
          </ElSelect>
        </ElFormItem>

        <!-- 排序设置 -->
        <ElFormItem v-if="batchForm.operationType === 'sort'" label="排序值">
          <ElInputNumber v-model="batchForm.sortOrder" :min="1" :max="999" style="width: 200px" />
          <span class="ml-8px text-sm text-gray-500">数值越小排序越靠前</span>
        </ElFormItem>

        <!-- 状态设置 -->
        <ElFormItem v-if="batchForm.operationType === 'status'" label="商品状态">
          <ElRadioGroup v-model="batchForm.status">
            <ElRadio :value="1">启用</ElRadio>
            <ElRadio :value="0">禁用</ElRadio>
          </ElRadioGroup>
        </ElFormItem>

        <!-- 前缀设置 -->
        <ElFormItem v-if="batchForm.operationType === 'prefix'" label="前缀操作">
          <div class="space-y-12px">
            <ElRadioGroup v-model="batchForm.prefixMode">
              <ElRadio value="add">添加前缀</ElRadio>
              <ElRadio value="replace">替换前缀</ElRadio>
              <ElRadio value="remove">移除前缀</ElRadio>
            </ElRadioGroup>

            <div v-if="batchForm.prefixMode !== 'remove'">
              <ElInput
                v-model="batchForm.prefixText"
                placeholder="输入前缀内容"
                style="width: 300px"
                maxlength="50"
                show-word-limit
              />
            </div>

            <div v-if="batchForm.prefixMode === 'replace'">
              <ElInput
                v-model="batchForm.oldPrefix"
                placeholder="输入要替换的旧前缀"
                style="width: 300px"
                maxlength="50"
              />
            </div>
          </div>
        </ElFormItem>

        <!-- 删除确认 -->
        <ElFormItem v-if="batchForm.operationType === 'delete'" label="删除确认">
          <ElAlert
            title="危险操作"
            description="批量删除操作不可恢复，请确认要删除选中的商品"
            type="error"
            :closable="false"
          />
          <ElCheckbox v-model="batchForm.confirmDelete" class="mt-12px">我确认要删除这些商品</ElCheckbox>
        </ElFormItem>

        <ElFormItem label="影响商品">
          <ElTag type="info">{{ selectedProducts.length }} 个商品</ElTag>
        </ElFormItem>
      </ElForm>

      <template #footer>
        <div class="flex justify-end gap-8px">
          <ElButton @click="showBatchDialog = false">取消</ElButton>
          <ElButton type="primary" :loading="batchProcessing" @click="handleBatchOperation">执行操作</ElButton>
        </div>
      </template>
    </ElDialog>

    <!-- 一键排序对话框 -->
    <ElDialog v-model="showSortDialog" title="一键设置排序" width="500px" @close="resetSortForm">
      <ElForm :model="sortForm" label-width="120px">
        <ElFormItem label="排序范围">
          <ElRadioGroup v-model="sortForm.scope">
            <ElRadio value="all">全部商品</ElRadio>
            <ElRadio value="category">指定分类</ElRadio>
          </ElRadioGroup>
        </ElFormItem>

        <ElFormItem v-if="sortForm.scope === 'category'" label="选择分类">
          <ElSelect v-model="sortForm.categoryId" placeholder="请选择分类" filterable style="width: 300px">
            <ElOption
              v-for="category in categories"
              :key="category.category_id"
              :label="category.name"
              :value="category.category_id"
            />
          </ElSelect>
        </ElFormItem>

        <ElFormItem label="排序方式">
          <ElRadioGroup v-model="sortForm.sortType">
            <div class="space-y-8px">
              <div><ElRadio value="by_cid_asc">按商品ID从小到大</ElRadio></div>
              <div><ElRadio value="by_cid_desc">按商品ID从大到小</ElRadio></div>
              <div><ElRadio value="by_name_asc">按商品名称A-Z排序</ElRadio></div>
              <div><ElRadio value="by_price_asc">按价格从低到高</ElRadio></div>
              <div><ElRadio value="by_price_desc">按价格从高到低</ElRadio></div>
              <div><ElRadio value="reset_default">重置为默认排序(10)</ElRadio></div>
            </div>
          </ElRadioGroup>
        </ElFormItem>

        <ElFormItem label="排序说明">
          <ElAlert :title="getSortDescription()" type="info" :closable="false" show-icon />
        </ElFormItem>
      </ElForm>

      <template #footer>
        <div class="flex justify-end gap-8px">
          <ElButton @click="showSortDialog = false">取消</ElButton>
          <ElButton type="primary" :loading="sortProcessing" @click="handleAutoSort">执行排序</ElButton>
        </div>
      </template>
    </ElDialog>

    <!-- 商品详情/编辑对话框 -->
    <ElDialog v-model="dialogVisible" :title="dialogTitle" width="800px" :close-on-click-modal="false">
      <ElForm ref="formRef" :model="formData" :rules="formRules" label-width="120px" class="space-y-16px">
        <div class="grid grid-cols-1 gap-16px md:grid-cols-2">
          <ElFormItem label="商品名称" prop="name">
            <ElInput v-model="formData.name" placeholder="请输入商品名称" :disabled="dialogMode === 'view'" />
          </ElFormItem>

          <ElFormItem label="商品分类" prop="fenlei">
            <ElSelect
              v-model="formData.fenlei"
              placeholder="选择商品分类"
              class="w-full"
              :disabled="dialogMode === 'view'"
            >
              <ElOption
                v-for="category in categories"
                :key="category.category_id"
                :label="category.name"
                :value="category.category_id"
              />
            </ElSelect>
          </ElFormItem>

          <ElFormItem label="服务商" prop="queryplat">
            <ElSelect
              v-model="formData.queryplat"
              placeholder="选择服务商"
              class="w-full"
              :disabled="dialogMode === 'view'"
            >
              <ElOption
                v-for="provider in providers"
                :key="provider.provider_id"
                :label="provider.name"
                :value="provider.provider_id"
              />
            </ElSelect>
          </ElFormItem>

          <ElFormItem label="销售价格" prop="price">
            <ElInputNumber
              v-model="formData.price"
              :min="0"
              :precision="2"
              placeholder="请输入销售价格"
              class="w-full"
              :disabled="dialogMode === 'view'"
            />
          </ElFormItem>

          <ElFormItem label="销售价格" prop="price">
            <ElInputNumber
              v-model="formData.price"
              :min="0"
              :precision="2"
              placeholder="请输入销售价格"
              class="w-full"
              :disabled="dialogMode === 'view'"
            />
          </ElFormItem>

          <ElFormItem label="查询参数" prop="getnoun">
            <ElInput v-model="formData.getnoun" placeholder="29平台商品CID" :disabled="dialogMode === 'view'" />
          </ElFormItem>

          <ElFormItem label="对接参数" prop="noun">
            <ElInput v-model="formData.noun" placeholder="对接参数" :disabled="dialogMode === 'view'" />
          </ElFormItem>

          <ElFormItem label="排序权重" prop="sort">
            <ElInputNumber
              v-model="formData.sort"
              :min="1"
              :max="999"
              placeholder="排序权重"
              class="w-full"
              :disabled="dialogMode === 'view'"
            />
          </ElFormItem>

          <ElFormItem label="商品状态" prop="status">
            <ElRadioGroup v-model="formData.status" :disabled="dialogMode === 'view'">
              <ElRadio :value="1">启用</ElRadio>
              <ElRadio :value="0">禁用</ElRadio>
            </ElRadioGroup>
          </ElFormItem>
        </div>

        <ElFormItem label="商品描述" prop="content">
          <ElInput
            v-model="formData.content"
            type="textarea"
            :rows="3"
            placeholder="请输入商品描述"
            :disabled="dialogMode === 'view'"
          />
        </ElFormItem>

        <!-- 高级设置 -->
        <ElCollapse v-model="activeCollapse">
          <ElCollapseItem title="高级设置" name="advanced">
            <div class="grid grid-cols-1 gap-16px md:grid-cols-3">
              <ElFormItem label="无查开关" prop="wck">
                <ElRadioGroup v-model="formData.wck" :disabled="dialogMode === 'view'">
                  <ElRadio :value="0">否</ElRadio>
                  <ElRadio :value="1">是</ElRadio>
                </ElRadioGroup>
              </ElFormItem>

              <ElFormItem label="课程ID" prop="kcid">
                <ElInputNumber
                  v-model="formData.kcid"
                  :min="1"
                  placeholder="课程ID"
                  class="w-full"
                  :disabled="dialogMode === 'view'"
                />
              </ElFormItem>

              <ElFormItem label="API标识" prop="api">
                <ElInputNumber
                  v-model="formData.api"
                  :min="1"
                  placeholder="API标识"
                  class="w-full"
                  :disabled="dialogMode === 'view'"
                />
              </ElFormItem>

              <ElFormItem label="免检查" prop="nocheck">
                <ElRadioGroup v-model="formData.nocheck" :disabled="dialogMode === 'view'">
                  <ElRadio :value="0">否</ElRadio>
                  <ElRadio :value="1">是</ElRadio>
                </ElRadioGroup>
              </ElFormItem>

              <ElFormItem label="服务商" prop="docking">
                <ElInput v-model="formData.docking" placeholder="服务商" :disabled="dialogMode === 'view'" />
              </ElFormItem>
            </div>
          </ElCollapseItem>
        </ElCollapse>
      </ElForm>

      <template #footer>
        <div class="flex justify-end gap-8px">
          <ElButton @click="dialogVisible = false">取消</ElButton>
          <ElButton v-if="dialogMode !== 'view'" type="primary" :loading="submitting" @click="handleSubmit">
            确定
          </ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<style scoped>
/* 移动端适配 */
@media (max-width: 768px) {
  .flex.flex-col.gap-16px.p-16px {
    padding: 12px;
    gap: 12px;
  }
}
</style>
