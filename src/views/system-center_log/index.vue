<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { fetchLogList } from '@/service/api/log';
import { useTableOperate } from '@/hooks/common/table-pagination';
import { formatLogDetails, getActionDescription, getModuleDescription, getStatusInfo } from '@/utils/logger';

defineOptions({
  name: 'SystemCenterLog'
});

// 搜索表单
const searchForm = reactive({
  module: '',
  action: '',
  status: '',
  username: '',
  dateRange: undefined as [string, string] | undefined
});

// 表格操作
const { pagination, loading, tableData, handleSizeChange, handlePageChange, fetchTableData, refreshTable } =
  useTableOperate({
    fetchData: async params => {
      const response = await fetchLogList({
        ...params,
        ...searchForm,
        start_date: searchForm.dateRange?.[0],
        end_date: searchForm.dateRange?.[1]
      });
      return {
        data: response.list || [],
        total: response.total || 0
      };
    }
  });

// 日志详情
const detailVisible = ref(false);
const selectedLog = ref<any>(null);

// 搜索
function handleSearch() {
  pagination.page = 1;
  fetchTableData();
}

// 重置
function handleReset() {
  Object.assign(searchForm, {
    module: '',
    action: '',
    status: '',
    username: '',
    dateRange: undefined
  });
  handleSearch();
}

// 刷新
function handleRefresh() {
  refreshTable();
}

// 查看详情
function handleView(row: any) {
  selectedLog.value = row;
  detailVisible.value = true;
}

// 获取用户类型文本
function getUserTypeText(username: string): string {
  if (username === 'system') return '系统';
  if (username === 'admin') return '管理员';
  return '用户';
}

// 获取用户类型颜色
function getUserTypeColor(username: string): 'success' | 'info' | 'warning' | 'danger' | 'primary' {
  if (username === 'system') return 'info';
  if (username === 'admin') return 'danger';
  return 'success';
}

// 获取模块文本
function getModuleText(module: string): string {
  return getModuleDescription(module);
}

// 获取操作文本
function getActionText(action: string): string {
  return getActionDescription(action);
}

// 获取状态文本
function getStatusText(status: string): string {
  return getStatusInfo(status)?.text || status;
}

// 获取状态类型
function getStatusType(status: string): 'success' | 'info' | 'warning' | 'danger' | 'primary' {
  const type = getStatusInfo(status)?.type;
  // 确保返回值是 Element Plus 支持的类型
  if (['success', 'info', 'warning', 'danger', 'primary'].includes(type)) {
    return type as 'success' | 'info' | 'warning' | 'danger' | 'primary';
  }
  return 'info'; // 默认值
}

// 格式化时间
function formatDateTime(dateTime: string): string {
  if (!dateTime) return '-';
  return new Date(dateTime).toLocaleString('zh-CN');
}

// 初始化
onMounted(() => {
  fetchTableData();
});
</script>

<template>
  <div class="flex flex-col gap-16px">
    <!-- 页面标题和搜索 -->
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-4">
            <span class="text-lg font-medium">系统日志</span>
            <div v-if="tableData.length > 0" class="flex gap-3 text-sm text-gray-600">
              <span>总记录: {{ pagination.total }}</span>
            </div>
          </div>
          <div class="flex gap-2">
            <ElButton @click="handleRefresh">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              刷新
            </ElButton>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <ElForm ref="searchFormRef" :model="searchForm" label-width="100px" class="mb-16px">
        <div class="grid grid-cols-1 gap-16px lg:grid-cols-4 md:grid-cols-2">
          <ElFormItem label="操作模块">
            <ElSelect v-model="searchForm.module" placeholder="请选择模块" clearable>
              <ElOption label="用户管理" value="user" />
              <ElOption label="商品管理" value="product" />
              <ElOption label="订单管理" value="order" />
              <ElOption label="货源管理" value="provider" />
              <ElOption label="29平台对接" value="platform29" />
              <ElOption label="系统管理" value="system" />
              <ElOption label="授权管理" value="license" />
            </ElSelect>
          </ElFormItem>

          <ElFormItem label="操作类型">
            <ElInput v-model="searchForm.action" placeholder="请输入操作类型" clearable />
          </ElFormItem>

          <ElFormItem label="操作状态">
            <ElSelect v-model="searchForm.status" placeholder="请选择状态" clearable>
              <ElOption label="成功" value="success" />
              <ElOption label="失败" value="failed" />
              <ElOption label="处理中" value="pending" />
            </ElSelect>
          </ElFormItem>

          <ElFormItem label="操作者">
            <ElInput v-model="searchForm.username" placeholder="请输入操作者" clearable />
          </ElFormItem>

          <ElFormItem label="时间范围">
            <ElDatePicker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </ElFormItem>

          <ElFormItem>
            <div class="flex gap-8px">
              <ElButton type="primary" @click="handleSearch">
                <template #icon>
                  <icon-ic-round-search class="text-icon" />
                </template>
                搜索
              </ElButton>
              <ElButton @click="handleReset">
                <template #icon>
                  <icon-ic-round-refresh class="text-icon" />
                </template>
                重置
              </ElButton>
            </div>
          </ElFormItem>
        </div>
      </ElForm>
    </ElCard>

    <!-- 日志表格 -->
    <ElCard>
      <ElTable v-loading="loading" :data="tableData" class="w-full">
        <ElTableColumn prop="log_id" label="ID" width="80" />

        <ElTableColumn prop="username" label="操作者" width="120">
          <template #default="{ row }">
            <ElTag :type="getUserTypeColor(row.username)" size="small">
              {{ getUserTypeText(row.username) }}
            </ElTag>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="module" label="模块" width="120">
          <template #default="{ row }">
            <ElTag type="info" size="small">{{ getModuleText(row.module) }}</ElTag>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="action" label="操作" width="150">
          <template #default="{ row }">
            <span class="font-medium">{{ getActionText(row.action) }}</span>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="target_type" label="目标类型" width="120">
          <template #default="{ row }">
            <span v-if="row.target_type">{{ row.target_type }}</span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="target_id" label="目标ID" width="100">
          <template #default="{ row }">
            <span v-if="row.target_id">{{ row.target_id }}</span>
            <span v-else class="text-gray-400">-</span>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="status" label="状态" width="100">
          <template #default="{ row }">
            <ElTag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </ElTag>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="ip_address" label="IP地址" width="140">
          <template #default="{ row }">
            <span class="text-xs">{{ row.ip_address || '-' }}</span>
          </template>
        </ElTableColumn>

        <ElTableColumn prop="create_time" label="操作时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.create_time) }}
          </template>
        </ElTableColumn>

        <ElTableColumn label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <ElButton size="small" type="primary" @click="handleView(row)">详情</ElButton>
          </template>
        </ElTableColumn>
      </ElTable>

      <!-- 分页 -->
      <div class="mt-4 flex justify-center">
        <ElPagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </ElCard>

    <!-- 日志详情对话框 -->
    <ElDialog v-model="detailVisible" title="日志详情" width="800px">
      <div v-if="selectedLog" class="space-y-16px">
        <ElDescriptions :column="2" border>
          <ElDescriptionsItem label="日志ID">
            {{ selectedLog.log_id }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="操作者">
            <ElTag :type="getUserTypeColor(selectedLog.username)" size="small">
              {{ getUserTypeText(selectedLog.username) }}
            </ElTag>
          </ElDescriptionsItem>
          <ElDescriptionsItem label="操作模块">
            <ElTag type="info" size="small">{{ getModuleText(selectedLog.module) }}</ElTag>
          </ElDescriptionsItem>
          <ElDescriptionsItem label="操作类型">
            {{ getActionText(selectedLog.action) }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="目标类型">
            {{ selectedLog.target_type || '-' }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="目标ID">
            {{ selectedLog.target_id || '-' }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="操作状态">
            <ElTag :type="getStatusType(selectedLog.status)" size="small">
              {{ getStatusText(selectedLog.status) }}
            </ElTag>
          </ElDescriptionsItem>
          <ElDescriptionsItem label="IP地址">
            {{ selectedLog.ip_address || '-' }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="操作时间" :span="2">
            {{ formatDateTime(selectedLog.create_time) }}
          </ElDescriptionsItem>
        </ElDescriptions>

        <div v-if="selectedLog.details && Object.keys(selectedLog.details).length > 0">
          <h4 class="mb-8px text-lg font-medium">操作详情</h4>
          <ElCard class="bg-gray-50">
            <pre class="whitespace-pre-wrap text-sm">{{ formatLogDetails(selectedLog.details) }}</pre>
          </ElCard>
        </div>

        <div v-if="selectedLog.data_after && Object.keys(selectedLog.data_after).length > 0">
          <h4 class="mb-8px text-lg font-medium">操作后数据</h4>
          <ElCard class="bg-gray-50">
            <pre class="whitespace-pre-wrap text-sm">{{ formatLogDetails(selectedLog.data_after) }}</pre>
          </ElCard>
        </div>

        <div v-if="selectedLog.error_message">
          <h4 class="mb-8px text-lg text-red-600 font-medium">错误信息</h4>
          <ElAlert :title="selectedLog.error_message" type="error" :closable="false" />
        </div>

        <div v-if="selectedLog.user_agent">
          <h4 class="mb-8px text-lg font-medium">用户代理</h4>
          <ElCard class="bg-gray-50">
            <span class="text-sm">{{ selectedLog.user_agent }}</span>
          </ElCard>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end">
          <ElButton @click="detailVisible = false">关闭</ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<style scoped>
.el-table {
  width: 100%;
}

pre {
  font-family: 'Courier New', monospace;
  margin: 0;
  white-space: pre-wrap;
  word-break: break-all;
}
</style>
