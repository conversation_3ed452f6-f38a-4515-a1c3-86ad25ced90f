<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  CircleCheck,
  CircleClose,
  List,
  Plus,
  QuestionFilled,
  Refresh,
  Setting,
  VideoPause,
  VideoPlay,
  Warning
} from '@element-plus/icons-vue';
import { useSyncManagement } from '@/composables/useSyncManagement';

defineOptions({ name: 'SyncManagement' });

// 使用统一状态管理
const {
  config,
  status,
  orderStats,
  syncStats,
  // history, // 暂时不使用
  loading,
  autoRefresh,
  isRunning,
  isEnabled,
  needsAttention,
  loadAllData,
  saveConfig,
  validateConfig
} = useSyncManagement();

// 本地状态
const activeTab = ref('dashboard');

// 配置表单 - 使用响应式配置
const configForm = ref<any>({});

// 监听配置变化，同步到表单
watch(
  () => config.value,
  newConfig => {
    if (newConfig && Object.keys(newConfig).length > 0) {
      configForm.value = { ...newConfig };
    }
  },
  { immediate: true, deep: true }
);

// 策略管理已移除，使用统一配置管理

// 移除手动同步功能（订单页面已有）

// 计算属性 - 使用统一状态管理提供的计算属性
const healthStatus = computed(() => status.value.healthStatus || { status: 'unknown', issues: [] });
const queueSize = computed(() => status.value.queueStatus?.total || 0);

// 状态颜色映射
const statusColors: Record<string, 'success' | 'warning' | 'danger' | 'info' | 'primary'> = {
  healthy: 'success',
  warning: 'warning',
  critical: 'danger',
  unknown: 'info'
};

// 生命周期 - 使用统一状态管理
// 数据加载已由 useSyncManagement 自动处理

// 旧的数据加载函数已移除，使用统一状态管理

// 所有数据加载和自动刷新功能已移至统一状态管理

// 同步控制 - 已移除独立的启动/停止功能，统一通过配置管理

// 配置保存 - 使用统一状态管理
async function handleSaveConfig() {
  const success = await saveConfig(configForm.value);
  if (success) {
    // 配置保存成功，状态管理会自动刷新数据
  }
}

// 配置检查
async function checkConfiguration() {
  try {
    loading.value = true;
    console.log('🔍 [配置检查] 开始检查配置...');

    const { request } = await import('@/service/request');
    const response = await request({
      url: '/api/sync/config/check',
      method: 'get'
    });

    console.log('🔍 [配置检查] 收到响应:', response);
    console.log('🔍 [配置检查] 响应类型:', typeof response);
    console.log('🔍 [配置检查] 响应键:', response ? Object.keys(response) : 'null');

    if (response) {
      const { isConsistent, issues, recommendations, report } = response;
      console.log('🔍 [配置检查] 解构数据:', { isConsistent, issues, recommendations });

      // 显示检查结果对话框
      const messageType = isConsistent ? 'success' : issues.some((i: any) => i.type === 'error') ? 'error' : 'warning';

      await ElMessageBox.alert(
        `<div style="text-align: left; max-height: 400px; overflow-y: auto;">
          <h4>配置检查结果</h4>
          <p><strong>整体状态:</strong> ${isConsistent ? '✅ 配置一致' : '❌ 存在问题'}</p>
          <p><strong>问题数量:</strong> ${issues.length}</p>
          <p><strong>建议数量:</strong> ${recommendations.length}</p>

          ${
            issues.length > 0
              ? `
            <h5>🔍 发现的问题:</h5>
            <ul style="margin: 0; padding-left: 20px;">
              ${issues
                .map((issue: any) => {
                  const icon = issue.type === 'error' ? '❌' : issue.type === 'warning' ? '⚠️' : 'ℹ️';
                  return `<li>${icon} [${issue.category}] ${issue.message}</li>`;
                })
                .join('')}
            </ul>
          `
              : ''
          }

          ${
            recommendations.length > 0
              ? `
            <h5>💡 优化建议:</h5>
            <ul style="margin: 0; padding-left: 20px;">
              ${recommendations.map((rec: any) => `<li>${rec}</li>`).join('')}
            </ul>
          `
              : ''
          }
        </div>`,
        '配置检查报告',
        {
          dangerouslyUseHTMLString: true,
          type: messageType,
          confirmButtonText: '我知道了'
        }
      );

      console.log('🔍 [配置检查] 检查完成:', report);

      // 额外的成功提示
      ElMessage.success('配置检查完成！请查看弹窗详情。');
    } else {
      console.warn('🔍 [配置检查] 响应为空或格式不正确');
      ElMessage.warning('配置检查响应格式不正确');
    }
  } catch (error) {
    console.error('❌ [配置检查] 检查失败:', error);
    ElMessage.error(`配置检查失败: ${error instanceof Error ? error.message : '未知错误'}`);
  } finally {
    loading.value = false;
  }
}

// 添加订单状态
function addOrderStatus() {
  configForm.value.orderStatusConfigs.push({
    status: 1,
    name: '新状态',
    enabled: true,
    priority: 5,
    intervalMinutes: 60,
    description: '请修改状态描述'
  });
}

// 删除订单状态
function removeOrderStatus(index: number) {
  if (configForm.value.orderStatusConfigs.length <= 1) {
    ElMessage.warning('至少需要保留一个状态配置');
    return;
  }
  configForm.value.orderStatusConfigs.splice(index, 1);
}

// 手动同步功能已移除，使用订单页面的同步按钮

// 格式化数字
function formatNumber(num: number): string {
  return new Intl.NumberFormat('zh-CN').format(num);
}

// 格式化百分比
function formatPercent(num: number): string {
  return `${(num * 100).toFixed(1)}%`;
}

// 格式化时间
function formatDuration(ms: number): string {
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
  return `${(ms / 60000).toFixed(1)}min`;
}

// 获取状态名称
function getStatusName(statusCode: string | number): string {
  const statusMap: Record<string, string> = {
    '1': '待支付',
    '2': '待考试',
    '3': '进行中',
    '4': '已完成',
    '5': '已取消',
    '6': '处理失败'
  };
  return statusMap[statusCode.toString()] || `状态${statusCode}`;
}

// 获取状态颜色
function getStatusColor(status: string | number): string {
  const colorMap: Record<string, string> = {
    '1': 'bg-gray-400',
    '2': 'bg-yellow-500',
    '3': 'bg-blue-500',
    '4': 'bg-green-500',
    '5': 'bg-gray-500',
    '6': 'bg-red-500'
  };
  return colorMap[status.toString()] || 'bg-gray-400';
}
</script>

<template>
  <div class="sync-management">
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <h2 class="text-lg font-semibold">订单同步管理</h2>
          <div class="flex items-center gap-3">
            <ElSwitch v-model="autoRefresh" active-text="自动刷新" inactive-text="手动刷新" size="small" />
            <ElButton :loading="loading" size="small" @click="() => loadAllData()">刷新数据</ElButton>
          </div>
        </div>
      </template>

      <!-- 状态概览 -->
      <div class="mb-6">
        <div class="grid grid-cols-1 mb-4 gap-4 md:grid-cols-4">
          <ElCard class="status-card">
            <div class="flex items-center justify-between">
              <div>
                <div class="text-sm text-gray-500">同步状态</div>
                <div class="text-lg font-semibold">
                  <ElTag :type="isRunning ? 'success' : 'danger'" size="small">
                    {{ isRunning ? '运行中' : '已停止' }}
                  </ElTag>
                </div>
              </div>
              <ElIcon class="text-2xl text-blue-500">
                <VideoPlay v-if="isRunning" />
                <VideoPause v-else />
              </ElIcon>
            </div>
          </ElCard>

          <ElCard class="status-card">
            <div class="flex items-center justify-between">
              <div>
                <div class="text-sm text-gray-500">队列大小</div>
                <div class="text-lg font-semibold">{{ formatNumber(queueSize) }}</div>
              </div>
              <ElIcon class="text-2xl text-green-500">
                <List />
              </ElIcon>
            </div>
          </ElCard>

          <ElCard class="status-card">
            <div class="flex items-center justify-between">
              <div>
                <div class="text-sm text-gray-500">健康状态</div>
                <div class="text-lg font-semibold">
                  <ElTag :type="statusColors[healthStatus.status]" size="small">
                    {{
                      healthStatus.status === 'healthy'
                        ? '健康'
                        : healthStatus.status === 'warning'
                          ? '警告'
                          : healthStatus.status === 'critical'
                            ? '严重'
                            : '未知'
                    }}
                  </ElTag>
                </div>
              </div>
              <ElIcon
                class="text-2xl"
                :class="{
                  'text-green-500': healthStatus.status === 'healthy',
                  'text-yellow-500': healthStatus.status === 'warning',
                  'text-red-500': healthStatus.status === 'critical',
                  'text-gray-500': healthStatus.status === 'unknown'
                }"
              >
                <CircleCheck v-if="healthStatus.status === 'healthy'" />
                <Warning v-else-if="healthStatus.status === 'warning'" />
                <CircleClose v-else-if="healthStatus.status === 'critical'" />
                <QuestionFilled v-else />
              </ElIcon>
            </div>
          </ElCard>

          <ElCard class="status-card">
            <div class="flex items-center justify-between">
              <div>
                <div class="text-sm text-gray-500">错误率</div>
                <div class="text-lg font-semibold">
                  {{ status.realtimeStats ? formatPercent(status.realtimeStats.errorRate) : '0%' }}
                </div>
              </div>
              <ElIcon class="text-2xl text-orange-500">
                <Warning />
              </ElIcon>
            </div>
          </ElCard>
        </div>

        <!-- 配置提示 -->
        <div class="config-notice">
          <ElAlert
            :title="config.enabled ? '自动同步已启用' : '自动同步已禁用'"
            :type="config.enabled ? 'success' : 'warning'"
            :description="
              config.enabled
                ? '系统将根据配置的策略自动同步订单状态。如需修改设置，请前往配置管理页面。'
                : '自动同步功能已禁用。如需启用，请前往配置管理页面开启。'
            "
            show-icon
            :closable="false"
          />
          <div class="mt-3 flex gap-3">
            <ElButton type="primary" @click="activeTab = 'config'">
              <ElIcon><Setting /></ElIcon>
              前往配置管理
            </ElButton>
            <ElButton type="info" :loading="loading" @click="() => loadAllData()">
              <ElIcon><Refresh /></ElIcon>
              刷新数据
            </ElButton>
          </div>
        </div>
      </div>

      <!-- 标签页 -->
      <ElTabs v-model="activeTab">
        <!-- 仪表板 -->
        <ElTabPane label="仪表板" name="dashboard">
          <div class="dashboard-content">
            <!-- 订单统计概览 -->
            <div class="mb-6">
              <h3 class="text-md mb-3 font-semibold">订单统计概览</h3>
              <div class="grid grid-cols-2 mb-6 gap-4 md:grid-cols-4">
                <ElCard class="stat-card">
                  <div class="text-center">
                    <div class="text-2xl text-blue-600 font-bold">{{ orderStats.totalOrders || 0 }}</div>
                    <div class="mt-1 text-sm text-gray-500">总订单数</div>
                  </div>
                </ElCard>
                <ElCard class="stat-card">
                  <div class="text-center">
                    <div class="text-2xl text-green-600 font-bold">{{ orderStats.submittedOrders || 0 }}</div>
                    <div class="mt-1 text-sm text-gray-500">已提交订单</div>
                    <div class="text-xs text-gray-400">有upstream_order_id</div>
                  </div>
                </ElCard>
                <ElCard class="stat-card">
                  <div class="text-center">
                    <div class="text-2xl text-orange-600 font-bold">{{ orderStats.pendingSync || 0 }}</div>
                    <div class="mt-1 text-sm text-gray-500">待同步订单</div>
                    <div class="text-xs text-gray-400">需要处理的订单</div>
                  </div>
                </ElCard>
                <ElCard class="stat-card">
                  <div class="text-center">
                    <div class="text-2xl text-purple-600 font-bold">{{ syncStats.todaySync || 0 }}</div>
                    <div class="mt-1 text-sm text-gray-500">今日同步</div>
                    <div class="text-xs text-gray-400">今日处理数量</div>
                  </div>
                </ElCard>
              </div>
            </div>

            <!-- 订单状态分布 -->
            <div v-if="orderStats.statusDistribution" class="mb-6">
              <h3 class="text-md mb-3 font-semibold">订单状态分布</h3>
              <ElCard>
                <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div
                    v-for="(count, status) in orderStats.statusDistribution"
                    :key="status"
                    class="flex items-center justify-between rounded bg-gray-50 px-3 py-2"
                  >
                    <div class="flex flex-col">
                      <span class="text-sm font-medium">{{ getStatusName(status) }}</span>
                      <span class="text-xs text-gray-500">状态 {{ status }}</span>
                    </div>
                    <div class="flex items-center">
                      <div class="mr-2 h-2 w-16 rounded-full bg-gray-200">
                        <div
                          class="h-2 rounded-full"
                          :class="getStatusColor(status)"
                          :style="{ width: `${Math.min((count / (orderStats.totalOrders || 1)) * 100, 100)}%` }"
                        ></div>
                      </div>
                      <span class="min-w-2rem text-right text-sm font-semibold">{{ count }}</span>
                    </div>
                  </div>
                </div>
              </ElCard>
            </div>

            <!-- 同步性能统计 -->
            <div class="mb-6">
              <h3 class="text-md mb-3 font-semibold">同步性能统计</h3>
              <div class="grid grid-cols-2 gap-4 md:grid-cols-4">
                <ElCard class="stat-card">
                  <div class="text-center">
                    <div class="text-xl text-green-600 font-bold">
                      {{ ((syncStats.successRate || 0) * 100).toFixed(1) }}%
                    </div>
                    <div class="mt-1 text-sm text-gray-500">成功率</div>
                    <div class="text-xs text-gray-400">最近24小时</div>
                  </div>
                </ElCard>
                <ElCard class="stat-card">
                  <div class="text-center">
                    <div class="text-xl text-red-600 font-bold">
                      {{ ((syncStats.errorRate || 0) * 100).toFixed(1) }}%
                    </div>
                    <div class="mt-1 text-sm text-gray-500">错误率</div>
                    <div class="text-xs text-gray-400">最近24小时</div>
                  </div>
                </ElCard>
                <ElCard class="stat-card">
                  <div class="text-center">
                    <div class="text-xl text-purple-600 font-bold">{{ syncStats.avgProcessingTime || 0 }}ms</div>
                    <div class="mt-1 text-sm text-gray-500">平均耗时</div>
                    <div class="text-xs text-gray-400">处理时间</div>
                  </div>
                </ElCard>
                <ElCard class="stat-card">
                  <div class="text-center">
                    <div class="text-xl text-blue-600 font-bold">{{ (syncStats.throughput || 0).toFixed(2) }}</div>
                    <div class="mt-1 text-sm text-gray-500">吞吐量</div>
                    <div class="text-xs text-gray-400">订单/分钟</div>
                  </div>
                </ElCard>
              </div>
            </div>

            <!-- 健康问题 -->
            <div v-if="healthStatus.issues.length > 0" class="mb-6">
              <h3 class="text-md mb-3 font-semibold">健康问题</h3>
              <ElAlert
                v-for="issue in healthStatus.issues"
                :key="issue"
                :title="issue"
                :type="healthStatus.status === 'critical' ? 'error' : 'warning'"
                :closable="false"
                class="mb-2"
              />
            </div>
          </div>
        </ElTabPane>

        <!-- 配置管理 -->
        <ElTabPane label="配置管理" name="config">
          <!-- 配置说明 -->
          <ElAlert title="统一配置控制" type="info" :closable="false" show-icon class="mb-4">
            <template #default>
              <p>通过此页面统一管理自动同步的所有配置。修改配置后点击"保存配置"，系统将自动应用新设置。</p>
              <ul class="mt-2 text-sm">
                <li>
                  •
                  <strong>启用自动同步</strong>
                  ：控制整个自动同步功能的开启/关闭
                </li>
                <li>
                  •
                  <strong>服务器启动时自动开启</strong>
                  ：服务器重启后是否自动启动同步
                </li>
                <li>• 配置保存后，运行中的调度器将自动重启以应用新配置</li>
              </ul>
            </template>
          </ElAlert>

          <ElForm :model="configForm" label-width="140px">
            <!-- 基础配置 -->
            <ElCard class="mb-4">
              <template #header>
                <span class="font-semibold">基础配置</span>
              </template>
              <ElRow :gutter="20">
                <ElCol :span="12">
                  <ElFormItem label="启用自动同步">
                    <ElSwitch v-model="configForm.enabled" />
                  </ElFormItem>
                  <ElFormItem label="服务器启动时自动开启">
                    <ElSwitch v-model="configForm.autoStartOnBoot" />
                  </ElFormItem>
                  <ElFormItem label="最大并发数">
                    <ElInputNumber v-model="configForm.maxConcurrency" :min="1" :max="50" />
                  </ElFormItem>
                  <ElFormItem label="批处理大小">
                    <ElInputNumber v-model="configForm.batchSize" :min="10" :max="500" />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem label="启用速率限制">
                    <ElSwitch v-model="configForm.enableRateLimit" />
                    <div class="text-xs text-gray-500">控制每秒最大请求数，避免过于频繁的API调用</div>
                  </ElFormItem>
                  <ElFormItem label="速率限制(/秒)" :class="{ 'is-disabled': !configForm.enableRateLimit }">
                    <ElInputNumber
                      v-model="configForm.rateLimit"
                      :min="1"
                      :max="100"
                      :disabled="!configForm.enableRateLimit"
                    />
                    <div class="text-xs text-gray-500">
                      当前值: {{ configForm.rateLimit || 'N/A' }} 请求/秒
                      {{ configForm.enableRateLimit ? '(已启用)' : '(已禁用)' }}
                    </div>
                  </ElFormItem>
                  <ElFormItem label="最大重试次数">
                    <ElInputNumber v-model="configForm.maxRetries" :min="1" :max="5" />
                  </ElFormItem>
                  <ElFormItem label="重试延迟(秒)">
                    <ElInputNumber v-model="configForm.retryDelaySeconds" :min="30" :max="300" />
                  </ElFormItem>
                </ElCol>
              </ElRow>
            </ElCard>

            <!-- 订单状态策略 -->
            <ElCard class="mb-4">
              <template #header>
                <div class="flex items-center justify-between">
                  <div>
                    <span class="font-semibold">订单状态同步策略</span>
                    <span class="ml-2 text-sm text-gray-500">只同步已提交的订单（有upstream_order_id）</span>
                  </div>
                  <ElButton type="primary" size="small" @click="addOrderStatus">
                    <ElIcon><Plus /></ElIcon>
                    添加状态
                  </ElButton>
                </div>
              </template>
              <ElTable :data="configForm.orderStatusConfigs" style="width: 100%">
                <ElTableColumn label="状态值" width="120">
                  <template #default="{ row }">
                    <ElInputNumber
                      v-model="row.status"
                      :min="1"
                      :max="99"
                      size="small"
                      class="status-input"
                      controls-position="right"
                    />
                  </template>
                </ElTableColumn>
                <ElTableColumn label="状态名称" width="140">
                  <template #default="{ row }">
                    <ElInput v-model="row.name" size="small" class="name-input" />
                  </template>
                </ElTableColumn>
                <ElTableColumn label="是否同步" width="100" align="center">
                  <template #default="{ row }">
                    <ElSwitch v-model="row.enabled" />
                  </template>
                </ElTableColumn>
                <ElTableColumn label="优先级" width="120">
                  <template #default="{ row }">
                    <ElInputNumber
                      v-model="row.priority"
                      :min="1"
                      :max="10"
                      size="small"
                      class="priority-input"
                      controls-position="right"
                    />
                  </template>
                </ElTableColumn>
                <ElTableColumn label="同步间隔(分钟)" width="160">
                  <template #default="{ row }">
                    <ElInputNumber
                      v-model="row.intervalMinutes"
                      :min="5"
                      :max="1440"
                      size="small"
                      class="interval-input"
                      controls-position="right"
                    />
                  </template>
                </ElTableColumn>
                <ElTableColumn label="说明">
                  <template #default="{ row }">
                    <ElInput v-model="row.description" size="small" />
                  </template>
                </ElTableColumn>
                <ElTableColumn label="操作" width="80">
                  <template #default="{ row, $index }">
                    <ElButton type="danger" size="small" @click="removeOrderStatus($index)">删除</ElButton>
                  </template>
                </ElTableColumn>
              </ElTable>

              <div class="mt-4 rounded bg-blue-50 p-3">
                <div class="text-sm text-blue-600">
                  <strong>说明：</strong>
                  <ul class="mt-2 space-y-1">
                    <li>• 只有已提交到上游平台的订单（有upstream_order_id）才会被同步</li>
                    <li>• 优先级越高（数字越大）的状态会优先处理</li>
                    <li>• 同步间隔控制该状态订单的同步频率，避免过于频繁的请求</li>
                    <li>• 可以根据实际业务需求添加自定义状态</li>
                  </ul>
                </div>
              </div>
            </ElCard>

            <!-- 优先级规则 -->
            <ElCard class="mb-4">
              <template #header>
                <span class="font-semibold">优先级规则</span>
                <span class="ml-2 text-sm text-gray-500">基于商品总订单量设置处理优先级</span>
              </template>
              <ElTable :data="configForm.priorityRules" style="width: 100%">
                <ElTableColumn prop="name" label="规则名称" width="150" />
                <ElTableColumn label="启用" width="80" align="center">
                  <template #default="{ row }">
                    <ElSwitch v-model="row.enabled" />
                  </template>
                </ElTableColumn>
                <ElTableColumn label="优先级" width="120">
                  <template #default="{ row }">
                    <ElInputNumber
                      v-model="row.priority"
                      :min="1"
                      :max="10"
                      size="small"
                      class="priority-input"
                      controls-position="right"
                    />
                  </template>
                </ElTableColumn>
                <ElTableColumn label="最小订单量" width="150">
                  <template #default="{ row }">
                    <ElInputNumber
                      v-model="row.minOrderCount"
                      :min="1"
                      :max="1000"
                      size="small"
                      class="amount-input"
                      controls-position="right"
                    />
                  </template>
                </ElTableColumn>
                <ElTableColumn prop="description" label="说明" />
              </ElTable>

              <div class="mt-4 rounded bg-blue-50 p-3">
                <div class="text-sm text-blue-600">
                  <strong>说明：</strong>
                  <ul class="mt-2 space-y-1">
                    <li>• 优先级规则基于商品的总订单量，热门商品优先处理</li>
                    <li>• 订单量≥50的热门商品获得最高优先级</li>
                    <li>• 订单量10-50的普通商品正常优先级</li>
                    <li>• 订单量1-10的新商品低优先级</li>
                    <li>• 优先级数字越大，处理顺序越靠前</li>
                  </ul>
                </div>
              </div>
            </ElCard>

            <!-- 时间限制 -->
            <ElCard class="mb-4">
              <template #header>
                <span class="font-semibold">时间限制</span>
              </template>
              <ElRow :gutter="20">
                <ElCol :span="12">
                  <ElFormItem label="最大订单年龄(小时)">
                    <ElInputNumber v-model="configForm.maxOrderAgeHours" :min="24" :max="720" />
                    <div class="text-xs text-gray-500">只同步指定时间内的订单</div>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem label="最小更新间隔(分钟)">
                    <ElInputNumber v-model="configForm.minUpdateIntervalMinutes" :min="5" :max="60" />
                    <div class="text-xs text-gray-500">避免过于频繁的更新</div>
                  </ElFormItem>
                </ElCol>
              </ElRow>
            </ElCard>

            <ElFormItem>
              <ElButton type="primary" :loading="loading" @click="handleSaveConfig">保存配置</ElButton>
              <ElButton :loading="loading" @click="() => loadAllData()">重置配置</ElButton>
              <ElButton type="warning" :loading="loading" @click="checkConfiguration">
                <ElIcon><Warning /></ElIcon>
                检查配置
              </ElButton>
              <div class="mt-2 text-xs text-gray-500">点击检查配置一致性、发现潜在问题并获得优化建议</div>
            </ElFormItem>
          </ElForm>
        </ElTabPane>

        <!-- 手动同步功能已移除 - 请使用订单页面的同步按钮 -->
        <ElTabPane label="使用说明" name="usage">
          <div class="usage-content">
            <ElAlert title="手动同步说明" type="info" :closable="false" class="mb-4">
              <template #default>
                <p>手动同步功能已集成到订单列表页面，您可以：</p>
                <ul class="ml-4 mt-2">
                  <li>• 在订单列表中选择单个或多个订单进行同步</li>
                  <li>• 使用批量操作功能同步多个订单</li>
                  <li>• 点击订单行的同步按钮进行单个同步</li>
                </ul>
              </template>
            </ElAlert>

            <ElCard class="mt-4">
              <template #header>
                <h3>自动同步配置建议</h3>
              </template>
              <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <h4 class="mb-2 font-semibold">高频同步场景</h4>
                  <ul class="text-sm text-gray-600">
                    <li>• 同步间隔：5分钟</li>
                    <li>• 批处理大小：100</li>
                    <li>• 最大并发：15</li>
                  </ul>
                </div>
                <div>
                  <h4 class="mb-2 font-semibold">稳定同步场景</h4>
                  <ul class="text-sm text-gray-600">
                    <li>• 同步间隔：10分钟</li>
                    <li>• 批处理大小：50</li>
                    <li>• 最大并发：10</li>
                  </ul>
                </div>
              </div>
            </ElCard>
          </div>
        </ElTabPane>
      </ElTabs>
    </ElCard>
  </div>
</template>

<style scoped>
.sync-management {
  padding: 20px;
}

.status-card {
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.stat-card {
  margin-bottom: 0;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.dashboard-content {
  min-height: 400px;
}

.grid {
  display: grid;
  gap: 1rem;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.text-md {
  font-size: 1rem;
}

.font-semibold {
  font-weight: 600;
}

.text-center {
  text-align: center;
}

.text-2xl {
  font-size: 1.5rem;
}

.font-bold {
  font-weight: 700;
}

.text-blue-600 {
  color: #2563eb;
}

.text-green-600 {
  color: #16a34a;
}

.text-orange-600 {
  color: #ea580c;
}

.text-purple-600 {
  color: #9333ea;
}

.text-gray-500 {
  color: #6b7280;
}

.text-gray-400 {
  color: #9ca3af;
}

.text-sm {
  font-size: 0.875rem;
}

.text-xs {
  font-size: 0.75rem;
}

.mt-1 {
  margin-top: 0.25rem;
}

.min-w-2rem {
  min-width: 2rem;
}

.text-right {
  text-align: right;
}

.w-16 {
  width: 4rem;
}

.bg-gray-200 {
  background-color: #e5e7eb;
}

.rounded-full {
  border-radius: 9999px;
}

.h-2 {
  height: 0.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.bg-blue-600 {
  background-color: #2563eb;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.rounded {
  border-radius: 0.25rem;
}

.font-medium {
  font-weight: 500;
}

.gap-4 {
  gap: 1rem;
}

/* 状态颜色 */
.bg-gray-400 {
  background-color: #9ca3af;
}

.bg-yellow-500 {
  background-color: #eab308;
}

.bg-blue-500 {
  background-color: #3b82f6;
}

.bg-green-500 {
  background-color: #22c55e;
}

.bg-gray-500 {
  background-color: #6b7280;
}

.bg-red-500 {
  background-color: #ef4444;
}

.flex-col {
  flex-direction: column;
}

/* 表格输入框样式 */
.status-input {
  width: 100px !important;
}

.name-input {
  width: 120px !important;
}

.priority-input {
  width: 100px !important;
}

.interval-input {
  width: 140px !important;
}

.amount-input {
  width: 130px !important;
}

/* 确保输入框不被压缩 */
.el-input-number {
  min-width: 80px;
}

.el-input {
  min-width: 80px;
}
</style>
