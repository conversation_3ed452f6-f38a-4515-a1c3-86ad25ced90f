import type { Ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

export function createCardActionHandlers(ctx: {
  openDetail: (row: any) => void;
  handleSyncOrder: (row: any) => Promise<void>;
  handleRefillOrder: (row: any) => Promise<void>;
  handleChangePassword: (row: any) => void;
  handleFeedback: (row: any) => void;
  handleCancelOrder: (row: any) => Promise<void>;
}) {
  return async function onCardMore(command: string, row: any) {
    switch (command) {
      case 'sync':
        await ctx.handleSyncOrder(row);
        break;
      case 'refill':
        await ctx.handleRefillOrder(row);
        break;
      case 'change-password':
        ctx.handleChangePassword(row);
        break;
      case 'feedback':
        ctx.handleFeedback(row);
        break;
      case 'view-courses':
        ctx.openDetail(row);
        break;
      case 'cancel':
        await ctx.handleCancelOrder(row);
        break;
      default:
        ElMessage.warning('未知操作');
    }
  };
}

