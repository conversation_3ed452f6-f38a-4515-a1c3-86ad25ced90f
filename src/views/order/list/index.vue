<script setup lang="ts">
import { h, onMounted, reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  ORDER_STATUS_TEXT,
  ORDER_STATUS_TYPE,
  OrderStatus,
  changeOrderPassword,
  feedbackOrder,
  fetchCancelOrder,
  fetchOrderDetail,
  fetchOrderList,
  refillOrder,
  syncOrderStatus
} from '@/service/api/order';
import { useIsMobile } from '@/composables/useIsMobile';
import { useTableOperate } from '@/hooks/common/table-pagination';
import FilterDrawer from '@/components/responsive/FilterDrawer.vue';
const { isMobile } = useIsMobile();

defineOptions({
  name: 'OrderManagement'
});

// 搜索参数
const searchParams = reactive({
  keyword: '',
  status: undefined as number | undefined,
  dateRange: undefined as [string, string] | undefined
});

// 使用表格操作Hook
const { pagination, loading, tableData, handlePageChange, handleSizeChange, fetchTableData, refreshTable } =
  useTableOperate({
    fetchData: async params => {
      const response = await fetchOrderList({
        ...params,
        ...searchParams,
        startTime: searchParams.dateRange?.[0],
        endTime: searchParams.dateRange?.[1]
      });
      return {
        data: response.list || [],
        total: response.total || 0
      };
    }
  });

// 响应式数据
const drawerVisible = ref(false);
const selectedOrder = ref<any>(null);

// 移动端检测（改为 useIsMobile）
// const isMobile = ref(false);
// const checkMobile = () => {
//   isMobile.value = window.innerWidth < 768;
// };

// 改密对话框
const changePasswordVisible = ref(false);
const changePasswordForm = reactive({
  order_id: 0,
  new_password: '',
  confirm_password: ''
});

// 反馈对话框
const feedbackVisible = ref(false);
const feedbackForm = reactive({
  order_id: 0,
  feedback_type: 'issue',
  feedback_content: '',
  attachments: [] as string[]
});

// 状态选项
const statusOptions = Object.entries(ORDER_STATUS_TEXT).map(([value, label]) => ({
  label,
  value: Number(value)
}));

// 工具函数
function getStatusType(status: number) {
  return ORDER_STATUS_TYPE[status as keyof typeof ORDER_STATUS_TYPE] || 'info';
}

function getStatusText(status: number): string {
  return ORDER_STATUS_TEXT[status as keyof typeof ORDER_STATUS_TEXT] || '未知状态';
}

// 格式化时间
function formatTime(timeStr: string): string {
  if (!timeStr) return '';
  const date = new Date(timeStr);
  const now = new Date();
  const diff = now.getTime() - date.getTime();

  // 如果是今天，显示时间
  if (diff < 24 * 60 * 60 * 1000 && date.getDate() === now.getDate()) {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  }

  // 如果是昨天，显示"昨天 HH:mm"
  const yesterday = new Date(now);
  yesterday.setDate(yesterday.getDate() - 1);
  if (date.getDate() === yesterday.getDate() && date.getMonth() === yesterday.getMonth()) {
    return `昨天 ${date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`;
  }

  // 其他情况显示日期
  return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit', hour: '2-digit', minute: '2-digit' });
}

// 格式化完整时间
function formatFullTime(timeStr: string): string {
  if (!timeStr) return '';
  const date = new Date(timeStr);

  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

// 获取相对时间
function getRelativeTime(timeStr: string): string {
  if (!timeStr) return '';
  const date = new Date(timeStr);
  const now = new Date();
  const diff = now.getTime() - date.getTime();

  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 30) return `${days}天前`;
  return '很久以前';
}

// 获取时间持续时长
function getTimeDuration(startTime: string, endTime?: string): string {
  if (!startTime) return '';
  const start = new Date(startTime);
  const end = endTime ? new Date(endTime) : new Date();
  const diff = end.getTime() - start.getTime();

  const days = Math.floor(diff / (1000 * 60 * 60 * 24));
  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

  if (days > 0) return `${days}天${hours}小时`;
  if (hours > 0) return `${hours}小时${minutes}分钟`;
  return `${minutes}分钟`;
}

// 获取处理持续时长
function getProcessDuration(startTime: string, endTime: string): string {
  return getTimeDuration(startTime, endTime);
}

// 处理函数
function handleCreateOrder() {
  // 跳转到订单创建页面
  window.open('/order/create', '_blank');
}

// 小屏筛选抽屉
const orderFilterDrawerVisible = ref(false);

function handleSearch() {
  fetchTableData();
}

function handleReset() {
  Object.assign(searchParams, {
    keyword: '',
    status: undefined,
    dateRange: undefined
  });
  fetchTableData();
}

async function handleViewDetail(row: any) {
  try {
    const response = await fetchOrderDetail(row.order_id);
    selectedOrder.value = response;
    drawerVisible.value = true;
  } catch (error: any) {
    ElMessage.error(error.message || '获取订单详情失败');
  }
}

// 获取源台状态类型
function getProviderStatusType(status: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' {
  if (!status) return 'info';

  switch (status) {
    case '已完成':
    case '完成':
    case '已结束':
      return 'success';
    case '进行中':
    case '处理中':
    case '学习中':
      return 'primary';
    case '已暂停':
    case '暂停':
      return 'warning';
    case '失败':
    case '错误':
    case '异常':
      return 'danger';
    case '队列中':
    case '等待中':
    case '排队中':
      return 'info';
    default:
      return 'info';
  }
}

// 判断是否可以补刷
function canRefill(row: any) {
  // 必须有上游订单ID才能补刷
  return (
    row.upstream_order_id &&
    (row.status === OrderStatus.PROCESSING || row.status === OrderStatus.FAILED || row.status === OrderStatus.COMPLETED)
  );
}

// 判断是否可以同步
function canSync(row: any) {
  // 必须有上游订单ID才能同步
  return (
    row.upstream_order_id &&
    (row.status === OrderStatus.PROCESSING ||
      row.status === OrderStatus.PENDING_PROCESS ||
      row.status === OrderStatus.COMPLETED)
  );
}

// 判断是否可以取消
function canCancel(row: any) {
  return row.status !== OrderStatus.COMPLETED && row.status !== OrderStatus.CANCELLED;
}

// 缩短订单号显示：前8位 + ... + 后4位
function shortOrderNo(no: string): string {
  if (!no) return '';
  return no.length > 14 ? `${no.slice(0, 8)}...${no.slice(-4)}` : no;
}

// 复制完整订单号
async function copyOrderNo(no: string) {
  try {
    await navigator.clipboard.writeText(no);
    ElMessage.success('订单号已复制');
  } catch (e) {
    ElMessage.error('复制失败');
  }
}


// 查看课程详情
function handleViewCourses(row: any) {
  try {
    let courses = [];
    if (row.selected_courses) {
      const coursesData =
        typeof row.selected_courses === 'string' ? JSON.parse(row.selected_courses) : row.selected_courses;
      courses = coursesData.courses || [];
    }

    ElMessageBox({
      title: '课程详情',
      message: h('div', { class: 'courses-detail' }, [
        h('p', `共 ${courses.length} 门课程：`),
        ...courses.map((course: any, index: number) =>
          h('div', { key: index, class: 'course-item' }, [
            h('strong', `${index + 1}. ${course.courseName}`),
            h('br'),
            h('span', { class: 'course-id' }, `课程ID: ${course.courseId}`),
            course.status && h('span', { class: 'course-status' }, ` | 状态: ${course.status}`),
            course.progress !== undefined && h('span', { class: 'course-progress' }, ` | 进度: ${course.progress}%`)
          ])
        )
      ]),
      showCancelButton: false,
      confirmButtonText: '关闭'
    });
  } catch {
    ElMessage.error('课程信息格式错误');
  }
}

// 补刷订单
async function handleRefillOrder(row: any) {
  try {
    await ElMessageBox.confirm('确定要补刷这个订单吗？补刷将重新处理未完成的课程。', '确认补刷', {
      type: 'warning'
    });

    // 设置加载状态
    row.refilling = true;

    await refillOrder(row.order_id);
    ElMessage.success('补刷请求已提交，请稍后查看订单状态');

    // 刷新表格数据
    refreshTable();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '补刷订单失败');
    }
  } finally {
    row.refilling = false;
  }
}

// 同步订单状态
async function handleSyncOrder(row: any) {
  try {
    await ElMessageBox.confirm('确定要同步这个订单的状态吗？', '确认同步', {
      type: 'info'
    });

    // 设置加载状态
    row.syncing = true;

    const response = await syncOrderStatus(row.order_id);

    // 显示详细的同步结果
    if (response && response.data) {
      const syncData = response.data;
      const message = h('div', { class: 'sync-result' }, [
        h('p', { class: 'mb-3 font-medium text-green-600' }, '🎉 同步成功！'),

        // 基本信息
        h('div', { class: 'mb-4 p-3 bg-blue-50 rounded-lg' }, [
          h('div', { class: 'font-medium text-blue-800 mb-2' }, '📊 同步信息'),
          h('div', { class: 'space-y-1 text-sm' }, [
            h('div', `订单号: ${syncData.orderNo || row.order_no}`),
            h('div', `本地状态: ${getStatusText(syncData.localStatus)} (${syncData.localStatus})`),
            h('div', `本地进度: ${syncData.localProgress || 0}%`),
            h('div', `同步时间: ${new Date(syncData.syncTime).toLocaleString('zh-CN')}`)
          ])
        ]),

        // 29平台原始数据
        syncData.providerData &&
          Object.keys(syncData.providerData).length > 0 &&
          h('div', { class: 'mb-4 p-3 bg-green-50 rounded-lg' }, [
            h('div', { class: 'font-medium text-green-800 mb-2' }, '🔄 29平台同步数据'),
            h(
              'pre',
              {
                class: 'text-xs bg-white p-3 rounded border max-h-40 overflow-y-auto font-mono'
              },
              JSON.stringify(syncData.providerData, null, 2)
            )
          ]),

        // 格式化数据（如果有）
        syncData.formattedData &&
          h('div', { class: 'mb-4 p-3 bg-yellow-50 rounded-lg' }, [
            h('div', { class: 'font-medium text-yellow-800 mb-2' }, '📋 格式化数据'),
            h('div', { class: 'space-y-1 text-sm' }, [
              syncData.formattedData.platformName && h('div', `平台: ${syncData.formattedData.platformName}`),
              syncData.formattedData.courseName && h('div', `课程: ${syncData.formattedData.courseName}`),
              syncData.formattedData.studentName && h('div', `学生: ${syncData.formattedData.studentName}`),
              syncData.formattedData.status && h('div', `状态: ${syncData.formattedData.status}`),
              syncData.formattedData.progress && h('div', `进度: ${syncData.formattedData.progress}%`),
              syncData.formattedData.remarks && h('div', `备注: ${syncData.formattedData.remarks}`)
            ])
          ]),

        // 完整原始响应数据
        syncData.rawSyncData &&
          h('div', { class: 'p-3 bg-gray-50 rounded-lg' }, [
            h('div', { class: 'font-medium text-gray-800 mb-2' }, '🔍 完整原始响应'),
            h(
              'pre',
              {
                class: 'text-xs bg-white p-3 rounded border max-h-60 overflow-y-auto font-mono'
              },
              JSON.stringify(syncData.rawSyncData, null, 2)
            )
          ])
      ]);

      ElMessageBox({
        title: '同步结果',
        message,
        showCancelButton: false,
        confirmButtonText: '确定',
        type: 'success'
      });
    } else {
      ElMessage.success('同步请求已提交，订单状态已更新');
    }

    // 刷新表格数据
    refreshTable();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '同步订单状态失败');
    }
  } finally {
    row.syncing = false;
  }
}

// 改密功能
function handleChangePassword(row: any) {
  changePasswordForm.order_id = row.order_id;
  changePasswordForm.new_password = '';
  changePasswordForm.confirm_password = '';
  changePasswordVisible.value = true;
}

async function submitChangePassword() {
  if (!changePasswordForm.new_password) {
    ElMessage.warning('请输入新密码');
    return;
  }

  if (changePasswordForm.new_password !== changePasswordForm.confirm_password) {
    ElMessage.warning('两次输入的密码不一致');
    return;
  }

  try {
    await changeOrderPassword(changePasswordForm.order_id, {
      new_password: changePasswordForm.new_password,
      confirm_password: changePasswordForm.confirm_password
    });

    ElMessage.success('改密请求已提交');
    changePasswordVisible.value = false;
    refreshTable();
  } catch (error: any) {
    ElMessage.error(error.message || '改密失败');
  }
}

// 反馈功能
function handleFeedback(row: any) {
  feedbackForm.order_id = row.order_id;
  feedbackForm.feedback_type = 'issue';
  feedbackForm.feedback_content = '';
  feedbackForm.attachments = [];
  feedbackVisible.value = true;
}

async function submitFeedback() {
  if (!feedbackForm.feedback_content.trim()) {
    ElMessage.warning('请输入反馈内容');
    return;
  }

  try {
    await feedbackOrder(feedbackForm.order_id, {
      feedback_type: feedbackForm.feedback_type,
      feedback_content: feedbackForm.feedback_content,
      attachments: feedbackForm.attachments
    });

    ElMessage.success('反馈已提交');
    feedbackVisible.value = false;
    refreshTable();
  } catch (error: any) {
    ElMessage.error(error.message || '提交反馈失败');
  }
}

// 取消订单
async function handleCancelOrder(row: any) {
  try {
    await ElMessageBox.confirm('确定要取消这个订单吗？', '确认取消', {
      type: 'warning'
    });

    await fetchCancelOrder(row.order_id, '用户取消');
    ElMessage.success('订单取消成功');
    refreshTable();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error(error.message || '取消订单失败');
    }
  }
}

// 获取订单课程列表
function getOrderCourses(order: any) {
  try {
    if (order.selected_courses) {
      const coursesData =
        typeof order.selected_courses === 'string' ? JSON.parse(order.selected_courses) : order.selected_courses;
      return coursesData.courses || [];
    }
    return [];
  } catch {
    return [];
  }
}

// 获取课程数量
function getCourseCount(row: any) {
  try {
    if (row.selected_courses) {
      const courses =
        typeof row.selected_courses === 'string' ? JSON.parse(row.selected_courses) : row.selected_courses;
      return courses.totalCount || courses.courses?.length || 0;
    }
    return row.quantity || 1;
  } catch {
    return row.quantity || 1;
  }
}

// 获取已完成课程数量
function getCompletedCourses(order: any): number {
  const courses = getOrderCourses(order);
  return courses.filter(
    (course: any) => course.status === '完成' || course.status === 'completed' || course.progress === 100
  ).length;
}

// 获取处理中课程数量
function getProcessingCourses(order: any): number {
  const courses = getOrderCourses(order);
  return courses.filter(
    (course: any) =>
      course.status === '处理中' || course.status === 'processing' || (course.progress > 0 && course.progress < 100)
  ).length;
}

// 获取失败课程数量
function getFailedCourses(order: any): number {
  const courses = getOrderCourses(order);
  return courses.filter(
    (course: any) => course.status === '失败' || course.status === 'failed' || course.status === '错误'
  ).length;
}

// 获取课程状态类型
function getCourseStatusType(status: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' {
  switch (status) {
    case '完成':
    case 'completed':
      return 'success';
    case '处理中':
    case 'processing':
      return 'primary';
    case '失败':
    case 'failed':
    case '错误':
      return 'danger';
    case '等待':
    case 'pending':
      return 'warning';
    default:
      return 'info';
  }
}

// 统计函数
function getTotalAmount(): string {
  const total = tableData.value.reduce((sum, order) => sum + (Number(order.amount) || 0), 0);
  return total.toFixed(2);
}

function getStatusCount(status: number): number {
  return tableData.value.filter(order => order.status === status).length;
}

// 生命周期
onMounted(() => {
  fetchTableData();
  // useIsMobile 已自动监听，无需手动绑定
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-medium">订单管理</span>
          <ElButton v-permission="'order:create'" type="primary" @click="handleCreateOrder">
            <template #icon>
              <icon-ic-round-plus class="text-icon" />
            </template>
            新建订单
          </ElButton>
        </div>
      </template>

      <!-- 搜索栏 -->
      <div class="flex flex-wrap items-center gap-16px pb-16px">
        <ElInput
          v-model="searchParams.keyword"
          placeholder="搜索订单号、课程名称、学校、账号"
          clearable
          class="w-280px"
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <icon-ic-round-search class="text-icon" />
          </template>
        </ElInput>

        <ElSelect v-model="searchParams.status" placeholder="订单状态" clearable class="hidden md:block w-140px">
          <ElOption v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </ElSelect>

        <ElDatePicker
          v-model="searchParams.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          class="hidden md:inline-flex"
        />

        <!-- 小屏改为使用筛选抽屉按钮 -->
        <ElButton class="md:hidden" @click="orderFilterDrawerVisible = true">筛选</ElButton>

        <FilterDrawer v-model="orderFilterDrawerVisible" @apply="handleSearch" @reset="handleReset">
          <div class="grid grid-cols-1 gap-12px">
            <ElSelect v-model="searchParams.status" placeholder="订单状态" clearable class="w-full">
              <ElOption v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
            <ElDatePicker
              v-model="searchParams.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </div>
        </FilterDrawer>

        <ElButton type="primary" class="hidden md:inline-flex" @click="handleSearch">
          <template #icon>
            <icon-ic-round-search class="text-icon" />
          </template>
          搜索
        </ElButton>

        <ElButton class="hidden md:inline-flex" @click="handleReset">
          <template #icon>
            <icon-ic-round-refresh class="text-icon" />
          </template>
          重置
        </ElButton>

        <ElButton type="success" class="hidden md:inline-flex" @click="refreshTable">
          <template #icon>
            <icon-ic-round-refresh class="text-icon" />
          </template>
          刷新
        </ElButton>
      </div>

      <!-- 数据表格（仅列表） -->
      <div class="w-full overflow-x-auto">
        <ElTable
          v-loading="loading"
          :data="tableData"
          class="min-w-760px w-full"
          max-height="70vh"
          stripe
          empty-text="暂无订单数据"
          :size="isMobile ? 'small' : 'default'"
        >
        <ElTableColumn prop="order_no" label="订单号" width="120">
          <template #default="{ row }">
            <ElTooltip placement="top">
              <template #content>
                <div class="text-12px space-y-2px">
                  <div>订单号：{{ row.order_no }}</div>
                  <div v-if="row.order_id">ID：{{ row.order_id }}</div>
                </div>
              </template>
              <div class="flex items-center gap-6px">
                <span class="font-mono text-12px">{{ shortOrderNo(row.order_no) }}</span>
                <ElButton link type="primary" size="small" @click.stop="copyOrderNo(row.order_no)">复制</ElButton>
              </div>
            </ElTooltip>
          </template>
        </ElTableColumn>

          <ElTableColumn label="平台" width="120" show-overflow-tooltip>
            <template #default="{ row }">
              <ElTag type="info" size="small">
                {{ row.product_name || '未知商品' }}
              </ElTag>
            </template>
          </ElTableColumn>

          <ElTableColumn label="学校 账号 密码" width="180" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="school-account-info">
                <div class="mb-1 font-medium">{{ row.school_name || '未填写' }}</div>
                <div class="flex items-center gap-2">
                  <ElText copyable size="small">{{ row.platform_account || '未设置' }}</ElText>
                  <ElText copyable show-password size="small">{{ row.platform_password || '***' }}</ElText>
                </div>
              </div>
            </template>
          </ElTableColumn>

          <ElTableColumn label="课程名称" width="160" show-overflow-tooltip>
            <template #default="{ row }">
              <div class="course-info">
                <div class="font-medium">{{ row.course_name || '未知课程' }}</div>
              </div>
            </template>
          </ElTableColumn>

          <ElTableColumn label="任务状态" width="110">
            <template #default="{ row }">
              <div class="task-status">
                <ElTag :type="getProviderStatusType(row.process_status)" size="small" effect="dark">
                  {{ row.process_status || getStatusText(row.status) }}
                </ElTag>
              </div>
            </template>
          </ElTableColumn>

          <ElTableColumn label="进度" width="100">
            <template #default="{ row }">
              <div class="progress-detail">
                <!-- 样式1：圆形进度条 -->
                <!--
 <div class="flex items-center justify-center">
                <ElProgress
                  :percentage="row.progress || 0"
                  type="circle"
                  :width="50"
                  :stroke-width="6"
                />
              </div>
-->

                <!-- 样式2：线性进度条 + 百分比 -->
                <!--
 <div class="flex items-center gap-2">
                <ElProgress :percentage="row.progress || 0" :show-text="false" class="flex-1" />
                <span class="text-sm font-medium">{{ row.progress || 0 }}%</span>
              </div>
-->

                <!-- 样式3：仪表盘进度条 -->
                <div class="flex items-center justify-center">
                  <ElProgress :percentage="Number(row.progress || 0)" type="dashboard" :width="60" />
                </div>
              </div>
            </template>
          </ElTableColumn>

          <ElTableColumn label="金额" width="90" prop="amount" sortable>
            <template #default="{ row }">
              <div class="amount-info">
                <span class="text-sm text-green-600 font-medium">¥{{ Number(row.amount || 0).toFixed(2) }}</span>
              </div>
            </template>
          </ElTableColumn>

          <ElTableColumn label="详细备注" width="260">
            <template #default="{ row }">
              <div class="remark-detail">
                <div
                  v-if="row.remark"
                  class="max-h-24 overflow-y-auto whitespace-pre-wrap break-words border rounded bg-gray-50 p-2 text-sm leading-relaxed"
                >
                  {{ row.remark }}
                </div>
                <div v-else class="text-xs text-gray-400">无备注</div>
              </div>
            </template>
          </ElTableColumn>

          <ElTableColumn label="提交时间" width="120" prop="create_time" sortable>
            <template #default="{ row }">
              <div class="time-detail">
                <div class="text-sm font-medium">{{ formatTime(row.create_time) }}</div>
                <div class="text-xs text-gray-500">{{ getRelativeTime(row.create_time) }}</div>
              </div>
            </template>
          </ElTableColumn>

          <ElTableColumn label="处理状态" width="90">
            <template #default="{ row }">
              <div class="process-status">
                <div v-if="row.status === 3" class="text-sm text-green-600 font-medium">已提交</div>
                <div v-else-if="row.status === 6" class="text-sm text-red-600 font-medium">提交失败</div>
                <div v-else class="text-sm text-blue-600 font-medium">等待处理</div>
              </div>
            </template>
          </ElTableColumn>

          <ElTableColumn label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <ElButton v-permission="'order:view'" size="small" type="primary" @click="handleViewDetail(row)">详情</ElButton>
                <ElButton
                  v-permission="'order:sync'"
                  v-if="canSync(row)"
                  size="small"
                  type="warning"
                  :loading="row.syncing"
                  @click="handleSyncOrder(row)"
                >
                  同步
                </ElButton>
                <ElButton
                  v-permission="'order:process'"
                  v-if="canRefill(row)"
                  size="small"
                  type="success"
                  :loading="row.refilling"
                  @click="handleRefillOrder(row)"
                >
                  补刷
                </ElButton>
                <ElDropdown trigger="click">
                  <ElButton size="small" type="info">
                    更多
                    <icon-ic-round-keyboard-arrow-down class="ml-1" />
                  </ElButton>
                  <template #dropdown>
                    <ElDropdownMenu>
                      <ElDropdownItem v-permission="'order:view'" @click="handleViewCourses(row)">
                        <icon-ic-round-school class="mr-1" />
                        查看课程
                      </ElDropdownItem>
                      <ElDropdownItem v-permission="'order:edit'" v-if="row.status === 3 || row.status === 4" @click="handleChangePassword(row)">
                        <icon-ic-round-lock class="mr-1" />
                        修改密码
                      </ElDropdownItem>
                      <ElDropdownItem v-permission="'order:edit'" @click="handleFeedback(row)">
                        <icon-ic-round-feedback class="mr-1" />
                        订单反馈
                      </ElDropdownItem>
                      <ElDropdownItem v-permission="'order:delete'" v-if="canCancel(row)" divided @click="handleCancelOrder(row)">
                        <icon-ic-round-cancel class="mr-1 text-red-500" />
                        取消订单
                      </ElDropdownItem>
                    </ElDropdownMenu>
                  </template>
                </ElDropdown>
              </div>
            </template>
          </ElTableColumn>
        </ElTable>
      </div>

      <!-- 统计信息 -->
      <div class="mt-12px flex items-center justify-between text-sm text-gray-600">
        <div class="flex items-center gap-16px">
          <span>共 {{ pagination.total }} 条订单</span>
          <span>总金额: ¥{{ getTotalAmount() }}</span>
        </div>
        <div class="flex items-center gap-12px">
          <ElTag size="small" type="success">已完成: {{ getStatusCount(4) }}</ElTag>
          <ElTag size="small" type="primary">处理中: {{ getStatusCount(2) }}</ElTag>
          <ElTag size="small" type="warning">待处理: {{ getStatusCount(1) }}</ElTag>
          <ElTag size="small" type="danger">失败: {{ getStatusCount(6) }}</ElTag>
        </div>
      </div>


      <!-- 分页 -->
      <div class="mt-16px flex justify-center md:justify-end">
        <ElPagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>


    </ElCard>

    <!-- 订单详情抽屉 -->
    <ElDrawer v-model="drawerVisible" title="订单详情" :size="700">
      <div v-if="selectedOrder" class="space-y-6">
        <!-- 基本信息 -->
        <ElCard>
          <template #header>
            <div class="flex items-center justify-between">
              <span class="text-lg font-medium">基本信息</span>
              <div class="flex items-center gap-2">
                <ElTag :type="getStatusType(selectedOrder.status)" size="large">
                  {{ getStatusText(selectedOrder.status) }}
                </ElTag>
                <ElProgress
                  v-if="selectedOrder.progress > 0"
                  :percentage="selectedOrder.progress"
                  :width="60"
                  type="circle"
                  :show-text="false"
                />
              </div>
            </div>
          </template>

          <ElDescriptions :column="2" border>
            <ElDescriptionsItem label="订单号" :span="2">
              <div class="flex items-center gap-2">
                <ElText copyable>{{ selectedOrder.order_no }}</ElText>
                <ElTag v-if="selectedOrder.order_id" size="small" type="info">ID: {{ selectedOrder.order_id }}</ElTag>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem label="用户信息">
              <div class="flex flex-col gap-1">
                <span class="font-medium">{{ selectedOrder.username || '未知用户' }}</span>
                <span class="text-sm text-gray-500">ID: {{ selectedOrder.user_id }}</span>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem label="客户端IP">
              <ElText copyable>{{ selectedOrder.client_ip || '未记录' }}</ElText>
            </ElDescriptionsItem>
            <ElDescriptionsItem label="商品信息" :span="2">
              <div class="flex items-center gap-2">
                <span class="font-medium">
                  {{ selectedOrder.itemName || selectedOrder.product_name || '未知商品' }}
                </span>
                <ElTag v-if="selectedOrder.product_id" size="small" type="info">
                  商品ID: {{ selectedOrder.product_id }}
                </ElTag>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem label="平台类型">
              <ElTag type="info">{{ selectedOrder.platformType || selectedOrder.platform_type || '未知平台' }}</ElTag>
            </ElDescriptionsItem>
            <ElDescriptionsItem label="货源信息">
              <div class="flex items-center gap-2">
                <ElTag v-if="selectedOrder.providerName || selectedOrder.provider_name" type="success">
                  {{ selectedOrder.providerName || selectedOrder.provider_name }}
                </ElTag>
                <span v-else class="text-gray-400">未知货源</span>
                <ElTag v-if="selectedOrder.provider_id" size="small" type="info">
                  ID: {{ selectedOrder.provider_id }}
                </ElTag>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem label="订单金额">
              <div class="flex flex-col gap-1">
                <ElText type="primary" size="large">¥{{ selectedOrder.amount || 0 }}</ElText>
                <div class="text-sm text-gray-500">
                  <span>单价: ¥{{ selectedOrder.unit_price || 0 }}</span>
                  <span v-if="selectedOrder.cost_amount">| 成本: ¥{{ selectedOrder.cost_amount }}</span>
                </div>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem label="课程数量">
              <div class="flex items-center gap-2">
                <ElTag type="primary" size="large">{{ getCourseCount(selectedOrder) }}门</ElTag>
                <span class="text-sm text-gray-500">数量: {{ selectedOrder.quantity || 0 }}</span>
              </div>
            </ElDescriptionsItem>
          </ElDescriptions>
        </ElCard>

        <!-- 账号信息 -->
        <ElCard>
          <template #header>
            <div class="flex items-center justify-between">
              <span class="text-lg font-medium">账号信息</span>
              <ElTag v-if="selectedOrder.platform_account" type="success" size="small">账号已配置</ElTag>
            </div>
          </template>

          <ElDescriptions :column="2" border>
            <ElDescriptionsItem label="学校名称" :span="2">
              <div class="flex items-center gap-2">
                <span class="font-medium">{{ selectedOrder.school_name || '未填写' }}</span>
                <ElTag v-if="selectedOrder.school_name" size="small" type="info">已验证</ElTag>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem label="平台账号">
              <div class="flex items-center gap-2">
                <ElText copyable>{{ selectedOrder.platform_account || '未设置' }}</ElText>
                <ElTag v-if="selectedOrder.platform_account" size="small" type="success">
                  <icon-ic-round-check class="text-xs" />
                </ElTag>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem label="平台密码">
              <div class="flex items-center gap-2">
                <ElText copyable show-password>{{ selectedOrder.platform_password || '***' }}</ElText>
                <ElButton
                  v-if="selectedOrder.status === 3 || selectedOrder.status === 4"
                  size="small"
                  type="warning"
                  @click="handleChangePassword(selectedOrder)"
                >
                  修改密码
                </ElButton>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem v-if="selectedOrder.login_status" label="登录状态">
              <ElTag :type="selectedOrder.login_status === 'success' ? 'success' : 'danger'">
                {{ selectedOrder.login_status === 'success' ? '登录成功' : '登录失败' }}
              </ElTag>
            </ElDescriptionsItem>
            <ElDescriptionsItem v-if="selectedOrder.process_status" label="处理状态">
              <ElTag type="info">{{ selectedOrder.process_status }}</ElTag>
            </ElDescriptionsItem>
          </ElDescriptions>
        </ElCard>

        <!-- 订单状态 -->
        <ElCard>
          <template #header>
            <div class="flex items-center justify-between">
              <span class="text-lg font-medium">订单状态</span>
              <div class="flex items-center gap-2">
                <ElButton
                  v-if="canSync(selectedOrder)"
                  size="small"
                  type="warning"
                  :loading="selectedOrder.syncing"
                  @click="handleSyncOrder(selectedOrder)"
                >
                  同步状态
                </ElButton>
                <ElButton
                  v-if="canRefill(selectedOrder)"
                  size="small"
                  type="success"
                  :loading="selectedOrder.refilling"
                  @click="handleRefillOrder(selectedOrder)"
                >
                  补刷订单
                </ElButton>
              </div>
            </div>
          </template>

          <ElDescriptions :column="2" border>
            <ElDescriptionsItem label="当前状态">
              <div class="flex items-center gap-2">
                <ElTag :type="getStatusType(selectedOrder.status)" size="large">
                  {{ getStatusText(selectedOrder.status) }}
                </ElTag>
                <span class="text-sm text-gray-500">状态码: {{ selectedOrder.status }}</span>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem label="完成进度">
              <div class="flex items-center gap-2">
                <ElProgress :percentage="selectedOrder.progress || 0" :width="120" />
                <span class="text-sm font-medium">{{ selectedOrder.progress || 0 }}%</span>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem label="上游订单ID" :span="2">
              <div class="flex items-center gap-2">
                <ElText v-if="selectedOrder.upstream_order_id" copyable>
                  {{ selectedOrder.upstream_order_id }}
                </ElText>
                <span v-else class="text-gray-400">暂无上游订单ID</span>
                <ElTag v-if="selectedOrder.upstream_order_id" size="small" type="success">已关联</ElTag>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem v-if="selectedOrder.error_message" label="错误信息" :span="2">
              <ElAlert :title="selectedOrder.error_message" type="error" :closable="false" show-icon />
            </ElDescriptionsItem>
            <ElDescriptionsItem v-if="selectedOrder.failure_reason" label="失败原因" :span="2">
              <ElAlert :title="selectedOrder.failure_reason" type="warning" :closable="false" show-icon />
            </ElDescriptionsItem>
            <ElDescriptionsItem v-if="selectedOrder.remark" label="备注信息" :span="2">
              <div class="border-l-4 border-blue-400 rounded bg-gray-50 p-3">
                <p class="text-sm text-gray-700">{{ selectedOrder.remark }}</p>
              </div>
            </ElDescriptionsItem>
          </ElDescriptions>
        </ElCard>

        <!-- 时间信息 -->
        <ElCard>
          <template #header>
            <div class="flex items-center justify-between">
              <span class="text-lg font-medium">时间信息</span>
              <ElTag v-if="selectedOrder.create_time" size="small" type="info">
                {{ getTimeDuration(selectedOrder.create_time) }}
              </ElTag>
            </div>
          </template>

          <ElDescriptions :column="2" border>
            <ElDescriptionsItem label="创建时间">
              <div class="flex flex-col gap-1">
                <span class="font-medium">{{ formatFullTime(selectedOrder.create_time) }}</span>
                <span class="text-sm text-gray-500">{{ getRelativeTime(selectedOrder.create_time) }}</span>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem label="最后更新">
              <div class="flex flex-col gap-1">
                <span class="font-medium">{{ formatFullTime(selectedOrder.update_time) }}</span>
                <span class="text-sm text-gray-500">{{ getRelativeTime(selectedOrder.update_time) }}</span>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem v-if="selectedOrder.start_time" label="开始处理时间">
              <div class="flex flex-col gap-1">
                <span class="font-medium">{{ formatFullTime(selectedOrder.start_time) }}</span>
                <span class="text-sm text-gray-500">{{ getRelativeTime(selectedOrder.start_time) }}</span>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem v-if="selectedOrder.complete_time" label="完成时间">
              <div class="flex flex-col gap-1">
                <span class="font-medium">{{ formatFullTime(selectedOrder.complete_time) }}</span>
                <span class="text-sm text-gray-500">{{ getRelativeTime(selectedOrder.complete_time) }}</span>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem v-if="selectedOrder.start_time && selectedOrder.complete_time" label="处理耗时">
              <ElTag type="success">
                {{ getProcessDuration(selectedOrder.start_time, selectedOrder.complete_time) }}
              </ElTag>
            </ElDescriptionsItem>
            <ElDescriptionsItem v-if="selectedOrder.create_time" label="订单年龄">
              <ElTag type="info">
                {{ getTimeDuration(selectedOrder.create_time) }}
              </ElTag>
            </ElDescriptionsItem>
          </ElDescriptions>
        </ElCard>

        <!-- 课程信息 -->
        <ElCard v-if="selectedOrder.selected_courses || selectedOrder.course_ids">
          <template #header>
            <div class="flex items-center justify-between">
              <span class="text-lg font-medium">课程信息</span>
              <div class="flex items-center gap-2">
                <ElTag type="primary">
                  共 {{ getOrderCourses(selectedOrder).length || getCourseCount(selectedOrder) }} 门课程
                </ElTag>
                <ElButton size="small" type="info" @click="handleViewCourses(selectedOrder)">查看详情</ElButton>
              </div>
            </div>
          </template>

          <!-- 课程统计 -->
          <div class="mb-4 border border-blue-200 rounded-lg bg-blue-50 p-3">
            <div class="grid grid-cols-4 gap-4 text-center">
              <div>
                <div class="text-lg text-blue-600 font-bold">
                  {{ getOrderCourses(selectedOrder).length || getCourseCount(selectedOrder) }}
                </div>
                <div class="text-sm text-gray-600">总课程数</div>
              </div>
              <div>
                <div class="text-lg text-green-600 font-bold">{{ getCompletedCourses(selectedOrder) }}</div>
                <div class="text-sm text-gray-600">已完成</div>
              </div>
              <div>
                <div class="text-lg text-orange-600 font-bold">{{ getProcessingCourses(selectedOrder) }}</div>
                <div class="text-sm text-gray-600">处理中</div>
              </div>
              <div>
                <div class="text-lg text-red-600 font-bold">{{ getFailedCourses(selectedOrder) }}</div>
                <div class="text-sm text-gray-600">失败</div>
              </div>
            </div>
          </div>

          <!-- 课程列表 -->
          <div class="course-list">
            <template v-for="(course, index) in getOrderCourses(selectedOrder).slice(0, 5)" :key="index">
              <div class="course-item">
                <div class="course-header">
                  <div class="flex items-center gap-2">
                    <span class="course-index">{{ index + 1 }}.</span>
                    <span class="course-name">{{ course.courseName || course.course_name }}</span>
                  </div>
                  <div class="flex items-center gap-2">
                    <ElTag v-if="course.status" size="small" :type="getCourseStatusType(course.status)">
                      {{ course.status }}
                    </ElTag>
                    <ElTag v-if="course.teacher" size="small" type="info">
                      {{ course.teacher }}
                    </ElTag>
                  </div>
                </div>
                <div class="course-details">
                  <span class="course-id">ID: {{ course.courseId || course.course_id }}</span>
                  <span v-if="course.class" class="course-class">班级: {{ course.class }}</span>
                  <span v-if="course.progress !== undefined" class="course-progress">进度: {{ course.progress }}%</span>
                </div>
                <div v-if="course.progress !== undefined" class="course-progress-bar">
                  <ElProgress :percentage="course.progress" :show-text="false" :stroke-width="4" />
                </div>
              </div>
            </template>

            <!-- 显示更多课程提示 -->
            <div v-if="getOrderCourses(selectedOrder).length > 5" class="py-3 text-center">
              <ElButton type="primary" link @click="handleViewCourses(selectedOrder)">
                查看全部 {{ getOrderCourses(selectedOrder).length }} 门课程
              </ElButton>
            </div>
          </div>

          <!-- 如果没有课程详情，显示基本信息 -->
          <div v-if="!getOrderCourses(selectedOrder).length && selectedOrder.course_ids" class="py-6 text-center">
            <ElEmpty description="课程详情加载中...">
              <template #image>
                <icon-ic-round-school class="text-4xl text-gray-400" />
              </template>
              <ElButton type="primary" @click="handleSyncOrder(selectedOrder)">同步课程信息</ElButton>
            </ElEmpty>
          </div>
        </ElCard>

        <!-- 技术信息 -->
        <ElCard v-if="selectedOrder.extra_data || selectedOrder.providerApiUrl">
          <template #header>
            <div class="flex items-center justify-between">
              <span class="text-lg font-medium">技术信息</span>
              <ElTag size="small" type="info">调试信息</ElTag>
            </div>
          </template>

          <ElDescriptions :column="2" border>
            <ElDescriptionsItem v-if="selectedOrder.providerApiUrl" label="货源API" :span="2">
              <ElText copyable>{{ selectedOrder.providerApiUrl }}</ElText>
            </ElDescriptionsItem>
            <ElDescriptionsItem v-if="selectedOrder.categoryName" label="分类信息">
              <ElTag type="info">{{ selectedOrder.categoryName }}</ElTag>
            </ElDescriptionsItem>
            <ElDescriptionsItem v-if="selectedOrder.source" label="订单来源">
              <ElTag type="success">{{ selectedOrder.source }}</ElTag>
            </ElDescriptionsItem>
            <ElDescriptionsItem v-if="selectedOrder.course_ids" label="课程ID列表" :span="2">
              <div class="max-h-20 overflow-y-auto">
                <ElText copyable>{{ selectedOrder.course_ids }}</ElText>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem v-if="selectedOrder.course_name" label="课程名称" :span="2">
              <ElText copyable>{{ selectedOrder.course_name }}</ElText>
            </ElDescriptionsItem>
            <ElDescriptionsItem v-if="selectedOrder.course_info" label="课程信息" :span="2">
              <div class="max-h-32 overflow-y-auto">
                <pre class="rounded bg-gray-50 p-2 text-sm">{{ selectedOrder.course_info }}</pre>
              </div>
            </ElDescriptionsItem>
            <ElDescriptionsItem v-if="selectedOrder.extra_data" label="扩展数据" :span="2">
              <div class="max-h-40 overflow-y-auto">
                <pre class="rounded bg-gray-50 p-2 text-sm">{{
                  JSON.stringify(selectedOrder.extra_data, null, 2)
                }}</pre>
              </div>
            </ElDescriptionsItem>
          </ElDescriptions>
        </ElCard>

        <!-- 操作按钮 -->
        <ElCard>
          <template #header>
            <span class="text-lg font-medium">订单操作</span>
          </template>

          <div class="grid grid-cols-2 gap-3 md:grid-cols-4">
            <!-- 主要操作 -->
            <ElButton
              v-if="canRefill(selectedOrder)"
              type="success"
              size="large"
              :loading="selectedOrder.refilling"
              class="h-12"
              @click="handleRefillOrder(selectedOrder)"
            >
              <template #icon>
                <icon-ic-round-refresh class="text-lg" />
              </template>
              补刷订单
            </ElButton>

            <ElButton
              v-if="canSync(selectedOrder)"
              type="warning"
              size="large"
              :loading="selectedOrder.syncing"
              class="h-12"
              @click="handleSyncOrder(selectedOrder)"
            >
              <template #icon>
                <icon-ic-round-sync class="text-lg" />
              </template>
              同步状态
            </ElButton>

            <ElButton
              v-if="selectedOrder.status === 3 || selectedOrder.status === 4"
              type="warning"
              size="large"
              class="h-12"
              @click="handleChangePassword(selectedOrder)"
            >
              <template #icon>
                <icon-ic-round-lock class="text-lg" />
              </template>
              修改密码
            </ElButton>

            <ElButton
              v-if="canCancel(selectedOrder)"
              type="danger"
              size="large"
              class="h-12"
              @click="handleCancelOrder(selectedOrder)"
            >
              <template #icon>
                <icon-ic-round-cancel class="text-lg" />
              </template>
              取消订单
            </ElButton>

            <!-- 次要操作 -->
            <ElButton type="info" size="large" class="h-12" @click="handleViewCourses(selectedOrder)">
              <template #icon>
                <icon-ic-round-school class="text-lg" />
              </template>
              查看课程
            </ElButton>

            <ElButton type="default" size="large" class="h-12" @click="handleFeedback(selectedOrder)">
              <template #icon>
                <icon-ic-round-feedback class="text-lg" />
              </template>
              订单反馈
            </ElButton>

            <ElButton type="primary" size="large" class="h-12" @click="refreshTable">
              <template #icon>
                <icon-ic-round-refresh class="text-lg" />
              </template>
              刷新数据
            </ElButton>

            <ElButton type="default" size="large" class="h-12" @click="drawerVisible = false">
              <template #icon>
                <icon-ic-round-close class="text-lg" />
              </template>
              关闭详情
            </ElButton>
          </div>

          <!-- 操作提示 -->
          <div class="mt-4 rounded-lg bg-blue-50 p-3">
            <div class="text-sm text-blue-700">
              <p class="mb-1 font-medium">操作说明：</p>
              <ul class="list-disc list-inside text-xs space-y-1">
                <li>补刷：重新处理失败或未完成的课程</li>
                <li>同步：从上游平台同步最新的订单状态</li>
                <li>改密：修改平台账号的登录密码</li>
                <li>反馈：提交订单相关的问题或建议</li>
              </ul>
            </div>
          </div>
        </ElCard>
      </div>
    </ElDrawer>

    <!-- 改密对话框 -->
    <ElDialog
      v-model="changePasswordVisible"
      title="订单改密"
      width="500px"
      @close="
        changePasswordForm.new_password = '';
        changePasswordForm.confirm_password = '';
      "
    >
      <ElForm :model="changePasswordForm" label-width="120px">
        <ElFormItem label="新密码" required>
          <ElInput v-model="changePasswordForm.new_password" type="password" placeholder="请输入新密码" show-password />
        </ElFormItem>

        <ElFormItem label="确认密码" required>
          <ElInput
            v-model="changePasswordForm.confirm_password"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </ElFormItem>

        <ElFormItem>
          <ElAlert
            title="注意"
            description="改密操作将修改平台账号的登录密码，请确保新密码安全可靠"
            type="warning"
            :closable="false"
          />
        </ElFormItem>
      </ElForm>

      <template #footer>
        <div class="flex justify-end gap-8px">
          <ElButton @click="changePasswordVisible = false">取消</ElButton>
          <ElButton type="primary" @click="submitChangePassword">确认改密</ElButton>
        </div>
      </template>
    </ElDialog>

    <!-- 反馈对话框 -->
    <ElDialog
      v-model="feedbackVisible"
      title="订单反馈"
      width="600px"
      @close="
        feedbackForm.feedback_content = '';
        feedbackForm.attachments = [];
      "
    >
      <ElForm :model="feedbackForm" label-width="120px">
        <ElFormItem label="反馈类型" required>
          <ElRadioGroup v-model="feedbackForm.feedback_type">
            <ElRadio value="issue">问题反馈</ElRadio>
            <ElRadio value="suggestion">建议反馈</ElRadio>
            <ElRadio value="complaint">投诉反馈</ElRadio>
            <ElRadio value="other">其他</ElRadio>
          </ElRadioGroup>
        </ElFormItem>

        <ElFormItem label="反馈内容" required>
          <ElInput
            v-model="feedbackForm.feedback_content"
            type="textarea"
            :rows="5"
            placeholder="请详细描述您遇到的问题或建议..."
            maxlength="500"
            show-word-limit
          />
        </ElFormItem>

        <ElFormItem label="附件">
          <div class="text-sm text-gray-500">可上传相关截图或文件（暂未实现上传功能）</div>
        </ElFormItem>
      </ElForm>

      <template #footer>
        <div class="flex justify-end gap-8px">
          <ElButton @click="feedbackVisible = false">取消</ElButton>
          <ElButton type="primary" @click="submitFeedback">提交反馈</ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<style scoped>
/* 响应式表格滚动 */
.el-table {
  width: 100%;
}

/* 订单号信息样式 */
.order-no-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* 学校信息样式 */
.school-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* 课程信息样式 */
.course-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 任务状态样式 */
.task-status {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* 进度详情样式 */
.progress-detail {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 备注详情样式 */
.remark-detail {
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
}

/* 学校账号信息样式 */
.school-account-info {
  line-height: 1.4;
}

/* 金额信息样式 */
.amount-info {
  text-align: center;
}

/* 进度详情样式 */
.progress-detail {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 任务状态样式 */
.task-status {
  display: flex;
  justify-content: center;
}

/* 处理状态样式 */
.process-status {
  text-align: center;
}

/* 时间详情样式 */
.time-detail {
  line-height: 1.3;
}

/* 表格行悬停效果 */
.el-table__row:hover {
  background-color: var(--el-table-row-hover-bg-color);
}

/* 操作按钮组样式 */
.action-buttons {
  display: flex;
  gap: 4px;
  flex-wrap: wrap;
  justify-content: center;
}

/* 时间详情样式 */
.time-detail {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* 处理状态样式 */
.process-status {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

/* 同步结果样式 */
.sync-result {
  text-align: left;
}

/* 商品信息样式（保留兼容性） */
.product-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.product-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
}

.course-count {
  color: var(--el-text-color-regular);
}

.provider-name {
  color: var(--el-color-success);
  font-size: 12px;
}

/* 账号信息样式 */
.account-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.school-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.platform-account {
  color: var(--el-text-color-regular);
  font-size: 12px;
}

/* 状态信息样式 */
.status-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.upstream-id {
  font-size: 11px;
  color: var(--el-text-color-regular);
  background: var(--el-bg-color-page);
  padding: 2px 6px;
  border-radius: 4px;
}

/* 进度金额样式 */
.progress-amount {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.amount {
  font-weight: 500;
  color: var(--el-color-primary);
  font-size: 14px;
}

/* 时间信息样式 */
.time-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  font-size: 12px;
}

.create-time {
  color: var(--el-text-color-primary);
}

.update-time {
  color: var(--el-text-color-regular);
}

/* 课程详情对话框样式 */
:deep(.courses-detail) {
  max-height: 400px;
  overflow-y: auto;
}

:deep(.course-item) {
  margin-bottom: 12px;
  padding: 8px;
  background: var(--el-bg-color-page);
  border-radius: 4px;
}

:deep(.course-id),
:deep(.course-status),
:deep(.course-progress) {
  color: var(--el-text-color-regular);
  font-size: 12px;
}

/* 订单详情抽屉中的课程列表样式 */
.course-list {
  max-height: 300px;
  overflow-y: auto;
}

.course-item {
  margin-bottom: 12px;
  padding: 12px;
  background: var(--el-bg-color-page);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

.course-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.course-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
  flex: 1;
}

.course-details {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.course-id {
  color: var(--el-text-color-regular);
}

.course-progress {
  color: var(--el-color-primary);
}

/* 移动端优化 */
@media (max-width: 768px) {
  .el-table {
    font-size: 12px;
  }

  .product-info,
  .account-info {
    font-size: 12px;
  }
}
</style>
