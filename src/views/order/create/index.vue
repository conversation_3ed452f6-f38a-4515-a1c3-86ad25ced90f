<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';
import { fetchProductCategories, fetchProductList } from '@/service/api/product';
import { batchCreateOrdersFromQuery, batchQueryCourses, queryCourses } from '@/service/api/query';
import { createOrder } from '@/service/api/order';

defineOptions({
  name: 'OrderCreate'
});

// 路由
const router = useRouter();

// 响应式数据
const selectedProduct = ref<any>(null);
const categories = ref<any[]>([]);
const products = ref<any[]>([]);
const loadingProducts = ref(false);
const querying = ref(false);
const submitting = ref(false);
const queriedCourses = ref<any[]>([]);
const selectedCourses = ref<any[]>([]);

// 移动端检测
const isMobile = ref(false);
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

// 组件状态标志
const isDestroyed = ref(false);

// 搜索和筛选相关数据
const searchKeyword = ref('');
const selectedAccountFilter = ref('');
const searchResults = ref<any[]>([]);

// 抽屉相关数据
const courseDrawerVisible = ref(false);
const expandedAccounts = ref<Set<string>>(new Set());

// 同名课程自动勾选开关
const autoSelectSameCourse = ref(false);

// 课程ID显示控制
const showCourseId = ref(true);

// 商品表单数据
const productFormRef = ref();
const productForm = ref({
  categoryId: 0,
  productId: undefined as number | undefined
});

// 批量查询相关数据
const queryMode = ref<'single' | 'batch'>('single');
const batchAccountsText = ref('');
const parsedBatchAccounts = ref<any[]>([]);
const batchParseErrors = ref<any[]>([]);

// 单个账号相关
const singleAccountText = ref('');
const parsedSingleAccount = ref<{ school: string; username: string; password: string } | null>(null);
const singleParseError = ref('');

// 表单数据
const accountFormRef = ref();
const accountInfo = ref({
  schoolName: '',
  platformAccount: '',
  platformPassword: '',
  remark: ''
});

// 表单验证规则
const accountRules = {
  platformAccount: [{ required: true, message: '请输入平台账号', trigger: 'blur' }],
  platformPassword: [{ required: true, message: '请输入平台密码', trigger: 'blur' }]
};

// 计算属性
const filteredProducts = computed(() => {
  if (!productForm.value.categoryId || productForm.value.categoryId === 0) {
    return products.value;
  }
  return products.value.filter(product => product.category_id === productForm.value.categoryId);
});

const canQuery = computed(() => {
  if (!selectedProduct.value) return false;

  if (queryMode.value === 'single') {
    return parsedSingleAccount.value && !singleParseError.value;
  }
  return parsedBatchAccounts.value.length > 0 && batchParseErrors.value.length === 0;
});

const canSubmit = computed(() => {
  return selectedProduct.value && selectedCourses.value.length > 0;
});

// 获取唯一账号列表
const uniqueAccountsList = computed(() => {
  if (queryMode.value !== 'batch') return [];

  const accountMap = new Map();
  queriedCourses.value.forEach(course => {
    if (course.accountInfo) {
      const key = course.accountInfo.username;
      if (!accountMap.has(key)) {
        accountMap.set(key, {
          username: course.accountInfo.username,
          userRealName: course.accountInfo.userRealName,
          school: course.accountInfo.school
        });
      }
    }
  });

  return Array.from(accountMap.values());
});

// 按账号分组的课程
const accountGroups = computed(() => {
  if (queryMode.value !== 'batch') return [];

  const groups = new Map();
  queriedCourses.value.forEach(course => {
    if (course.accountInfo) {
      const key = course.accountInfo.username;
      if (!groups.has(key)) {
        groups.set(key, {
          username: course.accountInfo.username,
          userRealName: course.accountInfo.userRealName,
          school: course.accountInfo.school,
          courses: []
        });
      }
      groups.get(key).courses.push(course);
    }
  });

  return Array.from(groups.values());
});

// 筛选后的课程列表
const filteredCourses = computed(() => {
  let filtered = queriedCourses.value;

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(
      course =>
        course.courseName?.toLowerCase().includes(keyword) ||
        String(course.courseId || '')
          .toLowerCase()
          .includes(keyword) ||
        course.teacher?.toLowerCase().includes(keyword) ||
        course.class?.toLowerCase().includes(keyword)
    );
  }

  // 账号筛选（批量模式）
  if (queryMode.value === 'batch' && selectedAccountFilter.value) {
    filtered = filtered.filter(course => course.accountInfo?.username === selectedAccountFilter.value);
  }

  return filtered;
});

// 筛选后的账号分组
const filteredAccountGroups = computed(() => {
  if (queryMode.value !== 'batch') return [];

  let groups = accountGroups.value;

  // 账号筛选
  if (selectedAccountFilter.value) {
    groups = groups.filter(group => group.username === selectedAccountFilter.value);
  }

  // 对每个分组内的课程进行搜索筛选
  return groups
    .map(group => ({
      ...group,
      courses: group.courses.filter((course: any) => {
        // 关键词搜索
        if (searchKeyword.value) {
          const keyword = searchKeyword.value.toLowerCase();
          return (
            course.courseName?.toLowerCase().includes(keyword) ||
            String(course.courseId || '')
              .toLowerCase()
              .includes(keyword) ||
            course.teacher?.toLowerCase().includes(keyword) ||
            course.class?.toLowerCase().includes(keyword)
          );
        }
        return true;
      })
    }))
    .filter(group => {
      // 只显示有课程的分组（搜索后可能为空）
      return group.courses.length > 0;
    });
});

const orderSummary = computed(() => {
  if (!selectedProduct.value || selectedCourses.value.length === 0) {
    return null;
  }

  const courseCount = selectedCourses.value.length;
  const unitPrice = selectedProduct.value.price;
  const totalAmount = unitPrice * courseCount;

  return {
    courseCount,
    unitPrice,
    totalAmount,
    productName: selectedProduct.value.product_name,
    platformType: selectedProduct.value.platform_type
  };
});

// 批量账号解析
const parseBatchAccounts = () => {
  const lines = batchAccountsText.value.split('\n').filter(line => line.trim());
  const accounts: any[] = [];
  const errors: any[] = [];

  lines.forEach((line, index) => {
    const trimmedLine = line.trim();
    if (!trimmedLine) return;

    const parts = trimmedLine.split(/\s+/);

    if (parts.length === 2) {
      // 格式：账号 密码
      accounts.push({
        school: '',
        username: parts[0],
        password: parts[1],
        lineNumber: index + 1,
        originalLine: trimmedLine
      });
    } else if (parts.length === 3) {
      // 格式：学校 账号 密码
      accounts.push({
        school: parts[0],
        username: parts[1],
        password: parts[2],
        lineNumber: index + 1,
        originalLine: trimmedLine
      });
    } else {
      errors.push({
        line: index + 1,
        text: trimmedLine,
        error: '格式错误，应为"账号 密码"或"学校 账号 密码"'
      });
    }
  });

  parsedBatchAccounts.value = accounts;
  batchParseErrors.value = errors;
};

// 解析单个账号文本
const parseSingleAccount = () => {
  parsedSingleAccount.value = null;
  singleParseError.value = '';

  if (!singleAccountText.value.trim()) {
    return;
  }

  const trimmedText = singleAccountText.value.trim();
  const parts = trimmedText.split(/\s+/);

  if (parts.length === 2) {
    // 格式：账号 密码
    const [username, password] = parts;
    parsedSingleAccount.value = {
      school: '自动识别',
      username,
      password
    };
  } else if (parts.length === 3) {
    // 格式：学校 账号 密码
    const [school, username, password] = parts;
    parsedSingleAccount.value = {
      school,
      username,
      password
    };
  } else {
    singleParseError.value = '格式错误，应为"学校 账号 密码"或"账号 密码"';
  }
};

// 方法
const loadCategories = async () => {
  try {
    const response = await fetchProductCategories();
    if (!isDestroyed.value) {
      categories.value = response || [];
    }
  } catch {
    if (!isDestroyed.value) {
      ElMessage.error('加载分类失败');
    }
  }
};

const loadProducts = async () => {
  try {
    if (!isDestroyed.value) {
      loadingProducts.value = true;
    }
    const response = await fetchProductList({
      page: 1,
      pageSize: 1000,
      status: 1 // 只加载启用的商品
    });
    if (!isDestroyed.value) {
      products.value = response.list || [];
    }
  } catch {
    if (!isDestroyed.value) {
      ElMessage.error('加载商品失败');
    }
  } finally {
    if (!isDestroyed.value) {
      loadingProducts.value = false;
    }
  }
};

const handleCategoryChange = () => {
  productForm.value.productId = undefined;
  selectedProduct.value = null;
  queriedCourses.value = [];
  selectedCourses.value = [];
};

const handleQueryModeChange = () => {
  // 切换查询模式时清空相关数据
  queriedCourses.value = [];
  selectedCourses.value = [];
  if (queryMode.value === 'batch') {
    parseBatchAccounts();
  } else {
    parseSingleAccount();
  }
};

const handleProductChange = () => {
  if (productForm.value.productId && productForm.value.productId > 0) {
    selectedProduct.value = products.value.find(p => p.product_id === productForm.value.productId);
  } else {
    selectedProduct.value = null;
  }
  queriedCourses.value = [];
  selectedCourses.value = [];
};

const handleReset = () => {
  // 重置所有表单数据
  productForm.value = {
    categoryId: 0,
    productId: undefined
  };
  accountInfo.value = {
    schoolName: '',
    platformAccount: '',
    platformPassword: '',
    remark: ''
  };
  batchAccountsText.value = '';
  singleAccountText.value = '';
  queryMode.value = 'single';
  selectedProduct.value = null;
  queriedCourses.value = [];
  selectedCourses.value = [];
  parsedBatchAccounts.value = [];
  batchParseErrors.value = [];
  parsedSingleAccount.value = null;
  singleParseError.value = '';
};

const handleQueryCourses = async () => {
  if (!canQuery.value) {
    ElMessage.warning('请先完善商品和账号信息');
    return;
  }

  try {
    if (!isDestroyed.value) {
      querying.value = true;
    }

    if (queryMode.value === 'single') {
      // 单个查询
      if (!parsedSingleAccount.value) {
        ElMessage.warning('请先输入正确格式的账号信息');
        return;
      }

      const queryData = {
        productId: selectedProduct.value.product_id,
        school: parsedSingleAccount.value.school,
        username: parsedSingleAccount.value.username,
        password: parsedSingleAccount.value.password
      };

      const response = await queryCourses(queryData);

      if (response && Array.isArray(response) && response.length > 0) {
        // 后端直接返回课程数组，统一字段名
        const coursesWithAccount = response.map(course => ({
          courseId: course.id,
          courseName: course.name,
          teacher: course.teacher,
          class: course.class,
          progress: course.progress,
          state: course.state,
          credit: course.credit,
          accountInfo: {
            school: course.school || queryData.school || '自动识别',
            username: course.username || queryData.username,
            password: queryData.password,
            userRealName: course.userRealName || ''
          }
        }));

        queriedCourses.value = coursesWithAccount;
        selectedCourses.value = [];

        // 自动打开抽屉
        nextTick(() => {
          courseDrawerVisible.value = true;
        });

        ElMessage.success(`查课成功，找到 ${response.length} 门课程`);
      } else {
        queriedCourses.value = [];
        ElMessage.error('查课失败或未找到课程');
      }
    } else {
      // 批量查询
      const batchData = {
        productId: selectedProduct.value.product_id,
        accountsText: batchAccountsText.value
      };

      const response = await batchQueryCourses(batchData);

      if (response && Array.isArray(response) && response.length > 0) {
        // 后端直接返回课程数组，统一字段名
        const allCourses = response.map(course => ({
          courseId: course.id,
          courseName: course.name,
          teacher: course.teacher || '',
          class: course.class || '',
          progress: course.progress || '',
          state: course.state || '',
          credit: course.credit || '',
          accountInfo: {
            school: course.school || '自动识别',
            username: course.username || '',
            password: '***', // 不显示密码
            userRealName: course.userRealName || ''
          }
        }));

        queriedCourses.value = allCourses;
        selectedCourses.value = [];

        // 统计账号数量
        const uniqueAccountsSet = new Set(allCourses.map(course => course.accountInfo.username));

        // 初始化展开所有账号并自动打开抽屉
        nextTick(() => {
          if (queryMode.value === 'batch') {
            uniqueAccountsList.value.forEach(account => {
              expandedAccounts.value.add(account.username);
            });
          }
          // 自动打开抽屉
          courseDrawerVisible.value = true;
        });

        ElMessage.success(`批量查课完成！共 ${uniqueAccountsSet.size} 个账号，找到 ${allCourses.length} 门课程`);
      } else {
        queriedCourses.value = [];
        ElMessage.error('批量查课失败或未找到课程');
      }
    }
  } catch (error: any) {
    if (!isDestroyed.value) {
      queriedCourses.value = [];

      // 提取后端返回的错误信息
      let errorMessage = '查课失败';

      if (error.response?.data?.msg) {
        // 后端返回的标准错误格式
        errorMessage = error.response.data.msg;
      } else if (error.message && error.message !== 'the backend request error' && error.message.trim() !== '') {
        // 其他类型的错误（排除空字符串）
        errorMessage = error.message;
      }

      ElMessage.error(errorMessage);
    }
  } finally {
    if (!isDestroyed.value) {
      querying.value = false;
    }
  }
};

const isSelected = (course: any) => {
  return selectedCourses.value.some(
    c => c.courseId === course.courseId && c.accountInfo?.username === course.accountInfo?.username
  );
};

const toggleCourse = (course: any) => {
  const index = selectedCourses.value.findIndex(
    c => c.courseId === course.courseId && c.accountInfo?.username === course.accountInfo?.username
  );

  if (index > -1) {
    // 取消选择
    selectedCourses.value.splice(index, 1);

    // 如果开启了同名课程自动勾选，同时取消其他账号的同名课程
    if (autoSelectSameCourse.value && queryMode.value === 'batch') {
      const sameCourses = selectedCourses.value.filter(
        c => c.courseName === course.courseName && c.accountInfo?.username !== course.accountInfo?.username
      );
      sameCourses.forEach(sameCourse => {
        const sameIndex = selectedCourses.value.findIndex(
          c => c.courseId === sameCourse.courseId && c.accountInfo?.username === sameCourse.accountInfo?.username
        );
        if (sameIndex > -1) {
          selectedCourses.value.splice(sameIndex, 1);
        }
      });
    }
  } else {
    // 选择课程
    selectedCourses.value.push(course);

    // 如果开启了同名课程自动勾选，同时选择其他账号的同名课程
    if (autoSelectSameCourse.value && queryMode.value === 'batch') {
      const sameCourses = queriedCourses.value.filter(
        c =>
          c.courseName === course.courseName &&
          c.accountInfo?.username !== course.accountInfo?.username &&
          !isSelected(c)
      );
      sameCourses.forEach(sameCourse => {
        selectedCourses.value.push(sameCourse);
      });
    }
  }
};

const handleSelectAll = () => {
  selectedCourses.value = [...filteredCourses.value];
};

const clearSelection = () => {
  selectedCourses.value = [];
};

// 抽屉相关方法
const openCourseDrawer = () => {
  courseDrawerVisible.value = true;
};

const closeCourseDrawer = () => {
  courseDrawerVisible.value = false;
};

// 账号折叠/展开
const toggleAccountExpand = (username: string) => {
  if (expandedAccounts.value.has(username)) {
    expandedAccounts.value.delete(username);
  } else {
    expandedAccounts.value.add(username);
  }
};

const isAccountExpanded = (username: string) => {
  return expandedAccounts.value.has(username);
};

// 搜索相关方法
const handleSearch = () => {
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    searchResults.value = queriedCourses.value.filter(
      course =>
        course.courseName?.toLowerCase().includes(keyword) ||
        String(course.courseId || '')
          .toLowerCase()
          .includes(keyword) ||
        course.teacher?.toLowerCase().includes(keyword) ||
        course.class?.toLowerCase().includes(keyword)
    );
  } else {
    searchResults.value = [];
  }
};

const selectAllSearchResults = () => {
  searchResults.value.forEach(course => {
    if (!isSelected(course)) {
      selectedCourses.value.push(course);
    }
  });
  ElMessage.success(`已选择 ${searchResults.value.length} 门搜索结果中的课程`);
};

// 账号筛选相关方法
const toggleAccountFilter = (username: string) => {
  selectedAccountFilter.value = selectedAccountFilter.value === username ? '' : username;
};

const clearAccountFilter = () => {
  selectedAccountFilter.value = '';
};

const getAccountCourseCount = (username: string) => {
  return queriedCourses.value.filter(course => course.accountInfo?.username === username).length;
};

const isAccountFullySelected = (username: string) => {
  const accountCourses = queriedCourses.value.filter(course => course.accountInfo?.username === username);
  if (accountCourses.length === 0) return false;

  return accountCourses.every(course => isSelected(course));
};

const isAccountPartiallySelected = (username: string) => {
  const accountCourses = queriedCourses.value.filter(course => course.accountInfo?.username === username);
  if (accountCourses.length === 0) return false;

  const selectedCount = accountCourses.filter(course => isSelected(course)).length;
  return selectedCount > 0 && selectedCount < accountCourses.length;
};

const toggleAccountSelection = (username: string) => {
  const accountCourses = queriedCourses.value.filter(course => course.accountInfo?.username === username);

  const isFullySelected = isAccountFullySelected(username);

  if (isFullySelected) {
    // 取消选择该账号的所有课程
    accountCourses.forEach(course => {
      const index = selectedCourses.value.findIndex(
        c => c.courseId === course.courseId && c.accountInfo?.username === course.accountInfo?.username
      );
      if (index > -1) {
        selectedCourses.value.splice(index, 1);
      }
    });
  } else {
    // 选择该账号的所有课程
    accountCourses.forEach(course => {
      if (!isSelected(course)) {
        selectedCourses.value.push(course);
      }
    });
  }
};

const getSelectedCountForAccount = (username: string) => {
  return selectedCourses.value.filter(course => course.accountInfo?.username === username).length;
};

// 提取错误信息的工具函数
const extractErrorMessage = (error: any, defaultMessage: string): string => {
  // 优先使用后端返回的错误信息
  if (error.response?.data?.msg) {
    return error.response.data.msg;
  }

  // 检查是否是网络请求错误，如果是则使用后端的错误信息
  if (error.code === 'BACKEND_ERROR' && error.response?.data?.msg) {
    return error.response.data.msg;
  }

  // 如果有具体的错误消息且不是通用的网络错误
  if (
    error.message &&
    error.message !== 'the backend request error' &&
    error.message !== 'Network Error' &&
    error.message.trim() !== ''
  ) {
    return error.message;
  }

  return defaultMessage;
};

// 构建单个订单数据
const buildSingleOrderData = () => {
  if (!parsedSingleAccount.value) {
    throw new Error('账号信息解析失败');
  }

  return {
    productId: selectedProduct.value.product_id,
    platformAccount: parsedSingleAccount.value.username,
    platformPassword: parsedSingleAccount.value.password,
    schoolName: parsedSingleAccount.value.school,
    selectedCourses: selectedCourses.value.map(course => ({
      courseId: course.courseId,
      courseName: course.courseName,
      teacher: course.teacher,
      class: course.class,
      state: course.state,
      progress: course.progress,
      credit: course.credit
    })),
    remark: accountInfo.value.remark
  };
};

// 处理批量下单
const handleBatchOrder = async () => {
  const selectedCoursesForBatch = selectedCourses.value.map(course => ({
    username: course.accountInfo.username,
    password: course.accountInfo.password,
    school: course.accountInfo.school || '自动识别',
    courseId: course.courseId,
    courseName: course.courseName,
    teacher: course.teacher,
    class: course.class
  }));

  const result = await batchCreateOrdersFromQuery({
    productId: selectedProduct.value.product_id,
    selectedCourses: selectedCoursesForBatch
  });

  if (result.success > 0) {
    ElMessage.success(`批量下单成功！成功创建 ${result.success} 个订单，失败 ${result.failed} 个`);
    router.push('/order');
    return true;
  }

  ElMessage.error('批量下单失败，所有订单创建失败');
  return false;
};

// 处理单个订单创建
const handleSingleOrder = async (orderData: any) => {
  try {
    const response = await createOrder(orderData);

    // 检查订单创建结果
    if (response.successCount > 0) {
      if (response.failedCount === 0) {
        ElMessage.success(`订单创建成功！共创建${response.successCount}个订单，所有课程已提交`);
      } else {
        ElMessage.success(`订单部分成功！${response.successCount}个成功，${response.failedCount}个失败`);
      }
      router.push('/order');
      return true;
    }

    // 兼容旧的单订单格式
    if (response.orderId) {
      ElMessage.success(`订单创建成功！订单号：${response.orderNo}`);
      router.push('/order');
      return true;
    }

    // 如果没有成功标识，说明创建失败
    ElMessage.error(response.message || '订单创建失败');
    return false;
  } catch (error: any) {
    // 捕获API调用异常，这里会包含后端返回的错误信息
    const errorMessage = extractErrorMessage(error, '订单创建失败');
    ElMessage.error(errorMessage);
    return false;
  }
};

// 确认下单对话框
const confirmOrder = async () => {
  await ElMessageBox.confirm(
    `确认下单？\n商品：${selectedProduct.value.product_name}\n课程数量：${selectedCourses.value.length} 门\n总金额：¥${orderSummary.value?.totalAmount}`,
    '确认下单',
    {
      type: 'warning',
      confirmButtonText: '确认下单',
      cancelButtonText: '取消'
    }
  );
};

// 主下单处理函数
const handleSubmitOrder = async () => {
  if (!canSubmit.value) {
    ElMessage.warning('请完善订单信息');
    return;
  }

  try {
    await confirmOrder();

    if (!isDestroyed.value) {
      submitting.value = true;
    }

    if (queryMode.value === 'single') {
      // 单个查询模式
      try {
        const orderData = buildSingleOrderData();
        await handleSingleOrder(orderData);
      } catch (error: any) {
        // 改进错误处理：优先显示后端返回的具体错误信息
        const errorMessage = extractErrorMessage(error, '下单失败');
        ElMessage.error(errorMessage);
      }
    } else {
      // 批量查询模式
      try {
        await handleBatchOrder();
      } catch (error: any) {
        const errorMessage = extractErrorMessage(error, '批量下单请求失败');
        ElMessage.error(errorMessage);
      }
    }
  } catch (error: any) {
    if (error !== 'cancel') {
      const errorMessage = extractErrorMessage(error, '订单创建失败');
      ElMessage.error(errorMessage);
    }
  } finally {
    if (!isDestroyed.value) {
      submitting.value = false;
    }
  }
};

// 监听批量账号文本变化
watch(
  batchAccountsText,
  () => {
    if (queryMode.value === 'batch') {
      parseBatchAccounts();
    }
  },
  { immediate: true }
);

// 监听单个账号文本变化
watch(
  singleAccountText,
  () => {
    if (queryMode.value === 'single') {
      parseSingleAccount();
    }
  },
  { immediate: true }
);

// 初始化
onMounted(async () => {
  await loadCategories();
  await loadProducts();

  // 初始化移动端检测
  checkMobile();
  window.addEventListener('resize', checkMobile);
});

// 组件销毁时清理
onUnmounted(() => {
  isDestroyed.value = true;
  window.removeEventListener('resize', checkMobile);
  // 清理所有响应式数据
  selectedProduct.value = null;
  categories.value = [];
  products.value = [];
  queriedCourses.value = [];
  selectedCourses.value = [];
  searchResults.value = [];
  // 重置状态
  loadingProducts.value = false;
  querying.value = false;
  submitting.value = false;
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <!-- 页面标题 -->
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-medium">创建订单</span>
          <div class="flex gap-8px">
            <ElButton @click="handleReset">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              重置
            </ElButton>
          </div>
        </div>
      </template>

      <div class="grid grid-cols-1 gap-16px lg:grid-cols-2 md:gap-24px">
        <!-- 左侧：商品选择和账号信息 -->
        <div class="space-y-16px">
          <ElCard>
            <template #header>
              <span class="font-medium">商品选择</span>
            </template>

            <ElForm ref="productFormRef" :model="productForm" label-width="100px" class="space-y-16px">
              <ElFormItem label="服务分类">
                <ElSelect
                  v-model="productForm.categoryId"
                  placeholder="选择服务分类"
                  clearable
                  class="w-full"
                  @change="handleCategoryChange"
                >
                  <ElOption label="全部分类" :value="0" />
                  <ElOption
                    v-for="category in categories"
                    :key="category.category_id"
                    :label="`${category.name} (${category.product_count || 0})`"
                    :value="category.category_id"
                  />
                </ElSelect>
              </ElFormItem>

              <ElFormItem label="选择商品" prop="productId">
                <ElSelect
                  v-model="productForm.productId"
                  placeholder="请先选择分类，然后选择商品"
                  clearable
                  filterable
                  class="w-full"
                  :loading="loadingProducts"
                  @change="handleProductChange"
                >
                  <ElOption
                    v-for="product in filteredProducts"
                    :key="product.product_id"
                    :label="`${product.product_name} - ¥${product.price}/${product.service_type || '门'}`"
                    :value="product.product_id"
                  >
                    <div class="w-full flex items-center justify-between">
                      <div>
                        <div class="font-medium">{{ product.product_name }}</div>
                        <div class="text-xs text-gray-500">{{ product.platform_type }}</div>
                      </div>
                      <div class="text-primary font-medium">
                        ¥{{ product.price }}/{{ product.service_type || '门' }}
                      </div>
                    </div>
                  </ElOption>
                </ElSelect>
              </ElFormItem>

              <!-- 商品详情显示 -->
              <ElFormItem v-if="selectedProduct">
                <ElAlert :title="`已选择：${selectedProduct.product_name}`" type="success" :closable="false" show-icon>
                  <div class="text-sm space-y-8px">
                    <div>
                      <strong>平台类型：</strong>
                      {{ selectedProduct.platform_type }}
                    </div>
                    <div>
                      <strong>服务价格：</strong>
                      ¥{{ selectedProduct.price }}/{{ selectedProduct.service_type || '门' }}
                    </div>
                    <div>
                      <strong>服务商：</strong>
                      {{ selectedProduct.provider_name }}
                    </div>
                    <div v-if="selectedProduct.description">
                      <strong>服务说明：</strong>
                      {{ selectedProduct.description }}
                    </div>
                  </div>
                </ElAlert>
              </ElFormItem>
            </ElForm>
          </ElCard>

          <ElCard>
            <template #header>
              <span class="font-medium">账号信息</span>
            </template>

            <ElForm
              ref="accountFormRef"
              :model="accountInfo"
              :rules="accountRules"
              label-width="100px"
              class="space-y-16px"
            >
              <ElFormItem label="查询模式">
                <ElRadioGroup v-model="queryMode" @change="handleQueryModeChange">
                  <ElRadio value="single">单个查询</ElRadio>
                  <ElRadio value="batch">批量查询</ElRadio>
                </ElRadioGroup>
              </ElFormItem>

              <!-- 单个查询模式 -->
              <template v-if="queryMode === 'single'">
                <ElFormItem label="账号信息">
                  <ElInput
                    v-model="singleAccountText"
                    placeholder="请按格式输入：学校名称 账号 密码 或 账号 密码（示例：清华大学 ********* password123）"
                    maxlength="200"
                    clearable
                  />
                </ElFormItem>

                <ElFormItem v-if="parsedSingleAccount || singleParseError">
                  <div class="space-y-8px">
                    <div class="flex gap-8px">
                      <ElTag v-if="parsedSingleAccount" type="success">账号解析成功</ElTag>
                      <ElTag v-if="singleParseError" type="danger">格式错误</ElTag>
                    </div>

                    <!-- 格式错误提示 -->
                    <div v-if="singleParseError">
                      <ElAlert title="格式错误" type="warning" :closable="false" show-icon>
                        <div class="text-xs">
                          {{ singleParseError }}
                        </div>
                      </ElAlert>
                    </div>

                    <!-- 解析成功显示 -->
                    <div v-if="parsedSingleAccount">
                      <ElAlert title="账号信息" type="success" :closable="false" show-icon>
                        <div class="text-xs space-y-2px">
                          <div>
                            <strong>学校：</strong>
                            {{ parsedSingleAccount.school }}
                          </div>
                          <div>
                            <strong>账号：</strong>
                            {{ parsedSingleAccount.username }}
                          </div>
                          <div>
                            <strong>密码：</strong>
                            {{ parsedSingleAccount.password.replace(/./g, '*') }}
                          </div>
                        </div>
                      </ElAlert>
                    </div>
                  </div>
                </ElFormItem>

                <ElFormItem label="备注信息">
                  <ElInput
                    v-model="accountInfo.remark"
                    type="textarea"
                    :rows="3"
                    placeholder="选填：特殊要求或备注信息"
                    maxlength="200"
                    show-word-limit
                  />
                </ElFormItem>
              </template>

              <!-- 批量查询模式 -->
              <template v-else>
                <ElFormItem label="账号列表">
                  <ElInput
                    v-model="batchAccountsText"
                    type="textarea"
                    :rows="isMobile ? 6 : 8"
                    placeholder="请按以下格式输入账号信息，每行一个账号：&#10;学校名称 账号 密码&#10;或&#10;账号 密码&#10;&#10;示例：&#10;清华大学 ********* password123&#10;北京大学 ********* password456&#10;********* password789"
                    maxlength="5000"
                    show-word-limit
                    :class="{ 'text-sm': isMobile }"
                  />
                </ElFormItem>

                <ElFormItem v-if="parsedBatchAccounts.length > 0 || batchParseErrors.length > 0">
                  <div class="space-y-8px">
                    <div class="flex gap-8px">
                      <ElTag v-if="parsedBatchAccounts.length > 0" type="success">
                        已解析 {{ parsedBatchAccounts.length }} 个账号
                      </ElTag>
                      <ElTag v-if="batchParseErrors.length > 0" type="danger">
                        {{ batchParseErrors.length }} 个格式错误
                      </ElTag>
                    </div>

                    <!-- 格式错误提示 -->
                    <div v-if="batchParseErrors.length > 0">
                      <ElAlert title="格式错误的行" type="warning" :closable="false" show-icon>
                        <div class="text-xs space-y-4px">
                          <div v-for="error in batchParseErrors.slice(0, 3)" :key="error.line">
                            第{{ error.line }}行: {{ error.text }} - {{ error.error }}
                          </div>
                          <div v-if="batchParseErrors.length > 3" class="text-gray-500">
                            还有 {{ batchParseErrors.length - 3 }} 个错误...
                          </div>
                        </div>
                      </ElAlert>
                    </div>
                  </div>
                </ElFormItem>

                <ElFormItem label="备注信息">
                  <ElInput
                    v-model="accountInfo.remark"
                    type="textarea"
                    :rows="2"
                    placeholder="选填：批量查询的备注信息"
                    maxlength="200"
                    show-word-limit
                  />
                </ElFormItem>
              </template>

              <ElFormItem>
                <ElButton
                  type="primary"
                  size="large"
                  :loading="querying"
                  :disabled="!canQuery"
                  class="w-full"
                  @click="handleQueryCourses"
                >
                  <template #icon>
                    <icon-ic-round-search class="text-icon" />
                  </template>
                  {{
                    querying
                      ? queryMode === 'batch'
                        ? '批量查课中...'
                        : '查课中...'
                      : queryMode === 'batch'
                        ? '批量查询课程'
                        : '查询课程'
                  }}
                </ElButton>
              </ElFormItem>
            </ElForm>
          </ElCard>
        </div>

        <!-- 右侧：查课结果和订单确认 -->
        <div class="space-y-16px">
          <ElCard>
            <template #header>
              <div class="flex items-center justify-between">
                <span class="font-medium">课程列表</span>
                <div class="flex gap-8px">
                  <ElButton v-if="queriedCourses.length > 0" size="small" @click="openCourseDrawer">
                    查看课程 ({{ queriedCourses.length }})
                  </ElButton>
                  <ElButton v-if="selectedCourses.length > 0" size="small" type="info" @click="clearSelection">
                    清空选择 ({{ selectedCourses.length }})
                  </ElButton>
                </div>
              </div>
            </template>

            <!-- 查课结果为空时的提示 -->
            <div v-if="!queriedCourses.length && !querying" class="py-32px text-center">
              <ElEmpty description="请先选择商品并输入账号信息，然后点击查询课程" :image-size="120" />
            </div>

            <!-- 简化的查课结果预览 -->
            <div v-else-if="queriedCourses.length > 0" class="space-y-12px">
              <div class="text-sm text-gray-600">
                <div class="flex items-center justify-between">
                  <span>找到 {{ queriedCourses.length }} 门课程，已选择 {{ selectedCourses.length }} 门</span>
                  <span v-if="queryMode === 'batch'" class="text-primary">
                    涉及 {{ uniqueAccountsList.length }} 个账号
                  </span>
                </div>
              </div>

              <!-- 选中课程预览 -->
              <div v-if="selectedCourses.length > 0" class="space-y-8px">
                <div class="text-sm text-gray-700 font-medium">已选择的课程：</div>
                <div class="max-h-200px overflow-y-auto space-y-4px">
                  <div
                    v-for="course in selectedCourses.slice(0, 10)"
                    :key="`${course.accountInfo?.username || 'single'}-${course.courseId}`"
                    class="flex items-center justify-between rounded bg-primary-50 p-8px text-sm"
                  >
                    <div class="min-w-0 flex-1">
                      <div class="truncate text-gray-900 font-medium">{{ course.courseName }}</div>
                      <div v-if="showCourseId" class="truncate text-xs text-gray-400">ID: {{ course.courseId }}</div>
                      <div v-if="queryMode === 'batch'" class="text-xs text-gray-500">
                        {{ course.accountInfo?.userRealName || course.accountInfo?.username }}
                      </div>
                    </div>
                    <ElButton size="small" text type="danger" @click="toggleCourse(course)">移除</ElButton>
                  </div>
                  <div v-if="selectedCourses.length > 10" class="text-center text-xs text-gray-500">
                    还有 {{ selectedCourses.length - 10 }} 门课程...
                  </div>
                </div>
              </div>
            </div>
          </ElCard>

          <ElCard>
            <template #header>
              <span class="font-medium">订单确认</span>
            </template>

            <div v-if="!orderSummary" class="py-32px text-center text-gray-500">请先选择商品和课程</div>

            <div v-else class="space-y-16px">
              <!-- 订单摘要 -->
              <div class="rounded-lg bg-gray-50 p-16px space-y-8px">
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">选择商品：</span>
                  <span class="font-medium">{{ selectedProduct?.product_name }}</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">平台类型：</span>
                  <span>{{ selectedProduct?.platform_type }}</span>
                </div>
                <div v-if="queryMode === 'single'" class="flex justify-between text-sm">
                  <span class="text-gray-600">学校名称：</span>
                  <span>{{ accountInfo.schoolName || '未填写' }}</span>
                </div>
                <div v-if="queryMode === 'single'" class="flex justify-between text-sm">
                  <span class="text-gray-600">平台账号：</span>
                  <span>{{ accountInfo.platformAccount }}</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">选择课程：</span>
                  <span class="text-primary font-medium">{{ selectedCourses.length }} 门</span>
                </div>
                <div class="flex justify-between text-sm">
                  <span class="text-gray-600">单价：</span>
                  <span>¥{{ selectedProduct?.price }}/{{ selectedProduct?.service_type || '门' }}</span>
                </div>
                <div class="border-t pt-8px">
                  <div class="flex justify-between text-lg font-bold">
                    <span>总金额：</span>
                    <span class="text-primary">¥{{ orderSummary.totalAmount }}</span>
                  </div>
                </div>
              </div>

              <!-- 提交按钮 -->
              <ElButton
                type="primary"
                size="large"
                :loading="submitting"
                :disabled="!canSubmit"
                class="w-full"
                @click="handleSubmitOrder"
              >
                <template #icon>
                  <icon-ic-round-check class="text-icon" />
                </template>
                {{ submitting ? '提交中...' : '确认下单' }}
              </ElButton>
            </div>
          </ElCard>
        </div>
      </div>
    </ElCard>
  </div>

  <!-- 课程选择抽屉 -->
  <ElDrawer
    v-model="courseDrawerVisible"
    title="课程选择"
    :size="isMobile ? '90%' : '60%'"
    direction="rtl"
    :before-close="closeCourseDrawer"
  >
    <template #header>
      <div class="w-full">
        <div class="mb-8px flex items-center justify-between">
          <span class="text-lg font-medium">课程选择</span>
          <div class="text-sm text-gray-500">已选择 {{ selectedCourses.length }}/{{ queriedCourses.length }}</div>
        </div>
        <!-- 移动端将开关放在下方 -->
        <div v-if="!isMobile" class="flex items-center gap-12px">
          <!-- 同名课程自动勾选开关 -->
          <div v-if="queryMode === 'batch'" class="flex items-center gap-8px">
            <ElSwitch v-model="autoSelectSameCourse" size="small" active-text="同名课程联动" inactive-text="" />
          </div>
          <!-- 课程ID显示控制开关 -->
          <div class="flex items-center gap-8px">
            <ElSwitch v-model="showCourseId" size="small" active-text="显示课程ID" inactive-text="" />
          </div>
        </div>
        <!-- 移动端开关布局 -->
        <div v-else class="flex flex-col gap-8px">
          <div v-if="queryMode === 'batch'" class="flex items-center gap-8px">
            <ElSwitch v-model="autoSelectSameCourse" size="small" />
            <span class="text-sm">同名课程联动</span>
          </div>
          <div class="flex items-center gap-8px">
            <ElSwitch v-model="showCourseId" size="small" />
            <span class="text-sm">显示课程ID</span>
          </div>
        </div>
      </div>
    </template>

    <div class="space-y-16px">
      <!-- 搜索和筛选工具栏 -->
      <div class="search-toolbar">
        <div class="mb-12px flex flex-col gap-8px sm:flex-row sm:gap-12px">
          <ElInput
            v-model="searchKeyword"
            placeholder="搜索课程名称、ID或教师..."
            clearable
            class="flex-1"
            :size="isMobile ? 'default' : 'default'"
            @input="handleSearch"
          >
            <template #prefix>
              <ElIcon><Search /></ElIcon>
            </template>
          </ElInput>
          <ElButton
            v-if="searchKeyword && searchResults.length > 0"
            type="primary"
            :size="isMobile ? 'default' : 'default'"
            class="w-full sm:w-auto"
            @click="selectAllSearchResults"
          >
            一键选择 ({{ searchResults.length }})
          </ElButton>
        </div>

        <!-- 账号筛选标签 -->
        <div v-if="queryMode === 'batch'" class="flex flex-wrap gap-8px">
          <ElTag
            v-for="account in uniqueAccountsList"
            :key="account.username"
            :type="selectedAccountFilter === account.username ? 'primary' : 'info'"
            :effect="selectedAccountFilter === account.username ? 'dark' : 'plain'"
            class="cursor-pointer"
            @click="toggleAccountFilter(account.username)"
          >
            {{ account.username }}
            ({{ getAccountCourseCount(account.username) }})
          </ElTag>
          <ElTag v-if="selectedAccountFilter" type="danger" class="cursor-pointer" @click="clearAccountFilter">
            清除筛选
          </ElTag>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-600">
          <span>找到 {{ queriedCourses.length }} 门课程</span>
          <span v-if="filteredCourses.length !== queriedCourses.length">，筛选后 {{ filteredCourses.length }} 门</span>
          <span v-if="queryMode === 'batch'">，涉及 {{ uniqueAccountsList.length }} 个账号</span>
        </div>
        <div class="flex gap-8px">
          <ElButton size="small" :disabled="selectedCourses.length === filteredCourses.length" @click="handleSelectAll">
            全选
          </ElButton>
          <ElButton size="small" type="info" :disabled="selectedCourses.length === 0" @click="clearSelection">
            清空
          </ElButton>
        </div>
      </div>

      <!-- 课程列表 -->
      <div class="course-list">
        <!-- 批量模式：按账号分组 -->
        <div v-if="queryMode === 'batch'" class="space-y-12px">
          <div
            v-for="account in filteredAccountGroups"
            :key="account.username"
            class="account-group overflow-hidden border border-gray-200 rounded-lg"
          >
            <!-- 账号标题 -->
            <div
              class="account-header flex cursor-pointer items-center justify-between bg-gray-50 p-12px hover:bg-gray-100"
              @click="toggleAccountExpand(account.username)"
            >
              <div class="flex items-center gap-12px">
                <div
                  class="h-16px w-16px flex cursor-pointer items-center justify-center transition-transform"
                  :class="{ 'rotate-90': isAccountExpanded(account.username) }"
                >
                  <span class="text-gray-400">▶</span>
                </div>
                <ElCheckbox
                  :model-value="isAccountFullySelected(account.username)"
                  :indeterminate="isAccountPartiallySelected(account.username)"
                  @change="toggleAccountSelection(account.username)"
                  @click.stop
                />
                <div>
                  <div class="text-sm text-gray-900 font-medium">
                    {{ account.username }}
                  </div>
                  <div class="text-xs text-gray-500">
                    <span v-if="account.userRealName">{{ account.userRealName }} |</span>
                    {{ account.school }} | {{ account.courses.length }} 门课程
                  </div>
                </div>
              </div>
              <ElTag size="small" type="info">
                已选 {{ getSelectedCountForAccount(account.username) }}/{{ account.courses.length }}
              </ElTag>
            </div>

            <!-- 课程列表（可折叠） -->
            <div v-show="isAccountExpanded(account.username)" class="border-t border-gray-200">
              <div class="p-8px space-y-4px">
                <div
                  v-for="course in account.courses"
                  :key="`${account.username}-${course.courseId}`"
                  class="course-item flex cursor-pointer items-center gap-8px rounded p-8px hover:bg-gray-50"
                  :class="{ 'bg-primary-50': isSelected(course) }"
                  @click="toggleCourse(course)"
                >
                  <ElCheckbox :model-value="isSelected(course)" @change="toggleCourse(course)" @click.stop />
                  <div class="min-w-0 flex-1">
                    <div class="truncate text-sm text-gray-900 font-medium">{{ course.courseName }}</div>
                    <div v-if="showCourseId" class="mb-1 truncate text-xs text-gray-400">ID: {{ course.courseId }}</div>
                    <div class="flex items-center gap-8px text-xs text-gray-500">
                      <span v-if="course.teacher">{{ course.teacher }}</span>
                      <span v-if="course.class">{{ course.class }}</span>
                      <ElTag v-if="course.progress" size="small" type="success">{{ course.progress }}</ElTag>
                      <ElTag
                        v-if="course.state"
                        size="small"
                        :type="course.state.includes('已结课') ? 'warning' : 'primary'"
                      >
                        {{ course.state }}
                      </ElTag>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 单个模式：普通列表 -->
        <div v-else class="space-y-4px">
          <div
            v-for="course in filteredCourses"
            :key="course.courseId"
            class="course-item flex cursor-pointer items-center gap-8px rounded p-8px hover:bg-gray-50"
            :class="{ 'bg-primary-50': isSelected(course) }"
            @click="toggleCourse(course)"
          >
            <ElCheckbox :model-value="isSelected(course)" @change="toggleCourse(course)" @click.stop />
            <div class="min-w-0 flex-1">
              <div class="truncate text-sm text-gray-900 font-medium">{{ course.courseName }}</div>
              <div v-if="showCourseId" class="mb-1 truncate text-xs text-gray-400">ID: {{ course.courseId }}</div>
              <div class="flex items-center gap-8px text-xs text-gray-500">
                <span v-if="course.teacher">{{ course.teacher }}</span>
                <span v-if="course.class">{{ course.class }}</span>
                <ElTag v-if="course.progress" size="small" type="success">{{ course.progress }}</ElTag>
                <ElTag v-if="course.state" size="small" :type="course.state.includes('已结课') ? 'warning' : 'primary'">
                  {{ course.state }}
                </ElTag>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex items-center justify-between">
        <div class="text-sm text-gray-600">已选择 {{ selectedCourses.length }} 门课程</div>
        <div class="flex gap-8px">
          <ElButton @click="closeCourseDrawer">关闭</ElButton>
          <ElButton type="primary" :disabled="selectedCourses.length === 0" @click="closeCourseDrawer">
            确认选择
          </ElButton>
        </div>
      </div>
    </template>
  </ElDrawer>
</template>

<style scoped>
/* 搜索工具栏样式 */
.search-toolbar {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 16px;
}

/* 账号分组样式 */
.account-group {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.account-header {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  transition: background-color 0.2s;
}

.account-header:hover {
  background-color: #f3f4f6;
}

/* 课程项样式 */
.course-item {
  transition: all 0.2s;
  border: 1px solid transparent;
}

.course-item:hover {
  background-color: #f9fafb !important;
  border-color: #e5e7eb;
}

.course-item.bg-primary-50 {
  background-color: var(--el-color-primary-light-9) !important;
  border-color: var(--el-color-primary-light-6);
}

/* 抽屉内容样式 */
.course-list {
  max-height: 60vh;
  overflow-y: auto;
}

/* 展开图标动画 */
.rotate-90 {
  transform: rotate(90deg);
}

/* 课程卡片样式优化 */
.border-primary {
  border-color: var(--el-color-primary) !important;
}

.bg-primary-50 {
  background-color: var(--el-color-primary-light-9) !important;
}

/* 标签样式 */
.el-tag {
  margin-right: 4px;
  margin-bottom: 4px;
}

.el-tag.cursor-pointer:hover {
  opacity: 0.8;
}

/* 滚动条样式 */
.max-h-500px::-webkit-scrollbar {
  width: 6px;
}

.max-h-500px::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.max-h-500px::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.max-h-500px::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式布局优化 */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr !important;
  }

  .gap-24px {
    gap: 16px !important;
  }

  .p-16px {
    padding: 12px !important;
  }

  .search-toolbar {
    padding: 12px;
  }

  .account-header {
    padding: 8px;
  }

  .flex.gap-12px {
    flex-direction: column;
    gap: 8px;
  }

  .flex.gap-8px.flex-wrap {
    gap: 4px;
  }
}

/* 滚动区域优化 */
.max-h-400px {
  max-height: 400px;
  overflow-y: auto;
}

/* 确保内容区域正常滚动 */
.space-y-8px > * + * {
  margin-top: 8px;
}

.space-y-16px > * + * {
  margin-top: 16px;
}

/* 移动端表单优化 */
@media (max-width: 640px) {
  .w-240px {
    width: 100% !important;
  }

  .w-120px {
    width: 100% !important;
  }
}
</style>
