<script setup lang="ts">
import { computed, nextTick, reactive, ref, watch } from 'vue';
import { ElForm } from 'element-plus';
import { Lock, Phone, School, User } from '@element-plus/icons-vue';

// Props
interface Props {
  accountInfo?: any;
  platformType?: string;
}

const props = withDefaults(defineProps<Props>(), {
  accountInfo: () => ({}),
  platformType: ''
});

// Emits
const emit = defineEmits<{
  'update:accountInfo': [info: any];
  formValid: [valid: boolean];
}>();

// 表单引用
const formRef = ref<InstanceType<typeof ElForm>>();

// 表单数据
const formData = reactive({
  school: '',
  username: '',
  password: '',
  phone: '',
  remark: ''
});

// 表单验证规则
const formRules = {
  school: [
    { required: true, message: '请输入学校名称', trigger: 'blur' },
    { min: 2, max: 50, message: '学校名称长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  username: [
    { required: true, message: '请输入平台账号', trigger: 'blur' },
    { min: 3, max: 30, message: '账号长度在 3 到 30 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入平台密码', trigger: 'blur' },
    { min: 6, max: 50, message: '密码长度在 6 到 50 个字符', trigger: 'blur' }
  ],
  phone: [{ pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }],
  remark: [{ max: 200, message: '备注信息不能超过 200 个字符', trigger: 'blur' }]
};

// 平台提示信息
const platformTips = computed(() => {
  const tips: Record<string, string[]> = {
    online: [
      '请确保账号密码正确，避免因登录失败导致查课失败',
      '如果账号有验证码或其他安全验证，请提前关闭',
      '建议在非高峰时段进行查课，提高成功率'
    ],
    offline: [
      '离线平台需要特殊处理，请确保提供准确的学校信息',
      '部分离线平台可能需要额外的验证信息',
      '处理时间可能比在线平台稍长'
    ],
    zhihuishu: ['智慧树平台请使用学号登录', '密码通常为身份证后6位或自设密码', '如遇登录问题，请联系客服'],
    chaoxing: ['超星学习通支持手机号和学号登录', '请确保账号已绑定学校信息', '建议关闭短信验证功能']
  };

  return tips[props.platformType] || tips.online;
});

// 表单验证状态
const isFormValid = ref(false);

// 方法
const handleFormValidate = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    isFormValid.value = true;
    emit('formValid', true);
  } catch {
    isFormValid.value = false;
    emit('formValid', false);
  }
};

const validateForm = async () => {
  await nextTick();
  handleFormValidate();
};

// 监听表单数据变化
watch(
  formData,
  newData => {
    emit('update:accountInfo', { ...newData });
    validateForm();
  },
  { deep: true }
);

// 监听props变化
watch(
  () => props.accountInfo,
  newInfo => {
    if (newInfo) {
      Object.assign(formData, newInfo);
    }
  },
  { immediate: true }
);

// 暴露方法给父组件
defineExpose({
  validate: handleFormValidate,
  resetForm: () => {
    formRef.value?.resetFields();
  }
});
</script>

<template>
  <div class="account-form">
    <div class="form-header">
      <h3>输入账号信息</h3>
      <p class="header-description">请输入您的学习平台账号信息</p>
    </div>

    <ElForm ref="formRef" :model="formData" :rules="formRules" label-width="100px" @validate="handleFormValidate">
      <div class="form-grid">
        <!-- 学校名称 -->
        <ElFormItem label="学校名称" prop="school" required>
          <ElInput v-model="formData.school" placeholder="请输入学校名称" clearable>
            <template #prefix>
              <ElIcon><School /></ElIcon>
            </template>
          </ElInput>
        </ElFormItem>

        <!-- 平台账号 -->
        <ElFormItem label="平台账号" prop="username" required>
          <ElInput v-model="formData.username" placeholder="请输入平台账号" clearable>
            <template #prefix>
              <ElIcon><User /></ElIcon>
            </template>
          </ElInput>
        </ElFormItem>

        <!-- 平台密码 -->
        <ElFormItem label="平台密码" prop="password" required>
          <ElInput v-model="formData.password" type="password" placeholder="请输入平台密码" show-password clearable>
            <template #prefix>
              <ElIcon><Lock /></ElIcon>
            </template>
          </ElInput>
        </ElFormItem>

        <!-- 手机号码（可选） -->
        <ElFormItem label="手机号码" prop="phone">
          <ElInput v-model="formData.phone" placeholder="请输入手机号码（可选）" clearable>
            <template #prefix>
              <ElIcon><Phone /></ElIcon>
            </template>
          </ElInput>
        </ElFormItem>
      </div>

      <!-- 备注信息 -->
      <ElFormItem label="备注信息" prop="remark">
        <ElInput
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息（可选）"
          maxlength="200"
          show-word-limit
        />
      </ElFormItem>
    </ElForm>

    <!-- 平台提示信息 -->
    <div v-if="platformType" class="platform-tips">
      <h4>{{ platformType }} 平台注意事项</h4>
      <ul class="tips-list">
        <li v-for="tip in platformTips" :key="tip">{{ tip }}</li>
      </ul>
    </div>

    <!-- 安全提示 -->
    <div class="security-notice">
      <ElAlert title="安全提示" type="info" :closable="false" show-icon>
        <template #default>
          <p>• 您的账号密码仅用于课程查询和代刷服务，我们承诺不会泄露您的个人信息</p>
          <p>• 建议使用专门的学习账号，避免使用重要账号</p>
          <p>• 服务完成后，建议您及时修改密码</p>
        </template>
      </ElAlert>
    </div>
  </div>
</template>

<style scoped>
.account-form {
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-header h3 {
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.header-description {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
}

.platform-tips {
  margin: 24px 0;
  padding: 16px;
  background: var(--el-color-info-light-9);
  border-radius: 6px;
  border-left: 4px solid var(--el-color-info);
}

.platform-tips h4 {
  color: var(--el-color-info);
  margin: 0 0 12px 0;
  font-size: 14px;
}

.tips-list {
  margin: 0;
  padding-left: 20px;
  color: var(--el-text-color-regular);
  font-size: 13px;
}

.tips-list li {
  margin-bottom: 4px;
  line-height: 1.5;
}

.security-notice {
  margin-top: 24px;
}

.security-notice :deep(.el-alert__content) p {
  margin: 2px 0;
  font-size: 13px;
  line-height: 1.5;
}
</style>
