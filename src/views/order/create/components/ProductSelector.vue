<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Check, Search } from '@element-plus/icons-vue';
import { fetchProductCategories, fetchProductList } from '@/service/api/product';

// Props
interface Props {
  selectedProduct?: any;
}

const props = withDefaults(defineProps<Props>(), {
  selectedProduct: null
});

// Emits
const emit = defineEmits<{
  'update:selectedProduct': [product: any];
  productSelect: [product: any];
}>();

// 响应式数据
const loading = ref(false);
const categories = ref<any[]>([]);
const products = ref<any[]>([]);
const selectedCategoryId = ref<number>(0);
const selectedPlatformType = ref('');
const searchKeyword = ref('');

// 计算属性
const platformTypes = computed(() => {
  const types = new Set(products.value.map(p => p.platform_type));
  return Array.from(types);
});

const filteredProducts = computed(() => {
  let filtered = products.value;

  // 分类筛选
  if (selectedCategoryId.value && selectedCategoryId.value !== 0) {
    filtered = filtered.filter(p => p.category_id === selectedCategoryId.value);
  }

  // 平台类型筛选
  if (selectedPlatformType.value) {
    filtered = filtered.filter(p => p.platform_type === selectedPlatformType.value);
  }

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(
      p =>
        p.product_name.toLowerCase().includes(keyword) ||
        (p.description && p.description.toLowerCase().includes(keyword))
    );
  }

  return filtered;
});

// 方法
const loadCategories = async () => {
  try {
    const response = await fetchProductCategories();
    categories.value = response;
  } catch (error) {
    console.error('加载分类失败:', error);
    ElMessage.error('加载分类失败');
  }
};

const loadProducts = async () => {
  loading.value = true;
  try {
    const response = await fetchProductList({
      page: 1,
      pageSize: 100, // 加载所有商品
      status: 1 // 只加载启用的商品
    });

    products.value = response.list;
  } catch (error) {
    console.error('加载商品失败:', error);
    ElMessage.error('加载商品失败');
  } finally {
    loading.value = false;
  }
};

const handleCategoryChange = () => {
  console.log('分类变更:', selectedCategoryId.value);
};

const handlePlatformChange = () => {
  console.log('平台类型变更:', selectedPlatformType.value);
};

const handleSearch = () => {
  console.log('搜索:', searchKeyword.value);
};

const handleProductSelect = (product: any) => {
  if (product.status !== 1) {
    ElMessage.warning('该商品已禁用，无法选择');
    return;
  }

  emit('update:selectedProduct', product);
  emit('productSelect', product);
  console.log('选择商品:', product);
};

// 监听props变化
watch(
  () => props.selectedProduct,
  newProduct => {
    console.log('商品选择变更:', newProduct);
  }
);

// 页面初始化
onMounted(async () => {
  await Promise.all([loadCategories(), loadProducts()]);
});
</script>

<template>
  <div class="product-selector">
    <div class="selector-header">
      <h3>选择服务商品</h3>
      <p class="header-description">请选择您需要的网课代刷服务</p>
    </div>

    <!-- 分类筛选 -->
    <div class="category-section">
      <h4>服务分类</h4>
      <div class="category-tabs">
        <ElRadioGroup v-model="selectedCategoryId" @change="handleCategoryChange">
          <ElRadioButton :value="0">全部</ElRadioButton>
          <ElRadioButton v-for="category in categories" :key="category.category_id" :value="category.category_id">
            {{ category.name }} ({{ category.product_count }})
          </ElRadioButton>
        </ElRadioGroup>
      </div>
    </div>

    <!-- 平台类型筛选 -->
    <div class="platform-section">
      <h4>平台类型</h4>
      <div class="platform-filters">
        <ElSelect v-model="selectedPlatformType" placeholder="选择平台类型" clearable @change="handlePlatformChange">
          <ElOption label="全部平台" value="" />
          <ElOption v-for="platform in platformTypes" :key="platform" :label="platform" :value="platform" />
        </ElSelect>

        <ElInput v-model="searchKeyword" placeholder="搜索商品名称" clearable @input="handleSearch">
          <template #prefix>
            <ElIcon><Search /></ElIcon>
          </template>
        </ElInput>
      </div>
    </div>

    <!-- 商品列表 -->
    <div class="products-section">
      <div v-if="loading" class="loading-container">
        <ElSkeleton :rows="3" animated />
      </div>

      <div v-else-if="filteredProducts.length === 0" class="empty-container">
        <ElEmpty description="暂无商品" />
      </div>

      <div v-else class="products-grid">
        <div
          v-for="product in filteredProducts"
          :key="product.product_id"
          class="product-card"
          :class="{
            selected: selectedProduct?.product_id === product.product_id,
            disabled: product.status !== 1
          }"
          @click="handleProductSelect(product)"
        >
          <div class="product-header">
            <h5 class="product-name">{{ product.product_name }}</h5>
            <ElTag :type="product.status === 1 ? 'success' : 'danger'" size="small">
              {{ product.status === 1 ? '可用' : '禁用' }}
            </ElTag>
          </div>

          <div class="product-info">
            <div class="info-item">
              <span class="label">分类：</span>
              <span class="value">{{ product.category_name }}</span>
            </div>
            <div class="info-item">
              <span class="label">平台：</span>
              <span class="value">{{ product.platform_type }}</span>
            </div>
            <div class="info-item">
              <span class="label">服务商：</span>
              <span class="value">{{ product.provider_name || '未配置' }}</span>
            </div>
          </div>

          <div class="product-description">
            <p>{{ product.description || '暂无描述' }}</p>
          </div>

          <div class="product-footer">
            <div class="price-info">
              <span class="price">¥{{ product.price }}</span>
              <span class="unit">/课程</span>
            </div>
            <div class="service-type">
              <ElTag size="small" type="info">{{ product.service_type }}</ElTag>
            </div>
          </div>

          <!-- 选中指示器 -->
          <div v-if="selectedProduct?.product_id === product.product_id" class="selected-indicator">
            <ElIcon color="#409EFF"><Check /></ElIcon>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.product-selector {
  padding: 20px;
}

.selector-header {
  text-align: center;
  margin-bottom: 30px;
}

.selector-header h3 {
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.header-description {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.category-section,
.platform-section {
  margin-bottom: 24px;
}

.category-section h4,
.platform-section h4 {
  color: var(--el-text-color-primary);
  margin-bottom: 12px;
  font-size: 16px;
}

.category-tabs {
  display: flex;
  justify-content: center;
}

.platform-filters {
  display: flex;
  gap: 16px;
  align-items: center;
}

.platform-filters .el-select {
  width: 200px;
}

.platform-filters .el-input {
  width: 300px;
}

.products-section {
  margin-top: 32px;
}

.loading-container,
.empty-container {
  padding: 40px;
  text-align: center;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
}

.product-card {
  position: relative;
  border: 2px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--el-bg-color);
}

.product-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.product-card.selected {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.product-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.product-name {
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 500;
  margin: 0;
}

.product-info {
  margin-bottom: 12px;
}

.info-item {
  display: flex;
  margin-bottom: 4px;
  font-size: 14px;
}

.info-item .label {
  color: var(--el-text-color-regular);
  width: 60px;
}

.info-item .value {
  color: var(--el-text-color-primary);
}

.product-description {
  margin-bottom: 16px;
}

.product-description p {
  color: var(--el-text-color-regular);
  font-size: 13px;
  line-height: 1.5;
  margin: 0;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-info {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.price {
  color: var(--el-color-danger);
  font-size: 18px;
  font-weight: 600;
}

.unit {
  color: var(--el-text-color-regular);
  font-size: 12px;
}

.selected-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  width: 24px;
  height: 24px;
  background: var(--el-color-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
