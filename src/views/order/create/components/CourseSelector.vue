<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { Medal, Search, User } from '@element-plus/icons-vue';

// Props
interface Props {
  courses: any[];
  product?: any;
  selectedCourses?: any[];
}

const props = withDefaults(defineProps<Props>(), {
  courses: () => [],
  product: null,
  selectedCourses: () => []
});

// Emits
const emit = defineEmits<{
  'update:selectedCourses': [courses: any[]];
  selectionChange: [courses: any[]];
}>();

// 响应式数据
const searchKeyword = ref('');
const statusFilter = ref('');
const progressFilter = ref('');
const internalSelectedCourses = ref<any[]>([]);

// 计算属性
const filteredCourses = computed(() => {
  let filtered = props.courses;

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(
      course => course.courseName?.toLowerCase().includes(keyword) || course.courseId?.toLowerCase().includes(keyword)
    );
  }

  // 状态筛选
  if (statusFilter.value) {
    filtered = filtered.filter(course => course.status === statusFilter.value);
  }

  // 进度筛选
  if (progressFilter.value) {
    switch (progressFilter.value) {
      case '0':
        filtered = filtered.filter(course => (course.progress || 0) === 0);
        break;
      case '1-50':
        filtered = filtered.filter(course => {
          const p = course.progress || 0;
          return p > 0 && p <= 50;
        });
        break;
      case '51-99':
        filtered = filtered.filter(course => {
          const p = course.progress || 0;
          return p > 50 && p < 100;
        });
        break;
      case '100':
        filtered = filtered.filter(course => (course.progress || 0) >= 100);
        break;
    }
  }

  return filtered;
});

const totalAmount = computed(() => {
  const price = props.product?.price || 0;
  return (price * internalSelectedCourses.value.length).toFixed(2);
});

// 方法
const isSelected = (course: any) => {
  return internalSelectedCourses.value.some(c => c.courseId === course.courseId);
};

const toggleCourse = (course: any) => {
  const index = internalSelectedCourses.value.findIndex(c => c.courseId === course.courseId);

  if (index > -1) {
    internalSelectedCourses.value.splice(index, 1);
  } else {
    internalSelectedCourses.value.push(course);
  }

  emitChange();
};

const selectAll = () => {
  internalSelectedCourses.value = [...filteredCourses.value];
  emitChange();
};

const clearSelection = () => {
  internalSelectedCourses.value = [];
  emitChange();
};

const selectByStatus = (status: string) => {
  const coursesToSelect = filteredCourses.value.filter(course => {
    if (status === '未完成') {
      return (course.progress || 0) < 100;
    }
    return course.status === status;
  });

  internalSelectedCourses.value = coursesToSelect;
  emitChange();
};

const emitChange = () => {
  emit('update:selectedCourses', internalSelectedCourses.value);
  emit('selectionChange', internalSelectedCourses.value);
};

const getStatusType = (status: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    未开始: 'info',
    进行中: 'warning',
    已完成: 'success',
    未完成: 'danger'
  };
  return typeMap[status] || 'info';
};

const getProgressColor = (progress: number) => {
  if (progress >= 100) return '#67c23a';
  if (progress >= 80) return '#e6a23c';
  if (progress >= 60) return '#f56c6c';
  return '#909399';
};

// 监听props变化
watch(
  () => props.selectedCourses,
  newSelected => {
    internalSelectedCourses.value = [...newSelected];
  },
  { immediate: true }
);
</script>

<template>
  <div class="course-selector">
    <div class="selector-header">
      <h3>选择课程</h3>
      <p class="header-description">请选择需要代刷的课程</p>
    </div>

    <!-- 选择统计 -->
    <div class="selection-summary">
      <div class="summary-info">
        <span class="total-courses">共 {{ courses.length }} 门课程</span>
        <span class="selected-count">已选择 {{ selectedCourses.length }} 门</span>
      </div>
      <div class="batch-actions">
        <ElButton size="small" @click="selectAll">全选</ElButton>
        <ElButton size="small" @click="clearSelection">清空</ElButton>
        <ElButton size="small" @click="selectByStatus('未完成')">选择未完成</ElButton>
      </div>
    </div>

    <!-- 筛选工具 -->
    <div class="filter-tools">
      <ElInput v-model="searchKeyword" placeholder="搜索课程名称" clearable class="search-input">
        <template #prefix>
          <ElIcon><Search /></ElIcon>
        </template>
      </ElInput>

      <ElSelect v-model="statusFilter" placeholder="筛选状态" clearable class="status-filter">
        <ElOption label="全部状态" value="" />
        <ElOption label="未开始" value="未开始" />
        <ElOption label="进行中" value="进行中" />
        <ElOption label="已完成" value="已完成" />
        <ElOption label="未完成" value="未完成" />
      </ElSelect>

      <ElSelect v-model="progressFilter" placeholder="筛选进度" clearable class="progress-filter">
        <ElOption label="全部进度" value="" />
        <ElOption label="0%" value="0" />
        <ElOption label="1-50%" value="1-50" />
        <ElOption label="51-99%" value="51-99" />
        <ElOption label="100%" value="100" />
      </ElSelect>
    </div>

    <!-- 课程列表 -->
    <div class="courses-list">
      <div v-if="filteredCourses.length === 0" class="empty-state">
        <ElEmpty description="没有找到符合条件的课程" />
      </div>

      <div v-else class="courses-grid">
        <div
          v-for="course in filteredCourses"
          :key="course.courseId"
          class="course-card"
          :class="{
            selected: isSelected(course),
            completed: course.progress >= 100
          }"
          @click="toggleCourse(course)"
        >
          <!-- 选择指示器 -->
          <div class="selection-indicator">
            <ElCheckbox :model-value="isSelected(course)" @change="toggleCourse(course)" @click.stop />
          </div>

          <!-- 课程信息 -->
          <div class="course-info">
            <h4 class="course-name" :title="course.courseName">
              {{ course.courseName }}
            </h4>

            <div class="course-meta">
              <div class="meta-item">
                <span class="label">课程ID：</span>
                <span class="value">{{ course.courseId }}</span>
              </div>
              <div class="meta-item">
                <span class="label">状态：</span>
                <ElTag :type="getStatusType(course.status)" size="small">
                  {{ course.status || '未知' }}
                </ElTag>
              </div>
            </div>

            <!-- 进度条 -->
            <div class="progress-section">
              <div class="progress-header">
                <span class="progress-label">学习进度</span>
                <span class="progress-value">{{ course.progress || 0 }}%</span>
              </div>
              <ElProgress
                :percentage="course.progress || 0"
                :color="getProgressColor(course.progress || 0)"
                :stroke-width="6"
              />
            </div>

            <!-- 额外信息 -->
            <div v-if="course.teacher || course.credit" class="extra-info">
              <div v-if="course.teacher" class="info-item">
                <ElIcon><User /></ElIcon>
                <span>{{ course.teacher }}</span>
              </div>
              <div v-if="course.credit" class="info-item">
                <ElIcon><Medal /></ElIcon>
                <span>{{ course.credit }} 学分</span>
              </div>
            </div>
          </div>

          <!-- 价格信息 -->
          <div class="price-info">
            <span class="price">¥{{ product?.price || 0 }}</span>
            <span class="unit">/门</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 选择汇总 -->
    <div v-if="selectedCourses.length > 0" class="selection-footer">
      <div class="footer-summary">
        <span class="summary-text">已选择 {{ selectedCourses.length }} 门课程， 总计：¥{{ totalAmount }}</span>
      </div>
      <div class="footer-actions">
        <ElButton type="text" @click="clearSelection">清空选择</ElButton>
      </div>
    </div>
  </div>
</template>

<style scoped>
.course-selector {
  padding: 20px;
}

.selector-header {
  text-align: center;
  margin-bottom: 24px;
}

.selector-header h3 {
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.header-description {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.selection-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: var(--el-color-primary-light-9);
  border-radius: 6px;
}

.summary-info {
  display: flex;
  gap: 20px;
  font-size: 14px;
}

.total-courses {
  color: var(--el-text-color-regular);
}

.selected-count {
  color: var(--el-color-primary);
  font-weight: 500;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.filter-tools {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  align-items: center;
}

.search-input {
  width: 300px;
}

.status-filter,
.progress-filter {
  width: 150px;
}

.courses-list {
  min-height: 400px;
}

.empty-state {
  padding: 60px 0;
  text-align: center;
}

.courses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 16px;
}

.course-card {
  position: relative;
  border: 2px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--el-bg-color);
}

.course-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.course-card.selected {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.course-card.completed {
  opacity: 0.8;
}

.selection-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
}

.course-info {
  margin-bottom: 16px;
}

.course-name {
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 500;
  margin: 0 0 12px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-meta {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  font-size: 13px;
}

.meta-item .label {
  color: var(--el-text-color-regular);
  margin-right: 4px;
}

.meta-item .value {
  color: var(--el-text-color-primary);
}

.progress-section {
  margin-bottom: 12px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 13px;
}

.progress-label {
  color: var(--el-text-color-regular);
}

.progress-value {
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.extra-info {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.price-info {
  display: flex;
  align-items: baseline;
  gap: 4px;
  justify-content: flex-end;
}

.price {
  color: var(--el-color-danger);
  font-size: 16px;
  font-weight: 600;
}

.unit {
  color: var(--el-text-color-regular);
  font-size: 12px;
}

.selection-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
  padding: 16px;
  background: var(--el-color-success-light-9);
  border-radius: 6px;
  border-left: 4px solid var(--el-color-success);
}

.summary-text {
  color: var(--el-color-success);
  font-weight: 500;
}
</style>
