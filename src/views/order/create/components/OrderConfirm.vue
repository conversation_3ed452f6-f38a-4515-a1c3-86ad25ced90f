<script setup lang="ts">
import { computed, ref } from 'vue';
import { ElMessage } from 'element-plus';

// Props
interface Props {
  product?: any;
  account?: any;
  selectedCourses?: any[];
  orderSummary?: any;
}

const props = withDefaults(defineProps<Props>(), {
  product: null,
  account: null,
  selectedCourses: () => [],
  orderSummary: null
});

// Emits
const emit = defineEmits<{
  submitOrder: [];
}>();

// 响应式数据
const agreedToTerms = ref(false);
const showTermsDialog = ref(false);
const showPrivacyDialog = ref(false);

// 计算属性
const canSubmit = computed(() => {
  return agreedToTerms.value && props.selectedCourses.length > 0 && props.orderSummary;
});

// 方法
const handleCloseTerms = () => {
  showTermsDialog.value = false;
};

const handleClosePrivacy = () => {
  showPrivacyDialog.value = false;
};

const handleAgreeTerms = () => {
  agreedToTerms.value = true;
  showTermsDialog.value = false;
  ElMessage.success('已同意服务条款');
};

const handleAgreePrivacy = () => {
  agreedToTerms.value = true;
  showPrivacyDialog.value = false;
  ElMessage.success('已同意隐私政策');
};

const handleSubmit = () => {
  if (!canSubmit.value) {
    ElMessage.error('请先同意服务条款');
    return;
  }

  emit('submitOrder');
};

// 暴露方法给父组件
defineExpose({
  canSubmit,
  handleSubmit
});
</script>

<template>
  <div class="order-confirm">
    <div class="confirm-header">
      <h3>确认订单</h3>
      <p class="header-description">请仔细核对订单信息，确认无误后提交</p>
    </div>

    <div class="confirm-content">
      <!-- 商品信息 -->
      <div class="info-section">
        <h4>商品信息</h4>
        <div class="product-info">
          <div class="product-card">
            <div class="product-header">
              <h5>{{ product?.product_name }}</h5>
              <ElTag type="success">{{ product?.service_type }}</ElTag>
            </div>
            <div class="product-details">
              <div class="detail-item">
                <span class="label">平台类型：</span>
                <span class="value">{{ product?.platform_type }}</span>
              </div>
              <div class="detail-item">
                <span class="label">服务分类：</span>
                <span class="value">{{ product?.category_name }}</span>
              </div>
              <div class="detail-item">
                <span class="label">单价：</span>
                <span class="value price">¥{{ product?.price }}/课程</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 账号信息 -->
      <div class="info-section">
        <h4>账号信息</h4>
        <div class="account-info">
          <div class="info-grid">
            <div class="info-item">
              <span class="label">学校名称：</span>
              <span class="value">{{ account?.school }}</span>
            </div>
            <div class="info-item">
              <span class="label">平台账号：</span>
              <span class="value">{{ account?.username }}</span>
            </div>
            <div class="info-item">
              <span class="label">备注信息：</span>
              <span class="value">{{ account?.remark || '无' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 选中课程 -->
      <div class="info-section">
        <h4>选中课程 ({{ selectedCourses.length }}门)</h4>
        <div class="courses-list">
          <div class="courses-header">
            <span class="course-name-header">课程名称</span>
            <span class="course-progress-header">当前进度</span>
            <span class="course-price-header">价格</span>
          </div>
          <div class="courses-body">
            <div v-for="course in selectedCourses" :key="course.courseId" class="course-row">
              <div class="course-name">
                <span class="name-text" :title="course.courseName">
                  {{ course.courseName }}
                </span>
                <span class="course-id">ID: {{ course.courseId }}</span>
              </div>
              <div class="course-progress">
                <ElProgress :percentage="course.progress || 0" :stroke-width="4" :show-text="false" />
                <span class="progress-text">{{ course.progress || 0 }}%</span>
              </div>
              <div class="course-price">¥{{ product?.price || 0 }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 费用明细 -->
      <div class="info-section">
        <h4>费用明细</h4>
        <div class="cost-breakdown">
          <div class="cost-item">
            <span class="cost-label">课程数量：</span>
            <span class="cost-value">{{ orderSummary?.courseCount || 0 }} 门</span>
          </div>
          <div class="cost-item">
            <span class="cost-label">单价：</span>
            <span class="cost-value">¥{{ orderSummary?.unitPrice || 0 }}</span>
          </div>
          <div class="cost-item subtotal">
            <span class="cost-label">小计：</span>
            <span class="cost-value">¥{{ orderSummary?.totalAmount || 0 }}</span>
          </div>
          <div class="cost-item total">
            <span class="cost-label">总计：</span>
            <span class="cost-value">¥{{ orderSummary?.totalAmount || 0 }}</span>
          </div>
        </div>
      </div>

      <!-- 服务条款 -->
      <div class="info-section">
        <h4>服务条款</h4>
        <div class="terms-content">
          <ElCheckbox v-model="agreedToTerms" size="large">
            我已阅读并同意
            <ElButton type="text" @click="showTermsDialog = true">《服务条款》</ElButton>
            和
            <ElButton type="text" @click="showPrivacyDialog = true">《隐私政策》</ElButton>
          </ElCheckbox>
        </div>
      </div>
    </div>

    <!-- 服务条款对话框 -->
    <ElDialog v-model="showTermsDialog" title="服务条款" width="60%" :before-close="handleCloseTerms">
      <div class="terms-dialog">
        <h5>1. 服务说明</h5>
        <p>本服务为网课代刷服务，帮助用户完成在线课程学习任务。</p>

        <h5>2. 用户责任</h5>
        <p>用户需提供准确的账号信息，并承担账号安全责任。</p>

        <h5>3. 服务保障</h5>
        <p>我们承诺按时完成代刷任务，如遇问题及时沟通解决。</p>

        <h5>4. 免责声明</h5>
        <p>因用户账号问题导致的服务失败，我们不承担责任。</p>
      </div>
      <template #footer>
        <ElButton @click="showTermsDialog = false">关闭</ElButton>
        <ElButton type="primary" @click="handleAgreeTerms">同意条款</ElButton>
      </template>
    </ElDialog>

    <!-- 隐私政策对话框 -->
    <ElDialog v-model="showPrivacyDialog" title="隐私政策" width="60%" :before-close="handleClosePrivacy">
      <div class="privacy-dialog">
        <h5>1. 信息收集</h5>
        <p>我们仅收集提供服务所必需的账号信息。</p>

        <h5>2. 信息使用</h5>
        <p>您的信息仅用于完成代刷服务，不会用于其他用途。</p>

        <h5>3. 信息保护</h5>
        <p>我们采用严格的安全措施保护您的个人信息。</p>

        <h5>4. 信息删除</h5>
        <p>服务完成后，我们会及时删除相关账号信息。</p>
      </div>
      <template #footer>
        <ElButton @click="showPrivacyDialog = false">关闭</ElButton>
        <ElButton type="primary" @click="handleAgreePrivacy">同意政策</ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<style scoped>
.order-confirm {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.confirm-header {
  text-align: center;
  margin-bottom: 30px;
}

.confirm-header h3 {
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.header-description {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.info-section {
  margin-bottom: 32px;
  padding: 20px;
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  background: var(--el-bg-color);
}

.info-section h4 {
  color: var(--el-text-color-primary);
  margin: 0 0 16px 0;
  font-size: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  padding-bottom: 8px;
}

.product-card {
  padding: 16px;
  background: var(--el-color-primary-light-9);
  border-radius: 6px;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.product-header h5 {
  color: var(--el-text-color-primary);
  margin: 0;
  font-size: 16px;
}

.product-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.detail-item {
  display: flex;
  font-size: 14px;
}

.detail-item .label {
  color: var(--el-text-color-regular);
  width: 80px;
  flex-shrink: 0;
}

.detail-item .value {
  color: var(--el-text-color-primary);
}

.detail-item .value.price {
  color: var(--el-color-danger);
  font-weight: 500;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  font-size: 14px;
}

.info-item .label {
  color: var(--el-text-color-regular);
  width: 80px;
  flex-shrink: 0;
}

.info-item .value {
  color: var(--el-text-color-primary);
}

.courses-list {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 6px;
  overflow: hidden;
}

.courses-header {
  display: grid;
  grid-template-columns: 1fr 120px 80px;
  gap: 16px;
  padding: 12px 16px;
  background: var(--el-color-info-light-9);
  font-weight: 500;
  font-size: 14px;
  color: var(--el-text-color-primary);
}

.courses-body {
  max-height: 300px;
  overflow-y: auto;
}

.course-row {
  display: grid;
  grid-template-columns: 1fr 120px 80px;
  gap: 16px;
  padding: 12px 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  align-items: center;
}

.course-row:last-child {
  border-bottom: none;
}

.course-name {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.name-text {
  color: var(--el-text-color-primary);
  font-size: 14px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.course-id {
  color: var(--el-text-color-regular);
  font-size: 12px;
}

.course-progress {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.progress-text {
  color: var(--el-text-color-regular);
  font-size: 12px;
  text-align: center;
}

.course-price {
  color: var(--el-color-danger);
  font-weight: 500;
  text-align: right;
}

.cost-breakdown {
  background: var(--el-color-success-light-9);
  padding: 16px;
  border-radius: 6px;
}

.cost-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.cost-item:last-child {
  margin-bottom: 0;
}

.cost-item.subtotal {
  border-top: 1px solid var(--el-border-color-lighter);
  padding-top: 8px;
  margin-top: 8px;
}

.cost-item.total {
  border-top: 2px solid var(--el-color-success);
  padding-top: 12px;
  margin-top: 12px;
  font-size: 16px;
  font-weight: 600;
}

.cost-label {
  color: var(--el-text-color-regular);
}

.cost-value {
  color: var(--el-text-color-primary);
}

.cost-item.total .cost-value {
  color: var(--el-color-success);
}

.terms-content {
  text-align: center;
  padding: 20px;
}

.terms-dialog,
.privacy-dialog {
  line-height: 1.6;
}

.terms-dialog h5,
.privacy-dialog h5 {
  color: var(--el-text-color-primary);
  margin: 16px 0 8px 0;
}

.terms-dialog p,
.privacy-dialog p {
  color: var(--el-text-color-regular);
  margin: 0 0 12px 0;
}
</style>
