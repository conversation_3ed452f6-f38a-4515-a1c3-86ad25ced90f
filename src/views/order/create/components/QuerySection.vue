<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { InfoFilled, Search } from '@element-plus/icons-vue';
import { queryCourses } from '@/service/api/query';

// Props
interface Props {
  product?: any;
  account?: any;
}

const props = withDefaults(defineProps<Props>(), {
  product: null,
  account: null
});

// Emits
const emit = defineEmits<{
  querySuccess: [courses: any[]];
  queryError: [error: string];
}>();

// 响应式数据
const querying = ref(false);
const queryResult = ref<any>(null);

// 计算属性
const canQuery = computed(() => {
  return props.product && props.account && props.account.school && props.account.username && props.account.password;
});

const previewCourses = computed(() => {
  if (!queryResult.value?.success || !queryResult.value.data?.courses) {
    return [];
  }
  return queryResult.value.data.courses.slice(0, 3);
});

// 方法
const handleQuery = async () => {
  if (!canQuery.value) {
    ElMessage.error('请先完成账号信息填写');
    return;
  }

  querying.value = true;
  queryResult.value = null;

  try {
    console.log('开始查课:', {
      productId: props.product.product_id,
      account: props.account
    });

    const response = await queryCourses({
      productId: props.product.product_id,
      school: props.account.school,
      username: props.account.username,
      password: props.account.password
    });

    queryResult.value = {
      success: true,
      data: response,
      message: '查课成功'
    };

    const courses = Array.isArray(response?.courses) ? response.courses : [];
    emit('querySuccess', courses);
    ElMessage.success(`查课成功，找到 ${courses.length} 门课程`);
  } catch (error: any) {
    console.error('查课异常:', error);

    queryResult.value = {
      success: false,
      message: error.message || '网络异常，请重试'
    };

    emit('queryError', error.message || '网络异常，请重试');
  } finally {
    querying.value = false;
  }
};

const handleRetry = () => {
  queryResult.value = null;
  handleQuery();
};

const handleContactSupport = () => {
  ElMessage.info('请联系客服QQ：*********');
};

const formatQueryTime = (timeStr: string) => {
  if (!timeStr) return '--';
  try {
    const date = new Date(timeStr);
    return date.toLocaleTimeString();
  } catch {
    return '--';
  }
};

// 监听props变化
watch([() => props.product, () => props.account], () => {
  queryResult.value = null;
});
</script>

<template>
  <div class="query-section">
    <div class="section-header">
      <h3>查课验证</h3>
      <p class="header-description">验证账号信息并获取课程列表</p>
    </div>

    <!-- 账号信息确认 -->
    <div class="account-confirm">
      <h4>账号信息确认</h4>
      <div class="confirm-grid">
        <div class="confirm-item">
          <span class="label">商品名称：</span>
          <span class="value">{{ product?.product_name }}</span>
        </div>
        <div class="confirm-item">
          <span class="label">平台类型：</span>
          <span class="value">{{ product?.platform_type }}</span>
        </div>
        <div class="confirm-item">
          <span class="label">学校名称：</span>
          <span class="value">{{ account?.school }}</span>
        </div>
        <div class="confirm-item">
          <span class="label">平台账号：</span>
          <span class="value">{{ account?.username }}</span>
        </div>
      </div>
    </div>

    <!-- 查课操作 -->
    <div class="query-actions">
      <ElButton type="primary" size="large" :loading="querying" :disabled="!canQuery" @click="handleQuery">
        <template #icon>
          <ElIcon><Search /></ElIcon>
        </template>
        {{ querying ? '查课中...' : '开始查课' }}
      </ElButton>

      <div class="query-tips">
        <ElIcon><InfoFilled /></ElIcon>
        <span>查课过程可能需要几秒钟，请耐心等待</span>
      </div>
    </div>

    <!-- 查课结果 -->
    <div v-if="queryResult" class="query-result">
      <div class="result-header">
        <h4>查课结果</h4>
        <ElTag :type="queryResult.success ? 'success' : 'danger'" size="large">
          {{ queryResult.success ? '查课成功' : '查课失败' }}
        </ElTag>
      </div>

      <!-- 成功结果 -->
      <div v-if="queryResult.success" class="success-result">
        <div class="result-summary">
          <ElStatistic title="找到课程数量" :value="queryResult.data?.courses?.length || 0" suffix="门" />
          <div class="query-time">
            <div class="text-12px text-gray-500">查课时间</div>
            <div class="text-16px font-semibold">{{ formatQueryTime(queryResult.data?.queryTime) }}</div>
          </div>
        </div>

        <!-- 课程预览 -->
        <div v-if="queryResult.data?.courses?.length > 0" class="courses-preview">
          <h5>课程预览</h5>
          <div class="preview-list">
            <div v-for="(course, index) in previewCourses" :key="course.courseId || index" class="preview-item">
              <div class="course-info">
                <span class="course-name">{{ course.courseName || '未知课程' }}</span>
                <ElTag size="small" type="info">{{ course.status || '未知状态' }}</ElTag>
              </div>
              <div class="course-progress">
                <span class="progress-text">进度: {{ course.progress || '0' }}%</span>
              </div>
            </div>
          </div>

          <div v-if="queryResult.data.courses.length > 3" class="more-courses">
            <span>还有 {{ queryResult.data.courses.length - 3 }} 门课程...</span>
          </div>
        </div>
      </div>

      <!-- 失败结果 -->
      <div v-else class="error-result">
        <ElAlert :title="queryResult.message || '查课失败'" type="error" :closable="false" show-icon>
          <template #default>
            <p>可能的原因：</p>
            <ul>
              <li>账号或密码错误</li>
              <li>网络连接问题</li>
              <li>平台服务异常</li>
              <li>账号被锁定或限制</li>
            </ul>
            <p>请检查账号信息后重试，或联系客服协助处理。</p>
          </template>
        </ElAlert>

        <div class="retry-actions">
          <ElButton @click="handleRetry">重新查课</ElButton>
          <ElButton type="text" @click="handleContactSupport">联系客服</ElButton>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.query-section {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.section-header {
  text-align: center;
  margin-bottom: 30px;
}

.section-header h3 {
  color: var(--el-text-color-primary);
  margin-bottom: 8px;
}

.header-description {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.account-confirm {
  margin-bottom: 30px;
  padding: 20px;
  background: var(--el-color-info-light-9);
  border-radius: 8px;
  border-left: 4px solid var(--el-color-info);
}

.account-confirm h4 {
  color: var(--el-color-info);
  margin: 0 0 16px 0;
  font-size: 16px;
}

.confirm-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

@media (max-width: 768px) {
  .confirm-grid {
    grid-template-columns: 1fr;
  }
}

.confirm-item {
  display: flex;
  font-size: 14px;
}

.confirm-item .label {
  color: var(--el-text-color-regular);
  width: 80px;
  flex-shrink: 0;
}

.confirm-item .value {
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.query-actions {
  text-align: center;
  margin-bottom: 30px;
}

.query-tips {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 12px;
  color: var(--el-text-color-regular);
  font-size: 13px;
}

.query-result {
  border: 1px solid var(--el-border-color-light);
  border-radius: 8px;
  padding: 24px;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.result-header h4 {
  color: var(--el-text-color-primary);
  margin: 0;
}

.success-result .result-summary {
  display: flex;
  gap: 40px;
  margin-bottom: 24px;
  justify-content: center;
}

.courses-preview {
  margin-top: 20px;
}

.courses-preview h5 {
  color: var(--el-text-color-primary);
  margin: 0 0 12px 0;
  font-size: 14px;
}

.preview-list {
  space-y: 8px;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: var(--el-bg-color-page);
  border-radius: 6px;
  margin-bottom: 8px;
}

.course-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.course-name {
  color: var(--el-text-color-primary);
  font-size: 14px;
}

.progress-text {
  color: var(--el-text-color-regular);
  font-size: 13px;
}

.more-courses {
  text-align: center;
  margin-top: 12px;
  color: var(--el-text-color-regular);
  font-size: 13px;
}

.error-result .retry-actions {
  margin-top: 16px;
  text-align: center;
}

.error-result :deep(.el-alert__content) ul {
  margin: 8px 0;
  padding-left: 20px;
}

.error-result :deep(.el-alert__content) li {
  margin-bottom: 4px;
}
</style>
