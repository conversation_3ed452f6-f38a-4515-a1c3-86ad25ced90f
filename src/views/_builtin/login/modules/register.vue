<script setup lang="ts">
import { computed, ref } from 'vue';
import { fetchRegister } from '@/service/api/auth';
import { useAuthStore } from '@/store/modules/auth';
import { useRouterPush } from '@/hooks/common/router';
import { useForm, useFormRules } from '@/hooks/common/form';
import { $t } from '@/locales';

defineOptions({ name: 'Register' });

const { toggleLoginModule } = useRouterPush();
const { formRef, validate } = useForm();
const authStore = useAuthStore();

interface FormModel {
  qqAccount: string;
  inviteCode: string;
  password: string;
  confirmPassword: string;
  nickname: string;
}

const model = ref<FormModel>({
  qqAccount: '',
  inviteCode: '',
  password: '',
  confirmPassword: '',
  nickname: ''
});

const loading = ref(false);

// 自定义验证规则
const rules = computed<Record<keyof FormModel, App.Global.FormRule[]>>(() => {
  const { createConfirmPwdRule } = useFormRules();

  return {
    qqAccount: [
      { required: true, message: 'QQ账号不能为空' },
      { pattern: /^[1-9][0-9]{4,10}$/, message: 'QQ账号格式不正确，应为5-11位数字且不能以0开头' }
    ],
    inviteCode: [
      { required: true, message: '邀请码不能为空' },
      { pattern: /^[A-Z0-9]{8}$/, message: '邀请码格式不正确' }
    ],
    password: [
      { required: true, message: '密码不能为空' },
      { min: 6, max: 20, message: '密码长度应为6-20位' },
      {
        validator: (rule: any, value: string, callback: any) => {
          if (!value) {
            callback();
            return;
          }
          if (!/\d/.test(value)) {
            callback(new Error('密码必须包含数字'));
            return;
          }
          if (!/[a-z]/.test(value)) {
            callback(new Error('密码必须包含小写字母'));
            return;
          }
          if (!/[A-Z]/.test(value)) {
            callback(new Error('密码必须包含大写字母'));
            return;
          }
          callback();
        }
      }
    ],
    confirmPassword: createConfirmPwdRule(model.value.password),
    nickname: [{ max: 20, message: '昵称长度不能超过20位' }]
  };
});

async function handleSubmit() {
  try {
    await validate();
  } catch (error) {
    console.log('表单验证失败:', error);
    return;
  }

  loading.value = true;
  try {
    const response = await fetchRegister(
      model.value.qqAccount,
      model.value.password,
      model.value.inviteCode,
      model.value.nickname || model.value.qqAccount
    );

    console.log('注册响应:', response);
    console.log('response.data:', (response as any).data);
    console.log('response.token:', response.token);

    // 修正响应结构检查：transformBackendResponse 返回的是 data 字段的内容
    // 所以 response 直接包含 token 和 userInfo
    if (response && response.token) {
      // 保存登录状态
      await authStore.login((response as any).token, 'dummy_password');

      // 显示成功消息
      window.$message?.success({
        message: `注册成功！欢迎 ${(response as any).userInfo?.nickname || model.value.qqAccount}`,
        duration: 3000
      });

      // 延迟跳转，让用户看到成功消息
      setTimeout(() => {
        window.location.reload(); // 刷新页面到主界面
      }, 1500);
    } else {
      window.$message?.error('注册失败：服务器响应异常');
    }
  } catch (error: any) {
    console.error('注册失败:', error);
    // 处理注册错误，包括邀请码验证失败
    const errorMessage = error.response?.data?.message || error.message || '注册失败';
    window.$message?.error(errorMessage);
  } finally {
    loading.value = false;
  }
}
</script>

<template>
  <ElForm ref="formRef" :model="model" :rules="rules" size="large" :show-label="false" @keyup.enter="handleSubmit">
    <ElFormItem prop="qqAccount">
      <ElInput v-model="model.qqAccount" placeholder="请输入QQ账号" />
    </ElFormItem>
    <ElFormItem prop="inviteCode">
      <ElInput
        v-model="model.inviteCode"
        placeholder="请输入邀请码"
        style="text-transform: uppercase"
        @input="model.inviteCode = model.inviteCode.toUpperCase()"
      />
    </ElFormItem>
    <ElFormItem prop="password">
      <ElInput
        v-model="model.password"
        type="password"
        show-password-on="click"
        placeholder="请输入密码(数字+大小写字母)"
      />
    </ElFormItem>
    <ElFormItem prop="confirmPassword">
      <ElInput v-model="model.confirmPassword" type="password" show-password-on="click" placeholder="请确认密码" />
    </ElFormItem>
    <ElFormItem prop="nickname">
      <ElInput v-model="model.nickname" placeholder="请输入昵称(可选)" />
    </ElFormItem>

    <!-- 注册说明 -->
    <div class="mb-16px text-12px text-#666 leading-relaxed">
      <div>• QQ账号：5-11位数字，不能以0开头</div>
      <div>• 密码要求：6-20位，包含数字+大小写字母</div>
      <div>• 邀请码：8位字母数字组合</div>
    </div>

    <ElSpace direction="vertical" :size="24" class="w-full" fill>
      <ElButton type="primary" size="large" round block :loading="loading" @click="handleSubmit">
        {{ $t('common.confirm') }}
      </ElButton>
      <ElButton size="large" round @click="toggleLoginModule('pwd-login')">
        {{ $t('page.login.common.back') }}
      </ElButton>
    </ElSpace>
  </ElForm>
</template>

<style scoped></style>
