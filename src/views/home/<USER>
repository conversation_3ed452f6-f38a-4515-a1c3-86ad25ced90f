<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useLicenseStore } from '@/store/modules/license';
import HeaderBanner from './modules/header-banner.vue';
import CardData from './modules/card-data.vue';
import LineChart from './modules/line-chart.vue';
import PieChart from './modules/pie-chart.vue';
import ProjectNews from './modules/project-news.vue';
import CreativityBanner from './modules/creativity-banner.vue';

const appStore = useAppStore();
const licenseStore = useLicenseStore();

const gap = computed(() => (appStore.isMobile ? 8 : 16));

// 初始化授权状态
onMounted(async () => {
  try {
    await licenseStore.initLicenseStatus();
  } catch (error) {
    console.error('初始化授权状态失败:', error);
  }
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <HeaderBanner />
    <CardData />
    <ElRow :gutter="gap" class="w-full">
      <ElCol :lg="14" :sm="24">
        <LineChart />
      </ElCol>
      <ElCol :lg="10" :sm="24">
        <PieChart />
      </ElCol>
    </ElRow>
    <ElRow :gutter="gap" class="w-full">
      <ElCol :lg="14" :sm="24">
        <ProjectNews />
      </ElCol>
      <ElCol :lg="10" :sm="24">
        <CreativityBanner />
      </ElCol>
    </ElRow>
  </div>
</template>

<style scoped>
/* 移动端适配 */
@media (max-width: 768px) {
  .flex.flex-col.gap-16px.p-16px {
    padding: 12px;
    gap: 12px;
  }
}
</style>
