<script setup lang="ts">
import { computed } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useAuthStore } from '@/store/modules/auth';
import { useLicenseStore } from '@/store/modules/license';
import { useRouterPush } from '@/hooks/common/router';
import { $t } from '@/locales';

defineOptions({ name: 'HeaderBanner' });

const appStore = useAppStore();
const authStore = useAuthStore();
const licenseStore = useLicenseStore();
const { routerPushByKey } = useRouterPush();

const gap = computed(() => (appStore.isMobile ? 0 : 16));

// 检查授权状态
const isLicenseValid = computed(() => licenseStore.isLicenseValid);

// 跳转到授权界面
function goToLicensePage() {
  routerPushByKey('system-center_license');
}

interface StatisticData {
  id: number;
  title: string;
  value: number;
}

const statisticData = computed<StatisticData[]>(() => [
  { id: 0, title: $t('page.home.projectCount'), value: 25 },
  { id: 1, title: $t('page.home.todo'), value: 4, formatter: (val: number) => `${val}/${16}` },
  { id: 2, title: $t('page.home.message'), value: 12 }
]);
</script>

<template>
  <ElCard class="card-wrapper">
    <ElRow :gutter="gap" class="px-8px">
      <ElCol :md="18" :sm="24">
        <div class="flex-y-center">
          <div class="size-72px shrink-0 overflow-hidden rd-1/2">
            <img src="@/assets/imgs/soybean.jpg" class="size-full" />
          </div>
          <div class="pl-12px">
            <h3 class="text-18px font-semibold">
              {{ $t('page.home.greeting', { userName: authStore.userInfo.username }) }}
            </h3>
            <p class="text-#999 leading-30px">{{ $t('page.home.weatherDesc') }}</p>
          </div>
        </div>
      </ElCol>
      <ElCol :md="6" :sm="24">
        <ElSpace direction="horizontal" class="w-full justify-end" :size="24">
          <ElStatistic v-for="item in statisticData" :key="item.id" class="whitespace-nowrap" v-bind="item" />
        </ElSpace>
      </ElCol>
    </ElRow>

    <!-- 未授权状态提示 -->
    <div v-if="!isLicenseValid" class="mt-16px border border-orange-200 rounded-8px bg-orange-50 p-16px">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-12px">
          <div class="h-24px w-24px flex-center rounded-6px bg-orange-100">
            <SvgIcon icon="carbon:warning" class="text-14px text-orange-600" />
          </div>
          <div>
            <h4 class="m-0 text-14px text-orange-800 font-medium">系统未授权</h4>
            <p class="m-0 text-12px text-orange-600">
              系统当前处于未授权状态，部分功能受限。请进行授权验证以正常使用系统。
            </p>
          </div>
        </div>
        <ElButton type="warning" size="small" @click="goToLicensePage">
          <SvgIcon icon="carbon:security" class="mr-4px" />
          立即授权
        </ElButton>
      </div>
    </div>
  </ElCard>
</template>

<style scoped></style>
