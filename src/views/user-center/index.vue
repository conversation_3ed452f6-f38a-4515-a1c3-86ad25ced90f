<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { fetchChangePassword, fetchInviteStats, fetchUpdateInviteCode, fetchUpdateUserInfo } from '@/service/api/auth';
import { useAuthStore } from '@/store/modules/auth';
import { useRouterPush } from '@/hooks/common/router';

defineOptions({ name: 'UserCenter' });

// 获取用户信息
const authStore = useAuthStore();
const userInfo = computed(() => authStore.userInfo);
const { toLogin } = useRouterPush(false);

// 角色映射
const roleMap = {
  0: '普通用户',
  1: '管理员',
  2: '代理商'
};

// 显示角色名称
const getRoleName = computed(() => {
  return (roleMap as any)[userInfo.value.role] || '未知角色';
});

// 表单数据
const formData = ref({
  nickname: userInfo.value.nickname || '',
  email: userInfo.value.email || ''
});

// 修改密码表单
const passwordForm = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
});

// 编辑状态
const isEditing = ref(false);

// 显示修改密码对话框
const showPasswordDialog = ref(false);

// 邀请统计数据
const inviteStats = ref({
  totalInvited: 0,
  activeInvited: 0,
  recentInvited: 0,
  subordinatesList: [] as any[],
  superior: null as any
});

// 加载状态
const loadingInviteStats = ref(false);
const loadingUpdateInviteCode = ref(false);
const loadingRegenerateInviteCode = ref(false);

// 启用编辑模式
const startEdit = () => {
  formData.value = {
    nickname: userInfo.value.nickname || '',
    email: userInfo.value.email || ''
  };
  isEditing.value = true;
};

// 取消编辑
const cancelEdit = () => {
  isEditing.value = false;
};

// 保存个人信息
const saveUserInfo = async () => {
  try {
    await fetchUpdateUserInfo({
      nickname: formData.value.nickname,
      email: formData.value.email
    });

    // 重新获取用户信息
    await authStore.initUserInfo();

    ElMessage.success('个人信息更新成功');
    isEditing.value = false;
  } catch {
    ElMessage.error('更新失败，请重试');
  }
};

// 修改密码
const changePassword = async () => {
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    ElMessage.error('两次输入的新密码不一致');
    return;
  }

  try {
    await fetchChangePassword(passwordForm.value.oldPassword, passwordForm.value.newPassword);

    // 密码修改成功，显示成功消息
    ElMessage.success('密码修改成功，请重新登录');
    showPasswordDialog.value = false;

    // 清空表单
    passwordForm.value = {
      oldPassword: '',
      newPassword: '',
      confirmPassword: ''
    };

    // 密码修改成功时，才退出登录并跳转到登录页
    setTimeout(async () => {
      await authStore.resetStore();
      toLogin();
    }, 1500);
  } catch (error: any) {
    // 当修改密码失败时，显示错误消息
    ElMessage.error(`密码修改失败：${error.message || '旧密码可能不正确'}`);
    // 不执行任何退出登录的操作
  }
};

// 刷新用户信息
const refreshUserInfo = async () => {
  try {
    await authStore.initUserInfo();
    ElMessage.success('用户信息已刷新');
  } catch {
    ElMessage.error('刷新用户信息失败');
  }
};

// 用户安全等级
const securityLevel = computed(() => {
  let level = 1; // 基础等级
  if (userInfo.value.email) level += 1;
  if (userInfo.value.nickname) level += 1;
  return Math.min(level, 3);
});

const securityLevelText = computed(() => {
  const levels = ['低', '中', '高'];
  return levels[securityLevel.value - 1];
});

const securityLevelColor = computed(() => {
  const colors = ['danger', 'warning', 'success'];
  return colors[securityLevel.value - 1];
});

// 获取邀请统计
const loadInviteStats = async () => {
  try {
    loadingInviteStats.value = true;
    const response = await fetchInviteStats();

    inviteStats.value = {
      totalInvited: response.totalInvited || 0,
      activeInvited: response.activeInvited || 0,
      recentInvited: response.recentInvited || 0,
      subordinatesList: response.subordinatesList || [],
      superior: response.superior || null
    };
  } catch (error) {
    console.error('获取邀请统计失败:', error);
    ElMessage.error('获取邀请统计失败');
  } finally {
    loadingInviteStats.value = false;
  }
};

// 更新邀请码
const updateInviteCode = async () => {
  try {
    await ElMessageBox.confirm('更新邀请码后，旧的邀请码将失效，确定要继续吗？', '确认更新', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    loadingUpdateInviteCode.value = true;
    const response = await fetchUpdateInviteCode();

    // 直接更新本地用户信息中的邀请码
    if (response.inviteCode) {
      userInfo.value.inviteCode = response.inviteCode;
    }

    // 同时刷新完整的用户信息
    await authStore.initUserInfo();

    ElMessage.success(response.message || '邀请码更新成功');
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('更新邀请码失败，请重试');
    }
  } finally {
    loadingUpdateInviteCode.value = false;
  }
};

// 复制邀请码
const copyInviteCode = async () => {
  try {
    const inviteCode = userInfo.value.inviteCode;
    if (!inviteCode) {
      ElMessage.error('邀请码为空，无法复制');
      return;
    }
    await navigator.clipboard.writeText(inviteCode);
    ElMessage.success('邀请码已复制到剪贴板');
  } catch {
    ElMessage.error('复制失败，请手动复制');
  }
};

// 重新生成邀请码
const regenerateInviteCode = async () => {
  try {
    await ElMessageBox.confirm('重新生成邀请码后，旧的邀请码将永久失效，确定要继续吗？', '确认重新生成', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    loadingRegenerateInviteCode.value = true;
    const response = await fetchUpdateInviteCode();

    // 直接更新本地用户信息中的邀请码
    if (response.inviteCode) {
      userInfo.value.inviteCode = response.inviteCode;
    }

    // 同时刷新完整的用户信息
    await authStore.initUserInfo();

    ElMessage.success(response.message || '邀请码重新生成成功');
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('重新生成邀请码失败，请重试');
    }
  } finally {
    loadingRegenerateInviteCode.value = false;
  }
};

// 页面加载时获取邀请统计
onMounted(() => {
  loadInviteStats();
});
</script>

<template>
  <ElSpace direction="vertical" fill class="user-center-container" :size="16">
    <!-- 用户信息横幅 -->
    <ElCard class="user-banner card-wrapper">
      <ElRow :gutter="16" class="px-8px">
        <ElCol :md="18" :sm="24">
          <div class="flex-y-center">
            <div class="user-avatar-wrapper">
              <ElAvatar :size="72" class="user-avatar">
                <i class="i-carbon:user-avatar text-24px" />
              </ElAvatar>
            </div>
            <div class="user-info pl-12px">
              <h3 class="mb-4px text-18px font-semibold">
                {{ userInfo.nickname || userInfo.username }}
              </h3>
              <div class="mb-4px flex items-center gap-8px">
                <ElTag effect="light" type="primary" size="small">{{ getRoleName }}</ElTag>
                <span class="text-12px text-#999">ID: {{ userInfo.userId }}</span>
              </div>
              <p class="text-12px text-#999 leading-20px">
                <i class="i-carbon:time mr-4px" />
                {{ new Date(userInfo.createTime).toLocaleDateString() }} 加入
              </p>
            </div>
          </div>
        </ElCol>
        <ElCol :md="6" :sm="24">
          <div class="h-full flex items-center justify-end">
            <div class="text-center">
              <div class="mb-4px text-24px text-primary font-bold">{{ inviteStats.totalInvited }}</div>
              <div class="text-12px text-#999">邀请用户</div>
            </div>
          </div>
        </ElCol>
      </ElRow>
    </ElCard>

    <!-- 三个卡片并列布局 -->
    <ElRow :gutter="16">
      <!-- 基本信息卡片 -->
      <ElCol :lg="8" :md="24" :sm="24">
        <ElCard class="h-full card-wrapper">
          <template #header>
            <div class="flex items-center justify-between">
              <span>基本信息</span>
              <div class="flex gap-4px">
                <ElButton v-if="!isEditing" type="info" text size="small" @click="refreshUserInfo">
                  <i class="i-carbon:refresh" />
                </ElButton>
                <ElButton v-if="!isEditing" type="primary" size="small" @click="startEdit">
                  <i class="i-carbon:edit mr-1" />
                  编辑
                </ElButton>
                <template v-else>
                  <ElButton type="primary" size="small" @click="saveUserInfo">
                    <i class="i-carbon:checkmark mr-1" />
                    保存
                  </ElButton>
                  <ElButton size="small" @click="cancelEdit">
                    <i class="i-carbon:close mr-1" />
                    取消
                  </ElButton>
                </template>
              </div>
            </div>
          </template>

          <div class="space-y-12px">
            <div class="info-item">
              <span class="info-label">用户名</span>
              <span class="info-value">{{ userInfo.username }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">角色</span>
              <span class="info-value">
                <ElTag effect="light" type="primary" size="small">{{ getRoleName }}</ElTag>
              </span>
            </div>
            <div class="info-item">
              <span class="info-label">昵称</span>
              <div class="info-value">
                <template v-if="isEditing">
                  <ElInput v-model="formData.nickname" placeholder="请输入昵称" size="small" />
                </template>
                <template v-else>
                  {{ userInfo.nickname || '-' }}
                </template>
              </div>
            </div>
            <div class="info-item">
              <span class="info-label">邮箱</span>
              <div class="info-value">
                <template v-if="isEditing">
                  <ElInput v-model="formData.email" placeholder="请输入邮箱" size="small" />
                </template>
                <template v-else>
                  {{ userInfo.email || '-' }}
                </template>
              </div>
            </div>
            <div class="info-item">
              <span class="info-label">账号余额</span>
              <span class="info-value">
                <span class="text-primary font-semibold">¥{{ userInfo.balance || '0.00' }}</span>
              </span>
            </div>
            <div class="info-item">
              <span class="info-label">注册时间</span>
              <span class="info-value text-12px text-gray-500">
                {{ new Date(userInfo.createTime).toLocaleDateString() }}
              </span>
            </div>
          </div>
        </ElCard>
      </ElCol>

      <!-- 邀请码与安全卡片 -->
      <ElCol :lg="8" :md="24" :sm="24">
        <ElCard class="h-full card-wrapper">
          <template #header>
            <span>邀请码与安全</span>
          </template>

          <!-- 邀请码部分 -->
          <div class="mb-20px">
            <div class="mb-8px text-14px text-gray-600 font-medium">我的邀请码</div>
            <div class="invite-code-container">
              <div class="invite-code-display">
                <span class="invite-code-text">{{ userInfo.inviteCode || '暂无邀请码' }}</span>
              </div>
              <div class="invite-code-actions">
                <ElButton size="small" type="primary" text :disabled="!userInfo.inviteCode" @click="copyInviteCode">
                  <i class="i-carbon:copy" />
                </ElButton>
                <ElButton size="small" type="warning" text :loading="loadingUpdateInviteCode" @click="updateInviteCode">
                  <i class="i-carbon:renew" />
                </ElButton>
              </div>
            </div>
            <!-- 邀请码更换按钮 -->
            <div class="mt-8px">
              <ElButton
                size="small"
                type="danger"
                plain
                :loading="loadingRegenerateInviteCode"
                block
                @click="regenerateInviteCode"
              >
                <i class="i-carbon:restart mr-1" />
                重新生成邀请码
              </ElButton>
            </div>
          </div>

          <!-- 安全状态部分 -->
          <div>
            <div class="mb-12px flex items-center justify-between">
              <span class="text-14px text-gray-600 font-medium">账号安全</span>
              <ElTag :type="securityLevelColor as any" size="small">{{ securityLevelText }}</ElTag>
            </div>
            <ElProgress
              :percentage="securityLevel * 33.33"
              :status="securityLevelColor as any"
              :stroke-width="6"
              class="mb-12px"
            />
            <div class="space-y-8px">
              <div class="security-item">
                <div class="flex items-center gap-6px">
                  <i class="i-carbon:email text-14px" :class="userInfo.email ? 'text-success' : 'text-gray-400'" />
                  <span class="text-13px">邮箱绑定</span>
                </div>
                <span class="text-12px" :class="userInfo.email ? 'text-success' : 'text-gray-400'">
                  {{ userInfo.email ? '已绑定' : '未绑定' }}
                </span>
              </div>

              <div class="security-item">
                <div class="flex items-center gap-6px">
                  <i class="i-carbon:user text-14px" :class="userInfo.nickname ? 'text-success' : 'text-gray-400'" />
                  <span class="text-13px">昵称设置</span>
                </div>
                <span class="text-12px" :class="userInfo.nickname ? 'text-success' : 'text-gray-400'">
                  {{ userInfo.nickname ? '已设置' : '未设置' }}
                </span>
              </div>
            </div>
          </div>
        </ElCard>
      </ElCol>

      <!-- 操作与权限卡片 -->
      <ElCol :lg="8" :md="24" :sm="24">
        <ElCard class="h-full card-wrapper">
          <template #header>
            <span>操作与权限</span>
          </template>

          <!-- 快捷操作 -->
          <div class="mb-20px">
            <div class="mb-12px text-14px text-gray-600 font-medium">快捷操作</div>
            <div class="space-y-8px">
              <ElButton size="small" block @click="showPasswordDialog = true">
                <i class="i-carbon:password mr-2" />
                修改密码
              </ElButton>
              <ElButton :loading="loadingInviteStats" size="small" block @click="loadInviteStats">
                <i class="i-carbon:refresh mr-2" />
                刷新统计
              </ElButton>
            </div>
          </div>

          <!-- 权限信息 -->
          <div v-if="userInfo.permissions?.length">
            <div class="mb-8px text-14px text-gray-600 font-medium">我的权限</div>
            <div class="permission-list">
              <ElTag
                v-for="perm in userInfo.permissions"
                :key="perm"
                class="permission-tag"
                effect="light"
                size="small"
              >
                {{ perm }}
              </ElTag>
            </div>
          </div>

          <!-- 如果没有权限，显示占位内容 -->
          <div v-else>
            <div class="mb-8px text-14px text-gray-600 font-medium">我的权限</div>
            <div class="py-20px text-center">
              <i class="i-carbon:locked mb-8px block text-24px text-gray-300" />
              <span class="text-12px text-gray-400">暂无特殊权限</span>
            </div>
          </div>
        </ElCard>
      </ElCol>
    </ElRow>

    <!-- 修改密码对话框 -->
    <ElDialog v-model="showPasswordDialog" title="修改密码" width="500px" destroy-on-close center>
      <ElForm :model="passwordForm" label-width="100px">
        <ElFormItem
          label="原密码"
          prop="oldPassword"
          :rules="[{ required: true, message: '请输入原密码', trigger: 'blur' }]"
        >
          <ElInput v-model="passwordForm.oldPassword" type="password" placeholder="请输入原密码" show-password />
        </ElFormItem>

        <ElFormItem
          label="新密码"
          prop="newPassword"
          :rules="[{ required: true, message: '请输入新密码', trigger: 'blur' }]"
        >
          <ElInput v-model="passwordForm.newPassword" type="password" placeholder="请输入新密码" show-password />
        </ElFormItem>

        <ElFormItem
          label="确认新密码"
          prop="confirmPassword"
          :rules="[
            { required: true, message: '请再次输入新密码', trigger: 'blur' },
            {
              validator: (_rule, value, callback) => {
                if (value !== passwordForm.newPassword) {
                  callback(new Error('两次输入的密码不一致'));
                } else {
                  callback();
                }
              },
              trigger: 'blur'
            }
          ]"
        >
          <ElInput
            v-model="passwordForm.confirmPassword"
            type="password"
            placeholder="请再次输入新密码"
            show-password
          />
        </ElFormItem>
      </ElForm>

      <template #footer>
        <ElButton @click="showPasswordDialog = false">取消</ElButton>
        <ElButton type="primary" @click="changePassword">确认修改</ElButton>
      </template>
    </ElDialog>
  </ElSpace>
</template>

<style scoped lang="scss">
.user-center-container {
  :deep(.el-space__item) {
    width: 100%;
  }
}

// 用户横幅样式
.user-banner {
  .user-avatar-wrapper {
    flex-shrink: 0;
    overflow: hidden;
    border-radius: 50%;
  }

  .user-avatar {
    border: 3px solid var(--el-color-primary-light-9);
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1);
  }

  .user-info {
    flex: 1;
    min-width: 0;
  }
}

// 基本信息样式
.info-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);

  &:last-child {
    border-bottom: none;
  }
}

.info-label {
  font-size: 13px;
  color: var(--el-text-color-secondary);
  font-weight: 500;
  min-width: 60px;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  text-align: right;
  font-size: 13px;
  color: var(--el-text-color-primary);
  min-width: 0;
}

// 邀请码样式
.invite-code-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  background: var(--el-color-primary-light-9);
  border: 1px solid var(--el-color-primary-light-7);
  border-radius: 6px;
}

.invite-code-display {
  flex: 1;
  min-width: 0;
}

.invite-code-text {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  letter-spacing: 1px;
  font-size: 14px;
  color: var(--el-color-primary);
  word-break: break-all;
}

.invite-code-actions {
  display: flex;
  gap: 4px;
  margin-left: 8px;
}

// 安全项样式
.security-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

// 权限标签样式
.permission-list {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.permission-tag {
  font-size: 12px;
}
</style>
