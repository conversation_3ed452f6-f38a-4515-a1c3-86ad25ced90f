<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { createCategory, deleteCategory, fetchCategoryList, updateCategory } from '@/service/api/product';

defineOptions({
  name: 'CategoryManage'
});

// 响应式数据
const loading = ref(false);
const submitting = ref(false);
const tableData = ref<any[]>([]);

// 搜索表单
const searchFormRef = ref();
const searchForm = reactive({
  keyword: '',
  status: undefined as number | undefined
});

// 分页数据
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
});

// 对话框相关
const dialogVisible = ref(false);
const dialogMode = ref<'create' | 'edit' | 'view'>('create');
const dialogTitle = computed(() => {
  const titles = {
    create: '新增分类',
    edit: '编辑分类',
    view: '查看分类'
  };
  return titles[dialogMode.value];
});

// 表单数据
const formRef = ref();
const formData = reactive({
  category_id: 0,
  name: '',
  sort_order: 10,
  status: 1
});

// 表单验证规则
const formRules = {
  name: [{ required: true, message: '请输入分类名称', trigger: 'blur' }]
};

// 方法
const fetchTableData = async () => {
  try {
    loading.value = true;
    const params = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      keyword: searchForm.keyword || undefined,
      status: searchForm.status
    };

    const response = await fetchCategoryList(params);
    tableData.value = response.list || [];
    pagination.total = response.total || 0;
  } catch (error) {
    ElMessage.error('获取分类列表失败');
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  pagination.page = 1;
  fetchTableData();
};

const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: undefined
  });
  pagination.page = 1;
  fetchTableData();
};

const handleRefresh = () => {
  fetchTableData();
};

const handleSizeChange = (size: number) => {
  pagination.pageSize = size;
  pagination.page = 1;
  fetchTableData();
};

const handleCurrentChange = (page: number) => {
  pagination.page = page;
  fetchTableData();
};

const handleSortChange = ({ prop, order }: any) => {
  // TODO: 实现排序功能
  console.log('排序:', prop, order);
};

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString();
};

const resetFormData = () => {
  Object.assign(formData, {
    category_id: 0,
    name: '',
    sort_order: 10,
    status: 1
  });
};

const handleCreate = () => {
  dialogMode.value = 'create';
  resetFormData();
  dialogVisible.value = true;
};

const handleView = (row: any) => {
  dialogMode.value = 'view';
  Object.assign(formData, row);
  dialogVisible.value = true;
};

const handleEdit = (row: any) => {
  dialogMode.value = 'edit';
  Object.assign(formData, row);
  dialogVisible.value = true;
};

const handleToggleStatus = async (row: any) => {
  try {
    const action = row.status === 1 ? '禁用' : '启用';
    await ElMessageBox.confirm(`确定要${action}该分类吗？`, '确认操作', {
      type: 'warning'
    });

    await updateCategory(row.category_id, {
      status: row.status === 1 ? 0 : 1
    });

    ElMessage.success(`${action}成功`);
    fetchTableData();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败');
    }
  }
};

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除该分类吗？删除后不可恢复！', '确认删除', {
      type: 'error'
    });

    await deleteCategory(row.category_id);
    ElMessage.success('删除成功');
    fetchTableData();
  } catch (error: any) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};

const handleSubmit = async () => {
  try {
    await formRef.value?.validate();
    submitting.value = true;

    if (dialogMode.value === 'create') {
      await createCategory({
        name: formData.name,
        sortOrder: formData.sort_order,
        status: formData.status
      });
      ElMessage.success('创建成功');
    } else {
      await updateCategory(formData.category_id, {
        name: formData.name,
        sortOrder: formData.sort_order,
        status: formData.status
      });
      ElMessage.success('更新成功');
    }

    dialogVisible.value = false;
    fetchTableData();
  } catch (error) {
    ElMessage.error('操作失败');
  } finally {
    submitting.value = false;
  }
};

// 初始化
onMounted(async () => {
  await fetchTableData();
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <!-- 页面标题 -->
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-medium">分类管理</span>
          <div class="flex gap-8px">
            <ElButton type="primary" @click="handleCreate">
              <template #icon>
                <icon-ic-round-add class="text-icon" />
              </template>
              新增分类
            </ElButton>
            <ElButton @click="handleRefresh">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              刷新
            </ElButton>
          </div>
        </div>
      </template>

      <!-- 搜索表单 -->
      <ElForm ref="searchFormRef" :model="searchForm" label-width="100px" class="mb-16px">
        <div class="grid grid-cols-1 gap-16px md:grid-cols-3">
          <ElFormItem label="分类名称">
            <ElInput v-model="searchForm.keyword" placeholder="请输入分类名称" clearable />
          </ElFormItem>

          <ElFormItem label="分类状态">
            <ElSelect v-model="searchForm.status" placeholder="选择分类状态" clearable class="w-full">
              <ElOption label="全部状态" value="" />
              <ElOption label="启用" :value="1" />
              <ElOption label="禁用" :value="0" />
            </ElSelect>
          </ElFormItem>

          <ElFormItem>
            <div class="flex gap-8px">
              <ElButton @click="handleReset">重置</ElButton>
              <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            </div>
          </ElFormItem>
        </div>
      </ElForm>
    </ElCard>

    <!-- 分类列表 -->
    <ElCard>
      <template #header>
        <span class="font-medium">分类列表</span>
      </template>

      <ElTable v-loading="loading" :data="tableData" class="w-full" @sort-change="handleSortChange">
        <ElTableColumn prop="category_id" label="ID" width="80" />
        <ElTableColumn prop="name" label="分类名称" min-width="200" />
        <ElTableColumn prop="product_count" label="商品数量" width="120">
          <template #default="{ row }">
            <ElTag type="info">{{ row.product_count || 0 }}</ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="status" label="状态" width="80">
          <template #default="{ row }">
            <ElTag :type="row.status === 1 ? 'success' : 'danger'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="sort_order" label="排序" width="80" sortable="custom" />
        <ElTableColumn prop="create_time" label="创建时间" width="180" sortable="custom">
          <template #default="{ row }">
            {{ formatDateTime(row.create_time) }}
          </template>
        </ElTableColumn>
        <ElTableColumn label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="flex gap-8px">
              <ElButton size="small" @click="handleView(row)">查看</ElButton>
              <ElButton size="small" type="primary" @click="handleEdit(row)">编辑</ElButton>
              <ElButton size="small" :type="row.status === 1 ? 'warning' : 'success'" @click="handleToggleStatus(row)">
                {{ row.status === 1 ? '禁用' : '启用' }}
              </ElButton>
              <ElButton size="small" type="danger" @click="handleDelete(row)">删除</ElButton>
            </div>
          </template>
        </ElTableColumn>
      </ElTable>

      <!-- 分页 -->
      <div class="mt-16px flex justify-center">
        <ElPagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </ElCard>

    <!-- 分类详情/编辑对话框 -->
    <ElDialog v-model="dialogVisible" :title="dialogTitle" width="600px" :close-on-click-modal="false">
      <ElForm ref="formRef" :model="formData" :rules="formRules" label-width="120px" class="space-y-16px">
        <ElFormItem label="分类名称" prop="name">
          <ElInput v-model="formData.name" placeholder="请输入分类名称" :disabled="dialogMode === 'view'" />
        </ElFormItem>

        <ElFormItem label="排序权重" prop="sort_order">
          <ElInputNumber
            v-model="formData.sort_order"
            :min="0"
            placeholder="请输入排序权重"
            class="w-full"
            :disabled="dialogMode === 'view'"
          />
        </ElFormItem>

        <ElFormItem label="分类状态" prop="status">
          <ElRadioGroup v-model="formData.status" :disabled="dialogMode === 'view'">
            <ElRadio :value="1">启用</ElRadio>
            <ElRadio :value="0">禁用</ElRadio>
          </ElRadioGroup>
        </ElFormItem>
      </ElForm>

      <template #footer>
        <div class="flex justify-end gap-8px">
          <ElButton @click="dialogVisible = false">取消</ElButton>
          <ElButton v-if="dialogMode !== 'view'" type="primary" :loading="submitting" @click="handleSubmit">
            确定
          </ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>
