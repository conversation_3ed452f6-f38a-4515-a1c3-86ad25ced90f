<script setup lang="ts">
import { defineAsyncComponent, onMounted, reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { request } from '@/service/request';
import UserPermissionPreview from '@/components/permission/UserPermissionPreview.vue';
// 复用已有三大页面的主体组件（后续可抽离更细）
const PermissionManagement = defineAsyncComponent(
  () => import('@/views/system-center/permission-management/index.vue')
);
const RoleManagement = defineAsyncComponent(() => import('@/views/system-center/role-management/index.vue'));
const UserManagement = defineAsyncComponent(() => import('@/views/user/index.vue'));

const activeTab = ref<'permission' | 'role' | 'user'>('user');

// 跨Tab联动：记录当前选中角色与用户
const selectedRole = ref<any | null>(null);
const selectedUserId = ref<number | null>(null);

// 顶部工具条：状态与统计
const syncing = ref(false);
const stats = reactive({ permissions: 0, permissionsEnabled: 0, roles: 0, rolesEnabled: 0, users: 0, usersEnabled: 0 });

function handleRoleSelected(role: any) {
  selectedRole.value = role;
  // 切换到用户Tab并按角色过滤
  activeTab.value = 'user';
}

function handleViewUserPermissions(userId: number) {
  selectedUserId.value = userId;
  // 切换到权限Tab并展示该用户的权限预览
  activeTab.value = 'permission';
}

async function onSync() {
  syncing.value = true;
  try {
    const res = await request({ url: '/api/permission-sync/sync', method: 'post' });
    const synced = (res as any)?.syncedCount ?? 0;
    const updated = (res as any)?.updatedCount ?? 0;
    ElMessage.success(`权限同步成功：新增 ${synced}，更新 ${updated}`);
    await loadStats();
  } finally {
    syncing.value = false;
  }
}

async function onCheck() {
  const res = await request({ url: '/api/permission-sync/check', method: 'get' });
  const missing = (res as any)?.missingPermissions?.length ?? 0;
  const extra = (res as any)?.extraPermissions?.length ?? 0;
  ElMessage.success(`完整性检查完成：缺失 ${missing}，多余 ${extra}`);
}

async function onCleanup() {
  await ElMessageBox.confirm('将删除数据库中配置外的多余权限，是否继续？', '清理多余权限', { type: 'warning' });
  const res = await request({ url: '/api/permission-sync/cleanup', method: 'delete' });
  const removed = (res as any)?.removedCount ?? 0;
  ElMessage.success(`清理完成：删除 ${removed} 个多余权限`);
  await loadStats();
}

function toCsv(rows: any[], headers: Record<string, string>) {
  const keys = Object.keys(headers);
  const head = keys.map(k => headers[k]).join(',');
  const body = rows
    .map(r =>
      keys
        .map(k => {
          const v = r?.[k];
          const s = v === undefined || v === null ? '' : String(v).replace(/"/g, '""');
          return `"${s}"`;
        })
        .join(',')
    )
    .join('\n');
  return `${head}\n${body}`;
}

function downloadCsv(csv: string, filename: string) {
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  a.click();
  URL.revokeObjectURL(url);
}

function normalizeListResponse(res: any) {
  if (!res) return { list: [], total: 0 };
  if (Array.isArray(res?.list)) return { list: res.list, total: res.total ?? res.list.length };
  if (Array.isArray(res?.data)) return { list: res.data, total: res.total ?? res.data.length };
  if (Array.isArray(res)) return { list: res, total: res.length };
  return { list: [], total: 0 };
}

async function fetchAll(url: string, params: Record<string, any> = {}) {
  const pageSize = 1000;
  let page = 1;
  let all: any[] = [];
  let total = Infinity;
  while (all.length < total) {
    const res = await request({ url, method: 'get', params: { page, pageSize, ...params } });
    const { list, total: t } = normalizeListResponse(res);
    total = Number.isFinite(t) ? t : list.length;
    all = all.concat(list);
    if (!list.length) break;
    page += 1;
  }
  return all;
}

async function exportPermissions() {
  const list = await fetchAll('/api/permission/list');
  const headers = {
    permission_code: '代码',
    permission_name: '名称',
    permission_type: '类型',
    permission_group: '分组',
    status: '状态'
  };
  const csv = toCsv(list, headers);
  downloadCsv(csv, 'permissions.csv');
}

async function exportRoles() {
  const list = await fetchAll('/api/role/list');
  const headers = { role_code: '代码', role_name: '名称', role_level: '级别', status: '状态' };
  const csv = toCsv(list, headers);
  downloadCsv(csv, 'roles.csv');
}

async function exportUsers() {
  const list = await fetchAll('/api/user/list');
  const headers = { userId: '用户ID', username: '用户名', email: '邮箱', status: '状态' };
  const csv = toCsv(list, headers);
  downloadCsv(csv, 'users.csv');
}

async function loadStats() {
  try {
    const [permRes, permOnRes, roleRes, roleOnRes, userRes, userOnRes] = await Promise.all([
      request({ url: '/api/permission/list', method: 'get', params: { page: 1, pageSize: 1 } }),
      request({ url: '/api/permission/list', method: 'get', params: { page: 1, pageSize: 1, status: 1 } }),
      request({ url: '/api/role/list', method: 'get', params: { page: 1, pageSize: 1 } }),
      request({ url: '/api/role/list', method: 'get', params: { page: 1, pageSize: 1, status: 1 } }),
      request({ url: '/api/user/list', method: 'get', params: { page: 1, pageSize: 1 } }),
      request({ url: '/api/user/list', method: 'get', params: { page: 1, pageSize: 1, status: 1 } })
    ]);
    const getTotal = (r: any) => r?.total ?? (Array.isArray(r?.list) ? r.list.length : Array.isArray(r) ? r.length : 0);
    stats.permissions = getTotal(permRes);
    stats.permissionsEnabled = getTotal(permOnRes);
    stats.roles = getTotal(roleRes);
    stats.rolesEnabled = getTotal(roleOnRes);
    stats.users = getTotal(userRes);
    stats.usersEnabled = getTotal(userOnRes);
  } catch (err) {
    // 统计失败不阻塞渲染
    console.warn('加载统计失败', err);
  }
}

onMounted(() => {
  loadStats();
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-medium">权限 · 角色 · 用户 管理中心</span>
        </div>
      </template>

      <!-- 顶部工具条：同步/检查/清理/导出 + 统计卡 -->
      <div class="mb-12px flex flex-col gap-12px">
        <div class="flex items-center gap-8px">
          <ElButton :loading="syncing" @click="onSync">同步权限</ElButton>
          <ElButton @click="onCheck">检查完整性</ElButton>
          <ElPopconfirm
            title="将删除数据库中配置外的多余权限，是否继续？"
            confirm-button-text="确认"
            cancel-button-text="取消"
            @confirm="onCleanup"
          >
            <template #reference>
              <ElButton type="danger">清理多余</ElButton>
            </template>
          </ElPopconfirm>
          <ElDropdown>
            <ElButton>导出</ElButton>
            <template #dropdown>
              <ElDropdownMenu>
                <ElDropdownItem @click="exportPermissions">导出权限</ElDropdownItem>
                <ElDropdownItem @click="exportRoles">导出角色</ElDropdownItem>
                <ElDropdownItem @click="exportUsers">导出用户</ElDropdownItem>
              </ElDropdownMenu>
            </template>
          </ElDropdown>
        </div>
        <div class="grid grid-cols-1 gap-12px sm:grid-cols-3">
          <ElCard shadow="hover">
            <div class="text-12px text-gray-500">权限</div>
            <div class="text-20px font-600">{{ stats.permissions }}</div>
            <div class="text-12px text-gray-500">启用 {{ stats.permissionsEnabled }}</div>
          </ElCard>
          <ElCard shadow="hover">
            <div class="text-12px text-gray-500">角色</div>
            <div class="text-20px font-600">{{ stats.roles }}</div>
            <div class="text-12px text-gray-500">启用 {{ stats.rolesEnabled }}</div>
          </ElCard>
          <ElCard shadow="hover">
            <div class="text-12px text-gray-500">用户</div>
            <div class="text-20px font-600">{{ stats.users }}</div>
            <div class="text-12px text-gray-500">启用 {{ stats.usersEnabled }}</div>
          </ElCard>
        </div>
      </div>

      <ElTabs v-model="activeTab" type="border-card">
        <ElTabPane label="用户" name="user" lazy>
          <UserManagement :preset-role-code="selectedRole?.role_code" @view-permissions="handleViewUserPermissions" />
        </ElTabPane>
        <ElTabPane label="角色" name="role" lazy>
          <RoleManagement @select-role="handleRoleSelected" />
        </ElTabPane>
        <ElTabPane label="权限" name="permission" lazy>
          <div class="space-y-12px">
            <PermissionManagement />
            <div v-if="selectedUserId !== null" class="mt-16px">
              <ElDivider>选中用户的有效权限预览</ElDivider>
              <Suspense>
                <UserPermissionPreview :user-id="selectedUserId" />
              </Suspense>
            </div>
          </div>
        </ElTabPane>
      </ElTabs>
    </ElCard>
  </div>
</template>
