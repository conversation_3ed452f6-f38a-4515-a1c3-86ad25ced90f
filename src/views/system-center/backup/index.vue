<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, Refresh, RefreshRight, Search } from '@element-plus/icons-vue';
import {
  BACKUP_STATUS_TEXT,
  BACKUP_STATUS_TYPE,
  BACKUP_TYPE_TEXT,
  fetchBackupList,
  fetchBackupStats,
  fetchCreateDatabaseBackup,
  fetchDeleteBackup,
  fetchRestoreDatabaseBackup
} from '@/service/api/backup';

defineOptions({
  name: 'BackupManagement'
});

// 响应式数据
const loading = ref(false);
const creating = ref(false);

// 统计数据
const stats = ref({
  overview: {
    totalBackups: 0,
    successBackups: 0,
    todayBackups: 0,
    totalSizeMB: '0',
    lastBackupTime: null
  },
  typeStats: []
});

// 表格数据
const tableData = ref([]);
const pagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
});

// 搜索参数
const searchParams = reactive({
  backupType: '',
  status: ''
});

// 加载统计数据
async function loadStats() {
  try {
    const response = await fetchBackupStats();
    stats.value = response.data;
  } catch (error) {
    console.error('获取备份统计失败:', error);
    ElMessage.error('获取备份统计失败');
  }
}

// 加载备份列表
async function loadBackupList() {
  try {
    loading.value = true;
    const response = await fetchBackupList({
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchParams
    });

    tableData.value = response.data.list;
    pagination.total = response.data.pagination.total;
  } catch (error) {
    console.error('获取备份列表失败:', error);
    ElMessage.error('获取备份列表失败');
  } finally {
    loading.value = false;
  }
}

// 创建备份
async function handleCreateBackup() {
  try {
    const { value: description } = await ElMessageBox.prompt('请输入备份描述（可选）', '创建数据库备份', {
      confirmButtonText: '开始备份',
      cancelButtonText: '取消',
      inputValue: '手动备份'
    });

    creating.value = true;
    await fetchCreateDatabaseBackup({ description });

    ElMessage.success('备份创建成功');
    refreshData();
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('创建备份失败:', error);
      ElMessage.error(error.message || '创建备份失败');
    }
  } finally {
    creating.value = false;
  }
}

// 恢复备份
async function handleRestoreBackup(row: any) {
  try {
    await ElMessageBox.confirm(
      `确定要恢复备份 "${row.backupName}" 吗？\n\n⚠️ 警告：此操作将覆盖当前数据库，请确保已做好准备！`,
      '确认恢复备份',
      {
        confirmButtonText: '确定恢复',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    );

    loading.value = true;
    await fetchRestoreDatabaseBackup(row.backupId);

    ElMessage.success('数据库恢复成功');
    refreshData();
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('恢复备份失败:', error);
      ElMessage.error(error.message || '恢复备份失败');
    }
  } finally {
    loading.value = false;
  }
}

// 删除备份
async function handleDeleteBackup(row: any) {
  try {
    await ElMessageBox.confirm(`确定要删除备份 "${row.backupName}" 吗？`, '确认删除', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning'
    });

    await fetchDeleteBackup(row.backupId);
    ElMessage.success('备份删除成功');
    refreshData();
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除备份失败:', error);
      ElMessage.error(error.message || '删除备份失败');
    }
  }
}

// 搜索备份
function searchBackups() {
  pagination.page = 1;
  loadBackupList();
}

// 重置搜索
function resetSearch() {
  Object.assign(searchParams, {
    backupType: '',
    status: ''
  });
  pagination.page = 1;
  loadBackupList();
}

// 分页处理
function handleSizeChange(size: number) {
  pagination.pageSize = size;
  pagination.page = 1;
  loadBackupList();
}

function handlePageChange(page: number) {
  pagination.page = page;
  loadBackupList();
}

// 刷新数据
function refreshData() {
  loadStats();
  loadBackupList();
}

// 生命周期
onMounted(() => {
  loadStats();
  loadBackupList();
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <!-- 备份统计 -->
    <ElCard>
      <template #header>
        <div class="flex items-center gap-12px">
          <SvgIcon icon="carbon:data-backup" class="text-24px text-primary" />
          <span class="text-18px font-semibold">备份统计</span>
        </div>
      </template>

      <div class="grid grid-cols-1 gap-16px lg:grid-cols-4 md:grid-cols-2">
        <div class="rounded-8px bg-blue-50 p-16px">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-12px text-gray-500">总备份数</div>
              <div class="text-24px text-blue-600 font-bold">{{ stats.overview.totalBackups }}</div>
            </div>
            <SvgIcon icon="carbon:data-backup" class="text-32px text-blue-500" />
          </div>
        </div>

        <div class="rounded-8px bg-green-50 p-16px">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-12px text-gray-500">成功备份</div>
              <div class="text-24px text-green-600 font-bold">{{ stats.overview.successBackups }}</div>
            </div>
            <SvgIcon icon="carbon:checkmark" class="text-32px text-green-500" />
          </div>
        </div>

        <div class="rounded-8px bg-purple-50 p-16px">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-12px text-gray-500">今日备份</div>
              <div class="text-24px text-purple-600 font-bold">{{ stats.overview.todayBackups }}</div>
            </div>
            <SvgIcon icon="carbon:calendar" class="text-32px text-purple-500" />
          </div>
        </div>

        <div class="rounded-8px bg-orange-50 p-16px">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-12px text-gray-500">总大小</div>
              <div class="text-24px text-orange-600 font-bold">{{ stats.overview.totalSizeMB }}MB</div>
            </div>
            <SvgIcon icon="carbon:data-volume" class="text-32px text-orange-500" />
          </div>
        </div>
      </div>

      <div v-if="stats.overview.lastBackupTime" class="mt-16px text-center text-gray-500">
        最近备份时间：{{ stats.overview.lastBackupTime }}
      </div>
    </ElCard>

    <!-- 备份管理 -->
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-12px">
            <SvgIcon icon="carbon:data-backup" class="text-24px text-primary" />
            <span class="text-18px font-semibold">备份管理</span>
          </div>
          <div class="flex items-center gap-8px">
            <ElButton type="primary" :icon="Plus" :loading="creating" @click="handleCreateBackup">创建备份</ElButton>
            <ElButton type="primary" :icon="Refresh" :loading="loading" @click="refreshData">刷新</ElButton>
          </div>
        </div>
      </template>

      <div class="space-y-16px">
        <!-- 搜索筛选 -->
        <div class="flex flex-wrap gap-16px">
          <ElSelect v-model="searchParams.backupType" placeholder="备份类型" style="width: 120px" clearable>
            <ElOption v-for="(text, value) in BACKUP_TYPE_TEXT" :key="value" :label="text" :value="value" />
          </ElSelect>
          <ElSelect v-model="searchParams.status" placeholder="备份状态" style="width: 120px" clearable>
            <ElOption v-for="(text, value) in BACKUP_STATUS_TEXT" :key="value" :label="text" :value="value" />
          </ElSelect>
          <ElButton type="primary" :icon="Search" @click="searchBackups">搜索</ElButton>
          <ElButton :icon="RefreshRight" @click="resetSearch">重置</ElButton>
        </div>

        <!-- 备份列表表格 -->
        <ElTable :data="tableData" :loading="loading" stripe border height="500">
          <ElTableColumn prop="backupName" label="备份名称" min-width="200" show-overflow-tooltip />
          <ElTableColumn prop="backupType" label="类型" width="120">
            <template #default="{ row }">
              <ElTag size="small">{{ BACKUP_TYPE_TEXT[row.backupType] || row.backupType }}</ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="fileSizeMB" label="文件大小" width="100">
            <template #default="{ row }">{{ row.fileSizeMB }}MB</template>
          </ElTableColumn>
          <ElTableColumn prop="status" label="状态" width="100">
            <template #default="{ row }">
              <ElTag :type="BACKUP_STATUS_TYPE[row.status] as any" size="small">
                {{ BACKUP_STATUS_TEXT[row.status] || row.status }}
              </ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="creatorName" label="创建者" width="120" />
          <ElTableColumn prop="createTime" label="创建时间" width="160" />
          <ElTableColumn prop="restoreTime" label="恢复时间" width="160">
            <template #default="{ row }">
              {{ row.restoreTime || '-' }}
            </template>
          </ElTableColumn>
          <ElTableColumn prop="description" label="描述" min-width="150" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.description || '-' }}
            </template>
          </ElTableColumn>
          <ElTableColumn label="操作" width="180" fixed="right">
            <template #default="{ row }">
              <div class="flex gap-8px">
                <ElButton
                  v-if="row.status === 'completed' && row.fileExists"
                  size="small"
                  type="success"
                  @click="handleRestoreBackup(row)"
                >
                  恢复
                </ElButton>
                <ElButton size="small" type="danger" @click="handleDeleteBackup(row)">删除</ElButton>
              </div>
            </template>
          </ElTableColumn>
        </ElTable>

        <!-- 分页 -->
        <div class="flex justify-center">
          <ElPagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </ElCard>
  </div>
</template>
