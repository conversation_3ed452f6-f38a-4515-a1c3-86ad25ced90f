<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useLicenseStore } from '@/store/modules/license';
import { useAuthStore } from '@/store/modules/auth';

defineOptions({ name: 'SystemLicense' });

const licenseStore = useLicenseStore();
const authStore = useAuthStore();

// 页面状态
const loading = ref(false);
const activating = ref(false);
const showActivateDialog = ref(false);
const licenseKey = ref('');

// 授权信息
const licenseInfo = computed(() => licenseStore.licenseInfo);
const isLicenseValid = computed(() => licenseStore.isLicenseValid);

// 格式化日期
function formatDate(date: string | null) {
  if (!date) return '未知';
  return new Date(date).toLocaleString('zh-CN');
}

// 获取状态显示文本
function getStatusText(status: string | null) {
  const statusMap: Record<string, string> = {
    active: '已激活',
    expired: '已过期',
    invalid: '无效',
    grace_period: '宽限期',
    suspended: '已暂停'
  };
  return statusMap[status || ''] || '未知';
}

// 获取状态颜色
function getStatusType(status: string | null) {
  const typeMap: Record<string, string> = {
    active: 'success',
    expired: 'danger',
    invalid: 'danger',
    grace_period: 'warning',
    suspended: 'warning'
  };
  return typeMap[status || ''] || 'info';
}

// 刷新授权状态
async function refreshLicenseStatus() {
  loading.value = true;
  try {
    await licenseStore.initLicenseStatus();
    ElMessage.success('授权状态已刷新');
  } catch (error) {
    console.error('刷新授权状态失败:', error);
    ElMessage.error('刷新授权状态失败');
  } finally {
    loading.value = false;
  }
}

// 激活授权
async function activateLicense() {
  if (!licenseKey.value.trim()) {
    ElMessage.warning('请输入授权密钥');
    return;
  }

  activating.value = true;
  try {
    const success = await licenseStore.validateLicenseKey(licenseKey.value.trim());
    if (success) {
      showActivateDialog.value = false;
      licenseKey.value = '';
      ElMessage.success('授权激活成功！');
      // 刷新授权状态
      await refreshLicenseStatus();
    } else {
      ElMessage.error('授权激活失败，请检查密钥是否正确');
    }
  } catch (error) {
    console.error('激活授权失败:', error);
    ElMessage.error('激活授权失败，请稍后重试');
  } finally {
    activating.value = false;
  }
}

// 重新绑定授权
async function rebindLicense() {
  try {
    await ElMessageBox.confirm('重新绑定将清除当前授权信息，确定要继续吗？', '确认重新绑定', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    // 调用重置授权方法
    const success = await licenseStore.resetLicense();
    if (success) {
      ElMessage.success('授权信息已重置，请重新激活');
      showActivateDialog.value = true;
      licenseKey.value = '';
    } else {
      ElMessage.error('重置授权信息失败');
    }
  } catch {
    // 用户取消
  }
}

// 页面初始化
onMounted(async () => {
  await refreshLicenseStatus();
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <!-- 授权认证 -->
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-12px">
            <SvgIcon icon="ic:round-verified" class="text-24px text-primary" />
            <span class="text-18px font-semibold">授权认证</span>
          </div>
          <div class="flex items-center gap-8px">
            <ElButton type="primary" @click="showActivateDialog = true">激活授权</ElButton>
            <ElButton type="success" :loading="loading" @click="refreshLicenseStatus">刷新状态</ElButton>
          </div>
        </div>
      </template>

      <!-- 授权状态概览 -->
      <div class="grid grid-cols-1 mb-24px gap-16px lg:grid-cols-3 md:grid-cols-2">
        <!-- 授权状态 -->
        <ElCard class="shadow-sm">
          <template #header>
            <div class="flex items-center gap-8px">
              <SvgIcon icon="carbon:security" class="text-16px text-primary" />
              <span class="text-14px font-medium">授权状态</span>
            </div>
          </template>
          <div class="text-center">
            <ElTag :type="getStatusType(licenseInfo.status) as any" size="large" class="mb-8px">
              {{ getStatusText(licenseInfo.status) }}
            </ElTag>
            <div class="text-12px text-gray-500">
              {{ isLicenseValid ? '系统已正常授权' : '系统需要授权验证' }}
            </div>
          </div>
        </ElCard>

        <!-- 绑定域名 -->
        <ElCard class="shadow-sm">
          <template #header>
            <div class="flex items-center gap-8px">
              <SvgIcon icon="carbon:earth" class="text-16px text-primary" />
              <span class="text-14px font-medium">绑定域名</span>
            </div>
          </template>
          <div class="text-center">
            <div class="mb-4px text-14px font-medium">{{ licenseInfo.domain || '未绑定' }}</div>
            <div class="text-12px text-gray-500">当前系统域名</div>
          </div>
        </ElCard>

        <!-- 到期时间 -->
        <ElCard class="shadow-sm">
          <template #header>
            <div class="flex items-center gap-8px">
              <SvgIcon icon="carbon:time" class="text-16px text-primary" />
              <span class="text-14px font-medium">到期时间</span>
            </div>
          </template>
          <div class="text-center">
            <div class="mb-4px text-14px font-medium">{{ formatDate(licenseInfo.expiryDate) }}</div>
            <div class="text-12px text-gray-500">授权有效期</div>
          </div>
        </ElCard>
      </div>

      <!-- 授权详细信息 -->
      <ElCard class="shadow-sm">
        <template #header>
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-8px">
              <SvgIcon icon="carbon:information" class="text-16px text-primary" />
              <span class="text-14px font-medium">授权详细信息</span>
            </div>
            <ElButton v-if="isLicenseValid" size="small" type="warning" @click="rebindLicense">重新绑定</ElButton>
          </div>
        </template>

        <div class="space-y-12px">
          <div class="flex items-center justify-between border-b border-gray-100 py-8px">
            <span class="text-14px text-gray-600">授权密钥</span>
            <span class="text-14px font-mono">
              {{ licenseInfo.licenseKey ? '●●●●●●●●●●●●●●●●' : '未设置' }}
            </span>
          </div>

          <div class="flex items-center justify-between border-b border-gray-100 py-8px">
            <span class="text-14px text-gray-600">绑定时间</span>
            <span class="text-14px">{{ formatDate(licenseInfo.boundAt) }}</span>
          </div>

          <div class="flex items-center justify-between border-b border-gray-100 py-8px">
            <span class="text-14px text-gray-600">授权计划</span>
            <span class="text-14px">{{ licenseInfo.plan || '未知' }}</span>
          </div>

          <div class="flex items-center justify-between border-b border-gray-100 py-8px">
            <span class="text-14px text-gray-600">计划描述</span>
            <span class="text-14px">{{ licenseInfo.planDescription || '无' }}</span>
          </div>

          <div class="flex items-center justify-between border-b border-gray-100 py-8px">
            <span class="text-14px text-gray-600">剩余天数</span>
            <span class="text-14px">{{ licenseInfo.daysLeft !== null ? `${licenseInfo.daysLeft} 天` : '未知' }}</span>
          </div>

          <div class="flex items-center justify-between border-b border-gray-100 py-8px">
            <span class="text-14px text-gray-600">最大产品数</span>
            <span class="text-14px">{{ licenseInfo.maxProducts !== null ? licenseInfo.maxProducts : '无限制' }}</span>
          </div>

          <div class="flex items-center justify-between border-b border-gray-100 py-8px">
            <span class="text-14px text-gray-600">最大用户数</span>
            <span class="text-14px">{{ licenseInfo.maxUsers !== null ? licenseInfo.maxUsers : '无限制' }}</span>
          </div>

          <div class="flex items-center justify-between border-b border-gray-100 py-8px">
            <span class="text-14px text-gray-600">验证次数</span>
            <span class="text-14px">{{ licenseInfo.validationCount || 0 }}</span>
          </div>

          <div class="flex items-center justify-between border-b border-gray-100 py-8px">
            <span class="text-14px text-gray-600">最后验证</span>
            <span class="text-14px">{{ formatDate(licenseInfo.lastExternalValidation || null) }}</span>
          </div>

          <div class="flex items-center justify-between py-8px">
            <span class="text-14px text-gray-600">用户角色</span>
            <ElTag size="small">{{ authStore.userInfo.roles.join(', ') }}</ElTag>
          </div>
        </div>
      </ElCard>

      <!-- 计划功能特性 -->
      <ElCard v-if="licenseInfo.planFeatures" class="shadow-sm">
        <template #header>
          <div class="flex items-center gap-8px">
            <SvgIcon icon="carbon:list-checked" class="text-16px text-primary" />
            <span class="text-14px font-medium">计划功能特性</span>
          </div>
        </template>

        <div class="space-y-8px">
          <div v-for="(feature, index) in licenseInfo.planFeatures" :key="index" class="flex items-center gap-8px">
            <SvgIcon icon="carbon:checkmark" class="text-12px text-green-500" />
            <span class="text-14px">{{ feature }}</span>
          </div>
        </div>
      </ElCard>

      <!-- 使用统计 -->
      <ElCard v-if="licenseInfo.usageStats" class="shadow-sm">
        <template #header>
          <div class="flex items-center gap-8px">
            <SvgIcon icon="carbon:analytics" class="text-16px text-primary" />
            <span class="text-14px font-medium">使用统计</span>
          </div>
        </template>

        <div class="grid grid-cols-2 gap-16px">
          <div v-for="(value, key) in licenseInfo.usageStats" :key="key" class="text-center">
            <div class="text-18px text-primary font-semibold">{{ value }}</div>
            <div class="text-12px text-gray-500">{{ key }}</div>
          </div>
        </div>
      </ElCard>

      <!-- 授权说明 -->
      <ElAlert title="授权说明" type="info" :closable="false" class="mt-16px">
        <template #default>
          <div class="text-12px space-y-4px">
            <p>• 系统需要有效的授权密钥才能正常使用所有功能</p>
            <p>• 授权密钥与当前域名绑定，更换域名需要重新激活</p>
            <p>• 授权过期后系统将进入受限模式，请及时续费</p>
            <p>• 如有授权相关问题，请联系技术支持</p>
          </div>
        </template>
      </ElAlert>
    </ElCard>

    <!-- 激活授权对话框 -->
    <ElDialog v-model="showActivateDialog" title="激活授权" width="500px" :close-on-click-modal="false">
      <div class="space-y-16px">
        <ElAlert title="请输入授权密钥" type="info" :closable="false">
          <template #default>
            <div class="text-12px">请输入您购买的授权密钥，系统将自动验证并激活授权。</div>
          </template>
        </ElAlert>

        <ElInput
          v-model="licenseKey"
          type="textarea"
          :rows="4"
          placeholder="请输入授权密钥..."
          :disabled="activating"
        />

        <div class="text-12px text-gray-500">
          <p>• 授权密钥区分大小写，请准确输入</p>
          <p>• 每个密钥只能绑定一个域名</p>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end gap-8px">
          <ElButton :disabled="activating" @click="showActivateDialog = false">取消</ElButton>
          <ElButton type="primary" :loading="activating" @click="activateLicense">
            {{ activating ? '激活中...' : '激活授权' }}
          </ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<style scoped>
.card-wrapper {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
