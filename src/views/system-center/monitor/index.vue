<script setup lang="ts">
import { computed, onMounted, onUnmounted, reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { Refresh } from '@element-plus/icons-vue';
import {
  HEALTH_STATUS_TEXT,
  HEALTH_STATUS_TYPE,
  MONITOR_THRESHOLDS,
  REALTIME_CONFIG,
  SERVICE_STATUS_TEXT,
  SERVICE_STATUS_TYPE,
  fetchApplicationMonitor,
  fetchDatabaseMonitor,
  fetchPerformanceMetrics,
  fetchSecurityMonitor,
  fetchSystemHealth,
  fetchSystemResources,
  getStatusLevel,
  monitorFormatters
} from '@/service/api/monitor';
import { formatters } from '@/service/api/statistics';

defineOptions({
  name: 'SystemMonitor'
});

// 响应式数据
const loading = ref(false);
const activeTab = ref('performance');
const autoRefresh = ref(true);
let refreshTimer: NodeJS.Timeout | null = null;

// 监控数据
const systemHealth = ref({
  status: 'healthy',
  checks: {
    uptime: 0,
    memoryUsage: { heapUsed: 0, heapTotal: 0 },
    cpuUsage: { user: 0, system: 0 },
    loadAverage: [0, 0, 0],
    averageResponseTime: 0,
    alerts: []
  }
});

const performanceData = ref<any>({
  stats: {
    totalRequests: 0,
    requestsPerSecond: 0,
    errorRate: 0,
    averageResponseTime: 0
  },
  metrics: [],
  alerts: []
});

const systemResources = ref({
  memory: { total: 0, free: 0, used: 0, usagePercent: 0 },
  cpu: {
    info: { model: '', cores: 0, speed: 0 },
    loadAverage: [0, 0, 0],
    usagePercent: 0
  },
  disk: { total: 0, free: 0, used: 0, usagePercent: 0 },
  system: { platform: '', arch: '', hostname: '', nodeVersion: '' }
});

const securityData = ref<any>({
  stats: {
    totalEvents: 0,
    eventsBySeverity: { CRITICAL: 0, WARNING: 0, INFO: 0 }
  },
  ipStats: []
});

const databaseData = ref<any>({
  connection: { status: 'unknown', error: null },
  size: { size_mb: 0 },
  tables: []
});

const applicationData = ref<any>({
  onlineUsers: 0,
  version: '1.0.0',
  environment: 'development',
  uptime: 0,
  apiStats: []
});

// 计算属性和方法
const slowRequests = computed(() => {
  return performanceData.value.metrics.filter((metric: any) => metric.responseTime > 1000);
});

// 获取内存使用率
function getMemoryUsagePercent(): number {
  const memory = systemHealth.value.checks?.memoryUsage;
  if (!memory || !memory.heapTotal) return 0;
  return (memory.heapUsed / memory.heapTotal) * 100;
}

// 获取CPU负载百分比
function getCpuLoadPercent(): number {
  const loadAverage = systemHealth.value.checks?.loadAverage;
  if (!loadAverage || loadAverage.length === 0) return 0;
  return (loadAverage[0] / 4) * 100; // 假设4核CPU
}

// 获取内存状态样式类
function getMemoryStatusClass(): string {
  const percent = getMemoryUsagePercent();
  const status = getStatusLevel(percent, MONITOR_THRESHOLDS.memory);
  const statusClasses: Record<string, string> = {
    healthy: 'bg-green-50 text-green-600',
    warning: 'bg-orange-50 text-orange-600',
    critical: 'bg-red-50 text-red-600'
  };
  return statusClasses[status] || 'bg-gray-50 text-gray-600';
}

// 获取CPU状态样式类
function getCpuStatusClass(): string {
  const percent = getCpuLoadPercent();
  const status = getStatusLevel(percent, MONITOR_THRESHOLDS.cpu);
  const statusClasses: Record<string, string> = {
    healthy: 'bg-green-50 text-green-600',
    warning: 'bg-orange-50 text-orange-600',
    critical: 'bg-red-50 text-red-600'
  };
  return statusClasses[status] || 'bg-gray-50 text-gray-600';
}

// 获取响应时间状态样式类
function getResponseTimeStatusClass(): string {
  const responseTime = systemHealth.value.checks?.averageResponseTime || 0;
  const status = getStatusLevel(responseTime, MONITOR_THRESHOLDS.responseTime);
  const statusClasses: Record<string, string> = {
    healthy: 'bg-green-50 text-green-600',
    warning: 'bg-orange-50 text-orange-600',
    critical: 'bg-red-50 text-red-600'
  };
  return statusClasses[status] || 'bg-gray-50 text-gray-600';
}

// 获取错误率样式类
function getErrorRateClass(): string {
  const errorRate = performanceData.value.stats?.errorRate || 0;
  const status = getStatusLevel(errorRate, MONITOR_THRESHOLDS.errorRate);
  const statusClasses: Record<string, string> = {
    healthy: 'text-green-600',
    warning: 'text-orange-600',
    critical: 'text-red-600'
  };
  return statusClasses[status] || 'text-gray-600';
}

// 获取内存使用率样式类
function getMemoryUsageClass(): string {
  const percent = systemResources.value.memory?.usagePercent || 0;
  const status = getStatusLevel(percent, MONITOR_THRESHOLDS.memory);
  const statusClasses: Record<string, string> = {
    healthy: 'text-green-600',
    warning: 'text-orange-600',
    critical: 'text-red-600'
  };
  return statusClasses[status] || 'text-gray-600';
}

// 获取磁盘使用率样式类
function getDiskUsageClass(): string {
  const percent = systemResources.value.disk?.usagePercent || 0;
  const status = getStatusLevel(percent, MONITOR_THRESHOLDS.disk);
  const statusClasses: Record<string, string> = {
    healthy: 'text-green-600',
    warning: 'text-orange-600',
    critical: 'text-red-600'
  };
  return statusClasses[status] || 'text-gray-600';
}

// 加载系统健康状态
async function loadSystemHealth() {
  try {
    const response = await fetchSystemHealth();
    systemHealth.value = response;
  } catch (error) {
    console.error('获取系统健康状态失败:', error);
    ElMessage.error('获取系统健康状态失败');
  }
}

// 加载性能指标
async function loadPerformanceMetrics() {
  try {
    const response = await fetchPerformanceMetrics({ limit: 100, type: 'all' });
    performanceData.value = response;
  } catch (error) {
    console.error('获取性能指标失败:', error);
    ElMessage.error('获取性能指标失败');
  }
}

// 加载系统资源
async function loadSystemResources() {
  try {
    const response = await fetchSystemResources();
    systemResources.value = response;
  } catch (error) {
    console.error('获取系统资源失败:', error);
    ElMessage.error('获取系统资源失败');
  }
}

// 加载安全监控数据
async function loadSecurityMonitor() {
  try {
    const response = await fetchSecurityMonitor();
    securityData.value = response;
  } catch (error) {
    console.error('获取安全监控数据失败:', error);
    ElMessage.error('获取安全监控数据失败');
  }
}

// 加载数据库监控
async function loadDatabaseMonitor() {
  try {
    const response = await fetchDatabaseMonitor();
    databaseData.value = response;
  } catch (error) {
    console.error('获取数据库监控失败:', error);
    ElMessage.error('获取数据库监控失败');
  }
}

// 加载应用监控
async function loadApplicationMonitor() {
  try {
    const response = await fetchApplicationMonitor();
    applicationData.value = response;
  } catch (error) {
    console.error('获取应用监控失败:', error);
    ElMessage.error('获取应用监控失败');
  }
}

// 标签切换处理
function handleTabChange(tabName: string | number) {
  activeTab.value = String(tabName);
  switch (tabName) {
    case 'performance':
      loadPerformanceMetrics();
      break;
    case 'resources':
      loadSystemResources();
      break;
    case 'security':
      loadSecurityMonitor();
      break;
    case 'database':
      loadDatabaseMonitor();
      break;
    case 'application':
      loadApplicationMonitor();
      break;
  }
}

// 自动刷新切换
function handleAutoRefreshChange(value: string | number | boolean) {
  const boolValue = Boolean(value);
  if (boolValue) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
}

// 开始自动刷新
function startAutoRefresh() {
  if (refreshTimer) return;

  refreshTimer = setInterval(() => {
    refreshCurrentTabData();
  }, REALTIME_CONFIG.refreshInterval);
}

// 停止自动刷新
function stopAutoRefresh() {
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
}

// 刷新当前标签数据
function refreshCurrentTabData() {
  // 始终刷新系统健康状态
  loadSystemHealth();

  // 根据当前标签刷新对应数据
  switch (activeTab.value) {
    case 'performance':
      loadPerformanceMetrics();
      break;
    case 'resources':
      loadSystemResources();
      break;
    case 'security':
      loadSecurityMonitor();
      break;
    case 'database':
      loadDatabaseMonitor();
      break;
    case 'application':
      loadApplicationMonitor();
      break;
  }
}

// 手动刷新数据
function refreshData() {
  loading.value = true;
  Promise.all([loadSystemHealth(), loadPerformanceMetrics(), loadSystemResources()]).finally(() => {
    loading.value = false;
  });
}

// 生命周期
onMounted(() => {
  loadSystemHealth();
  loadPerformanceMetrics();

  if (autoRefresh.value) {
    startAutoRefresh();
  }
});

onUnmounted(() => {
  stopAutoRefresh();
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <!-- 系统健康状态 -->
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-12px">
            <SvgIcon icon="carbon:health-cross" class="text-24px text-primary" />
            <span class="text-18px font-semibold">系统健康状态</span>
          </div>
          <div class="flex items-center gap-8px">
            <ElTag :type="HEALTH_STATUS_TYPE[systemHealth.status] as any" size="large">
              {{ HEALTH_STATUS_TEXT[systemHealth.status] }}
            </ElTag>
            <ElButton type="primary" :icon="Refresh" :loading="loading" @click="refreshData">刷新</ElButton>
          </div>
        </div>
      </template>

      <div class="grid grid-cols-1 gap-16px lg:grid-cols-4 md:grid-cols-2">
        <!-- 系统运行时间 -->
        <div class="health-card bg-blue-50">
          <div class="mb-8px flex items-center gap-8px">
            <SvgIcon icon="carbon:time" class="text-16px text-blue-600" />
            <span class="text-14px text-blue-800 font-medium">运行时间</span>
          </div>
          <div class="text-20px text-blue-600 font-bold">
            {{ monitorFormatters.uptime(systemHealth.checks?.uptime || 0) }}
          </div>
        </div>

        <!-- 内存使用率 -->
        <div class="health-card" :class="getMemoryStatusClass()">
          <div class="mb-8px flex items-center gap-8px">
            <SvgIcon icon="carbon:chip" class="text-16px" />
            <span class="text-14px font-medium">内存使用率</span>
          </div>
          <div class="text-20px font-bold">{{ getMemoryUsagePercent() }}%</div>
        </div>

        <!-- CPU负载 -->
        <div class="health-card" :class="getCpuStatusClass()">
          <div class="mb-8px flex items-center gap-8px">
            <SvgIcon icon="carbon:processor" class="text-16px" />
            <span class="text-14px font-medium">CPU负载</span>
          </div>
          <div class="text-20px font-bold">{{ getCpuLoadPercent() }}%</div>
        </div>

        <!-- 响应时间 -->
        <div class="health-card" :class="getResponseTimeStatusClass()">
          <div class="mb-8px flex items-center gap-8px">
            <SvgIcon icon="carbon:timer" class="text-16px" />
            <span class="text-14px font-medium">平均响应时间</span>
          </div>
          <div class="text-20px font-bold">
            {{ monitorFormatters.responseTime(systemHealth.checks?.averageResponseTime || 0) }}
          </div>
        </div>
      </div>

      <!-- 告警信息 -->
      <div v-if="systemHealth.checks?.alerts?.length > 0" class="mt-16px">
        <div class="mb-8px text-14px text-red-600 font-medium">⚠️ 系统告警</div>
        <div class="space-y-4px">
          <ElAlert
            v-for="(alert, index) in systemHealth.checks.alerts"
            :key="index"
            :title="alert"
            type="warning"
            :closable="false"
            show-icon
          />
        </div>
      </div>
    </ElCard>

    <!-- 监控选项卡 -->
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-12px">
            <SvgIcon icon="carbon:dashboard" class="text-24px text-primary" />
            <span class="text-18px font-semibold">系统监控</span>
          </div>
          <div class="flex items-center gap-8px">
            <ElSwitch v-model="autoRefresh" active-text="自动刷新" @change="handleAutoRefreshChange" />
          </div>
        </div>
      </template>

      <ElTabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- 性能监控 -->
        <ElTabPane label="性能监控" name="performance">
          <div class="grid grid-cols-1 gap-16px lg:grid-cols-2">
            <!-- 性能统计 -->
            <div class="monitor-section">
              <div class="section-title">性能统计</div>
              <div class="space-y-8px">
                <div class="stat-row">
                  <span class="label">请求总数</span>
                  <span class="value">{{ formatters.number(performanceData.stats?.totalRequests || 0) }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">每秒请求数</span>
                  <span class="value">{{ (performanceData.stats?.requestsPerSecond || 0).toFixed(2) }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">错误率</span>
                  <span class="value" :class="getErrorRateClass()">
                    {{ (performanceData.stats?.errorRate || 0).toFixed(2) }}%
                  </span>
                </div>
                <div class="stat-row">
                  <span class="label">平均响应时间</span>
                  <span class="value">
                    {{ monitorFormatters.responseTime(performanceData.stats?.averageResponseTime || 0) }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 慢请求 -->
            <div class="monitor-section">
              <div class="section-title">慢请求 (>1s)</div>
              <div class="max-h-200px overflow-y-auto">
                <div v-if="slowRequests.length === 0" class="py-20px text-center text-gray-500">暂无慢请求</div>
                <div v-else class="space-y-8px">
                  <div v-for="(request, index) in slowRequests.slice(0, 10)" :key="index" class="slow-request-item">
                    <div class="flex items-center justify-between">
                      <span class="text-12px text-gray-600">{{ request.method }} {{ request.url }}</span>
                      <span class="text-12px text-red-600 font-medium">
                        {{ monitorFormatters.responseTime(request.responseTime) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ElTabPane>

        <!-- 资源监控 -->
        <ElTabPane label="资源监控" name="resources">
          <div class="grid grid-cols-1 gap-16px lg:grid-cols-2">
            <!-- 内存使用 -->
            <div class="monitor-section">
              <div class="section-title">内存使用情况</div>
              <div class="space-y-8px">
                <div class="stat-row">
                  <span class="label">总内存</span>
                  <span class="value">{{ monitorFormatters.bytes(systemResources.memory?.total || 0) }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">已使用</span>
                  <span class="value">{{ monitorFormatters.bytes(systemResources.memory?.used || 0) }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">可用内存</span>
                  <span class="value">{{ monitorFormatters.bytes(systemResources.memory?.free || 0) }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">使用率</span>
                  <span class="value" :class="getMemoryUsageClass()">
                    {{ monitorFormatters.percentage(systemResources.memory?.usagePercent || 0) }}
                  </span>
                </div>
              </div>
            </div>

            <!-- CPU信息 -->
            <div class="monitor-section">
              <div class="section-title">CPU信息</div>
              <div class="space-y-8px">
                <div class="stat-row">
                  <span class="label">CPU型号</span>
                  <span class="value text-12px">{{ systemResources.cpu?.info?.model || 'Unknown' }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">核心数</span>
                  <span class="value">{{ systemResources.cpu?.info?.cores || 0 }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">频率</span>
                  <span class="value">{{ systemResources.cpu?.info?.speed || 0 }}MHz</span>
                </div>
                <div class="stat-row">
                  <span class="label">负载平均值</span>
                  <span class="value">
                    {{ monitorFormatters.loadAverage(systemResources.cpu?.loadAverage || [0, 0, 0]) }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 磁盘使用 -->
            <div class="monitor-section">
              <div class="section-title">磁盘使用情况</div>
              <div class="space-y-8px">
                <div class="stat-row">
                  <span class="label">总空间</span>
                  <span class="value">{{ monitorFormatters.bytes(systemResources.disk?.total || 0) }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">已使用</span>
                  <span class="value">{{ monitorFormatters.bytes(systemResources.disk?.used || 0) }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">可用空间</span>
                  <span class="value">{{ monitorFormatters.bytes(systemResources.disk?.free || 0) }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">使用率</span>
                  <span class="value" :class="getDiskUsageClass()">
                    {{ monitorFormatters.percentage(systemResources.disk?.usagePercent || 0) }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 系统信息 -->
            <div class="monitor-section">
              <div class="section-title">系统信息</div>
              <div class="space-y-8px">
                <div class="stat-row">
                  <span class="label">操作系统</span>
                  <span class="value">{{ systemResources.system?.platform || 'Unknown' }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">架构</span>
                  <span class="value">{{ systemResources.system?.arch || 'Unknown' }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">主机名</span>
                  <span class="value">{{ systemResources.system?.hostname || 'Unknown' }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">Node版本</span>
                  <span class="value">{{ systemResources.system?.nodeVersion || 'Unknown' }}</span>
                </div>
              </div>
            </div>
          </div>
        </ElTabPane>

        <!-- 安全监控 -->
        <ElTabPane label="安全监控" name="security">
          <div class="grid grid-cols-1 gap-16px lg:grid-cols-2">
            <!-- 安全统计 -->
            <div class="monitor-section">
              <div class="section-title">安全事件统计</div>
              <div class="space-y-8px">
                <div class="stat-row">
                  <span class="label">总事件数</span>
                  <span class="value">{{ securityData.stats?.totalEvents || 0 }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">严重事件</span>
                  <span class="value text-red-600">{{ securityData.stats?.eventsBySeverity?.CRITICAL || 0 }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">警告事件</span>
                  <span class="value text-orange-600">{{ securityData.stats?.eventsBySeverity?.WARNING || 0 }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">信息事件</span>
                  <span class="value text-blue-600">{{ securityData.stats?.eventsBySeverity?.INFO || 0 }}</span>
                </div>
              </div>
            </div>

            <!-- IP访问统计 -->
            <div class="monitor-section">
              <div class="section-title">IP访问统计 (24小时)</div>
              <div class="max-h-200px overflow-y-auto">
                <div v-if="securityData.ipStats?.length === 0" class="py-20px text-center text-gray-500">
                  暂无访问记录
                </div>
                <div v-else class="space-y-4px">
                  <div v-for="(ip, index) in securityData.ipStats?.slice(0, 10)" :key="index" class="ip-stat-item">
                    <div class="flex items-center justify-between">
                      <span class="text-12px text-gray-600">{{ ip.client_ip }}</span>
                      <div class="flex gap-8px text-12px">
                        <span class="text-blue-600">{{ ip.requestCount }}次</span>
                        <span v-if="ip.failureCount > 0" class="text-red-600">{{ ip.failureCount }}失败</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ElTabPane>

        <!-- 数据库监控 -->
        <ElTabPane label="数据库监控" name="database">
          <div class="grid grid-cols-1 gap-16px lg:grid-cols-2">
            <!-- 连接状态 -->
            <div class="monitor-section">
              <div class="section-title">数据库连接</div>
              <div class="space-y-8px">
                <div class="stat-row">
                  <span class="label">连接状态</span>
                  <ElTag :type="SERVICE_STATUS_TYPE[databaseData.connection?.status || ''] as any" size="small">
                    {{ SERVICE_STATUS_TEXT[databaseData.connection?.status] }}
                  </ElTag>
                </div>
                <div v-if="databaseData.connection?.error" class="stat-row">
                  <span class="label">错误信息</span>
                  <span class="value text-12px text-red-600">{{ databaseData.connection.error }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">数据库大小</span>
                  <span class="value">{{ databaseData.size?.size_mb || 0 }}MB</span>
                </div>
              </div>
            </div>

            <!-- 表统计 -->
            <div class="monitor-section">
              <div class="section-title">数据表统计</div>
              <div class="max-h-200px overflow-y-auto">
                <div v-if="databaseData.tables?.length === 0" class="py-20px text-center text-gray-500">暂无表信息</div>
                <div v-else class="space-y-4px">
                  <div v-for="(table, index) in databaseData.tables?.slice(0, 10)" :key="index" class="table-stat-item">
                    <div class="flex items-center justify-between">
                      <span class="text-12px text-gray-600">{{ table.table_name }}</span>
                      <div class="flex gap-8px text-12px">
                        <span class="text-blue-600">{{ formatters.number(table.table_rows) }}行</span>
                        <span class="text-green-600">{{ table.size_mb }}MB</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ElTabPane>

        <!-- 应用监控 -->
        <ElTabPane label="应用监控" name="application">
          <div class="grid grid-cols-1 gap-16px lg:grid-cols-2">
            <!-- 应用状态 -->
            <div class="monitor-section">
              <div class="section-title">应用状态</div>
              <div class="space-y-8px">
                <div class="stat-row">
                  <span class="label">在线用户</span>
                  <span class="value text-green-600">{{ applicationData.onlineUsers || 0 }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">应用版本</span>
                  <span class="value">{{ applicationData.version || '1.0.0' }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">运行环境</span>
                  <span class="value">{{ applicationData.environment || 'development' }}</span>
                </div>
                <div class="stat-row">
                  <span class="label">运行时长</span>
                  <span class="value">{{ monitorFormatters.uptime(applicationData.uptime || 0) }}</span>
                </div>
              </div>
            </div>

            <!-- API统计 -->
            <div class="monitor-section">
              <div class="section-title">API统计 (1小时)</div>
              <div class="max-h-200px overflow-y-auto">
                <div v-if="applicationData.apiStats?.length === 0" class="py-20px text-center text-gray-500">
                  暂无API调用记录
                </div>
                <div v-else class="space-y-4px">
                  <div
                    v-for="(api, index) in applicationData.apiStats?.slice(0, 10)"
                    :key="index"
                    class="api-stat-item"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-12px text-gray-600">{{ api.request_path }}</span>
                      <div class="flex gap-8px text-12px">
                        <span class="text-blue-600">{{ api.requestCount }}次</span>
                        <span class="text-green-600">{{ api.successRate?.toFixed(1) }}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ElTabPane>
      </ElTabs>
    </ElCard>
  </div>
</template>

<style scoped>
.flex-col {
  display: flex;
  flex-direction: column;
}

.gap-16px {
  gap: 16px;
}

.health-card {
  padding: 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.health-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.monitor-section {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: white;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.stat-row .label {
  font-size: 12px;
  color: #909399;
}

.stat-row .value {
  font-size: 12px;
  font-weight: 600;
  color: #303133;
}

.slow-request-item,
.ip-stat-item,
.table-stat-item,
.api-stat-item {
  padding: 8px;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  background: #fafafa;
  transition: background-color 0.2s ease;
}

.slow-request-item:hover,
.ip-stat-item:hover,
.table-stat-item:hover,
.api-stat-item:hover {
  background: #f5f5f5;
}

.max-h-200px {
  max-height: 200px;
}

.overflow-y-auto {
  overflow-y: auto;
}

.space-y-4px > * + * {
  margin-top: 4px;
}

.space-y-8px > * + * {
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .health-card {
    padding: 12px;
  }

  .monitor-section {
    padding: 12px;
  }

  .section-title {
    font-size: 13px;
  }

  .stat-row .label,
  .stat-row .value {
    font-size: 11px;
  }
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
