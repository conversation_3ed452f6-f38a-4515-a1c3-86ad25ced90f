<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';
import {
  ElAlert,
  ElButton,
  ElCard,
  ElDivider,
  ElDropdown,
  ElDropdownItem,
  ElDropdownMenu,
  ElForm,
  ElFormItem,
  ElIcon,
  ElInput,
  ElInputNumber,
  ElMessage,
  ElMessageBox,
  ElSwitch,
  ElTabPane,
  ElTabs
} from 'element-plus';
import { Check, Refresh, Setting } from '@element-plus/icons-vue';
import { fetchSystemConfig, resetSystemConfig, testEmailConfig, updateSystemConfig } from '@/service/api/config';
import SystemStatus from './components/SystemStatus.vue';
import ConfigImportExport from './components/ConfigImportExport.vue';

defineOptions({
  name: 'SystemCenterConfig'
});

// 响应式数据
const loading = ref(false);
const saving = ref(false);
const testing = ref(false);

// 移动端检测
const isMobile = ref(false);
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

// 当前激活的标签页
const activeTab = ref('basic');

// 测试邮件地址
const testEmail = ref('');

// 配置表单
const configForm = ref<Api.Config.SystemConfig>({
  // 基础配置
  systemName: 'SoybeanAdmin',
  systemDescription: '基于Vue3 + TypeScript + Element Plus的现代化管理系统',
  systemVersion: '2.0.0',
  maintenanceMode: false,

  // 安全配置
  tokenExpiry: 24,
  passwordMinLength: 6,
  enableTwoFactor: false,
  maxLoginAttempts: 5,
  sessionTimeout: 30,

  // 邮件配置
  smtpHost: '',
  smtpPort: 587,
  emailFrom: '',
  emailPassword: '',
  emailSSL: true,

  // 存储配置
  maxUploadSize: 10,
  autoBackup: true,
  backupInterval: 24,
  maxBackupFiles: 7,

  // 功能开关
  enableRegistration: false,
  enableGuestAccess: false,
  enableApiDocs: true,

  // 其他配置（只读）
  dbHost: 'localhost',
  dbPort: 3306,
  dbName: 'newfd',
  dbPoolSize: 10,
  jwtSecret: '****',
  logLevel: 'info',
  logRetentionDays: 30,
  enableFileLog: true,
  enableMonitoring: true,
  monitoringInterval: 60,
  alertThreshold: 80
});

// 加载配置
async function loadConfig() {
  loading.value = true;
  try {
    const data = await fetchSystemConfig();
    if (data) {
      configForm.value = data;
      ElMessage.success('配置加载成功');
    } else {
      ElMessage.error('配置数据为空');
    }
  } catch (error) {
    console.error('加载配置失败:', error);
    ElMessage.error('加载配置失败');
  } finally {
    loading.value = false;
  }
}

// 验证配置
function validateConfig(): boolean {
  const config = configForm.value;

  // 基础配置验证
  if (!config.systemName?.trim()) {
    ElMessage.error('系统名称不能为空');
    activeTab.value = 'basic';
    return false;
  }

  // 安全配置验证
  if (config.tokenExpiry < 1 || config.tokenExpiry > 168) {
    ElMessage.error('Token过期时间必须在1-168小时之间');
    activeTab.value = 'security';
    return false;
  }

  if (config.passwordMinLength < 6 || config.passwordMinLength > 32) {
    ElMessage.error('密码最小长度必须在6-32位之间');
    activeTab.value = 'security';
    return false;
  }

  // 邮件配置验证
  if (config.smtpHost && !config.emailFrom) {
    ElMessage.error('配置了SMTP服务器时，发件人邮箱不能为空');
    activeTab.value = 'email';
    return false;
  }

  if (config.emailFrom && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(config.emailFrom)) {
    ElMessage.error('发件人邮箱格式不正确');
    activeTab.value = 'email';
    return false;
  }

  // 存储配置验证
  if (config.maxUploadSize < 1 || config.maxUploadSize > 100) {
    ElMessage.error('最大上传大小必须在1-100MB之间');
    activeTab.value = 'storage';
    return false;
  }

  return true;
}

// 保存配置
async function saveConfig() {
  // 验证配置
  if (!validateConfig()) {
    return;
  }

  saving.value = true;
  try {
    const data = await updateSystemConfig(configForm.value);
    configForm.value = data;
    ElMessage.success('配置保存成功');
  } catch (error) {
    console.error('保存配置失败:', error);
    ElMessage.error('保存配置失败');
  } finally {
    saving.value = false;
  }
}

// 重置配置
async function resetConfig() {
  try {
    await ElMessageBox.confirm('此操作将重置所有配置为默认值，确定要继续吗？', '确认重置', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    loading.value = true;
    const data = await resetSystemConfig();
    configForm.value = data;
    ElMessage.success('配置重置成功');
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('重置配置失败:', error);
      ElMessage.error('重置配置失败');
    }
  } finally {
    loading.value = false;
  }
}

// 测试邮件配置
async function handleTestEmail() {
  if (!testEmail.value) {
    ElMessage.warning('请输入测试邮箱地址');
    return;
  }

  if (!configForm.value.smtpHost || !configForm.value.emailFrom) {
    ElMessage.warning('请先配置SMTP服务器和发件人邮箱');
    return;
  }

  testing.value = true;
  try {
    await testEmailConfig({
      smtpHost: configForm.value.smtpHost,
      smtpPort: configForm.value.smtpPort,
      emailFrom: configForm.value.emailFrom,
      emailPassword: configForm.value.emailPassword || '',
      emailSSL: configForm.value.emailSSL,
      testEmail: testEmail.value
    });
    ElMessage.success('测试邮件发送成功');
  } catch (error) {
    console.error('测试邮件失败:', error);
    ElMessage.error('测试邮件发送失败');
  } finally {
    testing.value = false;
  }
}

// 处理配置导入
function handleConfigImport(importedConfig: Api.Config.SystemConfig) {
  configForm.value = { ...importedConfig };
  ElMessage.success('配置导入成功，请保存配置使其生效');
}

// 处理配置重置（从导入导出组件触发）
function handleConfigReset() {
  resetConfig();
}

// 配置预设
const configPresets = {
  development: {
    name: '开发环境',
    config: {
      tokenExpiry: 24,
      passwordMinLength: 6,
      enableTwoFactor: false,
      maxLoginAttempts: 10,
      sessionTimeout: 60,
      enableRegistration: true,
      enableGuestAccess: true,
      enableApiDocs: true
    }
  },
  production: {
    name: '生产环境',
    config: {
      tokenExpiry: 8,
      passwordMinLength: 8,
      enableTwoFactor: true,
      maxLoginAttempts: 3,
      sessionTimeout: 30,
      enableRegistration: false,
      enableGuestAccess: false,
      enableApiDocs: false
    }
  },
  security: {
    name: '高安全',
    config: {
      tokenExpiry: 4,
      passwordMinLength: 12,
      enableTwoFactor: true,
      maxLoginAttempts: 3,
      sessionTimeout: 15,
      enableRegistration: false,
      enableGuestAccess: false,
      enableApiDocs: false
    }
  }
};

// 应用配置预设
function applyPreset(presetKey: keyof typeof configPresets) {
  const preset = configPresets[presetKey];
  if (preset) {
    // 合并预设配置到当前配置
    Object.assign(configForm.value, preset.config);
    ElMessage.success(`已应用${preset.name}预设配置`);
  }
}

// 生命周期
onMounted(() => {
  checkMobile();
  window.addEventListener('resize', checkMobile);
  loadConfig();
});

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile);
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <!-- 系统状态 -->
    <SystemStatus />

    <!-- 配置管理 -->
    <ConfigImportExport :config="configForm" @import="handleConfigImport" @reset="handleConfigReset" />

    <!-- 系统配置 -->
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-12px">
            <SvgIcon icon="carbon:settings-adjust" class="text-24px text-primary" />
            <span class="text-18px font-semibold">系统配置</span>
          </div>
          <div class="flex items-center gap-8px">
            <!-- 配置预设 -->
            <ElDropdown @command="applyPreset">
              <ElButton type="info">
                配置预设
                <SvgIcon icon="carbon:chevron-down" class="ml-4px" />
              </ElButton>
              <template #dropdown>
                <ElDropdownMenu>
                  <ElDropdownItem command="development">开发环境</ElDropdownItem>
                  <ElDropdownItem command="production">生产环境</ElDropdownItem>
                  <ElDropdownItem command="security">高安全</ElDropdownItem>
                </ElDropdownMenu>
              </template>
            </ElDropdown>

            <ElButton type="primary" :icon="Refresh" :loading="loading" @click="loadConfig">刷新配置</ElButton>
            <ElButton type="warning" :icon="Setting" :loading="loading" @click="resetConfig">重置配置</ElButton>
            <ElButton type="success" :icon="Check" :loading="saving" @click="saveConfig">保存配置</ElButton>
          </div>
        </div>
      </template>

      <!-- 配置表单 -->
      <div v-if="configForm && !loading" class="config-container">
        <ElTabs v-model="activeTab" type="border-card" class="config-tabs">
          <!-- 基础设置 -->
          <ElTabPane label="基础设置" name="basic">
            <div class="tab-content">
              <ElAlert
                title="基础设置"
                description="配置系统的基本信息和显示设置"
                type="info"
                :closable="false"
                class="mb-16px"
              />

              <ElForm
                :model="configForm"
                :label-width="isMobile ? '80px' : '120px'"
                :label-position="isMobile ? 'top' : 'right'"
                class="config-form"
              >
                <ElFormItem label="系统名称" required>
                  <ElInput
                    v-model="configForm.systemName"
                    placeholder="请输入系统名称"
                    maxlength="50"
                    show-word-limit
                  />
                  <div class="form-tip">系统显示名称，将在页面标题和登录界面显示</div>
                </ElFormItem>

                <ElFormItem label="系统描述">
                  <ElInput
                    v-model="configForm.systemDescription"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入系统描述"
                    maxlength="200"
                    show-word-limit
                  />
                  <div class="form-tip">系统的详细描述信息</div>
                </ElFormItem>

                <ElFormItem label="系统版本">
                  <ElInput v-model="configForm.systemVersion" placeholder="请输入系统版本，如：v1.0.0" maxlength="20" />
                  <div class="form-tip">当前系统版本号</div>
                </ElFormItem>

                <ElFormItem label="维护模式">
                  <ElSwitch v-model="configForm.maintenanceMode" active-text="开启" inactive-text="关闭" />
                  <div class="form-tip">开启后，普通用户将无法访问系统，只有管理员可以登录</div>
                </ElFormItem>
              </ElForm>
            </div>
          </ElTabPane>

          <!-- 安全设置 -->
          <ElTabPane label="安全设置" name="security">
            <div class="tab-content">
              <ElAlert
                title="安全设置"
                description="配置系统的安全策略和认证设置"
                type="warning"
                :closable="false"
                class="mb-16px"
              />

              <ElForm :model="configForm" label-width="120px" class="config-form">
                <ElFormItem label="Token过期时间" required>
                  <ElInputNumber
                    v-model="configForm.tokenExpiry"
                    :min="1"
                    :max="168"
                    controls-position="right"
                    class="w-full"
                  />
                  <span class="ml-8px text-gray-500">小时</span>
                  <div class="form-tip">用户登录Token的有效期，建议设置为24小时</div>
                </ElFormItem>

                <ElFormItem label="密码最小长度" required>
                  <ElInputNumber
                    v-model="configForm.passwordMinLength"
                    :min="6"
                    :max="32"
                    controls-position="right"
                    class="w-full"
                  />
                  <span class="ml-8px text-gray-500">位</span>
                  <div class="form-tip">用户密码的最小长度要求</div>
                </ElFormItem>

                <ElFormItem label="双因子认证">
                  <ElSwitch v-model="configForm.enableTwoFactor" active-text="开启" inactive-text="关闭" />
                  <div class="form-tip">开启后用户登录需要额外的验证步骤</div>
                </ElFormItem>

                <ElFormItem label="最大登录尝试">
                  <ElInputNumber
                    v-model="configForm.maxLoginAttempts"
                    :min="3"
                    :max="10"
                    controls-position="right"
                    class="w-full"
                  />
                  <span class="ml-8px text-gray-500">次</span>
                  <div class="form-tip">用户连续登录失败的最大次数，超过后将锁定账户</div>
                </ElFormItem>

                <ElFormItem label="会话超时时间">
                  <ElInputNumber
                    v-model="configForm.sessionTimeout"
                    :min="5"
                    :max="120"
                    controls-position="right"
                    class="w-full"
                  />
                  <span class="ml-8px text-gray-500">分钟</span>
                  <div class="form-tip">用户无操作时的会话超时时间</div>
                </ElFormItem>
              </ElForm>
            </div>
          </ElTabPane>

          <!-- 邮件设置 -->
          <ElTabPane label="邮件设置" name="email">
            <div class="tab-content">
              <ElAlert
                title="邮件设置"
                description="配置SMTP服务器用于发送系统邮件"
                type="success"
                :closable="false"
                class="mb-16px"
              />

              <ElForm :model="configForm" label-width="120px" class="config-form">
                <ElFormItem label="SMTP服务器">
                  <ElInput v-model="configForm.smtpHost" placeholder="请输入SMTP服务器地址" />
                  <div class="form-tip">邮件服务器的SMTP地址，如：smtp.qq.com</div>
                </ElFormItem>

                <ElFormItem label="SMTP端口">
                  <ElInputNumber
                    v-model="configForm.smtpPort"
                    :min="1"
                    :max="65535"
                    controls-position="right"
                    class="w-full"
                  />
                  <div class="form-tip">SMTP服务器端口，通常为587（TLS）或465（SSL）</div>
                </ElFormItem>

                <ElFormItem label="发件人邮箱">
                  <ElInput v-model="configForm.emailFrom" placeholder="请输入发件人邮箱地址" type="email" />
                  <div class="form-tip">系统发送邮件时使用的发件人邮箱</div>
                </ElFormItem>

                <ElFormItem label="邮箱密码">
                  <ElInput
                    v-model="configForm.emailPassword"
                    placeholder="请输入邮箱密码或授权码"
                    type="password"
                    show-password
                  />
                  <div class="form-tip">邮箱密码或第三方客户端授权码</div>
                </ElFormItem>

                <ElFormItem label="启用SSL">
                  <ElSwitch v-model="configForm.emailSSL" active-text="开启" inactive-text="关闭" />
                  <div class="form-tip">是否使用SSL/TLS加密连接</div>
                </ElFormItem>

                <ElDivider content-position="left">邮件测试</ElDivider>

                <ElFormItem label="测试邮箱">
                  <div class="flex gap-8px">
                    <ElInput v-model="testEmail" placeholder="请输入测试邮箱地址" type="email" class="flex-1" />
                    <ElButton type="primary" :loading="testing" @click="handleTestEmail">发送测试邮件</ElButton>
                  </div>
                  <div class="form-tip">发送测试邮件验证SMTP配置是否正确</div>
                </ElFormItem>
              </ElForm>
            </div>
          </ElTabPane>

          <!-- 功能开关 -->
          <ElTabPane label="功能开关" name="features">
            <div class="tab-content">
              <ElAlert
                title="功能开关"
                description="控制系统各项功能的启用状态"
                type="warning"
                :closable="false"
                class="mb-16px"
              />

              <ElForm :model="configForm" label-width="120px" class="config-form">
                <ElFormItem label="用户注册">
                  <ElSwitch v-model="configForm.enableRegistration" active-text="开启" inactive-text="关闭" />
                  <div class="form-tip">是否允许新用户注册账户</div>
                </ElFormItem>

                <ElFormItem label="访客访问">
                  <ElSwitch v-model="configForm.enableGuestAccess" active-text="开启" inactive-text="关闭" />
                  <div class="form-tip">是否允许未登录用户访问部分功能</div>
                </ElFormItem>

                <ElFormItem label="API文档">
                  <ElSwitch v-model="configForm.enableApiDocs" active-text="开启" inactive-text="关闭" />
                  <div class="form-tip">是否启用Swagger API文档访问</div>
                </ElFormItem>

                <ElFormItem label="最大上传大小">
                  <ElInputNumber
                    v-model="configForm.maxUploadSize"
                    :min="1"
                    :max="100"
                    controls-position="right"
                    class="w-full"
                  />
                  <span class="ml-8px text-gray-500">MB</span>
                  <div class="form-tip">单个文件的最大上传大小限制</div>
                </ElFormItem>
              </ElForm>
            </div>
          </ElTabPane>
        </ElTabs>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="flex items-center justify-center py-100px">
        <ElIcon class="is-loading mr-8px">
          <Loading />
        </ElIcon>
        <span>正在加载配置...</span>
      </div>
    </ElCard>
  </div>
</template>

<style scoped>
/* 使用项目标准的flex布局 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.gap-16px {
  gap: 16px;
}

/* 配置容器样式 */
.config-container {
  width: 100%;
}

.config-tabs {
  width: 100%;
}

.tab-content {
  padding: 16px 0;
}

.config-form {
  max-width: 600px;
}

.form-tip {
  font-size: 12px;
  color: #6b7280;
  margin-top: 4px;
  line-height: 1.4;
}

/* 输入框样式 */
.w-full {
  width: 100%;
}

.flex {
  display: flex;
}

.flex-1 {
  flex: 1;
}

.gap-8px {
  gap: 8px;
}

.ml-8px {
  margin-left: 8px;
}

.mr-8px {
  margin-right: 8px;
}

.mb-16px {
  margin-bottom: 16px;
}

.py-100px {
  padding-top: 100px;
  padding-bottom: 100px;
}

.ml-4px {
  margin-left: 4px;
}

.text-gray-500 {
  color: #6b7280;
}

/* 标签页样式优化 */
.config-tabs :deep(.el-tabs__content) {
  padding: 0;
}

.config-tabs :deep(.el-tab-pane) {
  padding: 0;
}

/* 表单项间距 */
.config-form .el-form-item {
  margin-bottom: 24px;
}

.config-form .el-form-item:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .flex.flex-col.gap-16px.p-16px {
    padding: 12px;
    gap: 12px;
  }

  .config-form {
    max-width: 100%;
  }

  .tab-content {
    padding: 8px 0;
  }
}
</style>
