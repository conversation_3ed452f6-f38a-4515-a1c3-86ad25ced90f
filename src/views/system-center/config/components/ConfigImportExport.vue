<script setup lang="ts">
import { ref } from 'vue';
import { <PERSON><PERSON><PERSON>on, El<PERSON>ard, ElDivider, ElMessage, ElMessageBox, ElScrollbar, ElUpload } from 'element-plus';
import type { UploadFile } from 'element-plus';
import { Download, FolderAdd, Refresh, Upload } from '@element-plus/icons-vue';

// Props
interface Props {
  config: Api.Config.SystemConfig;
}

const props = defineProps<Props>();

// Emits
interface Emits {
  (e: 'import', config: Api.Config.SystemConfig): void;
  (e: 'reset'): void;
}

const emit = defineEmits<Emits>();

// 响应式数据
const exporting = ref(false);
const importing = ref(false);
const backing = ref(false);
const uploadRef = ref();
const previewConfig = ref<Api.Config.SystemConfig | null>(null);

// 导出配置
async function exportConfig() {
  exporting.value = true;
  try {
    const configData = JSON.stringify(props.config, null, 2);
    const blob = new Blob([configData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = `system-config-${new Date().toISOString().slice(0, 10)}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    ElMessage.success('配置导出成功');
  } catch (error) {
    console.error('导出配置失败:', error);
    ElMessage.error('导出配置失败');
  } finally {
    exporting.value = false;
  }
}

// 文件上传前检查
function beforeUpload(file: File) {
  const isJSON = file.type === 'application/json' || file.name.endsWith('.json');
  if (!isJSON) {
    ElMessage.error('只能上传 JSON 格式的配置文件');
    return false;
  }

  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    ElMessage.error('配置文件大小不能超过 2MB');
    return false;
  }

  return false; // 阻止自动上传
}

// 处理文件变化
function handleFileChange(file: UploadFile) {
  if (!file.raw) return;

  importing.value = true;
  const reader = new FileReader();

  reader.onload = e => {
    try {
      const result = e.target?.result as string;
      const config = JSON.parse(result);

      // 验证配置格式
      if (!config.systemName || !config.systemVersion) {
        throw new Error('配置文件格式不正确');
      }

      previewConfig.value = config;
      ElMessage.success('配置文件解析成功，请预览后确认导入');
    } catch (error) {
      console.error('解析配置文件失败:', error);
      ElMessage.error('配置文件格式错误');
    } finally {
      importing.value = false;
    }
  };

  reader.onerror = () => {
    ElMessage.error('读取配置文件失败');
    importing.value = false;
  };

  reader.readAsText(file.raw);
}

// 确认导入
function confirmImport() {
  if (previewConfig.value) {
    emit('import', previewConfig.value);
    previewConfig.value = null;
    uploadRef.value?.clearFiles();
  }
}

// 取消导入
function cancelImport() {
  previewConfig.value = null;
  uploadRef.value?.clearFiles();
}

// 备份配置
async function backupConfig() {
  backing.value = true;
  try {
    // 创建备份文件名
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = `backup-${timestamp}.json`;

    // 导出为备份文件
    const configData = JSON.stringify(props.config, null, 2);
    const blob = new Blob([configData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = backupName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    ElMessage.success('配置备份创建成功');
  } catch (error) {
    console.error('创建备份失败:', error);
    ElMessage.error('创建备份失败');
  } finally {
    backing.value = false;
  }
}

// 确认重置
async function confirmReset() {
  try {
    await ElMessageBox.confirm('此操作将恢复所有配置为默认值，确定要继续吗？', '确认重置', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });

    emit('reset');
  } catch {
    // 用户取消操作
  }
}
</script>

<template>
  <ElCard class="mb-16px">
    <template #header>
      <div class="flex items-center gap-8px">
        <SvgIcon icon="carbon:data-backup" class="text-18px text-primary" />
        <span class="text-16px font-medium">配置管理</span>
      </div>
    </template>

    <div class="flex flex-wrap gap-12px">
      <!-- 导出配置 -->
      <ElButton type="primary" :icon="Download" :loading="exporting" @click="exportConfig">导出配置</ElButton>

      <!-- 导入配置 -->
      <ElUpload
        ref="uploadRef"
        :show-file-list="false"
        :before-upload="beforeUpload"
        :on-change="handleFileChange"
        accept=".json"
        :auto-upload="false"
      >
        <ElButton type="success" :icon="Upload" :loading="importing">导入配置</ElButton>
      </ElUpload>

      <!-- 备份配置 -->
      <ElButton type="warning" :icon="FolderAdd" :loading="backing" @click="backupConfig">创建备份</ElButton>

      <!-- 恢复默认 -->
      <ElButton type="danger" :icon="Refresh" @click="confirmReset">恢复默认</ElButton>
    </div>

    <!-- 配置预览 -->
    <div v-if="previewConfig" class="mt-16px">
      <ElDivider content-position="left">配置预览</ElDivider>
      <ElScrollbar height="200px">
        <pre class="overflow-auto rounded-4px bg-gray-50 p-12px text-12px">{{
          JSON.stringify(previewConfig, null, 2)
        }}</pre>
      </ElScrollbar>
      <div class="mt-12px flex gap-8px">
        <ElButton type="primary" size="small" @click="confirmImport">确认导入</ElButton>
        <ElButton size="small" @click="cancelImport">取消</ElButton>
      </div>
    </div>
  </ElCard>
</template>

<style scoped>
.flex {
  display: flex;
}

.flex-wrap {
  flex-wrap: wrap;
}

.gap-12px {
  gap: 12px;
}

.gap-8px {
  gap: 8px;
}

.mt-16px {
  margin-top: 16px;
}

.mt-12px {
  margin-top: 12px;
}

.text-12px {
  font-size: 12px;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.p-12px {
  padding: 12px;
}

.rounded-4px {
  border-radius: 4px;
}

.overflow-auto {
  overflow: auto;
}
</style>
