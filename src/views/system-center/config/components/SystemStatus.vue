<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { ElButton, ElCard, ElIcon, ElMessage, ElTag } from 'element-plus';
import { Loading, Refresh } from '@element-plus/icons-vue';
import { fetchSystemStatus } from '@/service/api/config';

// 响应式数据
const loading = ref(false);
const status = ref<Api.Config.SystemStatus | null>(null);

// 加载系统状态
async function loadStatus() {
  loading.value = true;
  try {
    const data = await fetchSystemStatus();
    status.value = data;
  } catch (error) {
    console.error('获取系统状态失败:', error);
    ElMessage.error('获取系统状态失败');
  } finally {
    loading.value = false;
  }
}

// 格式化运行时间
function formatUptime(seconds: number): string {
  const days = Math.floor(seconds / 86400);
  const hours = Math.floor((seconds % 86400) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);

  if (days > 0) {
    return `${days}天 ${hours}小时 ${minutes}分钟`;
  } else if (hours > 0) {
    return `${hours}小时 ${minutes}分钟`;
  }
  return `${minutes}分钟`;
}

// 格式化字节数
function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 B';

  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
}

// 格式化日期
function formatDate(timestamp: string): string {
  return new Date(timestamp).toLocaleString('zh-CN');
}

// 组件挂载时加载状态
onMounted(() => {
  loadStatus();
});
</script>

<template>
  <ElCard class="mb-16px">
    <template #header>
      <div class="flex items-center gap-8px">
        <SvgIcon icon="carbon:dashboard" class="text-18px text-primary" />
        <span class="text-16px font-medium">系统状态</span>
        <ElButton type="primary" size="small" :icon="Refresh" :loading="loading" @click="loadStatus">刷新</ElButton>
      </div>
    </template>

    <div v-if="loading" class="flex items-center justify-center py-40px">
      <ElIcon class="is-loading mr-8px">
        <Loading />
      </ElIcon>
      <span>正在获取系统状态...</span>
    </div>

    <div v-else-if="status" class="grid grid-cols-1 gap-16px lg:grid-cols-4 md:grid-cols-2">
      <!-- 系统信息 -->
      <div class="rounded-8px bg-blue-50 p-16px">
        <div class="mb-8px flex items-center gap-8px">
          <SvgIcon icon="carbon:information" class="text-16px text-blue-600" />
          <span class="text-14px text-blue-800 font-medium">系统信息</span>
        </div>
        <div class="text-12px text-gray-600 space-y-4px">
          <div>名称: {{ status.systemName }}</div>
          <div>版本: {{ status.systemVersion }}</div>
          <div>平台: {{ status.platform }}</div>
          <div>架构: {{ status.arch }}</div>
        </div>
      </div>

      <!-- 运行时间 -->
      <div class="rounded-8px bg-green-50 p-16px">
        <div class="mb-8px flex items-center gap-8px">
          <SvgIcon icon="carbon:time" class="text-16px text-green-600" />
          <span class="text-14px text-green-800 font-medium">运行时间</span>
        </div>
        <div class="text-12px text-gray-600 space-y-4px">
          <div>运行时长: {{ formatUptime(status.uptime) }}</div>
          <div>Node版本: {{ status.nodeVersion }}</div>
          <div>启动时间: {{ formatDate(status.timestamp) }}</div>
        </div>
      </div>

      <!-- 内存使用 -->
      <div class="rounded-8px bg-orange-50 p-16px">
        <div class="mb-8px flex items-center gap-8px">
          <SvgIcon icon="carbon:chip" class="text-16px text-orange-600" />
          <span class="text-14px text-orange-800 font-medium">内存使用</span>
        </div>
        <div class="text-12px text-gray-600 space-y-4px">
          <div>
            堆内存: {{ formatBytes(status.memoryUsage.heapUsed) }} / {{ formatBytes(status.memoryUsage.heapTotal) }}
          </div>
          <div>RSS: {{ formatBytes(status.memoryUsage.rss) }}</div>
          <div>外部: {{ formatBytes(status.memoryUsage.external) }}</div>
        </div>
      </div>

      <!-- 系统状态 -->
      <div class="rounded-8px bg-purple-50 p-16px">
        <div class="mb-8px flex items-center gap-8px">
          <SvgIcon icon="carbon:status" class="text-16px text-purple-600" />
          <span class="text-14px text-purple-800 font-medium">系统状态</span>
        </div>
        <div class="text-12px text-gray-600 space-y-4px">
          <div class="flex items-center gap-4px">
            <span>维护模式:</span>
            <ElTag :type="status.maintenanceMode ? 'warning' : 'success'" size="small">
              {{ status.maintenanceMode ? '开启' : '关闭' }}
            </ElTag>
          </div>
          <div>CPU用户时间: {{ Math.round(status.cpuUsage.user / 1000) }}ms</div>
          <div>CPU系统时间: {{ Math.round(status.cpuUsage.system / 1000) }}ms</div>
        </div>
      </div>
    </div>

    <div v-else class="py-40px text-center text-gray-500">暂无系统状态数据</div>
  </ElCard>
</template>

<style scoped>
.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

@media (min-width: 768px) {
  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
</style>
