<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';
import { request } from '@/service/request';
import { useTableOperate } from '@/hooks/common/table-pagination';

import FilterDrawer from '@/components/responsive/FilterDrawer.vue';
defineOptions({
  name: 'PermissionManagement'
});

// 响应式数据
const modalVisible = ref(false);
const isEdit = ref(false);
const saving = ref(false);

// 表单引用
const formRef = ref<FormInstance | null>(null);

// 搜索参数
const searchParams = reactive({
  keyword: '',
  permission_group: '',
  permission_type: 'button',
  status: undefined as number | undefined
});

// 小屏筛选抽屉
const filterDrawerVisible = ref(false);

// 权限表单
const permissionForm = reactive({
  permission_id: null as number | null,
  permission_code: '',
  permission_name: '',
  permission_description: '',
  permission_type: 'button',
  permission_group: 'system',

  is_system: 0,
  status: 1,
  sort_order: 0
});

// 表单验证规则
const formRules: FormRules = {
  permission_code: [
    { required: true, message: '请输入权限代码', trigger: 'blur' },
    { min: 2, max: 100, message: '权限代码长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  permission_name: [
    { required: true, message: '请输入权限名称', trigger: 'blur' },
    { min: 2, max: 100, message: '权限名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  permission_type: [{ required: true, message: '请选择权限类型', trigger: 'change' }],
  permission_group: [{ required: true, message: '请选择权限分组', trigger: 'change' }]
};

// 权限类型选项
const permissionTypes = [
  { label: '菜单权限', value: 'menu' },
  { label: '按钮权限', value: 'button' },
  { label: 'API权限', value: 'api' },
  { label: '数据权限', value: 'data' }
];

// 权限分组选项
const permissionGroups = [
  { label: '用户管理', value: 'user' },
  { label: '角色管理', value: 'role' },
  { label: '权限管理', value: 'permission' },
  { label: '系统管理', value: 'system' },
  { label: '订单管理', value: 'order' },
  { label: '课程管理', value: 'course' },
  { label: '供应商管理', value: 'supplier' },
  { label: '财务管理', value: 'finance' },
  { label: '报表统计', value: 'report' },
  { label: '配置管理', value: 'config' }
];

// 表格数据获取
const { pagination, loading, tableData, handlePageChange, handleSizeChange, fetchTableData, refreshTable } =
  useTableOperate({
    fetchData: async params => {
      const response = await request({
        url: '/api/permission/list',
        method: 'get',
        params: {
          ...params,
          ...searchParams
        }
      });

      // request函数已经解包了响应，直接访问数据
      return {
        data: (response as any).list || [],
        total: (response as any).total || 0
      };
      return { data: [], total: 0 };
    }
  });

// 计算属性
const groupedTableData = computed(() => {
  const groups: Record<string, any[]> = {};

  tableData.value.forEach(permission => {
    const groupName = getGroupDisplayName(permission.permission_group);
    if (!groups[groupName]) {
      groups[groupName] = [];
    }
    groups[groupName].push(permission);
  });

  return Object.entries(groups).map(([name, permissions]) => ({
    name,
    permissions: permissions.sort((a, b) => a.sort_order - b.sort_order)
  }));
});

// API调用函数
async function savePermission() {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    saving.value = true;

    const apiUrl = isEdit.value ? `/api/permission/${permissionForm.permission_id}` : '/api/permission';
    const method = isEdit.value ? 'put' : 'post';

    const response = await request({
      url: apiUrl,
      method,
      data: {
        permission_code: permissionForm.permission_code,
        permission_name: permissionForm.permission_name,
        permission_description: permissionForm.permission_description,
        permission_type: permissionForm.permission_type,
        permission_group: permissionForm.permission_group,
        is_system: permissionForm.is_system,
        status: permissionForm.status,
        sort_order: permissionForm.sort_order
      }
    });

    // 检查操作是否成功
    const isSuccess = response === true || (response as any)?.permission_id || (response as any)?.success;
    if (isSuccess) {
      ElMessage.success(isEdit.value ? '权限更新成功' : '权限创建成功');
      modalVisible.value = false;
      fetchTableData();
    } else {
      ElMessage.error('操作失败');
    }
  } catch (error: any) {
    console.error('保存权限失败:', error);
    ElMessage.error(error.message || '保存权限失败');
  } finally {
    saving.value = false;
  }
}

async function deletePermission(permissionId: number) {
  try {
    const response = await request({
      url: `/api/permission/${permissionId}`,
      method: 'delete'
    });
    // 检查删除是否成功
    const isSuccess = response === true || (response as any)?.success;
    if (isSuccess) {
      ElMessage.success('权限删除成功');
      fetchTableData();
    } else {
      ElMessage.error('删除失败');
    }
  } catch (error: any) {
    console.error('删除权限失败:', error);
    ElMessage.error(error.message || '删除权限失败');
  }
}

// 处理函数
function handleSearch() {
  fetchTableData();
}

function handleReset() {
  Object.assign(searchParams, {
    keyword: '',
    permission_group: '',
    permission_type: 'button',
    status: undefined
  });
  fetchTableData();
}

function handleCreate() {
  isEdit.value = false;
  Object.assign(permissionForm, {
    permission_id: null,
    permission_code: '',
    permission_name: '',
    permission_description: '',
    permission_type: 'button',
    permission_group: 'system',
    is_system: 0,
    status: 1,
    sort_order: 0
  });
  modalVisible.value = true;
}

function handleEdit(row: any) {
  isEdit.value = true;
  Object.assign(permissionForm, {
    permission_id: row.permission_id,
    permission_code: row.permission_code,
    permission_name: row.permission_name,
    permission_description: row.permission_description || '',
    permission_type: row.permission_type,
    permission_group: row.permission_group,
    resource_path: row.resource_path || '',
    resource_method: row.resource_method || '',
    is_system: row.is_system,
    status: row.status,
    sort_order: row.sort_order
  });
  modalVisible.value = true;
}

function handleDelete(permissionId: number) {
  ElMessageBox.confirm('确定要删除这个权限吗？', '确认删除', {
    type: 'warning'
  }).then(() => {
    deletePermission(permissionId);
  });
}

// 获取权限类型标签
function getPermissionType(type: string): 'primary' | 'success' | 'warning' | 'info' {
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'info'> = {
    menu: 'primary',
    button: 'success',
    api: 'warning',
    data: 'info'
  };
  return typeMap[type] || 'info';
}

function getPermissionTypeText(type: string) {
  const typeMap: Record<string, string> = {
    menu: '菜单',
    button: '按钮',
    api: 'API',
    data: '数据'
  };
  return typeMap[type] || type;
}

// 获取权限分组显示名称
function getGroupDisplayName(group: string) {
  const groupMap: Record<string, string> = {
    user: '用户管理',
    role: '角色管理',
    permission: '权限管理',
    system: '系统管理',
    order: '订单管理',
    course: '课程管理',
    supplier: '供应商管理',
    finance: '财务管理',
    report: '报表统计',
    config: '配置管理'
  };
  return groupMap[group] || group;
}

// 生命周期
onMounted(() => {
  fetchTableData();
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-medium">权限管理</span>
          <ElButton v-permission="'permission:create'" type="primary" @click="handleCreate">新增权限</ElButton>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="mb-16px">
        <ElForm :model="searchParams" inline>
          <ElFormItem label="关键词">
            <ElInput
              v-model="searchParams.keyword"
              placeholder="权限名称/代码"
              clearable
              style="width: 200px"
              @keyup.enter="handleSearch"
            />
          </ElFormItem>
          <ElFormItem label="分组" class="hidden md:inline-flex">
            <ElSelect v-model="searchParams.permission_group" placeholder="请选择分组" clearable style="width: 120px">
              <ElOption
                v-for="group in permissionGroups"
                :key="group.value"
                :label="group.label"
                :value="group.value"
              />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="类型" class="hidden md:inline-flex">
            <ElSelect v-model="searchParams.permission_type" placeholder="请选择类型" clearable style="width: 120px">
              <ElOption label="菜单" value="menu" />
              <ElOption label="按钮" value="button" />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="状态" class="hidden md:inline-flex">
            <ElSelect v-model="searchParams.status" placeholder="请选择状态" clearable style="width: 120px">
              <ElOption label="启用" :value="1" />
              <ElOption label="禁用" :value="0" />
            </ElSelect>
          </ElFormItem>
          <ElFormItem class="hidden md:inline-flex">
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>

          <!-- 小屏筛选抽屉入口 -->
          <ElFormItem class="md:hidden">
            <ElButton @click="filterDrawerVisible = true">筛选</ElButton>
          </ElFormItem>
        </ElForm>
      </div>

      <!-- 分组显示的权限表格（仅列表） -->
      <div v-loading="loading" class="space-y-16px">
        <div v-if="groupedTableData.length === 0 && !loading" class="py-32px text-center text-gray-500">暂无数据</div>
        <div v-for="group in groupedTableData" :key="group.name" class="border border-gray-200 rounded-8px">
          <div class="border-b border-gray-200 bg-gray-50 px-16px py-12px">
            <div class="flex items-center justify-between">
              <span class="text-14px text-gray-700 font-medium">{{ group.name }} ({{ group.permissions.length }})</span>
            </div>
          </div>

          <ElTable :data="group.permissions" style="width: 100%">
            <ElTableColumn prop="permission_code" label="权限代码" width="200" />
            <ElTableColumn prop="permission_name" label="权限名称" width="150" />
            <ElTableColumn prop="permission_description" label="描述" show-overflow-tooltip />
            <ElTableColumn prop="permission_type" label="类型" width="80">
              <template #default="{ row }">
                <ElTag size="small" :type="getPermissionType(row.permission_type)">
                  {{ getPermissionTypeText(row.permission_type) }}
                </ElTag>
              </template>
            </ElTableColumn>
            <!-- 移除资源路径列：精简为仅菜单/按钮权限 -->
            <ElTableColumn prop="status" label="状态" width="80">
              <template #default="{ row }">
                <ElTag :type="row.status === 1 ? 'success' : 'info'">
                  {{ row.status === 1 ? '启用' : '禁用' }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn label="操作" width="160" fixed="right">
              <template #default="{ row }">
                <div class="flex gap-4px">
                  <ElButton v-permission="'permission:edit'" size="small" type="primary" @click="handleEdit(row)">编辑</ElButton>
                  <ElButton v-permission="'permission:delete'" v-if="!row.is_system" size="small" type="danger" @click="handleDelete(row.permission_id)">
                    删除
                  </ElButton>
                </div>
              </template>
            </ElTableColumn>
          </ElTable>
        </div>

        <!-- 分页 -->
        <div class="mt-16px flex justify-center md:justify-end">
          <ElPagination
            v-model:current-page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :total="pagination.total"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </ElCard>

    <!-- 权限编辑模态框 -->
    <ElDialog
      v-model="modalVisible"
      :title="isEdit ? '编辑权限' : '新增权限'"
      width="600px"
      :close-on-click-modal="false"
    >
      <ElForm ref="formRef" :model="permissionForm" :rules="formRules" label-width="100px">
        <ElRow :gutter="16">
          <ElCol :span="12">
            <ElFormItem label="权限代码" prop="permission_code">
              <ElInput v-model="permissionForm.permission_code" placeholder="如: user:create" :disabled="isEdit" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="权限名称" prop="permission_name">
              <ElInput v-model="permissionForm.permission_name" placeholder="如: 创建用户" />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElFormItem label="权限描述">
          <ElInput
            v-model="permissionForm.permission_description"
            type="textarea"
            :rows="2"
            placeholder="请输入权限描述"
          />
        </ElFormItem>

        <ElRow :gutter="16">
          <ElCol :span="8">
            <ElFormItem label="权限类型" prop="permission_type">
              <ElSelect v-model="permissionForm.permission_type" style="width: 100%">
                <ElOption label="菜单" value="menu" />
                <ElOption label="按钮" value="button" />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="权限分组" prop="permission_group">
              <ElSelect v-model="permissionForm.permission_group" style="width: 100%">
                <ElOption
                  v-for="group in permissionGroups"
                  :key="group.value"
                  :label="group.label"
                  :value="group.value"
                />
              </ElSelect>
            </ElFormItem>
          </ElCol>
          <ElCol :span="8">
            <ElFormItem label="状态">
              <ElSelect v-model="permissionForm.status" style="width: 100%">
                <ElOption label="启用" :value="1" />
                <ElOption label="禁用" :value="0" />
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>



        <ElFormItem label="排序">
          <ElInputNumber v-model="permissionForm.sort_order" :min="0" style="width: 200px" />
        </ElFormItem>

        <ElFormItem label="系统权限">
          <ElSwitch v-model="permissionForm.is_system" :active-value="1" :inactive-value="0" />
          <div class="mt-4px text-12px text-gray-500">系统权限不可删除，仅可修改状态</div>
        </ElFormItem>
      </ElForm>

      <template #footer>
        <div class="flex justify-end gap-8px">
          <ElButton @click="modalVisible = false">取消</ElButton>
          <ElButton type="primary" :loading="saving" @click="savePermission">
            {{ isEdit ? '更新' : '创建' }}
          </ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<style scoped>
/* 移动端适配 */
@media (max-width: 768px) {
  .flex.flex-col.gap-16px.p-16px {
    padding: 12px;
    gap: 12px;
  }
}
</style>
