<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Refresh } from '@element-plus/icons-vue';
import {
  ORDER_STATUS_OPTIONS,
  REPORT_CONFIG,
  fetchDeleteReport,
  fetchDownloadReport,
  fetchGenerateFinanceReport,
  fetchGenerateOrderReport,
  fetchGenerateUserReport,
  fetchReportList,
  reportUtils
} from '@/service/api/report';

defineOptions({
  name: 'ReportManagement'
});

// 响应式数据
const loading = ref(false);
const generating = ref(false);
const activeTab = ref('user');

// 表单数据
const userForm = reactive({
  dateRange: REPORT_CONFIG.defaultDateRange() as any,
  format: 'excel' as 'excel' | 'csv'
});

const orderForm = reactive({
  dateRange: REPORT_CONFIG.defaultDateRange() as any,
  status: '' as string | undefined,
  format: 'excel' as 'excel' | 'csv'
});

const financeForm = reactive({
  dateRange: REPORT_CONFIG.defaultDateRange() as any,
  groupBy: 'day' as 'day' | 'week' | 'month',
  format: 'excel' as 'excel' | 'csv'
});

// 报表列表
const reportList = ref([]);

// 生成用户报表
async function generateUserReport() {
  try {
    if (!userForm.dateRange || userForm.dateRange.length !== 2) {
      ElMessage.warning('请选择日期范围');
      return;
    }

    const [startDate, endDate] = userForm.dateRange;

    if (!reportUtils.validateDateRange(startDate.toISOString(), endDate.toISOString())) {
      ElMessage.warning('日期范围无效');
      return;
    }

    generating.value = true;

    const response = await fetchGenerateUserReport({
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
      format: userForm.format
    });

    ElMessage.success('用户报表生成成功');
    loadReportList();
  } catch (error: any) {
    console.error('生成用户报表失败:', error);
    ElMessage.error(error.message || '生成用户报表失败');
  } finally {
    generating.value = false;
  }
}

// 生成订单报表
async function generateOrderReport() {
  try {
    if (!orderForm.dateRange || orderForm.dateRange.length !== 2) {
      ElMessage.warning('请选择日期范围');
      return;
    }

    const [startDate, endDate] = orderForm.dateRange;

    if (!reportUtils.validateDateRange(startDate.toISOString(), endDate.toISOString())) {
      ElMessage.warning('日期范围无效');
      return;
    }

    generating.value = true;

    const response = await fetchGenerateOrderReport({
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
      status: orderForm.status ? Number.parseInt(orderForm.status) : undefined,
      format: orderForm.format
    });

    ElMessage.success('订单报表生成成功');
    loadReportList();
  } catch (error: any) {
    console.error('生成订单报表失败:', error);
    ElMessage.error(error.message || '生成订单报表失败');
  } finally {
    generating.value = false;
  }
}

// 生成财务报表
async function generateFinanceReport() {
  try {
    if (!financeForm.dateRange || financeForm.dateRange.length !== 2) {
      ElMessage.warning('请选择日期范围');
      return;
    }

    const [startDate, endDate] = financeForm.dateRange;

    if (!reportUtils.validateDateRange(startDate.toISOString(), endDate.toISOString())) {
      ElMessage.warning('日期范围无效');
      return;
    }

    generating.value = true;

    const response = await fetchGenerateFinanceReport({
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
      groupBy: financeForm.groupBy,
      format: financeForm.format
    });

    ElMessage.success('财务报表生成成功');
    loadReportList();
  } catch (error: any) {
    console.error('生成财务报表失败:', error);
    ElMessage.error(error.message || '生成财务报表失败');
  } finally {
    generating.value = false;
  }
}

// 加载报表列表
async function loadReportList() {
  try {
    loading.value = true;
    const response = await fetchReportList();
    reportList.value = response.data;
  } catch (error) {
    console.error('获取报表列表失败:', error);
    ElMessage.error('获取报表列表失败');
  } finally {
    loading.value = false;
  }
}

// 下载报表
async function downloadReport(row: any) {
  try {
    const blob = await fetchDownloadReport(row.fileName);
    reportUtils.downloadFile(blob, row.fileName);
    ElMessage.success('报表下载成功');
  } catch (error: any) {
    console.error('下载报表失败:', error);
    ElMessage.error(error.message || '下载报表失败');
  }
}

// 删除报表
async function deleteReport(row: any) {
  try {
    await ElMessageBox.confirm(`确定要删除报表文件 "${row.fileName}" 吗？`, '确认删除', {
      confirmButtonText: '确定删除',
      cancelButtonText: '取消',
      type: 'warning'
    });

    await fetchDeleteReport(row.fileName);
    ElMessage.success('报表删除成功');
    loadReportList();
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除报表失败:', error);
      ElMessage.error(error.message || '删除报表失败');
    }
  }
}

// 生命周期
onMounted(() => {
  loadReportList();
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <!-- 报表生成 -->
    <ElCard>
      <template #header>
        <div class="flex items-center gap-12px">
          <SvgIcon icon="carbon:report" class="text-24px text-primary" />
          <span class="text-18px font-semibold">报表生成</span>
        </div>
      </template>

      <ElTabs v-model="activeTab">
        <!-- 用户报表 -->
        <ElTabPane label="用户报表" name="user">
          <div class="report-form">
            <ElForm :model="userForm" label-width="100px">
              <ElRow :gutter="16">
                <ElCol :span="12">
                  <ElFormItem label="日期范围">
                    <ElDatePicker
                      v-model="userForm.dateRange"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      style="width: 100%"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="12">
                  <ElFormItem label="导出格式">
                    <ElSelect v-model="userForm.format" style="width: 100%">
                      <ElOption
                        v-for="format in REPORT_CONFIG.supportedFormats"
                        :key="format.value"
                        :label="format.label"
                        :value="format.value"
                      />
                    </ElSelect>
                  </ElFormItem>
                </ElCol>
              </ElRow>
              <ElFormItem>
                <ElButton type="primary" :loading="generating" @click="generateUserReport">生成用户报表</ElButton>
              </ElFormItem>
            </ElForm>
          </div>
        </ElTabPane>

        <!-- 订单报表 -->
        <ElTabPane label="订单报表" name="order">
          <div class="report-form">
            <ElForm :model="orderForm" label-width="100px">
              <ElRow :gutter="16">
                <ElCol :span="8">
                  <ElFormItem label="日期范围">
                    <ElDatePicker
                      v-model="orderForm.dateRange"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      style="width: 100%"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="订单状态">
                    <ElSelect v-model="orderForm.status" style="width: 100%">
                      <ElOption
                        v-for="status in ORDER_STATUS_OPTIONS"
                        :key="status.value"
                        :label="status.label"
                        :value="status.value"
                      />
                    </ElSelect>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="导出格式">
                    <ElSelect v-model="orderForm.format" style="width: 100%">
                      <ElOption
                        v-for="format in REPORT_CONFIG.supportedFormats"
                        :key="format.value"
                        :label="format.label"
                        :value="format.value"
                      />
                    </ElSelect>
                  </ElFormItem>
                </ElCol>
              </ElRow>
              <ElFormItem>
                <ElButton type="primary" :loading="generating" @click="generateOrderReport">生成订单报表</ElButton>
              </ElFormItem>
            </ElForm>
          </div>
        </ElTabPane>

        <!-- 财务报表 -->
        <ElTabPane label="财务报表" name="finance">
          <div class="report-form">
            <ElForm :model="financeForm" label-width="100px">
              <ElRow :gutter="16">
                <ElCol :span="8">
                  <ElFormItem label="日期范围">
                    <ElDatePicker
                      v-model="financeForm.dateRange"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      style="width: 100%"
                    />
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="统计方式">
                    <ElSelect v-model="financeForm.groupBy" style="width: 100%">
                      <ElOption
                        v-for="group in REPORT_CONFIG.financeGroupOptions"
                        :key="group.value"
                        :label="group.label"
                        :value="group.value"
                      />
                    </ElSelect>
                  </ElFormItem>
                </ElCol>
                <ElCol :span="8">
                  <ElFormItem label="导出格式">
                    <ElSelect v-model="financeForm.format" style="width: 100%">
                      <ElOption
                        v-for="format in REPORT_CONFIG.supportedFormats"
                        :key="format.value"
                        :label="format.label"
                        :value="format.value"
                      />
                    </ElSelect>
                  </ElFormItem>
                </ElCol>
              </ElRow>
              <ElFormItem>
                <ElButton type="primary" :loading="generating" @click="generateFinanceReport">生成财务报表</ElButton>
              </ElFormItem>
            </ElForm>
          </div>
        </ElTabPane>
      </ElTabs>
    </ElCard>

    <!-- 报表文件管理 -->
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-12px">
            <SvgIcon icon="carbon:document-multiple" class="text-24px text-primary" />
            <span class="text-18px font-semibold">报表文件管理</span>
          </div>
          <ElButton type="primary" :icon="Refresh" :loading="loading" @click="loadReportList">刷新</ElButton>
        </div>
      </template>

      <ElTable :data="reportList" :loading="loading" stripe border>
        <ElTableColumn prop="fileName" label="文件名" min-width="200" show-overflow-tooltip />
        <ElTableColumn prop="fileSizeMB" label="文件大小" width="100">
          <template #default="{ row }">{{ row.fileSizeMB }}MB</template>
        </ElTableColumn>
        <ElTableColumn prop="createTime" label="创建时间" width="160" />
        <ElTableColumn prop="modifyTime" label="修改时间" width="160" />
        <ElTableColumn label="操作" width="180" fixed="right">
          <template #default="{ row }">
            <div class="flex gap-8px">
              <ElButton size="small" type="primary" @click="downloadReport(row)">下载</ElButton>
              <ElButton size="small" type="danger" @click="deleteReport(row)">删除</ElButton>
            </div>
          </template>
        </ElTableColumn>
      </ElTable>

      <div v-if="reportList.length === 0 && !loading" class="py-40px text-center text-gray-500">暂无报表文件</div>
    </ElCard>
  </div>
</template>

<style scoped>
.flex-col {
  display: flex;
  flex-direction: column;
}

.gap-16px {
  gap: 16px;
}

.report-form {
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  margin-bottom: 16px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-8px {
  gap: 8px;
}

.gap-12px {
  gap: 12px;
}

.text-center {
  text-align: center;
}

.py-40px {
  padding-top: 40px;
  padding-bottom: 40px;
}

.text-gray-500 {
  color: #909399;
}
</style>
