<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { request } from '@/service/request';
import EmailTemplatePreview from './EmailTemplatePreview.vue';

const emit = defineEmits<{
  editTemplate: [template: any];
  deleteTemplate: [templateId: string];
}>();

// 响应式数据
const templates = ref<any[]>([]);
const searchText = ref('');
const selectedCategory = ref('');
const showPreviewModal = ref(false);
const showEditModal = ref(false);
const isEdit = ref(false);
const previewTemplate = ref(null);
const currentTemplate = ref<any>(null);
const formRef = ref<FormInstance | null>(null);
const saving = ref(false);
const variablesText = ref('');
const newVariableName = ref('');
const formVariables = ref<string[]>([]);

// 表单数据
const formData = reactive({
  template_key: '',
  template_name: '',
  template_description: '',
  category: 'custom',
  subject: '',
  text_content: '',
  html_content: '',
  is_active: true
});

// 表单验证规则
const formRules: FormRules = {
  template_key: [
    { required: true, message: '请输入模板标识', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '模板标识只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  template_name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  category: [{ required: true, message: '请选择模板分类', trigger: 'change' }],
  subject: [{ required: true, message: '请输入邮件主题', trigger: 'blur' }],
  text_content: [{ required: true, message: '请输入纯文本内容', trigger: 'blur' }],
  html_content: [{ required: true, message: '请输入HTML内容', trigger: 'blur' }]
};

// 分类选项
const categoryOptions = [
  { label: '用户相关', value: 'user' },
  { label: '系统通知', value: 'system' },
  { label: '自定义', value: 'custom' }
];

// 系统默认模板
const systemTemplates = ['welcome', 'resetPassword', 'notification', 'maintenance'];

// 常用变量
const commonVariables = [
  'systemName',
  'userName',
  'userEmail',
  'registerTime',
  'orderNo',
  'courseName',
  'orderStatus',
  'updateTime',
  'title',
  'content',
  'sendTime',
  'verifyCode',
  'resetLink'
];

// HTML模板
const htmlTemplates = {
  basic: `<div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
  <h2 style="color: #333; border-bottom: 2px solid #409eff; padding-bottom: 10px;">{{title}}</h2>
  <p style="line-height: 1.6; color: #666;">{{content}}</p>
  <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #999; font-size: 12px;">
    <p>此邮件由 {{systemName}} 自动发送，请勿回复。</p>
    <p>发送时间：{{sendTime}}</p>
  </div>
</div>`,

  welcome: `<div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
  <div style="background: white; padding: 30px; border-radius: 10px; color: #333;">
    <h1 style="color: #409eff; text-align: center; margin-bottom: 30px;">欢迎加入 {{systemName}}</h1>
    <p style="font-size: 16px; line-height: 1.8;">亲爱的 {{userName}}，</p>
    <p style="line-height: 1.6;">感谢您注册我们的平台！您的账户已成功创建。</p>
    <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
      <p><strong>注册邮箱：</strong>{{userEmail}}</p>
      <p><strong>注册时间：</strong>{{registerTime}}</p>
    </div>
    <p style="line-height: 1.6;">现在您可以开始探索我们的课程了！</p>
    <div style="text-align: center; margin: 30px 0;">
      <a href="#" style="background: #409eff; color: white; padding: 12px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">开始学习</a>
    </div>
  </div>
</div>`,

  notification: `<div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
  <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 15px; margin-bottom: 20px;">
    <h3 style="color: #856404; margin: 0 0 10px 0;">📢 {{title}}</h3>
  </div>
  <div style="background: white; padding: 20px; border: 1px solid #e9ecef; border-radius: 5px;">
    <p style="line-height: 1.6; color: #333;">{{content}}</p>
    <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">
      <p style="margin: 0; color: #666; font-size: 14px;">
        <strong>更新时间：</strong>{{updateTime}}<br>
        <strong>系统：</strong>{{systemName}}
      </p>
    </div>
  </div>
</div>`
};

// 计算属性
const filteredTemplates = computed(() => {
  let result = templates.value;

  // 按搜索文本过滤
  if (searchText.value) {
    const search = searchText.value.toLowerCase();
    result = result.filter(
      template => template.name.toLowerCase().includes(search) || template.description.toLowerCase().includes(search)
    );
  }

  // 按分类过滤
  if (selectedCategory.value) {
    result = result.filter(template => template.category === selectedCategory.value);
  }

  return result;
});

// 方法
const loadTemplates = async () => {
  try {
    const response = await request({
      url: '/api/email/templates',
      method: 'GET'
    });
    if (response && Array.isArray(response)) {
      templates.value = response;
    } else {
      templates.value = [];
    }
  } catch (error) {
    console.error('获取模板列表失败:', error);
    window.$message?.error('获取模板列表失败');
  }
};

const getCategoryType = (category: string) => {
  const typeMap: Record<string, string> = {
    user: 'info',
    security: 'warning',
    system: 'success',
    custom: 'default'
  };
  return typeMap[category] || 'default';
};

const getCategoryLabel = (category: string) => {
  const labelMap: Record<string, string> = {
    user: '用户相关',
    system: '系统通知',
    custom: '自定义'
  };
  return labelMap[category] || category;
};

const isSystemTemplate = (templateId: string) => {
  return systemTemplates.includes(templateId);
};

const handlePreview = (template: any) => {
  previewTemplate.value = template;
  showPreviewModal.value = true;
};

const handleCreate = () => {
  isEdit.value = false;
  currentTemplate.value = null;
  // 重置表单
  Object.assign(formData, {
    template_key: '',
    template_name: '',
    template_description: '',
    category: 'custom',
    subject: '',
    text_content: '',
    html_content: '',
    is_active: true
  });
  formVariables.value = [];
  newVariableName.value = '';
  showEditModal.value = true;
};

const handleEdit = (template: any) => {
  isEdit.value = true;
  currentTemplate.value = { ...template };
  // 填充表单
  Object.assign(formData, {
    template_key: template.id || template.template_key,
    template_name: template.name || template.template_name,
    template_description: template.description || template.template_description,
    category: template.category,
    subject: template.subject,
    text_content: template.text || template.text_content,
    html_content: template.html || template.html_content,
    is_active: template.isActive !== undefined ? template.isActive : template.is_active
  });

  // 处理变量
  let variables = template.variables || [];
  if (typeof variables === 'string') {
    try {
      variables = JSON.parse(variables);
    } catch {
      variables = variables
        .split(',')
        .map((v: string) => v.trim())
        .filter(Boolean);
    }
  }
  formVariables.value = Array.isArray(variables) ? [...variables] : [];

  showEditModal.value = true;
};

const handleDelete = async (template: any) => {
  try {
    await request({
      url: `/api/email/templates/${template.templateId}`,
      method: 'DELETE'
    });
    window.$message?.success('模板删除成功');
    await loadTemplates();
    emit('deleteTemplate', template.id);
  } catch (error) {
    console.error('删除模板失败:', error);
    window.$message?.error('删除模板失败');
  }
};

// 变量管理方法
const addVariable = () => {
  if (newVariableName.value.trim() && !formVariables.value.includes(newVariableName.value.trim())) {
    formVariables.value.push(newVariableName.value.trim());
    newVariableName.value = '';
  }
};

const removeVariable = (index: number) => {
  formVariables.value.splice(index, 1);
};

const addCommonVariable = (variable: string) => {
  if (!formVariables.value.includes(variable)) {
    formVariables.value.push(variable);
  }
};

// HTML模板插入方法
const insertHtmlTemplate = (templateType: keyof typeof htmlTemplates) => {
  formData.html_content = htmlTemplates[templateType];

  // 根据模板类型设置默认主题和文本内容
  switch (templateType) {
    case 'basic':
      if (!formData.subject) formData.subject = '{{title}} - {{systemName}}';
      if (!formData.text_content)
        formData.text_content =
          '{{title}}\n\n{{content}}\n\n此邮件由 {{systemName}} 自动发送，请勿回复。\n发送时间：{{sendTime}}';
      break;
    case 'welcome':
      if (!formData.subject) formData.subject = '欢迎加入 {{systemName}}';
      if (!formData.text_content)
        formData.text_content =
          '亲爱的 {{userName}}，\n\n感谢您注册我们的平台！您的账户已成功创建。\n\n注册邮箱：{{userEmail}}\n注册时间：{{registerTime}}\n\n现在您可以开始探索我们的课程了！';
      break;
    case 'notification':
      if (!formData.subject) formData.subject = '{{title}} - {{systemName}}';
      if (!formData.text_content)
        formData.text_content = '{{title}}\n\n{{content}}\n\n更新时间：{{updateTime}}\n系统：{{systemName}}';
      break;
    default:
      // 可选：为未知模板类型设置默认行为
      break;
  }

  // 自动添加相关变量
  const templateVariables = extractVariablesFromTemplate(
    formData.html_content + formData.subject + formData.text_content
  );
  templateVariables.forEach(variable => {
    if (!formVariables.value.includes(variable)) {
      formVariables.value.push(variable);
    }
  });
};

// 从模板中提取变量
const extractVariablesFromTemplate = (template: string): string[] => {
  const regex = /\{\{([^}]+)\}\}/g;
  const variables: string[] = [];
  let match;

  while ((match = regex.exec(template)) !== null) {
    const variable = match[1].trim();
    if (!variables.includes(variable)) {
      variables.push(variable);
    }
  }

  return variables;
};

const handleSaveTemplate = async () => {
  try {
    await formRef.value?.validate();
    saving.value = true;

    // 处理变量
    const templateData = {
      ...formData,
      variables: formVariables.value
    };

    if (isEdit.value && currentTemplate.value) {
      await request({
        url: `/api/email/templates/${currentTemplate.value.templateId}`,
        method: 'PUT',
        data: templateData
      });
      window.$message?.success('更新成功');
    } else {
      await request({
        url: '/api/email/templates',
        method: 'POST',
        data: templateData
      });
      window.$message?.success('创建成功');
    }

    showEditModal.value = false;
    await loadTemplates();
  } catch (error) {
    console.error('保存模板失败:', error);
    window.$message?.error('保存失败');
  } finally {
    saving.value = false;
  }
};

const handleToggleActive = async (template: any) => {
  try {
    await request({
      url: `/api/email/templates/${template.templateId}`,
      method: 'PUT',
      data: {
        ...template,
        isActive: template.isActive
      }
    });
    window.$message?.success(`模板已${template.isActive ? '启用' : '禁用'}`);
  } catch (error) {
    console.error('更新模板状态失败:', error);
    window.$message?.error('更新模板状态失败');
    // 回滚状态
    template.isActive = !template.isActive;
  }
};

// 生命周期
onMounted(() => {
  loadTemplates();
});
</script>

<template>
  <div class="flex flex-col gap-16px">
    <!-- 搜索和筛选 -->
    <div class="flex flex-wrap items-center gap-16px">
      <ElInput v-model="searchText" placeholder="搜索模板名称或描述" class="w-300px" clearable>
        <template #prefix>
          <SvgIcon icon="carbon:search" class="text-icon" />
        </template>
      </ElInput>

      <ElSelect v-model="selectedCategory" placeholder="选择分类" class="w-150px" clearable>
        <ElOption v-for="item in categoryOptions" :key="item.value" :label="item.label" :value="item.value" />
      </ElSelect>

      <ElButton @click="loadTemplates">
        <template #icon>
          <SvgIcon icon="carbon:reset" class="text-icon" />
        </template>
        刷新
      </ElButton>

      <ElButton type="primary" @click="handleCreate">
        <template #icon>
          <SvgIcon icon="carbon:add" class="text-icon" />
        </template>
        新建模板
      </ElButton>
    </div>

    <!-- 模板列表 -->
    <div class="grid grid-cols-1 gap-16px lg:grid-cols-3 md:grid-cols-2">
      <ElCard v-for="template in filteredTemplates" :key="template.id" class="template-card" shadow="hover">
        <template #header>
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-8px">
              <span class="font-medium">{{ template.name }}</span>
              <ElTag :type="getCategoryType(template.category) as any" size="small">
                {{ getCategoryLabel(template.category) }}
              </ElTag>
            </div>
            <ElSwitch v-model="template.isActive" size="small" @change="handleToggleActive(template)" />
          </div>
        </template>

        <div class="flex flex-col gap-12px">
          <p class="line-clamp-2 text-14px text-gray-600">
            {{ template.description }}
          </p>

          <div class="flex flex-wrap gap-4px">
            <ElTag v-for="variable in template.variables" :key="variable" size="small" type="info">
              {{ variable }}
            </ElTag>
          </div>
        </div>

        <template #footer>
          <div class="flex gap-8px">
            <ElButton size="small" @click="handlePreview(template)">
              <template #icon>
                <SvgIcon icon="carbon:view" class="text-icon" />
              </template>
              预览
            </ElButton>
            <ElButton size="small" type="primary" @click="handleEdit(template)">
              <template #icon>
                <SvgIcon icon="carbon:edit" class="text-icon" />
              </template>
              编辑
            </ElButton>
            <ElPopconfirm
              v-if="!isSystemTemplate(template.id)"
              title="确定要删除这个模板吗？"
              @confirm="handleDelete(template)"
            >
              <template #reference>
                <ElButton size="small" type="danger">
                  <template #icon>
                    <SvgIcon icon="carbon:delete" class="text-icon" />
                  </template>
                  删除
                </ElButton>
              </template>
            </ElPopconfirm>
          </div>
        </template>
      </ElCard>
    </div>

    <!-- 空状态 -->
    <ElEmpty v-if="filteredTemplates.length === 0" description="暂无邮件模板" />

    <!-- 预览弹窗 -->
    <EmailTemplatePreview v-if="showPreviewModal" v-model:visible="showPreviewModal" :template="previewTemplate" />

    <!-- 编辑弹窗 -->
    <ElDialog v-model="showEditModal" :title="isEdit ? '编辑模板' : '新建模板'" width="800px" destroy-on-close>
      <ElForm ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <ElFormItem label="模板标识" prop="template_key">
          <ElInput v-model="formData.template_key" placeholder="模板唯一标识" :disabled="isEdit" />
        </ElFormItem>

        <ElFormItem label="模板名称" prop="template_name">
          <ElInput v-model="formData.template_name" placeholder="模板显示名称" />
        </ElFormItem>

        <ElFormItem label="模板分类" prop="category">
          <ElSelect v-model="formData.category" placeholder="选择分类">
            <ElOption label="用户相关" value="user" />
            <ElOption label="系统通知" value="system" />
            <ElOption label="自定义" value="custom" />
          </ElSelect>
        </ElFormItem>

        <ElFormItem label="模板描述">
          <ElInput v-model="formData.template_description" type="textarea" placeholder="模板用途描述" :rows="2" />
        </ElFormItem>

        <ElFormItem label="邮件主题" prop="subject">
          <ElInput v-model="formData.subject" placeholder="邮件主题" />
        </ElFormItem>

        <ElFormItem label="纯文本内容" prop="text_content">
          <ElInput v-model="formData.text_content" type="textarea" placeholder="纯文本邮件内容" :rows="4" />
        </ElFormItem>

        <ElFormItem label="HTML内容" prop="html_content">
          <div class="flex flex-col gap-8px">
            <div class="flex gap-8px">
              <ElButton size="small" @click="insertHtmlTemplate('basic')">
                <template #icon>
                  <SvgIcon icon="carbon:template" class="text-icon" />
                </template>
                基础模板
              </ElButton>
              <ElButton size="small" @click="insertHtmlTemplate('welcome')">
                <template #icon>
                  <SvgIcon icon="carbon:user" class="text-icon" />
                </template>
                欢迎模板
              </ElButton>
              <ElButton size="small" @click="insertHtmlTemplate('notification')">
                <template #icon>
                  <SvgIcon icon="carbon:notification" class="text-icon" />
                </template>
                通知模板
              </ElButton>
            </div>
            <ElInput
              v-model="formData.html_content"
              type="textarea"
              placeholder="HTML邮件内容，支持变量 {{变量名}}"
              :rows="8"
              class="font-mono"
            />
          </div>
        </ElFormItem>

        <ElFormItem label="模板变量">
          <div class="flex flex-col gap-8px">
            <div class="flex gap-8px">
              <ElInput v-model="newVariableName" placeholder="输入变量名" @keyup.enter="addVariable" />
              <ElButton @click="addVariable">
                <template #icon>
                  <SvgIcon icon="carbon:add" class="text-icon" />
                </template>
                添加
              </ElButton>
            </div>

            <div class="flex flex-wrap gap-8px">
              <ElTag v-for="(variable, index) in formVariables" :key="index" closable @close="removeVariable(index)">
                {{ variable }}
              </ElTag>
            </div>

            <div class="text-sm text-gray-500">
              常用变量：
              <ElButton
                v-for="commonVar in commonVariables"
                :key="commonVar"
                link
                size="small"
                @click="addCommonVariable(commonVar)"
              >
                {{ commonVar }}
              </ElButton>
            </div>
          </div>
        </ElFormItem>

        <ElFormItem label="是否启用">
          <ElSwitch v-model="formData.is_active" />
        </ElFormItem>
      </ElForm>

      <template #footer>
        <div class="flex justify-end gap-8px">
          <ElButton @click="showEditModal = false">取消</ElButton>
          <ElButton type="primary" :loading="saving" @click="handleSaveTemplate">
            {{ isEdit ? '更新' : '创建' }}
          </ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<style scoped>
.template-card {
  @apply transition-all duration-200;
}

.template-card:hover {
  @apply shadow-lg;
}

.flex-col {
  @apply flex flex-col;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
