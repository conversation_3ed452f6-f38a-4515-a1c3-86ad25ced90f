<script setup lang="ts">
import { computed, reactive, ref, watch } from 'vue';
import { request } from '@/service/request';

interface Props {
  visible: boolean;
  template: any;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
});

const emit = defineEmits<Emits>();

// 响应式数据
const activeTab = ref('rendered');
const testEmail = ref('');
const sending = ref(false);
const variableValues = reactive<Record<string, string>>({});

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: value => emit('update:visible', value)
});

const templateVariables = computed(() => {
  if (!props.template) return [];

  // 处理不同格式的变量数据
  let variables = props.template.variables || [];

  if (typeof variables === 'string') {
    try {
      variables = JSON.parse(variables);
    } catch {
      variables = variables
        .split(',')
        .map((v: string) => v.trim())
        .filter(Boolean);
    }
  }

  return Array.isArray(variables) ? variables : [];
});

const renderedContent = computed(() => {
  const subject = props.template?.subject || '';
  const html = props.template?.html || props.template?.html_content || '';
  const text = props.template?.text || props.template?.text_content || '';

  return {
    subject: renderTemplate(subject, variableValues),
    html: renderTemplate(html, variableValues),
    text: renderTemplate(text, variableValues)
  };
});

// 方法
function renderTemplate(template: string, variables: Record<string, string>): string {
  if (!template) return '';

  let rendered = template;

  // 替换 {{variable}} 格式的变量
  Object.entries(variables).forEach(([key, value]) => {
    const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
    rendered = rendered.replace(regex, value || `{{${key}}}`);
  });

  return rendered;
}

function fillSampleData() {
  const sampleData: Record<string, string> = {
    systemName: '在线课程管理系统',
    userName: '张三',
    userEmail: '<EMAIL>',
    registerTime: new Date().toLocaleString(),
    orderNo: `ORD${Date.now()}`,
    courseName: 'Vue3 + TypeScript 实战课程',
    orderStatus: '已支付',
    updateTime: new Date().toLocaleString(),
    title: '系统通知',
    content: '这是一条重要的系统通知消息。',
    sendTime: new Date().toLocaleString()
  };

  templateVariables.value.forEach(variable => {
    variableValues[variable] = sampleData[variable] || `示例${variable}`;
  });
}

function clearVariables() {
  templateVariables.value.forEach(variable => {
    variableValues[variable] = '';
  });
}

function updatePreview() {
  // 预览会自动更新，因为使用了计算属性
}

function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

async function sendTestEmail() {
  if (!testEmail.value || !isValidEmail(testEmail.value)) {
    window.$message?.error('请输入有效的邮箱地址');
    return;
  }

  try {
    sending.value = true;

    await request({
      url: '/api/email/send',
      method: 'POST',
      data: {
        to: testEmail.value,
        template: props.template.id || props.template.template_key,
        variables: variableValues
      }
    });

    window.$message?.success('测试邮件发送成功');
  } catch (error) {
    console.error('发送测试邮件失败:', error);
    window.$message?.error('发送测试邮件失败');
  } finally {
    sending.value = false;
  }
}

function handleClose() {
  visible.value = false;
}

// 监听模板变化，初始化变量值
watch(
  () => props.template,
  template => {
    if (template) {
      // 清空之前的变量值
      Object.keys(variableValues).forEach(key => {
        delete variableValues[key];
      });

      // 初始化新的变量值
      templateVariables.value.forEach(variable => {
        variableValues[variable] = '';
      });
    }
  },
  { immediate: true }
);
</script>

<template>
  <ElDialog v-model="visible" title="模板预览" width="1000px" destroy-on-close @close="handleClose">
    <div class="flex flex-col gap-16px">
      <!-- 变量输入 -->
      <ElCard v-if="templateVariables.length > 0" header="模板变量" shadow="never">
        <div class="grid grid-cols-1 gap-16px md:grid-cols-2">
          <ElFormItem v-for="variable in templateVariables" :key="variable" :label="variable" label-width="100px">
            <ElInput v-model="variableValues[variable]" :placeholder="`输入 ${variable} 的值`" @input="updatePreview" />
          </ElFormItem>
        </div>
        <div class="mt-16px flex gap-8px">
          <ElButton size="small" @click="fillSampleData">
            <template #icon>
              <SvgIcon icon="carbon:data-1" class="text-icon" />
            </template>
            填充示例数据
          </ElButton>
          <ElButton size="small" @click="clearVariables">
            <template #icon>
              <SvgIcon icon="carbon:clean" class="text-icon" />
            </template>
            清空变量
          </ElButton>
        </div>
      </ElCard>

      <!-- 预览内容 -->
      <ElTabs v-model="activeTab" type="border-card">
        <ElTabPane label="渲染预览" name="rendered">
          <div class="flex flex-col gap-16px">
            <!-- 邮件主题 -->
            <ElCard header="邮件主题" shadow="never">
              <div class="rounded bg-gray-50 p-12px text-16px font-medium">
                {{ renderedContent.subject }}
              </div>
            </ElCard>

            <!-- HTML预览 -->
            <ElCard header="HTML预览" shadow="never">
              <div class="min-h-200px border rounded bg-white p-16px">
                <div class="email-content" v-html="renderedContent.html"></div>
              </div>
            </ElCard>

            <!-- 纯文本预览 -->
            <ElCard header="纯文本预览" shadow="never">
              <div class="min-h-100px whitespace-pre-wrap rounded bg-gray-50 p-12px text-14px font-mono">
                {{ renderedContent.text }}
              </div>
            </ElCard>
          </div>
        </ElTabPane>

        <ElTabPane label="源码查看" name="source">
          <div class="flex flex-col gap-16px">
            <!-- 主题源码 -->
            <ElCard header="主题模板" shadow="never">
              <ElInput :model-value="template.subject" type="textarea" :rows="2" readonly class="font-mono" />
            </ElCard>

            <!-- HTML源码 -->
            <ElCard header="HTML模板" shadow="never">
              <ElInput
                :model-value="template.html || template.html_content"
                type="textarea"
                :rows="10"
                readonly
                class="font-mono"
              />
            </ElCard>

            <!-- 文本源码 -->
            <ElCard header="文本模板" shadow="never">
              <ElInput
                :model-value="template.text || template.text_content"
                type="textarea"
                :rows="6"
                readonly
                class="font-mono"
              />
            </ElCard>
          </div>
        </ElTabPane>

        <ElTabPane label="测试发送" name="test">
          <div class="flex flex-col gap-16px">
            <ElCard header="测试邮件发送" shadow="never">
              <div class="flex flex-col gap-16px">
                <ElFormItem label="收件人邮箱" label-width="100px">
                  <ElInput v-model="testEmail" placeholder="输入测试邮箱地址" type="email" />
                </ElFormItem>

                <div class="flex justify-end">
                  <ElButton
                    type="primary"
                    :loading="sending"
                    :disabled="!testEmail || !isValidEmail(testEmail)"
                    @click="sendTestEmail"
                  >
                    <template #icon>
                      <SvgIcon icon="carbon:email" class="text-icon" />
                    </template>
                    发送测试邮件
                  </ElButton>
                </div>
              </div>
            </ElCard>
          </div>
        </ElTabPane>
      </ElTabs>
    </div>

    <template #footer>
      <div class="flex justify-end">
        <ElButton @click="handleClose">关闭</ElButton>
      </div>
    </template>
  </ElDialog>
</template>

<style scoped>
.email-content {
  /* 邮件内容样式 */
  line-height: 1.6;
}

.email-content h1,
.email-content h2,
.email-content h3 {
  margin: 16px 0 8px 0;
  color: #333;
}

.email-content p {
  margin: 8px 0;
}

.email-content a {
  color: #409eff;
  text-decoration: none;
}

.email-content a:hover {
  text-decoration: underline;
}

.font-mono {
  font-family: 'Courier New', Courier, monospace;
}
</style>
