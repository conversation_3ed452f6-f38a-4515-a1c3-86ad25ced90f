<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { request } from '@/service/request';

defineOptions({
  name: 'EmailRecords'
});

// 响应式数据
const loading = ref(false);
const showDetailModal = ref(false);
const currentRecord = ref<any>(null);
const tableData = ref<any[]>([]);
const dateRange = ref<[string, string] | undefined>(undefined);

// 搜索参数
const searchParams = reactive({
  email: '',
  status: undefined as string | undefined
});

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0
});

// 选项数据
const statusOptions = [
  { label: '待发送', value: 'pending' },
  { label: '发送中', value: 'sending' },
  { label: '已发送', value: 'sent' },
  { label: '发送失败', value: 'failed' },
  { label: '退信', value: 'bounced' }
];

// 工具函数
function getStatusType(status: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' {
  const typeMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    pending: 'info',
    sending: 'primary',
    sent: 'success',
    failed: 'danger',
    bounced: 'warning'
  };
  return typeMap[status] || 'info';
}

function getStatusText(status: string): string {
  const textMap: Record<string, string> = {
    pending: '待发送',
    sending: '发送中',
    sent: '已发送',
    failed: '发送失败',
    bounced: '退信'
  };
  return textMap[status] || status;
}

// 处理函数
function handleSearch() {
  pagination.page = 1;
  fetchData();
}

function handleReset() {
  Object.assign(searchParams, {
    email: '',
    status: undefined
  });
  dateRange.value = undefined;
  pagination.page = 1;
  fetchData();
}

function handleSizeChange(size: number) {
  pagination.pageSize = size;
  pagination.page = 1;
  fetchData();
}

function handleCurrentChange(page: number) {
  pagination.page = page;
  fetchData();
}

function handleView(row: any) {
  currentRecord.value = row;
  showDetailModal.value = true;
}

// 获取数据
async function fetchData() {
  try {
    loading.value = true;

    const params: any = {
      page: pagination.page,
      pageSize: pagination.pageSize,
      ...searchParams
    };

    if (dateRange.value) {
      params.startDate = dateRange.value[0];
      params.endDate = dateRange.value[1];
    }

    const response = await request({
      url: '/api/email/records',
      method: 'GET',
      params
    });

    if (response?.records) {
      tableData.value = response.records;
      pagination.itemCount = response.pagination?.total || 0;
    } else {
      tableData.value = [];
      pagination.itemCount = 0;
    }
  } catch (error) {
    console.error('获取邮件记录失败:', error);
    // 不显示错误信息，避免干扰用户
  } finally {
    loading.value = false;
  }
}

// 生命周期
onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="flex flex-col gap-16px">
    <!-- 搜索栏 -->
    <div class="flex flex-wrap items-center gap-16px">
      <ElInput v-model="searchParams.email" placeholder="搜索收件人邮箱" clearable class="w-240px">
        <template #prefix>
          <SvgIcon icon="carbon:search" class="text-icon" />
        </template>
      </ElInput>

      <ElSelect v-model="searchParams.status" placeholder="发送状态" clearable class="w-120px">
        <ElOption v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
      </ElSelect>

      <ElDatePicker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        class="w-240px"
      />

      <div class="flex gap-8px">
        <ElButton type="primary" @click="handleSearch">
          <template #icon>
            <SvgIcon icon="carbon:search" class="text-icon" />
          </template>
          搜索
        </ElButton>

        <ElButton @click="handleReset">
          <template #icon>
            <SvgIcon icon="carbon:reset" class="text-icon" />
          </template>
          重置
        </ElButton>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="w-full overflow-x-auto">
      <ElTable v-loading="loading" :data="tableData" style="width: 100%" max-height="60vh">
        <ElTableColumn prop="recipientEmail" label="收件人" min-width="180" show-overflow-tooltip />
        <ElTableColumn prop="subject" label="邮件主题" min-width="200" show-overflow-tooltip />
        <ElTableColumn prop="templateName" label="使用模板" width="120" />
        <ElTableColumn prop="status" label="发送状态" width="100">
          <template #default="{ row }">
            <ElTag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="sendTime" label="发送时间" width="160" />
        <ElTableColumn prop="createTime" label="创建时间" width="160" />
        <ElTableColumn label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <div class="flex gap-8px">
              <ElButton size="small" type="info" @click="handleView(row)">查看</ElButton>
            </div>
          </template>
        </ElTableColumn>
      </ElTable>
    </div>

    <!-- 分页 -->
    <div class="mt-16px flex justify-center">
      <ElPagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="pagination.itemCount"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 邮件详情弹窗 -->
    <ElDialog v-model="showDetailModal" title="邮件详情" width="800px" destroy-on-close>
      <div v-if="currentRecord" class="space-y-16px">
        <ElDescriptions :column="2" border>
          <ElDescriptionsItem label="收件人邮箱">
            {{ currentRecord.recipientEmail }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="邮件主题">
            {{ currentRecord.subject }}
          </ElDescriptionsItem>
          <ElDescriptionsItem label="发送状态">
            <ElTag :type="getStatusType(currentRecord.status)">
              {{ getStatusText(currentRecord.status) }}
            </ElTag>
          </ElDescriptionsItem>
          <ElDescriptionsItem label="发送时间">
            {{ currentRecord.sendTime || '-' }}
          </ElDescriptionsItem>
        </ElDescriptions>
      </div>
    </ElDialog>
  </div>
</template>

<style scoped>
/* 响应式表格滚动 */
.el-table {
  @apply w-full;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .el-table {
    font-size: 12px;
  }

  .flex-wrap {
    flex-direction: column;
    align-items: stretch;
  }

  .w-240px,
  .w-120px {
    width: 100%;
  }
}
</style>
