<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';
import { fetchEmailStats, fetchSendEmail } from '@/service/api/email';
import EmailTemplates from './modules/EmailTemplates.vue';
import EmailRecords from './modules/EmailRecords.vue';

defineOptions({
  name: 'EmailManagement'
});

// 路由
const router = useRouter();

// 响应式数据
const activeTab = ref('templates');
const showTestModal = ref(false);
const testSending = ref(false);
const testFormRef = ref<FormInstance | null>(null);

// 统计数据
const stats = reactive({
  totalSent: 0,
  totalDelivered: 0,
  totalFailed: 0,
  totalBounced: 0,
  deliveryRate: 0
});

// 测试邮件表单
const testForm = reactive({
  email: '',
  subject: '测试邮件',
  content: '这是一封测试邮件，用于验证邮件配置是否正确。'
});

// 表单验证规则
const testFormRules: FormRules = {
  email: [
    { required: true, message: '请输入收件人邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  subject: [{ required: true, message: '请输入邮件主题', trigger: 'blur' }],
  content: [{ required: true, message: '请输入邮件内容', trigger: 'blur' }]
};

// 处理函数
function goToConfig() {
  router.push('/system-center/config');
}

function handleTestEmail() {
  showTestModal.value = true;
}

async function handleSendTest() {
  try {
    await testFormRef.value?.validate();
    testSending.value = true;

    await fetchSendEmail({
      to: testForm.email,
      subject: testForm.subject,
      text: testForm.content,
      html: `<p>${testForm.content.replace(/\n/g, '<br>')}</p>`
    });

    ElMessage.success('测试邮件发送成功');
    showTestModal.value = false;

    // 重置表单
    Object.assign(testForm, {
      email: '',
      subject: '测试邮件',
      content: '这是一封测试邮件，用于验证邮件配置是否正确。'
    });

    // 刷新统计
    fetchStats();
  } catch (error: any) {
    console.error('发送测试邮件失败:', error);
    ElMessage.error(error.message || '发送测试邮件失败');
  } finally {
    testSending.value = false;
  }
}

// 获取统计数据
async function fetchStats() {
  try {
    const response = await fetchEmailStats();
    if (response.data?.overview) {
      Object.assign(stats, response.data.overview);
    }
  } catch (error) {
    console.error('获取邮件统计失败:', error);
    // 使用默认值，不显示错误信息
  }
}

// 生命周期
onMounted(() => {
  fetchStats();
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-medium">邮件管理</span>
          <div class="flex gap-8px">
            <ElButton type="info" @click="handleTestEmail">
              <template #icon>
                <SvgIcon icon="carbon:email" class="text-icon" />
              </template>
              测试邮件
            </ElButton>
          </div>
        </div>
      </template>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 mb-24px gap-16px md:grid-cols-4">
        <ElCard shadow="hover">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl text-primary font-bold">{{ stats.totalSent }}</div>
              <div class="text-sm text-gray-500">总发送数</div>
            </div>
            <SvgIcon icon="carbon:email" class="text-3xl text-primary" />
          </div>
        </ElCard>

        <ElCard shadow="hover">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl text-success font-bold">{{ stats.totalDelivered }}</div>
              <div class="text-sm text-gray-500">成功投递</div>
            </div>
            <SvgIcon icon="carbon:checkmark-filled" class="text-3xl text-success" />
          </div>
        </ElCard>

        <ElCard shadow="hover">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-danger text-2xl font-bold">{{ stats.totalFailed }}</div>
              <div class="text-sm text-gray-500">发送失败</div>
            </div>
            <SvgIcon icon="carbon:error-filled" class="text-danger text-3xl" />
          </div>
        </ElCard>

        <ElCard shadow="hover">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-2xl text-info font-bold">{{ stats.deliveryRate }}%</div>
              <div class="text-sm text-gray-500">投递率</div>
            </div>
            <SvgIcon icon="carbon:chart-line" class="text-3xl text-info" />
          </div>
        </ElCard>
      </div>

      <!-- 配置提示 -->
      <ElAlert title="邮件配置" type="info" :closable="false" class="mb-16px">
        <template #default>
          邮件SMTP配置请前往
          <ElButton type="primary" link @click="goToConfig">系统配置 → 邮件设置</ElButton>
          进行配置
        </template>
      </ElAlert>

      <!-- 标签页 -->
      <ElTabs v-model="activeTab" type="border-card">
        <ElTabPane label="邮件模板" name="templates">
          <EmailTemplates @refresh="fetchStats" />
        </ElTabPane>

        <ElTabPane label="发送记录" name="records">
          <EmailRecords />
        </ElTabPane>
      </ElTabs>
    </ElCard>

    <!-- 测试邮件弹窗 -->
    <ElDialog v-model="showTestModal" title="发送测试邮件" width="500px" destroy-on-close>
      <ElForm ref="testFormRef" :model="testForm" :rules="testFormRules" label-width="80px">
        <ElFormItem label="收件人" prop="email">
          <ElInput v-model="testForm.email" placeholder="请输入收件人邮箱" />
        </ElFormItem>

        <ElFormItem label="邮件主题" prop="subject">
          <ElInput v-model="testForm.subject" placeholder="请输入邮件主题" />
        </ElFormItem>

        <ElFormItem label="邮件内容" prop="content">
          <ElInput v-model="testForm.content" type="textarea" :rows="4" placeholder="请输入邮件内容" />
        </ElFormItem>
      </ElForm>

      <template #footer>
        <div class="flex justify-end gap-8px">
          <ElButton @click="showTestModal = false">取消</ElButton>
          <ElButton type="primary" :loading="testSending" @click="handleSendTest">发送</ElButton>
        </div>
      </template>
    </ElDialog>
  </div>
</template>

<style scoped>
/* 统计卡片样式 */
.el-card {
  transition: all 0.3s ease;
}

.el-card:hover {
  transform: translateY(-2px);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .grid-cols-1.md\\:grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .grid-cols-1.md\\:grid-cols-4 {
    grid-template-columns: 1fr;
  }
}
</style>
