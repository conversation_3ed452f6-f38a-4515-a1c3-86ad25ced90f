<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ArrowDown, ArrowRight, Search } from '@element-plus/icons-vue';
import { request } from '@/service/request';
import { useTableOperate } from '@/hooks/common/table-pagination';
const emit = defineEmits<{
  (e: 'selectRole', role: any): void;
}>();
import ResponsiveDialog from '@/components/responsive/ResponsiveDialog.vue';


defineOptions({
  name: 'RoleManagement'
});

// 响应式数据
const modalVisible = ref(false);
// const permissionModalVisible = ref(false); // 已简化：移除模态框，保留抽屉方案
const rolePermissionManagerVisible = ref(false);
const isEdit = ref(false);
const saving = ref(false);
const permissionSaving = ref(false);
const currentRoleId = ref<number | null>(null);
const availablePermissions = ref<any[]>([]);
const rolePermissions = ref<number[]>([]);
const collapsedGroups = ref<Set<string>>(new Set());
const permissionSearchKeyword = ref('');
const permissionTypeFilter = ref('');
// const permissionSyncing = ref(false); // 已下架前端同步入口

// 表单引用
const formRef = ref<FormInstance | null>(null);

// 搜索参数
const searchParams = reactive({
  keyword: '',
  status: undefined as number | undefined
});

// 角色表单
const roleForm = reactive({
  role_id: null as number | null,
  role_code: '',
  role_name: '',
  role_description: '',
  parent_role_id: null as number | null,
  role_level: 1,
  is_default: 0,
  status: 1,
  sort_order: 0
});

// 表单验证规则
const formRules: FormRules = {
  role_code: [
    { required: true, message: '请输入角色代码', trigger: 'blur' },
    { min: 2, max: 50, message: '角色代码长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  role_name: [
    { required: true, message: '请输入角色名称', trigger: 'blur' },
    { min: 2, max: 100, message: '角色名称长度在 2 到 100 个字符', trigger: 'blur' }
  ],
  role_level: [
    { required: true, message: '请输入角色级别', trigger: 'blur' },
    { type: 'number', min: 1, max: 100, message: '角色级别在 1 到 100 之间', trigger: 'blur' }
  ]
};

// 计算属性
const groupedPermissions = computed(() => {
  const groups: Record<string, any[]> = {};

  availablePermissions.value.forEach(permission => {
    const groupName = getGroupDisplayName(permission.permission_group);
    if (!groups[groupName]) {
      groups[groupName] = [];
    }
    groups[groupName].push(permission);
  });

  return Object.entries(groups).map(([name, permissions]) => ({
    name,
    permissions: permissions.sort((a, b) => a.sort_order - b.sort_order)
  }));
});

// 表格数据获取
const { pagination, loading, tableData, handlePageChange, handleSizeChange, fetchTableData, refreshTable } =
  useTableOperate({
    fetchData: async params => {
      const response = await request({
        url: '/api/role/list',
        method: 'get',
        params: {
          ...params,
          ...searchParams
        }
      });

      // request函数已经解包了响应，直接访问数据
      return {
        data: (response as any).list || [],
        total: (response as any).total || 0
      };
      return { data: [], total: 0 };
    }
  });

// API调用函数
async function fetchPermissions() {
  try {
    const response = await request({
      url: '/api/permission/list',
      method: 'get',
      params: { limit: 1000 }
    });
    // 仅保留 menu/button 类型的权限
    const list = (response as any).list || [];
    availablePermissions.value = list.filter((p: any) => ['menu', 'button'].includes(p.permission_type));
  } catch (error) {
    console.error('获取权限列表失败:', error);
  }
}


// 获取角色权限
async function fetchRolePermissions(roleId: number) {
  try {
    const response = await request({
      url: `/api/role/${roleId}/permissions`,
      method: 'get'
    });
    return (response as any) || [];
  } catch (error) {
    console.error('获取角色权限失败:', error);
    return [];
  }
}

// 保存角色权限
async function saveRolePermissions(roleId: number, permissions: any[]) {
  try {
    await request({
      url: `/api/role/${roleId}/permissions`,
      method: 'post',
      data: { permissions }
    });
    ElMessage.success('权限设置成功');
    return true;
  } catch (error) {
    console.error('保存角色权限失败:', error);
    ElMessage.error('权限设置失败');
    return false;
  }
}

// 复制角色权限
async function copyRolePermissions(targetRoleId: number, sourceRoleId: number) {
  try {
    await request({
      url: `/api/role/${targetRoleId}/copy-permissions`,
      method: 'post',
      data: { sourceRoleId }
    });
    ElMessage.success('权限复制成功');
    return true;
  } catch (error) {
    console.error('复制角色权限失败:', error);
    ElMessage.error('权限复制失败');
    return false;
  }
}

async function saveRole() {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    saving.value = true;

    const apiUrl = isEdit.value ? `/api/role/${roleForm.role_id}` : '/api/role';
    const method = isEdit.value ? 'put' : 'post';

    const response = await request({
      url: apiUrl,
      method,
      data: {
        role_code: roleForm.role_code,
        role_name: roleForm.role_name,
        role_description: roleForm.role_description,
        parent_role_id: roleForm.parent_role_id,
        role_level: roleForm.role_level,
        is_default: roleForm.is_default,
        status: roleForm.status,
        sort_order: roleForm.sort_order
      }
    });

    // 检查操作是否成功
    const isSuccess = response === true || (response as any)?.role_id || (response as any)?.success;
    if (isSuccess) {
      ElMessage.success(isEdit.value ? '角色更新成功' : '角色创建成功');
      modalVisible.value = false;
      fetchTableData();
    } else {
      ElMessage.error('操作失败');
    }
  } catch (error: any) {
    console.error('保存角色失败:', error);
    ElMessage.error(error.message || '保存角色失败');
  } finally {
    saving.value = false;
  }
}

async function deleteRole(roleId: number) {
  try {
    const response = await request({
      url: `/api/role/${roleId}`,
      method: 'delete'
    });
    // 检查删除是否成功
    const isSuccess = response === true || (response as any)?.success;
    if (isSuccess) {
      ElMessage.success('角色删除成功');
      fetchTableData();
    } else {
      ElMessage.error('删除失败');
    }
  } catch (error: any) {
    console.error('删除角色失败:', error);
    ElMessage.error(error.message || '删除角色失败');
  }
}

// 处理函数
function handleSearch() {
  fetchTableData();
}

function handleReset() {
  Object.assign(searchParams, {
    keyword: '',
    status: undefined
  });
  fetchTableData();
}

function handleCreate() {
  isEdit.value = false;
  Object.assign(roleForm, {
    role_id: null,
    role_code: '',
    role_name: '',
    role_description: '',
    parent_role_id: null,
    role_level: 1,
    is_default: 0,
    status: 1,
    sort_order: 0
  });
  modalVisible.value = true;
}

function handleEdit(row: any) {
  isEdit.value = true;
  Object.assign(roleForm, {
    role_id: row.role_id,
    role_code: row.role_code,
    role_name: row.role_name,
    role_description: row.role_description || '',
    parent_role_id: row.parent_role_id,
    role_level: row.role_level,
    is_default: row.is_default,
    status: row.status,
    sort_order: row.sort_order
  });
  modalVisible.value = true;
}

// 权限管理相关函数
async function handleManagePermissions(row: any) {
  currentRoleId.value = row.role_id;

  // 获取所有权限
  await fetchPermissions();

  // 获取角色当前权限
// 操作合并：更多菜单（统一实现）
function onMoreAction(cmd: string, row: any) {
  switch (cmd) {
    case 'permission':
      handleManagePermissions(row);
      break;
    case 'delete':
      handleDelete(row.role_id);
      break;
    default:
      ElMessage.warning('未知操作');
  }
}

  try {
    const permissions = await fetchRolePermissions(row.role_id);
    rolePermissions.value = permissions.map((p: any) => p.permission_id);
  } catch (error) {
    console.error('获取角色权限失败:', error);
    rolePermissions.value = [];
  }

  rolePermissionManagerVisible.value = true;
  // 保持在本页，不进行跨Tab跳转；如需联动可在保存后触发
  // emit('selectRole', row);
}

// 保存权限设置
async function handleSavePermissions() {
  if (!currentRoleId.value) return;

  permissionSaving.value = true;
  try {
    const permissions = rolePermissions.value.map(permissionId => ({
      permission_id: permissionId
    }));

    const success = await saveRolePermissions(currentRoleId.value, permissions);
    if (success) {
      rolePermissionManagerVisible.value = false;
      await refreshTable();
    }
  } finally {
    permissionSaving.value = false;
  }
}


// 复制权限
async function handleCopyPermissions(sourceRoleId: number) {
  if (!currentRoleId.value) return;

  try {
    const success = await copyRolePermissions(currentRoleId.value, sourceRoleId);
    if (success) {
      // 重新获取权限
      const permissions = await fetchRolePermissions(currentRoleId.value);
      rolePermissions.value = permissions.map((p: any) => p.permission_id);
    }
  } catch (error) {
    console.error('复制权限失败:', error);
  }
}

function handleDelete(roleId: number) {
  ElMessageBox.confirm('确定要删除这个角色吗？', '确认删除', {
    type: 'warning'
  }).then(() => {
    deleteRole(roleId);
  });
}

// 权限管理成功回调
function onPermissionManageSuccess() {
  refreshTable(); // 刷新表格数据
}


// 获取角色类型标签
function getRoleType(roleCode: string): 'primary' | 'success' | 'info' | 'warning' | 'danger' {
  const typeMap: Record<string, 'primary' | 'success' | 'info' | 'warning' | 'danger'> = {
    super_admin: 'danger',
    admin: 'warning',
    manager: 'success',
    agent: 'info',
    vip: 'primary',
    user: 'info'
  };
  return typeMap[roleCode] || 'info';
}

// 获取权限分组显示名称
function getGroupDisplayName(group: string) {
  const groupMap: Record<string, string> = {
    // 核心管理模块
    用户管理: '用户管理',
    角色管理: '角色管理',
    权限管理: '权限管理',
    系统管理: '系统管理',

    // 业务管理模块
    订单管理: '订单管理',
    平台管理: '平台管理',
    服务商管理: '服务商管理',
    财务管理: '财务管理',
    统计报表: '统计报表',

    // 兼容旧的英文分组名
    user: '用户管理',
    role: '角色管理',
    permission: '权限管理',
    system: '系统管理',
    order: '订单管理',
    platform: '平台管理',
    provider: '服务商管理',
    finance: '财务管理',
    statistics: '统计报表',
    course: '课程管理',
    supplier: '供应商管理',
    report: '报表统计',
    config: '配置管理'
  };
  return groupMap[group] || group;
}

// 基于路由的权限分组计算属性
const permissionGroups = computed(() => {
  const routeGroups: Record<string, any> = {};

  availablePermissions.value.forEach(permission => {
    const group = permission.permission_group || 'other';

    // 如果是路由权限，创建路由分组
    if (permission.permission_code?.startsWith('route:')) {
      if (!routeGroups[group]) {
        routeGroups[group] = {
          name: group,
          displayName: getGroupDisplayName(group),
          routePermission: permission,
          buttonPermissions: [],
          permissions: [] // 兼容性字段
        };
      }
      routeGroups[group].routePermission = permission;
    } else {
      // 如果是按钮权限，找到对应的路由分组
      if (!routeGroups[group]) {
        routeGroups[group] = {
          name: group,
          displayName: getGroupDisplayName(group),
          routePermission: null,
          buttonPermissions: [],
          permissions: [] // 兼容性字段
        };
      }
      routeGroups[group].buttonPermissions.push(permission);
    }
  });

  // 转换为数组格式，并排序按钮权限
  return Object.values(routeGroups).map((group: any) => ({
    ...group,
    buttonPermissions: group.buttonPermissions.sort((a: any, b: any) =>
      a.permission_name.localeCompare(b.permission_name)
    ),
    // 合并所有权限用于兼容性
    permissions: [...(group.routePermission ? [group.routePermission] : []), ...group.buttonPermissions]
  }));
});

// 权限选择相关函数
function isGroupSelected(permissions: any[]) {
  return permissions.length > 0 && permissions.every(p => rolePermissions.value.includes(p.permission_id));
}

function isGroupIndeterminate(permissions: any[]) {
  const selectedCount = permissions.filter(p => rolePermissions.value.includes(p.permission_id)).length;
  return selectedCount > 0 && selectedCount < permissions.length;
}

function toggleGroupSelection(permissions: any[], selected: boolean) {
  if (selected) {
    permissions.forEach(p => {
      if (!rolePermissions.value.includes(p.permission_id)) {
        rolePermissions.value.push(p.permission_id);
      }
    });
  } else {
    permissions.forEach(p => {
      const index = rolePermissions.value.indexOf(p.permission_id);
      if (index > -1) {
        rolePermissions.value.splice(index, 1);
      }
    });
  }
}

function getPermissionTypeTag(type: string): 'primary' | 'success' | 'info' | 'warning' | 'danger' {
  const typeMap: Record<string, 'primary' | 'success' | 'info' | 'warning' | 'danger'> = {
    menu: 'primary',
    button: 'success'
  };
  return typeMap[type] || 'info';
}

function showCopyPermissionDialog() {
  // 这里可以实现复制权限的对话框
  ElMessage.info('复制权限功能开发中...');
}

// 权限分组折叠控制
function toggleGroupCollapse(groupName: string) {
  if (collapsedGroups.value.has(groupName)) {
    collapsedGroups.value.delete(groupName);
  } else {
    collapsedGroups.value.add(groupName);
  }
}

function isGroupCollapsed(groupName: string) {
  return collapsedGroups.value.has(groupName);
}

function expandAllGroups() {
  collapsedGroups.value.clear();
}

function collapseAllGroups() {
  permissionGroups.value.forEach(group => {
    collapsedGroups.value.add(group.name);
  });
}

// 权限搜索和筛选
const filteredPermissionGroups = computed(() => {
  let filtered = permissionGroups.value;

  // 按关键词搜索
  if (permissionSearchKeyword.value) {
    filtered = filtered
      .map(group => ({
        ...group,
        permissions: group.permissions.filter(
          (permission: any) =>
            permission.permission_name.toLowerCase().includes(permissionSearchKeyword.value.toLowerCase()) ||
            permission.permission_description.toLowerCase().includes(permissionSearchKeyword.value.toLowerCase()) ||
            permission.permission_code.toLowerCase().includes(permissionSearchKeyword.value.toLowerCase())
        )
      }))
      .filter(group => group.permissions.length > 0);
  }

  // 按权限类型筛选
  if (permissionTypeFilter.value) {
    filtered = filtered
      .map(group => ({
        ...group,
        permissions: group.permissions.filter(
          (permission: any) => permission.permission_type === permissionTypeFilter.value
        )
      }))
      .filter(group => group.permissions.length > 0);
  }

  return filtered;
});

// 权限同步、完整性检查与清理功能已彻底下架（前端不再保留代码路径）

//   try {
//     const response = await request({
//       url: '/api/permission-sync/cleanup',
//       method: 'delete'
//     });
//
//     const result = response as any;
//     ElMessage.success(`权限清理成功！删除了 ${result.cleanedCount} 个多余权限`);
//
//     // 重新获取权限列表
//     await fetchPermissions();
//   } catch (error: any) {
//     console.error('权限清理失败:', error);
//     ElMessage.error(error.message || '权限清理失败');
//   }
// }

// 清空搜索和筛选
function clearPermissionFilters() {
  permissionSearchKeyword.value = '';
  permissionTypeFilter.value = '';
}

// 生命周期
onMounted(() => {
  fetchTableData();
  fetchPermissions();
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-medium">角色管理</span>
          <ElButton v-permission="'role:create'" type="primary" @click="handleCreate">
            <template #icon>
              <icon-ic-round-plus class="text-icon" />
            </template>
            新增角色
          </ElButton>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="mb-16px">
        <ElForm :model="searchParams" inline>
          <ElFormItem label="关键词">
            <ElInput
              v-model="searchParams.keyword"
              placeholder="角色名称/代码"
              clearable
              style="width: 200px"
              @keyup.enter="handleSearch"
            />
          </ElFormItem>
          <ElFormItem label="状态">
            <ElSelect v-model="searchParams.status" placeholder="请选择状态" clearable style="width: 120px">
              <ElOption label="启用" :value="1" />
              <ElOption label="禁用" :value="0" />
            </ElSelect>
          </ElFormItem>
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">搜索</ElButton>
            <ElButton @click="handleReset">重置</ElButton>
          </ElFormItem>
        </ElForm>
      </div>

      <!-- 数据表格 -->
      <ElTable v-loading="loading" :data="tableData" style="width: 100%" max-height="70vh">
        <ElTableColumn prop="role_code" label="角色代码" width="120" />
        <ElTableColumn prop="role_name" label="角色名称" width="150">
          <template #default="{ row }">
            <div class="flex items-center gap-8px">
              <ElTag size="small" :type="getRoleType(row.role_code)">
                {{ row.role_name }}
              </ElTag>
              <ElTag v-if="row.is_system" size="small" type="info">系统</ElTag>
            </div>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="role_description" label="描述" show-overflow-tooltip />
        <ElTableColumn prop="role_level" label="级别" width="80" />
        <ElTableColumn prop="user_count" label="用户数" width="80" />
        <ElTableColumn prop="permission_count" label="权限数" width="80" />
        <ElTableColumn prop="status" label="状态" width="80">
          <template #default="{ row }">
            <ElTag :type="row.status === 1 ? 'success' : 'info'">
              {{ row.status === 1 ? '启用' : '禁用' }}
            </ElTag>
          </template>
        </ElTableColumn>
        <ElTableColumn prop="create_time" label="创建时间" width="160" />
        <ElTableColumn label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <div class="flex flex-wrap gap-4px">
              <ElButton v-permission="'role:edit'" size="small" type="primary" @click="handleEdit(row)">编辑</ElButton>
              <ElDropdown @command="cmd => onMoreAction(cmd, row)">
                <ElButton size="small" type="info">更多</ElButton>
                <template #dropdown>
                  <ElDropdownMenu>
                    <ElDropdownItem v-permission="'role:permission:assign'" command="permission">权限</ElDropdownItem>
                    <ElDropdownItem v-permission="'role:delete'" command="delete" v-if="!row.is_system" divided>删除</ElDropdownItem>
                  </ElDropdownMenu>
                </template>
              </ElDropdown>
            </div>
          </template>
        </ElTableColumn>
      </ElTable>

      <!-- 分页 -->
      <div class="mt-16px flex justify-center md:justify-end">
        <ElPagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </ElCard>

    <!-- 角色编辑模态框 -->
    <ElDialog
      v-model="modalVisible"
      :title="isEdit ? '编辑角色' : '新增角色'"
      width="600px"
      :close-on-click-modal="false"
    >
      <ElForm ref="formRef" :model="roleForm" :rules="formRules" label-width="100px">
        <ElRow :gutter="16">
          <ElCol :span="12">
            <ElFormItem label="角色代码" prop="role_code">
              <ElInput v-model="roleForm.role_code" placeholder="请输入角色代码" :disabled="isEdit" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="角色名称" prop="role_name">
              <ElInput v-model="roleForm.role_name" placeholder="请输入角色名称" />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElFormItem label="角色描述">
          <ElInput v-model="roleForm.role_description" type="textarea" :rows="3" placeholder="请输入角色描述" />
        </ElFormItem>

        <ElRow :gutter="16">
          <ElCol :xs="24" :sm="12" :md="8">
            <ElFormItem label="角色级别" prop="role_level">
              <ElInputNumber
                v-model="roleForm.role_level"
                :min="1"
                :max="100"
                style="width: 100%"
                placeholder="请输入角色级别"
              />
            </ElFormItem>
          </ElCol>
          <ElCol :xs="24" :sm="12" :md="8">
            <ElFormItem label="排序">
              <ElInputNumber v-model="roleForm.sort_order" :min="0" style="width: 100%" placeholder="请输入排序值" />
            </ElFormItem>
          </ElCol>
          <ElCol :xs="24" :sm="24" :md="8">
            <ElFormItem label="状态">
              <ElSelect v-model="roleForm.status" style="width: 100%" placeholder="请选择状态">
                <ElOption label="启用" :value="1" />
                <ElOption label="禁用" :value="0" />
              </ElSelect>
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElFormItem label="默认角色">
          <ElSwitch v-model="roleForm.is_default" :active-value="1" :inactive-value="0" />
          <div class="mt-4px text-12px text-gray-500">默认角色将在用户注册时自动分配</div>
        </ElFormItem>
      </ElForm>

      <template #footer>
        <div class="flex justify-end gap-8px">
          <ElButton @click="modalVisible = false">取消</ElButton>
          <ElButton type="primary" :loading="saving" @click="saveRole">
            {{ isEdit ? '更新' : '创建' }}
          </ElButton>
        </div>
      </template>
    </ElDialog>

    <!-- 删除权限分配模态框，统一使用下方权限管理抽屉 -->

    <!-- 角色权限管理模态框（小屏全屏） -->
    <ResponsiveDialog
      v-model="rolePermissionManagerVisible"
      title="角色权限管理"
      :as-drawer="true"
      mobile-mode="fullscreen"
      width="90%"
      top="5vh"
      :close-on-click-modal="false"
      :destroy-on-close="true"
    >
      <div class="space-y-16px">
        <!-- 权限操作栏 -->
        <div class="space-y-12px">
          <div class="text-14px text-gray-600">为角色分配权限，选中的权限将授予该角色下的所有用户</div>

          <!-- 权限搜索和筛选 -->
          <div class="flex items-center gap-12px">
            <ElInput
              v-model="permissionSearchKeyword"
              placeholder="搜索权限名称、描述或代码..."
              style="width: 300px"
              clearable
            >
              <template #prefix>
                <ElIcon><Search /></ElIcon>
              </template>
            </ElInput>

            <ElSelect v-model="permissionTypeFilter" placeholder="筛选权限类型" style="width: 150px" clearable>
              <ElOption value="" label="全部类型" />
              <ElOption value="menu" label="菜单权限" />
              <ElOption value="button" label="按钮权限" />
            </ElSelect>

            <ElButton size="small" @click="clearPermissionFilters">清空筛选</ElButton>

            <div class="ml-auto flex gap-8px">
              <ElButton size="small" @click="expandAllGroups">展开全部</ElButton>
              <ElButton size="small" @click="collapseAllGroups">折叠全部</ElButton>
              <ElButton size="small" @click="rolePermissions = []">清空选择</ElButton>
              <ElButton size="small" @click="rolePermissions = availablePermissions.map(p => p.permission_id)">
                全选
              </ElButton>
            </div>
          </div>
        </div>

        <!-- 权限列表 -->
        <div class="max-h-60vh overflow-y-auto border border-gray-200 rounded-6px">
          <div v-if="availablePermissions.length === 0" class="p-32px text-center text-gray-500">暂无权限数据</div>
          <div v-else class="p-16px">
            <!-- 按分组显示权限 -->
            <div v-for="group in filteredPermissionGroups" :key="group.name" class="mb-16px">
              <div
                class="permission-group-header mb-12px flex cursor-pointer items-center gap-8px rounded-4px p-8px"
                @click="toggleGroupCollapse(group.name)"
              >
                <ElIcon class="text-16px text-gray-500">
                  <component :is="isGroupCollapsed(group.name) ? 'ArrowRight' : 'ArrowDown'" />
                </ElIcon>
                <ElCheckbox
                  :model-value="isGroupSelected(group.permissions)"
                  :indeterminate="isGroupIndeterminate(group.permissions)"
                  @change="toggleGroupSelection(group.permissions, Boolean($event))"
                  @click.stop
                />
                <span class="text-16px text-gray-800 font-medium">{{ group.displayName }}</span>
                <ElTag size="small" type="info">{{ group.permissions.length }}</ElTag>
                <span class="ml-auto text-12px text-gray-500">
                  {{ isGroupCollapsed(group.name) ? '点击展开' : '点击折叠' }}
                </span>
              </div>

              <Transition name="slide-fade">
                <div v-if="!isGroupCollapsed(group.name)" class="ml-32px mt-8px">
                  <!-- 路由权限 -->
                  <div v-if="group.routePermission" class="mb-12px">
                    <div class="mb-4px text-12px text-gray-600 font-medium">📄 页面访问权限</div>
                    <div
                      class="permission-item flex items-center gap-8px border border-blue-200 rounded-4px bg-blue-50 p-8px"
                    >
                      <ElCheckbox :label="group.routePermission.permission_id" />
                      <div class="min-w-0 flex-1">
                        <div class="truncate text-14px text-blue-700 font-medium">
                          {{ group.routePermission.permission_name }}
                        </div>
                        <div class="truncate text-12px text-blue-500">
                          {{ group.routePermission.permission_description }}
                        </div>
                      </div>
                      <ElTag size="small" type="primary">页面权限</ElTag>
                    </div>
                  </div>

                  <!-- 按钮权限 -->
                  <div v-if="group.buttonPermissions.length > 0">
                    <div class="mb-4px text-12px text-gray-600 font-medium">🔘 功能操作权限</div>
                    <div class="grid grid-cols-1 gap-8px md:grid-cols-2">
                      <div
                        v-for="permission in group.buttonPermissions"
                        :key="permission.permission_id"
                        class="permission-item flex items-center gap-8px rounded-4px p-8px"
                      >
                        <ElCheckbox :label="permission.permission_id" />
                        <div class="min-w-0 flex-1">
                          <div class="truncate text-14px font-medium">{{ permission.permission_name }}</div>
                          <div class="truncate text-12px text-gray-500">
                            {{ permission.permission_description || permission.permission_code }}
                          </div>
                        </div>
                        <ElTag size="small" :type="getPermissionTypeTag(permission.permission_type)">
                          {{ permission.permission_type }}
                        </ElTag>
                      </div>
                    </div>
                  </div>


                </div>
              </Transition>
            </div>
          </div>
        </div>

        <!-- 权限统计 -->
        <div class="flex items-center justify-between rounded-6px bg-gray-50 p-12px">
          <div class="flex items-center gap-16px text-14px text-gray-600">
            <span>已选择 {{ rolePermissions.length }} / {{ availablePermissions.length }} 个权限</span>
            <span v-if="permissionSearchKeyword || permissionTypeFilter">
              当前显示
              {{ filteredPermissionGroups.reduce((total, group) => total + group.permissions.length, 0) }} 个权限
            </span>
            <span>共 {{ filteredPermissionGroups.length }} 个分组</span>
          </div>
          <div class="flex gap-8px">
            <ElButton size="small" type="info" @click="showCopyPermissionDialog">从其他角色复制</ElButton>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end gap-8px">
          <ElButton @click="rolePermissionManagerVisible = false">取消</ElButton>
          <ElButton type="primary" :loading="permissionSaving" @click="handleSavePermissions">保存权限</ElButton>
        </div>
      </template>
    </ResponsiveDialog>
  </div>
</template>

<style scoped>
/* 权限弹窗样式 */
:deep(.permission-dialog) {
  .el-dialog {
    margin: 5vh auto;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
  }

  .el-dialog__body {
    flex: 1;
    overflow: hidden;
    padding: 20px;
  }

  .el-dialog__footer {
    flex-shrink: 0;
    padding: 15px 20px;
    border-top: 1px solid #e4e7ed;
  }
}

/* 权限列表高度适配 */
.max-h-60vh {
  max-height: 60vh;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .flex.flex-col.gap-16px.p-16px {
    padding: 12px;
    gap: 12px;
  }

  :deep(.permission-dialog) {
    .el-dialog {
      margin: 2vh auto;
      max-height: 96vh;
      width: 95% !important;
    }
  }

  .max-h-60vh {
    max-height: 50vh;
  }
}

/* 折叠动画 */
.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateY(-10px);
  opacity: 0;
}

/* 权限分组样式优化 */
.permission-group-header {
  transition: background-color 0.2s ease;
}

.permission-group-header:hover {
  background-color: #f5f7fa;
}

/* 权限项样式优化 */
.permission-item {
  transition: all 0.2s ease;
  border: 1px solid #e4e7ed;
}

.permission-item:hover {
  border-color: #c0c4cc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 响应式网格优化 */
@media (max-width: 768px) {
  .grid.grid-cols-1.md\\:grid-cols-2 {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 768px) {
  .grid.grid-cols-1.md\\:grid-cols-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
