<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Delete, Download, Edit, Plus, Refresh } from '@element-plus/icons-vue';
import {
  DATABASE_STATUS_TEXT,
  DATABASE_STATUS_TYPE,
  fetchDatabaseInfo,
  fetchDropTable,
  fetchExecuteSQL,
  fetchExportTable,
  fetchOptimizeTable,
  fetchRepairTable,
  fetchTableList,
  fetchTableStructure,
  fetchTruncateTable
} from '@/service/api/database';

defineOptions({
  name: 'SystemCenterDatabase'
});

// 响应式数据
const loading = ref(false);
const activeTab = ref('overview');

// 数据库信息
const databaseInfo = ref({
  connection: {
    status: 'unknown',
    host: '',
    port: 0,
    database: '',
    charset: '',
    version: '',
    uptime: 0
  },
  size: {
    totalSize: 0,
    dataSize: 0,
    indexSize: 0,
    tableCount: 0
  },
  performance: {
    queries: 0,
    slowQueries: 0,
    connections: 0,
    maxConnections: 0
  }
});

// 表列表
const tableList = ref<any[]>([]);
const tablePagination = reactive({
  page: 1,
  pageSize: 20,
  total: 0
});

// 表搜索参数
const tableSearchParams = reactive({
  keyword: '',
  tableType: ''
});

// SQL执行
const sqlForm = reactive({
  sql: '',
  limit: 100
});
const sqlResult = ref<any>({
  columns: [],
  rows: [],
  affectedRows: 0,
  executionTime: 0
});
const sqlExecuting = ref(false);

// 加载数据库信息
async function loadDatabaseInfo() {
  try {
    const response = await fetchDatabaseInfo();
    databaseInfo.value = response;
  } catch (error) {
    console.error('获取数据库信息失败:', error);
    ElMessage.error('获取数据库信息失败');
  }
}

// 加载表列表
async function loadTableList() {
  try {
    loading.value = true;
    const response = await fetchTableList({
      page: tablePagination.page,
      pageSize: tablePagination.pageSize,
      ...tableSearchParams
    });

    tableList.value = response.list;
    tablePagination.total = response.pagination.total;
  } catch (error) {
    console.error('获取表列表失败:', error);
    ElMessage.error('获取表列表失败');
  } finally {
    loading.value = false;
  }
}

// 查看表结构
async function handleViewTableStructure(tableName: string) {
  try {
    const response = await fetchTableStructure(tableName);
    const structure = response;

    let html = `<div style="max-height: 400px; overflow-y: auto;">
      <h4>表：${tableName}</h4>
      <table style="width: 100%; border-collapse: collapse; font-size: 12px;">
        <thead>
          <tr style="background: #f5f5f5;">
            <th style="border: 1px solid #ddd; padding: 8px;">字段名</th>
            <th style="border: 1px solid #ddd; padding: 8px;">类型</th>
            <th style="border: 1px solid #ddd; padding: 8px;">允许空值</th>
            <th style="border: 1px solid #ddd; padding: 8px;">键</th>
            <th style="border: 1px solid #ddd; padding: 8px;">默认值</th>
            <th style="border: 1px solid #ddd; padding: 8px;">注释</th>
          </tr>
        </thead>
        <tbody>`;

    structure.forEach((column: any) => {
      html += `<tr>
        <td style="border: 1px solid #ddd; padding: 8px;">${column.Field}</td>
        <td style="border: 1px solid #ddd; padding: 8px;">${column.Type}</td>
        <td style="border: 1px solid #ddd; padding: 8px;">${column.Null}</td>
        <td style="border: 1px solid #ddd; padding: 8px;">${column.Key || '-'}</td>
        <td style="border: 1px solid #ddd; padding: 8px;">${column.Default || '-'}</td>
        <td style="border: 1px solid #ddd; padding: 8px;">${column.Comment || '-'}</td>
      </tr>`;
    });

    html += '</tbody></table></div>';

    ElMessageBox.alert(html, `表结构 - ${tableName}`, {
      dangerouslyUseHTMLString: true,
      customStyle: { width: '800px' }
    });
  } catch (error: any) {
    console.error('获取表结构失败:', error);
    ElMessage.error(error.message || '获取表结构失败');
  }
}

// 优化表
async function handleOptimizeTable(tableName: string) {
  try {
    await ElMessageBox.confirm(`确定要优化表 "${tableName}" 吗？`, '确认优化', {
      confirmButtonText: '确定优化',
      cancelButtonText: '取消',
      type: 'info'
    });

    await fetchOptimizeTable(tableName);
    ElMessage.success('表优化成功');
    loadTableList();
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('优化表失败:', error);
      ElMessage.error(error.message || '优化表失败');
    }
  }
}

// 修复表
async function handleRepairTable(tableName: string) {
  try {
    await ElMessageBox.confirm(`确定要修复表 "${tableName}" 吗？`, '确认修复', {
      confirmButtonText: '确定修复',
      cancelButtonText: '取消',
      type: 'warning'
    });

    await fetchRepairTable(tableName);
    ElMessage.success('表修复成功');
    loadTableList();
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('修复表失败:', error);
      ElMessage.error(error.message || '修复表失败');
    }
  }
}

// 清空表
async function handleTruncateTable(tableName: string) {
  try {
    await ElMessageBox.confirm(
      `⚠️ 警告：此操作将清空表 "${tableName}" 的所有数据，且无法恢复！\n\n确定要继续吗？`,
      '确认清空表',
      {
        confirmButtonText: '确定清空',
        cancelButtonText: '取消',
        type: 'error',
        dangerouslyUseHTMLString: true
      }
    );

    await fetchTruncateTable(tableName);
    ElMessage.success('表清空成功');
    loadTableList();
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('清空表失败:', error);
      ElMessage.error(error.message || '清空表失败');
    }
  }
}

// 删除表
async function handleDropTable(tableName: string) {
  try {
    await ElMessageBox.confirm(
      `⚠️ 危险操作：此操作将永久删除表 "${tableName}" 及其所有数据！\n\n请输入表名确认删除：`,
      '确认删除表',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'error',
        dangerouslyUseHTMLString: true
      }
    );

    // 二次确认
    const { value: confirmTableName } = await ElMessageBox.prompt(
      `请输入表名 "${tableName}" 以确认删除：`,
      '二次确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        inputPattern: new RegExp(`^${tableName}$`),
        inputErrorMessage: '表名不匹配'
      }
    );

    await fetchDropTable(tableName);
    ElMessage.success('表删除成功');
    loadTableList();
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除表失败:', error);
      ElMessage.error(error.message || '删除表失败');
    }
  }
}

// 导出表
async function handleExportTable(tableName: string) {
  try {
    const blob = await fetchExportTable(tableName);

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${tableName}_${new Date().toISOString().split('T')[0]}.sql`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    ElMessage.success('表导出成功');
  } catch (error: any) {
    console.error('导出表失败:', error);
    ElMessage.error(error.message || '导出表失败');
  }
}

// 执行SQL
async function executeSQL() {
  if (!sqlForm.sql.trim()) {
    ElMessage.warning('请输入SQL语句');
    return;
  }

  try {
    sqlExecuting.value = true;
    const response = await fetchExecuteSQL({
      sql: sqlForm.sql.trim(),
      limit: sqlForm.limit
    });

    sqlResult.value = response;
    ElMessage.success('SQL执行成功');
  } catch (error: any) {
    console.error('SQL执行失败:', error);
    ElMessage.error(error.message || 'SQL执行失败');
  } finally {
    sqlExecuting.value = false;
  }
}

// 搜索表
function searchTables() {
  tablePagination.page = 1;
  loadTableList();
}

// 重置搜索
function resetTableSearch() {
  Object.assign(tableSearchParams, {
    keyword: '',
    tableType: ''
  });
  tablePagination.page = 1;
  loadTableList();
}

// 分页处理
function handleTableSizeChange(size: number) {
  tablePagination.pageSize = size;
  tablePagination.page = 1;
  loadTableList();
}

function handleTablePageChange(page: number) {
  tablePagination.page = page;
  loadTableList();
}

// 标签切换
function handleTabChange(tabName: string | number) {
  activeTab.value = String(tabName);
  if (tabName === 'tables') {
    loadTableList();
  }
}

// 刷新数据
function refreshData() {
  loadDatabaseInfo();
  if (activeTab.value === 'tables') {
    loadTableList();
  }
}

// 生命周期
onMounted(() => {
  loadDatabaseInfo();
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <!-- 数据库概览 -->
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-12px">
            <SvgIcon icon="carbon:data-base" class="text-24px text-primary" />
            <span class="text-18px font-semibold">数据库概览</span>
          </div>
          <div class="flex items-center gap-8px">
            <ElTag :type="DATABASE_STATUS_TYPE[databaseInfo.connection.status] as any" size="large">
              {{ DATABASE_STATUS_TEXT[databaseInfo.connection.status] }}
            </ElTag>
            <ElButton type="primary" :icon="Refresh" :loading="loading" @click="refreshData">刷新</ElButton>
          </div>
        </div>
      </template>

      <div class="grid grid-cols-1 gap-16px lg:grid-cols-4 md:grid-cols-2">
        <!-- 连接信息 -->
        <div class="rounded-8px bg-blue-50 p-16px">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-12px text-gray-500">数据库</div>
              <div class="text-16px text-blue-600 font-bold">{{ databaseInfo.connection.database }}</div>
              <div class="text-10px text-gray-400">
                {{ databaseInfo.connection.host }}:{{ databaseInfo.connection.port }}
              </div>
            </div>
            <SvgIcon icon="carbon:data-base" class="text-32px text-blue-500" />
          </div>
        </div>

        <!-- 数据库大小 -->
        <div class="rounded-8px bg-green-50 p-16px">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-12px text-gray-500">总大小</div>
              <div class="text-16px text-green-600 font-bold">
                {{ (databaseInfo.size.totalSize / 1024 / 1024).toFixed(2) }}MB
              </div>
              <div class="text-10px text-gray-400">
                数据: {{ (databaseInfo.size.dataSize / 1024 / 1024).toFixed(2) }}MB
              </div>
            </div>
            <SvgIcon icon="carbon:data-volume" class="text-32px text-green-500" />
          </div>
        </div>

        <!-- 表数量 -->
        <div class="rounded-8px bg-purple-50 p-16px">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-12px text-gray-500">表数量</div>
              <div class="text-16px text-purple-600 font-bold">{{ databaseInfo.size.tableCount }}</div>
              <div class="text-10px text-gray-400">
                索引: {{ (databaseInfo.size.indexSize / 1024 / 1024).toFixed(2) }}MB
              </div>
            </div>
            <SvgIcon icon="carbon:table" class="text-32px text-purple-500" />
          </div>
        </div>

        <!-- 连接数 -->
        <div class="rounded-8px bg-orange-50 p-16px">
          <div class="flex items-center justify-between">
            <div>
              <div class="text-12px text-gray-500">连接数</div>
              <div class="text-16px text-orange-600 font-bold">{{ databaseInfo.performance.connections }}</div>
              <div class="text-10px text-gray-400">最大: {{ databaseInfo.performance.maxConnections }}</div>
            </div>
            <SvgIcon icon="carbon:network-3" class="text-32px text-orange-500" />
          </div>
        </div>
      </div>

      <!-- 数据库详细信息 -->
      <div class="grid grid-cols-1 mt-16px gap-16px md:grid-cols-2">
        <div class="space-y-8px">
          <div class="text-14px text-gray-700 font-medium">连接信息</div>
          <div class="text-12px space-y-4px">
            <div class="flex justify-between">
              <span class="text-gray-500">版本:</span>
              <span>{{ databaseInfo.connection.version }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-500">字符集:</span>
              <span>{{ databaseInfo.connection.charset }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-500">运行时间:</span>
              <span>{{ Math.floor(databaseInfo.connection.uptime / 3600) }}小时</span>
            </div>
          </div>
        </div>

        <div class="space-y-8px">
          <div class="text-14px text-gray-700 font-medium">性能统计</div>
          <div class="text-12px space-y-4px">
            <div class="flex justify-between">
              <span class="text-gray-500">查询总数:</span>
              <span>{{ databaseInfo.performance.queries.toLocaleString() }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-500">慢查询:</span>
              <span class="text-red-600">{{ databaseInfo.performance.slowQueries }}</span>
            </div>
          </div>
        </div>
      </div>
    </ElCard>

    <!-- 数据库管理 -->
    <ElCard>
      <template #header>
        <div class="flex items-center gap-12px">
          <SvgIcon icon="carbon:data-table" class="text-24px text-primary" />
          <span class="text-18px font-semibold">数据库管理</span>
        </div>
      </template>

      <ElTabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- 表管理 -->
        <ElTabPane label="表管理" name="tables">
          <div class="space-y-16px">
            <!-- 搜索筛选 -->
            <div class="flex flex-wrap gap-16px">
              <ElInput v-model="tableSearchParams.keyword" placeholder="搜索表名" style="width: 200px" clearable />
              <ElSelect v-model="tableSearchParams.tableType" placeholder="表类型" style="width: 120px" clearable>
                <ElOption label="基础表" value="BASE TABLE" />
                <ElOption label="视图" value="VIEW" />
                <ElOption label="系统表" value="SYSTEM TABLE" />
              </ElSelect>
              <ElButton type="primary" @click="searchTables">搜索</ElButton>
              <ElButton @click="resetTableSearch">重置</ElButton>
            </div>

            <!-- 表列表 -->
            <ElTable :data="tableList" :loading="loading" stripe border height="500">
              <ElTableColumn prop="tableName" label="表名" min-width="200" show-overflow-tooltip />
              <ElTableColumn prop="tableType" label="类型" width="100">
                <template #default="{ row }">
                  <ElTag size="small" :type="row.tableType === 'BASE TABLE' ? 'primary' : 'info'">
                    {{ row.tableType === 'BASE TABLE' ? '表' : row.tableType === 'VIEW' ? '视图' : '其他' }}
                  </ElTag>
                </template>
              </ElTableColumn>
              <ElTableColumn prop="tableRows" label="行数" width="100">
                <template #default="{ row }">
                  {{ row.tableRows ? row.tableRows.toLocaleString() : '-' }}
                </template>
              </ElTableColumn>
              <ElTableColumn prop="dataLength" label="数据大小" width="100">
                <template #default="{ row }">
                  {{ row.dataLength ? (row.dataLength / 1024 / 1024).toFixed(2) + 'MB' : '-' }}
                </template>
              </ElTableColumn>
              <ElTableColumn prop="indexLength" label="索引大小" width="100">
                <template #default="{ row }">
                  {{ row.indexLength ? (row.indexLength / 1024 / 1024).toFixed(2) + 'MB' : '-' }}
                </template>
              </ElTableColumn>
              <ElTableColumn prop="tableComment" label="注释" min-width="150" show-overflow-tooltip />
              <ElTableColumn prop="createTime" label="创建时间" width="160" />
              <ElTableColumn label="操作" width="300" fixed="right">
                <template #default="{ row }">
                  <div class="flex flex-wrap gap-4px">
                    <ElButton size="small" type="primary" @click="handleViewTableStructure(row.tableName)">
                      结构
                    </ElButton>
                    <ElButton size="small" type="success" @click="handleOptimizeTable(row.tableName)">优化</ElButton>
                    <ElButton size="small" type="warning" @click="handleRepairTable(row.tableName)">修复</ElButton>
                    <ElButton size="small" type="info" @click="handleExportTable(row.tableName)">导出</ElButton>
                    <ElButton size="small" type="danger" @click="handleTruncateTable(row.tableName)">清空</ElButton>
                    <ElButton size="small" type="danger" @click="handleDropTable(row.tableName)">删除</ElButton>
                  </div>
                </template>
              </ElTableColumn>
            </ElTable>

            <!-- 分页 -->
            <div class="flex justify-center">
              <ElPagination
                v-model:current-page="tablePagination.page"
                v-model:page-size="tablePagination.pageSize"
                :page-sizes="[10, 20, 50, 100]"
                :total="tablePagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleTableSizeChange"
                @current-change="handleTablePageChange"
              />
            </div>
          </div>
        </ElTabPane>

        <!-- SQL执行器 -->
        <ElTabPane label="SQL执行器" name="sql">
          <div class="space-y-16px">
            <!-- SQL输入 -->
            <div class="space-y-8px">
              <div class="flex items-center justify-between">
                <div class="text-14px font-medium">SQL语句</div>
                <div class="flex items-center gap-8px">
                  <span class="text-12px text-gray-500">结果限制:</span>
                  <ElInputNumber v-model="sqlForm.limit" :min="1" :max="1000" size="small" style="width: 80px" />
                  <ElButton type="primary" :loading="sqlExecuting" @click="executeSQL">
                    {{ sqlExecuting ? '执行中...' : '执行SQL' }}
                  </ElButton>
                </div>
              </div>

              <ElInput
                v-model="sqlForm.sql"
                type="textarea"
                :rows="8"
                placeholder="请输入SQL语句..."
                :disabled="sqlExecuting"
              />

              <ElAlert title="SQL执行提示" type="warning" :closable="false">
                <template #default>
                  <div class="text-12px space-y-2px">
                    <p>• 请谨慎执行修改数据的SQL语句（UPDATE、DELETE、DROP等）</p>
                    <p>• 建议在执行前先备份相关数据</p>
                    <p>• 查询结果最多显示 {{ sqlForm.limit }} 条记录</p>
                  </div>
                </template>
              </ElAlert>
            </div>

            <!-- SQL执行结果 -->
            <div v-if="sqlResult.columns.length > 0 || sqlResult.affectedRows > 0" class="space-y-8px">
              <div class="flex items-center justify-between">
                <div class="text-14px font-medium">执行结果</div>
                <div class="text-12px text-gray-500">
                  执行时间: {{ sqlResult.executionTime }}ms
                  <span v-if="sqlResult.affectedRows > 0">| 影响行数: {{ sqlResult.affectedRows }}</span>
                </div>
              </div>

              <!-- 查询结果表格 -->
              <div v-if="sqlResult.columns.length > 0">
                <ElTable :data="sqlResult.rows" stripe border max-height="400" style="width: 100%">
                  <ElTableColumn
                    v-for="column in sqlResult.columns"
                    :key="column"
                    :prop="column"
                    :label="column"
                    min-width="120"
                    show-overflow-tooltip
                  >
                    <template #default="{ row }">
                      <span class="text-12px">{{ row[column] }}</span>
                    </template>
                  </ElTableColumn>
                </ElTable>

                <div class="mt-8px text-center text-12px text-gray-500">共 {{ sqlResult.rows.length }} 条记录</div>
              </div>

              <!-- 非查询结果 -->
              <div v-else-if="sqlResult.affectedRows > 0" class="py-20px text-center">
                <SvgIcon icon="carbon:checkmark" class="mb-8px text-32px text-green-500" />
                <div class="text-16px text-green-600">SQL执行成功</div>
                <div class="text-12px text-gray-500">影响了 {{ sqlResult.affectedRows }} 行数据</div>
              </div>
            </div>
          </div>
        </ElTabPane>
      </ElTabs>
    </ElCard>
  </div>
</template>

<style scoped>
.flex-col {
  display: flex;
  flex-direction: column;
}

.gap-16px {
  gap: 16px;
}

.space-y-16px > * + * {
  margin-top: 16px;
}

.space-y-8px > * + * {
  margin-top: 8px;
}

.space-y-4px > * + * {
  margin-top: 4px;
}

.space-y-2px > * + * {
  margin-top: 2px;
}

.gap-4px {
  gap: 4px;
}

.flex-wrap {
  flex-wrap: wrap;
}
</style>
