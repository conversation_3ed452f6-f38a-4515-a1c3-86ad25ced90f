<script setup lang="ts">
import { computed, defineEmits, defineProps, reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { Check, Close, CopyDocument, Download, Plus, QuestionFilled, View } from '@element-plus/icons-vue';

// Props
const props = defineProps<{
  interfaceType?: string;
  initialMapping?: any[];
  interfaceData?: any;
}>();

// Emits
const emit = defineEmits<{
  change: [mapping: any[]];
  preview: [config: any];
  save: [data: any];
  cancel: [];
}>();

// 响应式数据
const fieldMappings = ref<any[]>([]);
const showPreview = ref(false);
const showCustomFieldDialog = ref(false);
const saving = ref(false);

// 自定义字段表单
const customFieldForm = reactive({
  name: '',
  description: '',
  required: false,
  example: ''
});

const customFieldRules = {
  name: [
    { required: true, message: '请输入字段名称', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z_][a-zA-Z0-9_]*$/,
      message: '字段名只能包含字母、数字和下划线，且以字母或下划线开头',
      trigger: 'blur'
    }
  ],
  description: [{ required: true, message: '请输入字段描述', trigger: 'blur' }]
};

// 标准字段指南
const standardFieldsGuide = [
  { name: 'username', description: 'API用户名，用于货源认证' },
  { name: 'password', description: 'API密码，用于货源认证' },
  { name: 'student_username', description: '学生登录用户名' },
  { name: 'student_password', description: '学生登录密码' },
  { name: 'school', description: '学生所在学校名称' },
  { name: 'platform', description: '学习平台类型' },
  { name: 'course_id', description: '课程唯一标识' },
  { name: 'course_name', description: '课程名称' },
  { name: 'upstream_order_id', description: '上游订单ID' }
];

// 29平台字段对照表 - 基于实际API文档
const platform29Mapping = [
  { standard: 'api_uid', platform29: 'uid', note: '29平台API用户名' },
  { standard: 'api_key', platform29: 'key', note: '29平台API密钥' },
  { standard: 'platform', platform29: 'platform', note: '平台ID（必传）' },
  { standard: 'student_username', platform29: 'user', note: '学生账号' },
  { standard: 'student_password', platform29: 'pass', note: '学生密码' },
  { standard: 'school', platform29: 'school', note: '学生学校' },
  { standard: 'course_id', platform29: 'kcid', note: '课程ID' },
  { standard: 'course_name', platform29: 'kcname', note: '课程名字' },
  { standard: 'order_id', platform29: 'id', note: '订单ID（补刷用）' },
  { standard: 'upstream_order_id', platform29: 'yid', note: '订单ID（查询/改密用）' },
  { standard: 'query_username', platform29: 'username', note: '订单账号（查询用）' },
  { standard: 'query_school', platform29: 'school', note: '订单学校（查询用）' },
  { standard: 'change_order_id', platform29: 'yid', note: '订单ID（改密用）' },
  { standard: 'new_password', platform29: 'pwd', note: '新密码' }
];

// 计算属性
const generatedRequestTemplate = computed(() => {
  const template: Record<string, string> = {};
  fieldMappings.value.forEach(field => {
    if (field.providerField) {
      if (field.standardField.startsWith('auth.')) {
        template[field.providerField] = `\${${field.standardField}}`;
      } else {
        template[field.providerField] = `\${data.${field.standardField}}`;
      }
    }
  });
  return template;
});

const generatedFieldMapping = computed(() => {
  const mapping: Record<string, any> = {};
  fieldMappings.value.forEach(field => {
    if (field.providerField) {
      mapping[field.standardField] = {
        provider_field: field.providerField,
        required: field.required,
        transform: field.transform || undefined,
        default_value: field.defaultValue || undefined
      };
    }
  });
  return mapping;
});

// 方法
const initializeMapping = () => {
  // 根据接口类型初始化标准字段 - 基于29平台实际API
  const baseFields = [
    {
      standardField: 'api_uid',
      description: '29平台API用户名',
      required: true,
      example: '5',
      isCustom: false,
      category: 'auth'
    },
    {
      standardField: 'api_key',
      description: '29平台API密钥',
      required: true,
      example: 'YsIYr7lZ75plP8Y5',
      isCustom: false,
      category: 'auth'
    }
  ];

  const interfaceSpecificFields: Record<string, any[]> = {
    query: [
      { standardField: 'platform', description: '平台ID', required: true, example: 'network_course', isCustom: false },
      {
        standardField: 'student_username',
        description: '学生账号',
        required: true,
        example: 'student123',
        isCustom: false
      },
      {
        standardField: 'student_password',
        description: '学生密码',
        required: true,
        example: 'password123',
        isCustom: false
      },
      { standardField: 'school', description: '学生学校', required: true, example: '北京大学', isCustom: false },
      {
        standardField: 'course_id',
        description: '课程ID（可选）',
        required: false,
        example: 'course_001',
        isCustom: false
      }
    ],
    order: [
      { standardField: 'platform', description: '平台ID', required: true, example: 'network_course', isCustom: false },
      {
        standardField: 'student_username',
        description: '学生账号',
        required: true,
        example: 'student123',
        isCustom: false
      },
      {
        standardField: 'student_password',
        description: '学生密码',
        required: true,
        example: 'password123',
        isCustom: false
      },
      { standardField: 'school', description: '学生学校', required: true, example: '北京大学', isCustom: false },
      { standardField: 'course_name', description: '课程名字', required: true, example: '高等数学', isCustom: false },
      { standardField: 'course_id', description: '课程ID', required: true, example: 'course_001', isCustom: false }
    ],
    sync: [
      {
        standardField: 'upstream_order_id',
        description: '订单ID',
        required: false,
        example: 'order_123',
        isCustom: false
      },
      {
        standardField: 'query_username',
        description: '订单账号',
        required: false,
        example: 'student123',
        isCustom: false
      },
      { standardField: 'query_school', description: '订单学校', required: false, example: '北京大学', isCustom: false }
    ],
    refill: [
      { standardField: 'order_id', description: '订单ID', required: true, example: 'order_123', isCustom: false }
    ],
    change_password: [
      {
        standardField: 'change_order_id',
        description: '订单ID',
        required: true,
        example: 'order_123',
        isCustom: false
      },
      { standardField: 'new_password', description: '新密码', required: true, example: 'newpass123', isCustom: false }
    ],
    get_courses: []
  };

  fieldMappings.value = [...baseFields, ...(interfaceSpecificFields[props.interfaceType || ''] || [])].map(field => ({
    ...field,
    providerField: '',
    transform: '',
    defaultValue: ''
  }));

  // 如果有初始映射，应用它
  if (props.initialMapping) {
    applyInitialMapping();
  }
};

const applyInitialMapping = () => {
  if (!props.initialMapping) return;

  props.initialMapping.forEach(mapping => {
    const field = fieldMappings.value.find(f => f.standardField === mapping.standardField);
    if (field) {
      field.providerField = mapping.providerField;
      field.transform = mapping.transform;
      field.defaultValue = mapping.defaultValue;
    }
  });
};

const addCustomField = () => {
  showCustomFieldDialog.value = true;
};

const confirmAddCustomField = () => {
  const newField = {
    standardField: customFieldForm.name,
    description: customFieldForm.description,
    required: customFieldForm.required,
    example: customFieldForm.example,
    isCustom: true,
    providerField: '',
    transform: '',
    defaultValue: ''
  };

  fieldMappings.value.push(newField);
  showCustomFieldDialog.value = false;

  // 重置表单
  Object.assign(customFieldForm, {
    name: '',
    description: '',
    required: false,
    example: ''
  });

  emitChange();
};

const removeCustomField = (index: number) => {
  fieldMappings.value.splice(index, 1);
  emitChange();
};

const loadTemplate = () => {
  // 加载29平台模板 - 基于实际API文档
  const template29Mapping: Record<string, string> = {
    api_uid: 'uid',
    api_key: 'key',
    platform: 'platform',
    student_username: 'user',
    student_password: 'pass',
    school: 'school',
    course_id: 'kcid',
    course_name: 'kcname',
    order_id: 'id',
    upstream_order_id: 'yid',
    query_username: 'username',
    query_school: 'school',
    change_order_id: 'yid',
    new_password: 'pwd'
  };

  fieldMappings.value.forEach(field => {
    if (template29Mapping[field.standardField]) {
      field.providerField = template29Mapping[field.standardField];
    }
  });

  ElMessage.success('已加载29平台字段模板');
  emitChange();
};

const validateMapping = (index: number) => {
  const field = fieldMappings.value[index];
  if (field && field.required && !field.providerField) {
    ElMessage.warning(`${field.standardField} 是必填字段，请配置货源字段名`);
  }
  emitChange();
};

const emitChange = () => {
  emit('change', fieldMappings.value);
  showPreview.value = true;
  emit('preview', {
    requestTemplate: generatedRequestTemplate.value,
    fieldMapping: generatedFieldMapping.value
  });
};

const copyConfig = () => {
  const config = {
    requestTemplate: generatedRequestTemplate.value,
    fieldMapping: generatedFieldMapping.value
  };

  navigator.clipboard.writeText(JSON.stringify(config, null, 2)).then(() => {
    ElMessage.success('配置已复制到剪贴板');
  });
};

// 保存配置
const handleSave = async () => {
  try {
    saving.value = true;

    // 验证必填字段
    const missingFields = fieldMappings.value.filter(field => field.required && !field.providerField);
    if (missingFields.length > 0) {
      ElMessage.error(`请配置以下必填字段: ${missingFields.map(f => f.standardField).join(', ')}`);
      return;
    }

    // 构建接口数据
    const interfaceData = {
      ...props.interfaceData,
      request_mapping: generatedRequestTemplate.value,
      response_mapping: {
        field_mapping: generatedFieldMapping.value,
        message_field: 'msg',
        success_condition: {
          field: 'code',
          value: 0,
          condition: 'gte'
        }
      }
    };

    emit('save', interfaceData);
  } catch (error: any) {
    ElMessage.error(`保存失败: ${error.message}`);
  } finally {
    saving.value = false;
  }
};

// 取消编辑
const handleCancel = () => {
  emit('cancel');
};

// 初始化
initializeMapping();
</script>

<template>
  <div class="field-mapping-manager">
    <ElCard class="mapping-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>字段映射配置</h3>
          <div class="header-actions">
            <ElButton type="primary" size="small" @click="addCustomField">
              <ElIcon><Plus /></ElIcon>
              添加自定义字段
            </ElButton>
            <ElButton size="small" @click="loadTemplate">
              <ElIcon><Download /></ElIcon>
              加载模板
            </ElButton>
          </div>
        </div>
      </template>

      <!-- 字段映射表格 -->
      <ElTable :data="fieldMappings" border class="mapping-table">
        <ElTableColumn label="项目标准字段" width="200">
          <template #default="{ row }">
            <div class="standard-field">
              <div class="field-info">
                <span class="field-name">{{ row.standardField }}</span>
                <ElSwitch
                  v-model="row.required"
                  size="small"
                  active-text="必填"
                  inactive-text="可选"
                  @change="validateMapping(row)"
                />
              </div>
              <div class="field-description">{{ row.description }}</div>
            </div>
          </template>
        </ElTableColumn>

        <ElTableColumn label="货源字段名" width="180">
          <template #default="{ row, $index }">
            <ElInput
              v-model="row.providerField"
              :placeholder="row.example || '请输入货源字段名'"
              size="small"
              @change="validateMapping($index)"
            />
          </template>
        </ElTableColumn>

        <ElTableColumn label="数据转换" width="150">
          <template #default="{ row }">
            <ElSelect v-model="row.transform" placeholder="选择转换" size="small" clearable>
              <ElOption label="无转换" value="" />
              <ElOption label="转字符串" value="toString" />
              <ElOption label="转数字" value="toNumber" />
              <ElOption label="MD5加密" value="md5" />
              <ElOption label="Base64编码" value="base64" />
              <ElOption label="URL编码" value="urlEncode" />
              <ElOption label="时间戳" value="timestamp" />
              <ElOption label="JSON字符串" value="jsonStringify" />
            </ElSelect>
          </template>
        </ElTableColumn>

        <ElTableColumn label="默认值" width="120">
          <template #default="{ row }">
            <ElInput v-model="row.defaultValue" placeholder="可选" size="small" />
          </template>
        </ElTableColumn>

        <ElTableColumn label="示例值" width="150">
          <template #default="{ row }">
            <ElText size="small" type="info">{{ row.example }}</ElText>
          </template>
        </ElTableColumn>

        <ElTableColumn label="操作" width="100">
          <template #default="{ row, $index }">
            <ElButton v-if="row.isCustom" type="danger" size="small" @click="removeCustomField($index)">删除</ElButton>
            <ElText v-else size="small" type="info">系统字段</ElText>
          </template>
        </ElTableColumn>
      </ElTable>

      <!-- 字段映射说明 -->
      <ElCard class="mapping-guide" shadow="never">
        <template #header>
          <ElIcon><QuestionFilled /></ElIcon>
          字段映射说明
        </template>

        <ElRow :gutter="20">
          <ElCol :span="12">
            <h4>项目标准字段说明</h4>
            <ElDescriptions :column="1" size="small" border>
              <ElDescriptionsItem v-for="field in standardFieldsGuide" :key="field.name" :label="field.name">
                {{ field.description }}
              </ElDescriptionsItem>
            </ElDescriptions>
          </ElCol>

          <ElCol :span="12">
            <h4>29平台字段对照表</h4>
            <ElTable :data="platform29Mapping" size="small" border>
              <ElTableColumn prop="standard" label="标准字段" width="120" />
              <ElTableColumn prop="platform29" label="29平台字段" width="120" />
              <ElTableColumn prop="note" label="说明" />
            </ElTable>
          </ElCol>
        </ElRow>
      </ElCard>

      <!-- 预览生成的映射配置 -->
      <ElCard v-if="showPreview" class="preview-card" shadow="never">
        <template #header>
          <div class="preview-header">
            <ElIcon><View /></ElIcon>
            生成的映射配置预览
            <ElButton type="text" @click="copyConfig">
              <ElIcon><CopyDocument /></ElIcon>
              复制配置
            </ElButton>
          </div>
        </template>

        <ElTabs>
          <ElTabPane label="请求模板" name="request">
            <pre class="json-preview">{{ JSON.stringify(generatedRequestTemplate, null, 2) }}</pre>
          </ElTabPane>
          <ElTabPane label="字段映射" name="mapping">
            <pre class="json-preview">{{ JSON.stringify(generatedFieldMapping, null, 2) }}</pre>
          </ElTabPane>
        </ElTabs>
      </ElCard>
    </ElCard>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <ElButton type="primary" :loading="saving" @click="handleSave">
        <ElIcon><Check /></ElIcon>
        保存配置
      </ElButton>
      <ElButton @click="handleCancel">
        <ElIcon><Close /></ElIcon>
        取消
      </ElButton>
    </div>

    <!-- 自定义字段对话框 -->
    <ElDialog v-model="showCustomFieldDialog" title="添加自定义字段" width="500px">
      <ElForm ref="customFieldFormRef" :model="customFieldForm" :rules="customFieldRules" label-width="100px">
        <ElFormItem label="字段名称" prop="name">
          <ElInput v-model="customFieldForm.name" placeholder="如: custom_param" />
        </ElFormItem>
        <ElFormItem label="字段描述" prop="description">
          <ElInput v-model="customFieldForm.description" placeholder="字段用途说明" />
        </ElFormItem>
        <ElFormItem label="是否必填" prop="required">
          <ElSwitch v-model="customFieldForm.required" />
        </ElFormItem>
        <ElFormItem label="示例值" prop="example">
          <ElInput v-model="customFieldForm.example" placeholder="示例值" />
        </ElFormItem>
      </ElForm>

      <template #footer>
        <ElButton @click="showCustomFieldDialog = false">取消</ElButton>
        <ElButton type="primary" @click="confirmAddCustomField">确定</ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<style scoped>
.field-mapping-manager {
  padding: 20px;
}

.mapping-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.mapping-table {
  margin-bottom: 20px;
}

.standard-field .field-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.standard-field .field-name {
  font-weight: 500;
  color: #303133;
}

.standard-field .field-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.3;
}

.mapping-guide {
  margin-bottom: 20px;
  background: #fafafa;
}

.mapping-guide h4 {
  margin: 0 0 12px 0;
  color: #303133;
}

.preview-card {
  background: #f8f9fa;
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #303133;
  font-weight: 500;
}

.json-preview {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  max-height: 400px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.action-buttons {
  margin-top: 20px;
  text-align: center;
  padding: 20px;
  border-top: 1px solid #ebeef5;
}

.action-buttons .el-button {
  margin: 0 10px;
}
</style>
