<script setup lang="ts">
import { computed, defineEmits, defineProps, reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { Check, Close, Setting } from '@element-plus/icons-vue';
import TemplateManager from './template-manager.vue';

// 定义props和emits
const props = defineProps<{
  providerId: number;
}>();

const emit = defineEmits<{
  save: [config: any];
  cancel: [];
}>();

// 响应式数据
const currentStep = ref(0);
const selectedTemplate = ref('');
const activeInterface = ref('query');
const testing = ref(false);
const testResults = ref<any[]>([]);
const showTemplateManager = ref(false);

// 模板数据
const templates = ref([
  {
    id: '29pt',
    name: '29平台模板',
    description: '适用于29平台的标准配置',
    features: ['查课接口', '下单接口', '补刷接口', '同步接口'],
    interfaces: [
      { type: 'query', name: '查课接口' },
      { type: 'order', name: '下单接口' },
      { type: 'refill', name: '补刷接口' },
      { type: 'sync', name: '同步接口' }
    ]
  },
  {
    id: 'custom',
    name: '自定义模板',
    description: '自定义配置所有参数',
    features: ['灵活配置', '自定义字段'],
    interfaces: [
      { type: 'query', name: '查课接口' },
      { type: 'order', name: '下单接口' }
    ]
  }
]);

// 接口配置
const interfaceConfigs = reactive<any>({
  query: {
    endpoint: '/api.php?act=get',
    method: 'POST',
    timeout: 30,
    description: '查询学生课程信息',
    headers: '{"Content-Type": "application/x-www-form-urlencoded"}',
    responseHandler: '',
    retryCount: 3,
    retryDelay: 5,
    enableCache: false,
    fields: [
      {
        label: '用户ID',
        standardField: 'api_uid',
        providerField: 'uid',
        fieldType: 'string',
        required: true,
        defaultValue: '',
        validation: '非空'
      },
      {
        label: '密钥',
        standardField: 'api_key',
        providerField: 'key',
        fieldType: 'string',
        required: true,
        defaultValue: '',
        validation: '非空'
      },
      {
        label: '学生账号',
        standardField: 'username',
        providerField: 'user',
        fieldType: 'string',
        required: true,
        defaultValue: '',
        validation: '非空'
      },
      {
        label: '学生密码',
        standardField: 'password',
        providerField: 'pass',
        fieldType: 'string',
        required: true,
        defaultValue: '',
        validation: '非空'
      },
      {
        label: '学校名称',
        standardField: 'school',
        providerField: 'school',
        fieldType: 'string',
        required: true,
        defaultValue: '',
        validation: '非空'
      },
      {
        label: '平台标识',
        standardField: 'platform',
        providerField: 'platform',
        fieldType: 'string',
        required: true,
        defaultValue: '',
        validation: '非空'
      }
    ]
  },
  order: {
    endpoint: '/api.php?act=add',
    method: 'POST',
    timeout: 60,
    description: '提交课程订单',
    headers: '{"Content-Type": "application/x-www-form-urlencoded"}',
    responseHandler: '',
    retryCount: 3,
    retryDelay: 5,
    enableCache: false,
    fields: [
      {
        label: '用户ID',
        standardField: 'api_uid',
        providerField: 'uid',
        fieldType: 'string',
        required: true,
        defaultValue: '',
        validation: '非空'
      },
      {
        label: '密钥',
        standardField: 'api_key',
        providerField: 'key',
        fieldType: 'string',
        required: true,
        defaultValue: '',
        validation: '非空'
      },
      {
        label: '学生账号',
        standardField: 'username',
        providerField: 'user',
        fieldType: 'string',
        required: true,
        defaultValue: '',
        validation: '非空'
      },
      {
        label: '学生密码',
        standardField: 'password',
        providerField: 'pass',
        fieldType: 'string',
        required: true,
        defaultValue: '',
        validation: '非空'
      },
      {
        label: '学校名称',
        standardField: 'school',
        providerField: 'school',
        fieldType: 'string',
        required: true,
        defaultValue: '',
        validation: '非空'
      },
      {
        label: '平台标识',
        standardField: 'platform',
        providerField: 'platform',
        fieldType: 'string',
        required: true,
        defaultValue: '',
        validation: '非空'
      },
      {
        label: '课程名称',
        standardField: 'course_name',
        providerField: 'kcname',
        fieldType: 'string',
        required: true,
        defaultValue: '',
        validation: '非空'
      },
      {
        label: '课程ID',
        standardField: 'course_id',
        providerField: 'kcid',
        fieldType: 'string',
        required: true,
        defaultValue: '',
        validation: '非空'
      }
    ]
  },
  refill: {
    endpoint: '/api.php?act=budan',
    method: 'POST',
    timeout: 30,
    description: '补刷订单',
    headers: '{"Content-Type": "application/x-www-form-urlencoded"}',
    responseHandler: '',
    retryCount: 3,
    retryDelay: 5,
    enableCache: false,
    fields: [
      {
        label: '用户ID',
        standardField: 'api_uid',
        providerField: 'uid',
        fieldType: 'string',
        required: true,
        defaultValue: '',
        validation: '非空'
      },
      {
        label: '密钥',
        standardField: 'api_key',
        providerField: 'key',
        fieldType: 'string',
        required: true,
        defaultValue: '',
        validation: '非空'
      },
      {
        label: '订单ID',
        standardField: 'upstream_order_id',
        providerField: 'id',
        fieldType: 'string',
        required: true,
        defaultValue: '',
        validation: '非空'
      }
    ]
  },
  sync: {
    endpoint: '/api.php?act=chadanoid',
    method: 'POST',
    timeout: 30,
    description: '同步订单状态',
    headers: '{"Content-Type": "application/x-www-form-urlencoded"}',
    responseHandler: '',
    retryCount: 3,
    retryDelay: 5,
    enableCache: false,
    fields: [
      {
        label: '用户ID',
        standardField: 'api_uid',
        providerField: 'uid',
        fieldType: 'string',
        required: true,
        defaultValue: '',
        validation: '非空'
      },
      {
        label: '密钥',
        standardField: 'api_key',
        providerField: 'key',
        fieldType: 'string',
        required: true,
        defaultValue: '',
        validation: '非空'
      },
      {
        label: '订单ID',
        standardField: 'upstream_order_id',
        providerField: 'yid',
        fieldType: 'string',
        required: true,
        defaultValue: '',
        validation: '非空'
      }
    ]
  }
});

// 计算属性
const selectedTemplateData = computed(() => {
  return templates.value.find(t => t.id === selectedTemplate.value);
});

const canProceed = computed(() => {
  switch (currentStep.value) {
    case 0:
      return selectedTemplate.value !== '';
    case 1:
      return true; // 可以进行基本验证
    case 2:
      return testResults.value.length > 0 && testResults.value.every(r => r.success);
    default:
      return true;
  }
});

// 方法
const selectTemplate = (templateId: string) => {
  selectedTemplate.value = templateId;
};

const nextStep = () => {
  if (currentStep.value < 3) {
    currentStep.value++;
    if (currentStep.value === 1) {
      // 设置默认的活跃接口
      activeInterface.value = selectedTemplateData.value?.interfaces[0]?.type || 'query';
    }
  }
};

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
};

const runTest = async () => {
  testing.value = true;
  testResults.value = [];

  try {
    const interfaces = selectedTemplateData.value?.interfaces || [];

    for (const iface of interfaces) {
      // 模拟测试
      await new Promise(resolve => setTimeout(resolve, 1000));

      const success = Math.random() > 0.2; // 80%成功率
      testResults.value.push({
        interface: iface.name,
        success,
        message: success ? '测试通过' : '测试失败'
      });
    }

    ElMessage.success('测试完成');

    // 如果所有测试通过，自动进入下一步
    if (testResults.value.every(r => r.success)) {
      setTimeout(() => {
        nextStep();
      }, 1000);
    }
  } catch (error: any) {
    ElMessage.error(`测试失败: ${error.message}`);
  } finally {
    testing.value = false;
  }
};

const finish = () => {
  // 生成配置数据
  const config = {
    template: selectedTemplate.value,
    interfaces: interfaceConfigs
  };

  emit('save', config);
};

const handleTemplateUpdated = () => {
  // 模板更新后，可以重新加载模板列表
  showTemplateManager.value = false;
  ElMessage.success('模板已更新');
};

const addFieldMapping = (interfaceType: string) => {
  if (!interfaceConfigs[interfaceType]) return;

  interfaceConfigs[interfaceType].fields.push({
    label: '新字段',
    standardField: '',
    providerField: '',
    fieldType: 'string',
    required: false,
    defaultValue: '',
    validation: ''
  });
};

const removeFieldMapping = (interfaceType: string, index: number) => {
  if (!interfaceConfigs[interfaceType] || interfaceConfigs[interfaceType].fields.length <= 1) return;

  interfaceConfigs[interfaceType].fields.splice(index, 1);
};
</script>

<template>
  <div class="config-wizard">
    <!-- 步骤指示器 -->
    <ElSteps :active="currentStep" align-center>
      <ElStep title="选择模板" />
      <ElStep title="配置接口" />
      <ElStep title="测试验证" />
      <ElStep title="完成" />
    </ElSteps>

    <!-- 步骤内容 -->
    <div class="wizard-content">
      <!-- 步骤1: 选择模板 -->
      <div v-if="currentStep === 0" class="step-content">
        <h3>选择配置模板</h3>
        <p>请选择适合的货源配置模板</p>

        <div class="template-actions">
          <ElButton @click="showTemplateManager = true">
            <ElIcon><Setting /></ElIcon>
            管理模板
          </ElButton>
        </div>

        <div class="template-grid">
          <div
            v-for="template in templates"
            :key="template.id"
            class="template-card"
            :class="{ active: selectedTemplate === template.id }"
            @click="selectTemplate(template.id)"
          >
            <div class="template-icon">
              <ElIcon size="32"><Setting /></ElIcon>
            </div>
            <h4>{{ template.name }}</h4>
            <p>{{ template.description }}</p>
            <div class="template-features">
              <ElTag v-for="iface in template.interfaces" :key="iface.type" size="small">
                {{ iface.name }}
              </ElTag>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤2: 配置接口 -->
      <div v-if="currentStep === 1" class="step-content">
        <h3>配置接口参数</h3>
        <p>根据选择的模板配置接口参数</p>

        <ElTabs v-model="activeInterface" type="card">
          <ElTabPane
            v-for="iface in selectedTemplateData?.interfaces"
            :key="iface.type"
            :label="iface.name"
            :name="iface.type"
          >
            <div class="interface-config">
              <ElForm :model="interfaceConfigs[iface.type]" label-width="120px">
                <ElRow :gutter="20">
                  <ElCol :span="12">
                    <ElFormItem label="接口地址" required>
                      <ElInput v-model="interfaceConfigs[iface.type].endpoint" placeholder="如：/api.php?act=get" />
                    </ElFormItem>
                  </ElCol>
                  <ElCol :span="6">
                    <ElFormItem label="请求方法">
                      <ElSelect v-model="interfaceConfigs[iface.type].method">
                        <ElOption label="GET" value="GET" />
                        <ElOption label="POST" value="POST" />
                        <ElOption label="PUT" value="PUT" />
                        <ElOption label="DELETE" value="DELETE" />
                      </ElSelect>
                    </ElFormItem>
                  </ElCol>
                  <ElCol :span="6">
                    <ElFormItem label="超时时间(秒)">
                      <ElInputNumber
                        v-model="interfaceConfigs[iface.type].timeout"
                        :min="1"
                        :max="300"
                        placeholder="30"
                      />
                    </ElFormItem>
                  </ElCol>
                </ElRow>

                <ElFormItem label="接口描述">
                  <ElInput
                    v-model="interfaceConfigs[iface.type].description"
                    type="textarea"
                    :rows="2"
                    placeholder="请输入接口功能描述"
                  />
                </ElFormItem>

                <div class="field-mappings-section">
                  <div class="section-header">
                    <h4>字段映射配置</h4>
                    <ElButton size="small" @click="addFieldMapping(iface.type)">
                      <ElIcon><Plus /></ElIcon>
                      添加字段
                    </ElButton>
                  </div>

                  <div class="field-mappings-table">
                    <ElTable :data="interfaceConfigs[iface.type].fields" border>
                      <ElTableColumn label="字段标签" width="120">
                        <template #default="{ row, $index }">
                          <ElInput v-model="row.label" placeholder="字段标签" size="small" />
                        </template>
                      </ElTableColumn>

                      <ElTableColumn label="标准字段名" width="140">
                        <template #default="{ row, $index }">
                          <ElSelect v-model="row.standardField" placeholder="选择标准字段" size="small" filterable>
                            <ElOptionGroup label="认证字段">
                              <ElOption label="用户ID" value="api_uid" />
                              <ElOption label="密钥" value="api_key" />
                              <ElOption label="访问令牌" value="token" />
                            </ElOptionGroup>
                            <ElOptionGroup label="学生信息">
                              <ElOption label="学生账号" value="username" />
                              <ElOption label="学生密码" value="password" />
                              <ElOption label="学校名称" value="school" />
                              <ElOption label="平台标识" value="platform" />
                            </ElOptionGroup>
                            <ElOptionGroup label="课程信息">
                              <ElOption label="课程ID" value="course_id" />
                              <ElOption label="课程名称" value="course_name" />
                              <ElOption label="教师姓名" value="teacher" />
                              <ElOption label="学分" value="credit" />
                            </ElOptionGroup>
                            <ElOptionGroup label="订单信息">
                              <ElOption label="上游订单ID" value="upstream_order_id" />
                              <ElOption label="新密码" value="new_password" />
                              <ElOption label="订单状态" value="status" />
                              <ElOption label="进度信息" value="progress" />
                              <ElOption label="备注信息" value="remarks" />
                            </ElOptionGroup>
                          </ElSelect>
                        </template>
                      </ElTableColumn>

                      <ElTableColumn label="货源字段名" width="140">
                        <template #default="{ row, $index }">
                          <ElInput v-model="row.providerField" placeholder="货源字段名" size="small" />
                        </template>
                      </ElTableColumn>

                      <ElTableColumn label="字段类型" width="100">
                        <template #default="{ row, $index }">
                          <ElSelect v-model="row.fieldType" placeholder="类型" size="small">
                            <ElOption label="字符串" value="string" />
                            <ElOption label="数字" value="number" />
                            <ElOption label="布尔值" value="boolean" />
                            <ElOption label="数组" value="array" />
                            <ElOption label="对象" value="object" />
                          </ElSelect>
                        </template>
                      </ElTableColumn>

                      <ElTableColumn label="必填" width="60">
                        <template #default="{ row, $index }">
                          <ElCheckbox v-model="row.required" />
                        </template>
                      </ElTableColumn>

                      <ElTableColumn label="默认值" width="120">
                        <template #default="{ row, $index }">
                          <ElInput v-model="row.defaultValue" placeholder="默认值" size="small" />
                        </template>
                      </ElTableColumn>

                      <ElTableColumn label="验证规则" width="140">
                        <template #default="{ row, $index }">
                          <ElInput v-model="row.validation" placeholder="如：非空、邮箱格式" size="small" />
                        </template>
                      </ElTableColumn>

                      <ElTableColumn label="操作" width="80">
                        <template #default="{ row, $index }">
                          <ElButton
                            size="small"
                            type="danger"
                            :disabled="interfaceConfigs[iface.type].fields.length <= 1"
                            @click="removeFieldMapping(iface.type, $index)"
                          >
                            删除
                          </ElButton>
                        </template>
                      </ElTableColumn>
                    </ElTable>
                  </div>
                </div>

                <div class="advanced-config">
                  <ElCollapse>
                    <ElCollapseItem title="高级配置" name="advanced">
                      <ElRow :gutter="20">
                        <ElCol :span="12">
                          <ElFormItem label="请求头配置">
                            <ElInput
                              v-model="interfaceConfigs[iface.type].headers"
                              type="textarea"
                              :rows="3"
                              placeholder='{"Content-Type": "application/json"}'
                            />
                          </ElFormItem>
                        </ElCol>
                        <ElCol :span="12">
                          <ElFormItem label="响应处理">
                            <ElInput
                              v-model="interfaceConfigs[iface.type].responseHandler"
                              type="textarea"
                              :rows="3"
                              placeholder="响应数据处理逻辑"
                            />
                          </ElFormItem>
                        </ElCol>
                      </ElRow>

                      <ElRow :gutter="20">
                        <ElCol :span="8">
                          <ElFormItem label="重试次数">
                            <ElInputNumber v-model="interfaceConfigs[iface.type].retryCount" :min="0" :max="10" />
                          </ElFormItem>
                        </ElCol>
                        <ElCol :span="8">
                          <ElFormItem label="重试间隔(秒)">
                            <ElInputNumber v-model="interfaceConfigs[iface.type].retryDelay" :min="1" :max="60" />
                          </ElFormItem>
                        </ElCol>
                        <ElCol :span="8">
                          <ElFormItem label="启用缓存">
                            <ElSwitch v-model="interfaceConfigs[iface.type].enableCache" />
                          </ElFormItem>
                        </ElCol>
                      </ElRow>
                    </ElCollapseItem>
                  </ElCollapse>
                </div>
              </ElForm>
            </div>
          </ElTabPane>
        </ElTabs>
      </div>

      <!-- 步骤3: 测试验证 -->
      <div v-if="currentStep === 2" class="step-content">
        <h3>测试验证</h3>
        <p>验证配置的正确性</p>

        <div class="test-section">
          <ElButton type="primary" :loading="testing" @click="runTest">开始测试</ElButton>

          <div v-if="testResults.length > 0" class="test-results">
            <h4>测试结果</h4>
            <div v-for="result in testResults" :key="result.interface" class="test-result-item">
              <ElIcon :class="result.success ? 'success' : 'error'">
                <Check v-if="result.success" />
                <Close v-else />
              </ElIcon>
              <span>{{ result.interface }}</span>
              <span class="result-message">{{ result.message }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤4: 完成 -->
      <div v-if="currentStep === 3" class="step-content">
        <h3>配置完成</h3>
        <p>恭喜！货源配置已完成</p>

        <ElResult icon="success" title="配置成功" sub-title="您的货源配置已保存并可以使用了">
          <template #extra>
            <ElButton type="primary" @click="finish">完成</ElButton>
          </template>
        </ElResult>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="wizard-actions">
      <ElButton v-if="currentStep > 0" @click="prevStep">上一步</ElButton>
      <ElButton v-if="currentStep < 3" type="primary" :disabled="!canProceed" @click="nextStep">下一步</ElButton>
    </div>

    <!-- 模板管理器对话框 -->
    <ElDialog v-model="showTemplateManager" title="模板管理" width="90%" :close-on-click-modal="false">
      <TemplateManager @template-updated="handleTemplateUpdated" />
    </ElDialog>
  </div>
</template>

<style scoped>
.config-wizard {
  padding: 20px;
}

.wizard-content {
  margin: 40px 0;
  min-height: 400px;
}

.step-content {
  text-align: center;
}

.step-content h3 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
}

.step-content p {
  margin: 0 0 32px 0;
  color: var(--el-text-color-regular);
}

.template-actions {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.template-card {
  padding: 24px;
  border: 2px solid var(--el-border-color);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  text-align: center;
}

.template-card:hover {
  border-color: var(--el-color-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.template-card.active {
  border-color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
}

.template-icon {
  margin-bottom: 16px;
  color: var(--el-color-primary);
}

.template-card h4 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
}

.template-card p {
  margin: 0 0 16px 0;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.template-features {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  justify-content: center;
}

.interface-config {
  text-align: left;
  max-width: 600px;
  margin: 0 auto;
}

.interface-config h4 {
  margin: 24px 0 16px 0;
  color: var(--el-text-color-primary);
}

.field-mappings-section {
  margin-top: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h4 {
  margin: 0;
  color: var(--el-text-color-primary);
}

.field-mappings-table {
  margin-bottom: 16px;
}

.field-mappings-table .el-table {
  font-size: 13px;
}

.advanced-config {
  margin-top: 24px;
  border-top: 1px solid var(--el-border-color-light);
  padding-top: 16px;
}

.test-section {
  max-width: 400px;
  margin: 0 auto;
}

.test-results {
  margin-top: 24px;
  text-align: left;
}

.test-results h4 {
  margin: 0 0 16px 0;
  text-align: center;
}

.test-result-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-light);
}

.test-result-item:last-child {
  border-bottom: none;
}

.test-result-item .success {
  color: var(--el-color-success);
}

.test-result-item .error {
  color: var(--el-color-danger);
}

.result-message {
  margin-left: auto;
  font-size: 14px;
  color: var(--el-text-color-regular);
}

.wizard-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding-top: 32px;
  border-top: 1px solid var(--el-border-color-light);
}
</style>
