<script setup lang="ts">
import { computed, defineEmits, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { Check, Grid, Plus, Setting } from '@element-plus/icons-vue';

// Emits
const emit = defineEmits<{
  applyMapping: [mapping: any[]];
  addCustomField: [field: any];
}>();

// 响应式数据
const selectedProvider = ref('29pt');
const activeInterfaceTab = ref('query');

// 货源映射配置 - 基于新的统一标准字段
const providerMappings: Record<string, any[]> = {
  '29pt': [
    // 认证字段 - 所有接口都需要
    {
      standardField: 'api_uid',
      providerField: 'uid',
      description: '货源用户ID',
      example: '5',
      required: true,
      enabled: true,
      category: 'auth'
    },
    {
      standardField: 'api_key',
      providerField: 'key',
      description: '货源密钥',
      example: 'YsIYr7lZ75plP8Y5',
      required: true,
      enabled: true,
      category: 'auth'
    },

    // 学生信息字段
    {
      standardField: 'username',
      providerField: 'user',
      description: '学生账号',
      example: 'student123',
      required: true,
      enabled: true,
      category: 'student'
    },
    {
      standardField: 'password',
      providerField: 'pass',
      description: '学生密码',
      example: 'password123',
      required: true,
      enabled: true,
      category: 'student'
    },
    {
      standardField: 'school',
      providerField: 'school',
      description: '学校名称',
      example: '北京大学',
      required: true,
      enabled: true,
      category: 'student'
    },
    {
      standardField: 'platform',
      providerField: 'platform',
      description: '平台标识',
      example: 'xuetong',
      required: true,
      enabled: true,
      category: 'student'
    },

    // 课程信息字段
    {
      standardField: 'course_id',
      providerField: 'kcid',
      description: '课程ID',
      example: 'course_001',
      required: true,
      enabled: true,
      category: 'course'
    },
    {
      standardField: 'course_name',
      providerField: 'kcname',
      description: '课程名称',
      example: '高等数学',
      required: true,
      enabled: true,
      category: 'course'
    },
    {
      standardField: 'teacher',
      providerField: 'teacher',
      description: '教师姓名',
      example: '张教授',
      required: false,
      enabled: true,
      category: 'course'
    },
    {
      standardField: 'credit',
      providerField: 'credit',
      description: '学分',
      example: '3',
      required: false,
      enabled: true,
      category: 'course'
    },

    // 订单信息字段
    {
      standardField: 'upstream_order_id',
      providerField: 'yid',
      description: '上游订单ID',
      example: '210105',
      required: true,
      enabled: true,
      category: 'order'
    },
    {
      standardField: 'new_password',
      providerField: 'newpass',
      description: '新密码',
      example: 'newpass123',
      required: true,
      enabled: true,
      category: 'order'
    },

    // 响应字段映射
    {
      standardField: 'status',
      providerField: 'status',
      description: '订单状态',
      example: '进行中',
      required: false,
      enabled: true,
      category: 'response'
    },
    {
      standardField: 'progress',
      providerField: 'process',
      description: '进度信息',
      example: '50%',
      required: false,
      enabled: true,
      category: 'response'
    },
    {
      standardField: 'remarks',
      providerField: 'remarks',
      description: '备注信息',
      example: '正常进行',
      required: false,
      enabled: true,
      category: 'response'
    },
    {
      standardField: 'balance',
      providerField: 'money',
      description: '余额',
      example: '100.50',
      required: false,
      enabled: true,
      category: 'response',
      transform: 'toNumber'
    }
  ],
  universal: [
    {
      standardField: 'username',
      providerField: 'username',
      description: 'API用户名',
      example: 'api_user',
      required: true,
      enabled: true
    },
    {
      standardField: 'password',
      providerField: 'password',
      description: 'API密码',
      example: 'api_pass',
      required: true,
      enabled: true
    },
    {
      standardField: 'student_username',
      providerField: 'student_user',
      description: '学生用户名',
      example: 'student',
      required: true,
      enabled: true
    },
    {
      standardField: 'student_password',
      providerField: 'student_pass',
      description: '学生密码',
      example: 'pass',
      required: true,
      enabled: true
    },
    {
      standardField: 'school',
      providerField: 'school_name',
      description: '学校名称',
      example: '学校',
      required: true,
      enabled: true
    },
    {
      standardField: 'platform',
      providerField: 'platform_type',
      description: '平台类型',
      example: 'platform',
      required: false,
      enabled: true
    },
    {
      standardField: 'course_id',
      providerField: 'course_id',
      description: '课程ID',
      example: 'course',
      required: false,
      enabled: true
    },
    {
      standardField: 'course_name',
      providerField: 'course_name',
      description: '课程名称',
      example: '课程',
      required: false,
      enabled: true
    },
    {
      standardField: 'upstream_order_id',
      providerField: 'order_id',
      description: '订单ID',
      example: 'order',
      required: true,
      enabled: true
    }
  ],
  custom: []
};

// 接口类型
const interfaceTypes = [
  { type: 'query', name: '查课接口' },
  { type: 'order', name: '下单接口' },
  { type: 'sync', name: '同步接口' },
  { type: 'refill', name: '补刷接口' },
  { type: 'change_password', name: '改密接口' },
  { type: 'get_balance', name: '余额查询' },
  { type: 'get_courses', name: '课程列表' }
];

// 接口特定字段 - 基于29平台实际API
const interfaceSpecificMappings: Record<string, any[]> = {
  query: [
    {
      standardField: 'api_uid',
      providerField: 'uid',
      description: '29平台API用户名',
      example: '5',
      url: 'https://freedomp.icu/api.php?act=get'
    },
    { standardField: 'api_key', providerField: 'key', description: '29平台API密钥', example: 'YsIYr7lZ75plP8Y5' },
    { standardField: 'platform', providerField: 'platform', description: '平台ID（必传）', example: 'network_course' },
    {
      standardField: 'student_username',
      providerField: 'user',
      description: '学生账号（必传）',
      example: 'student123'
    },
    {
      standardField: 'student_password',
      providerField: 'pass',
      description: '学生密码（必传）',
      example: 'password123'
    },
    { standardField: 'school', providerField: 'school', description: '学生学校（必传）', example: '北京大学' },
    { standardField: 'course_id', providerField: 'kcid', description: '课程ID（可选）', example: 'course_001' }
  ],
  order: [
    {
      standardField: 'api_uid',
      providerField: 'uid',
      description: '29平台API用户名',
      example: '5',
      url: 'https://freedomp.icu/api.php?act=add'
    },
    { standardField: 'api_key', providerField: 'key', description: '29平台API密钥', example: 'YsIYr7lZ75plP8Y5' },
    { standardField: 'platform', providerField: 'platform', description: '平台ID（必传）', example: 'network_course' },
    {
      standardField: 'student_username',
      providerField: 'user',
      description: '学生账号（必传）',
      example: 'student123'
    },
    {
      standardField: 'student_password',
      providerField: 'pass',
      description: '账号密码（必传）',
      example: 'password123'
    },
    { standardField: 'school', providerField: 'school', description: '学生学校（必传）', example: '北京大学' },
    { standardField: 'course_name', providerField: 'kcname', description: '课程名字（必传）', example: '高等数学' },
    { standardField: 'course_id', providerField: 'kcid', description: '课程ID（必传）', example: 'course_001' }
  ],
  sync: [
    {
      standardField: 'api_uid',
      providerField: 'uid',
      description: '29平台API用户名',
      example: '5',
      url: 'https://freedomp.icu/api.php?act=chadanoid'
    },
    { standardField: 'api_key', providerField: 'key', description: '29平台API密钥', example: 'YsIYr7lZ75plP8Y5' },
    { standardField: 'upstream_order_id', providerField: 'yid', description: '订单ID（可选）', example: 'order_123' },
    {
      standardField: 'query_username',
      providerField: 'username',
      description: '订单账号（可选）',
      example: 'student123'
    },
    { standardField: 'query_school', providerField: 'school', description: '订单学校（可选）', example: '北京大学' }
  ],
  refill: [
    {
      standardField: 'api_uid',
      providerField: 'uid',
      description: '29平台API用户名',
      example: '5',
      url: 'https://freedomp.icu/api.php?act=budan'
    },
    { standardField: 'api_key', providerField: 'key', description: '29平台API密钥', example: 'YsIYr7lZ75plP8Y5' },
    { standardField: 'order_id', providerField: 'id', description: '订单ID（必传）', example: 'order_123' }
  ],
  change_password: [
    {
      standardField: 'api_uid',
      providerField: 'uid',
      description: '29平台API用户名',
      example: '5',
      url: 'https://freedomp.icu/api.php?act=xgmm'
    },
    { standardField: 'api_key', providerField: 'key', description: '29平台API密钥', example: 'YsIYr7lZ75plP8Y5' },
    { standardField: 'change_order_id', providerField: 'yid', description: '订单ID（必传）', example: 'order_123' },
    { standardField: 'new_password', providerField: 'pwd', description: '新密码（必传）', example: 'newpass123' }
  ],
  get_courses: [
    {
      standardField: 'api_uid',
      providerField: 'uid',
      description: '29平台API用户名',
      example: '5',
      url: 'https://freedomp.icu/api.php?act=getclass'
    },
    { standardField: 'api_key', providerField: 'key', description: '29平台API密钥', example: 'YsIYr7lZ75plP8Y5' }
  ],
  get_balance: [],
  pause_order: []
};

// 自定义字段建议
const customFieldSuggestions = {
  auth: [
    { name: 'token', description: 'API令牌', example: 'bearer_token' },
    { name: 'app_id', description: '应用ID', example: 'app_123' },
    { name: 'signature', description: '签名', example: 'sign_hash' }
  ],
  business: [
    { name: 'category', description: '课程分类', example: 'math' },
    { name: 'priority', description: '优先级', example: 'high' },
    { name: 'callback_url', description: '回调地址', example: 'https://...' },
    { name: 'remark', description: '备注信息', example: '特殊要求' }
  ],
  system: [
    { name: 'timestamp', description: '时间戳', example: '**********' },
    { name: 'version', description: 'API版本', example: 'v1.0' },
    { name: 'format', description: '返回格式', example: 'json' },
    { name: 'charset', description: '字符编码', example: 'utf-8' }
  ]
};

// 计算属性
const currentProviderMapping = computed(() => {
  return providerMappings[selectedProvider.value] || [];
});

// 方法
const getTransformLabel = (transform: string) => {
  const labels: Record<string, string> = {
    md5: 'MD5',
    base64: 'Base64',
    urlEncode: 'URL编码',
    toString: '转字符串',
    toNumber: '转数字',
    timestamp: '时间戳'
  };
  return labels[transform] || transform;
};

const getInterfaceSpecificFields = (interfaceType: string) => {
  return interfaceSpecificMappings[interfaceType] || [];
};

const toggleField = (index: number) => {
  const field = currentProviderMapping.value[index];
  if (field.required && !field.enabled) {
    ElMessage.warning('必填字段不能禁用');
    field.enabled = true;
  }
};

const applyMapping = () => {
  const enabledMappings = currentProviderMapping.value.filter((field: any) => field.enabled);
  emit('applyMapping', enabledMappings);
  ElMessage.success(`已应用${selectedProvider.value === '29pt' ? '29平台' : '通用'}字段映射`);
};

const addSuggestedField = (field: any) => {
  emit('addCustomField', {
    name: field.name,
    description: field.description,
    example: field.example,
    required: false
  });
  ElMessage.success(`已添加自定义字段: ${field.name}`);
};
</script>

<template>
  <div class="provider-field-reference">
    <ElCard class="reference-card" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>
            <ElIcon><Grid /></ElIcon>
            货源字段映射参考表
          </h3>
          <div class="header-actions">
            <ElSelect v-model="selectedProvider" placeholder="选择货源" style="width: 150px">
              <ElOption label="29平台" value="29pt" />
              <ElOption label="通用货源" value="universal" />
              <ElOption label="自定义货源" value="custom" />
            </ElSelect>
            <ElButton type="primary" size="small" @click="applyMapping">
              <ElIcon><Check /></ElIcon>
              应用映射
            </ElButton>
          </div>
        </div>
      </template>

      <!-- 货源字段映射表 -->
      <ElTable :data="currentProviderMapping" border class="mapping-reference-table">
        <ElTableColumn label="项目标准字段" width="150">
          <template #default="{ row }">
            <div class="standard-field-info">
              <span class="field-name">{{ row.standardField }}</span>
              <ElTag v-if="row.required" type="danger" size="small">必填</ElTag>
              <ElTag v-else type="info" size="small">可选</ElTag>
            </div>
          </template>
        </ElTableColumn>

        <ElTableColumn label="货源字段名" width="150">
          <template #default="{ row }">
            <ElText type="primary">{{ row.providerField }}</ElText>
          </template>
        </ElTableColumn>

        <ElTableColumn label="字段说明" min-width="200">
          <template #default="{ row }">
            {{ row.description }}
          </template>
        </ElTableColumn>

        <ElTableColumn label="示例值" width="150">
          <template #default="{ row }">
            <ElText size="small" type="info">{{ row.example }}</ElText>
          </template>
        </ElTableColumn>

        <ElTableColumn label="数据转换" width="120">
          <template #default="{ row }">
            <ElTag v-if="row.transform" size="small">{{ getTransformLabel(row.transform) }}</ElTag>
            <ElText v-else size="small" type="info">无</ElText>
          </template>
        </ElTableColumn>

        <ElTableColumn label="操作" width="100">
          <template #default="{ row, $index }">
            <ElSwitch v-model="row.enabled" size="small" @change="toggleField($index)" />
          </template>
        </ElTableColumn>
      </ElTable>

      <!-- 接口类型特定字段 -->
      <ElCard class="interface-specific-fields" shadow="never">
        <template #header>
          <h4>
            <ElIcon><Setting /></ElIcon>
            接口类型特定字段
          </h4>
        </template>

        <ElTabs v-model="activeInterfaceTab">
          <ElTabPane
            v-for="interfaceType in interfaceTypes"
            :key="interfaceType.type"
            :label="interfaceType.name"
            :name="interfaceType.type"
          >
            <ElTable :data="getInterfaceSpecificFields(interfaceType.type)" size="small" border>
              <ElTableColumn prop="standardField" label="标准字段" width="150" />
              <ElTableColumn prop="providerField" label="货源字段" width="150" />
              <ElTableColumn prop="description" label="说明" />
              <ElTableColumn prop="example" label="示例" width="120" />
            </ElTable>
          </ElTabPane>
        </ElTabs>
      </ElCard>

      <!-- 自定义字段建议 -->
      <ElCard class="custom-fields-suggestion" shadow="never">
        <template #header>
          <h4>
            <ElIcon><Plus /></ElIcon>
            常用自定义字段建议
          </h4>
        </template>

        <ElRow :gutter="16">
          <ElCol :span="8">
            <h5>认证相关</h5>
            <ElSpace direction="vertical" size="small">
              <ElTag
                v-for="field in customFieldSuggestions.auth"
                :key="field.name"
                style="cursor: pointer"
                @click="addSuggestedField(field)"
              >
                {{ field.name }} - {{ field.description }}
              </ElTag>
            </ElSpace>
          </ElCol>

          <ElCol :span="8">
            <h5>业务相关</h5>
            <ElSpace direction="vertical" size="small">
              <ElTag
                v-for="field in customFieldSuggestions.business"
                :key="field.name"
                style="cursor: pointer"
                @click="addSuggestedField(field)"
              >
                {{ field.name }} - {{ field.description }}
              </ElTag>
            </ElSpace>
          </ElCol>

          <ElCol :span="8">
            <h5>系统相关</h5>
            <ElSpace direction="vertical" size="small">
              <ElTag
                v-for="field in customFieldSuggestions.system"
                :key="field.name"
                style="cursor: pointer"
                @click="addSuggestedField(field)"
              >
                {{ field.name }} - {{ field.description }}
              </ElTag>
            </ElSpace>
          </ElCol>
        </ElRow>
      </ElCard>
    </ElCard>
  </div>
</template>

<style scoped>
.provider-field-reference {
  padding: 20px;
}

.reference-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.mapping-reference-table {
  margin-bottom: 20px;
}

.standard-field-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.field-name {
  font-weight: 500;
  color: #303133;
}

.interface-specific-fields,
.custom-fields-suggestion {
  margin-top: 20px;
  background: #fafafa;
}

.interface-specific-fields h4,
.custom-fields-suggestion h4 {
  margin: 0;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.custom-fields-suggestion h5 {
  margin: 0 0 8px 0;
  color: #606266;
  font-size: 14px;
}

.custom-fields-suggestion .el-tag {
  margin-bottom: 8px;
  transition: all 0.3s;
}

.custom-fields-suggestion .el-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
