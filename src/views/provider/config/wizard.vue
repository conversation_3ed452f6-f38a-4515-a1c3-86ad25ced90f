<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import {
  ArrowLeft,
  ArrowRight,
  Check,
  CircleCheck,
  Connection,
  DataBoard,
  InfoFilled,
  Lightning,
  Link,
  Menu,
  QuestionFilled,
  Setting,
  Star,
  View
} from '@element-plus/icons-vue';

// 导入标准字段定义和API
import {
  INTERFACE_DEFINITIONS,
  type InterfaceDefinition,
  getCategories,
  getInterfacesByCategory
} from '@/config/standardFields';
import {
  fetchFieldMappingTemplates,
  fetchInterfaceDefinitions,
  fetchSaveWizardConfig,
  fetchTestProviderInterface
} from '@/service/api/provider';
import FieldMapping from './field-mapping.vue';

// Props
const props = defineProps<{
  providerId: number;
}>();

// Emits
const emit = defineEmits<{
  close: [];
  save: [config: any];
}>();

// 响应式数据
const currentStep = ref(0);
const selectedCategory = ref('basic');
const selectedInterface = ref<InterfaceDefinition | null>(null);
const showPreview = ref(false);
const testing = ref(false);
const saving = ref(false);
const testResult = ref<any>(null);
const activeCollapse = ref<string[]>([]);

// 分类数据
const categories = getCategories();

// 基本配置
const basicConfig = reactive({
  endpointUrl: '',
  httpMethod: 'POST'
});

// 字段映射
const fieldMappings = ref<any[]>([]);

// 测试数据
const testData = reactive<Record<string, any>>({});

// 表单验证规则
const basicRules = {
  endpointUrl: [
    { required: true, message: '请输入接口地址', trigger: 'blur' },
    {
      pattern: /^https?:\/\/.+/,
      message: '请输入有效的URL',
      trigger: 'blur'
    }
  ]
};

// 计算属性
const canProceed = computed(() => {
  switch (currentStep.value) {
    case 0:
      return Boolean(selectedInterface.value);
    case 1:
      return basicConfig.endpointUrl && basicConfig.httpMethod;
    case 2:
      return fieldMappings.value.filter(f => f.required).every(f => f.providerField);
    default:
      return true;
  }
});

const generatedConfig = computed(() => {
  if (!selectedInterface.value) return {};

  // 生成请求模板
  const requestTemplate: Record<string, string> = {};
  fieldMappings.value.forEach(field => {
    if (field.providerField) {
      requestTemplate[field.providerField] = `\${data.${field.name}}`;
    }
  });

  // 生成响应映射
  const responseMapping = selectedInterface.value.examples?.platform29?.response || {
    success_field: 'code',
    success_value: 0,
    message_field: 'msg',
    data_field: 'data'
  };

  // 生成字段映射
  const fieldMapping: Record<string, any> = {};
  fieldMappings.value.forEach(field => {
    if (field.providerField) {
      fieldMapping[field.name] = {
        provider_field: field.providerField,
        required: field.required,
        transform: field.transform || undefined,
        default_value: field.defaultValue || undefined
      };
    }
  });

  return {
    requestTemplate,
    responseMapping,
    fieldMapping
  };
});

// 方法
const selectInterface = (interfaceItem: InterfaceDefinition) => {
  selectedInterface.value = interfaceItem;
  initializeFieldMappings();
};

const initializeFieldMappings = () => {
  if (!selectedInterface.value) return;

  fieldMappings.value = selectedInterface.value.fields.map(field => ({
    ...field,
    providerField: '',
    transform: '',
    defaultValue: ''
  }));
};

const apply29Config = () => {
  if (!selectedInterface.value) return;

  // 设置接口地址 - 基于29平台实际API
  const interfaceUrls: Record<string, string> = {
    query: 'https://freedomp.icu/api.php?act=get',
    order: 'https://freedomp.icu/api.php?act=add',
    sync: 'https://freedomp.icu/api.php?act=chadanoid',
    refill: 'https://freedomp.icu/api.php?act=budan',
    change_password: 'https://freedomp.icu/api.php?act=xgmm',
    get_courses: 'https://freedomp.icu/api.php?act=getclass'
  };

  if (interfaceUrls[selectedInterface.value.type]) {
    basicConfig.endpointUrl = interfaceUrls[selectedInterface.value.type];
  }

  // 设置字段映射 - 基于29平台实际API文档
  const mapping29: Record<string, string> = {
    api_uid: 'uid',
    api_key: 'key',
    platform: 'platform',
    student_username: 'user',
    student_password: 'pass',
    school: 'school',
    course_id: 'kcid',
    course_name: 'kcname',
    order_id: 'id',
    upstream_order_id: 'yid',
    query_username: 'username',
    query_school: 'school',
    change_order_id: 'yid',
    new_password: 'pwd'
  };

  fieldMappings.value.forEach(field => {
    if (mapping29[field.name]) {
      field.providerField = mapping29[field.name];
    }
  });

  ElMessage.success('已应用29平台配置模板');
};

const getRequiredTestFields = () => {
  if (!selectedInterface.value) return [];
  return selectedInterface.value.fields.filter(f => f.required).slice(0, 8); // 增加显示数量
};

const fillDefaultTestData = () => {
  if (!selectedInterface.value) return;

  // 29平台默认测试数据
  const defaultTestData: Record<string, string> = {
    // 认证字段
    api_uid: '5',
    api_key: 'YsIYr7lZ75plP8Y5',

    // 学生信息
    student_username: 'test_student',
    student_password: 'test123456',
    school: '测试大学',
    platform: 'network_course',

    // 课程信息
    course_id: 'test_course_001',
    course_name: '测试课程',

    // 订单信息
    upstream_order_id: 'test_order_123',
    order_id: 'test_order_456',
    query_username: 'test_student',
    query_school: '测试大学',

    // 改密信息
    change_order_id: 'test_order_789',
    new_password: 'newpass123'
  };

  // 只填充当前接口需要的字段
  const requiredFields = getRequiredTestFields();
  requiredFields.forEach(field => {
    if (defaultTestData[field.name]) {
      testData[field.name] = defaultTestData[field.name];
    }
  });

  ElMessage.success('已填充默认测试数据');
};

const nextStep = () => {
  if (canProceed.value && currentStep.value < 3) {
    currentStep.value++;
  }
};

const prevStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--;
  }
};

const handleFieldMappingChange = (mappings: any[]) => {
  fieldMappings.value = mappings;
};

const handleMappingPreview = (config: any) => {
  // 不需要修改计算属性，配置会自动更新
  console.log('映射配置预览:', config);
};

const previewConfig = () => {
  showPreview.value = true;
};

const runTest = async () => {
  if (!selectedInterface.value || !props.providerId) {
    ElMessage.warning('请先选择接口类型');
    return;
  }

  // 检查是否有测试数据
  const requiredFields = getRequiredTestFields();
  const missingFields = requiredFields.filter(field => !testData[field.name]);

  if (missingFields.length > 0) {
    ElMessage.warning(`请填写必填的测试数据: ${missingFields.map(f => f.label).join(', ')}`);
    return;
  }

  // 检查是否有接口配置
  if (!basicConfig.endpointUrl) {
    ElMessage.warning('请先配置接口地址');
    return;
  }

  testing.value = true;
  try {
    console.log('开始测试接口:', {
      providerId: props.providerId,
      interfaceType: selectedInterface.value.type,
      testData,
      endpointUrl: basicConfig.endpointUrl
    });

    const response = await fetchTestProviderInterface(props.providerId, {
      interfaceType: selectedInterface.value.type,
      testData
    });

    console.log('测试响应:', response);

    if (response.data) {
      testResult.value = response.data;

      if (testResult.value.success) {
        ElMessage.success('接口测试成功');
      } else {
        ElMessage.warning(`接口测试失败: ${testResult.value.message || '未知错误'}`);
      }
    } else {
      throw new Error('响应数据为空');
    }
  } catch (error) {
    console.error('测试请求失败:', error);
    const errorMessage = error instanceof Error ? error.message : '测试请求失败';
    ElMessage.error(`测试请求失败: ${errorMessage}`);
    testResult.value = {
      success: false,
      message: errorMessage,
      responseTime: 0,
      statusCode: 0
    };
  } finally {
    testing.value = false;
  }
};

const saveConfig = async () => {
  if (!selectedInterface.value || !props.providerId) return;

  saving.value = true;
  try {
    const config = generatedConfig.value;

    await fetchSaveWizardConfig(props.providerId, {
      interfaceType: selectedInterface.value.type,
      endpointUrl: basicConfig.endpointUrl,
      httpMethod: basicConfig.httpMethod,
      requestTemplate: config.requestTemplate,
      responseMapping: config.responseMapping,
      fieldMapping: config.fieldMapping
    });

    ElMessage.success('配置保存成功');
    emit('save', config);
    emit('close');
  } catch (error) {
    ElMessage.error('保存配置失败');
  } finally {
    saving.value = false;
  }
};

onMounted(() => {
  // 初始化
});
</script>

<template>
  <div class="interface-config-wizard">
    <ElCard class="wizard-card">
      <template #header>
        <div class="wizard-header">
          <h2>
            <ElIcon><Star /></ElIcon>
            智能接口配置向导
          </h2>
          <p class="subtitle">通过向导快速配置货源接口，支持29平台等多种货源</p>
        </div>
      </template>

      <!-- 步骤指示器 -->
      <ElSteps :active="currentStep" align-center class="wizard-steps">
        <ElStep title="选择接口类型" icon="Menu" />
        <ElStep title="配置基本信息" icon="Setting" />
        <ElStep title="字段映射" icon="Connection" />
        <ElStep title="测试验证" icon="CircleCheck" />
      </ElSteps>

      <!-- 步骤内容 -->
      <div class="wizard-content">
        <!-- 步骤1: 选择接口类型 -->
        <div v-if="currentStep === 0" class="step-content">
          <h3>选择要配置的接口类型</h3>

          <!-- 接口分类 -->
          <ElTabs v-model="selectedCategory" class="interface-categories">
            <ElTabPane v-for="category in categories" :key="category.key" :label="category.label" :name="category.key">
              <div class="interface-grid">
                <ElCard
                  v-for="interfaceItem in getInterfacesByCategory(category.key)"
                  :key="interfaceItem.type"
                  class="interface-card"
                  :class="[{ selected: selectedInterface?.type === interfaceItem.type }]"
                  shadow="hover"
                  @click="selectInterface(interfaceItem)"
                >
                  <div class="interface-content">
                    <div class="interface-icon" :style="{ color: interfaceItem.color }">
                      <ElIcon :size="32">
                        <component :is="interfaceItem.icon" />
                      </ElIcon>
                    </div>
                    <h4>{{ interfaceItem.name }}</h4>
                    <p>{{ interfaceItem.description }}</p>

                    <!-- 29平台支持标识 -->
                    <div v-if="interfaceItem.examples?.platform29" class="platform-support">
                      <ElTag size="small" type="success">
                        <ElIcon><Check /></ElIcon>
                        29平台支持
                      </ElTag>
                    </div>
                  </div>
                </ElCard>
              </div>
            </ElTabPane>
          </ElTabs>

          <!-- 选中接口的详细信息 -->
          <ElCard v-if="selectedInterface" class="interface-detail" shadow="never">
            <h4>{{ selectedInterface.name }} - 详细说明</h4>
            <p>{{ selectedInterface.description }}</p>

            <div class="field-preview">
              <h5>需要配置的字段：</h5>
              <ElSpace wrap>
                <ElTag
                  v-for="field in selectedInterface.fields"
                  :key="field.name"
                  :type="field.required ? 'danger' : 'info'"
                  size="small"
                >
                  {{ field.label }}
                  <span v-if="field.required">*</span>
                </ElTag>
              </ElSpace>
            </div>
          </ElCard>
        </div>

        <!-- 步骤2: 配置基本信息 -->
        <div v-if="currentStep === 1" class="step-content">
          <h3>配置 {{ selectedInterface?.name }} 基本信息</h3>

          <ElForm ref="basicFormRef" :model="basicConfig" :rules="basicRules" label-width="120px">
            <ElRow :gutter="20">
              <ElCol :span="12">
                <ElFormItem label="接口地址" prop="endpointUrl" required>
                  <ElInput v-model="basicConfig.endpointUrl" placeholder="https://api.example.com/endpoint">
                    <template #prepend>
                      <ElIcon><Link /></ElIcon>
                    </template>
                  </ElInput>
                  <div class="field-tips">
                    <ElText size="small" type="info">
                      <ElIcon><InfoFilled /></ElIcon>
                      货源提供的API接口地址
                    </ElText>
                  </div>
                </ElFormItem>
              </ElCol>

              <ElCol :span="12">
                <ElFormItem label="请求方法" prop="httpMethod">
                  <ElSelect v-model="basicConfig.httpMethod" style="width: 100%">
                    <ElOption label="POST" value="POST" />
                    <ElOption label="GET" value="GET" />
                    <ElOption label="PUT" value="PUT" />
                    <ElOption label="DELETE" value="DELETE" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
            </ElRow>

            <!-- 29平台快速配置 -->
            <ElCard v-if="selectedInterface?.examples?.platform29" class="platform-quick-config" shadow="never">
              <template #header>
                <div class="quick-config-header">
                  <ElIcon><Lightning /></ElIcon>
                  29平台快速配置
                </div>
              </template>

              <ElAlert title="检测到29平台配置模板" type="success" :closable="false" show-icon>
                <template #default>
                  <p>我们为您准备了29平台的配置模板，点击下方按钮可以自动填充配置。</p>
                  <ElButton type="primary" size="small" style="margin-top: 10px" @click="apply29Config">
                    <ElIcon><Star /></ElIcon>
                    应用29平台配置
                  </ElButton>
                </template>
              </ElAlert>
            </ElCard>
          </ElForm>
        </div>

        <!-- 步骤3: 字段映射 -->
        <div v-if="currentStep === 2" class="step-content">
          <h3>配置字段映射</h3>
          <p class="step-description">将标准字段映射到货源接口的实际字段名，支持添加自定义字段</p>

          <FieldMapping
            :interface-type="selectedInterface?.type || ''"
            :initial-mapping="fieldMappings"
            @change="handleFieldMappingChange"
            @preview="handleMappingPreview"
          />
        </div>

        <!-- 步骤4: 测试验证 -->
        <div v-if="currentStep === 3" class="step-content">
          <h3>测试接口配置</h3>
          <p class="step-description">使用测试数据验证接口配置是否正确</p>

          <!-- 测试数据输入 -->
          <ElCard class="test-data-card" shadow="never">
            <template #header>
              <div class="test-data-header">
                <div>
                  <ElIcon><DataBoard /></ElIcon>
                  测试数据
                </div>
                <ElButton size="small" @click="fillDefaultTestData">
                  <ElIcon><Lightning /></ElIcon>
                  填充默认数据
                </ElButton>
              </div>
            </template>

            <ElForm :model="testData" label-width="120px">
              <ElRow :gutter="20">
                <ElCol v-for="field in getRequiredTestFields()" :key="field.name" :span="12">
                  <ElFormItem :label="field.label">
                    <ElInput
                      v-model="testData[field.name]"
                      :placeholder="field.example"
                      :type="field.name.includes('password') ? 'password' : 'text'"
                    />
                    <div v-if="field.tips" class="field-tips">
                      <ElText size="small" type="info">{{ field.tips[0] }}</ElText>
                    </div>
                  </ElFormItem>
                </ElCol>
              </ElRow>

              <ElAlert
                v-if="getRequiredTestFields().length === 0"
                title="请先选择接口类型并配置字段映射"
                type="info"
                show-icon
                :closable="false"
              />
            </ElForm>
          </ElCard>

          <!-- 测试结果 -->
          <ElCard v-if="testResult" class="test-result-card" shadow="never">
            <template #header>
              <ElIcon><CircleCheck /></ElIcon>
              测试结果
            </template>

            <ElResult
              :icon="testResult.success ? 'success' : 'error'"
              :title="testResult.success ? '测试成功' : '测试失败'"
              :sub-title="testResult.message"
            >
              <template #extra>
                <ElDescriptions :column="2" border>
                  <ElDescriptionsItem label="响应时间">{{ testResult.responseTime }}ms</ElDescriptionsItem>
                  <ElDescriptionsItem label="状态码">
                    {{ testResult.statusCode }}
                  </ElDescriptionsItem>
                </ElDescriptions>
              </template>
            </ElResult>

            <!-- 详细响应数据 -->
            <ElCollapse v-model="activeCollapse" class="response-details">
              <ElCollapseItem title="查看详细响应" name="response">
                <ElTabs>
                  <ElTabPane label="请求数据" name="request">
                    <pre class="json-display">{{ JSON.stringify(testResult.requestData, null, 2) }}</pre>
                  </ElTabPane>
                  <ElTabPane label="原始响应" name="raw">
                    <pre class="json-display">{{ JSON.stringify(testResult.rawResponse, null, 2) }}</pre>
                  </ElTabPane>
                  <ElTabPane label="映射响应" name="mapped">
                    <pre class="json-display">{{ JSON.stringify(testResult.mappedResponse, null, 2) }}</pre>
                  </ElTabPane>
                </ElTabs>
              </ElCollapseItem>
            </ElCollapse>
          </ElCard>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="wizard-actions">
        <ElButton v-if="currentStep > 0" @click="prevStep">
          <ElIcon><ArrowLeft /></ElIcon>
          上一步
        </ElButton>

        <ElButton v-if="currentStep < 3" type="primary" :disabled="!canProceed" @click="nextStep">
          下一步
          <ElIcon><ArrowRight /></ElIcon>
        </ElButton>

        <ElButton v-if="currentStep === 2" type="success" @click="previewConfig">
          <ElIcon><View /></ElIcon>
          预览配置
        </ElButton>

        <ElButton v-if="currentStep === 3" type="primary" :loading="testing" @click="runTest">
          <ElIcon><CircleCheck /></ElIcon>
          执行测试
        </ElButton>

        <ElButton v-if="currentStep === 3 && testResult?.success" type="success" :loading="saving" @click="saveConfig">
          <ElIcon><Check /></ElIcon>
          保存配置
        </ElButton>
      </div>
    </ElCard>

    <!-- 配置预览对话框 -->
    <ElDialog v-model="showPreview" title="配置预览" width="70%">
      <ElTabs>
        <ElTabPane label="请求模板" name="request">
          <pre class="json-display">{{ JSON.stringify(generatedConfig.requestTemplate, null, 2) }}</pre>
        </ElTabPane>
        <ElTabPane label="响应映射" name="response">
          <pre class="json-display">{{ JSON.stringify(generatedConfig.responseMapping, null, 2) }}</pre>
        </ElTabPane>
        <ElTabPane label="字段映射" name="fields">
          <pre class="json-display">{{ JSON.stringify(generatedConfig.fieldMapping, null, 2) }}</pre>
        </ElTabPane>
      </ElTabs>
    </ElDialog>
  </div>
</template>

<style scoped>
.interface-config-wizard {
  padding: 20px;
}

.wizard-card {
  max-width: 1200px;
  margin: 0 auto;
}

.wizard-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
}

.subtitle {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.wizard-steps {
  margin: 30px 0;
}

.wizard-content {
  min-height: 400px;
  padding: 20px 0;
}

.step-content h3 {
  margin: 0 0 20px 0;
  color: #303133;
}

.step-description {
  color: #606266;
  margin-bottom: 20px;
}

.interface-categories {
  margin-bottom: 20px;
}

.interface-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
  margin-top: 20px;
}

.interface-card {
  cursor: pointer;
  transition: all 0.3s;
  border: 2px solid transparent;
}

.interface-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.interface-card.selected {
  border-color: #409eff;
}

.interface-content {
  text-align: center;
  padding: 10px;
}

.interface-icon {
  margin-bottom: 12px;
}

.interface-content h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.interface-content p {
  margin: 0 0 12px 0;
  color: #606266;
  font-size: 13px;
  line-height: 1.4;
}

.platform-support {
  margin-top: 8px;
}

.interface-detail {
  margin-top: 20px;
  background: #f8f9fa;
}

.field-preview h5 {
  margin: 16px 0 8px 0;
  color: #303133;
}

.platform-quick-config {
  margin-top: 20px;
  background: #f0f9ff;
  border: 1px solid #409eff;
}

.quick-config-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #409eff;
  font-weight: 500;
}

.field-mapping-container {
  margin-top: 20px;
}

.mapping-table {
  margin-bottom: 20px;
}

.standard-field .field-name {
  font-weight: 500;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.standard-field .field-description {
  font-size: 12px;
  color: #909399;
  line-height: 1.3;
}

.mapping-tips {
  background: #fafafa;
}

.mapping-tips ul {
  margin: 0;
  padding-left: 20px;
}

.mapping-tips li {
  margin-bottom: 8px;
  color: #606266;
  font-size: 14px;
}

.test-data-card,
.test-result-card {
  margin-bottom: 20px;
}

.test-data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.test-data-header > div:first-child {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.field-tips {
  margin-top: 4px;
}

.response-details {
  margin-top: 20px;
}

.json-display {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.5;
  max-height: 400px;
  overflow-y: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

.wizard-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding-top: 30px;
  border-top: 1px solid #ebeef5;
  margin-top: 30px;
}
</style>
