<script setup lang="ts">
import { computed, defineEmits, defineProps, ref } from 'vue';
import { ElMessageBox } from 'element-plus';
import {
  CircleCheck,
  Connection,
  DataBoard,
  Delete,
  Edit,
  Search,
  Setting,
  Tools,
  View
} from '@element-plus/icons-vue';
import { getInterfaceDefinition } from '@/config/standardFields';

// Props
const props = defineProps<{
  interfaces: any[];
}>();

// Emits
const emit = defineEmits<{
  test: [interfaceItem: any];
  edit: [interfaceItem: any];
  view: [interfaceItem: any];
  delete: [interfaceItem: any];
  toggle: [interfaceItem: any];
  quickConfig: [interfaceItem: any];
}>();

// 响应式数据
const searchKeyword = ref('');
const filterCategory = ref('');

// 方法定义（需要在计算属性之前定义）
const getInterfaceName = (type: string) => {
  const definition = getInterfaceDefinition(type);
  return definition?.name || type;
};

const getInterfaceIcon = (type: string) => {
  const definition = getInterfaceDefinition(type);
  return definition?.icon || 'Setting';
};

const getInterfaceColor = (type: string) => {
  const definition = getInterfaceDefinition(type);
  return definition?.color || '#909399';
};

const getInterfaceCategory = (type: string) => {
  const definition = getInterfaceDefinition(type);
  const categoryMap: Record<string, string> = {
    basic: '基础接口',
    advanced: '高级接口',
    management: '管理接口',
    batch: '批量接口'
  };
  return categoryMap[definition?.category || ''] || '未知';
};

const getInterfaceTagType = (type: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {
  const definition = getInterfaceDefinition(type);
  const typeMap: Record<string, 'primary' | 'success' | 'warning' | 'danger' | 'info'> = {
    basic: 'primary',
    advanced: 'success',
    management: 'warning',
    batch: 'danger'
  };
  return typeMap[definition?.category || ''] || 'info';
};

const formatTime = (time: string) => {
  return new Date(time).toLocaleString();
};

// 计算属性
const stats = computed(() => {
  const basic = props.interfaces.filter(i => getInterfaceCategory(i.interface_type) === '基础接口').length;
  const advanced = props.interfaces.filter(i => getInterfaceCategory(i.interface_type) === '高级接口').length;
  const management = props.interfaces.filter(i => getInterfaceCategory(i.interface_type) === '管理接口').length;

  return {
    basic,
    advanced,
    management,
    total: props.interfaces.length
  };
});

const filteredInterfaces = computed(() => {
  let result = props.interfaces;

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    result = result.filter(
      item =>
        getInterfaceName(item.interface_type).toLowerCase().includes(keyword) ||
        item.endpoint_url.toLowerCase().includes(keyword)
    );
  }

  // 分类筛选
  if (filterCategory.value) {
    result = result.filter(item => {
      const definition = getInterfaceDefinition(item.interface_type);
      return definition?.category === filterCategory.value;
    });
  }

  return result;
});

// 事件处理
const testInterface = (interfaceItem: any) => {
  emit('test', interfaceItem);
};

const editInterface = (interfaceItem: any) => {
  emit('edit', interfaceItem);
};

const viewInterface = (interfaceItem: any) => {
  emit('view', interfaceItem);
};

const deleteInterface = async (interfaceItem: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除接口 "${getInterfaceName(interfaceItem.interface_type)}" 吗？`, '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    });
    emit('delete', interfaceItem);
  } catch {
    // 用户取消
  }
};

const toggleInterface = (interfaceItem: any) => {
  emit('toggle', interfaceItem);
};

const quickConfig = (interfaceItem: any) => {
  emit('quickConfig', interfaceItem);
};
</script>

<template>
  <div class="interface-list">
    <!-- 接口统计卡片 -->
    <ElRow :gutter="16" class="stats-row">
      <ElCol :span="6">
        <ElCard class="stat-card basic">
          <div class="stat-content">
            <div class="stat-icon">
              <ElIcon><Connection /></ElIcon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.basic }}</div>
              <div class="stat-label">基础接口</div>
            </div>
          </div>
        </ElCard>
      </ElCol>
      <ElCol :span="6">
        <ElCard class="stat-card advanced">
          <div class="stat-content">
            <div class="stat-icon">
              <ElIcon><Setting /></ElIcon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.advanced }}</div>
              <div class="stat-label">高级接口</div>
            </div>
          </div>
        </ElCard>
      </ElCol>
      <ElCol :span="6">
        <ElCard class="stat-card management">
          <div class="stat-content">
            <div class="stat-icon">
              <ElIcon><Tools /></ElIcon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.management }}</div>
              <div class="stat-label">管理接口</div>
            </div>
          </div>
        </ElCard>
      </ElCol>
      <ElCol :span="6">
        <ElCard class="stat-card total">
          <div class="stat-content">
            <div class="stat-icon">
              <ElIcon><DataBoard /></ElIcon>
            </div>
            <div class="stat-info">
              <div class="stat-number">{{ stats.total }}</div>
              <div class="stat-label">总计</div>
            </div>
          </div>
        </ElCard>
      </ElCol>
    </ElRow>

    <!-- 接口列表 -->
    <ElCard class="interface-list-card" shadow="never">
      <template #header>
        <div class="list-header">
          <h3>接口配置列表</h3>
          <div class="header-actions">
            <ElInput v-model="searchKeyword" placeholder="搜索接口..." class="search-input" clearable>
              <template #prefix>
                <ElIcon><Search /></ElIcon>
              </template>
            </ElInput>
            <ElSelect v-model="filterCategory" placeholder="筛选分类" class="filter-select" clearable>
              <ElOption label="全部" value="" />
              <ElOption label="基础" value="basic" />
              <ElOption label="高级" value="advanced" />
              <ElOption label="管理" value="management" />
              <ElOption label="批量" value="batch" />
            </ElSelect>
          </div>
        </div>
      </template>

      <!-- 接口卡片网格 -->
      <div v-if="filteredInterfaces.length > 0" class="interface-grid">
        <ElCard
          v-for="interfaceItem in filteredInterfaces"
          :key="interfaceItem.interface_id"
          class="interface-item-card"
          shadow="hover"
        >
          <!-- 接口头部 -->
          <div class="interface-header">
            <div class="interface-title">
              <div class="interface-icon" :style="{ color: getInterfaceColor(interfaceItem.interface_type) }">
                <ElIcon :size="20">
                  <component :is="getInterfaceIcon(interfaceItem.interface_type)" />
                </ElIcon>
              </div>
              <div class="title-info">
                <h4>{{ getInterfaceName(interfaceItem.interface_type) }}</h4>
                <ElTag :type="getInterfaceTagType(interfaceItem.interface_type)" size="small">
                  {{ getInterfaceCategory(interfaceItem.interface_type) }}
                </ElTag>
              </div>
            </div>

            <div class="interface-status">
              <ElSwitch
                v-model="interfaceItem.is_enabled"
                :active-value="1"
                :inactive-value="0"
                @change="toggleInterface(interfaceItem)"
              />
            </div>
          </div>

          <!-- 接口信息 -->
          <div class="interface-info">
            <div class="info-item">
              <span class="label">接口地址:</span>
              <ElText class="url" truncated>{{ interfaceItem.endpoint_url }}</ElText>
            </div>
            <div class="info-item">
              <span class="label">请求方法:</span>
              <ElTag :type="interfaceItem.http_method === 'POST' ? 'success' : 'info'" size="small">
                {{ interfaceItem.http_method }}
              </ElTag>
            </div>
            <div class="info-item">
              <span class="label">更新时间:</span>
              <ElText type="info" size="small">{{ formatTime(interfaceItem.update_time) }}</ElText>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="interface-actions">
            <ElButtonGroup>
              <ElButton size="small" @click="testInterface(interfaceItem)">
                <ElIcon><CircleCheck /></ElIcon>
                测试
              </ElButton>
              <ElButton size="small" @click="editInterface(interfaceItem)">
                <ElIcon><Edit /></ElIcon>
                编辑
              </ElButton>
              <ElButton size="small" @click="viewInterface(interfaceItem)">
                <ElIcon><View /></ElIcon>
                查看
              </ElButton>
              <ElButton size="small" type="danger" @click="deleteInterface(interfaceItem)">
                <ElIcon><Delete /></ElIcon>
                删除
              </ElButton>
            </ElButtonGroup>
          </div>

          <!-- 快速配置提示 -->
          <div v-if="!interfaceItem.request_template" class="config-tip">
            <ElAlert title="接口未完全配置" type="warning" :closable="false" show-icon>
              <template #default>
                <p>该接口缺少请求模板或字段映射配置</p>
                <ElButton type="text" size="small" @click="quickConfig(interfaceItem)">快速配置</ElButton>
              </template>
            </ElAlert>
          </div>
        </ElCard>
      </div>

      <!-- 空状态 -->
      <ElEmpty v-else description="暂无接口配置" />
    </ElCard>
  </div>
</template>

<style scoped>
.interface-list {
  padding: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  border: none;
  border-radius: 8px;
  overflow: hidden;
}

.stat-card.basic {
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
  color: white;
}

.stat-card.advanced {
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  color: white;
}

.stat-card.management {
  background: linear-gradient(135deg, #e6a23c 0%, #eebc6a 100%);
  color: white;
}

.stat-card.total {
  background: linear-gradient(135deg, #909399 0%, #b1b3b8 100%);
  color: white;
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stat-icon {
  font-size: 32px;
  margin-right: 16px;
  opacity: 0.8;
}

.stat-info {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

.interface-list-card {
  border-radius: 8px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-header h3 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.search-input {
  width: 200px;
}

.filter-select {
  width: 120px;
}

.interface-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 16px;
  margin-top: 20px;
}

.interface-item-card {
  border-radius: 8px;
  transition: all 0.3s;
}

.interface-item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.interface-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.interface-title {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.interface-icon {
  margin-top: 2px;
}

.title-info h4 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 16px;
}

.interface-info {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.info-item .label {
  color: #909399;
  margin-right: 8px;
  min-width: 70px;
}

.info-item .url {
  max-width: 200px;
}

.interface-actions {
  margin-bottom: 12px;
}

.config-tip {
  border-top: 1px solid #ebeef5;
  padding-top: 12px;
}
</style>
