<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Grid, Plus, Rank, RefreshRight, Setting, Star, VideoPlay, View } from '@element-plus/icons-vue';
import {
  fetchProviderInterfaces,
  fetchProviderList,
  fetchProviderUnifiedConfig,
  fetchSaveProviderInterface,
  fetchSaveProviderUnifiedConfig,
  fetchTestProviderInterface
} from '@/service/api/provider';
import InterfaceWizard from './wizard.vue';
import InterfaceList from './interface-list.vue';
import ProviderFieldReference from './provider-field-reference.vue';
import FieldMapping from './field-mapping.vue';
import UnifiedMapping from './unified-mapping.vue';
import ProviderForm from './provider-form.vue';
import ConfigWizard from './config-wizard.vue';

import FilterDrawer from '@/components/responsive/FilterDrawer.vue';
// 响应式数据
const showAddProvider = ref(false);
const showConfigWizard = ref(false);
const showWizard = ref(false);
const showFieldReference = ref(false);
const showUnifiedConfig = ref(false);
const showEditInterface = ref(false);
const showViewInterface = ref(false);
const showTestDialog = ref(false);
const activeEditTab = ref('basic');

const queryForm = reactive({
  providerId: undefined as number | undefined
});

const providerList = ref<any[]>([]);
const interfaceList = ref<any[]>([]);
const editingInterface = ref<any>(null);
const viewingInterface = ref<any>(null);
const testResult = ref<any>(null);
const filterDrawerVisible = ref(false);

const testing = ref(false);
const loading = ref(false);

// 方法
const refreshData = async () => {
  loading.value = true;
  try {
    console.log('开始加载货源列表...');
    // 传递默认参数获取所有货源
    const response = await fetchProviderList({
      page: 1,
      pageSize: 100
    });
    console.log('API响应:', response);

    // 根据 transformBackendResponse 的处理，response 已经是 data 字段的内容
    // 所以直接使用 response.list
    providerList.value = response?.list || [];

    console.log('货源列表加载成功:', providerList.value);
    console.log('货源列表长度:', providerList.value.length);
    ElMessage.success(`数据刷新成功，共加载 ${providerList.value.length} 个货源`);
  } catch (error: any) {
    console.error('加载货源列表失败:', error);
    ElMessage.error(`刷新失败: ${error.message || '未知错误'}`);
  } finally {
    loading.value = false;
  }
};

const handleProviderChange = () => {
  interfaceList.value = [];
  if (queryForm.providerId) {
    // 自动加载接口配置
    loadInterfaces();
  }
};

const loadInterfaces = async () => {
  if (!queryForm.providerId) {
    ElMessage.warning('请先选择货源');
    return;
  }

  try {
    console.log('开始加载接口配置，货源ID:', queryForm.providerId);
    const response = await fetchProviderInterfaces(queryForm.providerId);
    console.log('接口配置响应:', response);

    // 根据API响应结构处理数据
    if (response && response.list) {
      interfaceList.value = response.list;
    } else if (response && Array.isArray(response)) {
      interfaceList.value = response;
    } else {
      interfaceList.value = response?.data || [];
    }

    console.log('接口列表:', interfaceList.value);
    ElMessage.success(`加载成功，共 ${interfaceList.value.length} 个接口配置`);
  } catch (error: any) {
    console.error('加载接口失败:', error);
    ElMessage.error(`加载接口失败: ${error.message || '未知错误'}`);
  }
};

const handleProviderSave = async (providerData: any) => {
  try {
    // 保存货源逻辑
    ElMessage.success('货源保存成功');
    showAddProvider.value = false;
    refreshData();
  } catch (error: any) {
    ElMessage.error(`保存失败: ${error.message}`);
  }
};

const handleWizardSave = async (config: any) => {
  try {
    // 保存配置逻辑
    ElMessage.success('配置保存成功');
    showConfigWizard.value = false;
    loadInterfaces();
  } catch (error: any) {
    ElMessage.error(`保存失败: ${error.message}`);
  }
};

// 接口操作方法
const testInterface = async (interfaceData: any) => {
  testing.value = true;
  try {
    const response = await fetchTestProviderInterface(queryForm.providerId!, interfaceData.id);
    testResult.value = response.data;
    showTestDialog.value = true;
  } catch (error: any) {
    ElMessage.error(`测试失败: ${error.message}`);
  } finally {
    testing.value = false;
  }
};

const editInterface = (interfaceData: any) => {
  editingInterface.value = { ...interfaceData };
  activeEditTab.value = 'basic';
  showEditInterface.value = true;
};

const viewInterface = (interfaceData: any) => {
  viewingInterface.value = interfaceData;
  showViewInterface.value = true;
};

const deleteInterface = async (interfaceData: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个接口配置吗？', '确认删除', {
      type: 'warning'
    });

    // 删除接口逻辑
    ElMessage.success('接口删除成功');
    loadInterfaces();
  } catch (error) {
    // 用户取消删除
  }
};

const toggleInterface = async (interfaceData: any) => {
  try {
    // 切换接口状态逻辑
    ElMessage.success(`接口已${interfaceData.enabled ? '禁用' : '启用'}`);
    loadInterfaces();
  } catch (error: any) {
    ElMessage.error(`操作失败: ${error.message}`);
  }
};

const quickConfigInterface = (interfaceData: any) => {
  editInterface(interfaceData);
};

const handleInterfaceSave = async (interfaceData: any) => {
  try {
    await fetchSaveProviderInterface(queryForm.providerId!, interfaceData);
    ElMessage.success('接口保存成功');
    showEditInterface.value = false;
    loadInterfaces();
  } catch (error: any) {
    ElMessage.error(`保存失败: ${error.message}`);
  }
};

const closeEditInterface = () => {
  showEditInterface.value = false;
  editingInterface.value = null;
};

// 其他方法
const closeWizard = () => {
  showWizard.value = false;
};

const closeUnifiedConfig = () => {
  showUnifiedConfig.value = false;
};

const handleApplyMapping = (mapping: any) => {
  // 应用映射逻辑
  ElMessage.success('映射应用成功');
};

const handleAddCustomField = (field: any) => {
  // 添加自定义字段逻辑
  ElMessage.success('自定义字段添加成功');
};

const handleUnifiedConfigSave = async (config: any) => {
  try {
    await fetchSaveProviderUnifiedConfig(queryForm.providerId!, config);
    ElMessage.success('统一配置保存成功');
    closeUnifiedConfig();
    loadInterfaces();
  } catch (error: any) {
    ElMessage.error(`保存失败: ${error.message}`);
  }
};

// 生命周期
onMounted(() => {
  refreshData();
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <!-- 页面头部 -->
    <ElCard>
      <template #header>
        <div class="flex flex-col gap-12px lg:flex-row lg:items-center lg:justify-between">
          <div>
            <h2 class="mb-4px text-lg font-medium">货源配置管理</h2>
            <p class="text-sm text-gray-600">配置和管理货源接口映射</p>
          </div>
          <div class="flex flex-wrap gap-8px">
            <ElButton type="primary" @click="showAddProvider = true">
              <template #icon>
                <icon-ic-round-plus class="text-icon" />
              </template>
              新增货源
            </ElButton>
            <ElButton @click="refreshData">
              <template #icon>
                <icon-ic-round-refresh class="text-icon" />
              </template>
              刷新
            </ElButton>
          </div>
        </div>
      </template>
    </ElCard>

    <!-- 货源选择 -->
    <ElCard>
      <template #header>
        <span class="font-medium">货源选择</span>
      </template>

      <ElForm :model="queryForm" label-width="100px" class="space-y-16px">
        <ElFormItem label="选择货源">
          <div class="flex flex-wrap items-center gap-12px">
            <ElSelect
              v-model="queryForm.providerId"
              placeholder="请选择货源"
              clearable
              class="w-300px"
              :loading="loading"
              @change="handleProviderChange"
            >
              <ElOption
                v-for="provider in providerList"
                :key="provider.providerId"
                :label="`${provider.name} (${provider.code})`"
                :value="provider.providerId"
              />
            </ElSelect>
            <span class="text-sm text-gray-500 hidden md:inline-flex">
              {{ loading ? '加载中...' : `共 ${providerList.length} 个货源` }}
            </span>
            <ElButton class="hidden md:inline-flex" :disabled="!queryForm.providerId" @click="showConfigWizard = true">配置向导</ElButton>
            <ElButton class="hidden md:inline-flex" :loading="loading" @click="refreshData">刷新货源</ElButton>

            <!-- 小屏筛选抽屉入口：用于放置更多配置快捷入口 -->
            <ElButton class="md:hidden" @click="filterDrawerVisible = true">更多</ElButton>
            <FilterDrawer v-model="filterDrawerVisible" title="更多操作">
              <div class="space-y-12px">
                <div class="text-sm text-gray-500">在此可访问移动端收纳的操作</div>
                <ElButton :disabled="!queryForm.providerId" @click="showConfigWizard = true" type="primary" class="w-full">配置向导</ElButton>
                <ElButton :loading="loading" @click="refreshData" class="w-full">刷新货源</ElButton>
              </div>
            </FilterDrawer>
          </div>
        </ElFormItem>
      </ElForm>

      <!-- 调试信息 -->
      <div v-if="providerList.length === 0 && !loading" style="margin-top: 16px">
        <ElAlert
          title="暂无货源数据"
          description="请点击'刷新货源'按钮重新加载，或检查后端服务是否正常运行"
          type="warning"
          show-icon
          :closable="false"
        />
      </div>
    </ElCard>

    <!-- 接口配置列表 -->
    <div v-if="queryForm.providerId">
      <InterfaceList
        :interfaces="interfaceList"
        @test="testInterface"
        @edit="editInterface"
        @view="viewInterface"
        @delete="deleteInterface"
        @toggle="toggleInterface"
        @quick-config="quickConfigInterface"
      />
    </div>

    <!-- 新增货源对话框 -->
    <ElDialog v-model="showAddProvider" title="新增货源" width="600px">
      <ProviderForm @save="handleProviderSave" @cancel="showAddProvider = false" />
    </ElDialog>

    <!-- 配置向导对话框 -->
    <ElDialog v-model="showConfigWizard" title="配置向导" width="800px">
      <ConfigWizard
        v-if="queryForm.providerId"
        :provider-id="queryForm.providerId"
        @save="handleWizardSave"
        @cancel="showConfigWizard = false"
      />
    </ElDialog>

    <!-- 其他现有对话框保持不变 -->
    <ElDialog v-model="showWizard" title="智能接口配置向导" width="90%" :close-on-click-modal="false">
      <InterfaceWizard
        v-if="queryForm.providerId"
        :provider-id="queryForm.providerId"
        @close="closeWizard"
        @save="handleWizardSave"
      />
      <div v-else class="no-provider-selected">
        <ElAlert title="请先选择货源" type="warning" show-icon :closable="false" />
      </div>
    </ElDialog>

    <ElDialog v-model="showFieldReference" title="货源字段映射参考表" width="80%">
      <ProviderFieldReference @apply-mapping="handleApplyMapping" @add-custom-field="handleAddCustomField" />
    </ElDialog>

    <ElDialog v-model="showUnifiedConfig" title="统一配置管理" width="95%" :close-on-click-modal="false">
      <UnifiedMapping
        v-if="queryForm.providerId"
        :provider-id="queryForm.providerId"
        @close="closeUnifiedConfig"
        @save="handleUnifiedConfigSave"
      />
      <div v-else class="no-provider-selected">
        <ElAlert title="请先选择货源" type="warning" show-icon :closable="false" />
      </div>
    </ElDialog>

    <!-- 接口编辑对话框 -->
    <ElDialog
      v-model="showEditInterface"
      :title="editingInterface ? '编辑接口' : '新增接口'"
      width="80%"
      :close-on-click-modal="false"
    >
      <ElTabs v-model="activeEditTab" type="card" class="w-full">
        <ElTabPane label="基本配置" name="basic">
          <div class="p-8px">
            <FieldMapping
              v-if="editingInterface"
              :interface-data="editingInterface"
              @save="handleInterfaceSave"
              @cancel="closeEditInterface"
            />
          </div>
        </ElTabPane>
        <ElTabPane label="字段映射" name="mapping">
          <div class="mapping-content">
            <p>字段映射配置功能</p>
          </div>
        </ElTabPane>
        <ElTabPane label="测试验证" name="test">
          <div class="test-content">
            <p>接口测试功能</p>
          </div>
        </ElTabPane>
      </ElTabs>
    </ElDialog>

    <!-- 接口查看对话框 -->
    <ElDialog v-model="showViewInterface" title="查看接口详情" width="70%">
      <div v-if="viewingInterface" class="interface-details">
        <ElDescriptions :column="2" border>
          <ElDescriptionsItem label="接口名称">{{ viewingInterface.name }}</ElDescriptionsItem>
          <ElDescriptionsItem label="接口类型">{{ viewingInterface.type }}</ElDescriptionsItem>
          <ElDescriptionsItem label="请求方法">{{ viewingInterface.method }}</ElDescriptionsItem>
          <ElDescriptionsItem label="接口地址">{{ viewingInterface.endpoint }}</ElDescriptionsItem>
          <ElDescriptionsItem label="状态">
            <ElTag :type="viewingInterface.enabled ? 'success' : 'danger'">
              {{ viewingInterface.enabled ? '启用' : '禁用' }}
            </ElTag>
          </ElDescriptionsItem>
          <ElDescriptionsItem label="最后测试">{{ viewingInterface.lastTested || '未测试' }}</ElDescriptionsItem>
        </ElDescriptions>

        <div class="interface-config">
          <h4>字段映射配置</h4>
          <ElTable :data="viewingInterface.fieldMappings || []" border>
            <ElTableColumn prop="standardField" label="标准字段" />
            <ElTableColumn prop="providerField" label="货源字段" />
            <ElTableColumn prop="required" label="必填">
              <template #default="{ row }">
                <ElTag :type="row.required ? 'danger' : 'info'" size="small">
                  {{ row.required ? '是' : '否' }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn prop="description" label="说明" />
          </ElTable>
        </div>
      </div>
    </ElDialog>

    <!-- 测试结果对话框 -->
    <ElDialog v-model="showTestDialog" title="接口测试结果" width="60%">
      <div v-if="testResult" class="test-result">
        <ElAlert
          :title="testResult.success ? '测试成功' : '测试失败'"
          :type="testResult.success ? 'success' : 'error'"
          :description="testResult.message"
          show-icon
          :closable="false"
        />

        <div v-if="testResult.details" class="test-details">
          <h4>测试详情</h4>
          <ElDescriptions :column="2" border>
            <ElDescriptionsItem label="响应时间">{{ testResult.responseTime }}ms</ElDescriptionsItem>
            <ElDescriptionsItem label="状态码">{{ testResult.statusCode }}</ElDescriptionsItem>
            <ElDescriptionsItem label="响应大小">{{ testResult.responseSize }}B</ElDescriptionsItem>
            <ElDescriptionsItem label="测试时间">{{ testResult.timestamp }}</ElDescriptionsItem>
          </ElDescriptions>

          <div v-if="testResult.response" class="response-data">
            <h5>响应数据</h5>
            <ElInput v-model="testResult.response" type="textarea" :rows="8" readonly />
          </div>
        </div>
      </div>
    </ElDialog>
  </div>
</template>

<style scoped>
.provider-config-page {
  padding: 16px;
  background: var(--el-bg-color-page);
  min-height: 100vh;
}

.page-header {
  margin-bottom: 16px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section h2 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
  font-size: 20px;
  font-weight: 600;
}

.subtitle {
  margin: 0;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.action-section {
  display: flex;
  gap: 12px;
}

.provider-select-card {
  margin-bottom: 16px;
}

.no-provider-selected {
  padding: 20px;
  text-align: center;
}

.interface-details {
  padding: 16px 0;
}

.interface-config {
  margin-top: 24px;
}

.interface-config h4 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
}

.test-result {
  padding: 16px 0;
}

.test-details {
  margin-top: 20px;
}

.test-details h4 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
}

.response-data {
  margin-top: 16px;
}

.response-data h5 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
}

.mapping-content,
.test-content {
  padding: 20px;
  text-align: center;
  color: var(--el-text-color-placeholder);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .provider-config-page {
    padding: 12px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-section {
    justify-content: center;
  }
}
</style>
