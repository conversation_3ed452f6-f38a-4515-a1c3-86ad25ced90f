<script setup lang="ts">
import { computed, defineEmits, defineProps, onMounted, reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import { ArrowDown, ArrowRight, QuestionFilled } from '@element-plus/icons-vue';
import {
  fetchProviderUnifiedConfig,
  fetchSaveProviderUnifiedConfig,
  fetchTestUnifiedConfig
} from '@/service/api/provider';

// 定义props
const props = defineProps<{
  providerId: number;
}>();

// 定义emits
const emit = defineEmits<{
  close: [];
  save: [config: any];
}>();

// 响应式数据
const selectedInterface = ref('query');
const expandedGroups = ref(['auth', 'student']);
const newMappingField = ref('');
const newResponseField = ref('');
const newResponseProviderField = ref('');
const saving = ref(false);
const testing = ref(false);
const testDialogVisible = ref(false);

const requestMapping = reactive<any>({});
const responseMapping = reactive<any>({
  success_condition: {
    field: 'code',
    condition: 'eq',
    value: 0
  },
  message_field: 'msg',
  data_field: 'data',
  field_mapping: {}
});

const testResult = reactive<any>({
  success: false,
  data: null,
  error: null
});

// 接口类型
const interfaceTypes = [
  { label: '查课接口', value: 'query' },
  { label: '下单接口', value: 'order' },
  { label: '同步接口', value: 'sync' },
  { label: '补刷接口', value: 'refill' },
  { label: '改密接口', value: 'change_password' },
  { label: '余额查询', value: 'get_balance' }
];

// 转换函数
const transformFunctions = [
  { label: '转字符串', value: 'toString' },
  { label: '转数字', value: 'toNumber' },
  { label: '转布尔值', value: 'toBoolean' },
  { label: '转大写', value: 'toUpperCase' },
  { label: '转小写', value: 'toLowerCase' },
  { label: '去空格', value: 'trim' },
  { label: 'Base64编码', value: 'encodeBase64' },
  { label: 'Base64解码', value: 'decodeBase64' },
  { label: 'MD5加密', value: 'md5' },
  { label: 'SHA256加密', value: 'sha256' }
];

// 字段分组
const fieldGroups = ref([
  {
    name: 'auth',
    label: '认证字段',
    fields: [
      { name: 'api_uid', label: '货源用户ID', description: '货源平台的用户ID或账号' },
      { name: 'api_key', label: '货源密钥', description: '货源平台的API密钥或密码' },
      { name: 'token', label: '访问令牌', description: '可选的访问令牌' }
    ]
  },
  {
    name: 'student',
    label: '学生信息',
    fields: [
      { name: 'username', label: '学生账号', description: '学生的登录账号' },
      { name: 'password', label: '学生密码', description: '学生的登录密码' },
      { name: 'school', label: '学校名称', description: '学生所在学校的名称' },
      { name: 'platform', label: '平台标识', description: '学习平台的标识或CID' }
    ]
  },
  {
    name: 'course',
    label: '课程信息',
    fields: [
      { name: 'course_id', label: '课程ID', description: '课程的唯一标识' },
      { name: 'course_name', label: '课程名称', description: '课程的名称' },
      { name: 'teacher', label: '教师姓名', description: '课程教师的姓名' },
      { name: 'credit', label: '学分', description: '课程的学分' }
    ]
  },
  {
    name: 'order',
    label: '订单信息',
    fields: [
      { name: 'upstream_order_id', label: '上游订单ID', description: '货源平台返回的订单ID' },
      { name: 'new_password', label: '新密码', description: '修改密码时的新密码' }
    ]
  }
]);

// 响应字段
const responseFields = [
  'upstream_order_id',
  'status',
  'progress',
  'remarks',
  'course_name',
  'username',
  'school',
  'balance',
  'courses',
  'course_id',
  'teacher',
  'credit'
];

// 计算属性
const availableFields = computed(() => {
  const mapped = Object.keys(requestMapping);
  return fieldGroups.value.flatMap(group => group.fields).filter(field => !mapped.includes(field.name));
});

// 方法
const toggleGroup = (groupName: string) => {
  const index = expandedGroups.value.indexOf(groupName);
  if (index > -1) {
    expandedGroups.value.splice(index, 1);
  } else {
    expandedGroups.value.push(groupName);
  }
};

const isRequiredField = (fieldName: string): boolean => {
  const requiredFields: Record<string, string[]> = {
    query: ['api_uid', 'api_key', 'username', 'password', 'school', 'platform'],
    order: ['api_uid', 'api_key', 'username', 'password', 'school', 'platform', 'course_id', 'course_name'],
    sync: ['api_uid', 'api_key', 'upstream_order_id'],
    refill: ['api_uid', 'api_key', 'upstream_order_id'],
    change_password: ['api_uid', 'api_key', 'upstream_order_id', 'new_password'],
    get_balance: ['api_uid', 'api_key']
  };

  return requiredFields[selectedInterface.value]?.includes(fieldName) || false;
};

const isMapped = (fieldName: string): boolean => {
  return Object.keys(requestMapping).includes(fieldName);
};

const addMapping = () => {
  if (!newMappingField.value) return;

  requestMapping[newMappingField.value] = {
    provider_field: '',
    required: isRequiredField(newMappingField.value),
    default_value: '',
    transform: '',
    description: ''
  };

  newMappingField.value = '';
};

const removeMapping = (standardField: string) => {
  delete requestMapping[standardField];
};

const addResponseMapping = () => {
  if (!newResponseField.value || !newResponseProviderField.value) return;

  responseMapping.field_mapping[newResponseField.value] = {
    provider_field: newResponseProviderField.value,
    transform: ''
  };

  newResponseField.value = '';
  newResponseProviderField.value = '';
};

const removeResponseMapping = (standardField: string) => {
  delete responseMapping.field_mapping[standardField];
};

const onInterfaceChange = () => {
  // 接口切换时可以加载对应的配置
  loadInterfaceConfig();
};

const loadInterfaceConfig = async () => {
  try {
    const response = await fetchProviderUnifiedConfig(props.providerId);

    if (response.success && response.data.config) {
      const config = response.data.config;
      const interfaceConfig = config.interfaces[selectedInterface.value];

      if (interfaceConfig) {
        // 加载请求映射
        Object.keys(requestMapping).forEach(key => delete requestMapping[key]);
        Object.assign(requestMapping, interfaceConfig.request_mapping || {});

        // 加载响应映射
        Object.assign(
          responseMapping,
          interfaceConfig.response_mapping || {
            success_condition: { field: 'code', condition: 'eq', value: 0 },
            message_field: 'msg',
            data_field: 'data',
            field_mapping: {}
          }
        );
      }
    }
  } catch (error: any) {
    console.error('加载接口配置失败:', error);
    ElMessage.error('加载配置失败');
  }
};

const saveConfig = async () => {
  saving.value = true;
  try {
    // 构建配置对象
    const config = {
      version: '1.0.0',
      provider_info: {
        provider_id: props.providerId,
        provider_name: '货源名称',
        provider_code: 'provider_code'
      },
      auth: {
        api_uid: '',
        api_key: ''
      },
      interfaces: {
        [selectedInterface.value]: {
          enabled: true,
          http: {
            method: 'POST',
            endpoint: '/api.php',
            content_type: 'form'
          },
          request_mapping: requestMapping,
          response_mapping: responseMapping
        }
      },
      global_settings: {
        retry_count: 3,
        retry_delay: 1000,
        rate_limit: 10,
        debug_mode: false
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    await fetchSaveProviderUnifiedConfig(props.providerId, config);
    ElMessage.success('配置保存成功');
    emit('save', config);
  } catch (error: any) {
    console.error('保存配置失败:', error);
    ElMessage.error('配置保存失败');
  } finally {
    saving.value = false;
  }
};

const testConfig = async () => {
  testing.value = true;
  try {
    // 构建测试数据
    const testData: any = {};

    // 根据接口类型构建测试数据
    switch (selectedInterface.value) {
      case 'query':
        testData.username = 'test123';
        testData.password = 'pass123';
        testData.school = '测试学校';
        testData.platform = 'xuetong';
        break;
      case 'order':
        testData.username = 'test123';
        testData.password = 'pass123';
        testData.school = '测试学校';
        testData.platform = 'xuetong';
        testData.course_id = 'course_123';
        testData.course_name = '测试课程';
        break;
      case 'sync':
        testData.upstream_order_id = '210105';
        break;
    }

    const response = await fetchTestUnifiedConfig(props.providerId, selectedInterface.value, testData);

    testResult.success = response.success;
    testResult.data = response.data;
    testResult.error = response.success ? null : response.message;

    testDialogVisible.value = true;
  } catch (error: any) {
    testResult.success = false;
    testResult.data = null;
    testResult.error = error.message || '测试失败';
    testDialogVisible.value = true;
  } finally {
    testing.value = false;
  }
};

const resetConfig = () => {
  Object.keys(requestMapping).forEach(key => delete requestMapping[key]);
  Object.keys(responseMapping.field_mapping).forEach(key => delete responseMapping.field_mapping[key]);

  responseMapping.success_condition = {
    field: 'code',
    condition: 'eq',
    value: 0
  };
  responseMapping.message_field = 'msg';
  responseMapping.data_field = 'data';

  ElMessage.success('配置已重置');
};

const exportConfig = () => {
  try {
    const config = {
      version: '1.0.0',
      interface_type: selectedInterface.value,
      request_mapping: requestMapping,
      response_mapping: responseMapping,
      export_time: new Date().toISOString()
    };

    const dataStr = JSON.stringify(config, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });

    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `${selectedInterface.value}_config_${new Date().getTime()}.json`;
    link.click();

    ElMessage.success('配置导出成功');
  } catch (error: any) {
    ElMessage.error(`配置导出失败: ${error.message}`);
  }
};

const importConfig = () => {
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.json';

  input.onchange = (event: any) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e: any) => {
      try {
        const config = JSON.parse(e.target.result);

        // 验证配置格式
        if (!config.interface_type || !config.request_mapping || !config.response_mapping) {
          throw new Error('配置文件格式不正确');
        }

        // 应用配置
        selectedInterface.value = config.interface_type;

        // 清空现有配置
        Object.keys(requestMapping).forEach(key => delete requestMapping[key]);
        Object.assign(requestMapping, config.request_mapping);

        Object.assign(responseMapping, config.response_mapping);

        ElMessage.success('配置导入成功');
      } catch (error: any) {
        ElMessage.error(`配置导入失败: ${error.message}`);
      }
    };

    reader.readAsText(file);
  };

  input.click();
};

// 生命周期
onMounted(() => {
  loadInterfaceConfig();
});
</script>

<template>
  <div class="unified-mapping-config">
    <ElCard class="config-header">
      <div class="header-content">
        <h2>统一字段映射配置</h2>
        <p>配置标准字段与货源字段的映射关系</p>
      </div>
      <div class="header-actions">
        <ElButton type="primary" :loading="saving" @click="saveConfig">保存配置</ElButton>
        <ElButton :loading="testing" @click="testConfig">测试配置</ElButton>
        <ElButton @click="resetConfig">重置配置</ElButton>
        <ElButton @click="exportConfig">导出配置</ElButton>
        <ElButton @click="importConfig">导入配置</ElButton>
      </div>
    </ElCard>

    <div class="config-content">
      <!-- 接口类型选择 -->
      <ElCard class="interface-selector">
        <template #header>
          <span>选择接口类型</span>
        </template>
        <ElRadioGroup v-model="selectedInterface" @change="onInterfaceChange">
          <ElRadioButton v-for="iface in interfaceTypes" :key="iface.value" :label="iface.value">
            {{ iface.label }}
          </ElRadioButton>
        </ElRadioGroup>
      </ElCard>

      <!-- 映射配置区域 -->
      <div class="mapping-area">
        <!-- 请求映射 -->
        <ElCard class="mapping-section">
          <template #header>
            <div class="section-header">
              <span>请求字段映射</span>
              <ElTooltip content="配置标准字段如何映射到货源接口字段">
                <ElIcon><QuestionFilled /></ElIcon>
              </ElTooltip>
            </div>
          </template>

          <div class="mapping-container">
            <!-- 标准字段列表 -->
            <div class="standard-fields">
              <h4>项目标准字段</h4>
              <div class="field-groups">
                <div v-for="group in fieldGroups" :key="group.name" class="field-group">
                  <div class="group-header" @click="toggleGroup(group.name)">
                    <ElIcon>
                      <ArrowRight v-if="!expandedGroups.includes(group.name)" />
                      <ArrowDown v-else />
                    </ElIcon>
                    <span>{{ group.label }}</span>
                    <ElTag size="small">{{ group.fields.length }}</ElTag>
                  </div>

                  <div v-show="expandedGroups.includes(group.name)" class="group-fields">
                    <div
                      v-for="field in group.fields"
                      :key="field.name"
                      class="standard-field"
                      :class="{
                        required: isRequiredField(field.name),
                        mapped: isMapped(field.name)
                      }"
                    >
                      <div class="field-info">
                        <span class="field-name">{{ field.name }}</span>
                        <span class="field-label">{{ field.label }}</span>
                        <ElTag v-if="isRequiredField(field.name)" type="danger" size="small">必填</ElTag>
                      </div>
                      <div class="field-description">{{ field.description }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 映射配置 -->
            <div class="mapping-config">
              <h4>字段映射配置</h4>
              <div class="mapping-list">
                <div v-for="(mapping, standardField) in requestMapping" :key="standardField" class="mapping-item">
                  <div class="mapping-header">
                    <span class="standard-field-name">{{ standardField }}</span>
                    <ElButton type="danger" size="small" text @click="removeMapping(String(standardField))">
                      删除
                    </ElButton>
                  </div>

                  <div class="mapping-form">
                    <ElFormItem label="货源字段名" required>
                      <ElInput v-model="mapping.provider_field" placeholder="请输入货源字段名" />
                    </ElFormItem>

                    <ElFormItem label="是否必填">
                      <ElSwitch v-model="mapping.required" />
                    </ElFormItem>

                    <ElFormItem label="默认值">
                      <ElInput v-model="mapping.default_value" placeholder="可选的默认值" />
                    </ElFormItem>

                    <ElFormItem label="数据转换">
                      <ElSelect v-model="mapping.transform" placeholder="选择转换函数" clearable>
                        <ElOption
                          v-for="transform in transformFunctions"
                          :key="transform.value"
                          :label="transform.label"
                          :value="transform.value"
                        />
                      </ElSelect>
                    </ElFormItem>

                    <ElFormItem label="字段描述">
                      <ElInput v-model="mapping.description" type="textarea" :rows="2" placeholder="可选的字段描述" />
                    </ElFormItem>
                  </div>
                </div>
              </div>

              <!-- 添加映射按钮 -->
              <div class="add-mapping">
                <ElSelect
                  v-model="newMappingField"
                  placeholder="选择要映射的标准字段"
                  style="width: 200px; margin-right: 10px"
                >
                  <ElOption
                    v-for="field in availableFields"
                    :key="field.name"
                    :label="`${field.name} (${field.label})`"
                    :value="field.name"
                  />
                </ElSelect>
                <ElButton type="primary" :disabled="!newMappingField" @click="addMapping">添加映射</ElButton>
              </div>
            </div>
          </div>
        </ElCard>

        <!-- 响应映射 -->
        <ElCard class="mapping-section">
          <template #header>
            <div class="section-header">
              <span>响应字段映射</span>
              <ElTooltip content="配置货源响应字段如何映射到标准字段">
                <ElIcon><QuestionFilled /></ElIcon>
              </ElTooltip>
            </div>
          </template>

          <div class="response-mapping">
            <!-- 成功条件配置 -->
            <ElFormItem label="成功判断字段" required>
              <ElInput v-model="responseMapping.success_condition.field" placeholder="如: code" />
            </ElFormItem>

            <ElFormItem label="成功条件" required>
              <ElSelect v-model="responseMapping.success_condition.condition">
                <ElOption label="等于 (=)" value="eq" />
                <ElOption label="不等于 (≠)" value="ne" />
                <ElOption label="大于 (>)" value="gt" />
                <ElOption label="大于等于 (≥)" value="gte" />
                <ElOption label="小于 (<)" value="lt" />
                <ElOption label="小于等于 (≤)" value="lte" />
                <ElOption label="包含于" value="in" />
                <ElOption label="包含" value="contains" />
              </ElSelect>
            </ElFormItem>

            <ElFormItem label="期望值" required>
              <ElInput v-model="responseMapping.success_condition.value" placeholder="如: 0 或 success" />
            </ElFormItem>

            <ElFormItem label="消息字段" required>
              <ElInput v-model="responseMapping.message_field" placeholder="如: msg 或 message" />
            </ElFormItem>

            <ElFormItem label="数据字段">
              <ElInput v-model="responseMapping.data_field" placeholder="如: data (可选)" />
            </ElFormItem>

            <!-- 字段映射列表 -->
            <div class="response-field-mapping">
              <h5>响应字段映射</h5>
              <div
                v-for="(mapping, standardField) in responseMapping.field_mapping"
                :key="standardField"
                class="response-mapping-item"
              >
                <ElFormItem :label="String(standardField)">
                  <div class="response-mapping-config">
                    <ElInput
                      v-model="mapping.provider_field"
                      placeholder="货源字段名"
                      style="flex: 1; margin-right: 10px"
                    />
                    <ElSelect
                      v-model="mapping.transform"
                      placeholder="转换函数"
                      style="width: 120px; margin-right: 10px"
                      clearable
                    >
                      <ElOption
                        v-for="transform in transformFunctions"
                        :key="transform.value"
                        :label="transform.label"
                        :value="transform.value"
                      />
                    </ElSelect>
                    <ElButton type="danger" size="small" text @click="removeResponseMapping(String(standardField))">
                      删除
                    </ElButton>
                  </div>
                </ElFormItem>
              </div>

              <!-- 添加响应映射 -->
              <div class="add-response-mapping">
                <ElSelect
                  v-model="newResponseField"
                  placeholder="选择标准字段"
                  style="width: 150px; margin-right: 10px"
                >
                  <ElOption v-for="field in responseFields" :key="field" :label="field" :value="field" />
                </ElSelect>
                <ElInput
                  v-model="newResponseProviderField"
                  placeholder="货源字段名"
                  style="width: 150px; margin-right: 10px"
                />
                <ElButton
                  type="primary"
                  :disabled="!newResponseField || !newResponseProviderField"
                  @click="addResponseMapping"
                >
                  添加
                </ElButton>
              </div>
            </div>
          </div>
        </ElCard>
      </div>
    </div>

    <!-- 测试结果对话框 -->
    <ElDialog v-model="testDialogVisible" title="配置测试结果" width="60%">
      <div class="test-result">
        <ElAlert
          :title="testResult.success ? '测试成功' : '测试失败'"
          :type="testResult.success ? 'success' : 'error'"
          :closable="false"
        />

        <div v-if="testResult.data" class="test-data">
          <h4>测试数据</h4>
          <ElInput :model-value="JSON.stringify(testResult.data, null, 2)" type="textarea" :rows="10" readonly />
        </div>

        <div v-if="testResult.error" class="test-error">
          <h4>错误信息</h4>
          <ElAlert :title="testResult.error" type="error" :closable="false" />
        </div>
      </div>
    </ElDialog>
  </div>
</template>

<style scoped>
.unified-mapping-config {
  padding: 20px;
}

.config-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.header-content h2 {
  margin: 0;
  color: var(--el-text-color-primary);
}

.header-content p {
  margin: 0;
  color: var(--el-text-color-regular);
}

.header-actions {
  margin-top: 16px;
  display: flex;
  gap: 12px;
}

.interface-selector {
  margin-bottom: 20px;
}

.mapping-area {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.mapping-section {
  width: 100%;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mapping-container {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 20px;
  min-height: 500px;
}

.standard-fields h4,
.mapping-config h4 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
}

.field-group {
  margin-bottom: 12px;
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
}

.group-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: var(--el-bg-color-page);
  cursor: pointer;
  user-select: none;
}

.group-header:hover {
  background: var(--el-fill-color-light);
}

.group-fields {
  padding: 0 12px 12px 12px;
}

.standard-field {
  padding: 8px;
  margin: 4px 0;
  border-radius: 4px;
  border: 1px solid transparent;
}

.standard-field.required {
  border-color: var(--el-color-danger-light-7);
  background: var(--el-color-danger-light-9);
}

.standard-field.mapped {
  border-color: var(--el-color-success-light-7);
  background: var(--el-color-success-light-9);
}

.field-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.field-name {
  font-family: monospace;
  font-weight: 500;
  color: var(--el-color-primary);
}

.field-label {
  color: var(--el-text-color-primary);
}

.field-description {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.mapping-list {
  max-height: 400px;
  overflow-y: auto;
}

.mapping-item {
  margin-bottom: 16px;
  padding: 16px;
  border: 1px solid var(--el-border-color);
  border-radius: 6px;
  background: var(--el-bg-color-page);
}

.mapping-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.standard-field-name {
  font-family: monospace;
  font-weight: 500;
  color: var(--el-color-primary);
}

.mapping-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.add-mapping {
  margin-top: 16px;
  display: flex;
  align-items: center;
}

.response-mapping {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
}

.response-field-mapping {
  grid-column: 1 / -1;
  margin-top: 20px;
}

.response-field-mapping h5 {
  margin: 0 0 12px 0;
  color: var(--el-text-color-primary);
}

.response-mapping-item {
  margin-bottom: 12px;
}

.response-mapping-config {
  display: flex;
  align-items: center;
}

.add-response-mapping {
  margin-top: 12px;
  display: flex;
  align-items: center;
}

.test-result {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.test-data h4,
.test-error h4 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
}
</style>
