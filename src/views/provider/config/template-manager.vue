<script setup lang="ts">
import { defineEmits, onMounted, reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { CopyDocument, Delete, Edit, Plus } from '@element-plus/icons-vue';

// 定义emits
const emit = defineEmits<{
  'template-updated': [];
}>();

// 响应式数据
const showAddTemplate = ref(false);
const showTemplateDialog = ref(false);
const editingTemplate = ref<any>(null);
const saving = ref(false);
const templateFormRef = ref();

// 模板数据
const templates = ref([
  {
    id: '29pt',
    name: '29平台模板',
    description: '适用于29平台的标准配置',
    isSystem: true,
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date(),
    interfaces: [
      {
        type: 'query',
        name: '查课接口',
        defaultEndpoint: '/api.php?act=get',
        fields: [
          { label: '用户ID', standardField: 'api_uid', providerField: 'uid', required: true },
          { label: '密钥', standardField: 'api_key', providerField: 'key', required: true },
          { label: '学生账号', standardField: 'username', providerField: 'user', required: true },
          { label: '学生密码', standardField: 'password', providerField: 'pass', required: true },
          { label: '学校名称', standardField: 'school', providerField: 'school', required: true },
          { label: '平台标识', standardField: 'platform', providerField: 'platform', required: true }
        ]
      },
      {
        type: 'order',
        name: '下单接口',
        defaultEndpoint: '/api.php?act=add',
        fields: [
          { label: '用户ID', standardField: 'api_uid', providerField: 'uid', required: true },
          { label: '密钥', standardField: 'api_key', providerField: 'key', required: true },
          { label: '学生账号', standardField: 'username', providerField: 'user', required: true },
          { label: '学生密码', standardField: 'password', providerField: 'pass', required: true },
          { label: '学校名称', standardField: 'school', providerField: 'school', required: true },
          { label: '平台标识', standardField: 'platform', providerField: 'platform', required: true },
          { label: '课程名称', standardField: 'course_name', providerField: 'kcname', required: true },
          { label: '课程ID', standardField: 'course_id', providerField: 'kcid', required: true }
        ]
      },
      {
        type: 'refill',
        name: '补刷接口',
        defaultEndpoint: '/api.php?act=budan',
        fields: [
          { label: '用户ID', standardField: 'api_uid', providerField: 'uid', required: true },
          { label: '密钥', standardField: 'api_key', providerField: 'key', required: true },
          { label: '订单ID', standardField: 'upstream_order_id', providerField: 'id', required: true }
        ]
      },
      {
        type: 'sync',
        name: '同步接口',
        defaultEndpoint: '/api.php?act=chadanoid',
        fields: [
          { label: '用户ID', standardField: 'api_uid', providerField: 'uid', required: true },
          { label: '密钥', standardField: 'api_key', providerField: 'key', required: true },
          { label: '订单ID', standardField: 'upstream_order_id', providerField: 'yid', required: true }
        ]
      }
    ]
  }
]);

// 表单数据
const templateForm = reactive({
  id: '',
  name: '',
  description: '',
  interfaces: [
    {
      type: 'query',
      name: '查课接口',
      defaultEndpoint: '/api.php?act=get',
      fields: [
        { label: '用户ID', standardField: 'api_uid', providerField: 'uid', required: true },
        { label: '密钥', standardField: 'api_key', providerField: 'key', required: true }
      ]
    }
  ]
});

// 表单验证规则
const templateRules = {
  name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  id: [
    { required: true, message: '请输入模板ID', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '模板ID只能包含字母、数字、下划线和横线', trigger: 'blur' }
  ],
  description: [{ required: true, message: '请输入模板描述', trigger: 'blur' }]
};

// 方法
const formatDate = (date: Date) => {
  return date.toLocaleDateString('zh-CN');
};

const editTemplate = (template: any) => {
  editingTemplate.value = template;
  Object.assign(templateForm, JSON.parse(JSON.stringify(template)));
  showTemplateDialog.value = true;
};

const copyTemplate = (template: any) => {
  editingTemplate.value = null;
  const copied = JSON.parse(JSON.stringify(template));
  copied.id += '_copy';
  copied.name += ' (副本)';
  copied.isSystem = false;
  Object.assign(templateForm, copied);
  showTemplateDialog.value = true;
};

const deleteTemplate = async (template: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个模板吗？', '确认删除', {
      type: 'warning'
    });

    const index = templates.value.findIndex(t => t.id === template.id);
    if (index > -1) {
      templates.value.splice(index, 1);
      ElMessage.success('模板删除成功');
      emit('template-updated');
    }
  } catch (error) {
    // 用户取消删除
  }
};

const addInterface = () => {
  templateForm.interfaces.push({
    type: 'query',
    name: '新接口',
    defaultEndpoint: '/api.php',
    fields: [
      { label: '用户ID', standardField: 'api_uid', providerField: 'uid', required: true },
      { label: '密钥', standardField: 'api_key', providerField: 'key', required: true }
    ]
  });
};

const removeInterface = (index: number) => {
  templateForm.interfaces.splice(index, 1);
};

const addField = (iface: any) => {
  iface.fields.push({
    label: '新字段',
    standardField: '',
    providerField: '',
    required: false
  });
};

const removeField = (iface: any, fieldIndex: number) => {
  iface.fields.splice(fieldIndex, 1);
};

const saveTemplate = async () => {
  if (!templateFormRef.value) return;

  try {
    await templateFormRef.value.validate();
    saving.value = true;

    // 模拟保存延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    const templateData = {
      ...templateForm,
      updatedAt: new Date(),
      createdAt: editingTemplate.value?.createdAt || new Date(),
      isSystem: editingTemplate.value?.isSystem || false
    };

    if (editingTemplate.value) {
      // 编辑现有模板
      const index = templates.value.findIndex(t => t.id === editingTemplate.value.id);
      if (index > -1) {
        templates.value[index] = templateData;
      }
    } else {
      // 新增模板
      templates.value.push(templateData);
    }

    ElMessage.success('模板保存成功');
    showTemplateDialog.value = false;
    editingTemplate.value = null;
    emit('template-updated');
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确');
  } finally {
    saving.value = false;
  }
};

const handleAddTemplate = () => {
  editingTemplate.value = null;
  Object.assign(templateForm, {
    id: '',
    name: '',
    description: '',
    interfaces: [
      {
        type: 'query',
        name: '查课接口',
        defaultEndpoint: '/api.php?act=get',
        fields: [
          { label: '用户ID', standardField: 'api_uid', providerField: 'uid', required: true },
          { label: '密钥', standardField: 'api_key', providerField: 'key', required: true }
        ]
      }
    ]
  });
  showTemplateDialog.value = true;
};

// 暴露模板数据给父组件
defineExpose({
  templates
});
</script>

<template>
  <div class="template-manager">
    <div class="manager-header">
      <h3>配置模板管理</h3>
      <ElButton type="primary" @click="handleAddTemplate">
        <ElIcon><Plus /></ElIcon>
        新增模板
      </ElButton>
    </div>

    <!-- 模板列表 -->
    <div class="template-list">
      <ElCard v-for="template in templates" :key="template.id" class="template-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span class="template-name">{{ template.name }}</span>
            <div class="card-actions">
              <ElButton size="small" @click="editTemplate(template)">
                <ElIcon><Edit /></ElIcon>
                编辑
              </ElButton>
              <ElButton size="small" @click="copyTemplate(template)">
                <ElIcon><CopyDocument /></ElIcon>
                复制
              </ElButton>
              <ElButton v-if="!template.isSystem" size="small" type="danger" @click="deleteTemplate(template)">
                <ElIcon><Delete /></ElIcon>
                删除
              </ElButton>
            </div>
          </div>
        </template>

        <div class="template-content">
          <p class="template-description">{{ template.description }}</p>
          <div class="template-interfaces">
            <ElTag
              v-for="iface in template.interfaces"
              :key="iface.type"
              size="small"
              style="margin-right: 8px; margin-bottom: 4px"
            >
              {{ iface.name }}
            </ElTag>
          </div>
          <div class="template-meta">
            <span class="meta-item">创建时间: {{ formatDate(template.createdAt) }}</span>
            <span class="meta-item">更新时间: {{ formatDate(template.updatedAt) }}</span>
          </div>
        </div>
      </ElCard>
    </div>

    <!-- 新增/编辑模板对话框 -->
    <ElDialog
      v-model="showTemplateDialog"
      :title="editingTemplate ? '编辑模板' : '新增模板'"
      width="80%"
      :close-on-click-modal="false"
    >
      <ElForm ref="templateFormRef" :model="templateForm" :rules="templateRules" label-width="120px">
        <ElFormItem label="模板名称" prop="name">
          <ElInput v-model="templateForm.name" placeholder="请输入模板名称" />
        </ElFormItem>

        <ElFormItem label="模板ID" prop="id">
          <ElInput v-model="templateForm.id" placeholder="请输入模板ID（英文）" :disabled="!!editingTemplate" />
        </ElFormItem>

        <ElFormItem label="描述" prop="description">
          <ElInput v-model="templateForm.description" type="textarea" :rows="2" placeholder="请输入模板描述" />
        </ElFormItem>

        <ElFormItem label="接口配置">
          <div class="interfaces-config">
            <div v-for="(iface, index) in templateForm.interfaces" :key="index" class="interface-item">
              <ElCard>
                <template #header>
                  <div class="interface-header">
                    <span>接口 {{ index + 1 }}</span>
                    <ElButton
                      size="small"
                      type="danger"
                      :disabled="templateForm.interfaces.length <= 1"
                      @click="removeInterface(index)"
                    >
                      删除
                    </ElButton>
                  </div>
                </template>

                <ElFormItem label="接口类型">
                  <ElSelect v-model="iface.type" placeholder="选择接口类型">
                    <ElOption label="查课接口" value="query" />
                    <ElOption label="下单接口" value="order" />
                    <ElOption label="补刷接口" value="refill" />
                    <ElOption label="同步接口" value="sync" />
                    <ElOption label="改密接口" value="change_password" />
                    <ElOption label="余额查询" value="get_balance" />
                  </ElSelect>
                </ElFormItem>

                <ElFormItem label="接口名称">
                  <ElInput v-model="iface.name" placeholder="请输入接口名称" />
                </ElFormItem>

                <ElFormItem label="默认端点">
                  <ElInput v-model="iface.defaultEndpoint" placeholder="如：/api.php?act=get" />
                </ElFormItem>

                <ElFormItem label="字段配置">
                  <div class="fields-config">
                    <div v-for="(field, fieldIndex) in iface.fields" :key="fieldIndex" class="field-item">
                      <ElRow :gutter="12">
                        <ElCol :span="6">
                          <ElInput v-model="field.label" placeholder="字段标签" />
                        </ElCol>
                        <ElCol :span="6">
                          <ElInput v-model="field.standardField" placeholder="标准字段名" />
                        </ElCol>
                        <ElCol :span="6">
                          <ElInput v-model="field.providerField" placeholder="货源字段名" />
                        </ElCol>
                        <ElCol :span="4">
                          <ElCheckbox v-model="field.required">必填</ElCheckbox>
                        </ElCol>
                        <ElCol :span="2">
                          <ElButton
                            size="small"
                            type="danger"
                            :disabled="iface.fields.length <= 1"
                            @click="removeField(iface, fieldIndex)"
                          >
                            删除
                          </ElButton>
                        </ElCol>
                      </ElRow>
                    </div>
                    <ElButton size="small" @click="addField(iface)">添加字段</ElButton>
                  </div>
                </ElFormItem>
              </ElCard>
            </div>
            <ElButton @click="addInterface">添加接口</ElButton>
          </div>
        </ElFormItem>
      </ElForm>

      <template #footer>
        <ElButton @click="showTemplateDialog = false">取消</ElButton>
        <ElButton type="primary" :loading="saving" @click="saveTemplate">保存</ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<style scoped>
.template-manager {
  padding: 20px;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.manager-header h3 {
  margin: 0;
  color: var(--el-text-color-primary);
}

.template-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.template-card {
  height: fit-content;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-name {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.card-actions {
  display: flex;
  gap: 8px;
}

.template-content {
  padding: 16px 0;
}

.template-description {
  margin: 0 0 12px 0;
  color: var(--el-text-color-regular);
  line-height: 1.5;
}

.template-interfaces {
  margin-bottom: 16px;
}

.template-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.meta-item {
  font-size: 12px;
  color: var(--el-text-color-placeholder);
}

.interfaces-config {
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  padding: 16px;
  background: var(--el-bg-color-page);
}

.interface-item {
  margin-bottom: 16px;
}

.interface-item:last-child {
  margin-bottom: 0;
}

.interface-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fields-config {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  padding: 12px;
  background: white;
}

.field-item {
  margin-bottom: 12px;
}

.field-item:last-child {
  margin-bottom: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .template-list {
    grid-template-columns: 1fr;
  }

  .manager-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .card-actions {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
