<script setup lang="ts">
import { defineEmits, reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import type { FormInstance, FormRules } from 'element-plus';

// 定义emits
const emit = defineEmits<{
  save: [data: any];
  cancel: [];
}>();

// 响应式数据
const formRef = ref<FormInstance>();
const saving = ref(false);

const form = reactive({
  name: '',
  code: '',
  apiUrl: '',
  uid: '',
  key: '',
  description: '',
  enabled: true
});

const rules: FormRules = {
  name: [{ required: true, message: '请输入货源名称', trigger: 'blur' }],
  code: [
    { required: true, message: '请输入货源代码', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_-]+$/, message: '货源代码只能包含字母、数字、下划线和横线', trigger: 'blur' }
  ],
  apiUrl: [
    { required: true, message: '请输入API地址', trigger: 'blur' },
    { type: 'url', message: '请输入正确的URL格式', trigger: 'blur' }
  ],
  uid: [{ required: true, message: '请输入用户ID', trigger: 'blur' }],
  key: [{ required: true, message: '请输入密钥', trigger: 'blur' }]
};

// 方法
const handleSave = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    saving.value = true;

    // 模拟保存延迟
    await new Promise(resolve => setTimeout(resolve, 1000));

    emit('save', { ...form });
    ElMessage.success('货源保存成功');
  } catch (error) {
    ElMessage.error('请检查表单填写是否正确');
  } finally {
    saving.value = false;
  }
};

const handleCancel = () => {
  emit('cancel');
};
</script>

<template>
  <div class="provider-form">
    <ElForm ref="formRef" :model="form" :rules="rules" label-width="120px">
      <ElFormItem label="货源名称" prop="name" required>
        <ElInput v-model="form.name" placeholder="请输入货源名称" />
      </ElFormItem>

      <ElFormItem label="货源代码" prop="code" required>
        <ElInput v-model="form.code" placeholder="请输入货源代码，如：29pt" />
      </ElFormItem>

      <ElFormItem label="API地址" prop="apiUrl" required>
        <ElInput v-model="form.apiUrl" placeholder="请输入API地址，如：https://freedomp.icu" />
      </ElFormItem>

      <ElFormItem label="用户ID" prop="uid" required>
        <ElInput v-model="form.uid" placeholder="请输入货源用户ID" />
      </ElFormItem>

      <ElFormItem label="密钥" prop="key" required>
        <ElInput v-model="form.key" type="password" placeholder="请输入货源密钥" show-password />
      </ElFormItem>

      <ElFormItem label="描述" prop="description">
        <ElInput v-model="form.description" type="textarea" :rows="3" placeholder="请输入货源描述（可选）" />
      </ElFormItem>

      <ElFormItem label="状态" prop="enabled">
        <ElSwitch v-model="form.enabled" active-text="启用" inactive-text="禁用" />
      </ElFormItem>
    </ElForm>

    <div class="form-actions">
      <ElButton @click="handleCancel">取消</ElButton>
      <ElButton type="primary" :loading="saving" @click="handleSave">保存</ElButton>
    </div>
  </div>
</template>

<style scoped>
.provider-form {
  padding: 20px 0;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-light);
}
</style>
