<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus';
import {
  PROVIDER_STATUS_TEXT,
  PROVIDER_STATUS_TYPE,
  ProviderStatus,
  fetchCreateProvider,
  fetchDeleteProvider,
  fetchProviderBalance,
  fetchProviderList,
  fetchTestProviderConnection,
  fetchUpdateProvider
} from '@/service/api/provider';
import { useTableOperate } from '@/hooks/common/table-pagination';
import FilterDrawer from '@/components/responsive/FilterDrawer.vue';

defineOptions({
  name: 'ProviderManagement'
});

// 使用表格操作Hook
const { pagination, loading, tableData, handlePageChange, handleSizeChange, fetchTableData, refreshTable } =
  useTableOperate({
    fetchData: async params => {
      const response = await fetchProviderList({
        ...params,
        ...searchParams
      });
      return {
        data: response.list || [],
        total: response.pagination?.total || 0
      };
    }
  });

// 响应式数据
const modalVisible = ref(false);
const balanceDrawerVisible = ref(false);
const isEdit = ref(false);
const saving = ref(false);
const selectedProvider = ref<any>(null);
const balanceInfo = ref<any>(null);
const activeCodeTab = ref('query');

// 表单引用
const formRef = ref<FormInstance | null>(null);

// 搜索参数
const searchParams = reactive({
  keyword: '',
  status: undefined as number | undefined
});

// 供应商表单
const providerForm = reactive({
  providerId: null as number | null,
  code: '',
  name: '',
  logoUrl: '',
  apiUrl: '',
  username: '',
  password: '',
  token: '',
  ipWhitelist: '',
  apiConfig: null,
  customQueryCode: '',
  customOrderCode: '',
  customSyncCode: '',
  status: 1
});

// 小屏筛选抽屉
const filterDrawerVisible = ref(false);

function onMoreAction(cmd: string, row: any) {
  switch (cmd) {
    case 'test':
      handleTestConnection(row);
      break;
    case 'balance':
      handleCheckBalance(row);
      break;
    case 'delete':
      handleDelete(row.providerId);
      break;
    default:
      ElMessage.warning('未知操作');
  }
}


// 选项数据
const statusOptions = Object.entries(PROVIDER_STATUS_TEXT).map(([value, label]) => ({
  label,
  value: Number(value)
}));

// 表单验证规则
const formRules: FormRules = {
  code: [{ required: true, message: '请输入供应商代码', trigger: 'blur' }],
  name: [{ required: true, message: '请输入供应商名称', trigger: 'blur' }]
};

// 处理函数
function handleSearch() {
  fetchTableData();
}

function handleReset() {
  Object.assign(searchParams, {
    keyword: '',
    status: undefined
  });
  fetchTableData();
}

function handleCreate() {
  isEdit.value = false;
  Object.assign(providerForm, {
    providerId: null,
    code: '',
    name: '',
    logoUrl: '',
    apiUrl: '',
    username: '',
    password: '',
    token: '',
    ipWhitelist: '',
    apiConfig: null,
    status: 1
  });
  modalVisible.value = true;
}

function handleEdit(row: any) {
  isEdit.value = true;
  Object.assign(providerForm, {
    providerId: row.providerId,
    code: row.code,
    name: row.name,
    logoUrl: row.logoUrl,
    apiUrl: row.apiUrl,
    username: row.username,
    password: '', // 不显示原密码
    token: '', // 不显示原token
    ipWhitelist: row.ipWhitelist,
    apiConfig: row.apiConfig,
    status: row.status
  });
  modalVisible.value = true;
}

async function handleDelete(providerId: number) {
  try {
    await ElMessageBox.confirm('确定要删除这个供应商吗？', '确认删除', {
      type: 'warning'
    });

    await fetchDeleteProvider(providerId);
    ElMessage.success('删除成功');
    refreshTable();
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除失败:', error);
      ElMessage.error(error.message || '删除失败');
    }
  }
}

// API测试连接
async function handleTestConnection(row: any) {
  try {
    const loading = ElLoading.service({
      text: '正在测试连接...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    const response = await fetchTestProviderConnection(row.providerId);
    loading.close();

    if (response.success) {
      ElMessage.success(`连接测试成功 (${response.responseTime}ms)`);
    } else {
      ElMessage.error(`连接测试失败: ${response.message}`);
    }
  } catch (error: any) {
    console.error('连接测试失败:', error);
    ElMessage.error(error.message || '连接测试失败');
  }
}

// 查询余额
async function handleCheckBalance(row: any) {
  try {
    const loading = ElLoading.service({
      text: '正在查询余额...',
      background: 'rgba(0, 0, 0, 0.7)'
    });
    const response = await fetchProviderBalance(row.providerId);
    loading.close();

    console.log('余额查询响应:', response);

    // 当请求成功时，response就是data部分（由transformBackendResponse处理）
    // 如果response有balance字段，说明查询成功
    const isSuccess = response && typeof response.balance === 'number';

    balanceInfo.value = {
      success: isSuccess,
      balance: response?.balance || row.balance,
      userName: response?.userName || '',
      lastUpdate: response?.lastUpdate || new Date().toLocaleString('zh-CN'),
      message: isSuccess ? '余额查询成功' : '余额查询失败',
      platform: row.name?.includes('29平台') ? '29平台' : ''
    };

    selectedProvider.value = {
      ...row,
      balance: response?.balance || row.balance,
      lastUpdateTime: response?.lastUpdate || new Date().toLocaleString('zh-CN')
    };
    balanceDrawerVisible.value = true;

    if (isSuccess) {
      ElMessage.success('余额查询成功');

      // 更新表格中的余额显示
      const index = tableData.value.findIndex((item: any) => item.providerId === row.providerId);
      if (index !== -1) {
        tableData.value[index].balance = response.balance;
      }
    } else {
      ElMessage.warning('余额查询失败: 未知错误');
    }
  } catch (error: any) {
    console.error('查询余额失败:', error);
    ElMessage.error(error.message || '查询余额失败');
  }
}

// 前往充值（29平台）
function handleGoToRecharge() {
  if (selectedProvider.value?.apiUrl) {
    const rechargeUrl = `${selectedProvider.value.apiUrl.replace('/api.php', '')}/user/`;
    window.open(rechargeUrl, '_blank');
    ElMessage.info('已打开29平台充值页面');
  } else {
    ElMessage.warning('无法获取充值地址');
  }
}

// 保存供应商
async function handleSave() {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    saving.value = true;

    if (isEdit.value) {
      // 更新供应商
      const updateData: any = {
        code: providerForm.code,
        name: providerForm.name,
        logoUrl: providerForm.logoUrl,
        apiUrl: providerForm.apiUrl,
        username: providerForm.username,
        ipWhitelist: providerForm.ipWhitelist,
        apiConfig: providerForm.apiConfig,
        status: providerForm.status
      };

      // 只有输入了新密码才更新
      if (providerForm.password) {
        updateData.password = providerForm.password;
      }

      // 只有输入了新token才更新
      if (providerForm.token) {
        updateData.token = providerForm.token;
      }

      await fetchUpdateProvider(providerForm.providerId!, updateData);
      ElMessage.success('供应商更新成功');
    } else {
      // 创建供应商
      await fetchCreateProvider({
        code: providerForm.code,
        name: providerForm.name,
        logoUrl: providerForm.logoUrl,
        apiUrl: providerForm.apiUrl,
        username: providerForm.username,
        password: providerForm.password,
        token: providerForm.token,
        ipWhitelist: providerForm.ipWhitelist,
        apiConfig: providerForm.apiConfig,
        status: providerForm.status
      });
      ElMessage.success('供应商创建成功');
    }

    modalVisible.value = false;
    refreshTable();
  } catch (error: any) {
    console.error('保存失败:', error);
    ElMessage.error(error.message || '保存失败');
  } finally {
    saving.value = false;
  }
}

// 生命周期
onMounted(() => {
  fetchTableData();
});
</script>

<template>
  <div class="flex flex-col gap-16px p-16px">
    <ElCard>
      <template #header>
        <div class="flex items-center justify-between">
          <span class="text-lg font-medium">货源管理</span>
          <div class="flex gap-8px">
            <ElButton v-permission="'provider:create'" type="primary" @click="handleCreate">
              <template #icon>
                <icon-ic-round-plus class="text-icon" />
              </template>
              新增货源
            </ElButton>
          </div>
        </div>
      </template>

      <!-- 搜索栏（移动端收纳） -->
      <div class="flex flex-wrap items-center gap-16px pb-12px">
        <ElInput v-model="searchParams.keyword" placeholder="搜索货源名称" clearable class="w-240px">
          <template #prefix>
            <icon-ic-round-search class="text-icon" />
          </template>
        </ElInput>

        <ElSelect v-model="searchParams.status" placeholder="状态" clearable class="w-100px hidden md:inline-flex">
          <ElOption v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
        </ElSelect>

        <ElButton type="primary" class="hidden md:inline-flex" @click="handleSearch">
          <template #icon>
            <icon-ic-round-search class="text-icon" />
          </template>
          搜索
        </ElButton>

        <ElButton class="hidden md:inline-flex" @click="handleReset">
          <template #icon>
            <icon-ic-round-refresh class="text-icon" />
          </template>
          重置
        </ElButton>

        <!-- 小屏筛选抽屉入口 -->
        <ElButton class="md:hidden" @click="filterDrawerVisible = true">
          <template #icon>
            <icon-ic-round-tune class="text-icon" />
          </template>
          筛选
        </ElButton>

        <FilterDrawer v-model="filterDrawerVisible" title="筛选">
          <div class="space-y-12px">
            <ElSelect v-model="searchParams.status" placeholder="状态" clearable class="w-full">
              <ElOption v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
            </ElSelect>
            <div class="flex justify-end gap-8px">
              <ElButton @click="handleReset">重置</ElButton>
              <ElButton type="primary" @click="handleSearch">应用</ElButton>
            </div>
          </div>
        </FilterDrawer>
      </div>

      <!-- 数据表格（仅列表） -->
      <div class="w-full overflow-x-auto">
        <ElTable v-loading="loading" :data="tableData" style="width: 100%" max-height="70vh">
          <ElTableColumn prop="name" label="供应商名称" width="150" show-overflow-tooltip />
          <ElTableColumn prop="code" label="供应商代码" width="120" />
          <ElTableColumn prop="apiUrl" label="API地址" width="200" show-overflow-tooltip />
          <ElTableColumn prop="balance" label="余额" width="120">
            <template #default="{ row }">
              <div class="flex items-center gap-8px">
                <span>¥{{ row.balance }}</span>
                <ElButton v-permission="'provider:view'" size="small" type="info" @click="handleCheckBalance(row)">查询</ElButton>
              </div>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="status" label="状态" width="80">
            <template #default="{ row }">
              <ElTag :type="PROVIDER_STATUS_TYPE[row.status] as any">
                {{ PROVIDER_STATUS_TEXT[row.status] }}
              </ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn prop="createTime" label="创建时间" width="160" />
          <ElTableColumn label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <div class="flex gap-8px">
                <ElButton v-permission="'provider:edit'" size="small" type="primary" @click="handleEdit(row)">编辑</ElButton>
                <ElDropdown @command="cmd => onMoreAction(cmd, row)">
                  <ElButton size="small" type="info">更多</ElButton>
                  <template #dropdown>
                    <ElDropdownMenu>
                      <ElDropdownItem v-permission="'provider:test'" command="test">测试连接</ElDropdownItem>
                      <ElDropdownItem v-permission="'provider:view'" command="balance">查询余额</ElDropdownItem>
                      <ElDropdownItem v-permission="'provider:delete'" divided command="delete">删除</ElDropdownItem>
                    </ElDropdownMenu>
                  </template>
                </ElDropdown>
              </div>
            </template>
          </ElTableColumn>
        </ElTable>
      </div>

      <!-- 分页 -->
      <div class="mt-16px flex justify-center md:justify-end">
        <ElPagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </ElCard>

    <!-- 供应商表单模态框 -->
    <ElDialog
      v-model="modalVisible"
      :title="isEdit ? '编辑供应商' : '新增供应商'"
      width="700px"
      :close-on-click-modal="false"
    >
      <ElForm ref="formRef" :model="providerForm" :rules="formRules" label-width="100px">
        <ElRow :gutter="16">
          <ElCol :span="12">
            <ElFormItem label="供应商代码" prop="code">
              <ElInput v-model="providerForm.code" placeholder="请输入供应商代码" :disabled="isEdit" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="供应商名称" prop="name">
              <ElInput v-model="providerForm.name" placeholder="请输入供应商名称" />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElFormItem label="Logo URL">
          <ElInput v-model="providerForm.logoUrl" placeholder="请输入Logo地址" />
        </ElFormItem>

        <ElFormItem label="API地址">
          <ElInput v-model="providerForm.apiUrl" placeholder="请输入API接口地址" />
        </ElFormItem>

        <ElRow :gutter="16">
          <ElCol :span="12">
            <ElFormItem label="API用户名">
              <ElInput v-model="providerForm.username" placeholder="请输入API用户名" />
            </ElFormItem>
          </ElCol>
          <ElCol :span="12">
            <ElFormItem label="API密码">
              <ElInput v-model="providerForm.password" type="password" placeholder="请输入API密码" show-password />
            </ElFormItem>
          </ElCol>
        </ElRow>

        <ElFormItem label="API令牌">
          <ElInput v-model="providerForm.token" type="password" placeholder="请输入API令牌" show-password />
        </ElFormItem>

        <ElFormItem label="IP白名单">
          <ElInput
            v-model="providerForm.ipWhitelist"
            type="textarea"
            :rows="2"
            placeholder="请输入IP白名单，多个IP用逗号分隔"
          />
        </ElFormItem>

        <ElFormItem label="自定义对接代码">
          <div class="flex flex-col gap-8px">
            <div class="text-sm text-gray-500">配置不同货源商的个性化对接逻辑，支持JavaScript代码</div>
            <ElTabs v-model="activeCodeTab" type="card">
              <ElTabPane label="查课代码" name="query">
                <ElInput
                  v-model="providerForm.customQueryCode"
                  type="textarea"
                  :rows="8"
                  placeholder="// 自定义查课逻辑
function customQuery(params) {
  // params: { school, username, password, product }
  // 返回查课结果
  return {
    success: true,
    courses: []
  };
}"
                />
              </ElTabPane>
              <ElTabPane label="下单代码" name="order">
                <ElInput
                  v-model="providerForm.customOrderCode"
                  type="textarea"
                  :rows="8"
                  placeholder="// 自定义下单逻辑
function customOrder(params) {
  // params: { school, username, password, product, courses }
  // 返回下单结果
  return {
    success: true,
    orderId: 'xxx'
  };
}"
                />
              </ElTabPane>
              <ElTabPane label="同步代码" name="sync">
                <ElInput
                  v-model="providerForm.customSyncCode"
                  type="textarea"
                  :rows="8"
                  placeholder="// 自定义同步逻辑
function customSync(params) {
  // params: { orderId }
  // 返回同步结果
  return {
    success: true,
    status: 'completed'
  };
}"
                />
              </ElTabPane>
            </ElTabs>
          </div>
        </ElFormItem>

        <ElFormItem label="状态">
          <ElSelect v-model="providerForm.status" style="width: 100%">
            <ElOption v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value" />
          </ElSelect>
        </ElFormItem>
      </ElForm>

      <template #footer>
        <div class="flex justify-end gap-8px">
          <ElButton @click="modalVisible = false">取消</ElButton>
          <ElButton type="primary" :loading="saving" @click="handleSave">
            {{ isEdit ? '更新' : '创建' }}
          </ElButton>
        </div>
      </template>
    </ElDialog>

    <!-- 余额详情抽屉 -->
    <ElDrawer v-model="balanceDrawerVisible" title="余额详情" :size="450">
      <div v-if="selectedProvider && balanceInfo" class="space-y-16px">
        <!-- 基本信息 -->
        <ElCard shadow="never">
          <template #header>
            <div class="flex items-center gap-8px">
              <icon-ic-round-account-balance-wallet class="text-primary" />
              <span class="font-medium">余额信息</span>
            </div>
          </template>

          <ElDescriptions :column="1" border>
            <ElDescriptionsItem label="供应商名称">
              <div class="flex items-center gap-8px">
                {{ selectedProvider.name }}
                <ElTag v-if="balanceInfo.platform" type="info" size="small">
                  {{ balanceInfo.platform }}
                </ElTag>
              </div>
            </ElDescriptionsItem>

            <ElDescriptionsItem v-if="balanceInfo.userName" label="账户用户">
              {{ balanceInfo.userName }}
            </ElDescriptionsItem>

            <ElDescriptionsItem label="当前余额">
              <div class="flex items-center gap-8px">
                <span class="text-xl text-primary font-bold">
                  ¥{{ balanceInfo.balance || selectedProvider.balance }}
                </span>
                <ElTag
                  :type="(balanceInfo.balance || selectedProvider.balance) > 100 ? 'success' : 'warning'"
                  size="small"
                >
                  {{ (balanceInfo.balance || selectedProvider.balance) > 100 ? '充足' : '偏低' }}
                </ElTag>
              </div>
            </ElDescriptionsItem>

            <ElDescriptionsItem label="查询状态">
              <ElTag :type="balanceInfo.success ? 'success' : 'danger'" size="small">
                {{ balanceInfo.success ? '查询成功' : '查询失败' }}
              </ElTag>
            </ElDescriptionsItem>

            <ElDescriptionsItem label="最后更新">
              {{ balanceInfo.lastUpdate }}
            </ElDescriptionsItem>

            <ElDescriptionsItem v-if="balanceInfo.message" label="查询消息">
              <span :class="balanceInfo.success ? 'text-green-600' : 'text-red-600'">
                {{ balanceInfo.message }}
              </span>
            </ElDescriptionsItem>
          </ElDescriptions>
        </ElCard>

        <!-- 余额提醒 -->
        <ElAlert
          :title="(balanceInfo.balance || selectedProvider.balance) > 100 ? '余额充足' : '余额不足提醒'"
          :type="(balanceInfo.balance || selectedProvider.balance) > 100 ? 'success' : 'warning'"
          :closable="false"
          show-icon
        >
          <template #default>
            <div v-if="(balanceInfo.balance || selectedProvider.balance) > 100">当前余额充足，可以正常处理订单</div>
            <div v-else>当前余额偏低，建议及时充值以免影响订单处理</div>
          </template>
        </ElAlert>

        <!-- 操作按钮 -->
        <div class="flex gap-8px">
          <ElButton type="primary" :loading="loading" @click="handleCheckBalance(selectedProvider)">
            <template #icon>
              <icon-ic-round-refresh class="text-icon" />
            </template>
            刷新余额
          </ElButton>

          <ElButton v-if="balanceInfo.platform === '29平台'" type="success" @click="handleGoToRecharge">
            <template #icon>
              <icon-ic-round-payment class="text-icon" />
            </template>
            前往充值
          </ElButton>
        </div>
      </div>
    </ElDrawer>
  </div>
</template>

<style scoped>
/* 响应式表格滚动 */
.el-table {
  @apply w-full;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .el-table {
    font-size: 12px;
  }

  .flex-wrap {
    flex-direction: column;
    align-items: stretch;
  }

  .w-240px,
  .w-120px,
  .w-100px {
    width: 100%;
  }
}
</style>
