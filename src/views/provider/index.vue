<script setup lang="ts">
import { ref, watch } from 'vue';
import ProviderList from './list/index.vue';
import ProviderConfig from './config/index.vue';
import { useRoute, useRouter } from 'vue-router';

defineOptions({ name: 'Provider' });

const route = useRoute();
const router = useRouter();
import type { TabPaneName } from 'element-plus';
const activeTab = ref<TabPaneName>((route.query.tab as string) || 'list');

watch(
  () => route.query.tab,
  v => {
    activeTab.value = (v as string) || 'list';
  }
);

function onTabChange(name: TabPaneName) {
  router.replace({ path: '/provider', query: { tab: name } });
}
</script>

<template>
  <div class="p-16px">
    <ElTabs v-model="activeTab" @tab-change="onTabChange">
      <ElTabPane name="list" label="货源列表">
        <ProviderList />
      </ElTabPane>
      <ElTabPane name="config" label="配置管理">
        <ProviderConfig />
      </ElTabPane>
    </ElTabs>
  </div>
</template>
