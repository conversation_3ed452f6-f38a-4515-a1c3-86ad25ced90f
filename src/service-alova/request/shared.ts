import { useAuthStore } from '@/store/modules/auth';
import { getAuthorization, getRefreshToken, setTokens } from '@/utils/auth';
import { fetchRefreshToken } from '../api';
import type { RequestInstanceState } from './type';

// 重新导出授权函数以保持向后兼容
export { getAuthorization } from '@/utils/auth';

/** refresh token */
export async function handleRefreshToken() {
  const { resetStore } = useAuthStore();

  const rToken = getRefreshToken();
  const refreshTokenMethod = fetchRefreshToken(rToken);

  // set the refreshToken role, so that the request will not be intercepted
  refreshTokenMethod.meta.authRole = 'refreshToken';

  try {
    const data = await refreshTokenMethod;
    setTokens(data.token, data.refreshToken);
  } catch (error) {
    resetStore();
    throw error;
  }
}

export function showErrorMsg(state: RequestInstanceState, message: string) {
  if (!state.errMsgStack?.length) {
    state.errMsgStack = [];
  }

  const isExist = state.errMsgStack.includes(message);

  if (!isExist) {
    state.errMsgStack.push(message);

    if (window.$message) {
      window.$message({
        type: 'error',
        message,
        onClose: () => {
          state.errMsgStack = state.errMsgStack.filter(msg => msg !== message);

          setTimeout(() => {
            state.errMsgStack = [];
          }, 5000);
        }
      });
    }
  }
}
