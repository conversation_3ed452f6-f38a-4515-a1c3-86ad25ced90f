/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'category',
    path: '/category',
    component: 'layout.base$view.category',
    meta: {
      title: 'category',
      i18nKey: 'route.category',
      icon: 'mdi:folder-multiple',
      order: 5
    }
  },
  {
    name: 'dashboard',
    path: '/dashboard',
    component: 'layout.base',
    meta: {
      title: 'dashboard',
      i18nKey: 'route.dashboard',
      icon: 'mdi:view-dashboard',
      order: 1.5
    },
    children: [
      {
        name: 'dashboard_statistics',
        path: '/dashboard/statistics',
        component: 'view.dashboard_statistics',
        meta: {
          title: 'dashboard_statistics',
          i18nKey: 'route.dashboard_statistics'
        }
      }
    ]
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'mdi:monitor-dashboard',
      order: 1
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'order',
    path: '/order',
    component: 'layout.base',
    redirect: '/order/list',
    meta: {
      title: 'order',
      i18nKey: 'route.order',
      icon: 'mdi:clipboard-list',
      order: 2
    },
    children: [
      {
        name: 'order_create',
        path: '/order/create',
        component: 'view.order_create',
        meta: {
          title: 'order_create',
          i18nKey: 'route.order_create',
          icon: 'mdi:plus-circle',
          order: 2
        }
      },
      {
        name: 'order_list',
        path: '/order/list',
        component: 'view.order_list',
        meta: {
          title: 'order_list',
          i18nKey: 'route.order_list'
        }
      },
      {
        name: 'order_sync-management',
        path: '/order/sync-management',
        component: 'view.order_sync-management',
        meta: {
          title: 'order_sync-management',
          i18nKey: 'route.order_sync-management',
          icon: 'mdi:sync',
          order: 3
        }
      }
    ]
  },
  {
    name: 'payment',
    path: '/payment',
    component: 'layout.base',
    meta: {
      title: 'payment',
      i18nKey: 'route.payment',
      icon: 'mdi:credit-card',
      order: 8
    },
    children: [
      {
        name: 'payment_finance',
        path: '/payment/finance',
        component: 'view.payment_finance',
        meta: {
          title: 'payment_finance',
          i18nKey: 'route.payment_finance'
        }
      },
      {
        name: 'payment_recharge',
        path: '/payment/recharge',
        component: 'view.payment_recharge',
        meta: {
          title: 'payment_recharge',
          i18nKey: 'route.payment_recharge'
        }
      }
    ]
  },
  {
    name: 'platform29',
    path: '/platform29',
    component: 'layout.base$view.platform29',
    meta: {
      title: 'platform29',
      i18nKey: 'route.platform29',
      icon: 'mdi:api',
      order: 4.5,
      roles: ['admin']
    }
  },
  {
    name: 'product',
    path: '/product',
    component: 'layout.base$view.product',
    meta: {
      title: 'product',
      i18nKey: 'route.product',
      icon: 'mdi:package-variant',
      order: 4
    }
  },
  {
    name: 'provider',
    path: '/provider',
    component: 'layout.base$view.provider',
    meta: {
      title: 'provider',
      i18nKey: 'route.provider',
      icon: 'mdi:store',
      order: 6
    }
  },
  {
    name: 'system-center',
    path: '/system-center',
    component: 'layout.base',
    meta: {
      title: 'system-center',
      i18nKey: 'route.system-center',
      icon: 'carbon:settings',
      order: 9,
      permissions: ['route:/system-center']
    },
    children: [
      {
        name: 'system-center_backup',
        path: '/system-center/backup',
        component: 'view.system-center_backup',
        meta: {
          title: 'system-center_backup',
          i18nKey: 'route.system-center_backup',
          icon: 'carbon:cloud-backup',
          order: 9,
          permissions: ['route:/system-center/backup']
        }
      },
      {
        name: 'system-center_config',
        path: '/system-center/config',
        component: 'view.system-center_config',
        meta: {
          title: 'system-center_config',
          i18nKey: 'route.system-center_config',
          icon: 'carbon:settings-adjust',
          order: 3,
          permissions: ['route:/system-center/config']
        }
      },
      {
        name: 'system-center_database',
        path: '/system-center/database',
        component: 'view.system-center_database',
        meta: {
          title: 'system-center_database',
          i18nKey: 'route.system-center_database',
          icon: 'carbon:data-base',
          order: 8,
          permissions: ['route:/system-center/database']
        }
      },
      {
        name: 'system-center_email',
        path: '/system-center/email',
        component: 'view.system-center_email',
        meta: {
          title: 'system-center_email',
          i18nKey: 'route.system-center_email',
          icon: 'carbon:email',
          order: 4,
          permissions: ['route:/system-center/email']
        }
      },
      {
        name: 'system-center_iam',
        path: '/system-center/iam',
        component: 'view.system-center_iam',
        meta: {
          title: 'system-center_iam',
          i18nKey: 'route.system-center_iam',
          icon: 'mdi:account-cog',
          order: 1,
          permissions: ['route:/system-center/iam']
        }
      },
      {
        name: 'system-center_license',
        path: '/system-center/license',
        component: 'view.system-center_license',
        meta: {
          title: 'system-center_license',
          i18nKey: 'route.system-center_license',
          icon: 'ic:round-verified',
          order: 5,
          permissions: ['route:/system-center/license']
        }
      },
      {
        name: 'system-center_log',
        path: '/system-center/log',
        component: 'view.system-center_log',
        meta: {
          title: 'system-center_log',
          i18nKey: 'route.system-center_log',
          icon: 'carbon:document-tasks',
          order: 7,
          permissions: ['route:/system-center/log']
        }
      },
      {
        name: 'system-center_monitor',
        path: '/system-center/monitor',
        component: 'view.system-center_monitor',
        meta: {
          title: 'system-center_monitor',
          i18nKey: 'route.system-center_monitor',
          icon: 'carbon:dashboard',
          order: 6,
          permissions: ['route:/system-center/monitor']
        }
      },
      {
        name: 'system-center_permission-management',
        path: '/system-center/permission-management',
        component: 'view.system-center_permission-management',
        meta: {
          title: 'system-center_permission-management',
          i18nKey: 'route.system-center_permission-management',
          icon: 'carbon:security',
          order: 2,
          permissions: ['route:/system-center/permission-management']
        }
      },
      {
        name: 'system-center_report',
        path: '/system-center/report',
        component: 'view.system-center_report',
        meta: {
          title: 'system-center_report',
          i18nKey: 'route.system-center_report',
          icon: 'carbon:report',
          order: 10,
          permissions: ['route:/system-center/report']
        }
      },
      {
        name: 'system-center_role-management',
        path: '/system-center/role-management',
        component: 'view.system-center_role-management',
        meta: {
          title: 'system-center_role-management',
          i18nKey: 'route.system-center_role-management',
          icon: 'carbon:user-multiple',
          order: 1,
          permissions: ['route:/system-center/role-management']
        }
      }
    ]
  },
  {
    name: 'user',
    path: '/user',
    component: 'layout.base$view.user',
    meta: {
      title: 'user',
      i18nKey: 'route.user',
      icon: 'mdi:account-group',
      order: 7
    }
  },
  {
    name: 'user-center',
    path: '/user-center',
    component: 'layout.base$view.user-center',
    meta: {
      title: 'user-center',
      i18nKey: 'route.user-center',
      hideInMenu: true
    }
  }
];
