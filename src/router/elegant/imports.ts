/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteComponent } from "vue-router";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";

import BaseLayout from "@/layouts/base-layout/index.vue";
import BlankLayout from "@/layouts/blank-layout/index.vue";

export const layouts: Record<RouteLayout, RouteComponent | (() => Promise<RouteComponent>)> = {
  base: BaseLayout,
  blank: BlankLayout,
};

export const views: Record<LastLevelRouteKey, RouteComponent | (() => Promise<RouteComponent>)> = {
  403: () => import("@/views/_builtin/403/index.vue"),
  404: () => import("@/views/_builtin/404/index.vue"),
  500: () => import("@/views/_builtin/500/index.vue"),
  "iframe-page": () => import("@/views/_builtin/iframe-page/[url].vue"),
  login: () => import("@/views/_builtin/login/index.vue"),
  category: () => import("@/views/category/index.vue"),
  dashboard_statistics: () => import("@/views/dashboard/statistics/index.vue"),
  home: () => import("@/views/home/<USER>"),
  order_create: () => import("@/views/order/create/index.vue"),
  order_list: () => import("@/views/order/list/index.vue"),
  "order_sync-management": () => import("@/views/order/sync-management/index.vue"),
  payment_finance: () => import("@/views/payment/finance/index.vue"),
  payment_recharge: () => import("@/views/payment/recharge/index.vue"),
  platform29: () => import("@/views/platform29/index.vue"),
  product: () => import("@/views/product/index.vue"),
  provider_config: () => import("@/views/provider/config/index.vue"),
  provider: () => import("@/views/provider/index.vue"),
  provider_list: () => import("@/views/provider/list/index.vue"),
  "system-center_backup": () => import("@/views/system-center/backup/index.vue"),
  "system-center_config": () => import("@/views/system-center/config/index.vue"),
  "system-center_database": () => import("@/views/system-center/database/index.vue"),
  "system-center_email": () => import("@/views/system-center/email/index.vue"),
  "system-center_iam": () => import("@/views/system-center/iam/index.vue"),
  "system-center_license": () => import("@/views/system-center/license/index.vue"),
  "system-center_monitor": () => import("@/views/system-center/monitor/index.vue"),
  "system-center_permission-management": () => import("@/views/system-center/permission-management/index.vue"),
  "system-center_report": () => import("@/views/system-center/report/index.vue"),
  "system-center_role-management": () => import("@/views/system-center/role-management/index.vue"),
  "system-center_log": () => import("@/views/system-center_log/index.vue"),
  "user-center": () => import("@/views/user-center/index.vue"),
  user: () => import("@/views/user/index.vue"),
};
