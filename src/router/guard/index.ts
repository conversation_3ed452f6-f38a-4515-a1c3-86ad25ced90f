import type { Router } from 'vue-router';
import { createRouteGuard } from './route';
import { createProgressGuard } from './progress';
import { createDocumentTitleGuard } from './title';

/**
 * Router guard
 *
 * @param router - Router instance
 */
export function createRouterGuard(router: Router) {
  // 移除授权守卫，授权检查现在由后端统一处理
  // createLicenseGuard(router);
  createProgressGuard(router);
  createRouteGuard(router);
  createDocumentTitleGuard(router);
}
