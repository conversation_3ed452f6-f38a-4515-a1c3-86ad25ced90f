<script setup lang="ts">
import { computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { Loading } from '@element-plus/icons-vue';
import { LAYOUT_SCROLL_EL_ID } from '@sa/materials';
import { useAppStore } from '@/store/modules/app';
import { useTabStore } from '@/store/modules/tab';

defineOptions({ name: 'GlobalContent' });

interface Props {
  /** Show padding for content */
  showPadding?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  showPadding: true
});

const route = useRoute();
const appStore = useAppStore();

const tabStore = useTabStore();

// 根据路由动态决定是否显示padding
const shouldShowPadding = computed(() => {
  // 首页不显示padding，让内容占满屏幕
  if (route.name === 'home') {
    return false;
  }
  return props.showPadding;
});

function resetScroll() {
  const el = document.querySelector(`#${LAYOUT_SCROLL_EL_ID}`);
  el?.scrollTo({ left: 0, top: 0 });
}

// 监听路由变化，处理滚动和内容管理
watch(
  () => route.path,
  () => {
    // 手动处理原本由 Transition 处理的逻辑
    appStore.setContentXScrollable(true);
    resetScroll();

    // 延迟设置，确保新组件已挂载
    setTimeout(() => {
      appStore.setContentXScrollable(false);
    }, 100);
  }
);
</script>

<template>
  <RouterView v-slot="{ Component, route: currentRoute }">
    <div class="route-content-wrapper">
      <component
        :is="Component"
        v-if="appStore.reloadFlag && Component"
        :key="tabStore.getTabIdByRoute(currentRoute)"
        :class="{ 'p-16px': shouldShowPadding }"
        class="flex-grow bg-layout transition-300"
      />
      <!-- 备用显示，防止完全白屏 -->
      <div v-else class="h-200px flex-center">
        <ElIcon class="is-loading mr-8px">
          <Loading />
        </ElIcon>
        <span>页面加载中...</span>
      </div>
    </div>
  </RouterView>
</template>

<style></style>
