import { computed, reactive, ref } from 'vue';
import { defineStore } from 'pinia';
import {
  getLicenseStatus as getBackendLicenseStatus,
  getLicenseDetails,
  resetLicenseInfo,
  validateLicense
} from '@/service/api/license';
import { SetupStoreId } from '@/enum';

interface LicenseInfo {
  licenseKey: string;
  domain: string;
  isValid: boolean;
  expiryDate: string | null;
  status: string | null;
  boundAt: string | null;
  // 扩展的详细信息
  validatedAt?: string | null;
  plan?: string | null;
  planDescription?: string | null;
  startDate?: string | null;
  daysLeft?: number | null;
  maxProducts?: number | null;
  maxUsers?: number | null;
  planFeatures?: any;
  bindingInfo?: any;
  usageStats?: any;
  gracePeriodStart?: string | null;
  gracePeriodEnd?: string | null;
  inGracePeriod?: boolean;
  lastExternalValidation?: string | null;
  validationCount?: number;
}

export const useLicenseStore = defineStore(SetupStoreId.License, () => {
  // 授权信息 - 包含详细信息
  const licenseInfo = reactive<LicenseInfo>({
    licenseKey: '',
    domain: window.location.hostname,
    isValid: false,
    expiryDate: null,
    status: null,
    boundAt: null,
    // 扩展的详细信息初始值
    validatedAt: null,
    plan: null,
    planDescription: null,
    startDate: null,
    daysLeft: null,
    maxProducts: null,
    maxUsers: null,
    planFeatures: null,
    bindingInfo: null,
    usageStats: null,
    gracePeriodStart: null,
    gracePeriodEnd: null,
    inGracePeriod: false,
    lastExternalValidation: null,
    validationCount: 0
  });

  // 强制更新标志，用于触发计算属性重新计算
  const forceUpdate = ref(0);

  // 授权验证是否已经通过 - 基于后端API数据
  const isLicenseValid = computed(() => {
    // 依赖forceUpdate来触发重新计算
    forceUpdate.value;

    // 直接基于licenseInfo的状态判断
    return licenseInfo.isValid && (licenseInfo.status === 'active' || licenseInfo.status === 'grace_period');
  });

  // 强制更新授权状态
  function forceUpdateLicenseStatus() {
    forceUpdate.value++;
    console.log('🔒 强制更新授权状态:', forceUpdate.value);
  }

  /** 纯后端控制模式 - 不再保存任何授权信息到前端本地存储 所有授权状态通过后端API实时获取 */

  // 授权验证是否加载中
  const isValidating = ref<boolean>(false);

  /** 重置授权状态（纯后端控制模式） */
  function resetLicenseStore() {
    // 只清除前端内存状态，不操作本地存储
    licenseInfo.licenseKey = '';
    licenseInfo.isValid = false;
    licenseInfo.expiryDate = null;
    licenseInfo.status = 'inactive';
    licenseInfo.boundAt = null;
    licenseInfo.domain = window.location.hostname;

    console.log('🔒 [纯后端控制] 前端授权状态已重置');
  }

  /**
   * 检查授权状态
   *
   * @returns 授权是否有效
   */
  function checkLicenseStatus(): boolean {
    return isLicenseValid.value;
  }

  /** 获取基本的授权状态信息（安全模式） */
  function getDetailedLicenseStatus() {
    return {
      isValid: licenseInfo.isValid,
      status: licenseInfo.status,
      domain: licenseInfo.domain
    };
  }

  // 管理员详细信息功能已移除，授权管理已解耦

  /** 纯后端控制模式 - 移除所有授权系统本地存储功能 授权验证完全通过后端API进行 */

  /**
   * 数据库模式：激活授权密钥（通过项目后端API）
   *
   * @param key 授权密钥
   */
  async function validateLicenseKey(key: string): Promise<boolean> {
    if (!key) return false;

    isValidating.value = true;

    try {
      console.log('🔒 [数据库模式] 开始激活授权密钥...');

      // 调用项目后端API验证（项目后端会调用外部授权系统并保存到数据库）
      const result = await validateLicense(key);

      console.log('🔒 [数据库模式] 项目后端API验证结果:', {
        status: result?.status,
        domain: result?.domain
      });

      if (result && result.status === 'active') {
        console.log('🔒 [数据库模式] 激活成功，授权信息已保存到数据库');

        // 更新本地状态（数据已由后端保存到数据库）
        licenseInfo.licenseKey = key;
        licenseInfo.isValid = true;
        licenseInfo.expiryDate = result.expiryDate || null;
        licenseInfo.status = result.status;
        licenseInfo.domain = result.domain || window.location.hostname;

        // 强制更新授权状态
        forceUpdateLicenseStatus();

        console.log('🔒 [数据库模式] 软件激活完成:', {
          domain: licenseInfo.domain,
          status: licenseInfo.status,
          expiryDate: licenseInfo.expiryDate
        });

        window.$message?.success('软件激活成功！');
        return true;
      }

      // 处理激活失败
      console.log('🔒 [数据库模式] 激活失败: 状态不是active');
      window.$message?.error('激活失败，请检查授权密钥是否正确');

      return false;
    } catch (error: any) {
      console.error('🔒 [数据库模式] 激活过程出错:', error);

      // 检查是否是网络错误还是业务错误
      if (error?.response?.data?.message) {
        // 这是来自后端的业务错误响应
        window.$message?.error(`激活失败：${error.response.data.message}`);
      } else if (error?.message && error.message.includes('Network Error')) {
        // 网络连接错误
        window.$message?.error('无法连接到服务器，请检查网络连接后重试');
      } else if (error?.code === 'ECONNREFUSED' || error?.code === 'ENOTFOUND') {
        // 服务器连接被拒绝或域名解析失败
        window.$message?.error('无法连接到服务器，请检查网络连接后重试');
      } else if (error?.message) {
        // 其他已知错误
        window.$message?.error(`激活失败：${error.message}`);
      } else {
        // 未知错误
        window.$message?.error('激活失败，请稍后重试');
      }
      return false;
    } finally {
      isValidating.value = false;
    }
  }

  /** 纯后端控制模式 - 移除授权系统登录功能 授权验证完全通过项目后端API进行 */

  /**
   * 安全模式：从项目后端API获取授权状态 只在路由守卫首次调用时执行，避免重复请求
   *
   * @returns Promise<boolean> 返回是否已授权
   */
  async function initLicenseStatus(): Promise<boolean> {
    console.log('🔒 [安全模式] 执行一次性授权状态检查...');

    try {
      // 从项目后端API获取授权状态（安全模式）
      const result = await getBackendLicenseStatus();

      console.log('🔒 [安全模式] 后端授权检查结果:', {
        isAuthorized: result?.isAuthorized,
        status: result?.status
      });

      if (result && result.isAuthorized !== undefined) {
        // 更新本地状态（现在包含详细信息）
        licenseInfo.isValid = result.isAuthorized && (result.status === 'active' || result.status === 'grace_period');
        licenseInfo.status = result.status || 'inactive';
        licenseInfo.domain = result.domain || window.location.hostname;
        licenseInfo.licenseKey = result.licenseKey || ''; // 脱敏的授权密钥
        licenseInfo.expiryDate = result.expiryDate;
        licenseInfo.boundAt = result.boundAt;

        // 保存扩展的详细信息
        licenseInfo.validatedAt = result.validatedAt;
        licenseInfo.plan = result.plan;
        licenseInfo.planDescription = result.planDescription;
        licenseInfo.startDate = result.startDate;
        licenseInfo.daysLeft = result.daysLeft;
        licenseInfo.maxProducts = result.maxProducts;
        licenseInfo.maxUsers = result.maxUsers;
        licenseInfo.planFeatures = result.planFeatures;
        licenseInfo.bindingInfo = result.bindingInfo;
        licenseInfo.usageStats = result.usageStats;
        licenseInfo.gracePeriodStart = result.gracePeriodStart;
        licenseInfo.gracePeriodEnd = result.gracePeriodEnd;
        licenseInfo.inGracePeriod = result.inGracePeriod;
        licenseInfo.lastExternalValidation = result.lastExternalValidation;
        licenseInfo.validationCount = result.validationCount;

        console.log('🔒 [详细模式] 授权状态和详细信息更新完成:', {
          isValid: licenseInfo.isValid,
          status: licenseInfo.status,
          domain: licenseInfo.domain,
          plan: licenseInfo.plan,
          expiryDate: licenseInfo.expiryDate,
          daysLeft: licenseInfo.daysLeft
        });

        // 强制更新状态
        forceUpdateLicenseStatus();

        return licenseInfo.isValid;
      }
      console.log('🔒 [安全模式] 后端授权检查失败');
      resetLicenseStore();
      return false;
    } catch (error) {
      console.error('🔒 [安全模式] 获取授权状态失败:', error);

      // 网络错误时，系统无法运行（安全优先）
      console.warn('🔒 [安全模式] 无法连接到授权服务器');
      resetLicenseStore();
      return false;
    }
  }

  /** 纯后端控制模式 - 移除授权计划功能 授权管理完全通过后端进行 */

  /** 添加重置授权信息功能 */
  async function resetLicense(): Promise<boolean> {
    try {
      console.log('🔒 开始重置授权信息...');
      const result = await resetLicenseInfo();

      console.log('🔒 重置API响应:', result);

      if (result && result.success) {
        console.log('🔒 授权信息重置成功，清除本地状态');
        resetLicenseStore();

        // 重置后立即重新初始化授权状态
        console.log('🔒 重新初始化授权状态...');
        await initLicenseStatus();

        return true;
      }
      // 处理重置失败的情况
      const errorMessage = result?.message || '未知错误';
      console.error('🔒 重置授权信息失败:', errorMessage);

      // 如果是"未找到授权信息"，也算作成功（因为目标已达成）
      if (errorMessage.includes('未找到授权信息')) {
        console.log('🔒 系统已处于未授权状态，清除本地状态');
        resetLicenseStore();
        await initLicenseStatus();
        return true;
      }

      return false;
    } catch (error) {
      console.error('重置授权信息失败:', error);
      return false;
    }
  }

  return {
    licenseInfo,
    isLicenseValid,
    isValidating,
    resetLicenseStore,
    checkLicenseStatus,
    getDetailedLicenseStatus,
    validateLicenseKey,
    initLicenseStatus,
    resetLicense,
    forceUpdateLicenseStatus
  };
});
