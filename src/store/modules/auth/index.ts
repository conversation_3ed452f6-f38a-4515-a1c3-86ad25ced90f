import { computed, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { defineStore } from 'pinia';
import { useLoading } from '@sa/hooks';
import { fetchGetUserInfo, fetchLogin, fetchRefreshToken } from '@/service/api';
import { fetchClaims } from '@/service/api/claims';
import { useRouterPush } from '@/hooks/common/router';
import { localStg } from '@/utils/storage';
import { SetupStoreId } from '@/enum';
import { $t } from '@/locales';
import { useRouteStore } from '../route';
import { useTabStore } from '../tab';
import { useLicenseStore } from '../license';
import { clearAuthStorage, getToken } from './shared';

export const useAuthStore = defineStore(SetupStoreId.Auth, () => {
  const route = useRoute();
  const routeStore = useRouteStore();
  const tabStore = useTabStore();

  const { toLogin, redirectFromLogin } = useRouterPush(false);
  const { loading: loginLoading, startLoading, endLoading } = useLoading();

  const token = ref(getToken());

  const userInfo = reactive<Api.Auth.UserInfo>({
    userId: 0,
    username: '',
    nickname: '',
    email: '',
    phone: '',
    avatar: null,
    role: 0,
    userRole: '',
    status: 0,
    createTime: '',
    updateTime: '',
    inviteCode: '',
    balance: 0,
    userRate: 0,
    roles: [],
    permissions: []
  });

  /** is super role in static route */
  const isStaticSuper = computed(() => {
    const { VITE_AUTH_ROUTE_MODE, VITE_STATIC_SUPER_ROLE } = import.meta.env;

    return VITE_AUTH_ROUTE_MODE === 'static' && userInfo.roles.includes(VITE_STATIC_SUPER_ROLE);
  });

  /** Is login */
  const isLogin = computed(() => Boolean(token.value));

  /** Is admin user */
  const isAdmin = computed(() => userInfo.roles.includes('admin'));

  /** Reset auth store */
  async function resetStore() {
    console.log('🔒 重置用户状态...');

    // 重置重试计数器，防止无限循环
    getUserInfoRetryCount = 0;
    isGettingUserInfo = false;
    lastResetTime = Date.now();

    const authStore = useAuthStore();

    recordUserId();

    clearAuthStorage();

    authStore.$reset();

    if (!route.meta.constant) {
      await toLogin();
    }

    tabStore.cacheTabs();
    routeStore.resetStore();

    console.log('🔒 用户状态已重置');
  }

  /** Record the user ID of the previous login session Used to compare with the current user ID on next login */
  function recordUserId() {
    if (!userInfo.userId) {
      return;
    }

    // Store current user ID locally for next login comparison
    localStg.set('lastLoginUserId', String(userInfo.userId));
  }

  /**
   * Check if current login user is different from previous login user If different, clear all tabs
   *
   * @returns {boolean} Whether to clear all tabs
   */
  function checkTabClear(): boolean {
    if (!userInfo.userId) {
      return false;
    }

    const lastLoginUserId = localStg.get('lastLoginUserId');

    // Clear all tabs if current user is different from previous user
    if (!lastLoginUserId || lastLoginUserId !== String(userInfo.userId)) {
      localStg.remove('globalTabs');
      tabStore.clearTabs();

      localStg.remove('lastLoginUserId');
      return true;
    }

    localStg.remove('lastLoginUserId');
    return false;
  }

  /**
   * Login
   *
   * @param userName User name
   * @param password Password
   * @param [redirect=true] Whether to redirect after login. Default is `true`
   */
  async function login(userName: string, password: string, redirect = true) {
    startLoading();

    try {
      const loginToken = await fetchLogin(userName, password);
      const pass = await loginByToken(loginToken);

      if (pass) {
        // Check if the tab needs to be cleared
        const isClear = checkTabClear();
        let needRedirect = redirect;

        if (isClear) {
          // If the tab needs to be cleared,it means we don't need to redirect.
          needRedirect = false;
        }

        // 登录成功消息
        window.$notification?.success({
          title: $t('page.login.common.loginSuccess'),
          message: $t('page.login.common.welcomeBack', { userName: userInfo.username }),
          duration: 4500
        });

        // 直接重定向，不检查授权状态
        await redirectFromLogin(needRedirect);
      }
    } catch (error) {
      // 获取错误消息，支持多种格式
      let errorMessage = '登录失败，请检查账号密码';
      if ((error as any)?.response?.data) {
        const responseData = (error as any).response.data;
        errorMessage = responseData.message || responseData.msg || errorMessage;
      } else if ((error as any)?.message) {
        errorMessage = (error as any).message;
      }

      window.$message?.error(errorMessage);
    } finally {
      endLoading();
    }
  }

  async function loginByToken(loginToken: Api.Auth.LoginToken) {
    // 验证 loginToken 是否有效
    if (!loginToken || !loginToken.token) {
      window.$message?.error('登录响应无效，请重试');
      return false;
    }

    // 1. stored in the localStorage, the later requests need it in headers
    localStg.set('token', loginToken.token);
    localStg.set('refreshToken', loginToken.refreshToken);

    // 2. 立即设置token到store，这样后续请求就能携带Authorization头
    token.value = loginToken.token;

    // 3. get user info
    const pass = await getUserInfo();

    if (pass) {
      return true;
    }

    return false;
  }

  // 防止无限循环的标志
  let isGettingUserInfo = false;
  let getUserInfoRetryCount = 0;
  let lastResetTime = 0;
  const MAX_RETRY_COUNT = 3;
  const RESET_COOLDOWN = 5000; // 5秒冷却期

  async function getUserInfo() {
    // 防止并发调用和无限循环
    if (isGettingUserInfo) {
      console.warn('🔄 getUserInfo 正在执行中，跳过重复调用');
      return false;
    }

    // 检查是否在重置冷却期内
    const now = Date.now();
    if (now - lastResetTime < RESET_COOLDOWN) {
      console.warn('🔄 在重置冷却期内，跳过 getUserInfo 调用');
      return false;
    }

    if (getUserInfoRetryCount >= MAX_RETRY_COUNT) {
      console.error('🚫 getUserInfo 重试次数超限，停止调用');
      lastResetTime = now;
      resetStore();
      return false;
    }

    isGettingUserInfo = true;
    getUserInfoRetryCount++;

    try {
      // 只在重试时记录日志
      if (getUserInfoRetryCount > 1) {
        console.log(`🔄 获取用户信息 (第${getUserInfoRetryCount}次尝试)`);
      }
      const info = await fetchGetUserInfo();
      const claims = await fetchClaims();

      // update store
      Object.assign(userInfo, info, claims);

      // 成功后重置计数器
      getUserInfoRetryCount = 0;
      return true;
    } catch (error) {
      console.error(`❌ 获取用户信息失败 (第${getUserInfoRetryCount}次):`, error);

      // 如果是401错误或token过期，直接清除状态
      if ((error as any)?.response?.status === 401) {
        console.log('🔒 Token已过期，清除用户状态');
        resetStore();
        return false;
      }

      // 显示详细错误信息（但不要每次都显示，避免用户体验差）
      if (getUserInfoRetryCount === 1) {
        if ((error as any)?.response?.data?.message) {
          window.$message?.error(`获取用户信息失败：${(error as any).response.data.message}`);
        } else if ((error as any)?.message) {
          window.$message?.error(`获取用户信息失败：${(error as any).message}`);
        } else {
          window.$message?.error('获取用户信息失败，请重试');
        }
      }

      return false;
    } finally {
      isGettingUserInfo = false;
    }
  }

  // 防止重复初始化的标志
  let isInitializingUserInfo = false;

  async function initUserInfo() {
    const hasToken = getToken();

    if (!hasToken) {
      console.log('🔒 没有token，跳过用户信息初始化');
      return;
    }

    // 防止重复初始化
    if (isInitializingUserInfo) {
      console.log('🔄 用户信息正在初始化中，跳过重复调用');
      return;
    }

    // 如果用户信息已存在且有效，跳过初始化
    if (userInfo.userId && userInfo.username) {
      console.log('🔄 用户信息已存在，跳过重复初始化');
      return;
    }

    isInitializingUserInfo = true;

    try {
      console.log('🔄 初始化用户信息...');
      const pass = await getUserInfo();

      if (!pass) {
        console.log('🔒 用户信息初始化失败，清除状态');
        resetStore();
      }
    } finally {
      isInitializingUserInfo = false;
    }
  }

  async function refreshToken() {
    try {
      const { getRefreshToken } = await import('@/utils/auth');
      const refreshToken = getRefreshToken();
      if (!refreshToken) {
        return false;
      }

      const { token: newToken, refreshToken: newRefreshToken } = await fetchRefreshToken(refreshToken);

      localStg.set('token', newToken);
      localStg.set('refreshToken', newRefreshToken);

      token.value = newToken;

      return true;
    } catch (error) {
      console.error('刷新令牌失败:', error);
      return false;
    }
  }

  return {
    token,
    userInfo,
    isStaticSuper,
    isLogin,
    isAdmin,
    loginLoading,
    resetStore,
    login,
    initUserInfo,
    refreshToken
  };
});
