import { h } from 'vue';
import type { App } from 'vue';
import { ElButton } from 'element-plus';
import { $t } from '@/locales';

export function setupAppErrorHandle(app: App) {
  app.config.errorHandler = (err, vm, info) => {
    // eslint-disable-next-line no-console
    console.error(err, vm, info);
  };
}

export function setupAppVersionNotification() {
  // Update check interval in milliseconds
  const UPDATE_CHECK_INTERVAL = 3 * 60 * 1000;

  const canAutoUpdateApp = import.meta.env.VITE_AUTOMATICALLY_DETECT_UPDATE === 'Y' && import.meta.env.PROD;

  if (!canAutoUpdateApp) return;

  let isShow = false;
  let updateInterval: ReturnType<typeof setInterval> | undefined;
  let visibilityChangeHandler: (() => void) | null = null;

  const checkForUpdates = async () => {
    if (isShow) return;

    const buildTime = await getHtmlBuildTime();

    // If failed to get build time or build time hasn't changed, no update is needed
    if (!buildTime || buildTime === BUILD_TIME) {
      return;
    }

    isShow = true;

    // Show update notification
    const n = window.$notification!({
      duration: 0,
      title: $t('system.updateTitle'),
      message: h('div', {}, [
        h('p', {}, $t('system.updateContent')),
        h('div', { style: { display: 'flex', justifyContent: 'end', gap: '12px' } }, [
          h(
            ElButton,
            {
              onClick() {
                n?.close();
              }
            },
            () => $t('system.updateCancel')
          ),
          h(
            ElButton,
            {
              type: 'primary',
              onClick() {
                location.reload();
              }
            },
            () => $t('system.updateConfirm')
          )
        ])
      ])
    });
  };
  const startUpdateInterval = () => {
    if (updateInterval) {
      clearInterval(updateInterval);
    }
    updateInterval = setInterval(checkForUpdates, UPDATE_CHECK_INTERVAL);
  };
  // 清理函数
  const cleanup = () => {
    if (updateInterval) {
      clearInterval(updateInterval);
      updateInterval = undefined;
    }
    if (visibilityChangeHandler) {
      document.removeEventListener('visibilitychange', visibilityChangeHandler);
      visibilityChangeHandler = null;
    }
  };

  // If updates should be checked, set up the visibility change listener and start the update interval
  if (!isShow && document.visibilityState === 'visible') {
    // 创建事件处理器
    visibilityChangeHandler = () => {
      if (document.visibilityState === 'visible') {
        checkForUpdates();
        startUpdateInterval();
      } else {
        // 页面隐藏时清理定时器
        if (updateInterval) {
          clearInterval(updateInterval);
          updateInterval = undefined;
        }
      }
    };

    // Check for updates when the document is visible
    document.addEventListener('visibilitychange', visibilityChangeHandler);
    // Start the update interval
    startUpdateInterval();

    // 页面卸载时清理
    window.addEventListener('beforeunload', cleanup);
  }
}

async function getHtmlBuildTime(): Promise<string | null> {
  const baseUrl = import.meta.env.VITE_BASE_URL || '/';

  try {
    const res = await fetch(`${baseUrl}index.html?time=${Date.now()}`);

    if (!res.ok) {
      console.error('getHtmlBuildTime error:', res.status, res.statusText);
      return null;
    }

    const html = await res.text();
    const match = html.match(/<meta name="buildTime" content="(.*)">/);
    return match?.[1] || null;
  } catch (error) {
    console.error('getHtmlBuildTime error:', error);
    return null;
  }
}
