# 前端开发检查清单

## 📋 新页面开发检查清单

在创建新页面或修改现有页面时，请按照以下清单逐项检查：

### 🏗️ 基础结构

- [ ] **使用标准布局结构**
  ```vue
  <div class="flex flex-col gap-16px p-16px">
    <!-- 页面内容 -->
  </div>
  ```

- [ ] **添加页面组件定义**
  ```typescript
  defineOptions({
    name: 'YourPageName' // 使用 PascalCase
  });
  ```

- [ ] **导入必要的依赖**
  ```typescript
  import { onMounted, onUnmounted, ref } from 'vue';
  ```

### 📱 响应式设计

- [ ] **添加移动端检测**
  ```typescript
  const isMobile = ref(false);
  const checkMobile = () => {
    isMobile.value = window.innerWidth < 768;
  };
  ```

- [ ] **设置生命周期钩子**
  ```typescript
  onMounted(() => {
    checkMobile();
    window.addEventListener('resize', checkMobile);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile);
  });
  ```

- [ ] **使用响应式断点**
  - `sm:` (≥640px) - 小屏幕设备
  - `md:` (≥768px) - 平板设备
  - `lg:` (≥1024px) - 桌面设备
  - `xl:` (≥1280px) - 大屏幕设备

- [ ] **添加移动端适配样式**
  ```css
  @media (max-width: 768px) {
    .flex.flex-col.gap-16px.p-16px {
      padding: 12px;
      gap: 12px;
    }
  }
  ```

### 🧩 组件使用

- [ ] **使用正确的组件命名**
  - ✅ PascalCase: `ElButton`, `ElTable`, `ElForm`
  - ❌ kebab-case: `el-button`, `el-table`, `el-form`

- [ ] **表格组件规范**
  ```vue
  <ElTable
    v-loading="loading"
    :data="tableData"
    class="min-w-800px w-full"
    max-height="70vh"
    stripe
    empty-text="暂无数据"
    :size="isMobile ? 'small' : 'default'"
  >
  ```

- [ ] **表单组件规范**
  ```vue
  <ElForm
    ref="formRef"
    :model="formData"
    :rules="formRules"
    :label-width="isMobile ? '80px' : '100px'"
    :label-position="isMobile ? 'top' : 'right'"
  >
  ```

- [ ] **按钮组件规范**
  ```vue
  <ElButton type="primary" :loading="loading">
    确定
  </ElButton>
  ```

### 🔄 滚动兼容性

- [ ] **避免固定高度**
  - 使用 `max-height` 而不是 `height`
  - 使用相对单位如 `70vh`

- [ ] **表格滚动处理**
  ```vue
  <div class="w-full overflow-x-auto">
    <ElTable class="min-w-800px w-full">
  ```

- [ ] **内容自然流动**
  - 让内容根据实际需要自动调整高度
  - 避免使用 `overflow: hidden`

### ⚡ 性能优化

- [ ] **数据分页**
  ```typescript
  const pagination = reactive({
    page: 1,
    pageSize: 20,
    total: 0
  });
  ```

- [ ] **加载状态管理**
  ```typescript
  const loading = ref(false);
  ```

- [ ] **合理使用计算属性**
  ```typescript
  const filteredData = computed(() => {
    return data.value.filter(item => item.status === 'active');
  });
  ```

### 🚨 错误处理

- [ ] **API错误处理**
  ```typescript
  try {
    const response = await apiCall();
    // 处理成功响应
  } catch (error: any) {
    console.error('操作失败:', error);
    const errorMessage = error.response?.data?.msg || error.message || '操作失败';
    ElMessage.error(errorMessage);
  }
  ```

- [ ] **表单验证**
  ```typescript
  const formRules: FormRules = {
    name: [
      { required: true, message: '请输入名称', trigger: 'blur' }
    ]
  };
  ```

- [ ] **用户反馈**
  - 加载状态: `<ElButton :loading="loading">`
  - 空状态: `<ElTable empty-text="暂无数据">`
  - 错误提示: `ElMessage.error('操作失败')`

### 🎨 样式规范

- [ ] **优先使用 Tailwind CSS**
  ```vue
  <div class="flex items-center gap-12px p-16px">
  ```

- [ ] **自定义样式使用 scoped**
  ```vue
  <style scoped>
  .custom-class {
    /* 自定义样式 */
  }
  </style>
  ```

- [ ] **避免内联样式**
  ```vue
  <!-- ❌ 不推荐 -->
  <div style="margin: 16px;">

  <!-- ✅ 推荐 -->
  <div class="m-16px">
  ```

### 🧪 测试验证

- [ ] **功能测试**
  - 页面在不同设备尺寸下正常显示
  - 表格可以正常滚动和分页
  - 表单验证正确工作
  - 错误处理正确显示

- [ ] **性能测试**
  - 页面加载时间合理
  - 大数据量下表格性能良好
  - 内存使用合理，无内存泄漏

- [ ] **兼容性测试**
  - 主体滚动模式下正常工作
  - 外层滚动模式下正常工作
  - 移动端触摸操作正常

### 🔧 构建验证

- [ ] **TypeScript类型检查通过**
  ```bash
  npm run typecheck
  ```

- [ ] **ESLint检查通过**
  ```bash
  npm run lint
  ```

- [ ] **项目构建成功**
  ```bash
  npm run build
  ```

### 📚 文档和注释

- [ ] **添加必要的注释**
  ```typescript
  // 移动端检测
  const isMobile = ref(false);

  /**
   * 初始化页面数据
   */
  const initPageData = async () => {
    // 实现逻辑
  };
  ```

- [ ] **更新相关文档**
  - 如果添加了新功能，更新用户文档
  - 如果修改了API，更新API文档

### ✅ 最终检查

- [ ] **代码审查**
  - 代码逻辑清晰
  - 变量命名规范
  - 无冗余代码

- [ ] **用户体验**
  - 操作流程顺畅
  - 错误提示友好
  - 加载状态明确

- [ ] **安全性**
  - 用户输入验证
  - 权限检查
  - 敏感信息保护

## 🚀 快速检查命令

```bash
# 类型检查
npm run typecheck

# 代码规范检查
npm run lint

# 构建测试
npm run build

# 开发服务器
npm run dev
```

## 📖 参考文档

- [前端界面规范指南](./FRONTEND_STANDARDS.md)
- [页面模板使用指南](./PAGE_TEMPLATE_GUIDE.md)
- [组件数据绑定规范](./COMPONENT_DATA_BINDING.md)

---

**注意**: 此检查清单是强制性的，所有新开发的页面都必须通过这些检查项。如有疑问，请参考现有的标准页面实现或咨询开发团队。
