# 前端组件数据绑定规范

## 概述

本文档规范了前端组件中数据绑定的标准做法，确保与后端API响应格式保持一致。

## 用户相关组件

### 个人中心组件 (src/views/user-center/index.vue)

#### 数据绑定规范

```typescript
// ✅ 正确的数据绑定
const userInfo = computed(() => authStore.userInfo);

// 显示用户角色
const getRoleName = computed(() => {
  const roleMap = {
    1: '管理员',
    2: '代理商', 
    3: '普通用户'
  };
  return roleMap[userInfo.value.role] || '未知角色';
});

// 显示用户费率
const userRate = computed(() => {
  return (userInfo.value.price_rate * 100).toFixed(1) + '%';
});

// 显示账户余额
const balance = computed(() => {
  return userInfo.value.balance?.toFixed(2) || '0.00';
});

// 显示注册时间
const createTime = computed(() => {
  return new Date(userInfo.value.create_time).toLocaleDateString('zh-CN');
});
```

#### 模板使用

```vue
<template>
  <div class="user-center">
    <!-- ✅ 正确的字段引用 -->
    <div class="user-info">
      <h3>{{ userInfo.nickname || userInfo.username }}</h3>
      <p>用户ID: {{ userInfo.user_id }}</p>
      <p>角色: {{ getRoleName }}</p>
      <p>费率: {{ userRate }}</p>
      <p>余额: ¥{{ balance }}</p>
      <p>注册时间: {{ createTime }}</p>
      <p>邀请码: {{ userInfo.invite_code }}</p>
    </div>
    
    <!-- ✅ 条件显示 -->
    <div v-if="userInfo.total_recharge" class="recharge-info">
      <p>总充值: ¥{{ userInfo.total_recharge.toFixed(2) }}</p>
    </div>
    
    <!-- ✅ 推荐关系显示 -->
    <div v-if="userInfo.referrer_id" class="referrer-info">
      <p>推荐人ID: {{ userInfo.referrer_id }}</p>
    </div>
  </div>
</template>
```

#### 错误示例

```vue
<template>
  <!-- ❌ 错误的字段引用 -->
  <div class="user-center">
    <!-- <span>{{ userInfo.created_at }}</span> -->
    <!-- <span>{{ userInfo.userRate }}</span> -->
    <!-- <span>{{ userInfo.user_role }}</span> -->
  </div>
</template>
```

### 用户管理组件 (src/views/manage/user/index.vue)

#### 表格列定义

```typescript
// ✅ 正确的表格列定义
const columns = [
  {
    key: 'user_id',
    title: '用户ID',
    align: 'center'
  },
  {
    key: 'username', 
    title: '用户名',
    align: 'center'
  },
  {
    key: 'nickname',
    title: '昵称',
    align: 'center'
  },
  {
    key: 'userRole',  // 注意：API返回的是userRole
    title: '角色',
    align: 'center',
    render: (row: any) => {
      const roleMap = {
        'admin': '管理员',
        'agent': '代理商',
        'user': '普通用户'
      };
      return roleMap[row.userRole] || '未知';
    }
  },
  {
    key: 'balance',
    title: '余额',
    align: 'center',
    render: (row: any) => `¥${row.balance?.toFixed(2) || '0.00'}`
  },
  {
    key: 'userRate',  // 注意：API返回的是userRate
    title: '费率',
    align: 'center',
    render: (row: any) => `${(row.userRate * 100).toFixed(1)}%`
  },
  {
    key: 'createTime',  // 注意：API返回的是createTime
    title: '注册时间',
    align: 'center'
  }
];
```

#### 表单数据绑定

```typescript
// ✅ 正确的表单数据结构
interface UserFormData {
  username: string;
  nickname: string;
  email: string;
  phone: string;
  role: number;        // 数值角色
  user_role: string;   // 字符串角色
  status: number;
  balance: number;
  price_rate: number;  // 费率字段
}

// 表单提交处理
const handleSubmit = async (formData: UserFormData) => {
  try {
    const response = await api.createUser({
      ...formData,
      // 确保字段名称与后端API期望一致
      role: formData.role,
      user_role: formData.user_role,
      price_rate: formData.price_rate
    });
    
    if (response.code === '0000') {
      ElMessage.success('用户创建成功');
      await refreshUserList();
    }
  } catch (error) {
    ElMessage.error('用户创建失败');
  }
};
```

## 订单相关组件

### 订单管理组件 (src/views/manage/order/index.vue)

#### 数据绑定规范

```typescript
// ✅ 正确的订单数据处理
const processOrderData = (orders: any[]) => {
  return orders.map(order => ({
    ...order,
    // JSON字段正确处理
    courseInfo: order.course_info || {},
    extraData: order.extra_data || {},
    // 时间字段格式化
    createTime: new Date(order.createTime).toLocaleString('zh-CN'),
    updateTime: new Date(order.updateTime).toLocaleString('zh-CN'),
    // 金额格式化
    amount: order.amount?.toFixed(2) || '0.00',
    costAmount: order.costAmount?.toFixed(2) || '0.00'
  }));
};
```

#### 表格列定义

```typescript
const orderColumns = [
  {
    key: 'orderId',  // 注意：API返回的是orderId
    title: '订单ID',
    align: 'center'
  },
  {
    key: 'orderNo',
    title: '订单号',
    align: 'center'
  },
  {
    key: 'username',
    title: '用户名',
    align: 'center'
  },
  {
    key: 'courseName',
    title: '课程名称',
    align: 'center'
  },
  {
    key: 'amount',
    title: '订单金额',
    align: 'center',
    render: (row: any) => `¥${row.amount}`
  },
  {
    key: 'status',
    title: '状态',
    align: 'center',
    render: (row: any) => row.statusText || '未知状态'
  },
  {
    key: 'createTime',
    title: '创建时间',
    align: 'center'
  }
];
```

## 数据验证规范

### 表单验证规则

```typescript
// ✅ 统一的验证规则
const userFormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { pattern: /^\d{5,11}$/, message: '用户名必须是5-11位数字(QQ号)', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { 
      pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{6,}$/, 
      message: '密码必须包含大小写字母和数字，至少6位', 
      trigger: 'blur' 
    }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  price_rate: [
    { required: true, message: '请输入费率', trigger: 'blur' },
    { type: 'number', min: 0, max: 1, message: '费率必须在0-1之间', trigger: 'blur' }
  ]
};
```

## API调用规范

### 统一的API调用方式

```typescript
// ✅ 正确的API调用
import { request } from '@/service/request';

// 获取用户信息
export const getUserInfo = () => {
  return request<Auth.UserInfo>({
    url: '/auth/getUserInfo',
    method: 'get'
  });
};

// 获取用户列表
export const getUserList = (params: any) => {
  return request<{
    list: any[];
    total: number;
  }>({
    url: '/user/list',
    method: 'get',
    params
  });
};

// 创建用户
export const createUser = (data: any) => {
  return request({
    url: '/user/create',
    method: 'post',
    data: {
      ...data,
      // 确保字段名称正确
      role: data.role,
      user_role: data.user_role,
      price_rate: data.price_rate
    }
  });
};
```

## 错误处理规范

### 统一的错误处理

```typescript
// ✅ 统一的错误处理
const handleApiError = (error: any) => {
  console.error('API调用失败:', error);
  
  if (error.response?.data?.msg) {
    ElMessage.error(error.response.data.msg);
  } else if (error.message) {
    ElMessage.error(error.message);
  } else {
    ElMessage.error('操作失败，请重试');
  }
};

// 在组件中使用
const fetchUserList = async () => {
  try {
    loading.value = true;
    const response = await getUserList(searchParams.value);
    
    if (response.code === '0000') {
      userList.value = response.data.list;
      total.value = response.data.total;
    } else {
      ElMessage.error(response.msg || '获取用户列表失败');
    }
  } catch (error) {
    handleApiError(error);
  } finally {
    loading.value = false;
  }
};
```

## 注意事项

### 字段映射对照表

| 前端期望字段 | API响应字段 | 数据库字段 | 说明 |
|-------------|------------|-----------|------|
| `user_id` | `user_id` | `user_id` | 用户ID |
| `role` | `role` | `role` | 数值角色 |
| `userRole` | `userRole` | `user_role` | 字符串角色 |
| `userRate` | `userRate` | `price_rate` | 用户费率 |
| `createTime` | `createTime` | `create_time` | 创建时间 |
| `updateTime` | `updateTime` | `update_time` | 更新时间 |

### 开发建议

1. **类型安全**: 使用TypeScript确保类型安全
2. **字段验证**: 在使用字段前检查是否存在
3. **错误处理**: 统一的错误处理机制
4. **数据格式化**: 统一的数据格式化函数
5. **缓存策略**: 合理使用数据缓存

### 更新记录

- **2024-01-XX**: 创建组件数据绑定规范文档
- **2024-01-XX**: 添加用户相关组件规范
- **2024-01-XX**: 添加订单相关组件规范
- **2024-01-XX**: 添加API调用和错误处理规范
