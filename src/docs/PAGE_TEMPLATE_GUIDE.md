# 页面模板使用指南

## 概述

本文档提供了标准页面模板的使用指南，确保所有新页面都符合项目规范，包括布局、响应式设计、滚动兼容性等方面。

## 模板类型

### 1. 标准列表页面模板 (StandardPageTemplate.vue)

适用于数据展示、表格列表等页面。

#### 特性
- ✅ 标准布局结构 (`flex flex-col gap-16px p-16px`)
- ✅ 移动端检测和响应式设计
- ✅ 滚动兼容性 (主体滚动模式)
- ✅ 表格优化 (最小宽度、溢出滚动)
- ✅ 统一的错误处理
- ✅ 加载状态管理

#### 使用方法

```vue
<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';

defineOptions({
  name: 'YourPageName' // 修改为实际页面名称
});

// 移动端检测 (必需)
const isMobile = ref(false);
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

// 页面状态
const loading = ref(false);
const tableData = ref([]);

// 生命周期 (必需)
onMounted(() => {
  checkMobile();
  window.addEventListener('resize', checkMobile);
  initPageData();
});

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile);
});

// 初始化页面数据
const initPageData = async () => {
  loading.value = true;
  try {
    // 加载数据的API调用
    const response = await fetchYourData();
    tableData.value = response.data;
  } catch (error: any) {
    console.error('页面数据加载失败:', error);
    const errorMessage = error.response?.data?.msg || error.message || '加载失败';
    ElMessage.error(errorMessage);
  } finally {
    loading.value = false;
  }
};
</script>
```

### 2. 标准表单页面模板 (StandardFormTemplate.vue)

适用于数据录入、编辑等表单页面。

#### 特性
- ✅ 响应式表单布局 (移动端标签位置自动调整)
- ✅ 统一的表单验证规则
- ✅ 错误处理和用户反馈
- ✅ 数据预览功能
- ✅ 移动端优化

#### 使用方法

```vue
<script setup lang="ts">
import { reactive, ref } from 'vue';
import type { FormInstance, FormRules } from 'element-plus';

// 表单数据结构
const formData = reactive({
  // 根据实际需求定义字段
  name: '',
  email: '',
  // ...其他字段
});

// 表单验证规则
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ]
};

// 提交处理
const handleSubmit = async () => {
  if (!formRef.value) return;

  try {
    await formRef.value.validate();
    loading.value = true;

    // API调用
    await submitYourForm(formData);
    ElMessage.success('保存成功');
  } catch (error: any) {
    const errorMessage = error.response?.data?.msg || error.message || '保存失败';
    ElMessage.error(errorMessage);
  } finally {
    loading.value = false;
  }
};
</script>
```

## 布局规范

### 1. 标准布局结构

所有页面必须使用以下标准布局：

```vue
<template>
  <div class="flex flex-col gap-16px p-16px">
    <!-- 页面内容 -->
  </div>
</template>
```

### 2. 响应式断点

使用以下响应式断点：

- `sm:` - 640px 及以上
- `md:` - 768px 及以上
- `lg:` - 1024px 及以上
- `xl:` - 1280px 及以上

### 3. 移动端适配

```css
/* 移动端适配样式 */
@media (max-width: 768px) {
  .flex.flex-col.gap-16px.p-16px {
    padding: 12px;
    gap: 12px;
  }
}
```

## 组件使用规范

### 1. Element Plus 组件

- ✅ 使用 PascalCase 命名：`ElButton`、`ElTable`
- ❌ 不要使用 kebab-case：`el-button`、`el-table`

### 2. 表格组件

```vue
<ElTable
  v-loading="loading"
  :data="tableData"
  class="min-w-800px w-full"
  max-height="70vh"
  stripe
  empty-text="暂无数据"
  :size="isMobile ? 'small' : 'default'"
>
  <!-- 表格列 -->
</ElTable>
```

### 3. 表单组件

```vue
<ElForm
  ref="formRef"
  :model="formData"
  :rules="formRules"
  :label-width="isMobile ? '80px' : '100px'"
  :label-position="isMobile ? 'top' : 'right'"
>
  <!-- 表单项 -->
</ElForm>
```

## 错误处理规范

### 1. API错误处理

```typescript
try {
  const response = await apiCall();
  // 处理成功响应
} catch (error: any) {
  console.error('操作失败:', error);
  // 优先显示后端返回的错误信息
  const errorMessage = error.response?.data?.msg || error.message || '操作失败';
  ElMessage.error(errorMessage);
}
```

### 2. 表单验证错误

```typescript
try {
  await formRef.value?.validate();
  // 验证通过，继续处理
} catch (error) {
  // 表单验证失败，Element Plus会自动显示错误信息
  return;
}
```

## 性能优化

### 1. 表格性能

- 设置合理的 `max-height`
- 使用分页限制数据量
- 为大表格添加虚拟滚动

### 2. 移动端优化

- 检测移动端并调整组件大小
- 使用响应式布局
- 优化触摸交互

## 最佳实践

### 1. 命名规范

- 页面组件：使用 PascalCase，如 `UserManagement`
- 变量和函数：使用 camelCase，如 `userData`、`handleSubmit`

### 2. 代码组织

```typescript
// 1. 导入
import { ... } from 'vue';

// 2. 组件定义
defineOptions({ name: 'ComponentName' });

// 3. 响应式数据
const data = ref();

// 4. 计算属性
const computed = computed(() => {});

// 5. 方法
const method = () => {};

// 6. 生命周期
onMounted(() => {});
```

### 3. 注释规范

```typescript
// 移动端检测
const isMobile = ref(false);

/**
 * 初始化页面数据
 */
const initPageData = async () => {
  // 实现逻辑
};
```

## 快速开始模板

### 最小化页面模板

```vue
<script setup lang="ts">
import { onMounted, onUnmounted, ref } from 'vue';

defineOptions({
  name: 'YourPageName'
});

// 移动端检测 (必需)
const isMobile = ref(false);
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

// 页面状态
const loading = ref(false);

// 生命周期 (必需)
onMounted(() => {
  checkMobile();
  window.addEventListener('resize', checkMobile);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile);
});
</script>

<template>
  <!-- 标准布局 (必需) -->
  <div class="flex flex-col gap-16px p-16px">
    <ElCard>
      <template #header>
        <span class="text-lg font-medium">页面标题</span>
      </template>

      <!-- 页面内容 -->
      <div>页面内容</div>
    </ElCard>
  </div>
</template>

<style scoped>
/* 移动端适配 (必需) */
@media (max-width: 768px) {
  .flex.flex-col.gap-16px.p-16px {
    padding: 12px;
    gap: 12px;
  }
}
</style>
```

## 检查清单

创建新页面时，请确保：

- [ ] 使用标准布局结构 `flex flex-col gap-16px p-16px`
- [ ] 添加移动端检测和响应式设计
- [ ] 设置合理的表格高度和滚动
- [ ] 使用正确的组件命名 (PascalCase)
- [ ] 添加适当的错误处理
- [ ] 包含加载状态管理
- [ ] 添加移动端适配样式
- [ ] 遵循代码组织规范
