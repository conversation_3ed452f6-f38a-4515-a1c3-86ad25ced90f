# 前端界面规范指南

## 概述

本文档规定了项目前端界面开发的标准规范，确保所有页面具有一致的用户体验和代码质量。

## 🎯 核心原则

### 1. 一致性原则
- 所有页面使用统一的布局结构
- 统一的组件使用方式
- 一致的视觉风格和交互模式

### 2. 响应式原则
- 支持桌面端、平板和移动端
- 使用标准的响应式断点
- 移动端优先的设计思路

### 3. 性能原则
- 优化加载速度和渲染性能
- 合理的数据分页和虚拟滚动
- 避免不必要的重渲染

### 4. 可访问性原则
- 良好的键盘导航支持
- 合适的颜色对比度
- 清晰的错误提示和用户反馈

## 📐 布局规范

### 标准页面布局

**必须使用的标准布局结构：**

```vue
<template>
  <div class="flex flex-col gap-16px p-16px">
    <!-- 页面内容 -->
  </div>
</template>
```

### 布局要求

1. **外层容器**
   - 使用 `flex flex-col` 垂直布局
   - 使用 `gap-16px` 统一间距
   - 使用 `p-16px` 统一内边距

2. **内容区域**
   - 每个主要区域使用 `ElCard` 包装
   - 避免使用固定高度，让内容自然流动
   - 使用相对高度如 `max-height="70vh"`

3. **移动端适配**
   ```css
   @media (max-width: 768px) {
     .flex.flex-col.gap-16px.p-16px {
       padding: 12px;
       gap: 12px;
     }
   }
   ```

## 📱 响应式设计

### 断点定义

| 断点 | 屏幕宽度 | 用途 |
|------|----------|------|
| `sm:` | ≥640px | 小屏幕设备 |
| `md:` | ≥768px | 平板设备 |
| `lg:` | ≥1024px | 桌面设备 |
| `xl:` | ≥1280px | 大屏幕设备 |

### 响应式布局模式

1. **网格布局**
   ```vue
   <div class="grid grid-cols-1 gap-16px md:gap-24px lg:grid-cols-2">
     <!-- 内容 -->
   </div>
   ```

2. **弹性布局**
   ```vue
   <div class="flex flex-col gap-12px sm:flex-row sm:gap-16px">
     <!-- 内容 -->
   </div>
   ```

3. **表单布局**
   ```vue
   <ElForm
     :label-width="isMobile ? '80px' : '100px'"
     :label-position="isMobile ? 'top' : 'right'"
   >
   ```

### 移动端检测

**每个页面必须包含移动端检测：**

```typescript
// 移动端检测 (必需)
const isMobile = ref(false);
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

// 生命周期 (必需)
onMounted(() => {
  checkMobile();
  window.addEventListener('resize', checkMobile);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile);
});
```

## 🧩 组件使用规范

### Element Plus 组件

1. **命名规范**
   - ✅ 使用 PascalCase：`ElButton`、`ElTable`、`ElForm`
   - ❌ 不要使用 kebab-case：`el-button`、`el-table`

2. **表格组件**
   ```vue
   <ElTable
     v-loading="loading"
     :data="tableData"
     class="min-w-800px w-full"
     max-height="70vh"
     stripe
     empty-text="暂无数据"
     :size="isMobile ? 'small' : 'default'"
   >
   ```

3. **表单组件**
   ```vue
   <ElForm
     ref="formRef"
     :model="formData"
     :rules="formRules"
     :label-width="isMobile ? '80px' : '100px'"
   >
   ```

4. **按钮组件**
   ```vue
   <ElButton type="primary" :loading="loading">
     确定
   </ElButton>
   ```

## 🎨 样式规范

### CSS 类命名

1. **使用 Tailwind CSS 优先**
   ```vue
   <div class="flex items-center gap-12px p-16px">
   ```

2. **自定义样式使用 scoped**
   ```vue
   <style scoped>
   .custom-class {
     /* 自定义样式 */
   }
   </style>
   ```

3. **避免内联样式**
   ```vue
   <!-- ❌ 不推荐 -->
   <div style="margin: 16px;">

   <!-- ✅ 推荐 -->
   <div class="m-16px">
   ```

### 颜色使用

使用项目预定义的颜色变量：
- 主色：`text-primary`、`bg-primary`
- 成功：`text-success`、`bg-success`
- 警告：`text-warning`、`bg-warning`
- 错误：`text-error`、`bg-error`

## 🔄 滚动兼容性

### 滚动模式支持

项目支持两种滚动模式：
1. **主体滚动模式** (`content`)：内容区域自己滚动
2. **外层滚动模式** (`wrapper`)：整个页面滚动

### 兼容性要求

1. **避免固定高度**
   ```vue
   <!-- ❌ 不推荐 -->
   <div style="height: 400px;">

   <!-- ✅ 推荐 -->
   <div class="max-h-70vh">
   ```

2. **表格滚动处理**
   ```vue
   <div class="w-full overflow-x-auto">
     <ElTable class="min-w-800px w-full">
   ```

3. **内容自然流动**
   - 让内容根据实际需要自动调整高度
   - 使用 `max-height` 而不是 `height`
   - 避免使用 `overflow: hidden`

## ⚡ 性能优化

### 表格性能

1. **数据分页**
   ```typescript
   const pagination = reactive({
     page: 1,
     pageSize: 20,
     total: 0
   });
   ```

2. **虚拟滚动**
   - 对于大量数据使用虚拟滚动
   - 设置合理的 `max-height`

3. **懒加载**
   - 图片使用懒加载
   - 大型组件按需加载

### 渲染优化

1. **避免不必要的响应式**
   ```typescript
   // ✅ 使用 shallowRef 对于大对象
   const largeData = shallowRef([]);
   ```

2. **合理使用计算属性**
   ```typescript
   // ✅ 缓存复杂计算
   const filteredData = computed(() => {
     return data.value.filter(item => item.status === 'active');
   });
   ```

## 🚨 错误处理

### API 错误处理

```typescript
try {
  const response = await apiCall();
  // 处理成功响应
} catch (error: any) {
  console.error('操作失败:', error);
  // 优先显示后端返回的错误信息
  const errorMessage = error.response?.data?.msg || error.message || '操作失败';
  ElMessage.error(errorMessage);
}
```

### 表单验证

```typescript
const formRules: FormRules = {
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
    { min: 2, max: 50, message: '名称长度在 2 到 50 个字符', trigger: 'blur' }
  ]
};
```

### 用户反馈

1. **加载状态**
   ```vue
   <ElButton :loading="loading">提交</ElButton>
   ```

2. **空状态**
   ```vue
   <ElTable empty-text="暂无数据">
   ```

3. **错误提示**
   ```typescript
   ElMessage.error('操作失败，请重试');
   ```

## 🧪 测试要求

### 功能测试

- [ ] 页面在不同设备尺寸下正常显示
- [ ] 表格可以正常滚动和分页
- [ ] 表单验证正确工作
- [ ] 错误处理正确显示

### 性能测试

- [ ] 页面加载时间合理
- [ ] 大数据量下表格性能良好
- [ ] 内存使用合理，无内存泄漏

### 兼容性测试

- [ ] 主体滚动模式下正常工作
- [ ] 外层滚动模式下正常工作
- [ ] 移动端触摸操作正常

## 📋 开发检查清单

创建新页面时，请确保：

- [ ] 使用标准布局结构 `flex flex-col gap-16px p-16px`
- [ ] 添加移动端检测和响应式设计
- [ ] 使用正确的组件命名 (PascalCase)
- [ ] 设置合理的表格高度和滚动
- [ ] 添加适当的错误处理
- [ ] 包含加载状态管理
- [ ] 添加移动端适配样式
- [ ] 遵循代码组织规范
- [ ] 通过构建测试
- [ ] 在不同设备上测试通过

## 🔧 工具和资源

### 开发工具

- **ESLint**: 代码质量检查
- **TypeScript**: 类型安全
- **Tailwind CSS**: 样式框架
- **Element Plus**: UI 组件库

### 参考资源

- [页面模板使用指南](./PAGE_TEMPLATE_GUIDE.md)
- [组件数据绑定规范](./COMPONENT_DATA_BINDING.md)
- [Element Plus 官方文档](https://element-plus.org/)
- [Tailwind CSS 官方文档](https://tailwindcss.com/)

---

**注意**: 本规范是强制性的，所有新开发的页面都必须遵循这些标准。如有疑问，请参考现有的标准页面实现。
