import type { AxiosResponse } from 'axios';
import { BACKEND_ERROR_CODE, createRequest } from '@sa/axios';
import { useAuthStore } from '@/store/modules/auth';
import { getServiceBaseURL } from '@/utils/service';
import { $t } from '@/locales';
import { getAuthorization, handleExpiredRequest, showErrorMsg } from './shared';
import type { RequestInstanceState } from './type';

const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y';

const { otherBaseURL } = getServiceBaseURL(import.meta.env, isHttpProxy);

// 获取静态baseURL配置
const { baseURL: staticBaseURL } = getServiceBaseURL(import.meta.env, isHttpProxy);

export const request = createRequest<App.Service.Response, RequestInstanceState>(
  {
    baseURL: staticBaseURL // 使用静态配置的baseURL
  },
  {
    async onRequest(config) {
      // 确保baseURL已设置
      if (!config.baseURL || config.baseURL === '') {
        config.baseURL = staticBaseURL;
      }

      const Authorization = getAuthorization();
      Object.assign(config.headers, { Authorization });

      return config;
    },
    isBackendSuccess(response) {
      // SoybeanAdmin标准响应格式：{"code": "0000", "msg": "操作成功", "data": {...}}
      const data = response.data as any;
      return String(data.code) === import.meta.env.VITE_SERVICE_SUCCESS_CODE;
    },
    async onBackendFail(response, instance) {
      const authStore = useAuthStore();
      // SoybeanAdmin标准响应格式：获取错误代码
      const responseCode = String((response.data as any).code || '');

      function handleLogout() {
        authStore.resetStore();
      }

      function logoutAndCleanup() {
        handleLogout();
        window.removeEventListener('beforeunload', handleLogout);

        const errorMsg = (response.data as any).msg || '未知错误';
        request.state.errMsgStack = (request.state.errMsgStack || []).filter(msg => msg !== errorMsg);
      }

      // 当后端返回的code在logoutCodes中时，表示需要退出登录
      const logoutCodes = import.meta.env.VITE_SERVICE_LOGOUT_CODES?.split(',') || [];
      if (logoutCodes.includes(responseCode)) {
        handleLogout();
        return null;
      }

      // 当后端返回的code在modalLogoutCodes中时，表示需要显示弹窗并退出登录
      const modalLogoutCodes = import.meta.env.VITE_SERVICE_MODAL_LOGOUT_CODES?.split(',') || [];
      const errorMsg = (response.data as any).msg || '认证失败';
      if (modalLogoutCodes.includes(responseCode) && !request.state.errMsgStack?.includes(errorMsg)) {
        request.state.errMsgStack = [...(request.state.errMsgStack || []), errorMsg];

        // 防止用户刷新页面 - 确保不重复添加监听器
        window.removeEventListener('beforeunload', handleLogout);
        window.addEventListener('beforeunload', handleLogout);

        window.$messageBox
          ?.confirm(errorMsg, $t('common.error'), {
            confirmButtonText: $t('common.confirm'),
            cancelButtonText: $t('common.cancel'),
            type: 'error',
            closeOnClickModal: false,
            closeOnPressEscape: false
          })
          .then(() => {
            logoutAndCleanup();
          });

        return null;
      }

      // 当后端返回的code在expiredTokenCodes中时，表示令牌过期，需要刷新令牌
      // 刷新令牌的API不能返回expiredTokenCodes中的错误码，否则会导致死循环，应返回logoutCodes或modalLogoutCodes
      const expiredTokenCodes = import.meta.env.VITE_SERVICE_EXPIRED_TOKEN_CODES?.split(',') || [];
      if (expiredTokenCodes.includes(responseCode)) {
        const success = await handleExpiredRequest(request.state);
        if (success) {
          const Authorization = getAuthorization();
          Object.assign(response.config.headers, { Authorization });

          return instance.request(response.config) as Promise<AxiosResponse>;
        }
      }

      // 对于普通的业务错误，抛出包含后端错误信息的异常
      const backendErrorMsg = (response.data as any).msg || '请求失败';
      const error = new Error(backendErrorMsg);
      (error as any).code = 'BACKEND_ERROR';
      (error as any).response = response;
      throw error;
    },
    transformBackendResponse(response) {
      // SoybeanAdmin标准响应格式：{"code": "0000", "msg": "操作成功", "data": {...}}
      // 安全地返回data字段内容
      const responseData = response.data as any;
      if (responseData && typeof responseData === 'object' && 'data' in responseData) {
        return responseData.data;
      }
      // 如果响应格式不正确，返回null以避免前端访问undefined属性
      if (import.meta.env.DEV) {
        // eslint-disable-next-line no-console
        console.warn('🔍 [请求] API响应格式不正确:', responseData);
      }
      return null;
    },
    onError(error) {
      // 当请求失败时，显示错误信息
      let message = error.message;
      let backendErrorCode = '';

      // 获取后端错误信息和代码 - SoybeanAdmin标准格式
      if (error.code === BACKEND_ERROR_CODE) {
        const responseData = error.response?.data as any;
        message = responseData?.msg || message;
        backendErrorCode = String(responseData?.code || '');
      }

      // 错误信息在弹窗中显示
      const modalLogoutCodes = import.meta.env.VITE_SERVICE_MODAL_LOGOUT_CODES?.split(',') || [];
      if (modalLogoutCodes.includes(backendErrorCode)) {
        return;
      }

      // 当令牌过期时，刷新令牌并重试请求，无需显示错误信息
      const expiredTokenCodes = import.meta.env.VITE_SERVICE_EXPIRED_TOKEN_CODES?.split(',') || [];
      if (expiredTokenCodes.includes(backendErrorCode)) {
        return;
      }

      // 只显示有意义的错误信息
      if (message && message.trim() !== '' && message !== 'the backend request error') {
        showErrorMsg(request.state, message);
      }
    }
  }
);

export const demoRequest = createRequest<App.Service.DemoResponse>(
  {
    baseURL: otherBaseURL.demo
  },
  {
    async onRequest(config) {
      const { headers } = config;

      // 设置令牌
      const { getToken } = await import('@/utils/auth');
      const token = getToken();
      const Authorization = token ? `Bearer ${token}` : null;
      Object.assign(headers, { Authorization });

      return config;
    },
    isBackendSuccess(response) {
      // 当后端返回的状态码为"200"时，表示请求成功
      return response.data.status === '200';
    },
    async onBackendFail(_response) {
      // 当后端返回的状态码不是"200"时，表示请求失败
      // 例如：令牌过期，刷新令牌并重试请求
    },
    transformBackendResponse(response) {
      return response.data.result;
    },
    onError(error) {
      // 当请求失败时，显示错误信息
      let message = error.message;

      // 显示后端错误信息
      if (error.code === BACKEND_ERROR_CODE) {
        message = error.response?.data?.message || message;
      }

      window.$message?.error(message);
    }
  }
);
