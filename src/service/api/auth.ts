import { request } from '../request/index';

/**
 * 用户登录
 *
 * @param userName 用户名
 * @param password 密码
 */
export function fetchLogin(userName: string, password: string) {
  return request<Api.Auth.LoginToken>({
    url: '/api/auth/login',
    method: 'post',
    data: {
      userName,
      password
    }
  });
}

/**
 * 用户注册（基于邀请码）
 *
 * @param userName QQ账号
 * @param password 密码
 * @param inviteCode 邀请码
 * @param nickname 昵称
 */
export function fetchRegister(userName: string, password: string, inviteCode: string, nickname?: string) {
  return request<Api.Auth.LoginToken>({
    url: '/api/auth/register',
    method: 'post',
    data: {
      userName,
      password,
      inviteCode,
      nickname
    }
  });
}

/** 获取用户信息 */
export function fetchGetUserInfo() {
  return request<Api.Auth.UserInfo>({ url: '/api/auth/getUserInfo' });
}

// ===== 用户管理接口 =====

/** 获取用户列表 */
export function fetchUserList(params: {
  page?: number;
  pageSize?: number;
  keyword?: string;
  userRole?: string;
  status?: number;
}) {
  return request({
    url: '/api/user/list',
    method: 'get',
    params
  });
}

/** 创建用户 */
export function fetchCreateUser(data: {
  username: string;
  email?: string;
  password: string;
  userRate?: number;
  userRole?: string;
  balance?: number;
  status?: number;
}) {
  return request({
    url: '/api/user/create',
    method: 'post',
    data
  });
}

/** 更新用户信息 */
export function fetchUpdateUser(
  userId: number,
  data: {
    email?: string;
    userRate?: number;
    userRole?: string;
    balance?: number;
    status?: number;
    password?: string;
  }
) {
  return request({
    url: `/api/user/update/${userId}`,
    method: 'put',
    data
  });
}

/** 删除用户 */
export function fetchDeleteUser(userId: number) {
  return request({
    url: `/api/user/delete/${userId}`,
    method: 'delete'
  });
}

/** 批量更新用户费率 */
export function fetchBatchUpdateUserRate(data: { userIds: number[]; userRate: number }) {
  return request({
    url: '/api/user/batch-rate',
    method: 'post',
    data
  });
}

/** 获取用户统计信息 */
export function fetchUserStats() {
  return request({
    url: '/api/user/stats',
    method: 'get'
  });
}

/**
 * 刷新令牌
 *
 * @param refreshToken 刷新令牌
 */
export function fetchRefreshToken(refreshToken: string) {
  return request<Api.Auth.LoginToken>({
    url: '/api/auth/refreshToken',
    method: 'post',
    data: {
      refreshToken
    }
  });
}

/**
 * 修改密码
 *
 * @param oldPassword 旧密码
 * @param newPassword 新密码
 */
export function fetchChangePassword(oldPassword: string, newPassword: string) {
  return request({
    url: '/api/auth/changePassword',
    method: 'post',
    data: {
      oldPassword,
      newPassword
    }
  });
}

/**
 * 更新用户信息
 *
 * @param userInfo 用户信息
 */
export function fetchUpdateUserInfo(userInfo: { nickname?: string; email?: string; phone?: string; avatar?: string }) {
  return request<Api.Auth.UserInfo>({
    url: '/api/auth/updateUserInfo',
    method: 'put',
    data: userInfo
  });
}

/** 退出登录 */
/** 用户登出 */
export function fetchLogout() {
  return request({
    url: '/api/auth/logout',
    method: 'post'
  });
}

/** 更新邀请码 */
export function fetchUpdateInviteCode() {
  return request<{ inviteCode: string; message: string }>({
    url: '/api/auth/updateInviteCode',
    method: 'post'
  });
}

/** 获取邀请统计 */
export function fetchInviteStats() {
  return request<{
    totalInvited: number;
    activeInvited: number;
    recentInvited: number;
    subordinatesList: Array<{
      userId: number;
      username: string;
      nickname: string;
      status: number;
      createTime: string;
    }>;
    superior: {
      userId: number;
      username: string;
      nickname: string;
    } | null;
  }>({
    url: '/api/auth/inviteStats',
    method: 'get'
  });
}
