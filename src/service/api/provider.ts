import { request } from '../request/index';

/**
 * 供应商管理API
 * 遵循项目规范，提供完整的供应商管理接口
 */

// ===== 供应商管理接口 =====

/** 获取供应商列表 */
export function fetchProviderList(params: { page?: number; pageSize?: number; keyword?: string; status?: number }) {
  return request({
    url: '/api/provider/list',
    method: 'get',
    params
  });
}

/** 获取供应商详情 */
export function fetchProviderDetail(providerId: number) {
  return request({
    url: `/api/provider/${providerId}`,
    method: 'get'
  });
}

/** 创建供应商 */
export function fetchCreateProvider(data: {
  code: string;
  name: string;
  logoUrl?: string;
  apiUrl?: string;
  username?: string;
  password?: string;
  token?: string;
  ipWhitelist?: string;
  apiConfig?: any;
  status?: number;
}) {
  return request({
    url: '/api/provider/create',
    method: 'post',
    data
  });
}

/** 更新供应商 */
export function fetchUpdateProvider(
  providerId: number,
  data: {
    code?: string;
    name?: string;
    logoUrl?: string;
    apiUrl?: string;
    username?: string;
    password?: string;
    token?: string;
    ipWhitelist?: string;
    apiConfig?: any;
    status?: number;
  }
) {
  return request({
    url: `/api/provider/${providerId}`,
    method: 'put',
    data
  });
}

/** 删除供应商 */
export function fetchDeleteProvider(providerId: number) {
  return request({
    url: `/api/provider/${providerId}`,
    method: 'delete'
  });
}

/** 测试供应商API连接 */
export function fetchTestProviderConnection(providerId: number) {
  return request({
    url: `/api/provider/${providerId}/test-connection`,
    method: 'post'
  });
}

/** 查询供应商余额 */
export function fetchProviderBalance(providerId: number) {
  return request({
    url: `/api/provider/${providerId}/balance`,
    method: 'get'
  });
}

// ===== 供应商状态枚举 =====
export enum ProviderStatus {
  DISABLED = 0, // 禁用
  ENABLED = 1 // 启用
}

// 供应商状态文本映射
export const PROVIDER_STATUS_TEXT: Record<number, string> = {
  [ProviderStatus.DISABLED]: '禁用',
  [ProviderStatus.ENABLED]: '启用'
};

// 供应商状态颜色映射
export const PROVIDER_STATUS_TYPE: Record<number, string> = {
  [ProviderStatus.DISABLED]: 'info',
  [ProviderStatus.ENABLED]: 'success'
};

// ===== 货源配置管理接口 =====

/** 获取货源接口配置列表 */
export function fetchProviderInterfaces(providerId: number) {
  return request({
    url: `/api/provider/${providerId}/interfaces`,
    method: 'get'
  });
}

/** 保存货源接口配置 */
export function fetchSaveProviderInterface(
  providerId: number,
  data: {
    interfaceType: string;
    endpointUrl: string;
    httpMethod?: string;
    requestTemplate?: any;
    responseMapping?: any;
    customCode?: string;
    isEnabled?: number;
  }
) {
  return request({
    url: `/api/provider/${providerId}/interface`,
    method: 'post',
    data
  });
}

/** 获取字段映射模板 */
export function fetchFieldMappingTemplates(templateType?: string) {
  return request({
    url: '/api/provider/templates/field-mapping',
    method: 'get',
    params: { templateType }
  });
}

/** 测试货源接口 */
export function fetchTestProviderInterface(
  providerId: number,
  data: {
    interfaceType: string;
    testData: any;
  }
) {
  return request({
    url: `/api/provider/${providerId}/test`,
    method: 'post',
    data
  });
}

/** 获取货源测试日志 */
export function fetchProviderTestLogs(
  providerId: number,
  params: {
    page?: number;
    pageSize?: number;
    interfaceType?: string;
  }
) {
  return request({
    url: `/api/provider/${providerId}/test-logs`,
    method: 'get',
    params
  });
}

/** 更新货源字段映射 */
export function fetchUpdateProviderFieldMapping(
  providerId: number,
  data: {
    fieldMapping: any;
  }
) {
  return request({
    url: `/api/provider/${providerId}/field-mapping`,
    method: 'put',
    data
  });
}

/** 获取接口类型定义 */
export function fetchInterfaceDefinitions() {
  return request({
    url: '/api/provider/interface-definitions',
    method: 'get'
  });
}

/** 保存向导配置 */
export function fetchSaveWizardConfig(
  providerId: number,
  data: {
    interfaceType: string;
    endpointUrl: string;
    httpMethod?: string;
    fieldMapping?: any;
    requestTemplate?: any;
    responseMapping?: any;
    customCode?: string;
  }
) {
  return request({
    url: `/api/provider/${providerId}/wizard-config`,
    method: 'post',
    data
  });
}

// ===== 统一配置接口 =====

/** 获取货源统一配置 */
export function fetchProviderUnifiedConfig(providerId: number) {
  return request({
    url: `/api/provider/${providerId}/unified-config`,
    method: 'get'
  });
}

/** 保存货源统一配置 */
export function fetchSaveProviderUnifiedConfig(providerId: number, config: any) {
  return request({
    url: `/api/provider/${providerId}/unified-config`,
    method: 'put',
    data: { config }
  });
}

/** 测试统一配置接口 */
export function fetchTestUnifiedConfig(providerId: number, interfaceType: string, testData: any) {
  return request({
    url: `/api/provider/${providerId}/unified-config/test/${interfaceType}`,
    method: 'post',
    data: { testData }
  });
}

/** 获取配置模板列表 */
export function fetchConfigTemplates() {
  return request({
    url: '/api/provider/config-templates',
    method: 'get'
  });
}

/** 从模板创建配置 */
export function fetchCreateConfigFromTemplate(providerId: number, templateId: string, authInfo: any) {
  return request({
    url: `/api/provider/${providerId}/config-from-template`,
    method: 'post',
    data: { templateId, authInfo }
  });
}

/** 验证配置 */
export function fetchValidateConfig(config: any) {
  return request({
    url: '/api/provider/validate-config',
    method: 'post',
    data: { config }
  });
}
