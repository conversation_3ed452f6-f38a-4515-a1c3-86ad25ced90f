/**
 * 29平台对接API
 */

import { request } from '../request';

/**
 * 29平台商品接口类型定义
 */
export interface Platform29Product {
  cid: string;
  name: string;
  price: number;
  fenlei: string;
  fenlei1?: string;
  getnoun: string;
  noun: string;
  content?: string;
  sort?: number;
  status: number;
  wck?: number;
  kcid?: number;
  api?: number;
  nocheck?: number;
  // 动态添加的属性
  category_name?: string;
  isExisting?: boolean;
  adjustedPrice?: number;
  originalPrice?: number;
}

/**
 * 29平台配置接口类型定义
 */
export interface Platform29Config {
  config_id: number;
  platform_name: string;
  platform_url: string;
  uid: string;
  api_key: string;
  status: number;
  create_time: string;
  update_time: string;
}

/**
 * 同步日志接口类型定义
 */
export interface SyncLog {
  log_id: number;
  platform_name: string;
  sync_type: string;
  sync_status: string;
  sync_count: number;
  error_message?: string;
  sync_data?: any;
  create_time: string;
}

/**
 * 获取29平台商品列表
 */
export function fetch29PlatformProducts() {
  return request<{
    products: Platform29Product[];
    total: number;
  }>({
    url: '/api/platform29/products',
    method: 'get'
  });
}

/**
 * 同步29平台商品到本地
 */
export function sync29PlatformProducts(data: { selectedProducts: Platform29Product[]; providerId: number }) {
  return request<{
    successCount: number;
    failedCount: number;
    errors: string[];
  }>({
    url: '/api/platform29/sync',
    method: 'post',
    data
  });
}

/**
 * 获取29平台配置
 */
export function get29PlatformConfig() {
  return request<Platform29Config>({
    url: '/api/platform29/config',
    method: 'get'
  });
}

/**
 * 获取同步日志列表
 */
export function fetchSyncLogs(
  params: {
    page?: number;
    pageSize?: number;
  } = {}
) {
  return request<{
    list: SyncLog[];
    total: number;
    page: number;
    pageSize: number;
  }>({
    url: '/api/platform29/sync-logs',
    method: 'get',
    params
  });
}

/**
 * 获取已对接的商品列表
 */
export function fetchExistingProducts(providerId?: number) {
  return request<{
    products: any[];
    total: number;
  }>({
    url: '/api/platform29/existing-products',
    method: 'get',
    params: providerId ? { providerId } : {}
  });
}

/**
 * 高级同步商品到本地（支持分类分配）
 */
export function advancedSync29PlatformProducts(data: {
  selectedProducts: Platform29Product[];
  providerId: number;
  categoryId?: number;
  createNewCategory?: boolean;
  newCategoryName?: string;
}) {
  return request<{
    successCount: number;
    failedCount: number;
    errors: string[];
    categoryId?: number;
  }>({
    url: '/api/platform29/advanced-sync',
    method: 'post',
    data
  });
}

/**
 * 一键更新已对接商品
 */
export function updateExistingProducts(data: { providerId: number; productIds?: string[]; updateAll?: boolean }) {
  return request<{
    successCount: number;
    failedCount: number;
    errors: string[];
  }>({
    url: '/api/platform29/update-existing',
    method: 'post',
    data
  });
}

/**
 * 检测失效商品
 */
export function detectInvalidProducts(providerId: number) {
  return request<{
    invalidProducts: any[];
    total: number;
  }>({
    url: '/api/platform29/detect-invalid',
    method: 'get',
    params: { providerId }
  });
}

/**
 * 一键下架失效商品
 */
export function deactivateInvalidProducts(data: { providerId: number; productIds: string[] }) {
  return request<{
    successCount: number;
    failedCount: number;
    errors: string[];
  }>({
    url: '/api/platform29/deactivate-invalid',
    method: 'post',
    data
  });
}

/**
 * 获取29平台账户余额
 */
export function get29PlatformBalance() {
  return request<{
    code: number;
    msg: string;
    user: string;
    name: string;
    money: string;
  }>({
    url: '/api/platform29/balance',
    method: 'get'
  });
}
