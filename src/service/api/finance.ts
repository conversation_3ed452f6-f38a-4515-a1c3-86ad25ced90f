import { request } from '../request';
import type { FetchFinanceListParams, FetchFinanceListResult } from './types/finance';

function mapParams(params: FetchFinanceListParams) {
  const { page, pageSize, type, startTime, endTime, user_id } = params || {};
  const mapped: Record<string, any> = {};
  if (page) mapped.page = page;
  if (pageSize) mapped.pageSize = pageSize;
  if (type) mapped.type = type;
  if (startTime) mapped.startTime = startTime;
  if (endTime) mapped.endTime = endTime;
  if (user_id) mapped.user_id = user_id;
  return mapped;
}

function transformListResponse(raw: any, params: FetchFinanceListParams): FetchFinanceListResult {
  // 统一成功结构：优先从 data 读取
  const d = raw?.data ?? raw;

  // 取得列表
  const list = (d?.list || d?.records || d?.items || (Array.isArray(d) ? d : [])) as any[];

  // 取得总数/分页
  const total = Number(d?.pagination?.total ?? d?.total ?? 0);
  const page = Number(d?.pagination?.page ?? params.page ?? 1);
  const pageSize = Number(d?.pagination?.pageSize ?? params.pageSize ?? 20);

  return { list, pagination: { page, pageSize, total } };
}

/** 获取财务记录列表（标准化适配） */
export async function fetchFinanceList(params: FetchFinanceListParams): Promise<FetchFinanceListResult> {
  const mapped = mapParams(params);
  const res = await request({
    url: '/api/payment/finance/list',
    method: 'get',
    params: mapped
  });

  // 成功判断在全局 isBackendSuccess 里，失败会抛出异常；此处只做结构标准化
  return transformListResponse(res, params);
}

