import { request } from '../request/index';

/**
 * 系统监控API
 * 遵循项目规范，提供完整的系统监控接口
 */

// ===== 系统监控接口 =====

/** 获取系统健康状态 */
export function fetchSystemHealth() {
  return request({
    url: '/monitor/health',
    method: 'get'
  });
}

/** 获取性能指标 */
export function fetchPerformanceMetrics(params: { limit?: number; type?: 'all' | 'slow' | 'error' }) {
  return request({
    url: '/monitor/performance',
    method: 'get',
    params
  });
}

/** 获取系统资源使用情况 */
export function fetchSystemResources() {
  return request({
    url: '/monitor/resources',
    method: 'get'
  });
}

/** 获取安全监控数据 */
export function fetchSecurityMonitor() {
  return request({
    url: '/monitor/security',
    method: 'get'
  });
}

/** 获取数据库监控信息 */
export function fetchDatabaseMonitor() {
  return request({
    url: '/monitor/database',
    method: 'get'
  });
}

/** 获取应用监控信息 */
export function fetchApplicationMonitor() {
  return request({
    url: '/monitor/application',
    method: 'get'
  });
}

// ===== 监控状态枚举 =====
export enum HealthStatus {
  HEALTHY = 'healthy',
  WARNING = 'warning',
  CRITICAL = 'critical'
}

export enum ServiceStatus {
  HEALTHY = 'healthy',
  WARNING = 'warning',
  CRITICAL = 'critical',
  UNKNOWN = 'unknown'
}

// 健康状态文本映射
export const HEALTH_STATUS_TEXT: Record<string, string> = {
  [HealthStatus.HEALTHY]: '健康',
  [HealthStatus.WARNING]: '警告',
  [HealthStatus.CRITICAL]: '严重'
};

// 健康状态颜色映射
export const HEALTH_STATUS_TYPE: Record<string, string> = {
  [HealthStatus.HEALTHY]: 'success',
  [HealthStatus.WARNING]: 'warning',
  [HealthStatus.CRITICAL]: 'danger'
};

// 服务状态文本映射
export const SERVICE_STATUS_TEXT: Record<string, string> = {
  [ServiceStatus.HEALTHY]: '正常',
  [ServiceStatus.WARNING]: '警告',
  [ServiceStatus.CRITICAL]: '异常',
  [ServiceStatus.UNKNOWN]: '未知'
};

// 服务状态颜色映射
export const SERVICE_STATUS_TYPE: Record<string, string> = {
  [ServiceStatus.HEALTHY]: 'success',
  [ServiceStatus.WARNING]: 'warning',
  [ServiceStatus.CRITICAL]: 'danger',
  [ServiceStatus.UNKNOWN]: 'info'
};

// 监控数据格式化工具
export const monitorFormatters = {
  // 格式化字节大小
  bytes: (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
  },

  // 格式化百分比
  percentage: (value: number): string => {
    return `${value.toFixed(2)}%`;
  },

  // 格式化运行时间
  uptime: (seconds: number): string => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (days > 0) {
      return `${days}天 ${hours}小时 ${minutes}分钟`;
    } else if (hours > 0) {
      return `${hours}小时 ${minutes}分钟`;
    }
    return `${minutes}分钟`;
  },

  // 格式化响应时间
  responseTime: (ms: number): string => {
    if (ms < 1000) {
      return `${ms.toFixed(2)}ms`;
    }
    return `${(ms / 1000).toFixed(2)}s`;
  },

  // 格式化CPU使用率
  cpuUsage: (usage: any): string => {
    const userMs = usage.user / 1000;
    const systemMs = usage.system / 1000;
    return `用户: ${userMs.toFixed(2)}ms, 系统: ${systemMs.toFixed(2)}ms`;
  },

  // 格式化负载平均值
  loadAverage: (load: number[]): string => {
    return load.map(l => l.toFixed(2)).join(', ');
  },

  // 格式化网络地址
  networkAddress: (address: any): string => {
    return `${address.address}/${address.netmask} (${address.family})`;
  }
};

// 监控阈值配置
export const MONITOR_THRESHOLDS = {
  memory: {
    warning: 70,
    critical: 85
  },
  cpu: {
    warning: 70,
    critical: 85
  },
  disk: {
    warning: 80,
    critical: 90
  },
  responseTime: {
    warning: 1000,
    critical: 2000
  },
  errorRate: {
    warning: 2,
    critical: 5
  }
};

// 获取状态级别
export function getStatusLevel(value: number, thresholds: { warning: number; critical: number }): HealthStatus {
  if (value >= thresholds.critical) {
    return HealthStatus.CRITICAL;
  } else if (value >= thresholds.warning) {
    return HealthStatus.WARNING;
  }
  return HealthStatus.HEALTHY;
}

// 监控图表配置
export const MONITOR_CHART_CONFIG = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const
    },
    tooltip: {
      mode: 'index' as const,
      intersect: false
    }
  },
  scales: {
    x: {
      display: true,
      title: {
        display: true,
        text: '时间'
      }
    },
    y: {
      display: true,
      title: {
        display: true
      },
      beginAtZero: true
    }
  },
  interaction: {
    mode: 'nearest' as const,
    axis: 'x' as const,
    intersect: false
  }
};

// 实时监控配置
export const REALTIME_CONFIG = {
  refreshInterval: 5000, // 5秒刷新一次
  maxDataPoints: 60, // 最多保存60个数据点（5分钟）
  autoRefresh: true // 自动刷新
};
