import { request } from '../request/index';

/**
 * 支付管理API
 * 遵循项目规范，提供完整的支付管理接口
 */

// ===== 支付管理接口 =====

/** 获取支付记录列表 */
export function fetchPaymentList(params: {
  page?: number;
  pageSize?: number;
  status?: number;
  payment_type?: string;
  user_id?: number;
}) {
  return request({
    url: '/api/payment/list',
    method: 'get',
    params
  });
}

/** 获取财务记录列表 */
export function fetchFinanceList(params: { page?: number; pageSize?: number; type?: string; user_id?: number }) {
  return request({
    url: '/api/payment/finance/list',
    method: 'get',
    params
  });
}

/** 创建充值订单 */
export function createRechargeOrder(data: { amount: number; payment_method: string }) {
  return request({
    url: '/api/payment/recharge',
    method: 'post',
    data
  });
}

/** 获取充值配置 */
export function fetchRechargeConfig() {
  return request({
    url: '/api/payment/recharge/config',
    method: 'get'
  });
}

/** 更新充值配置（管理员） */
export function updateRechargeConfig(configId: number, data: any) {
  return request({
    url: `/api/payment/recharge/config/${configId}`,
    method: 'put',
    data
  });
}

/** 手动充值（管理员） */
export function manualRecharge(data: { user_id: number; amount: number; description?: string }) {
  return request({
    url: '/api/payment/manual-recharge',
    method: 'post',
    data
  });
}
