import { request } from '../request/index';

/**
 * 订单管理API
 * 遵循项目规范，提供完整的订单管理接口
 */

// ===== 类型定义 =====

// 订单列表查询参数
export interface OrderListParams {
  page?: number;
  pageSize?: number;
  keyword?: string;
  status?: number;
  startTime?: string;
  endTime?: string;
  productId?: number;
  platformType?: string;
}

// 订单信息接口
export interface Order {
  order_id: number;
  order_no: string;
  user_id: number;
  username?: string;
  product_id?: number;
  product_name?: string;
  provider_id?: number;
  provider_name?: string;
  platform_type: string;
  platform_account: string;
  school_name?: string;
  selected_courses?: any;
  quantity: number;
  unit_price: number;
  amount: number;
  cost_amount?: number;
  status: number;
  status_text?: string;
  progress: number;
  upstream_order_id?: string;
  error_message?: string;
  extra_data?: any;
  create_time: string;
  update_time: string;
}

// 创建订单参数
export interface CreateOrderParams {
  productId: number;
  platformAccount: string;
  platformPassword: string;
  schoolName: string;
  selectedCourses: {
    courseId: string;
    courseName: string;
    status?: string;
    progress?: number;
  }[];
  remark?: string;
}

// ===== 订单管理接口 =====

/** 获取订单列表 */
export function fetchOrderList(params: OrderListParams = {}) {
  return request<{
    list: Order[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }>({
    url: '/api/order/list',
    method: 'get',
    params
  });
}

/** 获取订单详情 */
export function fetchOrderDetail(orderId: number) {
  return request({
    url: `/api/order/${orderId}`,
    method: 'get'
  });
}

/** 创建订单（基于商品和选中课程） */
export function createOrder(data: CreateOrderParams) {
  return request<{
    orderId: number;
    orderNo: string;
    amount: number;
    courseCount: number;
    successCount: number;
    failedCount: number;
    submitResults: any[];
    message: string;
  }>({
    url: '/api/order/create',
    method: 'post',
    data
  });
}

/** 更新订单状态 */
export function updateOrderStatus(
  orderId: number,
  data: {
    status?: number;
    progress?: number;
    processStatus?: string;
    loginStatus?: string;
    remark?: string;
  }
) {
  return request({
    url: `/api/order/${orderId}/status`,
    method: 'put',
    data
  });
}

// ===== 29平台专用接口 =====

/** 查课接口 */
export function queryCourses(data: {
  providerId: number;
  platformType: string;
  platformAccount: string;
  platformPassword: string;
  schoolName: string;
  courseId?: string;
}) {
  return request({
    url: '/api/order/query-courses',
    method: 'post',
    data
  });
}

/** 获取服务商课程列表 */
export function fetchProviderCourses(providerId: number) {
  return request({
    url: `/api/order/provider/${providerId}/courses`,
    method: 'get'
  });
}

/** 同步订单状态 */
export function syncOrderStatus(orderId: number) {
  return request({
    url: `/api/order/${orderId}/sync`,
    method: 'post'
  });
}

/** 取消订单 */
export function fetchCancelOrder(orderId: number, reason?: string) {
  return request({
    url: `/api/order/${orderId}/cancel`,
    method: 'post',
    data: { reason }
  });
}

/** 获取订单统计信息 */
export function fetchOrderStats() {
  return request({
    url: '/api/order/stats/overview',
    method: 'get'
  });
}

// ===== 订单状态枚举 =====
export enum OrderStatus {
  PENDING_PAYMENT = 1, // 待支付
  PENDING_PROCESS = 2, // 待处理
  PROCESSING = 3, // 处理中
  COMPLETED = 4, // 已完成
  CANCELLED = 5, // 已取消
  FAILED = 6 // 处理失败
}

// 订单状态文本映射
export const ORDER_STATUS_TEXT = {
  [OrderStatus.PENDING_PAYMENT]: '待支付',
  [OrderStatus.PENDING_PROCESS]: '待处理',
  [OrderStatus.PROCESSING]: '处理中',
  [OrderStatus.COMPLETED]: '已完成',
  [OrderStatus.CANCELLED]: '已取消',
  [OrderStatus.FAILED]: '处理失败'
};

// 订单状态颜色映射
export const ORDER_STATUS_TYPE = {
  [OrderStatus.PENDING_PAYMENT]: 'warning',
  [OrderStatus.PENDING_PROCESS]: 'info',
  [OrderStatus.PROCESSING]: 'primary',
  [OrderStatus.COMPLETED]: 'success',
  [OrderStatus.CANCELLED]: 'info',
  [OrderStatus.FAILED]: 'danger'
} as const;

// ===== 订单操作接口 =====

/** 订单补刷 */
export function refillOrder(orderId: number, data?: any) {
  return request({
    url: `/api/order/${orderId}/refill`,
    method: 'post',
    data
  });
}

/** 订单同步 */
export function syncOrder(orderId: number) {
  return request({
    url: `/api/order/${orderId}/sync`,
    method: 'post'
  });
}

/** 订单改密 */
export function changeOrderPassword(
  orderId: number,
  data: {
    new_password: string;
    confirm_password: string;
  }
) {
  return request({
    url: `/api/order/${orderId}/change-password`,
    method: 'post',
    data
  });
}

/** 订单反馈 */
export function feedbackOrder(
  orderId: number,
  data: {
    feedback_type: string;
    feedback_content: string;
    attachments?: string[];
  }
) {
  return request({
    url: `/api/order/${orderId}/feedback`,
    method: 'post',
    data
  });
}

/** 批量订单操作 */
export function batchOrderOperation(data: {
  orderIds: number[];
  operation: 'refill' | 'sync' | 'cancel' | 'delete';
  params?: any;
}) {
  return request({
    url: '/api/order/batch-operation',
    method: 'post',
    data
  });
}
