import { request } from '../request/index';

/**
 * 数据统计分析API
 * 遵循项目规范，提供完整的数据统计分析接口
 */

// ===== 统计分析接口 =====

/** 获取系统概览统计 */
export function fetchSystemOverview() {
  return request({
    url: '/api/statistics/overview',
    method: 'get'
  });
}

/** 获取用户统计分析 */
export function fetchUserAnalytics(params: { period?: '24h' | '7d' | '30d' | '90d' }) {
  return request({
    url: '/api/statistics/users',
    method: 'get',
    params
  });
}

/** 获取订单统计分析 */
export function fetchOrderAnalytics(params: { period?: '24h' | '7d' | '30d' | '90d' }) {
  return request({
    url: '/api/statistics/orders',
    method: 'get',
    params
  });
}

/** 获取收入统计分析 */
export function fetchRevenueAnalytics(params: { period?: '24h' | '7d' | '30d' | '90d' }) {
  return request({
    url: '/api/statistics/revenue',
    method: 'get',
    params
  });
}

/** 获取实时统计数据 */
export function fetchRealTimeStats() {
  return request({
    url: '/api/statistics/realtime',
    method: 'get'
  });
}

// ===== 统计时间周期枚举 =====
export enum StatsPeriod {
  HOUR_24 = '24h',
  DAY_7 = '7d',
  DAY_30 = '30d',
  DAY_90 = '90d'
}

// 统计时间周期文本映射
export const STATS_PERIOD_TEXT = {
  [StatsPeriod.HOUR_24]: '最近24小时',
  [StatsPeriod.DAY_7]: '最近7天',
  [StatsPeriod.DAY_30]: '最近30天',
  [StatsPeriod.DAY_90]: '最近90天'
};

// 图表颜色配置
export const CHART_COLORS = {
  primary: '#409EFF',
  success: '#67C23A',
  warning: '#E6A23C',
  danger: '#F56C6C',
  info: '#909399',
  purple: '#9C27B0',
  orange: '#FF9800',
  teal: '#009688',
  pink: '#E91E63',
  indigo: '#3F51B5'
};

// 图表配置
export const CHART_CONFIG = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const
    },
    tooltip: {
      mode: 'index' as const,
      intersect: false
    }
  },
  scales: {
    x: {
      display: true,
      title: {
        display: true
      }
    },
    y: {
      display: true,
      title: {
        display: true
      }
    }
  },
  interaction: {
    mode: 'nearest' as const,
    axis: 'x' as const,
    intersect: false
  }
};

// 数据格式化工具函数
export const formatters = {
  // 格式化金额
  currency: (value: number): string => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY',
      minimumFractionDigits: 2
    }).format(value);
  },

  // 格式化数字
  number: (value: number): string => {
    return new Intl.NumberFormat('zh-CN').format(value);
  },

  // 格式化百分比
  percentage: (value: number): string => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'percent',
      minimumFractionDigits: 2
    }).format(value / 100);
  },

  // 格式化日期
  date: (value: string | Date): string => {
    const date = typeof value === 'string' ? new Date(value) : value;
    return date.toLocaleDateString('zh-CN');
  },

  // 格式化日期时间
  datetime: (value: string | Date): string => {
    const date = typeof value === 'string' ? new Date(value) : value;
    return date.toLocaleString('zh-CN');
  },

  // 格式化文件大小
  fileSize: (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
  },

  // 格式化时长
  duration: (seconds: number): string => {
    if (seconds < 60) {
      return `${seconds}秒`;
    } else if (seconds < 3600) {
      const minutes = Math.floor(seconds / 60);
      return `${minutes}分钟`;
    } else if (seconds < 86400) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      return `${hours}小时${minutes > 0 ? `${minutes}分钟` : ''}`;
    }
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    return `${days}天${hours > 0 ? `${hours}小时` : ''}`;
  }
};

// 图表数据处理工具
export const chartUtils = {
  // 生成渐变色
  createGradient: (ctx: CanvasRenderingContext2D, color: string) => {
    const gradient = ctx.createLinearGradient(0, 0, 0, 400);
    gradient.addColorStop(0, color);
    gradient.addColorStop(1, `${color}20`);
    return gradient;
  },

  // 处理趋势数据
  processTrendData: (data: any[], xField: string, yField: string) => {
    return {
      labels: data.map(item => item[xField]),
      datasets: [
        {
          label: yField,
          data: data.map(item => item[yField]),
          borderColor: CHART_COLORS.primary,
          backgroundColor: `${CHART_COLORS.primary}20`,
          tension: 0.4,
          fill: true
        }
      ]
    };
  },

  // 处理饼图数据
  processPieData: (data: any[], labelField: string, valueField: string) => {
    return {
      labels: data.map(item => item[labelField]),
      datasets: [
        {
          data: data.map(item => item[valueField]),
          backgroundColor: [
            CHART_COLORS.primary,
            CHART_COLORS.success,
            CHART_COLORS.warning,
            CHART_COLORS.danger,
            CHART_COLORS.info,
            CHART_COLORS.purple,
            CHART_COLORS.orange,
            CHART_COLORS.teal,
            CHART_COLORS.pink,
            CHART_COLORS.indigo
          ]
        }
      ]
    };
  },

  // 处理柱状图数据
  processBarData: (data: any[], xField: string, yFields: string[], labels: string[]) => {
    return {
      labels: data.map(item => item[xField]),
      datasets: yFields.map((field, index) => ({
        label: labels[index] || field,
        data: data.map(item => item[field]),
        backgroundColor: Object.values(CHART_COLORS)[index % Object.values(CHART_COLORS).length],
        borderColor: Object.values(CHART_COLORS)[index % Object.values(CHART_COLORS).length],
        borderWidth: 1
      }))
    };
  }
};
