import { request } from '../request/index';

/** get role list */
export function fetchGetRoleList(params?: Api.SystemManage.RoleSearchParams) {
  return request<Api.SystemManage.RoleList>({
    url: '/systemManage/getRoleList',
    method: 'get',
    params
  });
}

/**
 * get all roles
 *
 * these roles are all enabled
 */
export function fetchGetAllRoles() {
  return request<Api.SystemManage.AllRole[]>({
    url: '/systemManage/getAllRoles',
    method: 'get'
  });
}

/** get user list */
export function fetchGetUserList(params?: Api.SystemManage.UserSearchParams) {
  return request<Api.SystemManage.UserList>({
    url: '/user/list',
    method: 'get',
    params
  });
}

/** create user */
export function fetchCreateSystemUser(data: Api.Auth.CreateUserParams) {
  return request<Api.Auth.UserInfo>({
    url: '/user/create',
    method: 'post',
    data
  });
}

/** update user */
export function fetchUpdateSystemUser(data: Api.Auth.UpdateUserParams) {
  return request<Api.Auth.UserInfo>({
    url: `/user/update/${data.userId}`,
    method: 'put',
    data
  });
}

/** delete user */
export function fetchDeleteSystemUser(userId: number) {
  return request({
    url: `/user/delete/${userId}`,
    method: 'delete'
  });
}

/** get user stats */
export function fetchGetUserStats() {
  return request<{
    totalUsers: number;
    activeUsers: number;
    newUsersToday: number;
    totalBalance: number;
  }>({
    url: '/user/stats',
    method: 'get'
  });
}

/** get menu list */
export function fetchGetMenuList() {
  return request<Api.SystemManage.MenuList>({
    url: '/systemManage/getMenuList/v2',
    method: 'get'
  });
}

/** get all pages */
export function fetchGetAllPages() {
  return request<string[]>({
    url: '/systemManage/getAllPages',
    method: 'get'
  });
}

/** get menu tree */
export function fetchGetMenuTree() {
  return request<Api.SystemManage.MenuTree[]>({
    url: '/systemManage/getMenuTree',
    method: 'get'
  });
}
