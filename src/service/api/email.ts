import { request } from '../request/index';

/**
 * 邮件管理API
 * 遵循项目规范，提供完整的邮件管理接口
 */

// ===== 邮件配置和测试 =====

/** 测试邮件配置 */
export function fetchTestEmailConfig(data: {
  smtpHost: string;
  smtpPort: number;
  emailFrom: string;
  emailPassword?: string;
  emailSSL: boolean;
  testEmail: string;
}) {
  return request({
    url: '/api/email/test',
    method: 'post',
    data
  });
}

/** 发送邮件 */
export function fetchSendEmail(data: {
  to: string | string[];
  subject?: string;
  text?: string;
  html?: string;
  template?: string;
  variables?: Record<string, any>;
}) {
  return request({
    url: '/api/email/send',
    method: 'post',
    data
  });
}

/** 获取邮件服务状态 */
export function fetchEmailStatus() {
  return request({
    url: '/api/email/status',
    method: 'get'
  });
}

/** 重新加载邮件配置 */
export function fetchReloadEmailConfig() {
  return request({
    url: '/api/email/reload',
    method: 'post'
  });
}

// ===== 邮件模板管理 =====

/** 获取邮件模板列表 */
export function fetchEmailTemplates() {
  return request({
    url: '/api/email/templates',
    method: 'get'
  });
}

/** 获取邮件模板详情 */
export function fetchEmailTemplate(templateId: number) {
  return request({
    url: `/api/email/templates/${templateId}`,
    method: 'get'
  });
}

/** 获取模板变量定义 */
export function fetchTemplateVariables() {
  return request({
    url: '/api/email/templates/variables/definitions',
    method: 'get'
  });
}

/** 预览邮件模板 */
export function fetchPreviewEmailTemplate(data: { template: string; variables?: Record<string, any> }) {
  return request({
    url: '/api/email/templates/preview',
    method: 'post',
    data
  });
}

/** 保存邮件模板 */
export function fetchSaveEmailTemplate(data: {
  templateKey: string;
  templateName: string;
  templateDescription?: string;
  category: string;
  subject: string;
  textContent: string;
  htmlContent: string;
  variables?: string[];
  isActive?: boolean;
}) {
  return request({
    url: '/api/email/templates',
    method: 'post',
    data
  });
}

/** 更新邮件模板 */
export function fetchUpdateEmailTemplate(
  templateId: number,
  data: {
    templateKey?: string;
    templateName?: string;
    templateDescription?: string;
    category?: string;
    subject?: string;
    textContent?: string;
    htmlContent?: string;
    variables?: string[];
    isActive?: boolean;
  }
) {
  return request({
    url: `/api/email/templates/${templateId}`,
    method: 'put',
    data
  });
}

/** 删除邮件模板 */
export function fetchDeleteEmailTemplate(templateId: number) {
  return request({
    url: `/api/email/templates/${templateId}`,
    method: 'delete'
  });
}

// ===== 邮件记录和统计 =====

/** 获取邮件发送记录 */
export function fetchEmailRecords(params: {
  page?: number;
  pageSize?: number;
  status?: string;
  templateId?: number;
  email?: string;
  startDate?: string;
  endDate?: string;
}) {
  return request({
    url: '/api/email/records',
    method: 'get',
    params
  });
}

/** 获取邮件发送统计 */
export function fetchEmailStats(params?: { startDate?: string; endDate?: string }) {
  return request({
    url: '/api/email/stats',
    method: 'get',
    params
  });
}

// ===== 邮件状态枚举 =====
export enum EmailStatus {
  PENDING = 'pending', // 待发送
  SENDING = 'sending', // 发送中
  SENT = 'sent', // 已发送
  FAILED = 'failed', // 发送失败
  BOUNCED = 'bounced' // 退信
}

// 邮件状态文本映射
export const EMAIL_STATUS_TEXT = {
  [EmailStatus.PENDING]: '待发送',
  [EmailStatus.SENDING]: '发送中',
  [EmailStatus.SENT]: '已发送',
  [EmailStatus.FAILED]: '发送失败',
  [EmailStatus.BOUNCED]: '退信'
};

// 邮件状态颜色映射
export const EMAIL_STATUS_TYPE = {
  [EmailStatus.PENDING]: 'info',
  [EmailStatus.SENDING]: 'warning',
  [EmailStatus.SENT]: 'success',
  [EmailStatus.FAILED]: 'danger',
  [EmailStatus.BOUNCED]: 'warning'
} as const;

// 邮件模板分类
export enum EmailTemplateCategory {
  USER = 'user', // 用户相关
  SYSTEM = 'system', // 系统通知
  CUSTOM = 'custom' // 自定义
}

// 邮件模板分类文本映射
export const EMAIL_TEMPLATE_CATEGORY_TEXT = {
  [EmailTemplateCategory.USER]: '用户相关',
  [EmailTemplateCategory.SYSTEM]: '系统通知',
  [EmailTemplateCategory.CUSTOM]: '自定义'
};
