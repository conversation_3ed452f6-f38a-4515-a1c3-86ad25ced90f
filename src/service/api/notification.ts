import { request } from '../request/index';

/**
 * 通知管理API
 * 遵循项目规范，提供完整的通知管理接口
 */

// ===== 系统公告管理接口 =====

/** 创建系统公告 */
export function fetchCreateAnnouncement(data: {
  title: string;
  content: string;
  type?: 'info' | 'warning' | 'success' | 'error';
  priority?: 'low' | 'normal' | 'high' | 'urgent';
  targetUsers?: string;
  expiresAt?: string;
}) {
  return request({
    url: '/api/notification/announcement',
    method: 'post',
    data
  });
}

/** 获取系统公告列表（管理员） */
export function fetchAnnouncementList(params: {
  page?: number;
  pageSize?: number;
  type?: string;
  status?: string;
  keyword?: string;
}) {
  return request({
    url: '/api/notification/announcement/admin',
    method: 'get',
    params
  });
}

/** 更新系统公告 */
export function fetchUpdateAnnouncement(
  announcementId: number,
  data: {
    title: string;
    content: string;
    type: string;
    priority: string;
    targetUsers: string;
    expiresAt?: string;
    status: number;
  }
) {
  return request({
    url: `/api/notification/announcement/${announcementId}`,
    method: 'put',
    data
  });
}

/** 删除系统公告 */
export function fetchDeleteAnnouncement(announcementId: number) {
  return request({
    url: `/api/notification/announcement/${announcementId}`,
    method: 'delete'
  });
}

/** 创建用户通知 */
export function fetchCreateUserNotification(data: {
  userId: number;
  title: string;
  content: string;
  type?: 'info' | 'warning' | 'success' | 'error';
  actionUrl?: string;
}) {
  return request({
    url: '/api/notification/user',
    method: 'post',
    data
  });
}

// ===== 用户端通知接口 =====

/** 获取用户通知列表 */
export function fetchUserNotifications(params: { page?: number; pageSize?: number; status?: string }) {
  return request({
    url: '/api/notification/user',
    method: 'get',
    params
  });
}

/** 标记通知为已读 */
export function fetchMarkNotificationAsRead(notificationId: number) {
  return request({
    url: `/api/notification/user/${notificationId}/read`,
    method: 'put'
  });
}

/** 批量标记通知为已读 */
export function fetchMarkAllNotificationsAsRead() {
  return request({
    url: '/api/notification/user/read-all',
    method: 'put'
  });
}

/** 获取有效的系统公告（用户端） */
export function fetchActiveAnnouncements(params: { limit?: number }) {
  return request({
    url: '/api/notification/announcement',
    method: 'get',
    params
  });
}

// ===== 通知类型枚举 =====
export enum NotificationType {
  INFO = 'info',
  WARNING = 'warning',
  SUCCESS = 'success',
  ERROR = 'error'
}

export enum NotificationPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent'
}

export enum NotificationStatus {
  UNREAD = 'unread',
  READ = 'read'
}

// 通知类型文本映射
export const NOTIFICATION_TYPE_TEXT = {
  [NotificationType.INFO]: '信息',
  [NotificationType.WARNING]: '警告',
  [NotificationType.SUCCESS]: '成功',
  [NotificationType.ERROR]: '错误'
};

// 通知类型颜色映射
export const NOTIFICATION_TYPE_COLOR = {
  [NotificationType.INFO]: 'primary',
  [NotificationType.WARNING]: 'warning',
  [NotificationType.SUCCESS]: 'success',
  [NotificationType.ERROR]: 'danger'
} as const;

// 优先级文本映射
export const NOTIFICATION_PRIORITY_TEXT = {
  [NotificationPriority.LOW]: '低',
  [NotificationPriority.NORMAL]: '普通',
  [NotificationPriority.HIGH]: '高',
  [NotificationPriority.URGENT]: '紧急'
};

// 优先级颜色映射
export const NOTIFICATION_PRIORITY_COLOR = {
  [NotificationPriority.LOW]: 'info',
  [NotificationPriority.NORMAL]: 'primary',
  [NotificationPriority.HIGH]: 'warning',
  [NotificationPriority.URGENT]: 'danger'
} as const;

// 状态文本映射
export const NOTIFICATION_STATUS_TEXT = {
  [NotificationStatus.UNREAD]: '未读',
  [NotificationStatus.READ]: '已读'
};

// 目标用户选项
export const TARGET_USER_OPTIONS = [
  { label: '所有用户', value: 'all' },
  { label: '管理员', value: 'admin' },
  { label: '普通用户', value: 'user' }
];

// 通知配置
export const NOTIFICATION_CONFIG = {
  // 默认分页大小
  defaultPageSize: 20,

  // 最大内容长度
  maxContentLength: 1000,

  // 自动刷新间隔（毫秒）
  autoRefreshInterval: 30000,

  // 通知显示时长（毫秒）
  displayDuration: 5000
};

// 通知工具函数
export const notificationUtils = {
  // 格式化通知时间
  formatTime: (time: string): string => {
    const now = new Date();
    const notificationTime = new Date(time);
    const diff = now.getTime() - notificationTime.getTime();

    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (minutes < 1) {
      return '刚刚';
    } else if (minutes < 60) {
      return `${minutes}分钟前`;
    } else if (hours < 24) {
      return `${hours}小时前`;
    } else if (days < 7) {
      return `${days}天前`;
    }
    return notificationTime.toLocaleDateString('zh-CN');
  },

  // 截取通知内容
  truncateContent: (content: string, maxLength: number = 100): string => {
    if (content.length <= maxLength) {
      return content;
    }
    return `${content.substring(0, maxLength)}...`;
  },

  // 获取通知图标
  getNotificationIcon: (type: NotificationType): string => {
    const iconMap = {
      [NotificationType.INFO]: 'carbon:information',
      [NotificationType.WARNING]: 'carbon:warning',
      [NotificationType.SUCCESS]: 'carbon:checkmark',
      [NotificationType.ERROR]: 'carbon:error'
    };
    return iconMap[type] || iconMap[NotificationType.INFO];
  },

  // 获取优先级图标
  getPriorityIcon: (priority: NotificationPriority): string => {
    const iconMap = {
      [NotificationPriority.LOW]: 'carbon:arrow-down',
      [NotificationPriority.NORMAL]: 'carbon:subtract',
      [NotificationPriority.HIGH]: 'carbon:arrow-up',
      [NotificationPriority.URGENT]: 'carbon:warning-filled'
    };
    return iconMap[priority] || iconMap[NotificationPriority.NORMAL];
  },

  // 验证通知数据
  validateNotification: (data: any): { valid: boolean; message?: string } => {
    if (!data.title || data.title.trim().length === 0) {
      return { valid: false, message: '标题不能为空' };
    }

    if (data.title.length > 255) {
      return { valid: false, message: '标题长度不能超过255个字符' };
    }

    if (!data.content || data.content.trim().length === 0) {
      return { valid: false, message: '内容不能为空' };
    }

    if (data.content.length > NOTIFICATION_CONFIG.maxContentLength) {
      return { valid: false, message: `内容长度不能超过${NOTIFICATION_CONFIG.maxContentLength}个字符` };
    }

    return { valid: true };
  }
};
