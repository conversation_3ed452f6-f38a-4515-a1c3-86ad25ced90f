/**
 * 查课相关API
 */

import { request } from '../request';

// 查课请求参数接口
export interface QueryCoursesParams {
  productId: number;
  school: string;
  username: string;
  password: string;
}

// 查课结果接口
export interface QueryResult {
  product: {
    productId: number;
    productName: string;
    platformType: string;
    providerName: string;
    price: number;
  };
  courses: Array<{
    courseId: string;
    courseName: string;
    progress: number;
    status: string;
    teacher: string;
    school: string;
    semester: string;
    platform: string;
    description?: string;
  }>;
  account: {
    username: string;
    school: string;
  };
  queryTime: string;
  totalCourses: number;
}

// 批量查课请求参数接口
export interface BatchQueryParams {
  productId: number;
  accounts: Array<{
    school: string;
    username: string;
    password: string;
  }>;
}

// 批量查课结果接口
export interface BatchQueryResult {
  results: Array<{
    account: {
      username: string;
      school: string;
    };
    success: boolean;
    courses?: Array<{
      courseId: string;
      courseName: string;
      progress: number;
      status: string;
      teacher: string;
      school: string;
      semester: string;
      platform: string;
      description?: string;
    }>;
    error?: string;
    queryTime: string;
  }>;
  summary: {
    total: number;
    success: number;
    failed: number;
    totalCourses: number;
  };
}

/**
 * 查询课程
 */
export function queryCourses(data: QueryCoursesParams) {
  return request<QueryResult>({
    url: '/api/query/courses',
    method: 'post',
    data
  });
}

/**
 * 批量查询课程
 */
export function batchQueryCourses(data: { productId: number; accountsText: string }) {
  return request<BatchQueryResult>({
    url: '/api/query/batch-courses',
    method: 'post',
    data
  });
}

/**
 * 获取查课历史记录
 */
export function fetchQueryHistory(
  params: {
    page?: number;
    pageSize?: number;
    productId?: number;
    school?: string;
    username?: string;
  } = {}
) {
  return request<{
    list: Array<{
      queryId: number;
      productId: number;
      productName: string;
      school: string;
      username: string;
      coursesCount: number;
      queryTime: string;
      status: string;
    }>;
    total: number;
    page: number;
    pageSize: number;
  }>({
    url: '/api/query/history',
    method: 'get',
    params
  });
}

/**
 * 获取查课详情
 */
export function fetchQueryDetail(queryId: number) {
  return request<QueryResult>({
    url: `/api/query/detail/${queryId}`,
    method: 'get'
  });
}

/**
 * 根据查课结果批量下单
 */
export function batchCreateOrdersFromQuery(data: {
  productId: number;
  selectedCourses: Array<{
    username: string;
    password: string;
    school: string;
    courseId: string;
    courseName: string;
  }>;
}) {
  return request<{
    success: number;
    failed: number;
    total: number;
    results: Array<{
      success: boolean;
      orderId?: number;
      orderNo?: string;
      error?: string;
      account: {
        username: string;
        school: string;
      };
    }>;
  }>({
    url: '/api/query/batch-orders',
    method: 'post',
    data
  });
}
