import { request } from '../request';

/** 获取同步配置 */
export function fetchSyncConfig() {
  return request<any>({
    url: '/api/sync/config',
    method: 'get'
  });
}

/** 更新同步配置 */
export function updateSyncConfig(data: { config: any }) {
  return request<any>({
    url: '/api/sync/config',
    method: 'put',
    data
  });
}

/** 启动自动同步 */
export function startAutoSync() {
  return request<any>({
    url: '/api/sync/start',
    method: 'post'
  });
}

/** 停止自动同步 */
export function stopAutoSync() {
  return request<any>({
    url: '/api/sync/stop',
    method: 'post'
  });
}

/** 获取同步状态 */
export function fetchSyncStatus() {
  return request<any>({
    url: '/api/sync/status',
    method: 'get'
  });
}

/** 获取同步历史 */
export function fetchSyncHistory(params: { days?: number }) {
  return request<any>({
    url: '/api/sync/history',
    method: 'get',
    params
  });
}

/** 触发手动同步 */
export function triggerManualSync(data: { orderIds: string[]; priority?: number }) {
  return request<any>({
    url: '/api/sync/manual',
    method: 'post',
    data
  });
}

/** 管理同步策略 */
export function manageSyncStrategy(data: { action: 'add' | 'update' | 'remove'; strategyId?: string; strategy?: any }) {
  return request<any>({
    url: '/api/sync/strategy',
    method: 'post',
    data
  });
}

/** 获取同步事件 */
export function fetchSyncEvents(params: { limit?: number }) {
  return request<any>({
    url: '/api/sync/events',
    method: 'get',
    params
  });
}

/** 获取订单统计 */
export function fetchOrderStats() {
  return request<any>({
    url: '/api/sync/order-stats',
    method: 'get'
  });
}

/** 获取同步统计 */
export function fetchSyncStats() {
  return request<any>({
    url: '/api/sync/sync-stats',
    method: 'get'
  });
}
