import { request } from '../request/index';

/**
 * 系统管理API
 * 提供系统配置、公告管理、数据统计等功能
 */

// ===== 系统配置管理 =====

/** 获取系统配置 */
export function fetchSystemConfig() {
  return request({
    url: '/api/system/config',
    method: 'get'
  });
}

/** 更新系统配置 */
export function updateSystemConfig(data: { configs: Record<string, any> }) {
  return request({
    url: '/api/system/config',
    method: 'put',
    data
  });
}

/** 获取系统统计数据 */
export function fetchSystemStatistics() {
  return request({
    url: '/api/system/statistics',
    method: 'get'
  });
}

// ===== 系统公告管理 =====

/** 获取系统公告列表 */
export function fetchAnnouncementList(params: { page?: number; pageSize?: number; status?: number }) {
  return request({
    url: '/api/system/announcements',
    method: 'get',
    params
  });
}

/** 创建系统公告 */
export function createAnnouncement(data: { title: string; content: string; status?: number }) {
  return request({
    url: '/api/system/announcements',
    method: 'post',
    data
  });
}

/** 更新系统公告 */
export function updateAnnouncement(
  configId: number,
  data: {
    title: string;
    content: string;
    status?: number;
  }
) {
  return request({
    url: `/api/system/announcements/${configId}`,
    method: 'put',
    data
  });
}

/** 删除系统公告 */
export function deleteAnnouncement(configId: number) {
  return request({
    url: `/api/system/announcements/${configId}`,
    method: 'delete'
  });
}
