import type { LogEntry, LogFilter } from '@/utils/logger';
import { request } from '../request/index';

/**
 * 统一日志管理API接口
 * 所有操作日志都记录到fd_log表中
 */

/**
 * 记录操作日志
 */
export function createLog(data: Omit<LogEntry, 'user_id' | 'username'>) {
  return request({
    url: '/api/log/create',
    method: 'post',
    data
  });
}

/**
 * 获取日志列表
 */
export function fetchLogList(params: LogFilter = {}) {
  return request<{
    list: LogEntry[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }>({
    url: '/api/log/list',
    method: 'get',
    params
  });
}

/**
 * 获取日志详情
 */
export function fetchLogDetail(logId: number) {
  return request<LogEntry>({
    url: `/api/log/${logId}`,
    method: 'get'
  });
}

/**
 * 删除日志
 */
export function deleteLog(logId: number) {
  return request({
    url: `/api/log/${logId}`,
    method: 'delete'
  });
}

/**
 * 批量删除日志
 */
export function batchDeleteLogs(logIds: number[]) {
  return request({
    url: '/api/log/batch-delete',
    method: 'post',
    data: { logIds }
  });
}

/**
 * 清理过期日志
 */
export function cleanExpiredLogs(days: number = 30) {
  return request({
    url: '/api/log/clean',
    method: 'post',
    data: { days }
  });
}

/**
 * 导出日志
 */
export function exportLogs(params: LogFilter = {}) {
  return request({
    url: '/api/log/export',
    method: 'get',
    params,
    responseType: 'blob'
  });
}

/**
 * 获取日志统计信息
 */
export function fetchLogStats(
  params: {
    start_date?: string;
    end_date?: string;
    module?: string;
  } = {}
) {
  return request<{
    total: number;
    success: number;
    error: number;
    warning: number;
    info: number;
    modules: Array<{
      module: string;
      count: number;
    }>;
    actions: Array<{
      action: string;
      count: number;
    }>;
    users: Array<{
      username: string;
      count: number;
    }>;
  }>({
    url: '/api/log/stats',
    method: 'get',
    params
  });
}

/** 清理过期日志 */
export function fetchCleanExpiredLogs(data: { days: number; logType: 'all' | 'operation' | 'audit' }) {
  return request({
    url: '/api/log/clean',
    method: 'post',
    data
  });
}

/** 导出日志 */
export function fetchExportLogs(params: {
  logType: 'operation' | 'audit';
  startTime?: string;
  endTime?: string;
  format: 'json' | 'csv';
}) {
  return request({
    url: '/api/log/export',
    method: 'get',
    params,
    responseType: 'blob'
  });
}

// ===== 日志类型枚举 =====

// 操作日志目标类型
export enum LogTargetType {
  USER = 'user',
  ORDER = 'order',
  COURSE = 'course',
  PROVIDER = 'provider',
  SYSTEM = 'system',
  CONFIG = 'config',
  EMAIL = 'email'
}

// 操作日志类型
export enum LogOperationType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  LOGIN = 'login',
  LOGOUT = 'logout',
  EXPORT = 'export',
  IMPORT = 'import'
}

// 审计日志级别
export enum AuditLogLevel {
  INFO = 'info',
  WARN = 'warn',
  ERROR = 'error',
  CRITICAL = 'critical'
}

// 审计日志操作类型
export enum AuditLogAction {
  LOGIN = 'login',
  LOGOUT = 'logout',
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  VIEW = 'view',
  EXPORT = 'export',
  IMPORT = 'import',
  CONFIG_CHANGE = 'config_change',
  PERMISSION_CHANGE = 'permission_change'
}

// 日志目标类型文本映射
export const LOG_TARGET_TYPE_TEXT = {
  [LogTargetType.USER]: '用户',
  [LogTargetType.ORDER]: '订单',
  [LogTargetType.COURSE]: '课程',
  [LogTargetType.PROVIDER]: '供应商',
  [LogTargetType.SYSTEM]: '系统',
  [LogTargetType.CONFIG]: '配置',
  [LogTargetType.EMAIL]: '邮件'
};

// 操作类型文本映射
export const LOG_OPERATION_TYPE_TEXT = {
  [LogOperationType.CREATE]: '创建',
  [LogOperationType.UPDATE]: '更新',
  [LogOperationType.DELETE]: '删除',
  [LogOperationType.LOGIN]: '登录',
  [LogOperationType.LOGOUT]: '登出',
  [LogOperationType.EXPORT]: '导出',
  [LogOperationType.IMPORT]: '导入'
};

// 审计日志级别文本映射
export const AUDIT_LOG_LEVEL_TEXT = {
  [AuditLogLevel.INFO]: '信息',
  [AuditLogLevel.WARN]: '警告',
  [AuditLogLevel.ERROR]: '错误',
  [AuditLogLevel.CRITICAL]: '严重'
};

// 审计日志级别颜色映射
export const AUDIT_LOG_LEVEL_TYPE = {
  [AuditLogLevel.INFO]: 'info',
  [AuditLogLevel.WARN]: 'warning',
  [AuditLogLevel.ERROR]: 'danger',
  [AuditLogLevel.CRITICAL]: 'danger'
} as const;

// 审计日志操作类型文本映射
export const AUDIT_LOG_ACTION_TEXT = {
  [AuditLogAction.LOGIN]: '登录',
  [AuditLogAction.LOGOUT]: '登出',
  [AuditLogAction.CREATE]: '创建',
  [AuditLogAction.UPDATE]: '更新',
  [AuditLogAction.DELETE]: '删除',
  [AuditLogAction.VIEW]: '查看',
  [AuditLogAction.EXPORT]: '导出',
  [AuditLogAction.IMPORT]: '导入',
  [AuditLogAction.CONFIG_CHANGE]: '配置变更',
  [AuditLogAction.PERMISSION_CHANGE]: '权限变更'
};
