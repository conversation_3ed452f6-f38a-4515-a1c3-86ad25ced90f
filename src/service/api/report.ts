import { request } from '../request/index';

/**
 * 报表管理API
 * 遵循项目规范，提供完整的报表生成和管理接口
 */

// ===== 报表生成接口 =====

/** 生成用户报表 */
export function fetchGenerateUserReport(data: { startDate?: string; endDate?: string; format?: 'excel' | 'csv' }) {
  return request({
    url: '/api/report/user',
    method: 'post',
    data
  });
}

/** 生成订单报表 */
export function fetchGenerateOrderReport(data: {
  startDate?: string;
  endDate?: string;
  status?: number;
  format?: 'excel' | 'csv';
}) {
  return request({
    url: '/api/report/order',
    method: 'post',
    data
  });
}

/** 生成财务报表 */
export function fetchGenerateFinanceReport(data: {
  startDate?: string;
  endDate?: string;
  groupBy?: 'day' | 'week' | 'month';
  format?: 'excel' | 'csv';
}) {
  return request({
    url: '/api/report/finance',
    method: 'post',
    data
  });
}

/** 获取报表列表 */
export function fetchReportList() {
  return request({
    url: '/api/report/list',
    method: 'get'
  });
}

/** 下载报表文件 */
export function fetchDownloadReport(fileName: string) {
  return request({
    url: `/api/report/download/${fileName}`,
    method: 'get',
    responseType: 'blob'
  });
}

/** 删除报表文件 */
export function fetchDeleteReport(fileName: string) {
  return request({
    url: `/api/report/${fileName}`,
    method: 'delete'
  });
}

// ===== 报表类型枚举 =====
export enum ReportType {
  USER = 'user',
  ORDER = 'order',
  FINANCE = 'finance'
}

export enum ReportFormat {
  EXCEL = 'excel',
  CSV = 'csv'
}

export enum ReportGroupBy {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month'
}

// 报表类型文本映射
export const REPORT_TYPE_TEXT = {
  [ReportType.USER]: '用户报表',
  [ReportType.ORDER]: '订单报表',
  [ReportType.FINANCE]: '财务报表'
};

// 报表格式文本映射
export const REPORT_FORMAT_TEXT = {
  [ReportFormat.EXCEL]: 'Excel格式',
  [ReportFormat.CSV]: 'CSV格式'
};

// 分组方式文本映射
export const REPORT_GROUP_BY_TEXT = {
  [ReportGroupBy.DAY]: '按天统计',
  [ReportGroupBy.WEEK]: '按周统计',
  [ReportGroupBy.MONTH]: '按月统计'
};

// 订单状态选项
export const ORDER_STATUS_OPTIONS = [
  { label: '全部状态', value: '' },
  { label: '待支付', value: 1 },
  { label: '待处理', value: 2 },
  { label: '处理中', value: 3 },
  { label: '已完成', value: 4 },
  { label: '已取消', value: 5 },
  { label: '处理失败', value: 6 }
];

// 报表配置
export const REPORT_CONFIG = {
  // 默认日期范围（最近30天）
  defaultDateRange: () => {
    const end = new Date();
    const start = new Date();
    start.setDate(start.getDate() - 30);
    return [start, end];
  },

  // 支持的文件格式
  supportedFormats: [
    { label: 'Excel (.xlsx)', value: 'excel' },
    { label: 'CSV (.csv)', value: 'csv' }
  ],

  // 财务报表分组选项
  financeGroupOptions: [
    { label: '按天', value: 'day' },
    { label: '按周', value: 'week' },
    { label: '按月', value: 'month' }
  ],

  // 最大文件大小（MB）
  maxFileSize: 50,

  // 报表保留天数
  retentionDays: 30
};

// 报表工具函数
export const reportUtils = {
  // 格式化文件大小
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return `${Number.parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
  },

  // 格式化日期范围
  formatDateRange: (startDate: string, endDate: string): string => {
    const start = new Date(startDate).toLocaleDateString('zh-CN');
    const end = new Date(endDate).toLocaleDateString('zh-CN');
    return `${start} 至 ${end}`;
  },

  // 生成报表文件名
  generateFileName: (type: string, format: string): string => {
    const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
    return `${type}_report_${timestamp}.${format}`;
  },

  // 验证日期范围
  validateDateRange: (startDate: string, endDate: string): boolean => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const now = new Date();

    // 开始日期不能晚于结束日期
    if (start > end) return false;

    // 结束日期不能晚于当前日期
    if (end > now) return false;

    // 日期范围不能超过1年
    const oneYear = 365 * 24 * 60 * 60 * 1000;
    if (end.getTime() - start.getTime() > oneYear) return false;

    return true;
  },

  // 下载文件
  downloadFile: (blob: Blob, fileName: string): void => {
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  }
};
