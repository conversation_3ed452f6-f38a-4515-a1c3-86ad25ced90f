import { request } from '../request/index';

/**
 * 数据备份管理API
 * 遵循项目规范，提供完整的数据备份管理接口
 */

// ===== 备份管理接口 =====

/** 创建数据库备份 */
export function fetchCreateDatabaseBackup(data: { description?: string }) {
  return request({
    url: '/api/backup/database',
    method: 'post',
    data
  });
}

/** 获取备份列表 */
export function fetchBackupList(params: { page?: number; pageSize?: number; backupType?: string; status?: string }) {
  return request({
    url: '/api/backup/list',
    method: 'get',
    params
  });
}

/** 恢复数据库备份 */
export function fetchRestoreDatabaseBackup(backupId: number) {
  return request({
    url: `/api/backup/${backupId}/restore`,
    method: 'post'
  });
}

/** 删除备份 */
export function fetchDeleteBackup(backupId: number) {
  return request({
    url: `/api/backup/${backupId}`,
    method: 'delete'
  });
}

/** 获取备份统计信息 */
export function fetchBackupStats() {
  return request({
    url: '/api/backup/stats',
    method: 'get'
  });
}

// ===== 备份类型枚举 =====
export enum BackupType {
  DATABASE = 'database',
  CONFIG = 'config',
  FILES = 'files'
}

// 备份状态枚举
export enum BackupStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// 备份类型文本映射
export const BACKUP_TYPE_TEXT: Record<string, string> = {
  [BackupType.DATABASE]: '数据库备份',
  [BackupType.CONFIG]: '配置备份',
  [BackupType.FILES]: '文件备份'
};

// 备份状态文本映射
export const BACKUP_STATUS_TEXT: Record<string, string> = {
  [BackupStatus.PENDING]: '等待中',
  [BackupStatus.RUNNING]: '备份中',
  [BackupStatus.COMPLETED]: '已完成',
  [BackupStatus.FAILED]: '失败'
};

// 备份状态类型映射
// 备份状态颜色映射
export const BACKUP_STATUS_TYPE: Record<string, string> = {
  [BackupStatus.PENDING]: 'info',
  [BackupStatus.RUNNING]: 'warning',
  [BackupStatus.COMPLETED]: 'success',
  [BackupStatus.FAILED]: 'danger'
};
