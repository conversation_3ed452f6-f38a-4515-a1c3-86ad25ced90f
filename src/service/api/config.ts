import { request } from '../request';

/** 系统配置相关API */

/** 获取系统配置 */
export function fetchSystemConfig() {
  return request<Api.Config.SystemConfig>({
    url: '/api/config',
    method: 'get'
  });
}

/** 更新系统配置 */
export function updateSystemConfig(config: Api.Config.SystemConfig) {
  return request<Api.Config.SystemConfig>({
    url: '/api/config',
    method: 'put',
    data: { configs: config }
  });
}

/** 重置系统配置 */
export function resetSystemConfig() {
  return request<Api.Config.SystemConfig>({
    url: '/api/config/reset',
    method: 'post'
  });
}

/** 测试邮件配置 */
export function testEmailConfig(config: Api.Config.EmailTestConfig) {
  return request<null>({
    url: '/api/config/test-email',
    method: 'post',
    data: config
  });
}

/** 获取系统状态 */
export function fetchSystemStatus() {
  return request<Api.Config.SystemStatus>({
    url: '/api/config/status',
    method: 'get'
  });
}
