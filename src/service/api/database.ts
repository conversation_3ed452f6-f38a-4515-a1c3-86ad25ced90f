import { request } from '../request';

/** 数据库管理相关API */

/** 获取数据库信息 */
export function fetchDatabaseInfo() {
  return request<Api.Database.DatabaseInfo>({
    url: '/api/database/info',
    method: 'get'
  });
}

/** 获取表列表 */
export function fetchTableList(params?: Api.Database.TableSearchParams) {
  return request<Api.Database.TableList>({
    url: '/api/database/tables',
    method: 'get',
    params
  });
}

/** 获取表结构 */
export function fetchTableStructure(tableName: string) {
  return request<Api.Database.TableStructure[]>({
    url: `/api/database/tables/${tableName}/structure`,
    method: 'get'
  });
}

/** 优化表 */
export function fetchOptimizeTable(tableName: string) {
  return request<null>({
    url: `/api/database/tables/${tableName}/optimize`,
    method: 'post'
  });
}

/** 修复表 */
export function fetchRepairTable(tableName: string) {
  return request<null>({
    url: `/api/database/tables/${tableName}/repair`,
    method: 'post'
  });
}

/** 清空表 */
export function fetchTruncateTable(tableName: string) {
  return request<null>({
    url: `/api/database/tables/${tableName}/truncate`,
    method: 'post'
  });
}

/** 删除表 */
export function fetchDropTable(tableName: string) {
  return request<null>({
    url: `/api/database/tables/${tableName}`,
    method: 'delete'
  });
}

/** 导出表 */
export function fetchExportTable(tableName: string) {
  return request<Blob>({
    url: `/api/database/tables/${tableName}/export`,
    method: 'get'
  });
}

/** 执行SQL */
export function fetchExecuteSQL(data: Api.Database.SQLExecuteParams) {
  return request<Api.Database.SQLExecuteResult>({
    url: '/api/database/execute-sql',
    method: 'post',
    data
  });
}

/** 数据库状态文本映射 */
export const DATABASE_STATUS_TEXT: Record<string, string> = {
  connected: '已连接',
  disconnected: '已断开',
  connecting: '连接中',
  error: '连接错误',
  unknown: '未知状态'
};

/** 数据库状态类型映射 */
export const DATABASE_STATUS_TYPE: Record<string, string> = {
  connected: 'success',
  disconnected: 'danger',
  connecting: 'warning',
  error: 'danger',
  unknown: 'info'
};
