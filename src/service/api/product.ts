/**
 * 商品管理API接口
 */

import { request } from '../request';

// 商品列表查询参数
export interface ProductListParams {
  page?: number;
  pageSize?: number;
  categoryId?: number;
  platformType?: string;
  status?: number;
  keyword?: string;
}

// 商品信息接口
export interface Product {
  product_id: number;
  product_name: string;
  category_id: number;
  category_name?: string;
  platform_type: string;
  provider_id?: number;
  provider_name?: string;
  price: number;
  cost_price: number;
  service_type: string;
  description?: string;
  status: number;
  sort_order: number;
  getnoun?: string; // 查询参数
  noun?: string; // 对接参数
  docking?: string; // 对接配置
  wck?: number; // 无查课标志
  kcid?: number; // 课程ID标志
  api?: number; // API开关
  nocheck?: number; // 跳过检查
  addtime?: string; // 添加时间
  create_time: string;
  update_time: string;
}

// 商品分类接口
export interface ProductCategory {
  category_id: number;
  name: string;
  sort_order: number;
  status: number;
  product_count: number;
}

/**
 * 获取商品列表
 */
export function fetchProductList(params: ProductListParams = {}) {
  return request<{
    list: Product[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }>({
    url: '/api/product/list',
    method: 'get',
    params
  });
}

/**
 * 获取商品详情
 */
export function fetchProductDetail(productId: number) {
  return request<Product>({
    url: `/api/product/${productId}`,
    method: 'get'
  });
}

/**
 * 获取商品分类列表
 */
export function fetchProductCategories() {
  return request<ProductCategory[]>({
    url: '/api/product/categories/list',
    method: 'get'
  });
}

/**
 * 创建商品（管理员）
 */
export function createProduct(data: {
  productName: string;
  categoryId: number;
  platformType: string;
  providerId?: number;
  price: number;
  costPrice?: number;
  serviceType?: string;
  description?: string;
  status?: number;
  sortOrder?: number;
}) {
  return request<{ productId: number }>({
    url: '/api/product',
    method: 'post',
    data
  });
}

/**
 * 更新商品（管理员）
 */
export function updateProduct(
  productId: number,
  data: Partial<{
    productName: string;
    categoryId: number;
    platformType: string;
    providerId: number;
    price: number;
    costPrice: number;
    serviceType: string;
    description: string;
    status: number;
    sortOrder: number;
  }>
) {
  return request({
    url: `/api/product/${productId}`,
    method: 'put',
    data
  });
}

/**
 * 删除商品（管理员）
 */
export function deleteProduct(productId: number) {
  return request({
    url: `/api/product/${productId}`,
    method: 'delete'
  });
}

/**
 * 获取平台类型列表
 */
export function fetchPlatformTypes() {
  return request<string[]>({
    url: '/api/product/platform-types',
    method: 'get'
  });
}

/**
 * 获取分类列表（分页）
 */
export function fetchCategoryList(
  params: {
    page?: number;
    pageSize?: number;
    keyword?: string;
    status?: number;
  } = {}
) {
  return request<{
    list: ProductCategory[];
    total: number;
    page: number;
    pageSize: number;
    totalPages: number;
  }>({
    url: '/api/category/list',
    method: 'get',
    params
  });
}

/**
 * 批量更新商品
 */
export function batchUpdateProducts(data: { productIds: number[]; updateData: Record<string, any> }) {
  return request({
    url: '/api/product/batch-update',
    method: 'post',
    data
  });
}

/**
 * 批量删除商品
 */
export function batchDeleteProducts(productIds: number[]) {
  return request({
    url: '/api/product/batch-delete',
    method: 'post',
    data: { productIds }
  });
}

/**
 * 一键设置排序
 */
export function autoSetSort(data: {
  sortType: 'by_cid_asc' | 'by_cid_desc' | 'by_name_asc' | 'by_price_asc' | 'by_price_desc' | 'reset_default';
  categoryId?: number;
}) {
  return request({
    url: '/api/product/auto-sort',
    method: 'post',
    data
  });
}

/**
 * 创建分类
 */
export function createCategory(data: { name: string; sortOrder?: number; status?: number }) {
  return request<{ categoryId: number }>({
    url: '/api/category',
    method: 'post',
    data
  });
}

/**
 * 更新分类
 */
export function updateCategory(
  categoryId: number,
  data: Partial<{
    name: string;
    sortOrder: number;
    status: number;
  }>
) {
  return request({
    url: `/api/category/${categoryId}`,
    method: 'put',
    data
  });
}

/**
 * 删除分类
 */
export function deleteCategory(categoryId: number) {
  return request({
    url: `/api/category/${categoryId}`,
    method: 'delete'
  });
}
