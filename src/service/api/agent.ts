import { request } from '../request/index';

/**
 * 代理分销系统API
 * 基于现有fd_user表的代理关系字段实现基础的上下级关系管理
 */

// ===== 代理管理接口 =====

/** 获取用户的代理关系信息 */
export function fetchAgentInfo() {
  return request({
    url: '/api/agent/info',
    method: 'get'
  });
}

/** 获取下级用户列表 */
export function fetchSubordinateList(params: { page?: number; pageSize?: number; keyword?: string }) {
  return request({
    url: '/api/agent/subordinates',
    method: 'get',
    params
  });
}

/** 为下级用户充值 */
export function rechargeForSubordinate(data: { target_user_id: number; amount: number; description?: string }) {
  return request({
    url: '/api/agent/recharge',
    method: 'post',
    data
  });
}

/** 修改邀请码 */
export function updateInviteCode(data: { invite_code: string }) {
  return request({
    url: '/api/agent/invite-code',
    method: 'put',
    data
  });
}

/** 获取代理统计数据 */
export function fetchAgentStatistics() {
  return request({
    url: '/api/agent/statistics',
    method: 'get'
  });
}
