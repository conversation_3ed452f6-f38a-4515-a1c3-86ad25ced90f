export interface FinanceRecord {
  finance_id: number;
  type: string; // recharge | consume | refund | commission | other
  amount: number;
  balance_before: number;
  balance_after: number;
  description?: string;
  operator_name?: string;
  create_time: string; // ISO string or formatted
}

export interface FetchFinanceListParams {
  page?: number;
  pageSize?: number;
  type?: string;
  startTime?: string;
  endTime?: string;
  user_id?: number;
}

export interface FetchFinanceListResult {
  list: FinanceRecord[];
  pagination: {
    page: number;
    pageSize: number;
    total: number;
  };
}

