import { createRequest } from '@sa/axios';
import { licenseConfig } from '@/config/license';
import { request } from '@/service/request/index';
import { getServiceBaseURL } from '@/utils/service';

const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y';
const { baseURL } = getServiceBaseURL(import.meta.env, isHttpProxy);

// 创建专门用于授权系统API的请求实例，允许4xx状态码通过
const licenseRequest = createRequest<App.Service.Response>(
  {
    baseURL,
    // 允许4xx状态码通过，这样我们可以在业务层处理授权系统的错误响应
    validateStatus: (status: number) => {
      return (status >= 200 && status < 300) || status === 304 || (status >= 400 && status < 500);
    }
  },
  {
    async onRequest(config) {
      return config;
    },
    isBackendSuccess(response) {
      // 对于授权API，我们认为所有通过validateStatus的响应都是"成功"的
      // 具体的业务成功/失败由业务层判断
      return true;
    },
    async onBackendFail() {
      // 由于isBackendSuccess总是返回true，这个函数不会被调用
      return null;
    },
    transformBackendResponse(response) {
      // 直接返回响应数据，不做转换
      return response.data;
    },
    onError(error) {
      // 只处理真正的网络错误（5xx或网络连接问题）
      console.error('授权API网络错误:', error);
    }
  }
);

// 使用主要的request实例，它有正确的认证逻辑

/**
 * 验证授权密钥（通过后端API）
 *
 * @param licenseKey 授权密钥
 */
export function validateLicense(licenseKey: string) {
  return request<Api.License.ValidateResult>({
    url: '/license/validate',
    method: 'post',
    data: {
      licenseKey
    }
  });
}

/**
 * 绑定授权密钥到域名
 *
 * @param licenseKey 授权密钥
 * @param domain 域名
 */
/** 纯后端控制模式 - 移除绑定授权功能 授权绑定完全通过后端API进行 */

/** 获取授权状态（通过后端API） */
export function getLicenseStatus() {
  return request<Api.License.StatusResult>({
    url: '/license/status',
    method: 'get'
  });
}

/** 刷新授权状态（通过后端API） */
export function refreshLicenseStatus() {
  return request<Api.License.ValidateResult>({
    url: '/license/refresh',
    method: 'post'
  });
}

/** 重置授权信息（通过后端API） */
export function resetLicenseInfo() {
  return request<Api.License.ResetResult>({
    url: '/license/reset',
    method: 'delete'
  });
}

/** 获取授权详细信息（管理员专用，通过后端API） */
export function getLicenseDetails() {
  return request<Api.License.DetailsResult>({
    url: '/license/details',
    method: 'get'
  });
}

/** 获取授权统计信息 */
export function getLicenseStats() {
  return request<Api.License.StatusResult>({
    url: '/license/stats',
    method: 'get'
  });
}

// 授权系统登录和管理功能已解耦，不再需要这些API

// 同步功能已废弃，授权验证完全通过后端处理
