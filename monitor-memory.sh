#!/bin/bash

# 内存监控脚本
# 监控前后端进程的内存使用情况

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 格式化内存大小
format_memory() {
    local mem_kb=$1
    if [ $mem_kb -gt 1048576 ]; then
        echo "$(echo "scale=2; $mem_kb/1048576" | bc)GB"
    elif [ $mem_kb -gt 1024 ]; then
        echo "$(echo "scale=2; $mem_kb/1024" | bc)MB"
    else
        echo "${mem_kb}KB"
    fi
}

# 获取进程内存使用情况
get_process_memory() {
    local pattern=$1
    local name=$2
    
    local pids=($(pgrep -f "$pattern"))
    
    if [ ${#pids[@]} -eq 0 ]; then
        echo "  $name: 未运行"
        return
    fi
    
    local total_memory=0
    local process_count=0
    
    for pid in "${pids[@]}"; do
        if [ -f "/proc/$pid/status" ]; then
            local memory=$(grep "VmRSS:" /proc/$pid/status | awk '{print $2}')
            if [ ! -z "$memory" ]; then
                total_memory=$((total_memory + memory))
                process_count=$((process_count + 1))
                
                local formatted_mem=$(format_memory $memory)
                echo "    PID $pid: $formatted_mem"
            fi
        fi
    done
    
    if [ $process_count -gt 0 ]; then
        local formatted_total=$(format_memory $total_memory)
        echo "  $name 总计: $formatted_total ($process_count 个进程)"
        
        # 内存警告
        if [ $total_memory -gt 2097152 ]; then # 2GB
            log_error "$name 内存使用过高: $formatted_total"
        elif [ $total_memory -gt 1048576 ]; then # 1GB
            log_warning "$name 内存使用较高: $formatted_total"
        fi
    fi
}

# 获取系统内存信息
get_system_memory() {
    local total_mem=$(grep "MemTotal:" /proc/meminfo | awk '{print $2}')
    local free_mem=$(grep "MemFree:" /proc/meminfo | awk '{print $2}')
    local available_mem=$(grep "MemAvailable:" /proc/meminfo | awk '{print $2}')
    local used_mem=$((total_mem - available_mem))
    
    local usage_percent=$(echo "scale=2; $used_mem*100/$total_mem" | bc)
    
    echo "系统内存:"
    echo "  总内存: $(format_memory $total_mem)"
    echo "  已使用: $(format_memory $used_mem) (${usage_percent}%)"
    echo "  可用内存: $(format_memory $available_mem)"
    
    # 系统内存警告
    local usage_int=$(echo "$usage_percent" | cut -d'.' -f1)
    if [ $usage_int -gt 90 ]; then
        log_error "系统内存使用率过高: ${usage_percent}%"
    elif [ $usage_int -gt 80 ]; then
        log_warning "系统内存使用率较高: ${usage_percent}%"
    fi
}

# 清理内存
cleanup_memory() {
    log_info "清理系统内存..."
    
    # 清理页面缓存
    sync
    echo 1 > /proc/sys/vm/drop_caches
    
    # 清理npm缓存
    if command -v npm &> /dev/null; then
        npm cache clean --force 2>/dev/null
    fi
    
    # 清理pnpm缓存
    if command -v pnpm &> /dev/null; then
        pnpm store prune 2>/dev/null
    fi
    
    log_info "内存清理完成"
}

# 优化建议
show_optimization_tips() {
    echo
    log_info "=== 内存优化建议 ==="
    
    # 检查Node.js版本
    if command -v node &> /dev/null; then
        local node_version=$(node --version)
        echo "Node.js版本: $node_version"
        
        # 建议使用较新版本
        local major_version=$(echo $node_version | cut -d'.' -f1 | sed 's/v//')
        if [ $major_version -lt 18 ]; then
            log_warning "建议升级到Node.js 18+以获得更好的内存管理"
        fi
    fi
    
    # 检查是否有多个相同进程
    local vite_count=$(pgrep -f "vite" | wc -l)
    local node_count=$(pgrep -f "node.*ts-node" | wc -l)
    
    if [ $vite_count -gt 1 ]; then
        log_warning "发现多个Vite进程($vite_count个)，建议使用 ./start-dev.sh restart 重启"
    fi
    
    if [ $node_count -gt 1 ]; then
        log_warning "发现多个后端进程($node_count个)，建议使用 ./start-dev.sh restart 重启"
    fi
    
    echo "优化建议:"
    echo "  1. 使用 ./start-dev.sh 脚本管理服务，避免重复启动"
    echo "  2. 定期运行 $0 cleanup 清理内存"
    echo "  3. 关闭不必要的浏览器标签页"
    echo "  4. 使用内存优化的Vite配置"
    echo "  5. 定期重启开发服务释放内存"
}

# 主函数
main() {
    echo "=== SoybeanAdmin 内存监控 ==="
    echo "时间: $(date '+%Y-%m-%d %H:%M:%S')"
    echo
    
    # 系统内存信息
    get_system_memory
    echo
    
    # 进程内存信息
    echo "进程内存使用:"
    get_process_memory "vite.*--mode test" "前端服务(Vite)"
    get_process_memory "ts-node.*src/index.ts" "后端服务(Node.js)"
    get_process_memory "nginx.*master" "Nginx"
    get_process_memory "mysql" "MySQL"
    echo
    
    # VSCode相关进程（如果存在）
    local vscode_pids=($(pgrep -f "vscode-server"))
    if [ ${#vscode_pids[@]} -gt 0 ]; then
        get_process_memory "vscode-server" "VSCode Server"
        echo
    fi
    
    # 优化建议
    show_optimization_tips
}

# 处理命令行参数
case "${1:-monitor}" in
    "monitor")
        main
        ;;
    "cleanup")
        cleanup_memory
        echo
        main
        ;;
    "watch")
        log_info "开始监控内存使用情况 (每30秒刷新一次，按Ctrl+C退出)"
        while true; do
            clear
            main
            sleep 30
        done
        ;;
    *)
        echo "用法: $0 {monitor|cleanup|watch}"
        echo "  monitor - 显示当前内存使用情况 (默认)"
        echo "  cleanup - 清理内存后显示使用情况"
        echo "  watch   - 持续监控内存使用情况"
        exit 1
        ;;
esac
