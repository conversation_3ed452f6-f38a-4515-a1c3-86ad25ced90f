# 开发环境管理指南

## 🚀 快速开始

```bash
# 智能模式（推荐）
./dev.sh

# 常用命令
./dev.sh start    # 启动服务
./dev.sh stop     # 停止服务
./dev.sh status   # 查看状态
./dev.sh logs     # 查看日志
./dev.sh help     # 显示帮助
```

## 🔒 安全特性

- ✅ **系统保护**：不会影响SSH、nginx、systemd等关键进程
- ✅ **远程连接保护**：确保VSCode Server和SSH连接不受影响
- ✅ **温和终止**：先使用SIGTERM，必要时才使用SIGKILL
- ✅ **精确匹配**：只处理项目相关的进程

## 🤖 智能管理工具 (dev.sh)

### 可用命令
| 命令 | 功能 | 说明 |
|------|------|------|
| `./dev.sh` | 智能模式 | 自动检测状态并提供建议 |
| `./dev.sh start` | 启动服务 | 启动前后端开发环境 |
| `./dev.sh stop` | 停止服务 | 安全停止所有项目进程 |
| `./dev.sh restart` | 重启服务 | 先停止再启动 |
| `./dev.sh status` | 查看状态 | 显示服务运行状态 |
| `./dev.sh logs` | 查看日志 | 显示前后端日志 |
| `./dev.sh help` | 显示帮助 | 显示使用说明 |

## 🔍 故障排除

```bash
# 常见问题解决
./dev.sh status    # 查看状态
./dev.sh logs      # 查看日志
./dev.sh restart   # 重启服务
```

## ⚠️ 注意事项

- ✅ 优先使用 `./dev.sh` 智能管理
- ❌ 不要手动杀死SSH进程
- ❌ 不要使用 `killall node`
