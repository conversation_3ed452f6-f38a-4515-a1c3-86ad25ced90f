# FreeDom 网络课程交易与管理系统

## 项目概述

FreeDom 是一个综合性的网络课程交易和管理系统，主要用于网课代做、运动打卡、作业代写等服务的管理和交易。系统采用 PHP 开发，使用 MySQL 作为数据库，为用户提供了一个完整的服务交易平台。

## 系统架构

### 技术栈
- **前端**：Bootstrap 框架、自定义 UI 组件、jQuery、Vue.js、Element UI
- **后端**：PHP
- **数据库**：MySQL
- **服务器**：Linux 环境

### 文件结构
```
/www/wwwroot/**************/
├── index/                   # 主要页面文件目录
│   ├── assets/              # 静态资源
│   ├── components/          # 组件
│   ├── css/                 # 样式文件
│   ├── js/                  # JavaScript 文件
│   ├── layer/               # Layer 弹窗组件
│   ├── static/              # 静态资源
│   ├── downloads/           # 下载资源
│   ├── add*.php             # 各类交单页面
│   ├── class*.php           # 课程相关页面
│   ├── home.php             # 首页
│   ├── index.php            # 主框架页面
│   └── ...                  # 其他功能页面
├── confing/                 # 配置文件目录
│   ├── common.php           # 公共配置
│   └── config.php           # 数据库配置
├── assets/                  # 全局静态资源
├── includes/                # 公共函数库
├── Checkorder/              # 订单检查相关
├── epay/                    # 支付相关
├── layuiadmin/              # Layui 管理界面
├── .well-known/             # SSL 证书相关
├── index.php                # 入口文件
└── ...                      # 其他文件
```

## 核心功能模块

### 1. 用户管理系统
- **用户登录/注册**：支持账号密码登录
- **用户权限控制**：区分管理员、普通用户和代理用户
- **个人中心**：用户信息管理、密码修改
- **代理系统**：支持多级代理分销模式
- **操作日志**：记录用户操作行为

### 2. 订单管理
- **快捷交单**：简化的订单提交流程
- **经典交单**：完整的订单提交流程
- **订单汇总**：集中查看所有订单
- **订单状态跟踪**：实时跟踪订单进度
- **订单分类**：按类型、状态等分类查看

### 3. 商品管理
- **网课设置**：配置可代做的网课
- **分类管理**：对服务进行分类
- **货源添加**：添加新的服务货源
- **价格设置**：设置服务价格，支持密价
- **接口配置**：配置第三方接口

### 4. 特殊功能模块
- **运动打卡系统**
  - KEEP 打卡
  - 云运动打卡
  - 步道乐跑打卡
  - 闪动校园打卡
  - 体适能打卡
  - 宥马健身打卡
  - 小步点打卡
  - 运动世界打卡
- **实习打卡**
  - CATKA 实习打卡
  - APPUI 打卡
  - COPAI 打卡
- **TBS 撰写**：文档撰写服务

### 5. 支付系统
- **在线充值**：支持多种支付方式
- **余额管理**：用户余额查询、消费记录
- **支付渠道配置**：配置支付接口

### 6. 系统设置
- **网站配置**：基本信息、功能开关
- **邮件模板**：配置系统邮件
- **公告配置**：发布系统公告
- **数据统计**：统计系统使用数据
- **等级设置**：设置用户等级体系

### 7. 对接功能详解
- **API 接口对接**：
  - **查课接口**：根据平台ID、学生账号密码等信息查询可用课程
  - **下单接口**：提交订单信息，创建新订单
  - **补刷接口**：针对特定订单进行补充操作
  - **同步接口**：同步订单状态和进度
  - **改密接口**：修改账号密码
  - **独家项目接口**：针对特定项目的专用接口

- **第三方平台对接**：
  - **支付平台**：支持支付宝、微信支付等多种支付渠道
  - **邮件服务**：支持 SMTP 邮件服务，用于系统通知和营销

- **对接参数设置**：
  - 账号（uid）
  - 密码（key）
  - 平台ID（platform）
  - 学生账号（user）
  - 学生密码（pass）
  - 学校（school）等必要参数

- **29系统对接**：
  - 专门为29系统提供的对接标识和代码
  - 支持查课、下单等操作

### 8. 货源服务商管理功能
- **货源平台管理**：
  - 添加货源平台：设置平台名称、类型、域名、账号、密码、token等信息
  - 编辑货源平台：修改平台信息和对接参数
  - 平台余额查询：实时查询各平台账户余额

- **一键对接功能**：
  - 支持批量对接指定平台的所有项目
  - 可设置分类ID、分类名称和价格调整比例

- **平台类型支持**：
  - 系统内置多种平台类型，通过下拉选择
  - 支持自定义平台名称和对接参数

- **安全管理**：
  - 敏感信息（账号、密码、token等）在界面上以星号显示
  - 仅管理员可访问货源管理功能

### 9. 课程管理功能
- **课程信息管理**：
  - 平台名称：课程所属平台
  - 排序：控制课程显示顺序
  - 价格设置：支持定价和自动计算售价（定价/5）
  - 课程介绍：详细说明课程内容
  - 上架状态：控制课程是否可见和可购买
  - API对接：控制是否允许通过API访问该课程

- **课程分类功能**：
  - 支持按分类筛选课程
  - 可设置课程所属分类
  - 分类状态管理（常用项目/冷门项目）

- **批量操作功能**：
  - 批量上架/下架：快速调整多个课程状态
  - 批量删除：移除多个课程
  - 批量改关键词：批量修改课程关键词
  - 批量改价格：支持直接设置价格或按比例调整

- **课程对接设置**：
  - 查询平台：设置课程查询来源
  - 对接平台：设置课程对接目标
  - 查询参数：设置查询所需参数
  - 对接参数：设置对接所需参数
  - 计算方式：支持乘法和加法两种价格计算方式

### 10. 用户下单功能与流程逻辑
- **下单方式**：
  - **快捷交单**：简化流程，包括新版交单、常用交单、手机交单、无查项目等
  - **经典交单**：按分类进行下单，支持多级分类结构
  - **特殊分类交单**：针对运动打卡、实习打卡等特殊服务的专用下单页面

- **下单流程**：
  1. 用户选择交单方式（快捷/经典/特殊）
  2. 选择具体服务类型或课程
  3. 填写必要信息（账号、密码、学校等）
  4. 确认价格并完成支付
  5. 系统生成订单并分配给服务商

- **订单确认**：
  - 支付完成后自动生成订单
  - 可在订单汇总页面查看所有订单
  - 支持查询可用课程和热门排行

- **特殊项目下单**：
  - 运动打卡：支持多种运动平台（KEEP、云运动、步道乐跑等）
  - 实习打卡：支持CATKA、APPUI、COPAI等实习平台
  - TBS撰写：支持文档撰写服务

### 11. 订单管理逻辑
- **订单查看功能**：
  - 订单汇总：集中展示所有订单
  - 按分类查看：可按服务分类筛选订单
  - 订单详情：查看订单的详细信息和进度

- **订单状态管理**：
  - 支持多种订单状态（待付款、进行中、已完成等）
  - 实时更新订单状态和进度
  - 支持订单状态的手动调整

- **订单数据分析**：
  - 提供订单统计和数据分析功能
  - 支持按时间、类型等维度统计订单数据
  - 生成可视化报表，辅助决策

- **工单系统**：
  - 支持用户提交工单反馈问题
  - 工单通知功能，及时提醒处理
  - 工单状态跟踪和管理

## 用户角色与权限

### 管理员 (uid=1)
- 拥有系统所有权限
- 可进行系统配置、商品管理、用户管理等操作
- 可查看所有订单和数据统计
- 负责管理货源平台和对接设置

### 代理用户
- 可管理下级用户
- 可查看自己及下级的订单
- 可进行充值和提现操作
- 享受订单分成和推广奖励

### 普通用户
- 可提交订单
- 可查看自己的订单
- 可进行充值操作
- 可评价已完成的订单

## 数据库结构

主要数据表：
- `qingka_wangke_user`：用户表
- `qingka_wangke_config`：配置表
- `qingka_wangke_fenlei`：分类表
- `qingka_wangke_order`：订单表
- `qingka_wangke_course`：课程表
- `qingka_wangke_huoyuan`：货源表
- `qingka_wangke_payment`：支付记录表
- `qingka_wangke_log`：操作日志表
- `qingka_wangke_finance`：财务记录表
- 其他相关功能表

## 系统特点

1. **模块化设计**：功能分类清晰，易于扩展
2. **多服务支持**：支持网课代做、运动打卡、作业代写等多种服务
3. **代理分销**：支持多级代理分销模式，增强系统推广能力
4. **完善的订单管理**：从提交到完成的全流程管理
5. **灵活的配置**：大部分功能可通过后台配置调整
6. **多平台对接**：支持与多种第三方平台对接，扩展服务范围
7. **数据可视化**：提供直观的数据统计和分析功能
8. **接口丰富**：提供多种API接口，便于系统集成

## 使用场景

1. **学生用户**：需要网课代做、运动打卡、作业代写等服务
2. **代理商**：希望通过代理分销获取收益
3. **服务提供商**：提供网课代做、运动打卡等服务的机构或个人
4. **教育机构**：需要批量管理课程和学生作业的机构

## 安全措施

1. **用户认证**：账号密码登录，支持记住登录状态
2. **操作日志**：记录用户操作，便于追踪异常行为
3. **数据加密**：敏感数据加密存储
4. **接口鉴权**：API 接口采用 Token 认证机制
5. **防注入处理**：防止 SQL 注入等安全风险
6. **敏感信息保护**：对用户密码等敏感信息进行特殊处理

## 系统要求

- PHP 5.6+
- MySQL 5.5+
- 支持 PDO 或 MySQLi 扩展
- 现代浏览器（Chrome、Firefox、Edge 等）
- 建议服务器配置：2核4G以上，存储空间50GB以上

## 免责声明

本系统仅供学习交流使用，用户在使用过程中需遵守相关法律法规，不得用于违法用途。系统开发者不对用户的行为负责。 
