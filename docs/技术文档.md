# 货源对接系统技术文档

## 📖 文档概述

本文档提供货源对接系统的完整技术说明，包括架构设计、API接口、开发指南和部署说明。

## 🏗️ 系统架构

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    前端管理界面                              │
│              (Vue3 + Element Plus)                         │
└─────────────────────┬───────────────────────────────────────┘
                      │ HTTP/HTTPS
┌─────────────────────▼───────────────────────────────────────┐
│                  API网关层                                  │
│            (Express + 路由中间件)                           │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│                 业务逻辑层                                  │
│  ┌─────────────────┬─────────────────┬─────────────────┐    │
│  │   货源管理      │   接口配置      │   字段映射      │    │
│  │   Controller    │   Controller    │   Engine        │    │
│  └─────────────────┼─────────────────┼─────────────────┘    │
│                    │                 │                      │
│  ┌─────────────────▼─────────────────▼─────────────────┐    │
│  │              ProviderFactory                        │    │
│  │           (货源工厂 - 统一入口)                      │    │
│  └─────────────────┬─────────────────────────────────────┘    │
└────────────────────┼──────────────────────────────────────────┘
                     │
┌────────────────────▼──────────────────────────────────────────┐
│                  服务提供层                                   │
│  ┌──────────────┬──────────────┬──────────────────────────┐   │
│  │ Platform29   │ Universal    │      Future              │   │
│  │ Provider     │ Provider     │      Providers           │   │
│  │ (专用适配器)  │ (通用适配器)  │     (扩展适配器)          │   │
│  └──────────────┴──────────────┴──────────────────────────┘   │
└────────────────────┬──────────────────────────────────────────┘
                     │
┌────────────────────▼──────────────────────────────────────────┐
│                  数据持久层                                   │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │                MySQL 8.4.0                             │  │
│  │  ┌─────────────┬─────────────┬─────────────────────┐   │  │
│  │  │ fd_provider │ fd_provider │ fd_field_mapping    │   │  │
│  │  │             │ _interface  │ _template           │   │  │
│  │  └─────────────┴─────────────┴─────────────────────┘   │  │
│  └─────────────────────────────────────────────────────────┘  │
└───────────────────────────────────────────────────────────────┘
```

### 核心组件说明

#### 1. FieldMappingEngine (字段映射引擎)
- **职责**: 处理标准字段与货源字段的双向映射
- **特性**:
  - 支持数据类型转换
  - 默认值设置
  - 模板变量替换
  - 接口类型相关的字段验证

#### 2. UniversalProvider (通用货源适配器)
- **职责**: 基于配置驱动的货源对接实现
- **特性**:
  - 动态接口配置加载
  - 自定义代码执行
  - 统一的错误处理
  - 性能优化(缓存、连接池)

#### 3. ProviderFactory (货源工厂)
- **职责**: 货源实例的创建和管理
- **特性**:
  - 实例缓存
  - 配置验证
  - 统一的生命周期管理

## 📊 数据库设计

### 核心表结构

#### fd_provider (货源基础信息表)
```sql
CREATE TABLE fd_provider (
  provider_id BIGINT PRIMARY KEY AUTO_INCREMENT,
  code VARCHAR(50) UNIQUE NOT NULL,
  name VARCHAR(100) NOT NULL,
  api_url VARCHAR(255),
  username VARCHAR(100),
  password VARCHAR(100),
  token VARCHAR(255),
  field_mapping JSON,           -- 字段映射配置
  request_config JSON,          -- 请求配置
  response_config JSON,         -- 响应配置
  custom_headers JSON,          -- 自定义请求头
  auth_config JSON,             -- 认证配置
  webhook_config JSON,          -- Webhook配置
  status TINYINT DEFAULT 1,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### fd_provider_interface (货源接口配置表)
```sql
CREATE TABLE fd_provider_interface (
  interface_id INT PRIMARY KEY AUTO_INCREMENT,
  provider_id BIGINT NOT NULL,
  interface_type ENUM('query','order','sync','refill','change_password','get_courses'),
  endpoint_url VARCHAR(500) NOT NULL,
  http_method ENUM('GET','POST','PUT','DELETE') DEFAULT 'POST',
  request_template JSON,        -- 请求模板
  response_mapping JSON,        -- 响应映射
  custom_code TEXT,             -- 自定义处理代码
  is_enabled TINYINT DEFAULT 1,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  FOREIGN KEY (provider_id) REFERENCES fd_provider(provider_id) ON DELETE CASCADE
);
```

#### fd_field_mapping_template (字段映射模板表)
```sql
CREATE TABLE fd_field_mapping_template (
  template_id INT PRIMARY KEY AUTO_INCREMENT,
  template_name VARCHAR(100) NOT NULL,
  template_type VARCHAR(50) NOT NULL,
  standard_fields JSON NOT NULL,
  description TEXT,
  is_system TINYINT DEFAULT 0,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### fd_provider_test_log (货源测试记录表)
```sql
CREATE TABLE fd_provider_test_log (
  log_id INT PRIMARY KEY AUTO_INCREMENT,
  provider_id BIGINT NOT NULL,
  interface_type VARCHAR(50) NOT NULL,
  test_data JSON,
  request_data JSON,
  response_data JSON,
  test_result ENUM('success','failed','error'),
  error_message TEXT,
  response_time INT,
  test_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (provider_id) REFERENCES fd_provider(provider_id) ON DELETE CASCADE
);
```

## 🔌 API接口文档

### 货源管理接口

#### 获取货源列表
```http
GET /api/provider/list
Authorization: Bearer {token}

Query Parameters:
- page: 页码 (默认: 1)
- pageSize: 每页数量 (默认: 20)
- keyword: 搜索关键词
- status: 状态筛选

Response:
{
  "success": true,
  "data": {
    "list": [...],
    "pagination": {
      "page": 1,
      "pageSize": 20,
      "total": 100,
      "totalPages": 5
    }
  }
}
```

#### 创建货源
```http
POST /api/provider/create
Authorization: Bearer {token}
Content-Type: application/json

{
  "code": "new_provider",
  "name": "新货源",
  "api_url": "https://api.example.com",
  "username": "api_user",
  "password": "api_pass",
  "status": 1
}
```

### 接口配置管理

#### 获取货源接口配置
```http
GET /api/provider/{providerId}/interfaces
Authorization: Bearer {token}

Response:
{
  "success": true,
  "data": [
    {
      "interface_id": 1,
      "interface_type": "query",
      "endpoint_url": "https://api.example.com/query",
      "http_method": "POST",
      "request_template": {...},
      "response_mapping": {...},
      "is_enabled": 1
    }
  ]
}
```

#### 保存接口配置
```http
POST /api/provider/{providerId}/interface
Authorization: Bearer {token}
Content-Type: application/json

{
  "interfaceType": "query",
  "endpointUrl": "https://api.example.com/query",
  "httpMethod": "POST",
  "requestTemplate": {
    "uid": "${auth.username}",
    "key": "${auth.password}",
    "user": "${data.username}"
  },
  "responseMapping": {
    "success_field": "code",
    "success_value": 0,
    "message_field": "msg"
  },
  "isEnabled": 1
}
```

#### 测试接口
```http
POST /api/provider/{providerId}/test
Authorization: Bearer {token}
Content-Type: application/json

{
  "interfaceType": "query",
  "testData": {
    "username": "test123",
    "password": "pass123",
    "school": "测试学校"
  }
}

Response:
{
  "success": true,
  "data": {
    "success": true,
    "responseTime": 1250,
    "requestData": {...},
    "rawResponse": {...},
    "mappedResponse": {...}
  }
}
```

## 🛠️ 开发指南

### 添加新货源适配器

#### 1. 创建专用适配器 (可选)
```typescript
// src/service/provider/CustomProvider.ts
export class CustomProvider extends BaseProvider {
  async queryCourses(params: QueryCoursesParams): Promise<QueryCoursesResult> {
    // 实现查课逻辑
  }

  async createOrder(params: CreateOrderParams): Promise<CreateOrderResult> {
    // 实现下单逻辑
  }

  // ... 其他接口实现
}
```

#### 2. 注册到工厂类
```typescript
// src/service/provider/ProviderFactory.ts
case ProviderType.CUSTOM:
  return new CustomProvider(config);
```

#### 3. 使用通用适配器 (推荐)
直接通过数据库配置接口，无需编写代码：

1. 在 `fd_provider` 表中添加货源记录
2. 在 `fd_provider_interface` 表中配置各接口
3. 设置字段映射和响应解析规则

### 字段映射配置示例

```json
{
  "field_mapping": {
    "username": {
      "provider_field": "user",
      "required": true,
      "transform": "toString"
    },
    "password": {
      "provider_field": "pass",
      "required": true,
      "transform": "md5"
    },
    "school": {
      "provider_field": "school_name",
      "default_value": "默认学校"
    }
  }
}
```

### 自定义代码示例

```javascript
// 请求前处理
if (data.password) {
  data.password = utils.crypto.createHash('md5')
    .update(data.password)
    .digest('hex');
}

// 添加时间戳
data.timestamp = Date.now();

// 生成签名
const sign = utils.crypto.createHash('md5')
  .update(data.username + data.timestamp + 'secret_key')
  .digest('hex');
data.sign = sign;

return data;
```

## 🚀 部署指南

### 环境要求
- Node.js 16+
- MySQL 8.0+
- Redis (可选，用于缓存)

### 部署步骤

#### 1. 数据库初始化
```bash
# 执行迁移脚本
mysql -u root -p newfd < server/database/migrations/provider_field_mapping.sql
mysql -u root -p newfd < server/database/migrations/29platform_config.sql
```

#### 2. 后端部署
```bash
pnpm -F server install
pnpm -F server build
pnpm -F server start
```

#### 3. 前端部署
```bash
pnpm install
pnpm build
# 将 dist 目录部署到 Web 服务器
```

#### 4. 环境变量配置
```bash
# server/.env
NODE_ENV=production
PORT=3000
DB_HOST=localhost
DB_PORT=3306
DB_USER=newfd
DB_PASSWORD=jx7KFPLnEsr4fzi7
DB_NAME=newfd
ENCRYPTION_KEY=your_encryption_key_here
```

### 性能优化配置

#### 1. 缓存配置
```javascript
// 启用缓存
const cacheConfig = {
  provider: { ttl: 600000, maxSize: 100 },    // 10分钟
  interface: { ttl: 300000, maxSize: 500 },   // 5分钟
  mapping: { ttl: 900000, maxSize: 200 }      // 15分钟
};
```

#### 2. 连接池配置
```javascript
const poolConfig = {
  maxSockets: 50,
  maxFreeSockets: 10,
  timeout: 30000,
  keepAlive: true,
  retryCount: 3
};
```

## 📈 监控和维护

### 性能监控
- 接口响应时间
- 成功率统计
- 并发连接数
- 缓存命中率

### 日志管理
- 请求日志
- 错误日志
- 性能日志
- 安全审计日志

### 定期维护
- 清理过期日志
- 更新货源配置
- 性能调优
- 安全检查

## 🔧 故障排除

### 常见问题

#### 1. 接口调用失败
- 检查网络连接
- 验证接口配置
- 查看错误日志
- 测试接口连通性

#### 2. 字段映射错误
- 验证映射配置
- 检查数据类型
- 确认必填字段
- 测试映射结果

#### 3. 性能问题
- 检查缓存配置
- 优化数据库查询
- 调整连接池参数
- 监控系统资源

### 调试工具
- 接口测试功能
- 日志查看器
- 性能监控面板
- 错误追踪系统

## 📚 参考资料

- [SoybeanJS 官方文档](https://docs.soybeanjs.cn/)
- [Element Plus 组件库](https://element-plus.org/)
- [Vue3 官方文档](https://vuejs.org/)
- [TypeScript 文档](https://www.typescriptlang.org/)
- [MySQL 8.0 文档](https://dev.mysql.com/doc/)
