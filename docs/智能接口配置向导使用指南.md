# 智能接口配置向导使用指南

## 📋 概述

智能接口配置向导是一个现代化、直观的货源接口配置工具，支持29平台等多种货源的快速配置。通过分步骤的向导流程，您可以轻松配置各种接口类型，无需编写代码。

## 🚀 主要特性

### ✨ 智能化配置
- **分步骤向导**: 4步完成接口配置
- **29平台模板**: 内置29平台配置模板，一键应用
- **实时验证**: 配置过程中实时验证字段和格式
- **智能提示**: 详细的字段说明和使用提示

### 🔧 丰富的接口类型
- **基础接口**: 查课、下单、同步、补刷、改密
- **高级接口**: 获取课程列表、获取分类、获取平台列表
- **管理接口**: 余额查询、暂停订单、恢复订单、取消订单、订单日志
- **批量接口**: 批量查课、批量下单

### 🎨 现代化界面
- **卡片式布局**: 直观的接口类型选择
- **统计面板**: 实时显示配置统计
- **响应式设计**: 适配各种屏幕尺寸
- **Element Plus组件**: 统一的UI风格

## 📖 使用教程

### 第一步：选择接口类型

1. **打开智能配置向导**
   - 进入货源配置页面
   - 选择要配置的货源
   - 点击"智能配置向导"按钮

2. **选择接口分类**
   - **基础接口**: 日常使用的核心功能
   - **高级接口**: 扩展功能和数据获取
   - **管理接口**: 订单管理和控制功能
   - **批量接口**: 批量处理功能

3. **选择具体接口**
   - 点击接口卡片进行选择
   - 查看接口详细说明
   - 确认需要配置的字段

### 第二步：配置基本信息

1. **填写接口地址**
   ```
   示例：https://freedomp.icu/api.php?act=get
   ```

2. **选择请求方法**
   - POST（推荐）
   - GET
   - PUT
   - DELETE

3. **29平台快速配置**
   - 如果检测到29平台模板，点击"应用29平台配置"
   - 自动填充常用配置

### 第三步：配置字段映射

#### 字段映射说明

| 标准字段 | 29平台字段 | 说明 |
|---------|-----------|------|
| username | uid | API用户名 |
| password | key | API密码 |
| student_username | user | 学生用户名 |
| student_password | pass | 学生密码 |
| school | school | 学校名称 |
| platform | platform | 学习平台 |
| course_id | kcid | 课程ID |
| course_name | kcname | 课程名称 |

#### 数据转换选项

- **无转换**: 直接传递原始值
- **转字符串**: 将值转换为字符串
- **转数字**: 将值转换为数字
- **MD5加密**: 对值进行MD5加密
- **Base64编码**: 对值进行Base64编码
- **URL编码**: 对值进行URL编码

### 第四步：测试验证

1. **填写测试数据**
   ```json
   {
     "student_username": "test123",
     "student_password": "password123",
     "school": "测试学校",
     "platform": "network_course",
     "course_id": "course001"
   }
   ```

2. **执行测试**
   - 点击"执行测试"按钮
   - 查看测试结果
   - 检查响应数据

3. **保存配置**
   - 测试成功后点击"保存配置"
   - 配置将自动应用到货源

## 🔍 29平台配置示例

### 查课接口配置

```json
{
  "接口地址": "https://freedomp.icu/api.php?act=get",
  "请求方法": "POST",
  "字段映射": {
    "uid": "${auth.username}",
    "key": "${auth.password}",
    "platform": "${data.platform}",
    "user": "${data.student_username}",
    "pass": "${data.student_password}",
    "school": "${data.school}",
    "kcid": "${data.course_id}"
  },
  "响应映射": {
    "success_field": "code",
    "success_value": 0,
    "message_field": "msg",
    "data_field": "data"
  }
}
```

### 下单接口配置

```json
{
  "接口地址": "https://freedomp.icu/api.php?act=add",
  "请求方法": "POST",
  "字段映射": {
    "uid": "${auth.username}",
    "key": "${auth.password}",
    "platform": "${data.platform}",
    "user": "${data.student_username}",
    "pass": "${data.student_password}",
    "school": "${data.school}",
    "kcname": "${data.course_name}",
    "kcid": "${data.course_id}"
  },
  "响应映射": {
    "success_field": "code",
    "success_value": 0,
    "message_field": "msg",
    "upstream_order_id_field": "id"
  }
}
```

### 同步接口配置

```json
{
  "接口地址": "https://freedomp.icu/api.php?act=status",
  "请求方法": "POST",
  "字段映射": {
    "uid": "${auth.username}",
    "key": "${auth.password}",
    "id": "${data.upstream_order_id}"
  },
  "响应映射": {
    "success_field": "code",
    "success_value": 0,
    "message_field": "msg",
    "status_field": "status",
    "progress_field": "progress"
  }
}
```

## 🛠️ 高级功能

### 自定义代码执行

对于复杂的数据处理需求，可以添加自定义JavaScript代码：

```javascript
// 示例：密码MD5加密
if (data.student_password) {
  data.student_password = utils.crypto.createHash('md5')
    .update(data.student_password).digest('hex');
}

// 示例：添加时间戳
data.timestamp = Date.now();

// 示例：数据格式转换
if (data.course_id) {
  data.course_id = data.course_id.toString();
}

return data;
```

### 批量接口配置

批量接口支持一次处理多个请求：

```json
{
  "batch_data": [
    {
      "student_username": "user1",
      "student_password": "pass1",
      "school": "学校1"
    },
    {
      "student_username": "user2", 
      "student_password": "pass2",
      "school": "学校2"
    }
  ],
  "batch_size": 10
}
```

## 📊 接口统计面板

配置页面顶部显示接口统计信息：

- **基础接口**: 已配置的基础接口数量
- **高级接口**: 已配置的高级接口数量  
- **管理接口**: 已配置的管理接口数量
- **总计**: 所有已配置接口的总数

## 🔧 故障排除

### 常见问题

1. **测试失败**
   - 检查接口地址是否正确
   - 验证字段映射是否完整
   - 确认测试数据格式正确

2. **字段映射错误**
   - 参考29平台字段对照表
   - 检查必填字段是否配置
   - 验证数据转换设置

3. **响应解析失败**
   - 检查响应映射配置
   - 确认成功字段和值设置
   - 查看原始响应数据

### 调试技巧

1. **使用测试功能**
   - 先用简单数据测试
   - 查看详细响应信息
   - 逐步完善配置

2. **查看日志**
   - 检查测试日志记录
   - 分析错误信息
   - 对比成功案例

## 🎯 最佳实践

1. **配置前准备**
   - 了解货源API文档
   - 准备测试账号数据
   - 确认接口访问权限

2. **分步骤配置**
   - 先配置基础接口
   - 逐步添加高级功能
   - 每个接口都要测试

3. **定期维护**
   - 定期测试接口可用性
   - 更新过期的配置
   - 备份重要配置

## 📞 技术支持

如果在使用过程中遇到问题，请：

1. 查看本使用指南
2. 检查系统日志
3. 联系技术支持团队

---

**版本**: v2.0  
**更新时间**: 2024-12-10  
**适用范围**: SoybeanAdmin货源对接系统
