# 生产环境一键部署后端（宝塔/BT 友好）

适用场景：你已将前端打包为 dist/，希望在宝塔服务器上“一键安装并启动后端”。

## 一、前置条件
- Node.js 18+（建议 18 LTS）
- pnpm 8+（推荐通过 corepack 启用）
- 已安装基础编译链（用于 bcrypt 原生模块）
  - Ubuntu/Debian: `apt-get update && apt-get install -y python3 make g++ build-essential`
  - CentOS/RHEL: `yum groupinstall -y "Development Tools" && yum install -y python3`
  - Alpine: `apk add --no-cache python3 make g++`
- MySQL 已可用，并准备好 server/.env（或使用脚本生成的默认 .env 后再手动调整）

## 二、目录结构（建议）
```
/www/wwwroot/FDnew/SoybeanAdmin
├── dist/                    # 前端构建产物（由 pnpm build 生成）
├── server/
│   ├── dist/                # 后端 TS 构建产物（由 tsc 生成）
│   ├── package.json
│   ├── .env                 # 生产环境配置（可由脚本生成模板）
│   └── ...
├── scripts/
│   └── install-backend.sh   # 一键安装脚本
└── ...
```

## 三、一键部署步骤
1. 登录服务器，进入项目根目录（宝塔面板也可在终端执行）
2. 执行脚本：
```bash
bash scripts/install-backend.sh
```
脚本将自动完成：
- 检测并安装（尽力而为）编译工具链
- 启用 corepack 并准备 pnpm
- 安装后端依赖（生产模式）并根据需要编译 TS
- 针对当前系统环境 rebuild bcrypt
- 使用 PM2 启动 server/dist/index.js，并执行 pm2 save

> 注意：不要把本地 node_modules 打包上传；始终在目标服务器上安装依赖与重建 bcrypt。

## 四、Nginx（宝塔站点）配置要点
- 站点根目录指向前端 dist/
- 反向代理 /api 到 http://127.0.0.1:3000
- 启用 gzip 与缓存策略
- SPA 站点 404 回退到 index.html

示例片段：
```
location /api/ {
  proxy_pass http://127.0.0.1:3000/;
  proxy_set_header Host $host;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
}
```

## 五、常见问题与排错
- bcrypt 安装失败（node-gyp/编译链缺失）
  - 按“前置条件”安装 python3/make/g++/build-essential，再执行：
    - 工作区：`pnpm -F server install && pnpm -F server rebuild bcrypt`
    - 独立目录：`cd server && pnpm install && pnpm rebuild bcrypt`
- 权限/EACCES
  - 避免以 root 身份在非 root 目录安装；或修正项目目录权限
- PM2 未自启
  - 确保执行过 `pm2 save`；宝塔环境可使用 PM2 管理器或配置开机自启
- Alpine/musl 与 glibc 二进制不兼容
  - 强制本地编译：`pnpm rebuild bcrypt`（脚本已尝试）

## 六、后续维护建议
- 固定 Node 与 pnpm 版本，锁定依赖
- 使用 PM2 日志与宝塔日志排查问题
- 定期更新系统与依赖，查看 npm 审计报告

