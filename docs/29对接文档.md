原项目为29平台，29pt为29平台的简称
下面以29平台对接地址：https://freedomp.icu 为例展示29平台对接29平台的对接代码

对接uid：5
对接key：YsIYr7lZ75plP8Y5



获取服务商全部课程：https://freedomp.icu/api.php?act=getclass
uid	登录验证	必传
key	登录验证	必传








POST网址:
https://freedomp.icu/api.php?act=get
请求参数	说明
传输类型
uid	登录验证	必传
key	登录验证	必传
platform	平台ID	必传
user	学生账号	必传
pass	学生密码	必传
school	学生学校	必传
29系统对接网址
https://freedomp.icu
对接参数设置如下：
账号：uid 密码：key token：key
29对接标识：
//FD标识  "FD" => "FreeDom", 放在/Checkorder/xdjk.php 文件
29对接代码：
//FD平台查课接口 复制代码放在/Checkorder/ckjk.php 文件

                        else if ($type == "FD")
                        {
                            $data = array("uid" => $a["user"], "key" => $a["pass"], "school" => $school, "user" => $user, "pass" => $pass, "platform" => $noun, "kcid" => $kcid);
                            $dx_rl = $a["url"];
                            $dx_url = "$dx_rl/api.php?act=get";
                            $result = get_url($dx_url, $data);
                            $result = json_decode($result, true);
                            return $result;
                        }









POST:
https://freedomp.icu/api.php?act=add
请求参数	说明
传输类型
uid	登录验证	必传
key	登录验证	必传
platform	平台ID	必传
user	学生账号	必传
pass	账号密码	必传
kcname	课程名字	必传
kcid	课程ID	必传
29系统对接网址
https://freedomp.icu
对接参数设置如下：
账号：uid 密码：key token：key
29对接标识：
//FD标识  "FD" => "FreeDom", 放在/Checkorder/xdjk.php 文件
29对接代码：
//FD平台下单接口 复制代码放在/Checkorder/xdjk.php 文件

                        else if ($type == "FD")
                        {
                            $data = array("uid" => $a["user"], "key" => $a["pass"], "platform" => $noun, "school" => $school, "user" => $user, "pass" => $pass, "kcname" => $kcname, "kcid" => $kcid);
                            $dx_rl = $a["url"];
                            $dx_url = "$dx_rl/api.php?act=add";
                            $result = get_url($dx_url, $data);
                            $result = json_decode($result, true);
                            if ($result["code"] == "0") {
                                $b = array("code" => 1, "msg" => "下单成功",'yid'=>$result['id']);
                            } else {
                                $b = array("code" => - 1, "msg" => $result["msg"]);
                        }
                        return $b;
                        }





POST:
https://freedomp.icu/api.php?act=budan
请求参数	说明
传输类型
uid	登录验证	必传
key	登录验证	必传
id	订单账号	必传
29系统对接网址
https://freedomp.icu
对接参数设置如下：
账号：uid 密码：key token：key
29对接标识：
//FD标识  "FD" => "FreeDom", 放在/Checkorder/xdjk.php 文件
29对接代码：
//FD平台补刷接口 复制代码放在/Checkorder/bsjk.php 文件

                        elseif ($type == "FD")
                        {
                            $data = array("uid" => $a["user"], "key" => $a["pass"], "id" => $yid);
                            $dx_rl = $a["url"];
                            $dx_url = "$dx_rl/api.php?act=budan";
                            $result = get_url($dx_url, $data);
                            $result = json_decode($result, true);
                            return $result;
                        }













POST:（下面使用的是实时进度接口chadanoid，如遇到问题可以更换为默认的进度接口chadanmr，记到传yid，两个接口均不会串进度！）
https://freedomp.icu/api.php?act=chadanoid
请求参数	说明
传输类型
uid	登录验证	必传
key	登录验证	必传
yid	订单id	可选
username	订单账号	可选
school	订单学校	可选
29系统对接网址
https://freedomp.icu
对接参数设置如下：
账号：uid 密码：key token：key
29对接标识：
//FD标识  "FD" => "FreeDom", 放在/Checkorder/xdjk.php 文件
29进度对接代码（支持yid和账号查询）：
//FD平台进度接口 复制代码放在/Checkorder/jdjk.php 文件

        else if ($type == "FD") {
            $data = array("username" => $user,"yid"=>$d["yid"],"uid" => $a["user"],"key" => $a["pass"]);//$d["yid"]为订单yid，如果没有自行在jdjk.php文件顶部添加该参数！
            $dx_rl = $a["url"];
            $dx_url  = "$dx_rl/api.php?act=chadanoid";
            $result = get_url($dx_url, $data);
            $result = json_decode($result, true);
            if ($result["code"] == "1") {
                foreach ($result["data"] as $res) {
                $yid = $res["id"];
                $kcname = $res["kcname"];
                $status = $res["status"];
                $process = $res["process"];
                $remarks = $res["remarks"];
                $kcks = $res["courseStartTime"];
                $kcjs = $res["courseEndTime"];
                $ksks = $res["examStartTime"];
                $ksjs = $res["examEndTime"];
                $b[] = array("code" => 1, "msg" => "查询成功", "yid" => $yid, "kcname" => $kcname, "user" => $user, "pass" => $pass, "ksks" => $ksks, "ksjs" => $ksjs, "status_text" => $status, "process" => $process, "remarks" => $remarks);
                }
            } else {
                $b[] = array("code" => -1, "msg" => $result);
            }
            return $b;
        }






POST:
https://freedomp.icu/api.php?act=xgmm
请求参数	说明
传输类型
uid	登录验证	必传
key	登录验证	必传
yid	订单id	必传
pwd	新密码	必传
29系统对接网址
https://freedomp.icu
对接参数设置如下：
账号：uid 密码：key token：key
29对接标识：
//FD标识  "FD" => "FreeDom", 放在/Checkorder/gmjk.php 文件
29改密对接代码：
//FD平台改密接口 复制代码放在/Checkorder/gmjk.php 文件

        elseif ($type == "FD") {
            $data = array("uid" => $a["user"], "key" => $a["pass"], "oid" => $yid,'pwd' =>$newpass);
            $dx_rl = $a["url"];
            $dx_url = "$dx_rl/api.php?act=xgmm";
            $result = get_url($dx_url, $data);
            $result = json_decode($result, true);
            return $result;
            }








