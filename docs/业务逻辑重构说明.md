# 项目业务逻辑重构说明

## 📋 重构概述

本次重构将项目的核心业务逻辑从"课程管理"改为"商品管理"，实现了正确的下单流程：**选择商品 → 输入账号 → 查课验证 → 选择课程 → 确认下单**。

## 🎯 重构目标

### 原有问题
- 业务逻辑错误：用户直接选择课程，然后选择项目分类
- 没有查课验证环节
- 课程是预设的，不是动态获取的
- 下单流程不符合实际业务需求

### 重构后的正确逻辑
1. **商品选择**：用户选择服务商提供的网课代刷服务（商品）
2. **账号验证**：输入学习平台的账号密码
3. **查课获取**：通过服务商接口查询账号中的实际课程
4. **课程选择**：从查课结果中选择需要代刷的课程
5. **订单确认**：确认商品、课程和费用信息后下单

## 🗄️ 数据库重构

### 表结构变更

#### 1. 课程表 → 商品表
```sql
-- 重命名表
RENAME TABLE fd_course TO fd_product;

-- 更新字段
ALTER TABLE fd_product
CHANGE COLUMN course_id product_id INT AUTO_INCREMENT,
CHANGE COLUMN course_name product_name VARCHAR(255) NOT NULL,
ADD COLUMN service_type VARCHAR(100) DEFAULT '代刷服务';
```

#### 2. 订单表更新
```sql
-- 添加新字段
ALTER TABLE fd_order
ADD COLUMN product_id INT COMMENT '商品ID',
ADD COLUMN selected_courses JSON COMMENT '用户选择的课程信息';

-- 添加新的订单状态
-- PARTIAL_SUCCESS = 7 (部分成功)
```

### 核心概念重新定义

| 概念 | 原定义 | 新定义 |
|------|--------|--------|
| **商品** | 不存在 | 服务商提供的网课代刷服务项目 |
| **课程** | 预设的课程列表 | 通过查课动态获取的用户账号中的课程 |
| **订单** | 基于预设课程 | 基于商品和用户选中的实际课程 |

## 🔧 后端架构重构

### 新增模块

#### 1. 商品管理模块 (`src/controller/product.ts`)
- `getProductList()` - 获取商品列表
- `getProductDetail()` - 获取商品详情
- `createProduct()` - 创建商品（管理员）
- `updateProduct()` - 更新商品（管理员）
- `deleteProduct()` - 删除商品（管理员）
- `getProductCategories()` - 获取商品分类

#### 2. 查课逻辑重构 (`src/controller/query.ts`)
```typescript
// 原逻辑：基于服务商ID查课
queryCourses(providerId, account)

// 新逻辑：基于商品ID查课
queryCourses(productId, account) → 获取商品信息 → 调用服务商查课
```

#### 3. 订单逻辑重构 (`src/controller/order.ts`)
```typescript
// 原逻辑：基于课程ID下单
createOrder(courseId, account, courseName)

// 新逻辑：基于商品和选中课程下单
createOrder(productId, account, selectedCourses[])
```

### API接口变更

#### 商品管理接口
```
GET    /api/product/list           - 获取商品列表
GET    /api/product/:id            - 获取商品详情
GET    /api/product/categories/list - 获取商品分类
POST   /api/product                - 创建商品（管理员）
PUT    /api/product/:id            - 更新商品（管理员）
DELETE /api/product/:id            - 删除商品（管理员）
```

#### 查课接口更新
```typescript
// 新的查课接口
POST /api/query/courses
{
  "productId": 1,
  "school": "某某大学",
  "username": "学号",
  "password": "密码"
}

// 返回格式
{
  "product": { "productId": 1, "productName": "智慧树代刷", ... },
  "account": { "username": "学号", "school": "某某大学" },
  "courses": [
    { "courseId": "123", "courseName": "高等数学", "progress": 30 }
  ],
  "queryTime": "2025-07-09T10:00:00Z"
}
```

#### 订单接口更新
```typescript
// 新的下单接口
POST /api/order/create
{
  "productId": 1,
  "platformAccount": "学号",
  "platformPassword": "密码",
  "schoolName": "某某大学",
  "selectedCourses": [
    { "courseId": "123", "courseName": "高等数学", "progress": 30 }
  ],
  "remark": "备注"
}
```

## 🎨 前端界面重构

### 新的下单流程页面

#### 1. 步骤式下单界面 (`src/views/order/create/index.vue`)
- **步骤1**：商品选择 - 分类筛选、商品展示
- **步骤2**：账号信息 - 学校、账号、密码输入
- **步骤3**：查课验证 - 实时查课、结果展示
- **步骤4**：课程选择 - 多选课程、批量操作
- **步骤5**：订单确认 - 信息核对、费用计算

#### 2. 组件化设计
```
src/views/order/create/components/
├── ProductSelector.vue    - 商品选择组件
├── AccountForm.vue        - 账号表单组件
├── QuerySection.vue       - 查课验证组件
├── CourseSelector.vue     - 课程选择组件
└── OrderConfirm.vue       - 订单确认组件
```

#### 3. 订单管理页面更新
- 显示商品信息而非课程信息
- 新增"查看课程"功能，显示订单中的具体课程
- 新增"补刷"功能，支持失败订单重新处理
- 优化表格布局，适配新的数据结构

### 前端API接口更新

#### 1. 商品API (`src/service/api/product.ts`)
```typescript
export interface Product {
  product_id: number;
  product_name: string;
  platform_type: string;
  service_type: string;
  price: number;
  // ...
}

export function fetchProductList(params: ProductListParams)
export function fetchProductDetail(productId: number)
export function fetchProductCategories()
```

#### 2. 查课API (`src/service/api/query.ts`)
```typescript
export interface QueryCoursesParams {
  productId: number;
  school: string;
  username: string;
  password: string;
}

export function queryCourses(data: QueryCoursesParams)
```

#### 3. 订单API更新 (`src/service/api/order.ts`)
```typescript
export interface CreateOrderParams {
  productId: number;
  platformAccount: string;
  platformPassword: string;
  schoolName: string;
  selectedCourses: Course[];
  remark?: string;
}
```

## 📊 业务流程对比

### 原流程（错误）
```
用户登录 → 选择课程 → 选择分类 → 输入账号 → 直接下单
```

### 新流程（正确）
```
用户登录 → 选择分类 → 选择商品 → 输入账号 → 查课验证 → 选择课程 → 确认下单 → 支付 → 执行
```

## 🔍 关键改进点

### 1. 业务逻辑正确性
- ✅ 商品是服务，课程是查课结果
- ✅ 查课验证确保账号有效性
- ✅ 动态获取课程，不依赖预设数据

### 2. 用户体验优化
- ✅ 步骤式引导，流程清晰
- ✅ 实时查课验证，减少失败率
- ✅ 批量课程选择，提高效率

### 3. 数据结构优化
- ✅ 商品表存储服务信息
- ✅ 订单表记录选中课程详情
- ✅ 支持部分成功状态

### 4. 系统扩展性
- ✅ 模块化组件设计
- ✅ 统一的API接口规范
- ✅ 支持多平台服务商对接

## 🚀 部署和测试

### 数据库迁移
1. 执行数据库备份
2. 运行迁移脚本
3. 验证数据完整性

### 功能测试
1. 商品管理功能测试
2. 新下单流程端到端测试
3. 订单管理功能验证

### 性能优化
1. 查课接口响应时间优化
2. 前端组件加载优化
3. 数据库查询性能优化

## 📝 后续计划

### 短期目标
- [ ] 完善错误处理和用户提示
- [ ] 添加更多平台支持
- [ ] 优化移动端体验

### 长期目标
- [ ] 智能推荐商品功能
- [ ] 批量账号管理
- [ ] 数据分析和报表

## 🔧 开发指南

### 添加新商品
1. 管理员登录后台
2. 进入商品管理页面
3. 点击"添加商品"
4. 填写商品信息：名称、分类、平台类型、价格等
5. 配置服务商对接信息
6. 保存并启用

### 添加新平台支持
1. 在 `server/src/service/provider/` 下创建新的平台适配器
2. 实现 `ProviderInterface` 接口
3. 在 `ProviderManager` 中注册新平台
4. 更新前端平台类型选项
5. 测试查课和下单功能

### 自定义下单流程
1. 修改 `src/views/order/create/index.vue` 中的步骤配置
2. 添加或修改步骤组件
3. 更新步骤验证逻辑
4. 测试完整流程

---

**重构完成时间**：2025年7月9日
**重构负责人**：开发团队
**文档版本**：v1.0
