# 查课功能测试指南

## 测试环境准备

1. 确保前后端服务正常运行
2. 确保数据库中有测试商品数据
3. 准备测试账号信息

## 功能测试步骤

### 1. 单个账号查课测试

#### 测试步骤：
1. 访问下单页面：`http://localhost:5959/order/create`
2. 选择商品分类和具体商品
3. 输入单个账号信息：
   - 学校名称：测试学校
   - 平台账号：test001
   - 平台密码：password123
4. 点击"查询课程"按钮

#### 预期结果：
- 显示该账号的课程列表
- 每门课程显示：课程名称、教师、班级、进度、状态、学分等信息
- 可以勾选/取消勾选课程
- 显示统计信息：找到X门课程，已选择Y门

### 2. 批量账号查课测试

#### 测试步骤：
1. 切换到"批量查询"模式
2. 在文本框中输入多个账号信息（每行一个）：
   ```
   测试学校1 test001 password123
   测试学校2 test002 password456
   自动识别 test003 password789
   ```
3. 点击"批量查询"按钮

#### 预期结果：
- 显示按账号分组的课程列表
- 每个账号显示为一个分组，包含：
  - 账号信息（用户名、真实姓名、学校）
  - 该账号的课程数量
  - 选择状态指示器
- 可以按账号批量选择/取消选择

### 3. 搜索功能测试

#### 测试步骤：
1. 在有课程数据的情况下，在搜索框输入关键词
2. 测试不同类型的搜索：
   - 课程名称关键词：如"数学"
   - 课程ID：如"MATH001"
   - 教师姓名：如"张老师"

#### 预期结果：
- 实时筛选显示匹配的课程
- 显示搜索结果数量
- 出现"一键选择"按钮
- 点击"一键选择"可选择所有搜索结果

### 4. 账号筛选测试（批量模式）

#### 测试步骤：
1. 在批量查课结果中，点击不同的账号标签
2. 测试筛选功能：
   - 点击账号标签筛选该账号的课程
   - 点击"清除筛选"显示所有课程

#### 预期结果：
- 点击账号标签后只显示该账号的课程
- 标签状态正确切换（高亮/普通）
- 统计信息正确更新
- 清除筛选后恢复显示所有课程

### 5. 批量选择测试

#### 测试步骤：
1. 测试账号级别的批量选择：
   - 点击账号标题的复选框
   - 验证该账号所有课程的选择状态
2. 测试全选功能：
   - 点击"全选"按钮
   - 验证当前筛选结果中所有课程被选中
3. 测试清空功能：
   - 点击"清空"按钮
   - 验证所有选择被清除

#### 预期结果：
- 账号复选框状态正确：空白/减号/勾选
- 全选/清空功能正常工作
- 统计信息实时更新

### 6. 相同课程名称处理测试

#### 测试步骤：
1. 准备多个账号，确保有相同名称的课程
2. 进行批量查课
3. 搜索相同的课程名称
4. 使用"一键选择"功能

#### 预期结果：
- 相同课程名称在不同账号下被正确区分
- 搜索能找到所有匹配的课程（不同账号）
- 一键选择能选择所有匹配的课程
- 每门课程都有唯一标识（账号+课程ID）

### 7. 界面响应式测试

#### 测试步骤：
1. 在不同屏幕尺寸下测试界面
2. 测试移动端兼容性
3. 测试大量数据的性能

#### 预期结果：
- 界面在不同尺寸下正常显示
- 移动端操作流畅
- 大量课程数据时滚动性能良好

## 错误场景测试

### 1. 网络错误测试
- 断网情况下的错误提示
- 服务器错误的处理

### 2. 数据异常测试
- 空数据的处理
- 格式错误数据的处理
- 超大数据量的处理

### 3. 用户操作异常测试
- 快速连续点击的处理
- 无效输入的验证
- 权限不足的处理

## 性能测试

### 1. 大数据量测试
- 测试1000+课程的显示性能
- 测试100+账号的批量查课
- 测试复杂搜索的响应时间

### 2. 内存使用测试
- 长时间使用的内存泄漏检查
- 大量数据切换的内存管理

## 兼容性测试

### 1. 浏览器兼容性
- Chrome、Firefox、Safari、Edge
- 不同版本的兼容性

### 2. 设备兼容性
- 桌面端、平板、手机
- 不同分辨率的适配

## 测试数据准备

### 商品数据
```sql
-- 插入测试商品分类
INSERT INTO fd_category (name, status) VALUES ('测试分类', 1);

-- 插入测试商品
INSERT INTO fd_product (name, price, fenlei, queryplat, getnoun, noun, status) 
VALUES ('测试课程商品', 10.00, '测试分类', 1, 'test_platform', 'test_noun', 1);
```

### 测试账号
```
测试学校1 test001 password123
测试学校2 test002 password456
自动识别 test003 password789
测试学校1 test004 password000
```

## 预期问题和解决方案

### 1. 课程数据格式不一致
- 检查后端API返回的数据格式
- 确保前端正确解析嵌套结构

### 2. 搜索性能问题
- 大量数据时考虑防抖优化
- 实现虚拟滚动（如需要）

### 3. 状态同步问题
- 确保选择状态在各个组件间正确同步
- 验证计算属性的依赖关系

## 测试完成标准

- [ ] 所有基础功能正常工作
- [ ] 搜索和筛选功能准确
- [ ] 批量操作功能完整
- [ ] 错误处理机制完善
- [ ] 性能满足要求
- [ ] 界面响应式良好
- [ ] 兼容性测试通过

## 问题反馈

测试过程中发现的问题请记录：
1. 问题描述
2. 复现步骤
3. 预期结果
4. 实际结果
5. 浏览器和设备信息
