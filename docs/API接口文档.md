# API接口文档

## 📋 概述

本文档描述了重构后的API接口，主要包括商品管理、查课验证、订单管理等核心功能。

## 🔐 认证说明

所有API接口都需要在请求头中携带JWT Token：
```
Authorization: Bearer <token>
```

## 📦 商品管理接口

### 1. 获取商品列表
```
GET /api/product/list
```

**请求参数：**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | number | 否 | 页码，默认1 |
| pageSize | number | 否 | 每页数量，默认20 |
| categoryId | number | 否 | 分类ID |
| platformType | string | 否 | 平台类型 |
| status | number | 否 | 状态：1启用，0禁用 |
| keyword | string | 否 | 搜索关键词 |

**响应示例：**
```json
{
  "code": "200",
  "msg": "获取商品列表成功",
  "data": {
    "list": [
      {
        "product_id": 1,
        "product_name": "智慧树代刷服务",
        "category_id": 1,
        "category_name": "在线学习",
        "platform_type": "zhihuishu",
        "provider_id": 1,
        "provider_name": "29平台",
        "price": 15.00,
        "cost_price": 10.00,
        "service_type": "代刷服务",
        "description": "专业智慧树网课代刷服务",
        "status": 1,
        "sort_order": 10,
        "create_time": "2025-07-09T10:00:00Z",
        "update_time": "2025-07-09T10:00:00Z"
      }
    ],
    "total": 10,
    "page": 1,
    "pageSize": 20,
    "totalPages": 1
  }
}
```

### 2. 获取商品详情
```
GET /api/product/:productId
```

**响应示例：**
```json
{
  "code": "200",
  "msg": "获取商品详情成功",
  "data": {
    "product_id": 1,
    "product_name": "智慧树代刷服务",
    "category_name": "在线学习",
    "provider_name": "29平台",
    "provider_api_url": "https://api.example.com",
    // ... 其他字段
  }
}
```

### 3. 获取商品分类列表
```
GET /api/product/categories/list
```

**响应示例：**
```json
{
  "code": "200",
  "msg": "获取商品分类列表成功",
  "data": [
    {
      "category_id": 1,
      "name": "在线学习服务",
      "sort_order": 1,
      "status": 1,
      "product_count": 5
    }
  ]
}
```

## 🔍 查课验证接口

### 1. 基于商品的查课
```
POST /api/query/courses
```

**请求参数：**
```json
{
  "productId": 1,
  "school": "某某大学",
  "username": "学号或用户名",
  "password": "密码"
}
```

**响应示例：**
```json
{
  "code": "200",
  "msg": "查课成功",
  "data": {
    "product": {
      "productId": 1,
      "productName": "智慧树代刷服务",
      "platformType": "zhihuishu",
      "providerName": "29平台"
    },
    "account": {
      "username": "*********",
      "school": "某某大学"
    },
    "courses": [
      {
        "courseId": "123456",
        "courseName": "高等数学",
        "status": "进行中",
        "progress": 30,
        "teacher": "张教授",
        "credit": 4
      },
      {
        "courseId": "123457",
        "courseName": "大学英语",
        "status": "未开始",
        "progress": 0,
        "teacher": "李教授",
        "credit": 3
      }
    ],
    "queryTime": "2025-07-09T10:30:00Z"
  }
}
```

### 2. 批量查课
```
POST /api/query/batch-courses
```

**请求参数：**
```json
{
  "productId": 1,
  "accountsText": "某某大学 ********* password123\n某某大学 ********* password456"
}
```

## 📋 订单管理接口

### 1. 创建订单
```
POST /api/order/create
```

**请求参数：**
```json
{
  "productId": 1,
  "platformAccount": "*********",
  "platformPassword": "password123",
  "schoolName": "某某大学",
  "selectedCourses": [
    {
      "courseId": "123456",
      "courseName": "高等数学",
      "status": "进行中",
      "progress": 30
    },
    {
      "courseId": "123457",
      "courseName": "大学英语",
      "status": "未开始",
      "progress": 0
    }
  ],
  "remark": "请尽快完成"
}
```

**响应示例：**
```json
{
  "code": "200",
  "msg": "订单处理完成",
  "data": {
    "orderId": 1001,
    "orderNo": "ORD20250709001",
    "amount": 30.00,
    "courseCount": 2,
    "successCount": 2,
    "failedCount": 0,
    "submitResults": [
      {
        "courseId": "123456",
        "courseName": "高等数学",
        "success": true,
        "upstreamOrderId": "UP123456",
        "message": "提交成功"
      },
      {
        "courseId": "123457",
        "courseName": "大学英语",
        "success": true,
        "upstreamOrderId": "UP123457",
        "message": "提交成功"
      }
    ],
    "message": "订单创建成功，所有课程已提交"
  }
}
```

### 2. 获取订单列表
```
GET /api/order/list
```

**请求参数：**
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | number | 否 | 页码 |
| pageSize | number | 否 | 每页数量 |
| status | number | 否 | 订单状态 |
| productId | number | 否 | 商品ID |
| keyword | string | 否 | 搜索关键词 |

**响应示例：**
```json
{
  "code": "200",
  "msg": "获取订单列表成功",
  "data": {
    "list": [
      {
        "order_id": 1001,
        "order_no": "ORD20250709001",
        "username": "testuser",
        "product_name": "智慧树代刷服务",
        "platform_type": "zhihuishu",
        "platform_account": "*********",
        "school_name": "某某大学",
        "selected_courses": {
          "courses": [
            {
              "courseId": "123456",
              "courseName": "高等数学"
            }
          ],
          "totalCount": 1
        },
        "quantity": 1,
        "amount": 15.00,
        "status": 3,
        "status_text": "处理中",
        "progress": 50,
        "create_time": "2025-07-09T10:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 20
  }
}
```

## 📊 订单状态说明

| 状态码 | 状态名称 | 说明 |
|--------|----------|------|
| 1 | 待支付 | 订单已创建，等待支付 |
| 2 | 待处理 | 已支付，等待处理 |
| 3 | 处理中 | 正在执行代刷服务 |
| 4 | 已完成 | 服务执行完成 |
| 5 | 已取消 | 订单已取消 |
| 6 | 处理失败 | 服务执行失败 |
| 7 | 部分成功 | 部分课程处理成功 |

## ❌ 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
| 9999 | 业务逻辑错误 |

## 🔧 开发说明

### 请求格式
- Content-Type: application/json
- 所有POST请求都使用JSON格式

### 响应格式
```json
{
  "code": "状态码",
  "msg": "响应消息",
  "data": "响应数据"
}
```

### 分页格式
```json
{
  "list": [],
  "total": 100,
  "page": 1,
  "pageSize": 20,
  "totalPages": 5
}
```

---

**文档版本**：v1.0  
**更新时间**：2025年7月9日
