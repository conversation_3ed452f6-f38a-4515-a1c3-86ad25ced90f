# 同步配置详细说明

## 📋 配置概览

通过同步管理页面可以统一管理自动同步的所有配置。修改配置后点击"保存配置"，系统将自动应用新设置。

### 🔧 基础配置

#### **启用自动同步**
- **作用**: 控制整个自动同步功能的开启/关闭
- **默认值**: 关闭
- **说明**: 关闭后所有自动同步功能将停止

#### **服务器启动时自动开启**
- **作用**: 服务器重启后是否自动启动同步
- **默认值**: 关闭
- **说明**: 开启后服务器重启时会自动恢复同步状态

#### **最大并发数**
- **范围**: 1-100
- **默认值**: 10
- **说明**: 同时处理的最大订单数量，过高可能导致系统负载过大

#### **批处理大小**
- **范围**: 1-1000
- **默认值**: 50
- **说明**: 每次批量处理的订单数量，影响处理效率和内存使用

#### **启用速率限制**
- **作用**: 控制每秒最大请求数，避免过于频繁的API调用
- **默认值**: 启用
- **说明**: 防止对上游服务造成压力，建议保持开启

#### **速率限制(/秒)**
- **范围**: 1-100
- **默认值**: 5
- **说明**: 每秒最大API请求数，需要根据上游服务能力调整
- **建议**: 不超过并发数的2倍

#### **最大重试次数**
- **范围**: 0-10
- **默认值**: 3
- **说明**: 请求失败时的最大重试次数

#### **重试延迟(秒)**
- **范围**: 1-300
- **默认值**: 60
- **说明**: 重试前的等待时间，避免频繁重试

### 📊 订单状态同步策略

只同步已提交的订单（有upstream_order_id）

#### **配置说明**
- **状态值**: 订单在系统中的状态编号
- **状态名称**: 状态的描述性名称
- **是否同步**: 控制该状态是否参与自动同步
- **优先级**: 数字越大优先级越高，优先处理
- **同步间隔**: 该状态订单的同步频率（分钟）

#### **默认状态配置**
| 状态值 | 状态名称 | 优先级 | 同步间隔 | 说明 |
|--------|----------|--------|----------|------|
| 2 | 待考试 | 5 | 120分钟 | 已提交待考试，同步间隔较长 |
| 3 | 进行中 | 9 | 5分钟 | 考试进行中，需要频繁同步状态 |
| 6 | 处理失败 | 8 | 15分钟 | 处理失败需要频繁重试 |

### 🎯 优先级规则

基于商品总订单量设置处理优先级

#### **规则说明**
- **热门商品**: 订单量≥50，优先级10
- **普通商品**: 订单量10-50，优先级7
- **新商品**: 订单量1-10，优先级5

### ⏰ 时间限制

#### **最大订单年龄(小时)**
- **范围**: 1-720
- **默认值**: 168 (7天)
- **说明**: 只同步指定时间内的订单，过期订单不再同步

#### **最小更新间隔(分钟)**
- **范围**: 1-60
- **默认值**: 5
- **说明**: 避免过于频繁的更新，保护系统性能

## ⚠️ 配置注意事项

### **性能相关**
1. **并发数与批处理大小**: 批处理大小不建议超过并发数的50倍
2. **速率限制**: 建议不超过并发数的2倍，避免请求积压
3. **同步间隔**: 高优先级状态间隔不宜过长，建议30分钟以内

### **稳定性相关**
1. **重试配置**: 重试次数过多可能导致长时间阻塞
2. **时间限制**: 订单年龄过长会增加同步负担
3. **状态配置**: 至少保留一个启用的状态配置

### **兼容性相关**
1. **上游限制**: 速率限制需要根据上游服务能力调整
2. **系统资源**: 并发数过高需要确保系统资源充足
3. **网络环境**: 重试延迟需要考虑网络稳定性

## 🔍 配置检查功能

点击"检查配置"按钮可以：
- 检查配置一致性
- 发现潜在问题
- 获得优化建议
- 识别配置冲突

## 📈 配置优化建议

### **高并发场景**
- 适当提高并发数和批处理大小
- 启用速率限制防止过载
- 缩短高优先级状态的同步间隔

### **稳定性优先**
- 降低并发数和速率限制
- 增加重试延迟时间
- 延长同步间隔

### **资源受限环境**
- 减小批处理大小
- 降低并发数
- 适当延长同步间隔

## 🚨 常见问题

### **配置无法保存**
1. 检查数值是否在有效范围内
2. 确认速率限制配置正确
3. 查看浏览器控制台错误信息

### **同步效果不佳**
1. 检查同步间隔设置
2. 确认优先级配置合理
3. 验证状态配置是否启用

### **系统性能问题**
1. 降低并发数和批处理大小
2. 启用并调整速率限制
3. 增加重试延迟时间

---

💡 **提示**: 配置修改后会自动重启调度器以应用新设置，无需手动重启服务。
