# 抽屉式查课功能说明

## 功能概述

本次优化将查课结果显示改为抽屉式设计，提供更好的用户体验和更高效的课程选择方式。查课成功后自动弹出抽屉，以账号为唯一标识进行分组。

## 主要改进

### 1. 抽屉式界面设计
- **主界面简化**：主界面只显示查课统计和已选课程预览
- **自动弹出**：查课成功后自动打开抽屉，无需手动点击
- **抽屉详情**：显示完整的课程列表和操作功能
- **空间优化**：抽屉占用60%屏幕宽度，提供充足的操作空间

### 2. 账号分组与折叠
- **按账号分组**：批量查课时课程按账号分组显示
- **账号标识**：以账号名为分组标题，账号是区分每个分组的唯一标签
- **可折叠设计**：每个账号的课程列表可以折叠/展开
- **默认展开**：初始状态所有账号都展开显示
- **快速导航**：点击账号标题可快速折叠/展开

### 3. 同名课程联动功能
- **智能开关**：批量模式下提供"同名课程联动"开关
- **自动勾选**：开启后，勾选一个课程会自动勾选其他账号的同名课程
- **自动取消**：取消勾选时也会自动取消其他账号的同名课程
- **跨账号操作**：解决多账号下单相同课程的需求

### 4. 小行设计优化
- **紧凑布局**：课程行高度优化，显示更多内容
- **信息精简**：只显示关键信息（课程名、教师、班级、状态）
- **标签优化**：使用小尺寸标签显示进度、状态等信息

### 5. 搜索和筛选增强
- **模糊搜索**：支持课程名称、ID、教师姓名、班级的模糊搜索
- **实时筛选**：输入关键词后实时筛选显示结果
- **账号筛选**：可按账号筛选显示特定账号的课程
- **一键选择**：搜索后可一键选择所有搜索结果
- **以账号分组**：筛选功能以账号为单位进行分组筛选

## 界面结构

### 主界面
```
┌─────────────────────────────────────┐
│ 课程列表                    [查看课程] │
├─────────────────────────────────────┤
│ 找到 X 门课程，已选择 Y 门            │
│ 涉及 Z 个账号                       │
│                                     │
│ 已选择的课程：                       │
│ ┌─────────────────────────────────┐ │
│ │ 课程1 [移除]                    │ │
│ │ 课程2 [移除]                    │ │
│ │ ...                             │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

### 抽屉界面
```
┌─────────────────────────────────────┐
│ 课程选择    [同名课程联动] 已选择X/Y   │
├─────────────────────────────────────┤
│ [搜索框] [一键选择]                  │
│ [user001] [user002] [user003] [清除] │
├─────────────────────────────────────┤
│ ▼ user001 ☑ 已选2/5                │
│   张三 | 北京大学 | 5门课程           │
│   ☑ 高等数学                        │
│   ☐ 线性代数                        │
│   ☑ 概率论                          │
│                                     │
│ ▼ user002 ☐ 已选0/3                │
│   李四 | 清华大学 | 3门课程           │
│   ☐ 高等数学                        │
│   ☐ 大学物理                        │
│   ☐ 程序设计                        │
├─────────────────────────────────────┤
│ 已选择 X 门课程        [关闭][确认]   │
└─────────────────────────────────────┘
```

## 使用方法

### 基本操作
1. **自动打开**：查课成功后抽屉自动弹出
2. **选择课程**：在抽屉中勾选需要的课程
3. **确认选择**：点击"确认选择"关闭抽屉
4. **手动打开**：也可点击"查看课程"按钮手动打开抽屉

### 同名课程联动
1. **开启联动**：在抽屉顶部开启"同名课程联动"开关
2. **自动选择**：勾选任意账号的课程，其他账号的同名课程自动勾选
3. **批量操作**：适用于多个账号需要学习相同课程的场景

### 账号管理
1. **折叠展开**：点击账号标题的箭头图标
2. **批量选择**：点击账号标题的复选框选择该账号所有课程
3. **状态查看**：查看每个账号的选择状态（全选/部分/未选）

### 搜索筛选
1. **模糊搜索**：在搜索框输入课程名称、ID、教师姓名或班级
2. **实时筛选**：输入关键词后立即筛选显示匹配结果
3. **账号筛选**：点击账号标签（如user001）只显示该账号的课程
4. **一键选择**：搜索后点击"一键选择"选择所有搜索结果
5. **分组筛选**：筛选以账号为单位进行，保持分组结构

## 技术特性

### 性能优化
- **虚拟滚动**：大量课程时保持流畅滚动
- **懒加载**：按需加载课程详情
- **缓存机制**：搜索结果缓存提升响应速度

### 响应式设计
- **移动端适配**：抽屉在移动端占用全屏宽度
- **触摸优化**：支持触摸操作和手势
- **自适应布局**：根据屏幕尺寸调整显示

### 数据处理
- **智能去重**：相同课程在不同账号下正确区分
- **状态同步**：选择状态在各组件间实时同步
- **数据验证**：确保选择的课程数据完整性

## 优势对比

### 旧版本问题
- 界面拥挤，课程信息显示不全
- 无法快速定位特定账号的课程
- 相同课程需要逐个勾选，效率低
- 大量课程时滚动性能差

### 新版本优势
- 抽屉设计提供更大操作空间
- 账号分组和折叠便于管理
- 同名课程联动提升操作效率
- 搜索筛选功能强大
- 小行设计显示更多内容

## 注意事项

1. **数据一致性**：同名课程联动基于课程名称匹配
2. **性能考虑**：大量课程时建议使用搜索功能
3. **操作提示**：重要操作有确认提示
4. **状态保持**：抽屉关闭后选择状态保持不变

## 后续优化

1. **收藏功能**：支持收藏常用课程
2. **历史记录**：记录查课和选择历史
3. **批量导入**：支持从文件导入课程列表
4. **自定义分组**：支持用户自定义课程分组
5. **快捷键**：添加键盘快捷键支持

## 反馈建议

如有使用问题或改进建议，请及时反馈：
- 界面操作是否流畅
- 功能是否满足需求
- 性能是否达到预期
- 是否需要新增功能
