# SoybeanAdmin 企业级管理系统开发指南 v2.0

## 项目概述

### 基本信息
- **项目名称**: SoybeanAdmin 企业级管理系统
- **版本**: v2.0 (数据结构统一化版本)
- **技术架构**: 基于SoybeanAdmin的Vue3+TypeScript+Element Plus前后端分离项目
- **开发规范**: 严格遵循SoybeanJS项目架构标准 (https://docs.soybeanjs.cn/zh/standard/)
- **数据库**: MySQL 8.4.0 (数据库名: newfd, 用户: newfd)
- **部署方式**: 宝塔面板部署，外网访问端口5959
- **开发端口**: 前端5960，后端3000

### v2.0 重要更新
- ✅ **数据结构统一化**: 解决前后端数据字段不一致问题
- ✅ **配置管理模块化**: 统一使用ConfigManager管理所有配置
- ✅ **SQL查询语句修正**: 修复严重的语法错误和字段映射问题
- ✅ **API响应格式标准化**: 统一字段命名和数据类型
- ✅ **文档体系完善**: 建立完整的技术文档和规范文档

### 环境配置
- **前端环境变量**: `/www/wwwroot/FDnew/SoybeanAdmin/.env`
- **后端环境变量**: `/www/wwwroot/FDnew/SoybeanAdmin/server/.env`
- **数据库密码**: jx7KFPLnEsr4fzi7
- **授权系统**: 独立外部API (https://shouquan.hhigq.luxe/public/api/)

### 开发规范要求
1. **严格遵循SoybeanJS架构标准**，参考官方文档进行开发
2. **前后端分离设计**，API统一使用RESTful风格
3. **代码规范统一**，使用TypeScript进行类型约束
4. **组件复用优先**，避免重复造轮子
5. **权限控制完善**，支持多角色权限管理
6. **响应式设计**，兼容移动端访问
7. **滚动适配**，确保主题滚动和外层滚动兼容性

## 快速启动

### 开发环境启动
```bash
# 启动后端服务 (开发模式)
pnpm -F server dev

# 启动前端服务
pnpm dev

# 检测后端端口并更新配置
node scripts/check-backend-port.js
```

### 生产环境部署
```bash
# 构建前端（在项目根目录）
pnpm build

# 一键安装并启动后端（在项目根目录）
bash scripts/install-backend.sh
```

更多细节与排错，请参阅：docs/deployment/backend-oneclick.md

### 常用命令
```bash
# 关闭所有后端程序
ps -ef | grep node | grep -v grep | awk '{print $2}' | xargs kill -9

# 关闭占用的端口
lsof -ti:3000 | xargs kill -9
lsof -ti:5959 | xargs kill -9

# 数据库连接测试
mysql -h localhost -u newfd -pjx7KFPLnEsr4fzi7 newfd
```

## 技术架构

### 前端技术栈
- **框架**: Vue 3.5+ (Composition API)
- **构建工具**: Vite 7.0+
- **语言**: TypeScript 5.0+
- **UI组件库**: Element Plus
- **CSS框架**: UnoCSS
- **状态管理**: Pinia
- **路由管理**: Vue Router + @elegant-router/vue
- **HTTP客户端**: Axios
- **包管理器**: pnpm

### 后端技术栈
- **运行环境**: Node.js 18+
- **框架**: Express.js + TypeScript
- **数据库**: MySQL 8.4.0
- **数据库连接**: mysql2连接池
- **身份验证**: JWT + bcrypt
- **邮件服务**: nodemailer
- **进程管理**: PM2 (生产环境)

### 核心特性
- **多角色权限系统**: 支持超级管理员、管理员、普通用户三种角色
- **系统配置管理**: 完整的系统配置CRUD和导入导出功能
- **邮件管理系统**: SMTP配置、模板管理、发送记录
- **授权验证系统**: 集成外部授权API，确保系统安全
- **响应式设计**: 支持PC端和移动端访问，滚动模式兼容
- **国际化支持**: 内置中英文切换
- **主题切换**: 支持明暗主题切换

## 数据结构规范 (v2.0)

### 字段命名规范

#### 时间字段规范
- **统一使用**: `create_time`, `update_time`
- **数据库类型**: `timestamp`
- **后端类型**: `Date`
- **前端类型**: `string` (ISO格式)
- **API响应格式**: `"YYYY-MM-DD HH:mm:ss"`

```typescript
// ✅ 正确的时间字段使用
interface BaseEntity {
  create_time: string;  // 创建时间
  update_time: string;  // 更新时间
}

// ❌ 已废弃的字段名
// created_at, updated_at
```

#### ID字段规范
- **命名格式**: `{entity}_id` (如: `user_id`, `order_id`)
- **数据库类型**: `bigint` (主键), `int` (外键)
- **后端类型**: `number`
- **前端类型**: `number`

#### 角色字段规范
- **数值角色**: `role` (tinyint) - 用于权限判断
  - `1`: 管理员
  - `2`: 代理商
  - `3`: 普通用户
- **字符串角色**: `user_role` (varchar) - 用于显示
  - `"admin"`: 管理员
  - `"agent"`: 代理商
  - `"user"`: 普通用户

```typescript
// ✅ 正确的角色字段使用
interface UserInfo {
  role: number;        // 数值角色，用于权限判断
  user_role: string;   // 字符串角色，用于显示
}

// 权限判断示例
if (userInfo.role === 1) {
  // 管理员权限逻辑
}

// 显示示例
const roleText = {
  'admin': '管理员',
  'agent': '代理商',
  'user': '普通用户'
}[userInfo.user_role];
```

#### 金额字段规范
- **余额字段**: `balance` - `decimal(10,2)`
- **费率字段**: `price_rate` - `decimal(10,3)`
- **金额字段**: `amount`, `cost_amount` - `decimal(10,2)`

```typescript
// ✅ 正确的金额字段使用
interface UserFinance {
  balance: number;      // 账户余额
  price_rate: number;   // 用户费率 (0-1之间的小数)
  total_recharge: number; // 总充值金额
}

// ❌ 已废弃的字段名
// userRate -> price_rate
```

### JSON字段处理规范

#### 数据库存储
- **数据库类型**: `json`
- **存储格式**: 标准JSON字符串

#### 后端处理
```typescript
// ✅ 正确的JSON字段处理
const processOrderData = (orders: any[]) => {
  return orders.map(order => ({
    ...order,
    course_info: order.course_info ?
      (typeof order.course_info === 'string' ?
        JSON.parse(order.course_info) :
        order.course_info
      ) : null,
    extra_data: order.extra_data ?
      (typeof order.extra_data === 'string' ?
        JSON.parse(order.extra_data) :
        order.extra_data
      ) : null
  }));
};
```

#### 前端处理
```typescript
// ✅ 前端JSON字段使用
interface OrderInfo {
  course_info: {
    course_name: string;
    course_id: number;
    provider_info: any;
  } | null;
  extra_data: Record<string, any> | null;
}
```

### API响应格式规范

#### 标准响应结构
```typescript
interface ApiResponse<T = any> {
  code: string;    // "0000" 表示成功
  msg: string;     // 响应消息
  data: T | null;  // 响应数据
}
```

#### 用户信息响应示例
```typescript
// ✅ 标准的用户信息响应
{
  "code": "0000",
  "msg": "获取用户信息成功",
  "data": {
    "user_id": 1,
    "username": "admin",
    "nickname": "管理员",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "avatar": null,
    "role": 1,                    // 数值角色
    "user_role": "admin",         // 字符串角色
    "status": 1,
    "create_time": "2024-01-01 12:00:00",
    "update_time": "2024-01-01 12:00:00",
    "invite_code": "ABC123",
    "balance": 100.00,
    "price_rate": 0.85,          // 用户费率
    "total_recharge": 500.00,    // 总充值金额
    "referrer_id": null,         // 推荐人ID
    "level_id": 1,               // 用户等级ID
    "sid": null,                 // 上级用户ID
    "roles": ["admin"],
    "permissions": ["user:read", "user:write"]
  }
}
```

## 项目结构

### 前端目录结构
```
src/
├── components/              # 通用组件
│   ├── advanced/           # 高级组件
│   ├── common/             # 基础组件
│   └── custom/             # 自定义组件
├── views/                  # 页面视图
│   ├── order/              # 订单模块
│   │   ├── index.vue       # 订单管理列表
│   │   └── create/         # 下单界面
│   │       └── index.vue   # 创建订单页面
│   ├── course/             # 课程模块
│   │   └── index.vue       # 课程管理
│   ├── provider/           # 货源模块
│   │   └── index.vue       # 货源管理
│   ├── user/               # 用户模块
│   │   └── index.vue       # 用户管理
│   ├── system-center/      # 系统中心
│   │   ├── config/         # 系统配置管理
│   │   ├── email/          # 邮件管理
│   │   ├── license/        # 授权管理
│   │   └── permission/     # 权限管理
│   ├── user-center/        # 用户中心
│   └── home/               # 首页仪表板
├── service/                # API服务
│   ├── api/               # API接口定义
│   └── request/           # 请求配置
├── store/                  # 状态管理
│   └── modules/           # 状态模块
├── router/                 # 路由配置
├── locales/               # 国际化
├── hooks/                 # 组合式函数
├── utils/                 # 工具函数
├── constants/             # 常量定义
└── typings/               # 类型定义
```

### 后端目录结构
```
server/
├── src/
│   ├── controller/         # 控制器层
│   │   ├── auth.ts        # 认证控制器
│   │   ├── config.ts      # 系统配置控制器
│   │   ├── email.ts       # 邮件管理控制器
│   │   └── license.ts     # 授权管理控制器
│   ├── model/             # 数据模型层
│   │   ├── user.ts        # 用户模型
│   │   ├── config.ts      # 配置模型
│   │   └── license.ts     # 授权模型
│   ├── routes/            # 路由定义
│   ├── middleware/        # 中间件
│   │   ├── auth.ts        # 认证中间件
│   │   ├── license.ts     # 授权中间件
│   │   └── response.ts    # 响应格式中间件
│   ├── utils/             # 工具函数
│   │   ├── database.ts    # 数据库连接
│   │   ├── emailService.ts # 邮件服务
│   │   ├── emailTemplateManager.ts # 邮件模板管理
│   │   └── configManager.ts # 配置管理
│   ├── types/             # 类型定义
│   └── routes/            # 路由定义
├── dist/                  # 编译输出
└── package.json
```

## 数据库设计

### 核心业务表详细结构

#### 1. fd_user (用户表)
**表功能**: 用户基本信息和权限管理

**核心字段**:
- `user_id`: 主键，自增ID
- `username`: 用户名（唯一，QQ号格式）
- `password`: 密码（bcrypt加密）
- `invite_code`: 邀请码（唯一）
- `sid`: 上级用户ID（层级关系）
- `balance`: 账户余额
- `user_role`: 用户角色（super/admin/user）
- `status`: 状态（0=禁用，1=启用）
- `create_time`: 创建时间
- `update_time`: 更新时间

**业务逻辑**:
- 支持邀请码注册机制
- 用户层级关系管理
- 多角色权限体系
- 账户余额管理

#### 2. fd_config (系统配置表)
**表功能**: 存储系统的各种配置信息

**核心字段**:
- `config_id`: 主键，自增ID
- `config_key`: 配置键名（唯一）
- `config_value`: 配置值（JSON格式）
- `config_type`: 配置类型（system/email/license等）
- `description`: 配置描述
- `is_encrypted`: 是否加密存储
- `create_time`: 创建时间
- `update_time`: 更新时间

**业务逻辑**:
- 支持多种配置类型管理
- 敏感配置支持加密存储
- JSON格式支持复杂配置结构
- 配置变更历史记录

#### 3. fd_email_log (邮件发送日志表)
**表功能**: 记录邮件发送历史和状态

**核心字段**:
- `log_id`: 主键，自增ID
- `recipient`: 收件人邮箱
- `subject`: 邮件主题
- `template_id`: 模板ID（可选）
- `status`: 发送状态（pending/success/failed）
- `error_message`: 错误信息
- `sent_at`: 发送时间
- `variables`: 模板变量（JSON格式）

**业务逻辑**:
- 记录所有邮件发送历史
- 支持模板和直接发送两种方式
- 错误信息记录便于问题排查
- 发送统计和分析支持

#### 4. fd_license_info (授权信息表)
**表功能**: 存储系统授权信息

**核心字段**:
- `license_id`: 主键，自增ID
- `license_key`: 授权密钥（加密存储）
- `license_data`: 授权数据（JSON格式，加密）
- `expire_time`: 过期时间
- `last_check`: 最后验证时间
- `status`: 状态（0=无效，1=有效）
- `offline_days`: 离线天数

**业务逻辑**:
- 授权信息加密存储
- 支持在线和离线验证
- 3天离线模式支持
- 自动过期检查

### 数据库连接和配置
- **数据库版本**: MySQL 8.4.0
- **连接信息**:
  - 主机: localhost:3306
  - 数据库: newfd
  - 用户: newfd
  - 密码: jx7KFPLnEsr4fzi7
- **连接池配置**: 最大10个连接，支持自动重连
- **字符集**: utf8mb4，支持emoji和特殊字符
- **事务支持**: InnoDB引擎，支持ACID事务

## API设计规范

### 统一响应格式
```typescript
interface ApiResponse<T = any> {
  code: string;           // 响应码
  msg: string;            // 响应消息
  data: T | null;         // 响应数据
}
```

### 业务模块API详细说明

#### 1. 系统配置管理API (`/api/config`)
**权限要求**: 管理员权限

**接口列表**:
- `GET /api/config` - 获取系统配置
  - 返回：完整的系统配置信息
  - 支持配置类型筛选

- `PUT /api/config` - 更新系统配置
  - 请求体：配置数据
  - 验证：配置格式验证
  - 返回：更新结果

- `POST /api/config/export` - 导出配置
  - 返回：JSON格式的配置文件
  - 支持选择性导出

- `POST /api/config/import` - 导入配置
  - 请求体：配置文件数据
  - 验证：配置完整性检查
  - 返回：导入结果

- `GET /api/config/presets` - 获取配置预设
  - 返回：预设配置模板列表

#### 2. 邮件管理API (`/api/email`)
**权限要求**: 管理员权限

**接口列表**:
- `GET /api/email/status` - 获取邮件服务状态
  - 返回：SMTP配置状态和服务可用性

- `POST /api/email/test` - 测试邮件配置
  - 请求体：SMTP配置和测试邮箱
  - 返回：测试结果

- `POST /api/email/send` - 发送邮件
  - 请求体：收件人、主题、内容或模板
  - 支持模板发送和直接发送

- `GET /api/email/templates` - 获取邮件模板列表
  - 返回：所有可用的邮件模板

- `GET /api/email/templates/:id` - 获取模板详情
  - 返回：模板完整信息

- `POST /api/email/templates/preview` - 预览邮件模板
  - 请求体：模板ID和变量
  - 返回：渲染后的邮件内容

#### 3. 用户费率管理API (`/api/user-rate`)
**权限要求**: 用户可查看自己费率，管理员可管理所有

**接口列表**:
- `GET /api/user-rate/levels` - 获取费率等级列表
  - 返回：所有费率等级定义

- `GET /api/user-rate/info/:userId?` - 获取用户费率信息
  - 参数：用户ID（可选，默认当前用户）
  - 返回：用户费率、等级、价格示例

- `PUT /api/user-rate/:userId` - 更新用户费率
  - 权限：管理员
  - 请求体：新费率和变更原因
  - 返回：更新结果

- `GET /api/user-rate/log/:userId?` - 获取费率变更日志
  - 权限：管理员
  - 返回：费率变更历史记录

- `POST /api/user-rate/batch-update` - 批量更新用户费率
  - 权限：超级管理员
  - 请求体：用户ID列表、新费率、原因
  - 返回：批量更新结果

- `GET /api/provider/:id/balance` - 查询余额
  - 实时查询服务商余额
  - 更新本地余额记录

#### 3. 订单管理API (`/api/order`)
**权限要求**: 所有登录用户

**接口列表**:
- `GET /api/order/list` - 获取订单列表
  - 普通用户只能查看自己的订单
  - 管理员可查看所有订单
  - 支持状态筛选和时间范围

- `GET /api/order/:id` - 获取订单详情
  - 权限检查：用户只能查看自己的订单
  - 返回完整订单信息和处理日志

- `POST /api/order` - 创建订单
  - 验证用户余额和商品状态
  - 创建订单记录和日志
  - 返回订单号和支付信息

- `PUT /api/order/:id` - 更新订单状态
  - 管理员权限：可更新任意订单
  - 用户权限：只能取消自己的订单
  - 记录操作日志

#### 4. 用户认证API (`/api/auth`)
**接口列表**:
- `POST /api/auth/login` - 用户登录
  - 验证用户名密码
  - 返回JWT token和用户信息

- `GET /api/auth/getUserInfo` - 获取用户信息
  - 基于token获取用户详情
  - 包含权限和角色信息

- `POST /api/auth/refreshToken` - 刷新token
  - 延长token有效期
  - 返回新的token

### 权限控制体系

#### 认证机制
- **JWT Token**: 使用JSON Web Token进行用户认证
- **Token过期**: 设置合理的过期时间，支持自动刷新
- **安全存储**: 前端使用HttpOnly Cookie存储token

#### 权限中间件
- **authenticate**: 验证用户登录状态
- **requireAdmin**: 验证管理员权限
- **optionalAuthenticate**: 可选认证（支持游客访问）

#### 角色权限映射
- **超级管理员**: 所有权限，包括系统配置
- **管理员**: 业务数据管理权限
- **普通用户**: 个人订单查看和操作权限

#### 前端权限控制
- **路由守卫**: 基于用户角色控制页面访问
- **按钮权限**: 使用useAuth hook控制按钮显示
- **菜单权限**: 根据用户角色动态生成菜单

## 开发流程

### 新功能开发流程
1. **需求分析**: 明确功能需求和技术方案
2. **数据库设计**: 设计或调整相关数据表
3. **API设计**: 定义接口规范和数据格式
4. **后端开发**: 实现控制器、模型和路由
5. **前端开发**: 实现页面、组件和API集成
6. **权限配置**: 配置相关权限和路由
7. **测试验证**: 功能测试和集成测试
8. **文档更新**: 更新开发指南和API文档

### 代码规范
1. **命名规范**: 使用驼峰命名法，变量和函数名要有意义
2. **类型定义**: 所有接口和数据结构都要有TypeScript类型定义
3. **注释规范**: 关键函数和复杂逻辑要有详细注释
4. **错误处理**: 统一的错误处理机制和用户友好的错误提示
5. **性能优化**: 合理使用缓存、懒加载等优化手段

### 部署注意事项
1. **环境变量**: 生产环境要配置正确的环境变量
2. **数据库备份**: 定期备份数据库数据
3. **日志管理**: 配置日志轮转和错误监控
4. **安全配置**: HTTPS、防火墙、访问控制等安全措施
5. **性能监控**: 监控系统性能和资源使用情况

## 常见问题

### 开发环境问题
1. **端口冲突**: 检查3000和5961端口是否被占用
2. **数据库连接**: 确认数据库服务正常和连接参数正确
3. **依赖安装**: 全仓统一使用 pnpm 安装依赖（含后端）：`pnpm install`，或按 package 选择 `pnpm -F <pkg> install`
4. **权限问题**: 确保有足够的文件读写权限

### 生产环境问题
1. **内存不足**: 监控Node.js进程内存使用
2. **数据库性能**: 优化SQL查询和添加必要索引
3. **并发处理**: 合理配置连接池和请求限制
4. **日志文件**: 定期清理和归档日志文件

## 核心业务功能详解

### 系统配置管理模块
**功能描述**: 管理系统的各种配置信息，支持配置的导入导出和预设模板。

**主要功能**:
- 系统基础配置管理（名称、版本、描述等）
- 配置导入导出功能
- 配置预设模板管理
- 配置变更历史记录
- 敏感配置加密存储

**技术实现**:
- 前端使用Element Plus组件实现配置表单
- 支持JSON格式的配置导入导出
- 使用加密算法保护敏感配置
- 集成权限控制，仅管理员可操作

**数据表**: `fd_config`
**API接口**: `/api/config/*`
**权限要求**: 管理员权限

### 邮件管理模块
**功能描述**: 完整的邮件管理系统，包括SMTP配置、模板管理和发送记录。

**主要功能**:
- SMTP服务器配置和测试
- 邮件模板管理（CRUD操作）
- 模板变量系统和预览功能
- 邮件发送（模板发送和直接发送）
- 发送记录和统计分析

**技术实现**:
- 使用nodemailer实现邮件发送
- 模板系统支持HTML和变量替换
- 前端使用Element Plus实现管理界面
- 支持邮件预览和测试发送

**数据表**: `fd_email_log`
**API接口**: `/api/email/*`
**权限要求**: 管理员权限

### 授权管理模块
**功能描述**: 集成外部授权API，确保系统在授权状态下运行，支持在线和离线验证。

**主要功能**:
- 系统授权状态检查和显示
- 授权信息加密存储
- 定期自动授权验证
- 授权到期提醒和处理
- 3天离线模式支持

**技术实现**:
- 集成外部授权API验证
- 授权信息加密存储到数据库
- 定时任务进行授权检查
- 支持离线模式和本地验证
- 前端授权状态实时显示

**数据表**: `fd_license_info`
**API接口**: `/api/license/*`
**权限要求**: 管理员权限

### 用户权限系统
**功能描述**: 完整的用户认证和权限管理体系，支持多角色权限控制。

**角色定义**:
- **超级管理员(super)**: 拥有所有权限，可管理系统配置
- **管理员(admin)**: 可管理业务数据，无系统配置权限
- **VIP用户(vip)**: 最高等级用户，费率≤0.2，享受最低价格
- **代理商(agent)**: 代理商等级，费率≤0.5，享受代理价格
- **会员(member)**: 会员等级，费率≤0.8，享受会员价格
- **普通用户(user)**: 标准用户，费率=1.0，按原价购买

**用户费率系统**:
- **费率范围**: 0.2-1.0，数值越小等级越高
- **价格计算**: 商品基础价格 × 用户费率 = 实际支付价格
- **等级判定**: 根据费率自动分配用户角色和权限
- **业务逻辑**: 继承老项目的费率定价体系

**权限控制**:
- 路由级权限：基于用户角色控制页面访问
- 按钮级权限：基于权限标识控制操作按钮显示
- API级权限：后端中间件验证用户权限

**技术实现**:
- JWT token认证机制
- 基于角色的权限控制(RBAC)
- 前端路由守卫和权限检查
- 后端中间件权限验证

### 用户注册系统
**功能描述**: 基于邀请码的用户注册机制，支持用户层级关系管理。

**主要功能**:
- 邀请码注册机制
- 用户层级关系管理
- 账户余额管理
- 用户状态控制

**技术实现**:
- 邀请码唯一性验证
- 用户名格式验证（QQ号）
- 密码强度验证（数字+大小写字母）
- 用户层级关系维护（sid字段）

## 开发实现细节

### 前端开发规范和实现

#### 组件开发标准
**Vue3 Composition API使用**:
- 统一使用`<script setup lang="tsx">`语法
- 组件props使用interface定义类型
- 事件emits明确声明类型
- 使用ref/reactive管理响应式数据

**TypeScript类型定义**:
- 所有API接口都有对应的类型定义
- 组件props和emits严格类型约束
- 业务数据模型统一类型管理
- 避免使用any类型，提高代码质量

**组件结构规范**:
- 页面组件放在views目录下
- 通用组件放在components目录下
- 业务组件按模块分类组织
- 组件命名使用PascalCase

#### 状态管理架构
**Pinia Store设计**:
- 按业务模块划分store（auth、config、email、license）
- 使用composition API风格编写store
- 支持状态持久化（localStorage）
- 统一的loading和error状态管理

**数据流管理**:
- 页面组件通过store获取数据
- API调用统一在store中处理
- 组件间通信优先使用props/emits
- 复杂状态使用provide/inject

#### API集成架构
**请求封装**:
- 基于axios封装HTTP客户端
- 统一的请求/响应拦截器
- 自动token附加和刷新
- 错误状态码统一处理

**接口类型定义**:
- 所有API接口都有TypeScript类型定义
- 请求参数和响应数据类型化
- 支持泛型接口提高复用性
- API错误类型统一管理

#### 样式和UI规范
使用Element Plus组件库，遵循项目UI规范。

#### 路由和权限
**路由配置**:
- 使用@elegant-router/vue自动生成路由
- 路由meta信息包含权限配置
- 支持动态路由和嵌套路由
- 路由守卫统一权限检查

**权限控制实现**:
- useAuth hook提供权限检查方法
- 路由级权限基于用户角色
- 按钮级权限基于权限标识
- 菜单权限动态生成

### 后端开发规范和实现

#### 控制器层架构
**Express.js路由设计**:
- 按业务模块划分路由文件
- 统一的路由前缀（/api）
- RESTful API设计规范
- 路由参数验证和类型转换

**响应格式统一**:
- 所有API返回统一格式：`{code, msg, data}`
- 成功响应码：0000
- 错误响应码：1001-9999
- 分页数据格式：`{records, current, size, total}`

**参数验证机制**:
- 请求参数类型验证
- 必填参数检查
- 参数范围和格式验证
- 错误信息友好提示

#### 数据模型层实现
**数据库连接管理**:
- 使用mysql2连接池
- 连接池配置：最大10个连接
- 自动重连机制
- 连接超时和错误处理

**SQL查询封装**:
- 原生SQL查询，避免ORM性能损耗
- 预编译语句防止SQL注入
- 事务支持确保数据一致性
- 查询结果类型化处理

**数据安全处理**:
- 敏感数据AES加密存储
- 密码bcrypt哈希处理
- 数据库连接信息环境变量管理
- 查询日志记录和审计

#### 中间件系统
**认证中间件(authenticate)**:
- JWT token验证
- token过期检查
- 用户信息解析和注入
- 认证失败统一处理

**权限中间件(requireAdmin)**:
- 用户角色验证
- 权限不足错误处理
- 操作日志记录
- 权限检查缓存优化

**授权中间件(verifySystemLicense)**:
- 系统授权状态检查
- 允许路径白名单管理
- 授权失败响应处理
- 定期授权验证

#### 安全措施实现
**密码安全**:
- bcrypt哈希算法（salt rounds: 10）
- 密码强度验证
- 登录失败次数限制
- 密码重置安全机制

**JWT Token安全**:
- 安全的密钥管理
- 合理的过期时间设置
- token刷新机制
- 黑名单管理（可选）

**API安全防护**:
- 请求频率限制
- SQL注入防护
- XSS攻击防护
- CORS跨域配置

#### 业务逻辑实现
**平台管理业务**:
- 平台商品CRUD操作
- 状态管理和批量操作
- 价格计算和成本控制
- 商品分类和筛选

**服务商管理业务**:
- 服务商配置管理
- API连接测试
- 余额查询和监控
- 服务商状态控制

**订单处理业务**:
- 订单创建和验证
- 状态流转管理
- 进度跟踪更新
- 订单日志记录

**用户管理业务**:
- 用户注册和登录
- 权限验证和角色管理
- 余额管理和交易记录
- 用户层级关系维护

### 数据库设计原则

**表结构设计**:
- 统一使用`fd_`前缀命名
- 主键使用`表名_id`格式
- 时间字段统一为`create_time`和`update_time`
- 状态字段使用TINYINT类型

**索引优化**:
- 主键自动索引
- 外键关系索引
- 高频查询字段索引
- 唯一约束索引

**数据类型选择**:
- 金额字段使用DECIMAL(10,2)
- 状态字段使用TINYINT
- 时间字段使用TIMESTAMP
- 配置信息使用JSON

**性能优化**:
- 合理的表结构设计
- 适当的索引策略
- 连接池配置优化
- 查询语句优化

## 部署和运维

### 开发环境配置

**环境要求**:
- Node.js 18+
- MySQL 8.4.0
- pnpm 8+
- Git

**配置步骤**:
1. 克隆项目代码
2. 安装前端依赖：`pnpm install`
3. 安装后端依赖：`pnpm -F server install`
4. 配置数据库连接
5. 启动后端服务：`npm run dev`
6. 启动前端服务：`pnpm dev`

### 生产环境部署

**服务器要求**:
- Linux服务器（推荐Ubuntu 20.04+）
- 4GB+ 内存
- 50GB+ 存储空间
- 宝塔面板管理

**部署步骤**:
1. 配置生产环境变量
2. 构建前端项目：`pnpm build`
3. 编译后端项目：`pnpm -F server build`
4. 配置Nginx反向代理
5. 启动后端服务：`pnpm -F server start`
6. 配置SSL证书
7. 设置定时备份

### 监控和维护

**日志管理**:
- 应用日志记录
- 错误日志监控
- 访问日志分析
- 日志轮转配置

**性能监控**:
- 服务器资源监控
- 数据库性能监控
- API响应时间监控
- 用户访问统计

**备份策略**:
- 数据库定时备份
- 代码版本管理
- 配置文件备份
- 灾难恢复计划

## 更新日志

### 2024年12月25日 - v1.0.0
- ✅ 完成项目基础架构搭建
- ✅ 实现用户认证和权限管理系统
- ✅ 完成业务模块核心功能开发
- ✅ 集成外部授权验证系统
- ✅ 完成前端业务页面开发
- ✅ 实现API接口和权限控制
- ✅ 优化数据库结构和性能
- ✅ 完善开发文档和部署指南

### 具体开发成果详解

#### 前端开发成果
**业务模块完整实现**:
- `src/views/business/platform/` - 平台管理模块
  - index.vue: 平台列表页面，支持搜索、分页、状态筛选
  - modules/platform-search.vue: 搜索组件
  - 集成TableHeaderOperation组件实现增删改查操作

- `src/views/business/provider/` - 服务商管理模块
  - index.vue: 服务商列表页面，支持API测试和余额查询
  - modules/provider-search.vue: 搜索组件
  - 实现服务商状态管理和配置功能

- `src/views/business/order/` - 订单管理模块
  - index.vue: 订单列表页面，支持状态跟踪和进度显示
  - modules/order-search.vue: 订单搜索组件
  - 实现订单状态标签和进度条显示

**权限控制实现**:
- `src/hooks/business/auth.ts` - 权限检查hook
- `src/router/elegant/routes.ts` - 路由权限配置
- `src/views/system-center/permission/` - 权限管理页面
- 实现了路由级和按钮级权限控制

**UI组件集成**:
- 全站采用 Element Plus 组件（如 ElTable、ElCard、ElButton 等）
- 集成 TableHeaderOperation 高级表格操作组件
- 实现响应式设计，支持移动端访问
- 配置 UnoCSS 原子化样式系统

#### 后端开发成果
**API接口完整实现**:
- `server/src/controller/platform.ts` - 平台管理控制器
  - 实现平台商品的CRUD操作
  - 支持分页查询和条件筛选
  - 提供状态管理和批量操作

- `server/src/controller/provider.ts` - 服务商管理控制器
  - 实现服务商信息管理
  - 提供API连接测试功能
  - 支持余额查询和状态控制

- `server/src/controller/order.ts` - 订单管理控制器
  - 实现订单全生命周期管理
  - 支持订单状态跟踪和进度更新
  - 提供订单日志记录功能

**认证和权限系统**:
- `server/src/middleware/auth.ts` - 认证中间件
  - JWT token验证和用户信息解析
  - 管理员权限检查
  - 可选认证支持

- `server/src/middleware/license.ts` - 授权中间件
  - 系统授权状态验证
  - 白名单路径管理
  - 授权失败处理

**数据模型实现**:
- `server/src/model/platform.ts` - 平台数据模型
- `server/src/model/provider.ts` - 服务商数据模型
- `server/src/model/order.ts` - 订单数据模型
- `server/src/model/user.ts` - 用户数据模型
- 实现了完整的数据库操作和业务逻辑

#### 数据库设计成果
**核心业务表实现**:
- fd_platform_item: 平台商品表，包含价格、状态、配置等字段
- fd_provider: 服务商表，支持API配置和余额管理
- fd_order: 订单表，完整的订单信息和状态跟踪
- fd_user: 用户表，支持多角色和权限管理

**数据安全措施**:
- 敏感数据AES加密存储
- 密码bcrypt哈希处理
- 数据库连接池配置
- 索引优化和查询性能提升

#### 系统集成成果
**前后端完整对接**:
- 统一的API响应格式
- 完整的错误处理机制
- 自动token刷新
- 权限验证全覆盖

**授权系统集成**:
- 外部授权API集成
- 授权状态实时检查
- 数据签名防篡改
- 离线模式支持

**开发环境配置**:
- 前端开发服务器配置
- 后端开发环境配置
- 数据库连接配置
- 环境变量管理

**生产环境部署**:
- 宝塔面板部署配置
- Nginx反向代理设置
- SSL证书配置
- 进程管理和监控

## 关键技术实现说明

### 权限系统实现细节
**前端权限控制**:
- 路由权限基于用户角色配置在routes.ts中
- 使用useAuth hook进行按钮级权限检查
- 权限信息存储在Pinia store中，支持持久化
- 菜单权限通过路由meta信息动态生成

**后端权限验证**:
- JWT token包含用户ID、角色等信息
- 中间件层面进行权限验证
- 数据库查询时根据用户角色过滤数据
- API接口按权限级别分组保护

### 数据安全实现
**敏感数据保护**:
- 平台账号密码使用AES-256加密存储
- 用户密码使用bcrypt哈希处理
- API密钥和配置信息加密存储
- 数据传输使用HTTPS协议

**授权验证机制**:
- 集成外部授权API进行系统激活验证
- 授权信息使用数据签名防篡改
- 支持离线模式和定期验证
- 授权失败时限制系统功能访问

### 性能优化措施
**前端性能优化**:
- 使用Vite构建工具提高开发效率
- 组件懒加载减少初始包大小
- 图片资源压缩和CDN加速
- 合理使用缓存策略

**后端性能优化**:
- 数据库连接池管理
- SQL查询优化和索引使用
- API响应数据压缩
- 合理的缓存策略

### 错误处理机制
**统一错误处理**:
- 前端统一错误拦截和用户提示
- 后端统一错误响应格式
- 详细的错误日志记录
- 用户友好的错误信息展示

**异常监控**:
- 系统异常自动记录
- 关键操作审计日志
- 性能监控和告警
- 错误统计和分析

## 开发最佳实践

### 代码质量保证
**TypeScript使用**:
- 严格的类型检查配置
- 接口和类型定义完整
- 避免使用any类型
- 泛型使用提高代码复用性

**代码规范**:
- ESLint和Prettier配置
- 统一的命名规范
- 代码注释和文档
- Git提交信息规范

### 测试策略
**单元测试**:
- 关键业务逻辑单元测试
- API接口测试
- 组件功能测试
- 测试覆盖率监控

**集成测试**:
- 前后端接口集成测试
- 用户权限流程测试
- 业务流程端到端测试
- 性能压力测试

### 部署和运维
**CI/CD流程**:
- 代码提交自动构建
- 自动化测试执行
- 生产环境自动部署
- 回滚机制和版本管理

**监控和维护**:
- 系统性能监控
- 错误日志监控
- 用户行为分析
- 定期备份和恢复测试

## 扩展开发指南

### 新增业务模块
**开发流程**:
1. 数据库表设计和创建
2. 后端API接口开发
3. 前端页面和组件开发
4. 权限配置和路由设置
5. 测试验证和文档更新

**注意事项**:
- 遵循现有的代码规范和架构
- 复用现有组件和工具函数
- 考虑权限控制和数据安全
- 保持API接口的一致性

### 第三方集成
**API集成**:
- 统一的HTTP客户端封装
- 错误处理和重试机制
- 接口文档和类型定义
- 测试和监控

**支付系统集成**:
- 多种支付方式支持
- 支付安全和验证
- 订单状态同步
- 财务对账功能

### 待开发功能规划
- 🔄 订单处理自动化流程
  - 自动分派订单到服务商
  - 订单状态自动同步
  - 异常订单自动处理

- 🔄 数据统计和报表功能
  - 订单统计和分析
  - 用户行为分析
  - 财务报表生成
  - 性能指标监控

- 🔄 消息通知系统
  - 订单状态变更通知
  - 系统公告推送
  - 邮件和短信通知
  - 站内消息系统

- 🔄 工单系统完善
  - 工单分类和优先级
  - 工单处理流程
  - 客服工作台
  - 满意度评价

- 🔄 移动端适配优化
  - 响应式设计完善
  - 移动端专用组件
  - 触摸交互优化
  - 性能优化

## 前端界面模块详解

### 订单管理模块
**页面路径**: `/order`
**组件位置**: `src/views/order/index.vue`

**主要功能**:
- 订单列表展示（分页、搜索、筛选）
- 订单状态管理（待处理、处理中、已完成、失败、已取消）
- 订单详情查看（抽屉式详情页）
- 订单进度跟踪（进度条显示）
- 订单操作日志

**技术特点**:
- 使用NDataTable组件实现高性能表格
- 响应式设计，支持移动端访问
- 实时状态更新和进度显示
- 统一的搜索和筛选交互

### 下单界面模块
**页面路径**: `/order/create`
**组件位置**: `src/views/order/create/index.vue`

**主要功能**:
- 课程选择（分类筛选、平台筛选、关键词搜索）
- 课程信息展示（价格、平台、分类）
- 用户费率计算（实时价格计算）
- 订单信息填写（平台账号、密码、学校、联系方式）
- 表单验证和提交

**技术特点**:
- 左右分栏布局，用户体验友好
- 实时价格计算基于用户费率
- 完整的表单验证机制
- 课程选择的交互式列表

### 课程管理模块
**页面路径**: `/course`
**组件位置**: `src/views/course/index.vue`

**主要功能**:
- 课程CRUD操作（增删改查）
- 课程分类管理
- 平台类型配置
- 价格体系管理（销售价、成本价）
- 课程状态控制（上架/下架）
- 批量导入功能

**技术特点**:
- 模态框编辑，操作流畅
- 支持批量操作
- 完整的权限控制
- 数据验证和错误处理

### 货源管理模块
**页面路径**: `/provider`
**组件位置**: `src/views/provider/index.vue`

**主要功能**:
- 货源CRUD操作
- API配置管理
- 连接测试功能
- 余额查询和监控
- IP白名单配置
- 货源状态管理

**技术特点**:
- API连接测试集成
- 实时余额查询
- 抽屉式余额详情
- 安全的配置管理

### 用户管理模块
**页面路径**: `/user`
**组件位置**: `src/views/user/index.vue`

**主要功能**:
- 用户CRUD操作
- 用户费率管理
- 批量费率调整
- 用户角色分配
- 账户余额管理
- 用户状态控制

**技术特点**:
- 支持批量选择和操作
- 费率等级可视化
- 完整的用户权限体系
- 数据导出功能

## 数据库迁移执行记录

### 迁移执行状态
✅ **用户费率数据迁移完成**
- 迁移时间：2025-06-28
- 迁移内容：老项目用户费率数据(addprice字段)
- 迁移结果：成功更新用户费率和角色分配
- 数据完整性：已验证

### 迁移统计信息
- **VIP用户** (费率≤0.2): 根据实际数据统计
- **代理商** (费率≤0.5): 根据实际数据统计
- **会员** (费率≤0.8): 根据实际数据统计
- **普通用户** (费率=1.0): 根据实际数据统计

### 待迁移模块
- [ ] 课程数据迁移
- [ ] 订单数据迁移
- [ ] 货源数据迁移
- [ ] 分类数据迁移

## 路由开发规范

### SoybeanJS路由规范
项目严格遵循SoybeanJS路由规范：https://docs.soybeanjs.cn/zh/guide/router/create.html

**文件命名规范**:
- 每个功能模块一个目录：`src/views/模块名/`
- 主页面命名为：`index.vue`
- 子页面使用目录结构：`src/views/模块名/子页面名/index.vue`

**路由自动生成**:
- 开发模式下，elegant-router会自动监听文件变化并生成路由
- 路由配置文件：`src/router/elegant/routes.ts`（自动生成，请勿手动修改）
- 国际化配置：`src/locales/langs/zh-cn.ts`

**示例结构**:
```
src/views/
├── order/                  # 订单模块
│   ├── index.vue          # 订单管理 (/order)
│   └── create/            # 下单页面
│       └── index.vue      # 创建订单 (/order/create)
├── course/                # 课程模块
│   └── index.vue          # 课程管理 (/course)
├── provider/              # 货源模块
│   └── index.vue          # 货源管理 (/provider)
└── user/                  # 用户模块
    └── index.vue          # 用户管理 (/user)
```

## UI组件规范

### Element Plus组件库
项目统一使用Element Plus组件库，严禁混用其他UI库。

**常用组件映射**:
- 卡片：`ElCard`
- 按钮：`ElButton`
- 表格：`ElTable`, `ElTableColumn`
- 表单：`ElForm`, `ElFormItem`
- 输入框：`ElInput`
- 选择器：`ElSelect`, `ElOption`
- 标签：`ElTag`
- 分页：`ElPagination`
- 对话框：`ElDialog`
- 抽屉：`ElDrawer`
- 确认框：`ElPopconfirm`

**组件使用示例**:
```vue
<template>
  <ElCard>
    <template #header>
      <div class="flex justify-between items-center">
        <span class="text-lg font-medium">页面标题</span>
        <ElButton type="primary" @click="handleCreate">
          新增
        </ElButton>
      </div>
    </template>

    <ElTable :data="tableData" v-loading="loading">
      <ElTableColumn prop="name" label="名称" />
      <ElTableColumn label="操作">
        <template #default="{ row }">
          <ElButton size="small" @click="handleEdit(row)">
            编辑
          </ElButton>
        </template>
      </ElTableColumn>
    </ElTable>
  </ElCard>
</template>
```

## 开发环境配置

### 前端开发环境
- **开发模式**: 支持热更新，路由自动生成
- **端口**: 5961 (当前运行端口)
- **构建工具**: Vite 6.0.1 (已降级解决兼容性问题)
- **包管理器**: pnpm
- **UI组件库**: Element Plus

### 后端开发环境
- **开发模式**: 支持热重载
- **端口**: 3000
- **数据库**: MySQL 8.4.0
- **进程管理**: PM2

### 开发流程
1. **启动开发环境**: 前后端已在宝塔中部署开发模式
2. **创建新页面**: 在src/views目录下按规范创建，开发模式自动生成路由
3. **组件开发**: 统一使用Element Plus组件，遵循项目UI规范
4. **API开发**: 在server/src目录下开发，支持热重载
5. **数据库操作**: 使用root账号(密码: effd343bc1fcf472)进行管理

## 滚动兼容性规范

### 重要提醒
前端界面需要适配主题滚动和外层滚动的兼容性问题，在所有界面开发中都要注意这个问题。

### 滚动模式说明
项目支持两种滚动模式：
1. **主体滚动模式**: 内容区域自己滚动，外层容器固定
2. **外层滚动模式**: 整个页面滚动，内容区域跟随

### 开发新页面检查清单

开发新页面时，请检查：

- [ ] **页面在外层滚动模式下正常显示**
  - 内容可以完整展示，不被截断
  - 没有出现双重滚动条
  - 页面高度自适应内容

- [ ] **页面在主体滚动模式下正常显示**
  - 内容区域可以正常滚动
  - 表格等组件在容器内正常显示
  - 没有内容溢出问题

- [ ] **移动端响应式布局正常**
  - 在小屏幕设备上布局不错乱
  - 表格可以横向滚动
  - 搜索栏在移动端正确换行

- [ ] **没有使用固定高度限制内容**
  - 避免使用 `height="400"` 等固定高度
  - 使用 `max-height="70vh"` 等相对高度
  - 让内容自然流动

- [ ] **使用了合适的间距类**
  - 使用 `gap-16px` 等间距类
  - 避免使用 `margin` 和 `padding` 的固定值
  - 保持一致的视觉间距

- [ ] **内容可以正常滚动**
  - 长列表可以正常滚动查看
  - 表格内容不会被遮挡
  - 分页组件正常工作

### 标准页面布局模板

```vue
<template>
  <div class="flex flex-col gap-16px p-16px">
    <ElCard>
      <template #header>
        <div class="flex justify-between items-center">
          <span class="text-lg font-medium">页面标题</span>
          <div class="flex gap-8px">
            <!-- 操作按钮 -->
          </div>
        </div>
      </template>

      <!-- 搜索栏 -->
      <div class="pb-12px flex flex-wrap gap-16px items-center">
        <!-- 搜索组件 -->
      </div>

      <!-- 数据表格 -->
      <div class="w-full overflow-x-auto">
        <ElTable
          :data="tableData"
          v-loading="loading"
          style="width: 100%"
          max-height="70vh"
        >
          <!-- 表格列 -->
        </ElTable>
      </div>

      <!-- 分页 -->
      <div class="flex justify-center mt-16px">
        <ElPagination />
      </div>
    </ElCard>
  </div>
</template>

<style scoped>
/* 响应式表格滚动 */
.el-table {
  @apply w-full;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .el-table {
    font-size: 12px;
  }

  .flex-wrap {
    flex-direction: column;
    align-items: stretch;
  }

  .w-240px,
  .w-120px,
  .w-100px {
    width: 100%;
  }
}
</style>
```

### 禁用的CSS类和属性

❌ **禁止使用**:
- `min-h-500px` - 固定最小高度
- `height="400"` - 固定高度
- `overflow-hidden` - 隐藏溢出内容
- `flex-col-stretch` - 强制拉伸可能导致问题
- `sm:flex-1-hidden` - 复杂的隐藏类

✅ **推荐使用**:
- `max-height="70vh"` - 相对最大高度
- `overflow-x-auto` - 横向自动滚动
- `flex flex-col gap-16px` - 简单的flex布局
- `p-16px` - 统一的内边距

### 注意事项
- 所有页面必须使用Element Plus组件，禁止使用Naive UI等其他组件库
- 路由文件由elegant-router自动生成，请勿手动修改
- 新增页面后需要在国际化文件中添加对应的中文标题
- 开发过程中严格遵循滚动兼容性规范，确保在所有滚动模式下都能正常工作

## 前端开发新规范 (2025年更新)

### 强制性要求

所有新开发的页面必须遵循以下规范：

1. **标准布局结构**
   ```vue
   <div class="flex flex-col gap-16px p-16px">
     <!-- 页面内容 -->
   </div>
   ```

2. **移动端检测**
   ```typescript
   const isMobile = ref(false);
   const checkMobile = () => {
     isMobile.value = window.innerWidth < 768;
   };
   ```

3. **组件命名规范**
   - 使用 PascalCase：`ElButton`、`ElTable`
   - 禁止使用 kebab-case：`el-button`、`el-table`

4. **错误处理标准**
   ```typescript
   const errorMessage = error.response?.data?.msg || error.message || '操作失败';
   ElMessage.error(errorMessage);
   ```

### 页面模板

项目提供了标准页面模板：
- `src/templates/StandardPageTemplate.vue` - 列表页面模板
- `src/templates/StandardFormTemplate.vue` - 表单页面模板

### 文档参考

- [前端界面规范指南](../src/docs/FRONTEND_STANDARDS.md)
- [页面模板使用指南](../src/docs/PAGE_TEMPLATE_GUIDE.md)
- [组件数据绑定规范](../src/docs/COMPONENT_DATA_BINDING.md)

### 开发检查清单

创建新页面时，请确保：
- [ ] 使用标准布局结构
- [ ] 添加移动端检测
- [ ] 使用正确的组件命名
- [ ] 添加适当的错误处理
- [ ] 通过构建测试
- [ ] 在不同设备上测试通过
