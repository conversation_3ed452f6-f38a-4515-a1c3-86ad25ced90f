# 上游参数传递最终修复方案

## 📋 问题根源分析

从上游接口的错误检查代码可以看出问题的根本原因：

```php
// 上游接口检查逻辑
if ($uid == '' || $key == '') {
    $result = array("code" => -1, "msg" => "所有项目不能为空！$uid,$key,$act");
    exit(json_encode($result));
}
```

### 错误信息解析
```json
{
  "code": -1,
  "msg": "所有项目不能为空11！,,get"
}
```

从错误信息 `"所有项目不能为空11！,,get"` 可以分析出：
- `$uid` = 空值（显示为空）
- `$key` = 空值（显示为空）  
- `$act` = "get"

### 问题原因
1. **参数传递格式错误**: 使用了JSON格式，但上游期望表单格式
2. **HTTP方法不匹配**: 使用了POST，但上游可能期望GET
3. **参数接收方式**: PHP通过`$_GET`或`$_POST`接收参数，不是JSON

## ✨ 最终修复方案

### 1. **修复HTTP请求格式**

#### 支持GET和POST两种方式
```typescript
// 根据HTTP方法决定参数传递方式
if (interfaceConfig.http_method.toUpperCase() === 'GET') {
  // GET请求：参数放在URL查询字符串中
  axiosConfig = {
    method: 'GET',
    url: interfaceConfig.endpoint_url,
    params: requestData,  // 自动转换为 ?key=value&uid=value
    timeout: 30000
  };
} else {
  // POST请求：使用表单格式
  const formData = new URLSearchParams();
  for (const [key, value] of Object.entries(requestData)) {
    formData.append(key, String(value || ''));
  }
  
  axiosConfig = {
    method: 'POST',
    url: interfaceConfig.endpoint_url,
    data: formData.toString(),  // key=value&uid=value
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  };
}
```

**效果**:
- ✅ GET请求：参数作为查询字符串发送
- ✅ POST请求：参数作为表单数据发送
- ✅ 兼容PHP的`$_GET`和`$_POST`接收方式

### 2. **完善请求模板**

#### 添加缺少的act参数
```json
{
  "key": "${auth.token}",           // API密钥
  "uid": "${auth.uid}",             // 用户ID  
  "act": "get",                     // 操作类型（固定值）
  "pass": "${data.password}",       // 学生密码
  "user": "${data.username}",       // 学生用户名
  "school": "${data.school}",       // 学校名称
  "platform": "${data.platform}",  // 平台类型
  "course_id": "${data.course_id}"  // 课程ID
}
```

#### 参数说明
- `key`: API密钥，从货源token字段获取
- `uid`: 用户ID，从货源配置获取（默认为"5"）
- `act`: 操作类型，固定值"get"表示查询操作
- `pass`: 学生密码，从测试数据获取
- `user`: 学生用户名，从测试数据获取
- `school`: 学校名称，从测试数据获取
- `platform`: 平台类型，从测试数据获取
- `course_id`: 课程ID，查课时可为空

**效果**:
- ✅ 包含上游检查的所有必要参数
- ✅ 参数名称与上游期望完全匹配
- ✅ 支持灵活的变量替换

### 3. **更新HTTP方法**

#### 改为GET方法
```sql
UPDATE fd_provider_interface 
SET http_method = 'GET', request_template = ? 
WHERE provider_id = ? AND interface_type = 'query';
```

#### GET请求的优势
- **简单直接**: 参数直接在URL中，易于调试
- **缓存友好**: 浏览器和代理可以缓存GET请求
- **日志可见**: 服务器日志中可以看到完整的请求参数
- **PHP兼容**: 通过`$_GET`数组直接访问参数

**效果**:
- ✅ 参数通过URL查询字符串传递
- ✅ PHP可以通过`$_GET['key']`、`$_GET['uid']`等获取参数
- ✅ 避免了JSON解析的复杂性

## 🎯 修复效果对比

### 修复前的请求
```http
POST /api/query HTTP/1.1
Content-Type: application/json

{
  "key": "YsIYr7lZ75plP8Y5",
  "uid": "5", 
  "pass": "test123456",
  "user": "test_student",
  "school": "测试大学",
  "platform": "network_course"
}
```

**问题**: PHP无法通过`$_POST['key']`获取JSON中的参数

### 修复后的请求
```http
GET /api/query?key=YsIYr7lZ75plP8Y5&uid=5&act=get&pass=test123456&user=test_student&school=测试大学&platform=network_course&course_id= HTTP/1.1
```

**优势**: PHP可以直接通过`$_GET['key']`、`$_GET['uid']`等获取参数

### 上游接收对比
```php
// 修复前（无法获取参数）
$uid = $_POST['uid'];  // 空值，因为POST数据是JSON格式
$key = $_POST['key'];  // 空值，因为POST数据是JSON格式

// 修复后（正确获取参数）
$uid = $_GET['uid'];   // "5"
$key = $_GET['key'];   // "YsIYr7lZ75plP8Y5"
$act = $_GET['act'];   // "get"
```

## 🔧 技术实现细节

### URLSearchParams使用
```typescript
// 将对象转换为表单格式
const URLSearchParams = require('url').URLSearchParams;
const formData = new URLSearchParams();

for (const [key, value] of Object.entries(requestData)) {
  formData.append(key, String(value || ''));
}

// 结果: "key=value&uid=value&act=get"
const formString = formData.toString();
```

### 查询参数处理
```typescript
// GET请求时，axios自动处理params
{
  method: 'GET',
  url: 'http://example.com/api',
  params: { key: 'value', uid: '5' }
}

// 实际发送: http://example.com/api?key=value&uid=5
```

### 空值处理
```typescript
// 确保所有参数都有值，避免undefined
for (const [key, value] of Object.entries(requestData)) {
  formData.append(key, String(value || ''));
}
```

## 🚀 测试验证

### 预期的成功响应
```json
{
  "code": 0,
  "msg": "查询成功", 
  "data": {
    "courses": [
      {
        "course_id": "001",
        "course_name": "高等数学",
        "price": 100,
        "description": "大学数学基础课程"
      }
    ]
  }
}
```

### 测试数据格式
```json
{
  "school": "测试大学",
  "username": "test_student",
  "password": "test123456", 
  "platform": "network_course",
  "course_id": ""
}
```

### 实际发送的请求
```
GET /api/query?key=YsIYr7lZ75plP8Y5&uid=5&act=get&pass=test123456&user=test_student&school=测试大学&platform=network_course&course_id=
```

## 📋 验证清单

### 配置验证
- [x] HTTP方法已改为GET
- [x] 请求模板包含act参数
- [x] 所有必要参数都已配置
- [x] 参数名称与上游期望匹配

### 请求验证
- [x] 参数以查询字符串形式发送
- [x] 参数值正确填充
- [x] 空值处理正确
- [x] 编码格式正确

### 响应验证
- [ ] 上游不再返回"参数为空"错误
- [ ] 返回有效的业务响应
- [ ] 响应格式符合预期
- [ ] 错误处理正确

## 🔍 故障排查

### 如果仍然提示参数为空

1. **检查URL编码**
   ```javascript
   // 确保中文参数正确编码
   console.log(encodeURIComponent('测试大学'));
   ```

2. **检查参数顺序**
   ```javascript
   // 某些接口可能对参数顺序敏感
   const orderedParams = { key, uid, act, user, pass, school, platform };
   ```

3. **检查参数值**
   ```javascript
   // 确保所有参数都有值
   Object.entries(requestData).forEach(([key, value]) => {
     if (!value) console.log(`参数 ${key} 为空`);
   });
   ```

### 如果需要POST方法

```typescript
// 使用表单格式的POST请求
{
  method: 'POST',
  url: interfaceConfig.endpoint_url,
  data: formData.toString(),
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded'
  }
}
```

### 调试技巧

1. **查看实际请求**
   ```javascript
   console.log('实际发送的请求:', axiosConfig);
   ```

2. **模拟上游接收**
   ```php
   // 在上游接口添加调试代码
   error_log('GET参数: ' . print_r($_GET, true));
   error_log('POST参数: ' . print_r($_POST, true));
   ```

3. **使用curl测试**
   ```bash
   curl "http://api.example.com/query?key=YsIYr7lZ75plP8Y5&uid=5&act=get&user=test&pass=123456&school=测试大学"
   ```

---

**修复完成时间**: 2024-12-10  
**修复范围**: HTTP请求格式和参数传递方式  
**影响模块**: 接口测试、参数构建、请求发送  
**状态**: ✅ 修复完成，等待测试验证
