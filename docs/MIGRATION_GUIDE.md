# 数据结构迁移指南 v1.x → v2.0

## 迁移概述

本指南帮助开发者从 v1.x 版本迁移到 v2.0 版本。v2.0 版本主要解决了前后端数据结构不一致的问题，统一了字段命名规范，修正了SQL查询语句错误。

### 迁移优先级

🔴 **高优先级** - 必须立即修改，否则功能异常
🟡 **中优先级** - 建议修改，提高代码质量
🟢 **低优先级** - 可选修改，向后兼容

## 前端代码迁移

### 🔴 用户信息字段变更

#### 时间字段统一
```typescript
// ❌ v1.x 旧写法
userInfo.created_at
userInfo.updated_at

// ✅ v2.0 新写法  
userInfo.create_time
userInfo.update_time
```

#### 费率字段重命名
```typescript
// ❌ v1.x 旧写法
userInfo.userRate
user.userRate

// ✅ v2.0 新写法
userInfo.price_rate
user.price_rate
```

#### 角色字段规范化
```typescript
// ❌ v1.x 混乱的角色处理
const role = userInfo.role; // 可能是字符串或数字

// ✅ v2.0 规范的角色处理
const numericRole = userInfo.role;        // 数值角色，用于权限判断
const stringRole = userInfo.user_role;    // 字符串角色，用于显示

// 权限判断
if (userInfo.role === 1) {
  // 管理员权限
}

// 显示文本
const roleText = {
  'admin': '管理员',
  'agent': '代理商',
  'user': '普通用户'
}[userInfo.user_role];
```

### 🔴 API调用参数更新

#### 用户相关API
```typescript
// ❌ v1.x 旧的API调用
const createUser = (data: any) => {
  return request({
    url: '/user/create',
    method: 'post',
    data: {
      ...data,
      userRate: data.userRate  // 旧字段名
    }
  });
};

// ✅ v2.0 新的API调用
const createUser = (data: any) => {
  return request({
    url: '/user/create',
    method: 'post',
    data: {
      ...data,
      price_rate: data.price_rate,  // 新字段名
      role: data.role,              // 数值角色
      user_role: data.user_role     // 字符串角色
    }
  });
};
```

### 🟡 组件数据绑定更新

#### 表格列定义
```typescript
// ❌ v1.x 旧的表格列定义
const columns = [
  {
    key: 'userRate',
    title: '费率',
    render: (row: any) => `${(row.userRate * 100).toFixed(1)}%`
  },
  {
    key: 'createTime',
    title: '创建时间',
    render: (row: any) => new Date(row.created_at).toLocaleDateString()
  }
];

// ✅ v2.0 新的表格列定义
const columns = [
  {
    key: 'userRate',  // API返回的字段名
    title: '费率',
    render: (row: any) => `${(row.userRate * 100).toFixed(1)}%`
  },
  {
    key: 'createTime',  // API返回的字段名
    title: '创建时间',
    render: (row: any) => new Date(row.createTime).toLocaleDateString()
  }
];
```

#### 表单数据结构
```typescript
// ❌ v1.x 旧的表单结构
interface UserFormData {
  username: string;
  userRate: number;  // 旧字段名
  role: string;      // 混用字符串
}

// ✅ v2.0 新的表单结构
interface UserFormData {
  username: string;
  price_rate: number;  // 新字段名
  role: number;        // 数值角色
  user_role: string;   // 字符串角色
}
```

### 🟡 类型定义更新

#### 用户信息接口
```typescript
// ❌ v1.x 旧的类型定义
interface UserInfo {
  user_id: number;
  username: string;
  role: string | number;  // 类型不明确
  created_at: string;     // 旧字段名
  userRate: number;       // 旧字段名
}

// ✅ v2.0 新的类型定义
interface UserInfo {
  user_id: number;
  username: string;
  role: number;           // 明确的数值类型
  user_role: string;      // 明确的字符串类型
  create_time: string;    // 新字段名
  price_rate: number;     // 新字段名
  total_recharge?: number; // 新增字段
  referrer_id?: number;   // 新增字段
  level_id?: number;      // 新增字段
}
```

## 后端代码迁移

### 🔴 SQL查询语句修正

#### 严重语法错误修正
```sql
-- ❌ v1.x 错误的SQL语法
SELECT o.order_id as Number(orderId) FROM fd_order o;

-- ✅ v2.0 正确的SQL语法
SELECT o.order_id as orderId FROM fd_order o;
```

#### 字段映射修正
```sql
-- ❌ v1.x 错误的字段映射
SELECT 
  u.user_id,
  u.price_rate as userRate,
  u.user_role as roles,     -- 错误的别名
  u.created_at as createTime -- 错误的字段名
FROM fd_user u;

-- ✅ v2.0 正确的字段映射
SELECT 
  u.user_id,
  u.price_rate as userRate,
  u.user_role as userRole,  -- 正确的别名
  u.create_time as createTime -- 正确的字段名
FROM fd_user u;
```

### 🔴 API响应格式统一

#### 用户信息API
```typescript
// ❌ v1.x 不一致的响应格式
return res.json(createSuccessResponse({
  user_id: user.user_id,
  role: user.user_role,      // 字段映射错误
  create_time: user.created_at, // 字段名错误
  // 缺少重要字段
}));

// ✅ v2.0 统一的响应格式
return res.json(createSuccessResponse({
  user_id: user.user_id,
  role: user.role,           // 数值角色
  user_role: user.user_role, // 字符串角色
  create_time: user.create_time, // 正确字段名
  price_rate: user.price_rate,   // 费率字段
  total_recharge: user.total_recharge, // 新增字段
  referrer_id: user.referrer_id,       // 新增字段
  level_id: user.level_id,             // 新增字段
}));
```

### 🟡 JSON字段处理优化

#### 订单数据处理
```typescript
// ❌ v1.x 未处理JSON字段
const processedOrderList = orderList.map((order: any) => ({
  ...order,
  // course_info 和 extra_data 未正确处理
}));

// ✅ v2.0 正确处理JSON字段
const processedOrderList = orderList.map((order: any) => ({
  ...order,
  course_info: order.course_info ? 
    (typeof order.course_info === 'string' ? 
      JSON.parse(order.course_info) : 
      order.course_info
    ) : null,
  extra_data: order.extra_data ? 
    (typeof order.extra_data === 'string' ? 
      JSON.parse(order.extra_data) : 
      order.extra_data
    ) : null
}));
```

## 配置管理迁移

### 🔴 统一配置管理

#### 移除重复配置定义
```typescript
// ❌ v1.x 多处重复的配置定义
// 在 controller/config.ts 中
const DEFAULT_CONFIG = {
  systemName: 'SoybeanAdmin',
  // ... 大量重复配置
};

// 在 utils/configManager.ts 中
const defaultConfig = {
  systemName: 'SoybeanAdmin',
  // ... 相同的配置
};

// ✅ v2.0 统一的配置管理
// 只在 utils/configManager.ts 中定义
// 其他地方通过 ConfigManager 使用
import ConfigManager from '../utils/configManager';

const config = ConfigManager.getConfig();
```

## 数据库迁移

### 🟢 数据库连接统一

#### 连接配置统一
```typescript
// ❌ v1.x 不一致的数据库用户
// db.ts 使用 newfd 用户
// connection.ts 使用 root 用户

// ✅ v2.0 统一使用 newfd 用户
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'newfd',  // 统一用户
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'newfd'
};
```

## 测试验证

### 迁移后验证清单

#### 🔴 核心功能验证
- [ ] 用户登录功能正常
- [ ] 用户信息获取字段完整
- [ ] 用户列表显示正确
- [ ] 订单列表查询无错误
- [ ] 角色权限判断正常

#### 🟡 数据显示验证
- [ ] 时间字段显示格式正确
- [ ] 费率字段计算准确
- [ ] 角色显示文本正确
- [ ] JSON字段解析正常

#### 🟢 性能验证
- [ ] API响应时间正常
- [ ] 数据库查询效率
- [ ] 前端渲染性能

### 测试用例

#### API测试
```bash
# 测试用户信息API
curl -H "Authorization: Bearer {token}" \
     http://localhost:3000/api/auth/getUserInfo

# 预期响应包含所有新字段
# user_role, price_rate, total_recharge, create_time
```

#### 前端功能测试
1. 登录系统
2. 查看个人中心 - 验证字段显示
3. 访问用户管理 - 验证列表数据
4. 创建新用户 - 验证表单提交
5. 查看订单列表 - 验证JSON字段

## 常见问题

### Q: 迁移后出现字段undefined错误
**A**: 检查API响应是否包含新字段，确保后端已更新字段映射。

### Q: 角色权限判断失效
**A**: 确保使用数值类型的 `role` 字段进行权限判断，而非字符串类型的 `user_role`。

### Q: 时间显示异常
**A**: 检查是否使用了新的时间字段名 `create_time` 和 `update_time`。

### Q: 费率计算错误
**A**: 确保使用 `price_rate` 字段而非 `userRate`，注意API响应中的字段映射。

## 回滚方案

如果迁移过程中出现问题，可以按以下步骤回滚：

1. **代码回滚**: 使用Git恢复到迁移前的版本
2. **数据库回滚**: 数据库结构未变更，无需回滚
3. **配置回滚**: 恢复原有的环境变量配置
4. **服务重启**: 重启前后端服务

## 技术支持

如果在迁移过程中遇到问题，请参考：
- [数据库字段映射文档](../server/database/FIELD_MAPPING.md)
- [API接口规范文档](../server/docs/api-specification.md)
- [前端组件数据绑定规范](../src/docs/COMPONENT_DATA_BINDING.md)
