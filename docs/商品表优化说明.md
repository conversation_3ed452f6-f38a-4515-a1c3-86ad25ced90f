# 商品表结构优化说明

## 优化目标
1. 简化字段结构，移除重复和冗余字段
2. 统一命名规范，与原项目保持一致
3. 合并功能相同的字段
4. 提高查询性能和维护性

## 字段映射关系

### 保留的核心字段
| 新字段名 | 原字段名 | 类型 | 说明 |
|---------|---------|------|------|
| `cid` | `product_id` | INT(11) | 商品主键ID |
| `name` | `product_name` | VARCHAR(255) | 商品名称 |
| `sort` | `sort_order` | FLOAT | 排序权重 |
| `price` | `price` | DECIMAL(10,2) | 销售价格 |
| `cost_price` | `cost_price` | DECIMAL(10,2) | 成本价格 |
| `content` | `description` | VARCHAR(500) | 商品说明 |

### 分类字段
| 新字段名 | 原字段名 | 类型 | 说明 |
|---------|---------|------|------|
| `fenlei` | `fenlei` | VARCHAR(11) | 主分类ID |
| `fenlei1` | `fenlei1` | VARCHAR(11) | 精确分类ID |

### 统一的对接字段
| 新字段名 | 原字段名 | 类型 | 说明 |
|---------|---------|------|------|
| `platform_cid` | `platform_29_cid`, `docking_cid`, `query_cid` | VARCHAR(255) | **统一的平台商品CID** |
| `getnoun` | `getnoun` | VARCHAR(255) | 查询参数 |
| `noun` | `noun` | VARCHAR(255) | 对接参数 |

### 服务商配置
| 新字段名 | 原字段名 | 类型 | 说明 |
|---------|---------|------|------|
| `provider_id` | `provider_id` | INT(11) | 主服务商ID |
| `query_provider` | `query_provider` | VARCHAR(255) | 查课服务商 |
| `docking_provider` | `docking_provider` | VARCHAR(255) | 下单服务商 |

### 功能开关
| 新字段名 | 原字段名 | 类型 | 说明 |
|---------|---------|------|------|
| `wck` | `wck` | INT(11) | 是否支持无查课 |
| `kcid` | `kcid` | INT(11) | 是否需要课程ID |
| `api` | `api` | INT(11) | API对接开关 |
| `nocheck` | `nocheck` | INT(11) | 跳过检查标志 |

### 其他字段
| 新字段名 | 原字段名 | 类型 | 说明 |
|---------|---------|------|------|
| `yunsuan` | `yunsuan` | VARCHAR(255) | 代理费率运算公式 |
| `status` | `status` | INT(11) | 商品状态 |
| `addtime` | `create_time` | VARCHAR(255) | 添加时间（字符串格式） |

## 移除的冗余字段

### 重复的CID字段
- ❌ `query_cid` - 合并到 `platform_cid`
- ❌ `docking_cid` - 合并到 `platform_cid`
- ❌ `platform_29_cid` - 合并到 `platform_cid`

### 29平台专用字段
- ❌ `platform_29_name` - 使用 `name` 字段
- ❌ `platform_29_price` - 使用 `price` 字段
- ❌ `platform_29_category` - 使用 `fenlei` 字段
- ❌ `is_29_synced` - 不再需要同步标志
- ❌ `sync_time` - 不再需要同步时间

### 其他冗余字段
- ❌ `category_id` - 使用原项目的 `fenlei` 字段
- ❌ `platform_type` - 通过 `provider_id` 确定
- ❌ `service_type` - 固定为代刷服务
- ❌ `upstream_order_id` - 订单相关，不属于商品属性
- ❌ `query_platform` - 使用 `query_provider`
- ❌ `docking_config` - 配置信息移到服务商表

## 关键优化点

### 1. 统一CID字段
```sql
-- 之前：多个重复字段
platform_29_cid, docking_cid, query_cid

-- 之后：统一字段
platform_cid  -- 存储29平台CID或其他平台的商品标识
```

### 2. 简化分类系统
```sql
-- 保留原项目的分类方式
fenlei   -- 主分类
fenlei1  -- 精确分类
```

### 3. 服务商配置分离
```sql
-- 商品表只保留服务商引用
provider_id      -- 主服务商
query_provider   -- 查课服务商（可选）
docking_provider -- 下单服务商（可选）
```

## 代码修改建议

### 1. 更新查课接口
```typescript
// 使用统一的platform_cid字段
const platformCid = product.platform_cid;
```

### 2. 更新商品管理
```typescript
// 简化商品查询
SELECT cid, name, price, platform_cid, fenlei, status 
FROM fd_product 
WHERE status = 1
```

### 3. 更新29平台对接
```typescript
// 统一使用platform_cid
platform: product.platform_cid
```

## 迁移步骤

1. **备份数据** - 创建 `fd_product_backup` 表
2. **创建新表** - 执行 `fd_product_new` 创建语句
3. **数据迁移** - 运行数据迁移脚本
4. **验证数据** - 检查迁移结果
5. **更新代码** - 修改相关的查询和接口
6. **替换表** - 重命名表完成迁移

## 性能优化

### 新增索引
- `idx_fenlei` - 分类查询优化
- `idx_status` - 状态筛选优化
- `idx_sort` - 排序优化
- `idx_platform_cid` - 平台CID查询优化

### 外键约束
- `provider_id` 关联 `fd_provider` 表，确保数据一致性
