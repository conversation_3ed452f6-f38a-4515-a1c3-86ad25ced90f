# 老项目迁移指南

## 项目概述

### 老项目架构分析
- **项目名称**: 青卡网课管理系统
- **数据库**: old (MySQL)
- **表前缀**: qingka_wangke_
- **核心业务**: 多平台网课代刷服务

### 新项目目标
- **项目名称**: SoybeanAdmin 企业级管理系统
- **数据库**: newfd (MySQL 8.4.0)
- **表前缀**: fd_
- **核心业务**: 企业级管理系统 + 网课业务模块

## 数据库架构对比分析

### 老项目表结构分析

#### 1. 用户系统 (qingka_wangke_user)
**核心字段**:
- `uid`: 用户ID (主键)
- `user`: 用户名
- `pass`: 密码
- `money`: 余额
- `yqm`: 邀请码
- `addprice`: 用户费率（老项目真实用户等级，最高0.2级，商品乘费率即为商品最终价格）
- `grade`: 用户等级 （老项目已弃用字段，不同费率即不同等级）
- `active`: 激活状态

**业务特点**:
- 支持邀请码系统
- 多种余额字段 (money, money1, zcz)
- 用户等级和权限管理
- QQ登录集成

#### 2. 订单系统 (qingka_wangke_order)
**核心字段**:
- `oid`: 订单ID (主键)
- `uid`: 用户ID
- `cid`: 课程ID
- `hid`: 货源ID
- `ptname`: 平台名称
- `user/pass`: 学习平台账号密码
- `status`: 订单状态
- `fees`: 费用

**业务特点**:
- 完整的订单生命周期管理
- 支持多平台课程
- 货源对接机制
- 详细的课程信息记录

#### 3. 课程管理 (qingka_wangke_class)
**核心字段**:
- `cid`: 课程ID (主键)
- `name`: 课程名称
- `price`: 价格
- `queryplat`: 查询平台
- `docking`: 对接状态
- `fenlei`: 分类ID

**业务特点**:
- 课程分类管理
- 价格体系
- 平台对接配置
- 状态控制

#### 4. 货源管理 (qingka_wangke_huoyuan)
**核心字段**:
- `hid`: 货源ID (主键)
- `pt`: 平台类型
- `name`: 货源名称
- `url`: API地址
- `token`: 认证令牌
- `money`: 余额

**业务特点**:
- 多货源对接
- API配置管理
- 余额监控
- 状态控制

#### 5. 系统配置 (qingka_wangke_config)
**核心字段**:
- `v`: 配置键
- `k`: 配置值

**业务特点**:
- 键值对配置存储
- 系统参数管理

## 迁移映射方案

### 表结构映射

| 老项目表 | 新项目表 | 映射关系 | 备注 |
|---------|---------|---------|------|
| qingka_wangke_user | fd_user | 用户基础信息 | 需要字段映射和数据清洗 |
| qingka_wangke_order | fd_order | 订单信息 | 保留核心业务字段 |
| qingka_wangke_class | fd_product | 商品信息 | 新建商品管理表 |
| qingka_wangke_fenlei | fd_category | 分类信息 | 新建分类管理表 |
| qingka_wangke_huoyuan | fd_provider | 货源/服务商 | 映射到服务商表 |
| qingka_wangke_config | fd_config | 系统配置 | 整合到新配置系统 |

### 字段映射详情

#### 用户表映射 (qingka_wangke_user → fd_user)
```sql
-- 字段映射关系
uid → user_id
user → username
pass → password (需要重新加密)
money → balance
yqm → invite_code
addprice → user_rate (用户费率，最高0.2级)
grade → 废弃字段 (不迁移)
active → status
addtime → create_time
```

**重要业务逻辑说明**:
- `addprice`: 用户费率（老项目真实用户等级，最高0.2级，商品乘费率即为商品最终价格）
- `grade`: 用户等级（老项目已弃用字段，不同费率即不同等级）
- **定价公式**: 商品基础价格 × 用户费率(addprice) = 用户实际支付价格

#### 订单表映射 (qingka_wangke_order → fd_order)
```sql
-- 字段映射关系
oid → order_id
uid → user_id
cid → course_id
hid → provider_id
ptname → platform_name
user → platform_account
pass → platform_password
kcname → course_name
fees → amount
status → status (需要状态转换)
addtime → create_time
```

## 新项目功能完善方案

### 1. 新增业务模块

#### 商品管理模块
**表结构**: `fd_product`
```sql
CREATE TABLE fd_product (
  product_id INT PRIMARY KEY AUTO_INCREMENT,
  product_name VARCHAR(255) NOT NULL,
  category_id INT NOT NULL,
  platform_type VARCHAR(100) NOT NULL,
  price DECIMAL(10,2) NOT NULL,
  cost_price DECIMAL(10,2) NOT NULL,
  description TEXT,
  status TINYINT DEFAULT 1,
  sort_order INT DEFAULT 0,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_category (category_id),
  INDEX idx_platform (platform_type),
  INDEX idx_status (status)
);
```

#### 分类管理模块
**表结构**: `fd_category`
```sql
CREATE TABLE fd_category (
  category_id INT PRIMARY KEY AUTO_INCREMENT,
  category_name VARCHAR(100) NOT NULL,
  parent_id INT DEFAULT 0,
  sort_order INT DEFAULT 0,
  status TINYINT DEFAULT 1,
  description TEXT,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_parent (parent_id),
  INDEX idx_status (status)
);
```

#### 服务商管理模块
**表结构**: `fd_provider`
```sql
CREATE TABLE fd_provider (
  provider_id INT PRIMARY KEY AUTO_INCREMENT,
  provider_name VARCHAR(255) NOT NULL,
  platform_type VARCHAR(100) NOT NULL,
  api_url VARCHAR(500),
  api_config JSON,
  balance DECIMAL(10,2) DEFAULT 0.00,
  status TINYINT DEFAULT 1,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_platform (platform_type),
  INDEX idx_status (status)
);
```

#### 订单管理模块
**表结构**: `fd_order`
```sql
CREATE TABLE fd_order (
  order_id INT PRIMARY KEY AUTO_INCREMENT,
  order_no VARCHAR(32) UNIQUE NOT NULL,
  user_id INT NOT NULL,
  course_id INT NOT NULL,
  provider_id INT NOT NULL,
  platform_name VARCHAR(100) NOT NULL,
  platform_account VARCHAR(255) NOT NULL,
  platform_password VARCHAR(255) NOT NULL,
  course_name VARCHAR(255) NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  status TINYINT DEFAULT 1,
  progress INT DEFAULT 0,
  remarks TEXT,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  INDEX idx_user (user_id),
  INDEX idx_course (course_id),
  INDEX idx_provider (provider_id),
  INDEX idx_status (status),
  INDEX idx_create_time (create_time)
);
```

### 2. 业务逻辑完善

#### 订单状态管理
```typescript
enum OrderStatus {
  PENDING = 1,      // 待处理
  PROCESSING = 2,   // 处理中
  COMPLETED = 3,    // 已完成
  FAILED = 4,       // 失败
  CANCELLED = 5,    // 已取消
  REFUNDED = 6      // 已退款
}
```

#### 用户角色权限
```typescript
enum UserRole {
  SUPER = 'super',  // 超级管理员
  ADMIN = 'admin',  // 管理员
  AGENT = 'agent',  // 代理商
  USER = 'user'     // 普通用户
}
```

#### 平台类型定义
```typescript
enum PlatformType {
  ZHIHUISHU = 'zhihuishu',      // 智慧树
  CHAOXING = 'chaoxing',        // 超星
  XUETANGX = 'xuetangx',        // 学堂在线
  ICOURSE163 = 'icourse163',    // 中国大学MOOC
  GAOXIAOBANG = 'gaoxiaobang'   // 高校邦
}
```

## 迁移实施计划

### 阶段一：基础架构搭建 (1-2周)
1. **数据库设计**
   - 创建新项目表结构
   - 建立索引和约束
   - 设计数据迁移脚本

2. **后端API开发**
   - 课程管理API
   - 分类管理API
   - 订单管理API
   - 服务商管理API

3. **前端页面开发**
   - 课程管理界面
   - 订单管理界面
   - 分类管理界面
   - 服务商管理界面

### 阶段二：数据迁移 (1周)
1. **数据清洗**
   - 用户数据清洗和验证
   - 订单数据整理
   - 课程数据标准化

2. **数据迁移**
   - 批量数据导入
   - 数据完整性验证
   - 关联关系建立

3. **数据验证**
   - 迁移数据核对
   - 业务逻辑测试
   - 性能测试

### 阶段三：功能测试 (1周)
1. **功能测试**
   - 用户注册登录
   - 订单创建流程
   - 支付集成测试

2. **性能优化**
   - 数据库查询优化
   - 接口响应优化
   - 前端加载优化

3. **安全测试**
   - 权限验证测试
   - 数据安全测试
   - API安全测试

### 阶段四：上线部署 (1周)
1. **生产环境部署**
   - 服务器配置
   - 数据库部署
   - 应用部署

2. **监控配置**
   - 系统监控
   - 日志监控
   - 性能监控

3. **用户培训**
   - 管理员培训
   - 用户手册编写
   - 技术文档完善

## 风险评估与应对

### 技术风险
1. **数据迁移风险**
   - 风险：数据丢失或损坏
   - 应对：多重备份，分批迁移

2. **性能风险**
   - 风险：新系统性能不达标
   - 应对：压力测试，性能优化

3. **兼容性风险**
   - 风险：新旧系统数据不兼容
   - 应对：充分测试，数据验证

### 业务风险
1. **服务中断风险**
   - 风险：迁移期间服务不可用
   - 应对：分阶段迁移，灰度发布

2. **用户体验风险**
   - 风险：新系统用户体验差
   - 应对：用户测试，界面优化

## 成功标准

### 技术指标
- 数据迁移完整性 > 99.9%
- 系统响应时间 < 2秒
- 系统可用性 > 99.5%

### 业务指标
- 用户满意度 > 90%
- 订单处理效率提升 > 30%
- 系统维护成本降低 > 50%

## 后续优化方向

### 功能扩展
1. **移动端支持**
2. **API开放平台**
3. **数据分析报表**
4. **智能推荐系统**

### 技术升级
1. **微服务架构**
2. **容器化部署**
3. **自动化运维**
4. **AI集成应用**

## 迁移执行命令

### 数据库迁移
```bash
# 1. 备份老项目数据
mysqldump -h localhost -u old -pBAEZaZj4fcRyGjTT old > old_backup.sql

# 2. 执行迁移脚本
mysql -h localhost -u newfd -pjx7KFPLnEsr4fzi7 newfd < server/scripts/migration.sql

# 3. 验证迁移结果
mysql -h localhost -u newfd -pjx7KFPLnEsr4fzi7 newfd -e "
SELECT table_name, table_rows
FROM information_schema.tables
WHERE table_schema = 'newfd' AND table_name LIKE 'fd_%';"
```

### 应用部署
```bash
# 1. 安装依赖
cd /www/wwwroot/FDnew/SoybeanAdmin
pnpm install
cd server && npm install

# 2. 构建前端
pnpm build

# 3. 启动服务
npm run start:prod
```

## 迁移检查清单

### 数据迁移检查
- [ ] 用户数据完整性
- [ ] 订单数据完整性
- [ ] 课程数据完整性
- [ ] 分类数据完整性
- [ ] 服务商数据完整性
- [ ] 配置数据完整性

### 功能测试检查
- [ ] 用户登录注册
- [ ] 课程管理功能
- [ ] 订单创建流程
- [ ] 支付集成测试
- [ ] 权限控制测试
- [ ] API接口测试

### 性能测试检查
- [ ] 数据库查询性能
- [ ] 接口响应时间
- [ ] 并发处理能力
- [ ] 内存使用情况
- [ ] CPU使用情况

### 安全测试检查
- [ ] 用户权限验证
- [ ] 数据加密存储
- [ ] API安全防护
- [ ] SQL注入防护
- [ ] XSS攻击防护
