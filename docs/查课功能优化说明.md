# 查课功能优化说明

## 功能概述

本次优化主要解决了多账号查课时课程区分不清、无法快速筛选和批量选择的问题。新增了以下功能：

## 新增功能

### 1. 账号分组显示
- **批量查课模式**：课程按账号分组显示，每个账号下显示其对应的课程
- **账号信息展示**：显示账号的真实姓名、用户名和学校信息
- **统计信息**：显示每个账号的课程数量和选择状态

### 2. 搜索功能
- **关键词搜索**：支持按课程名称、课程ID、教师姓名搜索
- **实时筛选**：输入关键词后实时筛选显示结果
- **一键选择搜索结果**：搜索后可一键选择所有搜索结果中的课程

### 3. 账号筛选
- **账号标签筛选**：点击账号标签可筛选显示该账号的课程
- **课程数量显示**：每个账号标签显示该账号的课程数量
- **快速切换**：支持快速切换不同账号的课程查看

### 4. 批量选择优化
- **账号级别选择**：可以按账号批量选择/取消选择该账号下的所有课程
- **选择状态指示**：显示每个账号的选择状态（全选/部分选择/未选择）
- **智能去重**：相同课程名称在不同账号下被视为不同课程，避免冲突

## 使用方法

### 单个账号查课
1. 选择商品和输入账号信息
2. 点击"查询课程"
3. 在课程列表中勾选需要的课程
4. 使用搜索功能快速定位特定课程

### 批量账号查课
1. 切换到"批量查询"模式
2. 输入多个账号信息（格式：学校 账号 密码，每行一个）
3. 点击"批量查询"
4. 查看按账号分组的课程结果
5. 使用以下功能进行筛选和选择：

#### 搜索功能
- 在搜索框输入关键词（课程名称、ID、教师）
- 点击"一键选择"按钮选择所有搜索结果

#### 账号筛选
- 点击账号标签筛选显示该账号的课程
- 点击"清除筛选"显示所有账号的课程

#### 批量选择
- 点击账号标题的复选框选择/取消该账号的所有课程
- 点击"全选"选择当前筛选结果中的所有课程
- 点击"清空"取消所有选择

## 界面说明

### 搜索工具栏
- **搜索框**：输入关键词进行搜索
- **一键选择按钮**：选择所有搜索结果（仅在有搜索结果时显示）

### 账号筛选标签（批量模式）
- **账号标签**：显示账号名称和课程数量
- **激活状态**：选中的筛选标签会高亮显示
- **清除筛选**：红色标签用于清除当前筛选

### 统计信息
- **总课程数**：显示查课找到的总课程数
- **筛选后数量**：显示当前筛选条件下的课程数
- **已选择数量**：显示已选择的课程数
- **账号数量**：显示涉及的账号数量（批量模式）

### 账号分组（批量模式）
- **账号标题**：显示账号信息和选择状态
- **选择指示器**：复选框显示该账号的选择状态
  - 空白：未选择任何课程
  - 减号：部分选择
  - 勾选：全部选择
- **统计标签**：显示已选择/总数量

## 技术实现

### 前端优化
- 使用Vue 3 Composition API重构组件逻辑
- 优化数据结构，支持账号和课程的关联
- 实现响应式搜索和筛选
- 优化UI交互和用户体验

### 数据处理
- 后端返回的课程数据包含账号信息
- 前端按账号分组处理课程数据
- 实现智能的课程去重和选择逻辑

### 性能优化
- 使用计算属性缓存筛选结果
- 优化大量课程数据的渲染性能
- 实现虚拟滚动（如需要）

## 注意事项

1. **课程唯一性**：相同课程名称在不同账号下被视为不同的课程
2. **密码安全**：批量查课结果中不显示账号密码
3. **性能考虑**：大量课程时建议使用搜索和筛选功能
4. **响应式设计**：界面适配移动端显示

## 后续优化建议

1. 添加课程收藏功能
2. 支持自定义筛选条件
3. 添加课程进度排序
4. 实现查课历史记录
5. 支持导出课程列表
