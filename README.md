# 多平台在线课程产品管理系统

基于 SoybeanAdmin ElementPlus 的在线课程管理平台，采用 Vue3 + TypeScript + Element Plus 技术栈。

## 🚀 快速开始

```bash
# 智能启动开发环境
./dev.sh

# 查看帮助
./dev.sh help
```

## 📋 主要功能

- 在线课程管理
- 用户权限管理
- 授权系统集成
- 多平台支持

## 🔧 技术栈

- **前端**: Vue3 + TypeScript + Element Plus + Pinia
- **后端**: Node.js + Express + MySQL
- **构建**: Vite + UnoCSS
- **代码规范**: ESLint + Prettier

## 📚 文档

- [开发指南](./docs/development-guide.md)
- [进程管理](./docs/process-management.md)

## 📄 许可证

MIT License
