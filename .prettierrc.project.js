/** 项目专用Prettier配置 基于SoybeanAdmin的基础配置，添加项目特定格式化规则 */

module.exports = {
  // 基础配置
  printWidth: 120, // 行宽
  tabWidth: 2, // 缩进宽度
  useTabs: false, // 使用空格而不是tab
  semi: true, // 语句末尾添加分号
  singleQuote: true, // 使用单引号
  quoteProps: 'as-needed', // 对象属性引号
  jsxSingleQuote: true, // JSX中使用单引号
  trailingComma: 'none', // 尾随逗号
  bracketSpacing: true, // 对象字面量的大括号间添加空格
  bracketSameLine: false, // 多行JSX元素的>放在下一行
  arrowParens: 'avoid', // 箭头函数参数括号
  rangeStart: 0, // 格式化范围开始
  rangeEnd: Infinity, // 格式化范围结束
  requirePragma: false, // 不需要pragma
  insertPragma: false, // 不插入pragma
  proseWrap: 'preserve', // 散文换行
  htmlWhitespaceSensitivity: 'css', // HTML空白敏感性
  vueIndentScriptAndStyle: false, // Vue文件中script和style标签不缩进
  endOfLine: 'lf', // 行尾序列
  embeddedLanguageFormatting: 'auto', // 嵌入式语言格式化
  singleAttributePerLine: false, // 单个属性不强制换行

  // 项目特定配置
  overrides: [
    {
      files: '*.vue',
      options: {
        printWidth: 100, // Vue文件行宽稍短
        htmlWhitespaceSensitivity: 'ignore', // Vue模板忽略空白敏感性
        vueIndentScriptAndStyle: true // Vue文件中script和style标签缩进
      }
    },
    {
      files: '*.json',
      options: {
        printWidth: 80,
        tabWidth: 2
      }
    },
    {
      files: '*.md',
      options: {
        printWidth: 80,
        proseWrap: 'always',
        tabWidth: 2
      }
    },
    {
      files: '*.yaml',
      options: {
        tabWidth: 2,
        singleQuote: false
      }
    },
    {
      files: '*.yml',
      options: {
        tabWidth: 2,
        singleQuote: false
      }
    },
    {
      files: ['*.css', '*.scss', '*.less'],
      options: {
        printWidth: 100,
        singleQuote: false
      }
    },
    {
      files: 'server/**/*.ts',
      options: {
        printWidth: 120, // 后端文件可以更长
        semi: true,
        singleQuote: true,
        trailingComma: 'es5' // 后端使用ES5风格的尾随逗号
      }
    }
  ]
};
