/**
 * SoybeanAdmin项目规范合规性检查脚本
 * 确保项目开发完全符合项目规范要求
 */

const mysql = require('./server/node_modules/mysql2/promise');
const fs = require('node:fs').promises;
const path = require('node:path');

// 项目规范配置
const COMPLIANCE_CONFIG = {
  database: {
    host: 'localhost',
    user: 'newfd',
    password: 'jx7KFPLnEsr4fzi7',
    database: 'newfd',
    charset: 'utf8mb4'
  },
  expectedPort: 5959,
  adminCredentials: {
    username: 'admin',
    password: '123456'
  },
  requiredTablePrefix: 'fd_',
  checkResults: []
};

// 记录检查结果
function recordCheck(category, name, status, message, details = null) {
  const result = {
    category,
    name,
    status, // 'pass', 'fail', 'warning'
    message,
    details,
    timestamp: new Date().toLocaleString('zh-CN')
  };

  COMPLIANCE_CONFIG.checkResults.push(result);

  const statusIcon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
  console.log(`${statusIcon} [${category}] ${name}: ${message}`);

  if (details) {
    console.log(`   详情: ${JSON.stringify(details, null, 2)}`);
  }
}

// 检查端口配置
async function checkPortConfiguration() {
  try {
    // 检查后端端口配置
    const serverEnvPath = 'server/.env';
    const serverEnvContent = await fs.readFile(serverEnvPath, 'utf8');

    const portMatch = serverEnvContent.match(/PORT=(\d+)/);
    const actualPort = portMatch ? Number.parseInt(portMatch[1]) : null;

    if (actualPort === COMPLIANCE_CONFIG.expectedPort) {
      recordCheck('端口配置', '后端端口', 'pass', `端口配置正确: ${actualPort}`);
    } else {
      recordCheck(
        '端口配置',
        '后端端口',
        'fail',
        `端口配置错误: 期望${COMPLIANCE_CONFIG.expectedPort}, 实际${actualPort}`
      );
    }

    return true;
  } catch (error) {
    recordCheck('端口配置', '端口检查', 'fail', `端口配置检查失败: ${error.message}`);
    return false;
  }
}

// 检查管理员账户
async function checkAdminAccount() {
  try {
    const connection = await mysql.createConnection(COMPLIANCE_CONFIG.database);

    // 检查管理员账户
    const [adminUsers] = await connection.execute(
      "SELECT user_id, username, user_role, role FROM fd_user WHERE username = ? AND (role = 1 OR user_role IN ('admin', 'super_admin'))",
      [COMPLIANCE_CONFIG.adminCredentials.username]
    );

    if (adminUsers.length > 0) {
      recordCheck('用户管理', '管理员账户', 'pass', `管理员账户存在: ${adminUsers[0].username}`, {
        userId: adminUsers[0].user_id,
        userRole: adminUsers[0].user_role,
        role: adminUsers[0].role
      });
    } else {
      recordCheck('用户管理', '管理员账户', 'fail', '管理员账户不存在或配置错误');
    }

    await connection.end();
    return true;
  } catch (error) {
    recordCheck('用户管理', '管理员账户检查', 'fail', `管理员账户检查失败: ${error.message}`);
    return false;
  }
}

// 检查数据库表命名规范
async function checkTableNamingConvention() {
  try {
    const connection = await mysql.createConnection(COMPLIANCE_CONFIG.database);

    // 获取所有表名
    const [tables] = await connection.execute('SHOW TABLES');
    const tableNames = tables.map(row => Object.values(row)[0]);

    // 检查表名前缀
    const nonCompliantTables = tableNames.filter(
      tableName => !tableName.startsWith(COMPLIANCE_CONFIG.requiredTablePrefix)
    );

    if (nonCompliantTables.length === 0) {
      recordCheck(
        '数据库规范',
        '表命名规范',
        'pass',
        `所有表都使用正确的前缀 '${COMPLIANCE_CONFIG.requiredTablePrefix}'`,
        {
          totalTables: tableNames.length,
          compliantTables: tableNames.length
        }
      );
    } else {
      recordCheck('数据库规范', '表命名规范', 'fail', `发现不符合命名规范的表`, {
        totalTables: tableNames.length,
        nonCompliantTables,
        requiredPrefix: COMPLIANCE_CONFIG.requiredTablePrefix
      });
    }

    await connection.end();
    return true;
  } catch (error) {
    recordCheck('数据库规范', '表命名检查', 'fail', `表命名规范检查失败: ${error.message}`);
    return false;
  }
}

// 检查环境变量文件
async function checkEnvironmentFiles() {
  try {
    const envFiles = [
      { path: '.env', name: '前端环境变量' },
      { path: 'server/.env', name: '后端环境变量' }
    ];

    let allFilesExist = true;

    for (const envFile of envFiles) {
      try {
        await fs.access(envFile.path);
        const content = await fs.readFile(envFile.path, 'utf8');

        // 检查关键配置项
        const hasRequiredConfig = envFile.path.includes('server')
          ? content.includes('DB_HOST') && content.includes('JWT_SECRET')
          : content.includes('VITE_APP_TITLE');

        if (hasRequiredConfig) {
          recordCheck('环境配置', envFile.name, 'pass', `${envFile.name}配置完整`);
        } else {
          recordCheck('环境配置', envFile.name, 'warning', `${envFile.name}可能缺少关键配置`);
        }
      } catch {
        recordCheck('环境配置', envFile.name, 'fail', `${envFile.name}文件不存在`);
        allFilesExist = false;
      }
    }

    return allFilesExist;
  } catch (error) {
    recordCheck('环境配置', '环境变量检查', 'fail', `环境变量检查失败: ${error.message}`);
    return false;
  }
}

// 检查SoybeanJS架构规范
async function checkSoybeanArchitecture() {
  try {
    const requiredDirs = ['src/router', 'src/views', 'src/service', 'src/store', 'src/components', 'src/utils'];

    let allDirsExist = true;

    for (const dir of requiredDirs) {
      try {
        await fs.access(dir);
        recordCheck('架构规范', `目录结构-${dir}`, 'pass', `目录存在: ${dir}`);
      } catch {
        recordCheck('架构规范', `目录结构-${dir}`, 'fail', `目录不存在: ${dir}`);
        allDirsExist = false;
      }
    }

    // 检查路由配置
    try {
      await fs.access('src/router/elegant');
      recordCheck('架构规范', 'Elegant路由', 'pass', 'Elegant路由配置存在');
    } catch {
      recordCheck('架构规范', 'Elegant路由', 'warning', 'Elegant路由配置可能不完整');
    }

    return allDirsExist;
  } catch (error) {
    recordCheck('架构规范', 'SoybeanJS架构检查', 'fail', `架构检查失败: ${error.message}`);
    return false;
  }
}

// 检查授权系统配置
async function checkLicenseSystemConfig() {
  try {
    // 检查前端授权配置
    const frontendEnv = await fs.readFile('.env', 'utf8');
    const hasLicenseConfig = frontendEnv.includes('VITE_LICENSE_API_URL');

    // 检查后端授权配置
    const backendEnv = await fs.readFile('server/.env', 'utf8');
    const hasBackendLicenseConfig = backendEnv.includes('LICENSE_API_URL');

    if (hasLicenseConfig && hasBackendLicenseConfig) {
      recordCheck('授权系统', '授权配置', 'pass', '授权系统配置完整');
    } else {
      recordCheck('授权系统', '授权配置', 'warning', '授权系统配置可能不完整');
    }

    // 检查授权表
    const connection = await mysql.createConnection(COMPLIANCE_CONFIG.database);
    try {
      const [licenseTable] = await connection.execute('DESCRIBE fd_license_info');
      recordCheck('授权系统', '授权数据表', 'pass', '授权数据表存在');
    } catch {
      recordCheck('授权系统', '授权数据表', 'warning', '授权数据表可能不存在');
    }
    await connection.end();

    return true;
  } catch (error) {
    recordCheck('授权系统', '授权系统检查', 'fail', `授权系统检查失败: ${error.message}`);
    return false;
  }
}

// 检查用户注册规范
async function checkUserRegistrationRules() {
  try {
    const connection = await mysql.createConnection(COMPLIANCE_CONFIG.database);

    // 检查用户表结构
    const [userColumns] = await connection.execute('DESCRIBE fd_user');
    const columnNames = userColumns.map(col => col.Field);

    const hasInviteCode = columnNames.includes('invite_code');
    const hasSid = columnNames.includes('sid');

    if (hasInviteCode && hasSid) {
      recordCheck('用户注册', '注册规范', 'pass', '用户注册字段配置正确', {
        hasInviteCode,
        hasSid
      });
    } else {
      recordCheck('用户注册', '注册规范', 'fail', '用户注册字段配置不完整', {
        hasInviteCode,
        hasSid,
        missingFields: [!hasInviteCode && 'invite_code', !hasSid && 'sid'].filter(Boolean)
      });
    }

    await connection.end();
    return true;
  } catch (error) {
    recordCheck('用户注册', '注册规范检查', 'fail', `注册规范检查失败: ${error.message}`);
    return false;
  }
}

// 生成合规性报告
function generateComplianceReport() {
  const totalChecks = COMPLIANCE_CONFIG.checkResults.length;
  const passedChecks = COMPLIANCE_CONFIG.checkResults.filter(r => r.status === 'pass').length;
  const failedChecks = COMPLIANCE_CONFIG.checkResults.filter(r => r.status === 'fail').length;
  const warningChecks = COMPLIANCE_CONFIG.checkResults.filter(r => r.status === 'warning').length;

  console.log(`\n${'='.repeat(70)}`);
  console.log('📋 SoybeanAdmin项目规范合规性报告');
  console.log('='.repeat(70));
  console.log(`📊 检查统计:`);
  console.log(`   总检查项: ${totalChecks}`);
  console.log(`   ✅ 通过: ${passedChecks}`);
  console.log(`   ❌ 失败: ${failedChecks}`);
  console.log(`   ⚠️  警告: ${warningChecks}`);
  console.log(`   📈 合规率: ${((passedChecks / totalChecks) * 100).toFixed(1)}%`);

  // 按类别分组显示结果
  const categories = [...new Set(COMPLIANCE_CONFIG.checkResults.map(r => r.category))];

  console.log('\n📋 分类检查结果:');
  categories.forEach(category => {
    const categoryResults = COMPLIANCE_CONFIG.checkResults.filter(r => r.category === category);
    const categoryPassed = categoryResults.filter(r => r.status === 'pass').length;
    const categoryTotal = categoryResults.length;

    console.log(`\n🔍 ${category} (${categoryPassed}/${categoryTotal})`);
    categoryResults.forEach(result => {
      const statusIcon = result.status === 'pass' ? '✅' : result.status === 'fail' ? '❌' : '⚠️';
      console.log(`   ${statusIcon} ${result.name}: ${result.message}`);
    });
  });

  console.log(`\n${'='.repeat(70)}`);

  // 总体评估
  if (failedChecks === 0 && warningChecks <= 2) {
    console.log('🎉 项目完全符合SoybeanAdmin规范要求！');
  } else if (failedChecks <= 2) {
    console.log('⚠️  项目基本符合规范，少数问题需要修复');
  } else {
    console.log('❌ 项目存在多个规范问题，需要重点修复');
  }

  return {
    summary: {
      total: totalChecks,
      passed: passedChecks,
      failed: failedChecks,
      warnings: warningChecks,
      complianceRate: ((passedChecks / totalChecks) * 100).toFixed(1)
    },
    results: COMPLIANCE_CONFIG.checkResults,
    categories: categories.map(category => ({
      name: category,
      results: COMPLIANCE_CONFIG.checkResults.filter(r => r.category === category)
    }))
  };
}

// 主检查函数
async function runComplianceCheck() {
  console.log('🔍 开始SoybeanAdmin项目规范合规性检查...\n');

  // 运行各项检查
  await checkPortConfiguration();
  await checkAdminAccount();
  await checkTableNamingConvention();
  await checkEnvironmentFiles();
  await checkSoybeanArchitecture();
  await checkLicenseSystemConfig();
  await checkUserRegistrationRules();

  // 生成报告
  const report = generateComplianceReport();

  // 保存报告
  try {
    await fs.writeFile('compliance-report.json', JSON.stringify(report, null, 2), 'utf8');
    console.log('\n📄 合规性报告已保存到 compliance-report.json');
  } catch (error) {
    console.log('\n⚠️  无法保存合规性报告:', error.message);
  }

  return report;
}

// 运行检查
runComplianceCheck().catch(error => {
  console.error('❌ 合规性检查运行失败:', error);
  process.exit(1);
});
