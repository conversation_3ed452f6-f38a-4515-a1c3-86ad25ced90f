import CryptoJS from 'crypto-js';

export class Crypto<T extends object> {
  /** Secret */
  secret: string;

  constructor(secret: string) {
    this.secret = secret;
  }

  encrypt(data: T): string {
    const dataString = JSON.stringify(data);
    const encrypted = CryptoJS.AES.encrypt(dataString, this.secret);
    return encrypted.toString();
  }

  decrypt(encrypted: string) {
    const decrypted = CryptoJS.AES.decrypt(encrypted, this.secret);
    const dataString = decrypted.toString(CryptoJS.enc.Utf8);
    try {
      return JSON.parse(dataString) as T;
    } catch {
      // avoid parse error
      return null;
    }
  }
}

/** 授权安全工具类 */
export class LicenseCrypto {
  private readonly systemSecret: string;
  private readonly licenseSecret: string;

  constructor(systemSecret: string, licenseSecret: string) {
    this.systemSecret = systemSecret;
    this.licenseSecret = licenseSecret;
  }

  /** 生成授权签名 */
  generateSignature(licenseKey: string, domain: string, timestamp: number): string {
    const data = `${licenseKey}:${domain}:${timestamp}`;
    return CryptoJS.HmacSHA256(data, this.systemSecret).toString();
  }

  /** 验证授权签名 */
  verifySignature(licenseKey: string, domain: string, timestamp: number, signature: string): boolean {
    const expectedSignature = this.generateSignature(licenseKey, domain, timestamp);
    return expectedSignature === signature;
  }

  /** 生成安全的授权令牌 */
  generateSecureToken(licenseData: any): string {
    const timestamp = Date.now();
    const signature = this.generateSignature(licenseData.licenseKey, licenseData.domain, timestamp);

    const tokenData = {
      ...licenseData,
      timestamp,
      signature
    };

    const crypto = new Crypto(this.licenseSecret);
    return crypto.encrypt(tokenData);
  }

  /** 验证安全授权令牌 */
  verifySecureToken(token: string): { valid: boolean; data?: any; error?: string } {
    try {
      const crypto = new Crypto(this.licenseSecret);
      const tokenData = crypto.decrypt(token);

      if (!tokenData) {
        return { valid: false, error: '令牌解密失败' };
      }

      const { licenseKey, domain, timestamp, signature, ...otherData } = tokenData as any;

      // 验证必要字段
      if (!licenseKey || !domain || !timestamp || !signature) {
        return { valid: false, error: '令牌数据不完整' };
      }

      // 验证签名
      if (!this.verifySignature(licenseKey, domain, timestamp, signature)) {
        return { valid: false, error: '令牌签名验证失败' };
      }

      // 验证时间戳（防止重放攻击，令牌有效期7天）
      const now = Date.now();
      const maxAge = 7 * 24 * 60 * 60 * 1000; // 7天
      if (now - timestamp > maxAge) {
        return { valid: false, error: '令牌已过期' };
      }

      return {
        valid: true,
        data: {
          licenseKey,
          domain,
          timestamp,
          ...otherData
        }
      };
    } catch (error) {
      return { valid: false, error: '令牌验证异常' };
    }
  }

  /** 生成随机盐值 */
  generateSalt(): string {
    return CryptoJS.lib.WordArray.random(16).toString();
  }

  /** 哈希密码 */
  hashPassword(password: string, salt: string): string {
    return CryptoJS.PBKDF2(password, salt, {
      keySize: 256 / 32,
      iterations: 10000
    }).toString();
  }
}
