# SoybeanAdmin 宝塔部署指南

## 🚀 一键部署（推荐）

### 第一步：运行部署脚本
```bash
cd /www/wwwroot/FDnew/SoybeanAdmin
chmod +x scripts/deploy-production.sh
bash scripts/deploy-production.sh
```

### 第二步：在宝塔面板创建站点
1. 打开宝塔面板
2. 点击 **网站** → **添加站点**
3. 填写信息：
   - **域名**：`************_6666` （改成您的服务器IP）
   - **根目录**：`/www/wwwroot/FDnew/SoybeanAdmin/dist`
   - **PHP版本**：纯静态（不需要PHP）
   - 不创建数据库和FTP

### 第三步：配置Nginx
1. 在站点列表中找到刚创建的站点
2. 点击 **设置** → **配置文件**
3. 将以下配置**完全替换**原有内容：

```nginx
server {
    listen 6666;
    server_name ************_6666;  # 改成您的IP
    root /www/wwwroot/FDnew/SoybeanAdmin/dist;
    index index.html;

    # API代理 - 让前端能访问后端
    location /api/ {
        proxy_pass http://127.0.0.1:3000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # Vue路由支持 - 让前端路由正常工作
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public";
    }

    # 开启压缩
    gzip on;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml;

    # 安全配置
    location ~ ^/(\.user\.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README\.md) {
        return 404;
    }
}
```

4. 点击 **保存**
5. 重载配置

### 第四步：测试访问
- 打开浏览器访问：`http://************:6666`
- 使用账号：`admin` 密码：`123456` 登录

---

## 🔧 手动部署（如果一键部署失败）

### 1. 构建前端
```bash
cd /www/wwwroot/FDnew/SoybeanAdmin
pnpm install
pnpm build
```

### 2. 部署后端
```bash
cd /www/wwwroot/FDnew/SoybeanAdmin/server
pnpm install
pnpm run build
pm2 start dist/index.js --name soybean-server
pm2 save
```

### 3. 按照上面的步骤配置宝塔站点

---

## 📋 重要概念解释

### SPA（单页应用）
- 您的前端是Vue.js单页应用
- 所有页面都通过一个`index.html`文件加载
- 需要配置`try_files $uri $uri/ /index.html;`让路由正常工作

### API反向代理
- 前端发送的`/api/xxx`请求会自动转发到后端（3000端口）
- 配置：`proxy_pass http://127.0.0.1:3000/;`

### Gzip压缩
- 自动压缩文件，让网站加载更快
- 配置：`gzip on;`

---

## 🚨 常见问题

### 问题1：访问显示404
**解决**：检查站点根目录是否正确指向`/www/wwwroot/FDnew/SoybeanAdmin/dist`

### 问题2：API请求失败
**解决**：检查后端服务是否运行
```bash
pm2 status
pm2 logs soybean-server
```

### 问题3：前端路由不工作
**解决**：确保Nginx配置中有`try_files $uri $uri/ /index.html;`

### 问题4：静态资源加载慢
**解决**：确保开启了gzip压缩和静态文件缓存

---

## 🔄 更新部署

当代码有更新时：
```bash
cd /www/wwwroot/FDnew/SoybeanAdmin
git pull  # 如果使用git
pnpm build  # 重新构建前端
pm2 restart soybean-server  # 重启后端
```

---

## 📞 技术支持

如果遇到问题：
1. 检查宝塔面板的错误日志
2. 查看PM2日志：`pm2 logs soybean-server`
3. 检查Nginx配置：`nginx -t`
4. 确保防火墙开放6666端口
