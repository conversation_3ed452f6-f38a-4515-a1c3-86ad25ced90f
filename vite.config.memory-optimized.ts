import { defineConfig, loadEnv } from 'vite';
import { setupVitePlugins } from './build/plugins';
import { createViteProxy } from './build/config';

export default defineConfig(configEnv => {
  const viteEnv = loadEnv(configEnv.mode, process.cwd()) as unknown as Env.ImportMeta;

  const { VITE_PORT, VITE_PUBLIC_PATH, VITE_USE_PROXY, VITE_PROXY_TYPE } = viteEnv;

  const isOpenProxy = VITE_USE_PROXY === 'Y' && configEnv.command === 'serve';

  return {
    base: VITE_PUBLIC_PATH || '/',
    resolve: {
      alias: {
        '~': __dirname,
        '@': `${__dirname}/src`
      }
    },
    define: {
      PROJECT_BUILD_TIME: JSON.stringify(new Date())
    },
    plugins: setupVitePlugins(viteEnv, new Date().toISOString()),
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "./src/styles/scss/global.scss" as *;`
        }
      }
    },
    server: {
      host: '0.0.0.0',
      port: VITE_PORT,
      open: false,
      proxy: isOpenProxy ? createViteProxy(viteEnv, VITE_PROXY_TYPE) : undefined,
      // 内存优化配置
      hmr: {
        overlay: false // 减少HMR开销
      },
      // 减少文件监听
      watch: {
        ignored: ['**/node_modules/**', '**/dist/**', '**/coverage/**', '**/.git/**', '**/logs/**', '**/temp/**']
      }
    },
    build: {
      reportCompressedSize: false,
      sourcemap: configEnv.mode !== 'test', // 测试模式不生成sourcemap
      chunkSizeWarningLimit: 1000,
      rollupOptions: {
        output: {
          // 代码分割优化
          manualChunks: {
            'vue-vendor': ['vue', 'vue-router', 'pinia'],
            'element-plus': ['element-plus'],
            utils: ['lodash-es', 'dayjs']
          }
        }
      },
      // 内存优化
      minify: configEnv.mode === 'production' ? 'esbuild' : false,
      target: 'es2015'
    },
    optimizeDeps: {
      include: ['vue', 'vue-router', 'pinia', 'element-plus', 'lodash-es', 'dayjs'],
      // 减少预构建开销
      force: false
    },
    // 内存限制配置
    esbuild: {
      // 减少内存使用
      target: 'es2015',
      drop: configEnv.mode === 'production' ? ['console', 'debugger'] : []
    }
  };
});
