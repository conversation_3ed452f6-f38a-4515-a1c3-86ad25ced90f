version: 2
updates:
  # 前端依赖更新
  - package-ecosystem: "npm"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:00"
      timezone: "Asia/Shanghai"
    open-pull-requests-limit: 10
    reviewers:
      - "TWLW9784"
    assignees:
      - "TWLW9784"
    commit-message:
      prefix: "chore(deps)"
      include: "scope"
    labels:
      - "dependencies"
      - "frontend"
    # 只更新安全相关的依赖
    allow:
      - dependency-type: "direct"
        update-type: "security"
      - dependency-type: "indirect"
        update-type: "security"
    # 忽略主要版本更新，避免破坏性变更
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"]

  # 后端依赖更新
  - package-ecosystem: "npm"
    directory: "/server"
    schedule:
      interval: "weekly"
      day: "monday"
      time: "09:30"
      timezone: "Asia/Shanghai"
    open-pull-requests-limit: 10
    reviewers:
      - "TWLW9784"
    assignees:
      - "TWLW9784"
    commit-message:
      prefix: "chore(deps)"
      include: "scope"
    labels:
      - "dependencies"
      - "backend"
    # 只更新安全相关的依赖
    allow:
      - dependency-type: "direct"
        update-type: "security"
      - dependency-type: "indirect"
        update-type: "security"
    # 忽略主要版本更新
    ignore:
      - dependency-name: "*"
        update-types: ["version-update:semver-major"]

  # GitHub Actions 更新
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "monthly"
      day: "1"
      time: "10:00"
      timezone: "Asia/Shanghai"
    open-pull-requests-limit: 5
    reviewers:
      - "TWLW9784"
    assignees:
      - "TWLW9784"
    commit-message:
      prefix: "chore(ci)"
      include: "scope"
    labels:
      - "dependencies"
      - "github-actions"
