name: Security Scan

on:
  schedule:
    # 每天凌晨2点运行安全扫描
    - cron: '0 2 * * *'
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  # 依赖安全扫描
  dependency-scan:
    name: Dependency Security Scan
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: ${{ env.PNPM_VERSION }}

      - name: Install dependencies
        run: pnpm install --frozen-lockfile

      - name: Frontend dependency audit
        run: |
          echo "🔍 Scanning frontend dependencies..."
          pnpm audit --audit-level moderate || true

      - name: Backend dependency audit
        working-directory: ./server
        run: |
          echo "🔍 Scanning backend dependencies..."
          npm audit --audit-level moderate || true

      - name: Check for known vulnerabilities
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'table'
          exit-code: '0'

  # CodeQL 代码安全分析
  codeql:
    name: CodeQL Security Analysis
    runs-on: ubuntu-latest
    permissions:
      actions: read
      contents: read
      security-events: write

    strategy:
      fail-fast: false
      matrix:
        language: [ 'javascript', 'typescript' ]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v2
        with:
          languages: ${{ matrix.language }}
          queries: security-extended,security-and-quality

      - name: Autobuild
        uses: github/codeql-action/autobuild@v2

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v2
        with:
          category: "/language:${{matrix.language}}"

  # 密钥泄露检测
  secret-scan:
    name: Secret Leak Detection
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Run TruffleHog
        uses: trufflesecurity/trufflehog@main
        with:
          path: ./
          base: main
          head: HEAD
          extra_args: --debug --only-verified

  # 容器安全扫描（如果有 Docker）
  container-scan:
    name: Container Security Scan
    runs-on: ubuntu-latest
    if: github.event_name == 'push'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Build Docker image
        if: hashFiles('Dockerfile') != ''
        run: |
          docker build -t security-scan:latest .

      - name: Run Trivy container scan
        if: hashFiles('Dockerfile') != ''
        uses: aquasecurity/trivy-action@master
        with:
          image-ref: 'security-scan:latest'
          format: 'sarif'
          output: 'trivy-container-results.sarif'

      - name: Upload container scan results
        if: hashFiles('Dockerfile') != ''
        uses: github/codeql-action/upload-sarif@v2
        with:
          sarif_file: 'trivy-container-results.sarif'

  # 安全配置检查
  security-config:
    name: Security Configuration Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Check for security files
        run: |
          echo "🔍 Checking security configuration files..."
          
          # 检查是否存在安全相关文件
          files_to_check=(
            "SECURITY.md"
            ".nvmrc"
            ".gitignore"
            "server/.env.example"
            ".env.example"
          )
          
          missing_files=()
          for file in "${files_to_check[@]}"; do
            if [ ! -f "$file" ]; then
              missing_files+=("$file")
            else
              echo "✅ Found: $file"
            fi
          done
          
          if [ ${#missing_files[@]} -gt 0 ]; then
            echo "⚠️ Missing security files:"
            printf '%s\n' "${missing_files[@]}"
          else
            echo "✅ All security files present"
          fi

      - name: Check environment variables
        run: |
          echo "🔍 Checking for hardcoded secrets..."
          
          # 检查是否有硬编码的密钥
          if grep -r -i "password\s*=\s*['\"][^'\"]*['\"]" --include="*.ts" --include="*.js" --include="*.vue" . || \
             grep -r -i "secret\s*=\s*['\"][^'\"]*['\"]" --include="*.ts" --include="*.js" --include="*.vue" . || \
             grep -r -i "api_key\s*=\s*['\"][^'\"]*['\"]" --include="*.ts" --include="*.js" --include="*.vue" .; then
            echo "⚠️ Potential hardcoded secrets found!"
            exit 1
          else
            echo "✅ No hardcoded secrets detected"
          fi

  # 生成安全报告
  security-report:
    name: Generate Security Report
    runs-on: ubuntu-latest
    needs: [dependency-scan, codeql, secret-scan, security-config]
    if: always()
    
    steps:
      - name: Generate security summary
        run: |
          echo "# 🔒 Security Scan Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "| Check | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|-------|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| Dependency Scan | ${{ needs.dependency-scan.result == 'success' && '✅ Passed' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| CodeQL Analysis | ${{ needs.codeql.result == 'success' && '✅ Passed' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Secret Detection | ${{ needs.secret-scan.result == 'success' && '✅ Passed' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Security Config | ${{ needs.security-config.result == 'success' && '✅ Passed' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "📅 Scan completed at: $(date)" >> $GITHUB_STEP_SUMMARY
